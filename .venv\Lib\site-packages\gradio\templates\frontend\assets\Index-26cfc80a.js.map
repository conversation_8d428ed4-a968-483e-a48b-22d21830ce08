{"version": 3, "mappings": "yDACO,SAASA,IAAO,CAAE,CAEb,MAACC,GAAYC,GAAMA,EAoCxB,SAASC,GAAIC,EAAI,CACvB,OAAOA,EAAE,CACV,CAUO,SAASC,GAAQC,EAAK,CAC5BA,EAAI,QAAQH,EAAG,CAChB,CAMO,SAASI,GAAYC,EAAO,CAClC,OAAO,OAAOA,GAAU,UACzB,CAGO,SAASC,GAAeC,EAAGC,EAAG,CACpC,OAAOD,GAAKA,EAAIC,GAAKA,EAAID,IAAMC,GAAMD,GAAK,OAAOA,GAAM,UAAa,OAAOA,GAAM,UAClF,CAiEO,SAASE,GAAUC,KAAUC,EAAW,CAC9C,GAAID,GAAS,KAAM,CAClB,UAAWE,KAAYD,EACtBC,EAAS,MAAS,EAEnB,OAAOf,GAER,MAAMgB,EAAQH,EAAM,UAAU,GAAGC,CAAS,EAC1C,OAAOE,EAAM,YAAc,IAAMA,EAAM,YAAW,EAAKA,CACxD,CAUO,SAASC,GAAgBJ,EAAO,CACtC,IAAIK,EACJ,OAAAN,GAAUC,EAAQM,GAAOD,EAAQC,CAAE,IAC5BD,CACR,CAmIO,SAASE,GAAeF,EAAO,CACrC,MAAMG,EAAQ,OAAOH,GAAU,UAAYA,EAAM,MAAM,4BAA4B,EACnF,OAAOG,EAAQ,CAAC,WAAWA,EAAM,CAAC,CAAC,EAAGA,EAAM,CAAC,GAAK,IAAI,EAAI,CAAwBH,EAAQ,IAAI,CAC/F,CC9RO,MAAMI,GAAY,OAAO,OAAW,IAGpC,IAAIC,GAAMD,GAAY,IAAM,OAAO,YAAY,MAAQ,IAAM,KAAK,MAE9DE,GAAMF,GAAaG,GAAO,sBAAsBA,CAAE,EAAIzB,GCLjE,MAAM0B,GAAQ,IAAI,IAMlB,SAASC,GAAUJ,EAAK,CACvBG,GAAM,QAASE,GAAS,CAClBA,EAAK,EAAEL,CAAG,IACdG,GAAM,OAAOE,CAAI,EACjBA,EAAK,EAAC,EAET,CAAE,EACGF,GAAM,OAAS,GAAGF,GAAIG,EAAS,CACpC,CAgBO,SAASE,GAAKd,EAAU,CAE9B,IAAIa,EACJ,OAAIF,GAAM,OAAS,GAAGF,GAAIG,EAAS,EAC5B,CACN,QAAS,IAAI,QAASG,GAAY,CACjCJ,GAAM,IAAKE,EAAO,CAAE,EAAGb,EAAU,EAAGe,CAAO,EAC9C,CAAG,EACD,OAAQ,CACPJ,GAAM,OAAOE,CAAI,CACjB,CACH,CACA,CCnCA,MAAMG,GAAmB,GAWlB,SAASC,GAASd,EAAOe,EAAO,CACtC,MAAO,CACN,UAAWC,GAAShB,EAAOe,CAAK,EAAE,SACpC,CACA,CAWO,SAASC,GAAShB,EAAOe,EAAQjC,GAAM,CAE7C,IAAImC,EAEJ,MAAMC,EAAc,IAAI,IAIxB,SAASC,EAAIC,EAAW,CACvB,GAAI7B,GAAeS,EAAOoB,CAAS,IAClCpB,EAAQoB,EACJH,GAAM,CAET,MAAMI,EAAY,CAACR,GAAiB,OACpC,UAAWS,KAAcJ,EACxBI,EAAW,CAAC,IACZT,GAAiB,KAAKS,EAAYtB,CAAK,EAExC,GAAIqB,EAAW,CACd,QAASE,EAAI,EAAGA,EAAIV,GAAiB,OAAQU,GAAK,EACjDV,GAAiBU,CAAC,EAAE,CAAC,EAAEV,GAAiBU,EAAI,CAAC,CAAC,EAE/CV,GAAiB,OAAS,GAI7B,CAMD,SAASW,EAAOtC,EAAI,CACnBiC,EAAIjC,EAAGc,CAAK,CAAC,CACb,CAOD,SAASN,EAAUT,EAAKwC,EAAa3C,GAAM,CAE1C,MAAMwC,EAAa,CAACrC,EAAKwC,CAAU,EACnC,OAAAP,EAAY,IAAII,CAAU,EACtBJ,EAAY,OAAS,IACxBD,EAAOF,EAAMI,EAAKK,CAAM,GAAK1C,IAE9BG,EAAIe,CAAK,EACF,IAAM,CACZkB,EAAY,OAAOI,CAAU,EACzBJ,EAAY,OAAS,GAAKD,IAC7BA,IACAA,EAAO,KAEX,CACE,CACD,MAAO,CAAE,IAAAE,EAAK,OAAAK,EAAQ,UAAA9B,EACvB,CAsCO,SAASgC,GAAQC,EAAQzC,EAAI0C,EAAe,CAClD,MAAMC,EAAS,CAAC,MAAM,QAAQF,CAAM,EAE9BG,EAAeD,EAAS,CAACF,CAAM,EAAIA,EACzC,GAAI,CAACG,EAAa,MAAM,OAAO,EAC9B,MAAM,IAAI,MAAM,sDAAsD,EAEvE,MAAMC,EAAO7C,EAAG,OAAS,EACzB,OAAO4B,GAASc,EAAe,CAACT,EAAKK,IAAW,CAC/C,IAAIQ,EAAU,GACd,MAAMC,EAAS,GACf,IAAIC,EAAU,EACVC,EAAUrD,GACd,MAAMsD,EAAO,IAAM,CAClB,GAAIF,EACH,OAEDC,IACA,MAAME,EAASnD,EAAG2C,EAASI,EAAO,CAAC,EAAIA,EAAQd,EAAKK,CAAM,EACtDO,EACHZ,EAAIkB,CAAM,EAEVF,EAAU9C,GAAYgD,CAAM,EAAIA,EAASvD,EAE7C,EACQwD,EAAgBR,EAAa,IAAI,CAACnC,EAAO4B,IAC9C7B,GACCC,EACCK,GAAU,CACViC,EAAOV,CAAC,EAAIvB,EACZkC,GAAW,EAAE,GAAKX,GACdS,GACHI,GAED,EACD,IAAM,CACLF,GAAW,GAAKX,CAChB,CACD,CACJ,EACE,OAAAS,EAAU,GACVI,IACO,UAAgB,CACtBjD,GAAQmD,CAAa,EACrBH,IAIAH,EAAU,EACb,CACA,CAAE,CACF,CCtLA,MAAeO,GAAA,svBCEf,IAAIC,GAAoB,SAA2BxC,EAAO,CACzD,OAAOyC,GAAgBzC,CAAK,GACxB,CAAC0C,GAAU1C,CAAK,CACrB,EAEA,SAASyC,GAAgBzC,EAAO,CAC/B,MAAO,CAAC,CAACA,GAAS,OAAOA,GAAU,QACpC,CAEA,SAAS0C,GAAU1C,EAAO,CACzB,IAAI2C,EAAc,OAAO,UAAU,SAAS,KAAK3C,CAAK,EAEtD,OAAO2C,IAAgB,mBACnBA,IAAgB,iBAChBC,GAAe5C,CAAK,CACzB,CAGA,IAAI6C,GAAe,OAAO,QAAW,YAAc,OAAO,IACtDC,GAAqBD,GAAe,OAAO,IAAI,eAAe,EAAI,MAEtE,SAASD,GAAe5C,EAAO,CAC9B,OAAOA,EAAM,WAAa8C,EAC3B,CAEA,SAASC,GAAYC,EAAK,CACzB,OAAO,MAAM,QAAQA,CAAG,EAAI,CAAE,EAAG,CAAE,CACpC,CAEA,SAASC,GAA8BjD,EAAOkD,EAAS,CACtD,OAAQA,EAAQ,QAAU,IAASA,EAAQ,kBAAkBlD,CAAK,EAC/DmD,GAAUJ,GAAY/C,CAAK,EAAGA,EAAOkD,CAAO,EAC5ClD,CACJ,CAEA,SAASoD,GAAkBC,EAAQC,EAAQJ,EAAS,CACnD,OAAOG,EAAO,OAAOC,CAAM,EAAE,IAAI,SAASC,EAAS,CAClD,OAAON,GAA8BM,EAASL,CAAO,CACvD,CAAE,CACF,CAEA,SAASM,GAAiBC,EAAKP,EAAS,CACvC,GAAI,CAACA,EAAQ,YACZ,OAAOC,GAER,IAAIO,EAAcR,EAAQ,YAAYO,CAAG,EACzC,OAAO,OAAOC,GAAgB,WAAaA,EAAcP,EAC1D,CAEA,SAASQ,GAAgCN,EAAQ,CAChD,OAAO,OAAO,sBACX,OAAO,sBAAsBA,CAAM,EAAE,OAAO,SAASO,EAAQ,CAC9D,OAAO,OAAO,qBAAqB,KAAKP,EAAQO,CAAM,CACzD,CAAG,EACC,CAAE,CACN,CAEA,SAASC,GAAQR,EAAQ,CACxB,OAAO,OAAO,KAAKA,CAAM,EAAE,OAAOM,GAAgCN,CAAM,CAAC,CAC1E,CAEA,SAASS,GAAmBC,EAAQC,EAAU,CAC7C,GAAI,CACH,OAAOA,KAAYD,CACnB,MAAC,CACD,MAAO,EACP,CACF,CAGA,SAASE,GAAiBZ,EAAQI,EAAK,CACtC,OAAOK,GAAmBT,EAAQI,CAAG,GACjC,EAAE,OAAO,eAAe,KAAKJ,EAAQI,CAAG,GACvC,OAAO,qBAAqB,KAAKJ,EAAQI,CAAG,EAClD,CAEA,SAASS,GAAYb,EAAQC,EAAQJ,EAAS,CAC7C,IAAIiB,EAAc,GAClB,OAAIjB,EAAQ,kBAAkBG,CAAM,GACnCQ,GAAQR,CAAM,EAAE,QAAQ,SAASI,EAAK,CACrCU,EAAYV,CAAG,EAAIR,GAA8BI,EAAOI,CAAG,EAAGP,CAAO,CACxE,CAAG,EAEFW,GAAQP,CAAM,EAAE,QAAQ,SAASG,EAAK,CACjCQ,GAAiBZ,EAAQI,CAAG,IAI5BK,GAAmBT,EAAQI,CAAG,GAAKP,EAAQ,kBAAkBI,EAAOG,CAAG,CAAC,EAC3EU,EAAYV,CAAG,EAAID,GAAiBC,EAAKP,CAAO,EAAEG,EAAOI,CAAG,EAAGH,EAAOG,CAAG,EAAGP,CAAO,EAEnFiB,EAAYV,CAAG,EAAIR,GAA8BK,EAAOG,CAAG,EAAGP,CAAO,EAExE,CAAE,EACMiB,CACR,CAEA,SAAShB,GAAUE,EAAQC,EAAQJ,EAAS,CAC3CA,EAAUA,GAAW,GACrBA,EAAQ,WAAaA,EAAQ,YAAcE,GAC3CF,EAAQ,kBAAoBA,EAAQ,mBAAqBV,GAGzDU,EAAQ,8BAAgCD,GAExC,IAAImB,EAAgB,MAAM,QAAQd,CAAM,EACpCe,EAAgB,MAAM,QAAQhB,CAAM,EACpCiB,EAA4BF,IAAkBC,EAElD,OAAKC,EAEMF,EACHlB,EAAQ,WAAWG,EAAQC,EAAQJ,CAAO,EAE1CgB,GAAYb,EAAQC,EAAQJ,CAAO,EAJnCD,GAA8BK,EAAQJ,CAAO,CAMtD,CAEAC,GAAU,IAAM,SAAsBoB,EAAOrB,EAAS,CACrD,GAAI,CAAC,MAAM,QAAQqB,CAAK,EACvB,MAAM,IAAI,MAAM,mCAAmC,EAGpD,OAAOA,EAAM,OAAO,SAASC,EAAMC,EAAM,CACxC,OAAOtB,GAAUqB,EAAMC,EAAMvB,CAAO,CACpC,EAAE,EAAE,CACN,EAEA,IAAIwB,GAAcvB,GAElBwB,GAAiBD,0DCrHjB,IAAIE,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,GACAC,GACAC,EACAC,GACAC,GACH,SAAUC,EAAS,CAChB,IAAIC,GAAO,OAAOC,IAAW,SAAWA,GAAS,OAAO,MAAS,SAAW,KAAO,OAAO,MAAS,SAAW,KAAO,GAKjHF,EAAQG,GAAeF,GAAME,GAAeC,EAAO,OAAO,CAAC,CAAC,EAKhE,SAASD,GAAeE,GAASC,EAAU,CACvC,OAAID,KAAYJ,KACR,OAAO,OAAO,QAAW,WACzB,OAAO,eAAeI,GAAS,aAAc,CAAE,MAAO,EAAI,CAAE,EAG5DA,GAAQ,WAAa,IAGtB,SAAUE,EAAIC,EAAG,CAAE,OAAOH,GAAQE,CAAE,EAAID,EAAWA,EAASC,EAAIC,CAAC,EAAIA,CAAE,CACjF,CACJ,GACA,SAAUC,EAAU,CACjB,IAAIC,GAAgB,OAAO,gBACtB,CAAE,UAAW,cAAgB,OAAS,SAAUC,EAAG7H,EAAG,CAAE6H,EAAE,UAAY7H,CAAE,GACzE,SAAU6H,EAAG7H,EAAG,CAAE,QAAS8H,KAAK9H,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAG8H,CAAC,IAAGD,EAAEC,CAAC,EAAI9H,EAAE8H,CAAC,IAEjG3C,EAAY,SAAU0C,EAAG7H,EAAG,CACxB,GAAI,OAAOA,GAAM,YAAcA,IAAM,KACjC,MAAM,IAAI,UAAU,uBAAyB,OAAOA,CAAC,EAAI,+BAA+B,EAC5F4H,GAAcC,EAAG7H,CAAC,EAClB,SAAS+H,GAAK,CAAE,KAAK,YAAcF,CAAI,CACvCA,EAAE,UAAY7H,IAAM,KAAO,OAAO,OAAOA,CAAC,GAAK+H,EAAG,UAAY/H,EAAE,UAAW,IAAI+H,EACvF,EAEI3C,EAAW,OAAO,QAAU,SAAU4C,EAAG,CACrC,QAASC,EAAGnG,EAAI,EAAGoG,EAAI,UAAU,OAAQpG,EAAIoG,EAAGpG,IAAK,CACjDmG,EAAI,UAAUnG,CAAC,EACf,QAASgG,KAAKG,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAGH,CAAC,IAAGE,EAAEF,CAAC,EAAIG,EAAEH,CAAC,GAE/E,OAAOE,CACf,EAEI3C,EAAS,SAAU4C,EAAGE,EAAG,CACrB,IAAIH,EAAI,GACR,QAASF,KAAKG,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAGH,CAAC,GAAKK,EAAE,QAAQL,CAAC,EAAI,IAC9EE,EAAEF,CAAC,EAAIG,EAAEH,CAAC,GACd,GAAIG,GAAK,MAAQ,OAAO,OAAO,uBAA0B,WACrD,QAASnG,EAAI,EAAGgG,EAAI,OAAO,sBAAsBG,CAAC,EAAGnG,EAAIgG,EAAE,OAAQhG,IAC3DqG,EAAE,QAAQL,EAAEhG,CAAC,CAAC,EAAI,GAAK,OAAO,UAAU,qBAAqB,KAAKmG,EAAGH,EAAEhG,CAAC,CAAC,IACzEkG,EAAEF,EAAEhG,CAAC,CAAC,EAAImG,EAAEH,EAAEhG,CAAC,CAAC,GAE5B,OAAOkG,CACf,EAEI1C,EAAa,SAAU8C,EAAYxE,EAAQI,EAAKqE,EAAM,CAClD,IAAIC,EAAI,UAAU,OAAQC,EAAID,EAAI,EAAI1E,EAASyE,IAAS,KAAOA,EAAO,OAAO,yBAAyBzE,EAAQI,CAAG,EAAIqE,EAAMR,EAC3H,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAYU,EAAI,QAAQ,SAASH,EAAYxE,EAAQI,EAAKqE,CAAI,MACxH,SAASvG,EAAIsG,EAAW,OAAS,EAAGtG,GAAK,EAAGA,KAAS+F,EAAIO,EAAWtG,CAAC,KAAGyG,GAAKD,EAAI,EAAIT,EAAEU,CAAC,EAAID,EAAI,EAAIT,EAAEjE,EAAQI,EAAKuE,CAAC,EAAIV,EAAEjE,EAAQI,CAAG,IAAMuE,GAChJ,OAAOD,EAAI,GAAKC,GAAK,OAAO,eAAe3E,EAAQI,EAAKuE,CAAC,EAAGA,CACpE,EAEIhD,EAAU,SAAUiD,EAAYC,EAAW,CACvC,OAAO,SAAU7E,EAAQI,EAAK,CAAEyE,EAAU7E,EAAQI,EAAKwE,CAAU,CAAI,CAC7E,EAEIhD,EAAe,SAAUkD,EAAMC,EAAcP,EAAYQ,EAAWC,EAAcC,EAAmB,CACjG,SAASC,EAAOC,EAAG,CAAE,GAAIA,IAAM,QAAU,OAAOA,GAAM,WAAY,MAAM,IAAI,UAAU,mBAAmB,EAAG,OAAOA,CAAI,CAKvH,QAJIC,EAAOL,EAAU,KAAM5E,EAAMiF,IAAS,SAAW,MAAQA,IAAS,SAAW,MAAQ,QACrFrF,EAAS,CAAC+E,GAAgBD,EAAOE,EAAU,OAAYF,EAAOA,EAAK,UAAY,KAC/EQ,EAAaP,IAAiB/E,EAAS,OAAO,yBAAyBA,EAAQgF,EAAU,IAAI,EAAI,IACjGpI,EAAG2I,EAAO,GACLrH,GAAIsG,EAAW,OAAS,EAAGtG,IAAK,EAAGA,KAAK,CAC7C,IAAIsH,GAAU,GACd,QAAStB,MAAKc,EAAWQ,GAAQtB,EAAC,EAAIA,KAAM,SAAW,GAAKc,EAAUd,EAAC,EACvE,QAASA,MAAKc,EAAU,OAAQQ,GAAQ,OAAOtB,EAAC,EAAIc,EAAU,OAAOd,EAAC,EACtEsB,GAAQ,eAAiB,SAAUJ,EAAG,CAAE,GAAIG,EAAM,MAAM,IAAI,UAAU,wDAAwD,EAAGL,EAAkB,KAAKC,EAAOC,GAAK,IAAI,CAAC,GACzK,IAAIpG,MAAawF,EAAWtG,EAAC,GAAGmH,IAAS,WAAa,CAAE,IAAKC,EAAW,IAAK,IAAKA,EAAW,GAAG,EAAKA,EAAWlF,CAAG,EAAGoF,EAAO,EAC7H,GAAIH,IAAS,WAAY,CACrB,GAAIrG,KAAW,OAAQ,SACvB,GAAIA,KAAW,MAAQ,OAAOA,IAAW,SAAU,MAAM,IAAI,UAAU,iBAAiB,GACpFpC,EAAIuI,EAAOnG,GAAO,GAAG,KAAGsG,EAAW,IAAM1I,IACzCA,EAAIuI,EAAOnG,GAAO,GAAG,KAAGsG,EAAW,IAAM1I,IACzCA,EAAIuI,EAAOnG,GAAO,IAAI,IAAGiG,EAAa,QAAQrI,CAAC,OAE9CA,EAAIuI,EAAOnG,EAAM,KAClBqG,IAAS,QAASJ,EAAa,QAAQrI,CAAC,EACvC0I,EAAWlF,CAAG,EAAIxD,GAG3BoD,GAAQ,OAAO,eAAeA,EAAQgF,EAAU,KAAMM,CAAU,EACpEC,EAAO,EACf,EAEI1D,EAAoB,SAAU4D,EAASR,EAActI,EAAO,CAExD,QADI+I,EAAW,UAAU,OAAS,EACzBxH,EAAI,EAAGA,EAAI+G,EAAa,OAAQ/G,IACrCvB,EAAQ+I,EAAWT,EAAa/G,CAAC,EAAE,KAAKuH,EAAS9I,CAAK,EAAIsI,EAAa/G,CAAC,EAAE,KAAKuH,CAAO,EAE1F,OAAOC,EAAW/I,EAAQ,MAClC,EAEImF,EAAY,SAAUnG,EAAG,CACrB,OAAO,OAAOA,GAAM,SAAWA,EAAI,GAAG,OAAOA,CAAC,CACtD,EAEIoG,EAAoB,SAAU,EAAG4D,EAAMC,EAAQ,CAC3C,OAAI,OAAOD,GAAS,WAAUA,EAAOA,EAAK,YAAc,IAAI,OAAOA,EAAK,YAAa,GAAG,EAAI,IACrF,OAAO,eAAe,EAAG,OAAQ,CAAE,aAAc,GAAM,MAAOC,EAAS,GAAG,OAAOA,EAAQ,IAAKD,CAAI,EAAIA,CAAI,CAAE,CAC3H,EAEI3D,EAAa,SAAU6D,EAAaC,EAAe,CAC/C,GAAI,OAAO,SAAY,UAAY,OAAO,QAAQ,UAAa,WAAY,OAAO,QAAQ,SAASD,EAAaC,CAAa,CACrI,EAEI7D,EAAY,SAAUwD,EAASM,EAAYC,EAAGC,EAAW,CACrD,SAASC,EAAMvJ,EAAO,CAAE,OAAOA,aAAiBqJ,EAAIrJ,EAAQ,IAAIqJ,EAAE,SAAUG,EAAS,CAAEA,EAAQxJ,CAAK,CAAE,CAAE,CAAI,CAC5G,OAAO,IAAKqJ,IAAMA,EAAI,UAAU,SAAUG,EAASC,EAAQ,CACvD,SAASC,EAAU1J,EAAO,CAAE,GAAI,CAAE2J,EAAKL,EAAU,KAAKtJ,CAAK,CAAC,CAAE,OAAU4H,EAAP,CAAY6B,EAAO7B,CAAC,EAAM,CAC3F,SAASgC,EAAS5J,EAAO,CAAE,GAAI,CAAE2J,EAAKL,EAAU,MAAStJ,CAAK,CAAC,CAAI,OAAQ4H,EAAP,CAAY6B,EAAO7B,CAAC,EAAM,CAC9F,SAAS+B,EAAKtH,EAAQ,CAAEA,EAAO,KAAOmH,EAAQnH,EAAO,KAAK,EAAIkH,EAAMlH,EAAO,KAAK,EAAE,KAAKqH,EAAWE,CAAQ,CAAI,CAC9GD,GAAML,EAAYA,EAAU,MAAMR,EAASM,GAAc,CAAE,IAAG,KAAI,CAAE,CAChF,CAAS,CACT,EAEI7D,EAAc,SAAUuD,EAASe,EAAM,CACnC,IAAI5J,EAAI,CAAE,MAAO,EAAG,KAAM,UAAW,CAAE,GAAIwH,EAAE,CAAC,EAAI,EAAG,MAAMA,EAAE,CAAC,EAAG,OAAOA,EAAE,CAAC,CAAI,EAAE,KAAM,CAAE,EAAE,IAAK,EAAI,EAAEgB,EAAGqB,EAAGrC,EAAGsC,EAC/G,OAAOA,EAAI,CAAE,KAAMC,EAAK,CAAC,EAAG,MAASA,EAAK,CAAC,EAAG,OAAUA,EAAK,CAAC,CAAG,EAAE,OAAO,QAAW,aAAeD,EAAE,OAAO,QAAQ,EAAI,UAAW,CAAE,OAAO,IAAO,GAAGA,EACvJ,SAASC,EAAKrC,EAAG,CAAE,OAAO,SAAUR,EAAG,CAAE,OAAOwC,EAAK,CAAChC,EAAGR,CAAC,CAAC,CAAI,CAAG,CAClE,SAASwC,EAAKM,EAAI,CACd,GAAIxB,EAAG,MAAM,IAAI,UAAU,iCAAiC,EAC5D,KAAOsB,IAAMA,EAAI,EAAGE,EAAG,CAAC,IAAMhK,EAAI,IAAKA,GAAG,GAAI,CAC1C,GAAIwI,EAAI,EAAGqB,IAAMrC,EAAIwC,EAAG,CAAC,EAAI,EAAIH,EAAE,OAAYG,EAAG,CAAC,EAAIH,EAAE,SAAcrC,EAAIqC,EAAE,SAAcrC,EAAE,KAAKqC,CAAC,EAAG,GAAKA,EAAE,OAAS,EAAErC,EAAIA,EAAE,KAAKqC,EAAGG,EAAG,CAAC,CAAC,GAAG,KAAM,OAAOxC,EAE3J,OADIqC,EAAI,EAAGrC,IAAGwC,EAAK,CAACA,EAAG,CAAC,EAAI,EAAGxC,EAAE,KAAK,GAC9BwC,EAAG,CAAC,EAAC,CACT,IAAK,GAAG,IAAK,GAAGxC,EAAIwC,EAAI,MACxB,IAAK,GAAG,OAAAhK,EAAE,QAAgB,CAAE,MAAOgK,EAAG,CAAC,EAAG,KAAM,EAAK,EACrD,IAAK,GAAGhK,EAAE,QAAS6J,EAAIG,EAAG,CAAC,EAAGA,EAAK,CAAC,CAAC,EAAG,SACxC,IAAK,GAAGA,EAAKhK,EAAE,IAAI,MAAOA,EAAE,KAAK,IAAG,EAAI,SACxC,QACI,GAAMwH,EAAIxH,EAAE,KAAM,EAAAwH,EAAIA,EAAE,OAAS,GAAKA,EAAEA,EAAE,OAAS,CAAC,KAAOwC,EAAG,CAAC,IAAM,GAAKA,EAAG,CAAC,IAAM,GAAI,CAAEhK,EAAI,EAAG,SACjG,GAAIgK,EAAG,CAAC,IAAM,IAAM,CAACxC,GAAMwC,EAAG,CAAC,EAAIxC,EAAE,CAAC,GAAKwC,EAAG,CAAC,EAAIxC,EAAE,CAAC,GAAK,CAAExH,EAAE,MAAQgK,EAAG,CAAC,EAAG,MAC9E,GAAIA,EAAG,CAAC,IAAM,GAAKhK,EAAE,MAAQwH,EAAE,CAAC,EAAG,CAAExH,EAAE,MAAQwH,EAAE,CAAC,EAAGA,EAAIwC,EAAI,MAC7D,GAAIxC,GAAKxH,EAAE,MAAQwH,EAAE,CAAC,EAAG,CAAExH,EAAE,MAAQwH,EAAE,CAAC,EAAGxH,EAAE,IAAI,KAAKgK,CAAE,EAAG,MACvDxC,EAAE,CAAC,GAAGxH,EAAE,IAAI,IAAG,EACnBA,EAAE,KAAK,IAAK,EAAE,QACrB,CACDgK,EAAKJ,EAAK,KAAKf,EAAS7I,CAAC,CAC5B,OAAQ2H,EAAP,CAAYqC,EAAK,CAAC,EAAGrC,CAAC,EAAGkC,EAAI,CAAE,QAAW,CAAErB,EAAIhB,EAAI,CAAI,CAC1D,GAAIwC,EAAG,CAAC,EAAI,EAAG,MAAMA,EAAG,CAAC,EAAG,MAAO,CAAE,MAAOA,EAAG,CAAC,EAAIA,EAAG,CAAC,EAAI,OAAQ,KAAM,GAC7E,CACT,EAEIzE,EAAe,SAAS0E,EAAGC,EAAG,CAC1B,QAAS5C,KAAK2C,EAAO3C,IAAM,WAAa,CAAC,OAAO,UAAU,eAAe,KAAK4C,EAAG5C,CAAC,GAAGf,EAAgB2D,EAAGD,EAAG3C,CAAC,CACpH,EAEIf,EAAkB,OAAO,OAAU,SAAS2D,EAAGD,EAAGE,EAAGC,EAAI,CACjDA,IAAO,SAAWA,EAAKD,GAC3B,IAAItC,EAAO,OAAO,yBAAyBoC,EAAGE,CAAC,GAC3C,CAACtC,IAAS,QAASA,EAAO,CAACoC,EAAE,WAAapC,EAAK,UAAYA,EAAK,iBAChEA,EAAO,CAAE,WAAY,GAAM,IAAK,UAAW,CAAE,OAAOoC,EAAEE,CAAC,CAAE,IAE7D,OAAO,eAAeD,EAAGE,EAAIvC,CAAI,CACpC,EAAK,SAASqC,EAAGD,EAAGE,EAAGC,EAAI,CACpBA,IAAO,SAAWA,EAAKD,GAC3BD,EAAEE,CAAE,EAAIH,EAAEE,CAAC,CACnB,EAEI3E,EAAW,SAAU0E,EAAG,CACpB,IAAIzC,EAAI,OAAO,QAAW,YAAc,OAAO,SAAU,EAAIA,GAAKyC,EAAEzC,CAAC,EAAGnG,EAAI,EAC5E,GAAI,EAAG,OAAO,EAAE,KAAK4I,CAAC,EACtB,GAAIA,GAAK,OAAOA,EAAE,QAAW,SAAU,MAAO,CAC1C,KAAM,UAAY,CACd,OAAIA,GAAK5I,GAAK4I,EAAE,SAAQA,EAAI,QACrB,CAAE,MAAOA,GAAKA,EAAE5I,GAAG,EAAG,KAAM,CAAC4I,EACvC,CACb,EACQ,MAAM,IAAI,UAAUzC,EAAI,0BAA4B,iCAAiC,CAC7F,EAEIhC,EAAS,SAAUyE,EAAGxC,EAAG,CACrB,IAAI,EAAI,OAAO,QAAW,YAAcwC,EAAE,OAAO,QAAQ,EACzD,GAAI,CAAC,EAAG,OAAOA,EACf,IAAI5I,EAAI,EAAE,KAAK4I,CAAC,EAAGnC,EAAGsC,EAAK,CAAE,EAAE1C,EAC/B,GAAI,CACA,MAAQD,IAAM,QAAUA,KAAM,IAAM,EAAEK,EAAIzG,EAAE,KAAM,GAAE,MAAM+I,EAAG,KAAKtC,EAAE,KAAK,CAC5E,OACMuC,EAAP,CAAgB3C,EAAI,CAAE,MAAO2C,CAAO,CAAG,QAC/B,CACJ,GAAI,CACIvC,GAAK,CAACA,EAAE,OAAS,EAAIzG,EAAE,SAAY,EAAE,KAAKA,CAAC,CAClD,QACO,CAAE,GAAIqG,EAAG,MAAMA,EAAE,KAAQ,CACpC,CACD,OAAO0C,CACf,EAGI3E,EAAW,UAAY,CACnB,QAAS2E,EAAK,GAAI/I,EAAI,EAAGA,EAAI,UAAU,OAAQA,IAC3C+I,EAAKA,EAAG,OAAO5E,EAAO,UAAUnE,CAAC,CAAC,CAAC,EACvC,OAAO+I,CACf,EAGI1E,EAAiB,UAAY,CACzB,QAAS8B,EAAI,EAAGnG,EAAI,EAAGiJ,EAAK,UAAU,OAAQjJ,EAAIiJ,EAAIjJ,IAAKmG,GAAK,UAAUnG,CAAC,EAAE,OAC7E,QAASyG,EAAI,MAAMN,CAAC,EAAG0C,EAAI,EAAG7I,EAAI,EAAGA,EAAIiJ,EAAIjJ,IACzC,QAAS/B,EAAI,UAAU+B,CAAC,EAAGkJ,EAAI,EAAGC,EAAKlL,EAAE,OAAQiL,EAAIC,EAAID,IAAKL,IAC1DpC,EAAEoC,CAAC,EAAI5K,EAAEiL,CAAC,EAClB,OAAOzC,CACf,EAEInC,EAAgB,SAAU8E,EAAIC,EAAMC,EAAM,CACtC,GAAIA,GAAQ,UAAU,SAAW,EAAG,QAAStJ,EAAI,EAAGuJ,EAAIF,EAAK,OAAQN,EAAI/I,EAAIuJ,EAAGvJ,KACxE+I,GAAM,EAAE/I,KAAKqJ,MACRN,IAAIA,EAAK,MAAM,UAAU,MAAM,KAAKM,EAAM,EAAGrJ,CAAC,GACnD+I,EAAG/I,CAAC,EAAIqJ,EAAKrJ,CAAC,GAGtB,OAAOoJ,EAAG,OAAOL,GAAM,MAAM,UAAU,MAAM,KAAKM,CAAI,CAAC,CAC/D,EAEI9E,EAAU,SAAUqB,EAAG,CACnB,OAAO,gBAAgBrB,GAAW,KAAK,EAAIqB,EAAG,MAAQ,IAAIrB,EAAQqB,CAAC,CAC3E,EAEIpB,EAAmB,SAAU+C,EAASM,EAAYE,EAAW,CACzD,GAAI,CAAC,OAAO,cAAe,MAAM,IAAI,UAAU,sCAAsC,EACrF,IAAIS,EAAIT,EAAU,MAAMR,EAASM,GAAc,EAAE,EAAG7H,EAAGwJ,EAAI,GAC3D,OAAOxJ,EAAI,GAAIyI,EAAK,MAAM,EAAGA,EAAK,OAAO,EAAGA,EAAK,QAAQ,EAAGzI,EAAE,OAAO,aAAa,EAAI,UAAY,CAAE,OAAO,IAAO,EAAEA,EACpH,SAASyI,EAAKrC,EAAG,CAAMoC,EAAEpC,CAAC,IAAGpG,EAAEoG,CAAC,EAAI,SAAUR,GAAG,CAAE,OAAO,IAAI,QAAQ,SAAU3H,GAAGC,GAAG,CAAEsL,EAAE,KAAK,CAACpD,EAAGR,GAAG3H,GAAGC,EAAC,CAAC,EAAI,GAAKuL,EAAOrD,EAAGR,EAAC,CAAE,CAAE,CAAI,EAAG,CAC1I,SAAS6D,EAAOrD,EAAGR,GAAG,CAAE,GAAI,CAAEwC,EAAKI,EAAEpC,CAAC,EAAER,EAAC,CAAC,CAAE,OAAUS,GAAP,CAAYqD,EAAOF,EAAE,CAAC,EAAE,CAAC,EAAGnD,EAAC,EAAM,CAClF,SAAS+B,EAAK3B,EAAG,CAAEA,EAAE,iBAAiBlC,EAAU,QAAQ,QAAQkC,EAAE,MAAM,CAAC,EAAE,KAAKpH,EAAS6I,CAAM,EAAIwB,EAAOF,EAAE,CAAC,EAAE,CAAC,EAAG/C,CAAC,CAAK,CACzH,SAASpH,EAAQZ,EAAO,CAAEgL,EAAO,OAAQhL,CAAK,CAAI,CAClD,SAASyJ,EAAOzJ,EAAO,CAAEgL,EAAO,QAAShL,CAAK,CAAI,CAClD,SAASiL,EAAOxC,EAAGtB,GAAG,CAAMsB,EAAEtB,EAAC,EAAG4D,EAAE,MAAK,EAAIA,EAAE,QAAQC,EAAOD,EAAE,CAAC,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAE,CAAC,CAAC,CAAI,CAC1F,EAEI/E,EAAmB,SAAUmE,EAAG,CAC5B,IAAI5I,EAAGgG,EACP,OAAOhG,EAAI,GAAIyI,EAAK,MAAM,EAAGA,EAAK,QAAS,SAAUpC,EAAG,CAAE,MAAMA,CAAE,CAAE,EAAGoC,EAAK,QAAQ,EAAGzI,EAAE,OAAO,QAAQ,EAAI,UAAY,CAAE,OAAO,IAAO,EAAEA,EAC1I,SAASyI,EAAKrC,EAAGc,EAAG,CAAElH,EAAEoG,CAAC,EAAIwC,EAAExC,CAAC,EAAI,SAAUR,EAAG,CAAE,OAAQI,EAAI,CAACA,GAAK,CAAE,MAAOzB,EAAQqE,EAAExC,CAAC,EAAER,CAAC,CAAC,EAAG,KAAM,EAAO,EAAGsB,EAAIA,EAAEtB,CAAC,EAAIA,CAAE,EAAKsB,CAAI,CAC9I,EAEIxC,EAAgB,SAAUkE,EAAG,CACzB,GAAI,CAAC,OAAO,cAAe,MAAM,IAAI,UAAU,sCAAsC,EACrF,IAAID,EAAIC,EAAE,OAAO,aAAa,EAAG5I,EACjC,OAAO2I,EAAIA,EAAE,KAAKC,CAAC,GAAKA,EAAI,OAAO1E,GAAa,WAAaA,EAAS0E,CAAC,EAAIA,EAAE,OAAO,QAAQ,EAAC,EAAI5I,EAAI,CAAE,EAAEyI,EAAK,MAAM,EAAGA,EAAK,OAAO,EAAGA,EAAK,QAAQ,EAAGzI,EAAE,OAAO,aAAa,EAAI,UAAY,CAAE,OAAO,IAAK,EAAIA,GAC9M,SAASyI,EAAKrC,EAAG,CAAEpG,EAAEoG,CAAC,EAAIwC,EAAExC,CAAC,GAAK,SAAUR,EAAG,CAAE,OAAO,IAAI,QAAQ,SAAUqC,EAASC,EAAQ,CAAEtC,EAAIgD,EAAExC,CAAC,EAAER,CAAC,EAAG8D,EAAOzB,EAASC,EAAQtC,EAAE,KAAMA,EAAE,KAAK,CAAE,CAAE,CAAI,CAAG,CAChK,SAAS8D,EAAOzB,EAASC,EAAQnC,EAAGH,EAAG,CAAE,QAAQ,QAAQA,CAAC,EAAE,KAAK,SAASA,EAAG,CAAEqC,EAAQ,CAAE,MAAOrC,EAAG,KAAMG,CAAC,CAAE,CAAE,EAAImC,CAAM,CAAI,CACpI,EAEIvD,EAAuB,SAAUgF,EAAQC,EAAK,CAC1C,OAAI,OAAO,eAAkB,OAAO,eAAeD,EAAQ,MAAO,CAAE,MAAOC,CAAG,CAAE,EAAYD,EAAO,IAAMC,EAClGD,CACf,EAEI,IAAIE,GAAqB,OAAO,OAAU,SAASjB,EAAGhD,EAAG,CACrD,OAAO,eAAegD,EAAG,UAAW,CAAE,WAAY,GAAM,MAAOhD,CAAC,CAAE,CAC1E,EAAS,SAASgD,EAAGhD,EAAG,CAChBgD,EAAE,QAAahD,CACvB,EAEIhB,EAAe,SAAUkF,EAAK,CAC1B,GAAIA,GAAOA,EAAI,WAAY,OAAOA,EAClC,IAAIhJ,EAAS,GACb,GAAIgJ,GAAO,KAAM,QAASjB,KAAKiB,EAASjB,IAAM,WAAa,OAAO,UAAU,eAAe,KAAKiB,EAAKjB,CAAC,GAAG5D,EAAgBnE,EAAQgJ,EAAKjB,CAAC,EACvI,OAAAgB,GAAmB/I,EAAQgJ,CAAG,EACvBhJ,CACf,EAEI+D,EAAkB,SAAUiF,EAAK,CAC7B,OAAQA,GAAOA,EAAI,WAAcA,EAAM,CAAE,QAAWA,EAC5D,EAEIhF,EAAyB,SAAUiF,EAAUC,EAAO7C,EAAMD,EAAG,CACzD,GAAIC,IAAS,KAAO,CAACD,EAAG,MAAM,IAAI,UAAU,+CAA+C,EAC3F,GAAI,OAAO8C,GAAU,WAAaD,IAAaC,GAAS,CAAC9C,EAAI,CAAC8C,EAAM,IAAID,CAAQ,EAAG,MAAM,IAAI,UAAU,0EAA0E,EACjL,OAAO5C,IAAS,IAAMD,EAAIC,IAAS,IAAMD,EAAE,KAAK6C,CAAQ,EAAI7C,EAAIA,EAAE,MAAQ8C,EAAM,IAAID,CAAQ,CACpG,EAEIhF,GAAyB,SAAUgF,EAAUC,EAAOvL,EAAO0I,EAAMD,EAAG,CAChE,GAAIC,IAAS,IAAK,MAAM,IAAI,UAAU,gCAAgC,EACtE,GAAIA,IAAS,KAAO,CAACD,EAAG,MAAM,IAAI,UAAU,+CAA+C,EAC3F,GAAI,OAAO8C,GAAU,WAAaD,IAAaC,GAAS,CAAC9C,EAAI,CAAC8C,EAAM,IAAID,CAAQ,EAAG,MAAM,IAAI,UAAU,yEAAyE,EAChL,OAAQ5C,IAAS,IAAMD,EAAE,KAAK6C,EAAUtL,CAAK,EAAIyI,EAAIA,EAAE,MAAQzI,EAAQuL,EAAM,IAAID,EAAUtL,CAAK,EAAIA,CAC5G,EAEIuG,GAAwB,SAAUgF,EAAOD,EAAU,CAC/C,GAAIA,IAAa,MAAS,OAAOA,GAAa,UAAY,OAAOA,GAAa,WAAa,MAAM,IAAI,UAAU,wCAAwC,EACvJ,OAAO,OAAOC,GAAU,WAAaD,IAAaC,EAAQA,EAAM,IAAID,CAAQ,CACpF,EAEI7E,GAA0B,SAAU+E,EAAKxL,EAAOyL,EAAO,CACnD,GAAIzL,GAAU,KAA0B,CACpC,GAAI,OAAOA,GAAU,UAAY,OAAOA,GAAU,WAAY,MAAM,IAAI,UAAU,kBAAkB,EACpG,IAAI0L,EACJ,GAAID,EAAO,CACP,GAAI,CAAC,OAAO,aAAc,MAAM,IAAI,UAAU,qCAAqC,EACnFC,EAAU1L,EAAM,OAAO,YAAY,EAEvC,GAAI0L,IAAY,OAAQ,CACpB,GAAI,CAAC,OAAO,QAAS,MAAM,IAAI,UAAU,gCAAgC,EACzEA,EAAU1L,EAAM,OAAO,OAAO,EAElC,GAAI,OAAO0L,GAAY,WAAY,MAAM,IAAI,UAAU,wBAAwB,EAC/EF,EAAI,MAAM,KAAK,CAAE,MAAOxL,EAAO,QAAS0L,EAAS,MAAOD,CAAK,CAAE,OAE1DA,GACLD,EAAI,MAAM,KAAK,CAAE,MAAO,EAAM,GAElC,OAAOxL,CACf,EAEI,IAAI2L,GAAmB,OAAO,iBAAoB,WAAa,gBAAkB,SAAUpB,EAAOqB,EAAYC,EAAS,CACnH,IAAIjE,EAAI,IAAI,MAAMiE,CAAO,EACzB,OAAOjE,EAAE,KAAO,kBAAmBA,EAAE,MAAQ2C,EAAO3C,EAAE,WAAagE,EAAYhE,CACvF,EAEIlB,EAAqB,SAAU8E,EAAK,CAChC,SAASM,EAAKlE,EAAG,CACb4D,EAAI,MAAQA,EAAI,SAAW,IAAIG,GAAiB/D,EAAG4D,EAAI,MAAO,0CAA0C,EAAI5D,EAC5G4D,EAAI,SAAW,EAClB,CACD,SAAS/G,GAAO,CACZ,KAAO+G,EAAI,MAAM,QAAQ,CACrB,IAAIO,EAAMP,EAAI,MAAM,IAAG,EACvB,GAAI,CACA,IAAInJ,EAAS0J,EAAI,SAAWA,EAAI,QAAQ,KAAKA,EAAI,KAAK,EACtD,GAAIA,EAAI,MAAO,OAAO,QAAQ,QAAQ1J,CAAM,EAAE,KAAKoC,EAAM,SAASmD,EAAG,CAAE,OAAAkE,EAAKlE,CAAC,EAAUnD,EAAI,CAAG,CAAE,CACnG,OACMmD,EAAP,CACIkE,EAAKlE,CAAC,CACT,EAEL,GAAI4D,EAAI,SAAU,MAAMA,EAAI,KAC/B,CACD,OAAO/G,EAAI,CACnB,EAEI2C,EAAS,YAAaxC,CAAS,EAC/BwC,EAAS,WAAYvC,CAAQ,EAC7BuC,EAAS,SAAUtC,CAAM,EACzBsC,EAAS,aAAcrC,CAAU,EACjCqC,EAAS,UAAWpC,CAAO,EAC3BoC,EAAS,eAAgBnC,CAAY,EACrCmC,EAAS,oBAAqBlC,CAAiB,EAC/CkC,EAAS,YAAajC,CAAS,EAC/BiC,EAAS,oBAAqBhC,CAAiB,EAC/CgC,EAAS,aAAc/B,CAAU,EACjC+B,EAAS,YAAa9B,CAAS,EAC/B8B,EAAS,cAAe7B,CAAW,EACnC6B,EAAS,eAAgB5B,CAAY,EACrC4B,EAAS,kBAAmBZ,CAAe,EAC3CY,EAAS,WAAY3B,CAAQ,EAC7B2B,EAAS,SAAU1B,CAAM,EACzB0B,EAAS,WAAYzB,CAAQ,EAC7ByB,EAAS,iBAAkBxB,CAAc,EACzCwB,EAAS,gBAAiBvB,CAAa,EACvCuB,EAAS,UAAWtB,CAAO,EAC3BsB,EAAS,mBAAoBrB,CAAgB,EAC7CqB,EAAS,mBAAoBpB,CAAgB,EAC7CoB,EAAS,gBAAiBnB,CAAa,EACvCmB,EAAS,uBAAwBlB,CAAoB,EACrDkB,EAAS,eAAgBjB,CAAY,EACrCiB,EAAS,kBAAmBhB,CAAe,EAC3CgB,EAAS,yBAA0Bf,CAAsB,EACzDe,EAAS,yBAA0Bd,EAAsB,EACzDc,EAAS,wBAAyBb,EAAqB,EACvDa,EAAS,0BAA2BX,EAAuB,EAC3DW,EAAS,qBAAsBV,CAAkB,CACrD,CAAC,4CCnaD,OAAO,eAAe6D,GAAS,aAAc,CAAE,MAAO,EAAI,CAAE,EAC3CA,GAAA,UAAG,OACpB,IAAIyB,IACH,SAAUA,EAAW,CAElBA,EAAUA,EAAU,8BAAmC,CAAC,EAAI,gCAE5DA,EAAUA,EAAU,eAAoB,CAAC,EAAI,iBAE7CA,EAAUA,EAAU,mBAAwB,CAAC,EAAI,qBAEjDA,EAAUA,EAAU,qBAA0B,CAAC,EAAI,uBAEnDA,EAAUA,EAAU,sBAA2B,CAAC,EAAI,wBAEpDA,EAAUA,EAAU,sBAA2B,CAAC,EAAI,wBAEpDA,EAAUA,EAAU,wBAA6B,CAAC,EAAI,0BAEtDA,EAAUA,EAAU,2BAAgC,CAAC,EAAI,6BAEzDA,EAAUA,EAAU,uBAA4B,CAAC,EAAI,yBAErDA,EAAUA,EAAU,0BAA+B,EAAE,EAAI,4BAEzDA,EAAUA,EAAU,iCAAsC,EAAE,EAAI,mCAEhEA,EAAUA,EAAU,+BAAoC,EAAE,EAAI,iCAE9DA,EAAUA,EAAU,oCAAyC,EAAE,EAAI,sCAEnEA,EAAUA,EAAU,qCAA0C,EAAE,EAAI,uCAEpEA,EAAUA,EAAU,gCAAqC,EAAE,EAAI,kCAE/DA,EAAUA,EAAU,gCAAqC,EAAE,EAAI,kCAE/DA,EAAUA,EAAU,yCAA8C,EAAE,EAAI,2CAKxEA,EAAUA,EAAU,yCAA8C,EAAE,EAAI,2CAExEA,EAAUA,EAAU,iCAAsC,EAAE,EAAI,mCAKhEA,EAAUA,EAAU,mCAAwC,EAAE,EAAI,qCAIlEA,EAAUA,EAAU,mCAAwC,EAAE,EAAI,qCAElEA,EAAUA,EAAU,qBAA0B,EAAE,EAAI,uBAEpDA,EAAUA,EAAU,YAAiB,EAAE,EAAI,cAE3CA,EAAUA,EAAU,iBAAsB,EAAE,EAAI,mBAEhDA,EAAUA,EAAU,sBAA2B,EAAE,EAAI,wBAErDA,EAAUA,EAAU,aAAkB,EAAE,EAAI,cAChD,GAAGA,KAAczB,GAAA,UAAoByB,GAAY,CAAE,EAAC,iBChEpD,OAAO,eAAeC,EAAS,aAAc,CAAE,MAAO,EAAI,CAAE,EAC5DA,EAAA,oBAA8BA,EAAA,qBAA+BA,EAAA,mBAA6BA,EAAA,iBAA2BA,EAAA,aAAuBA,EAAA,eAAyBA,EAAA,gBAAiDA,EAAA,gBAAwBA,EAAA,cAAwBA,EAAA,cAA0BA,EAAA,oCAA+BA,EAAA,iBAA2BA,EAAA,cAAwBA,EAAA,KAAe,OACjY,IAAIC,IACH,SAAUA,EAAM,CAIbA,EAAKA,EAAK,QAAa,CAAC,EAAI,UAI5BA,EAAKA,EAAK,SAAc,CAAC,EAAI,WAI7BA,EAAKA,EAAK,OAAY,CAAC,EAAI,SAI3BA,EAAKA,EAAK,KAAU,CAAC,EAAI,OAIzBA,EAAKA,EAAK,KAAU,CAAC,EAAI,OAIzBA,EAAKA,EAAK,OAAY,CAAC,EAAI,SAI3BA,EAAKA,EAAK,OAAY,CAAC,EAAI,SAK3BA,EAAKA,EAAK,MAAW,CAAC,EAAI,QAI1BA,EAAKA,EAAK,IAAS,CAAC,EAAI,KAC5B,GAAGA,KAAqBD,EAAA,KAAGC,GAAO,GAAG,EACrC,IAAIC,IACH,SAAUA,EAAe,CACtBA,EAAcA,EAAc,OAAY,CAAC,EAAI,SAC7CA,EAAcA,EAAc,SAAc,CAAC,EAAI,UACnD,GAAGA,KAAuCF,EAAA,cAAGE,GAAgB,GAAG,EAIhE,SAASC,GAAiBC,EAAI,CAC1B,OAAOA,EAAG,OAASH,GAAK,OAC5B,CACwBD,EAAA,iBAAGG,GAC3B,SAASE,GAAkBD,EAAI,CAC3B,OAAOA,EAAG,OAASH,GAAK,QAC5B,CACyBD,EAAA,kBAAGK,GAC5B,SAASC,GAAgBF,EAAI,CACzB,OAAOA,EAAG,OAASH,GAAK,MAC5B,CACuBD,EAAA,gBAAGM,GAC1B,SAASC,GAAcH,EAAI,CACvB,OAAOA,EAAG,OAASH,GAAK,IAC5B,CACqBD,EAAA,cAAGO,GACxB,SAASC,GAAcJ,EAAI,CACvB,OAAOA,EAAG,OAASH,GAAK,IAC5B,CACqBD,EAAA,cAAGQ,GACxB,SAASC,GAAgBL,EAAI,CACzB,OAAOA,EAAG,OAASH,GAAK,MAC5B,CACuBD,EAAA,gBAAGS,GAC1B,SAASC,GAAgBN,EAAI,CACzB,OAAOA,EAAG,OAASH,GAAK,MAC5B,CACuBD,EAAA,gBAAGU,GAC1B,SAASC,GAAeP,EAAI,CACxB,OAAOA,EAAG,OAASH,GAAK,KAC5B,CACsBD,EAAA,eAAGW,GACzB,SAASC,GAAaR,EAAI,CACtB,OAAOA,EAAG,OAASH,GAAK,GAC5B,CACoBD,EAAA,aAAGY,GACvB,SAASC,GAAiBT,EAAI,CAC1B,MAAO,CAAC,EAAEA,GAAM,OAAOA,GAAO,UAAYA,EAAG,OAASF,GAAc,OACxE,CACwBF,EAAA,iBAAGa,GAC3B,SAASC,GAAmBV,EAAI,CAC5B,MAAO,CAAC,EAAEA,GAAM,OAAOA,GAAO,UAAYA,EAAG,OAASF,GAAc,SACxE,CAC0BF,EAAA,mBAAGc,GAC7B,SAASC,GAAqBhN,EAAO,CACjC,MAAO,CACH,KAAMkM,GAAK,QACX,MAAOlM,CACf,CACA,CAC4BiM,EAAA,qBAAGe,GAC/B,SAASC,GAAoBjN,EAAOkN,EAAO,CACvC,MAAO,CACH,KAAMhB,GAAK,OACX,MAAOlM,EACP,MAAOkN,CACf,CACA,CACAjB,EAAA,oBAA8BgB,aC5G9B,OAAO,eAAeE,GAAS,aAAc,CAAE,MAAO,EAAI,CAAE,EAC5DA,GAAA,kBAA4BA,GAAA,sBAAgC,OAE/BA,GAAA,sBAAG,+CAChCA,GAAA,kBAA4B,6FCJ5B,OAAO,eAAeC,GAAS,aAAc,CAAE,MAAO,EAAI,CAAE,EAC/BA,GAAA,sBAAG,OAMhC,IAAIC,EAAkB,4KAOtB,SAASC,EAAsBC,EAAU,CACrC,IAAIlL,EAAS,GACb,OAAAkL,EAAS,QAAQF,EAAiB,SAAUG,EAAO,CAC/C,IAAIC,EAAMD,EAAM,OAChB,OAAQA,EAAM,CAAC,EAAC,CAEZ,IAAK,IACDnL,EAAO,IAAMoL,IAAQ,EAAI,OAASA,IAAQ,EAAI,SAAW,QACzD,MAEJ,IAAK,IACDpL,EAAO,KAAOoL,IAAQ,EAAI,UAAY,UACtC,MACJ,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACD,MAAM,IAAI,WAAW,8DAA8D,EAEvF,IAAK,IACL,IAAK,IACD,MAAM,IAAI,WAAW,4CAA4C,EAErE,IAAK,IACL,IAAK,IACDpL,EAAO,MAAQ,CAAC,UAAW,UAAW,QAAS,OAAQ,QAAQ,EAAEoL,EAAM,CAAC,EACxE,MAEJ,IAAK,IACL,IAAK,IACD,MAAM,IAAI,WAAW,yCAAyC,EAClE,IAAK,IACDpL,EAAO,IAAM,CAAC,UAAW,SAAS,EAAEoL,EAAM,CAAC,EAC3C,MACJ,IAAK,IACL,IAAK,IACL,IAAK,IACD,MAAM,IAAI,WAAW,2DAA2D,EAEpF,IAAK,IACDpL,EAAO,QAAUoL,IAAQ,EAAI,OAASA,IAAQ,EAAI,SAAW,QAC7D,MACJ,IAAK,IACD,GAAIA,EAAM,EACN,MAAM,IAAI,WAAW,+CAA+C,EAExEpL,EAAO,QAAU,CAAC,QAAS,OAAQ,SAAU,OAAO,EAAEoL,EAAM,CAAC,EAC7D,MACJ,IAAK,IACD,GAAIA,EAAM,EACN,MAAM,IAAI,WAAW,+CAA+C,EAExEpL,EAAO,QAAU,CAAC,QAAS,OAAQ,SAAU,OAAO,EAAEoL,EAAM,CAAC,EAC7D,MAEJ,IAAK,IACDpL,EAAO,OAAS,GAChB,MACJ,IAAK,IACL,IAAK,IACD,MAAM,IAAI,WAAW,4DAA4D,EAErF,IAAK,IACDA,EAAO,UAAY,MACnBA,EAAO,KAAO,CAAC,UAAW,SAAS,EAAEoL,EAAM,CAAC,EAC5C,MACJ,IAAK,IACDpL,EAAO,UAAY,MACnBA,EAAO,KAAO,CAAC,UAAW,SAAS,EAAEoL,EAAM,CAAC,EAC5C,MACJ,IAAK,IACDpL,EAAO,UAAY,MACnBA,EAAO,KAAO,CAAC,UAAW,SAAS,EAAEoL,EAAM,CAAC,EAC5C,MACJ,IAAK,IACDpL,EAAO,UAAY,MACnBA,EAAO,KAAO,CAAC,UAAW,SAAS,EAAEoL,EAAM,CAAC,EAC5C,MACJ,IAAK,IACL,IAAK,IACL,IAAK,IACD,MAAM,IAAI,WAAW,kEAAkE,EAE3F,IAAK,IACDpL,EAAO,OAAS,CAAC,UAAW,SAAS,EAAEoL,EAAM,CAAC,EAC9C,MAEJ,IAAK,IACDpL,EAAO,OAAS,CAAC,UAAW,SAAS,EAAEoL,EAAM,CAAC,EAC9C,MACJ,IAAK,IACL,IAAK,IACD,MAAM,IAAI,WAAW,4DAA4D,EAErF,IAAK,IACDpL,EAAO,aAAeoL,EAAM,EAAI,QAAU,OAC1C,MACJ,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACD,MAAM,IAAI,WAAW,sEAAsE,CAClG,CACD,MAAO,EACf,CAAK,EACMpL,CACV,CACD,OAAA+K,GAAA,sBAAgCE,uDC3HhC,OAAO,eAAeH,GAAS,aAAc,CAAE,MAAO,EAAI,CAAE,EACnCA,GAAA,kBAAG,OAE5BA,GAAA,kBAA4B,sFCH5B,OAAO,eAAeO,GAAS,aAAc,CAAE,MAAO,EAAI,CAAE,EAC5DA,GAAA,oBAA8BA,GAAA,8BAAwC,OACtE,IAAIC,EAAUC,GACVC,EAAoBC,KACxB,SAASC,EAA8BR,EAAU,CAC7C,GAAIA,EAAS,SAAW,EACpB,MAAM,IAAI,MAAM,iCAAiC,EAOrD,QAJIS,EAAeT,EACd,MAAMM,EAAkB,iBAAiB,EACzC,OAAO,SAAU7O,EAAG,CAAE,OAAOA,EAAE,OAAS,CAAE,CAAE,EAC7CiP,EAAS,GACJC,EAAK,EAAGC,EAAiBH,EAAcE,EAAKC,EAAe,OAAQD,IAAM,CAC9E,IAAIE,EAAcD,EAAeD,CAAE,EAC/BG,EAAiBD,EAAY,MAAM,GAAG,EAC1C,GAAIC,EAAe,SAAW,EAC1B,MAAM,IAAI,MAAM,yBAAyB,EAG7C,QADIC,EAAOD,EAAe,CAAC,EAAGnL,EAAUmL,EAAe,MAAM,CAAC,EACrDE,EAAK,EAAGC,EAAYtL,EAASqL,EAAKC,EAAU,OAAQD,IAAM,CAC/D,IAAIE,EAASD,EAAUD,CAAE,EACzB,GAAIE,EAAO,SAAW,EAClB,MAAM,IAAI,MAAM,yBAAyB,EAGjDR,EAAO,KAAK,CAAE,KAAMK,EAAM,QAASpL,CAAO,CAAE,EAEhD,OAAO+K,CACV,CACoCP,GAAA,8BAAGK,EACxC,SAASW,EAAcC,EAAM,CACzB,OAAOA,EAAK,QAAQ,UAAW,EAAE,CACpC,CACD,IAAIC,EAA2B,mCAC3BC,EAA8B,wBAC9BC,EAAsB,0BACtBC,EAA8B,SAClC,SAASC,EAA0BC,EAAK,CACpC,IAAI5M,EAAS,GACb,OAAI4M,EAAIA,EAAI,OAAS,CAAC,IAAM,IACxB5M,EAAO,iBAAmB,gBAErB4M,EAAIA,EAAI,OAAS,CAAC,IAAM,MAC7B5M,EAAO,iBAAmB,iBAE9B4M,EAAI,QAAQJ,EAA6B,SAAU5O,EAAGiP,EAAIC,EAAI,CAE1D,OAAI,OAAOA,GAAO,UACd9M,EAAO,yBAA2B6M,EAAG,OACrC7M,EAAO,yBAA2B6M,EAAG,QAGhCC,IAAO,IACZ9M,EAAO,yBAA2B6M,EAAG,OAGhCA,EAAG,CAAC,IAAM,IACf7M,EAAO,yBAA2B6M,EAAG,QAIrC7M,EAAO,yBAA2B6M,EAAG,OACrC7M,EAAO,yBACH6M,EAAG,QAAU,OAAOC,GAAO,SAAWA,EAAG,OAAS,IAEnD,EACf,CAAK,EACM9M,CACV,CACD,SAAS+M,EAAUH,EAAK,CACpB,OAAQA,EAAG,CACP,IAAK,YACD,MAAO,CACH,YAAa,MAC7B,EACQ,IAAK,kBACL,IAAK,KACD,MAAO,CACH,aAAc,YAC9B,EACQ,IAAK,cACL,IAAK,KACD,MAAO,CACH,YAAa,QAC7B,EACQ,IAAK,yBACL,IAAK,MACD,MAAO,CACH,YAAa,SACb,aAAc,YAC9B,EACQ,IAAK,mBACL,IAAK,KACD,MAAO,CACH,YAAa,YAC7B,EACQ,IAAK,8BACL,IAAK,MACD,MAAO,CACH,YAAa,aACb,aAAc,YAC9B,EACQ,IAAK,aACL,IAAK,KACD,MAAO,CACH,YAAa,OAC7B,CACK,CACJ,CACD,SAASI,EAAyCf,EAAM,CAEpD,IAAIjM,EAaJ,GAZIiM,EAAK,CAAC,IAAM,KAAOA,EAAK,CAAC,IAAM,KAC/BjM,EAAS,CACL,SAAU,aACtB,EACQiM,EAAOA,EAAK,MAAM,CAAC,GAEdA,EAAK,CAAC,IAAM,MACjBjM,EAAS,CACL,SAAU,YACtB,EACQiM,EAAOA,EAAK,MAAM,CAAC,GAEnBjM,EAAQ,CACR,IAAIiN,EAAchB,EAAK,MAAM,EAAG,CAAC,EASjC,GARIgB,IAAgB,MAChBjN,EAAO,YAAc,SACrBiM,EAAOA,EAAK,MAAM,CAAC,GAEdgB,IAAgB,OACrBjN,EAAO,YAAc,aACrBiM,EAAOA,EAAK,MAAM,CAAC,GAEnB,CAACS,EAA4B,KAAKT,CAAI,EACtC,MAAM,IAAI,MAAM,2CAA2C,EAE/DjM,EAAO,qBAAuBiM,EAAK,OAEvC,OAAOjM,CACV,CACD,SAASkN,EAAqBC,EAAK,CAC/B,IAAInN,EAAS,GACToN,EAAWL,EAAUI,CAAG,EAC5B,OAAIC,GAGGpN,CACV,CAID,SAASqN,EAAoBzB,EAAQ,CAEjC,QADI5L,EAAS,GACJ6L,EAAK,EAAGyB,EAAW1B,EAAQC,EAAKyB,EAAS,OAAQzB,IAAM,CAC5D,IAAI0B,EAAQD,EAASzB,CAAE,EACvB,OAAQ0B,EAAM,KAAI,CACd,IAAK,UACL,IAAK,IACDvN,EAAO,MAAQ,UACf,SACJ,IAAK,QACDA,EAAO,MAAQ,UACfA,EAAO,MAAQ,IACf,SACJ,IAAK,WACDA,EAAO,MAAQ,WACfA,EAAO,SAAWuN,EAAM,QAAQ,CAAC,EACjC,SACJ,IAAK,YACL,IAAK,KACDvN,EAAO,YAAc,GACrB,SACJ,IAAK,oBACL,IAAK,IACDA,EAAO,sBAAwB,EAC/B,SACJ,IAAK,eACL,IAAK,OACDA,EAAO,MAAQ,OACfA,EAAO,KAAOqM,EAAckB,EAAM,QAAQ,CAAC,CAAC,EAC5C,SACJ,IAAK,gBACL,IAAK,IACDvN,EAAO,SAAW,UAClBA,EAAO,eAAiB,QACxB,SACJ,IAAK,eACL,IAAK,KACDA,EAAO,SAAW,UAClBA,EAAO,eAAiB,OACxB,SACJ,IAAK,aACDA,EAASsL,EAAQ,SAASA,EAAQ,SAASA,EAAQ,SAAS,CAAE,EAAEtL,CAAM,EAAG,CAAE,SAAU,YAAY,CAAE,EAAGuN,EAAM,QAAQ,OAAO,SAAUC,EAAKL,EAAK,CAAE,OAAQ7B,EAAQ,SAASA,EAAQ,SAAS,GAAIkC,CAAG,EAAGN,EAAqBC,CAAG,CAAC,CAAK,EAAE,CAAE,EAAC,EACxO,SACJ,IAAK,cACDnN,EAASsL,EAAQ,SAASA,EAAQ,SAASA,EAAQ,SAAS,CAAE,EAAEtL,CAAM,EAAG,CAAE,SAAU,aAAa,CAAE,EAAGuN,EAAM,QAAQ,OAAO,SAAUC,EAAKL,EAAK,CAAE,OAAQ7B,EAAQ,SAASA,EAAQ,SAAS,GAAIkC,CAAG,EAAGN,EAAqBC,CAAG,CAAC,CAAK,EAAE,CAAE,EAAC,EACzO,SACJ,IAAK,kBACDnN,EAAO,SAAW,WAClB,SAEJ,IAAK,oBACDA,EAAO,gBAAkB,eACzBA,EAAO,YAAc,SACrB,SACJ,IAAK,mBACDA,EAAO,gBAAkB,OACzBA,EAAO,YAAc,QACrB,SACJ,IAAK,uBACDA,EAAO,gBAAkB,OACzBA,EAAO,YAAc,OACrB,SACJ,IAAK,sBACDA,EAAO,gBAAkB,SACzB,SACJ,IAAK,QACDA,EAAO,MAAQ,WAAWuN,EAAM,QAAQ,CAAC,CAAC,EAC1C,SAEJ,IAAK,gBACD,GAAIA,EAAM,QAAQ,OAAS,EACvB,MAAM,IAAI,WAAW,0DAA0D,EAEnFA,EAAM,QAAQ,CAAC,EAAE,QAAQd,EAAqB,SAAU7O,EAAGiP,EAAIC,EAAIW,EAAIC,EAAIC,EAAI,CAC3E,GAAId,EACA7M,EAAO,qBAAuB8M,EAAG,WAEhC,IAAIW,GAAMC,EACX,MAAM,IAAI,MAAM,oDAAoD,EAEnE,GAAIC,EACL,MAAM,IAAI,MAAM,kDAAkD,EAEtE,MAAO,EAC3B,CAAiB,EACD,QACP,CAED,GAAIjB,EAA4B,KAAKa,EAAM,IAAI,EAAG,CAC9CvN,EAAO,qBAAuBuN,EAAM,KAAK,OACzC,SAEJ,GAAIhB,EAAyB,KAAKgB,EAAM,IAAI,EAAG,CAI3C,GAAIA,EAAM,QAAQ,OAAS,EACvB,MAAM,IAAI,WAAW,+DAA+D,EAExFA,EAAM,KAAK,QAAQhB,EAA0B,SAAU3O,EAAGiP,EAAIC,EAAIW,EAAIC,EAAIC,EAAI,CAE1E,OAAIb,IAAO,IACP9M,EAAO,sBAAwB6M,EAAG,OAG7BY,GAAMA,EAAG,CAAC,IAAM,IACrBzN,EAAO,sBAAwByN,EAAG,OAG7BC,GAAMC,GACX3N,EAAO,sBAAwB0N,EAAG,OAClC1N,EAAO,sBAAwB0N,EAAG,OAASC,EAAG,SAG9C3N,EAAO,sBAAwB6M,EAAG,OAClC7M,EAAO,sBAAwB6M,EAAG,QAE/B,EACvB,CAAa,EACD,IAAIM,EAAMI,EAAM,QAAQ,CAAC,EAErBJ,IAAQ,IACRnN,EAASsL,EAAQ,SAASA,EAAQ,SAAS,GAAItL,CAAM,EAAG,CAAE,oBAAqB,gBAAkB,GAE5FmN,IACLnN,EAASsL,EAAQ,SAASA,EAAQ,SAAS,GAAItL,CAAM,EAAG2M,EAA0BQ,CAAG,CAAC,GAE1F,SAGJ,GAAIX,EAA4B,KAAKe,EAAM,IAAI,EAAG,CAC9CvN,EAASsL,EAAQ,SAASA,EAAQ,SAAS,GAAItL,CAAM,EAAG2M,EAA0BY,EAAM,IAAI,CAAC,EAC7F,SAEJ,IAAIH,EAAWL,EAAUQ,EAAM,IAAI,EAC/BH,IACApN,EAASsL,EAAQ,SAASA,EAAQ,SAAS,GAAItL,CAAM,EAAGoN,CAAQ,GAEpE,IAAIQ,EAAsCZ,EAAyCO,EAAM,IAAI,EACzFK,IACA5N,EAASsL,EAAQ,SAASA,EAAQ,SAAS,GAAItL,CAAM,EAAG4N,CAAmC,GAGnG,OAAO5N,CACV,CACDqL,UAAA,oBAA8BgC,kBC1S9B,OAAO,eAAc1I,EAAU,aAAc,CAAE,MAAO,EAAI,CAAE,EAC5D,IAAI2G,EAAUC,GACdD,EAAQ,aAAaG,GAAsB,EAAE9G,CAAO,EACpD2G,EAAQ,aAAauC,GAAmB,EAAElJ,CAAO,wBCHjD,OAAO,eAAemJ,GAAS,aAAc,CAAE,MAAO,EAAI,CAAE,EAC5CA,GAAA,SAAG,OAGnBA,GAAA,SAAmB,CACf,MAAO,CACH,IACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,KACA,KACA,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,KACA,GACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,KACA,KACA,IACA,GACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,KACA,GACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACA,GACH,EACD,GAAM,CACF,KACA,KACA,IACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,KACA,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,KACA,KACA,IACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,KACA,KACA,IACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,KACA,KACA,IACA,GACH,EACD,GAAM,CACF,KACA,KACA,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,KACA,KACA,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,GAAM,CACF,IACA,KACA,GACH,EACD,GAAM,CACF,IACA,KACA,KACA,GACH,EACD,GAAM,CACF,IACA,IACH,EACD,GAAM,CACF,IACA,IACA,KACA,IACH,EACD,GAAM,CACF,IACA,KACA,IACA,IACH,EACD,GAAM,CACF,IACA,GACH,EACD,QAAS,CACL,IACA,IACA,KACA,IACH,EACD,SAAU,CACN,IACA,KACA,KACA,GACH,EACD,QAAS,CACL,IACA,IACA,IACH,EACD,SAAU,CACN,IACA,KACA,IACA,IACH,EACD,QAAS,CACL,IACA,IACA,KACA,IACH,EACD,QAAS,CACL,IACA,IACA,KACA,IACH,EACD,QAAS,CACL,IACA,IACA,KACA,IACH,EACD,QAAS,CACL,IACA,IACA,KACA,IACH,EACD,QAAS,CACL,IACA,IACA,KACA,IACH,EACD,QAAS,CACL,IACA,IACA,KACA,IACH,EACD,QAAS,CACL,IACA,IACA,IACH,EACD,QAAS,CACL,IACA,IACA,IACH,EACD,QAAS,CACL,KACA,KACA,IACA,GACH,EACD,QAAS,CACL,KACA,IACA,GACH,EACD,QAAS,CACL,IACA,IACA,IACH,EACD,QAAS,CACL,IACA,IACA,IACH,EACD,QAAS,CACL,KACA,IACA,GACH,EACD,QAAS,CACL,KACA,IACA,GACH,EACD,QAAS,CACL,KACA,KACA,IACA,GACH,EACD,QAAS,CACL,KACA,KACA,IACA,GACH,EACD,QAAS,CACL,KACA,IACA,KACA,GACH,EACD,QAAS,CACL,KACA,IACA,GACH,EACD,QAAS,CACL,IACA,KACA,KACA,GACH,CACL,ECp4CA,OAAO,eAAeC,GAAS,aAAc,CAAE,MAAO,EAAI,CAAE,EACtCA,GAAA,eAAG,OACzB,IAAIC,GAAwBzC,GAQ5B,SAAS0C,GAAe/C,EAAUgD,EAAQ,CAEtC,QADIC,EAAe,GACVC,EAAa,EAAGA,EAAalD,EAAS,OAAQkD,IAAc,CACjE,IAAIC,EAAcnD,EAAS,OAAOkD,CAAU,EAC5C,GAAIC,IAAgB,IAAK,CAErB,QADIC,EAAc,EACXF,EAAa,EAAIlD,EAAS,QAC7BA,EAAS,OAAOkD,EAAa,CAAC,IAAMC,GACpCC,IACAF,IAEJ,IAAIG,EAAU,GAAKD,EAAc,GAC7BE,EAAeF,EAAc,EAAI,EAAI,GAAKA,GAAe,GACzDG,EAAgB,IAChBC,EAAWC,GAA+BT,CAAM,EAIpD,KAHIQ,GAAY,KAAOA,GAAY,OAC/BF,EAAe,GAEZA,KAAiB,GACpBL,GAAgBM,EAEpB,KAAOF,KAAY,GACfJ,EAAeO,EAAWP,OAGzBE,IAAgB,IACrBF,GAAgB,IAGhBA,GAAgBE,EAGxB,OAAOF,CACX,CACsBJ,GAAA,eAAGE,GAMzB,SAASU,GAA+BT,EAAQ,CAC5C,IAAIU,EAAYV,EAAO,UASvB,GARIU,IAAc,QAEdV,EAAO,YAEPA,EAAO,WAAW,SAElBU,EAAYV,EAAO,WAAW,CAAC,GAE/BU,EACA,OAAQA,EAAS,CACb,IAAK,MACD,MAAO,IACX,IAAK,MACD,MAAO,IACX,IAAK,MACD,MAAO,IACX,IAAK,MACD,MAAO,IACX,QACI,MAAM,IAAI,MAAM,mBAAmB,CAC1C,CAGL,IAAIC,EAAcX,EAAO,SACrBY,EACAD,IAAgB,SAChBC,EAAYZ,EAAO,SAAU,EAAC,QAElC,IAAIa,EAAaf,GAAsB,SAASc,GAAa,EAAE,GAC3Dd,GAAsB,SAASa,GAAe,EAAE,GAChDb,GAAsB,SAAS,GAAG,OAAOa,EAAa,MAAM,CAAC,GAC7Db,GAAsB,SAAS,KAAK,EACxC,OAAOe,EAAW,CAAC,CACvB,CCrFA,IAAI7C,GACJ,OAAO,eAAe8C,GAAS,aAAc,CAAE,MAAO,EAAI,CAAE,EAC9CA,GAAA,OAAG,OACjB,IAAI1D,GAAUC,GACV0D,EAAUxD,GACVyD,GAAUrB,EACVrC,GAAoB2D,GACpBC,GAAwBC,GACxBC,GAAgCC,GAChCC,GAA8B,IAAI,OAAO,IAAI,OAAOhE,GAAkB,sBAAsB,OAAQ,GAAG,CAAC,EACxGiE,GAA4B,IAAI,OAAO,GAAG,OAAOjE,GAAkB,sBAAsB,OAAQ,IAAI,CAAC,EAC1G,SAASkE,EAAehR,EAAOiR,EAAK,CAChC,MAAO,CAAE,MAAOjR,EAAO,IAAKiR,CAAG,CACnC,CAGA,IAAIC,GAAsB,CAAC,CAAC,OAAO,UAAU,YAAc,KAAK,WAAW,IAAK,CAAC,EAC7EC,GAAyB,CAAC,CAAC,OAAO,cAClCC,GAAuB,CAAC,CAAC,OAAO,YAChCC,GAAuB,CAAC,CAAC,OAAO,UAAU,YAC1CC,GAAe,CAAC,CAAC,OAAO,UAAU,UAClCC,GAAa,CAAC,CAAC,OAAO,UAAU,QAChCC,GAAyB,CAAC,CAAC,OAAO,cAClCC,GAAgBD,GACd,OAAO,cACP,SAAU5K,EAAG,CACX,OAAQ,OAAOA,GAAM,UACjB,SAASA,CAAC,GACV,KAAK,MAAMA,CAAC,IAAMA,GAClB,KAAK,IAAIA,CAAC,GAAK,gBAC3B,EAEI8K,GAAyB,GAC7B,GAAI,CACA,IAAIC,GAAKC,GAAG,4CAA6C,IAAI,EAO7DF,KAA2BlE,GAAKmE,GAAG,KAAK,GAAG,KAAO,MAAQnE,KAAO,OAAS,OAASA,GAAG,CAAC,KAAO,GAClG,MACA,CACIkE,GAAyB,EAC7B,CACA,IAAIG,GAAaX,GAET,SAAoBvK,EAAGmL,EAAQC,EAAU,CACrC,OAAOpL,EAAE,WAAWmL,EAAQC,CAAQ,CACvC,EAED,SAAoBpL,EAAGmL,EAAQC,EAAU,CACrC,OAAOpL,EAAE,MAAMoL,EAAUA,EAAWD,EAAO,MAAM,IAAMA,CACnE,EACIE,GAAgBb,GACd,OAAO,cAEL,UAAyB,CAErB,QADIc,EAAa,GACR9E,EAAK,EAAGA,EAAK,UAAU,OAAQA,IACpC8E,EAAW9E,CAAE,EAAI,UAAUA,CAAE,EAMjC,QAJI+E,EAAW,GACXC,EAASF,EAAW,OACpB,EAAI,EACJG,EACGD,EAAS,GAAG,CAEf,GADAC,EAAOH,EAAW,GAAG,EACjBG,EAAO,QACP,MAAM,WAAWA,EAAO,4BAA4B,EACxDF,GACIE,EAAO,MACD,OAAO,aAAaA,CAAI,EACxB,OAAO,eAAeA,GAAQ,QAAY,IAAM,MAASA,EAAO,KAAS,KAAM,EAE7F,OAAOF,CACnB,EACIG,GAEJjB,GACM,OAAO,YAEL,SAAqBkB,EAAS,CAE1B,QADIC,EAAM,GACDpF,EAAK,EAAGqF,EAAYF,EAASnF,EAAKqF,EAAU,OAAQrF,IAAM,CAC/D,IAAIK,EAAKgF,EAAUrF,CAAE,EAAG9D,EAAImE,EAAG,CAAC,EAAGpH,EAAIoH,EAAG,CAAC,EAC3C+E,EAAIlJ,CAAC,EAAIjD,EAEb,OAAOmM,CACnB,EACIE,GAAcpB,GAEV,SAAqB1K,EAAG+L,EAAO,CAC3B,OAAO/L,EAAE,YAAY+L,CAAK,CAC7B,EAED,SAAqB/L,EAAG+L,EAAO,CAC3B,IAAIC,EAAOhM,EAAE,OACb,GAAI,EAAA+L,EAAQ,GAAKA,GAASC,GAG1B,KAAIC,EAAQjM,EAAE,WAAW+L,CAAK,EAC1BG,EACJ,OAAOD,EAAQ,OACXA,EAAQ,OACRF,EAAQ,IAAMC,IACbE,EAASlM,EAAE,WAAW+L,EAAQ,CAAC,GAAK,OACrCG,EAAS,MACPD,GACEA,EAAQ,OAAW,KAAOC,EAAS,OAAU,MACjE,EACIC,GAAYxB,GAER,SAAmB3K,EAAG,CAClB,OAAOA,EAAE,WACZ,EAED,SAAmBA,EAAG,CAClB,OAAOA,EAAE,QAAQmK,GAA6B,EAAE,CAC5D,EACIiC,GAAUxB,GAEN,SAAiB5K,EAAG,CAChB,OAAOA,EAAE,SACZ,EAED,SAAiBA,EAAG,CAChB,OAAOA,EAAE,QAAQoK,GAA2B,EAAE,CAC1D,EAEA,SAASa,GAAGjL,EAAGqM,EAAM,CACjB,OAAO,IAAI,OAAOrM,EAAGqM,CAAI,CAC7B,CAEA,IAAIC,GACJ,GAAIvB,GAAwB,CAExB,IAAIwB,GAAyBtB,GAAG,4CAA6C,IAAI,EACjFqB,GAAyB,SAAgCtM,EAAG+L,EAAO,CAC/D,IAAIlF,EACJ0F,GAAuB,UAAYR,EACnC,IAAIjG,EAAQyG,GAAuB,KAAKvM,CAAC,EACzC,OAAQ6G,EAAKf,EAAM,CAAC,KAAO,MAAQe,IAAO,OAASA,EAAK,EAChE,OAIIyF,GAAyB,SAAgCtM,EAAG+L,EAAO,CAE/D,QADIjG,EAAQ,KACC,CACT,IAAIzF,EAAIyL,GAAY9L,EAAG+L,CAAK,EAC5B,GAAI1L,IAAM,QAAamM,GAAcnM,CAAC,GAAKoM,GAAiBpM,CAAC,EACzD,MAEJyF,EAAM,KAAKzF,CAAC,EACZ0L,GAAS1L,GAAK,MAAU,EAAI,EAEhC,OAAOgL,GAAc,MAAM,OAAQvF,CAAK,CAChD,EAEA,IAAI4G,GAAwB,UAAY,CACpC,SAASA,EAAOvI,EAAS3I,EAAS,CAC1BA,IAAY,SAAUA,EAAU,CAAE,GACtC,KAAK,QAAU2I,EACf,KAAK,SAAW,CAAE,OAAQ,EAAG,KAAM,EAAG,OAAQ,GAC9C,KAAK,UAAY,CAAC,CAAC3I,EAAQ,UAC3B,KAAK,OAASA,EAAQ,OACtB,KAAK,oBAAsB,CAAC,CAACA,EAAQ,oBACrC,KAAK,qBAAuB,CAAC,CAACA,EAAQ,oBACzC,CACD,OAAAkR,EAAO,UAAU,MAAQ,UAAY,CACjC,GAAI,KAAK,OAAQ,IAAK,EAClB,MAAM,MAAM,8BAA8B,EAE9C,OAAO,KAAK,aAAa,EAAG,GAAI,EAAK,CAC7C,EACIA,EAAO,UAAU,aAAe,SAAUC,EAAcC,EAAeC,EAAmB,CAEtF,QADItB,EAAW,GACR,CAAC,KAAK,SAAS,CAClB,IAAIuB,EAAO,KAAK,OAChB,GAAIA,IAAS,IAAe,CACxB,IAAInS,EAAS,KAAK,cAAcgS,EAAcE,CAAiB,EAC/D,GAAIlS,EAAO,IACP,OAAOA,EAEX4Q,EAAS,KAAK5Q,EAAO,GAAG,MAEvB,IAAImS,IAAS,KAAiBH,EAAe,EAC9C,MAEC,GAAIG,IAAS,KACbF,IAAkB,UAAYA,IAAkB,iBAAkB,CACnE,IAAIxB,EAAW,KAAK,gBACpB,KAAK,KAAI,EACTG,EAAS,KAAK,CACV,KAAM1B,GAAQ,KAAK,MACnB,SAAUQ,EAAee,EAAU,KAAK,cAAa,CAAE,CAC3E,CAAiB,UAEI0B,IAAS,IACd,CAAC,KAAK,WACN,KAAK,KAAI,IAAO,GAClB,CACE,GAAID,EACA,MAGA,OAAO,KAAK,MAAMjD,EAAQ,UAAU,sBAAuBS,EAAe,KAAK,cAAa,EAAI,KAAK,cAAa,CAAE,CAAC,UAGpHyC,IAAS,IACd,CAAC,KAAK,WACNC,GAAS,KAAK,KAAM,GAAI,CAAC,EAAG,CAC5B,IAAIpS,EAAS,KAAK,SAASgS,EAAcC,CAAa,EACtD,GAAIjS,EAAO,IACP,OAAOA,EAEX4Q,EAAS,KAAK5Q,EAAO,GAAG,MAEvB,CACD,IAAIA,EAAS,KAAK,aAAagS,EAAcC,CAAa,EAC1D,GAAIjS,EAAO,IACP,OAAOA,EAEX4Q,EAAS,KAAK5Q,EAAO,GAAG,IAGhC,MAAO,CAAE,IAAK4Q,EAAU,IAAK,IAAI,CACzC,EAmBImB,EAAO,UAAU,SAAW,SAAUC,EAAcC,EAAe,CAC/D,IAAII,EAAgB,KAAK,gBACzB,KAAK,KAAI,EACT,IAAIC,EAAU,KAAK,eAEnB,GADA,KAAK,UAAS,EACV,KAAK,OAAO,IAAI,EAEhB,MAAO,CACH,IAAK,CACD,KAAMpD,GAAQ,KAAK,QACnB,MAAO,IAAI,OAAOoD,EAAS,IAAI,EAC/B,SAAU5C,EAAe2C,EAAe,KAAK,cAAa,CAAE,CAC/D,EACD,IAAK,IACrB,EAEa,GAAI,KAAK,OAAO,GAAG,EAAG,CACvB,IAAIE,EAAiB,KAAK,aAAaP,EAAe,EAAGC,EAAe,EAAI,EAC5E,GAAIM,EAAe,IACf,OAAOA,EAEX,IAAIC,EAAWD,EAAe,IAE1BE,EAAsB,KAAK,gBAC/B,GAAI,KAAK,OAAO,IAAI,EAAG,CACnB,GAAI,KAAK,SAAW,CAACL,GAAS,KAAK,KAAI,CAAE,EACrC,OAAO,KAAK,MAAMnD,EAAQ,UAAU,YAAaS,EAAe+C,EAAqB,KAAK,cAAa,CAAE,CAAC,EAE9G,IAAIC,EAA8B,KAAK,gBACnCC,EAAiB,KAAK,eAC1B,OAAIL,IAAYK,EACL,KAAK,MAAM1D,EAAQ,UAAU,sBAAuBS,EAAegD,EAA6B,KAAK,cAAa,CAAE,CAAC,GAEhI,KAAK,UAAS,EACT,KAAK,OAAO,GAAG,EAGb,CACH,IAAK,CACD,KAAMxD,GAAQ,KAAK,IACnB,MAAOoD,EACP,SAAUE,EACV,SAAU9C,EAAe2C,EAAe,KAAK,cAAa,CAAE,CAC/D,EACD,IAAK,IACzB,EAV2B,KAAK,MAAMpD,EAAQ,UAAU,YAAaS,EAAe+C,EAAqB,KAAK,cAAa,CAAE,CAAC,OAa9G,QAAO,KAAK,MAAMxD,EAAQ,UAAU,aAAcS,EAAe2C,EAAe,KAAK,cAAa,CAAE,CAAC,MAIzG,QAAO,KAAK,MAAMpD,EAAQ,UAAU,YAAaS,EAAe2C,EAAe,KAAK,cAAa,CAAE,CAAC,CAEhH,EAIIN,EAAO,UAAU,aAAe,UAAY,CACxC,IAAIa,EAAc,KAAK,SAEvB,IADA,KAAK,KAAI,EACF,CAAC,KAAK,MAAO,GAAIC,GAA4B,KAAK,KAAI,CAAE,GAC3D,KAAK,KAAI,EAEb,OAAO,KAAK,QAAQ,MAAMD,EAAa,KAAK,OAAM,CAAE,CAC5D,EACIb,EAAO,UAAU,aAAe,SAAUC,EAAcC,EAAe,CAGnE,QAFIvT,EAAQ,KAAK,gBACbf,EAAQ,KACC,CACT,IAAImV,EAAmB,KAAK,cAAcb,CAAa,EACvD,GAAIa,EAAkB,CAClBnV,GAASmV,EACT,SAEJ,IAAIC,EAAsB,KAAK,iBAAiBf,EAAcC,CAAa,EAC3E,GAAIc,EAAqB,CACrBpV,GAASoV,EACT,SAEJ,IAAIC,EAAuB,KAAK,2BAChC,GAAIA,EAAsB,CACtBrV,GAASqV,EACT,SAEJ,MAEJ,IAAIC,EAAWvD,EAAehR,EAAO,KAAK,cAAe,GACzD,MAAO,CACH,IAAK,CAAE,KAAMwQ,GAAQ,KAAK,QAAS,MAAOvR,EAAO,SAAUsV,CAAU,EACrE,IAAK,IACjB,CACA,EACIlB,EAAO,UAAU,yBAA2B,UAAY,CACpD,MAAI,CAAC,KAAK,MAAO,GACb,KAAK,KAAI,IAAO,KACf,KAAK,WAEF,CAACmB,GAAgB,KAAK,KAAI,GAAM,CAAC,IACrC,KAAK,KAAI,EACF,KAEJ,IACf,EAMInB,EAAO,UAAU,cAAgB,SAAUE,EAAe,CACtD,GAAI,KAAK,MAAO,GAAI,KAAK,KAAI,IAAO,GAChC,OAAO,KAIX,OAAQ,KAAK,KAAM,GACf,IAAK,IAED,YAAK,KAAI,EACT,KAAK,KAAI,EACF,IAEX,IAAK,KACL,IAAK,IACL,IAAK,IACL,IAAK,KACD,MACJ,IAAK,IACD,GAAIA,IAAkB,UAAYA,IAAkB,gBAChD,MAEJ,OAAO,KACX,QACI,OAAO,IACd,CACD,KAAK,KAAI,EACT,IAAItB,EAAa,CAAC,KAAK,KAAM,GAG7B,IAFA,KAAK,KAAI,EAEF,CAAC,KAAK,SAAS,CAClB,IAAIwC,EAAK,KAAK,OACd,GAAIA,IAAO,GACP,GAAI,KAAK,KAAM,IAAK,GAChBxC,EAAW,KAAK,EAAE,EAElB,KAAK,KAAI,MAER,CAED,KAAK,KAAI,EACT,WAIJA,EAAW,KAAKwC,CAAE,EAEtB,KAAK,KAAI,EAEb,OAAOzC,GAAc,MAAM,OAAQC,CAAU,CACrD,EACIoB,EAAO,UAAU,iBAAmB,SAAUC,EAAcC,EAAe,CACvE,GAAI,KAAK,QACL,OAAO,KAEX,IAAIkB,EAAK,KAAK,OACd,OAAIA,IAAO,IACPA,IAAO,KACNA,IAAO,KACHlB,IAAkB,UAAYA,IAAkB,kBACpDkB,IAAO,KAAiBnB,EAAe,EACjC,MAGP,KAAK,KAAI,EACFtB,GAAcyC,CAAE,EAEnC,EACIpB,EAAO,UAAU,cAAgB,SAAUC,EAAcE,EAAmB,CACxE,IAAIkB,EAAuB,KAAK,gBAGhC,GAFA,KAAK,KAAI,EACT,KAAK,UAAS,EACV,KAAK,QACL,OAAO,KAAK,MAAMnE,EAAQ,UAAU,8BAA+BS,EAAe0D,EAAsB,KAAK,cAAa,CAAE,CAAC,EAEjI,GAAI,KAAK,KAAM,IAAK,IAChB,YAAK,KAAI,EACF,KAAK,MAAMnE,EAAQ,UAAU,eAAgBS,EAAe0D,EAAsB,KAAK,cAAa,CAAE,CAAC,EAGlH,IAAIzV,EAAQ,KAAK,0BAAyB,EAAG,MAC7C,GAAI,CAACA,EACD,OAAO,KAAK,MAAMsR,EAAQ,UAAU,mBAAoBS,EAAe0D,EAAsB,KAAK,cAAa,CAAE,CAAC,EAGtH,GADA,KAAK,UAAS,EACV,KAAK,QACL,OAAO,KAAK,MAAMnE,EAAQ,UAAU,8BAA+BS,EAAe0D,EAAsB,KAAK,cAAa,CAAE,CAAC,EAEjI,OAAQ,KAAK,KAAM,GAEf,IAAK,KACD,YAAK,KAAI,EACF,CACH,IAAK,CACD,KAAMlE,GAAQ,KAAK,SAEnB,MAAOvR,EACP,SAAU+R,EAAe0D,EAAsB,KAAK,cAAa,CAAE,CACtE,EACD,IAAK,IACzB,EAGY,IAAK,IAGD,OAFA,KAAK,KAAI,EACT,KAAK,UAAS,EACV,KAAK,QACE,KAAK,MAAMnE,EAAQ,UAAU,8BAA+BS,EAAe0D,EAAsB,KAAK,cAAa,CAAE,CAAC,EAE1H,KAAK,qBAAqBpB,EAAcE,EAAmBvU,EAAOyV,CAAoB,EAEjG,QACI,OAAO,KAAK,MAAMnE,EAAQ,UAAU,mBAAoBS,EAAe0D,EAAsB,KAAK,cAAa,CAAE,CAAC,CACzH,CACT,EAKIrB,EAAO,UAAU,0BAA4B,UAAY,CACrD,IAAIsB,EAAmB,KAAK,gBACxBT,EAAc,KAAK,SACnBjV,EAAQgU,GAAuB,KAAK,QAASiB,CAAW,EACxDU,EAAYV,EAAcjV,EAAM,OACpC,KAAK,OAAO2V,CAAS,EACrB,IAAIC,EAAc,KAAK,gBACnBN,EAAWvD,EAAe2D,EAAkBE,CAAW,EAC3D,MAAO,CAAE,MAAO5V,EAAO,SAAUsV,CAAQ,CACjD,EACIlB,EAAO,UAAU,qBAAuB,SAAUC,EAAcE,EAAmBvU,EAAOyV,EAAsB,CAC5G,IAAIlH,EAIAsH,EAAoB,KAAK,gBACzBC,EAAU,KAAK,0BAAyB,EAAG,MAC3CC,EAAkB,KAAK,gBAC3B,OAAQD,EAAO,CACX,IAAK,GAED,OAAO,KAAK,MAAMxE,EAAQ,UAAU,qBAAsBS,EAAe8D,EAAmBE,CAAe,CAAC,EAChH,IAAK,SACL,IAAK,OACL,IAAK,OAAQ,CAIT,KAAK,UAAS,EACd,IAAIC,EAAmB,KACvB,GAAI,KAAK,OAAO,GAAG,EAAG,CAClB,KAAK,UAAS,EACd,IAAIC,EAAqB,KAAK,gBAC1B5T,EAAS,KAAK,gCAClB,GAAIA,EAAO,IACP,OAAOA,EAEX,IAAI6K,EAAQ4G,GAAQzR,EAAO,GAAG,EAC9B,GAAI6K,EAAM,SAAW,EACjB,OAAO,KAAK,MAAMoE,EAAQ,UAAU,sBAAuBS,EAAe,KAAK,cAAa,EAAI,KAAK,cAAa,CAAE,CAAC,EAEzH,IAAImE,EAAgBnE,EAAekE,EAAoB,KAAK,cAAe,GAC3ED,EAAmB,CAAE,MAAO9I,EAAO,cAAegJ,CAAa,EAEnE,IAAIC,EAAiB,KAAK,sBAAsBV,CAAoB,EACpE,GAAIU,EAAe,IACf,OAAOA,EAEX,IAAIC,EAAarE,EAAe0D,EAAsB,KAAK,cAAe,GAE1E,GAAIO,GAAoBpD,GAA+EoD,GAAiB,MAAO,KAAM,CAAC,EAAG,CAErI,IAAIzI,EAAWsG,GAAUmC,EAAiB,MAAM,MAAM,CAAC,CAAC,EACxD,GAAIF,IAAY,SAAU,CACtB,IAAIzT,EAAS,KAAK,8BAA8BkL,EAAUyI,EAAiB,aAAa,EACxF,OAAI3T,EAAO,IACAA,EAEJ,CACH,IAAK,CAAE,KAAMkP,GAAQ,KAAK,OAAQ,MAAOvR,EAAO,SAAUoW,EAAY,MAAO/T,EAAO,GAAK,EACzF,IAAK,IACjC,MAEyB,CACD,GAAIkL,EAAS,SAAW,EACpB,OAAO,KAAK,MAAM+D,EAAQ,UAAU,0BAA2B8E,CAAU,EAE7E,IAAIC,EAAkB9I,EAIlB,KAAK,SACL8I,KAAsB1E,GAA8B,gBAAgBpE,EAAU,KAAK,MAAM,GAE7F,IAAIL,EAAQ,CACR,KAAMqE,GAAQ,cAAc,SAC5B,QAAS8E,EACT,SAAUL,EAAiB,cAC3B,cAAe,KAAK,wBACVvE,GAAsB,uBAAuB4E,CAAe,EAChE,CAAE,CACpC,EAC4BC,EAAOR,IAAY,OAASvE,GAAQ,KAAK,KAAOA,GAAQ,KAAK,KACjE,MAAO,CACH,IAAK,CAAE,KAAM+E,EAAM,MAAOtW,EAAO,SAAUoW,EAAY,MAAOlJ,CAAO,EACrE,IAAK,IACjC,GAIgB,MAAO,CACH,IAAK,CACD,KAAM4I,IAAY,SACZvE,GAAQ,KAAK,OACbuE,IAAY,OACRvE,GAAQ,KAAK,KACbA,GAAQ,KAAK,KACvB,MAAOvR,EACP,SAAUoW,EACV,OAAQ7H,EAAyEyH,GAAiB,SAAW,MAAQzH,IAAO,OAASA,EAAK,IAC7I,EACD,IAAK,IACzB,CACa,CACD,IAAK,SACL,IAAK,gBACL,IAAK,SAAU,CAIX,IAAIgI,EAAoB,KAAK,gBAE7B,GADA,KAAK,UAAS,EACV,CAAC,KAAK,OAAO,GAAG,EAChB,OAAO,KAAK,MAAMjF,EAAQ,UAAU,+BAAgCS,EAAewE,EAAmB5I,GAAQ,SAAS,CAAE,EAAE4I,CAAiB,CAAC,CAAC,EAElJ,KAAK,UAAS,EASd,IAAIC,EAAwB,KAAK,4BAC7BC,EAAe,EACnB,GAAIX,IAAY,UAAYU,EAAsB,QAAU,SAAU,CAClE,GAAI,CAAC,KAAK,OAAO,GAAG,EAChB,OAAO,KAAK,MAAMlF,EAAQ,UAAU,oCAAqCS,EAAe,KAAK,cAAa,EAAI,KAAK,cAAa,CAAE,CAAC,EAEvI,KAAK,UAAS,EACd,IAAI1P,EAAS,KAAK,uBAAuBiP,EAAQ,UAAU,oCAAqCA,EAAQ,UAAU,oCAAoC,EACtJ,GAAIjP,EAAO,IACP,OAAOA,EAGX,KAAK,UAAS,EACdmU,EAAwB,KAAK,4BAC7BC,EAAepU,EAAO,IAE1B,IAAIqU,EAAgB,KAAK,8BAA8BrC,EAAcyB,EAASvB,EAAmBiC,CAAqB,EACtH,GAAIE,EAAc,IACd,OAAOA,EAEX,IAAIP,EAAiB,KAAK,sBAAsBV,CAAoB,EACpE,GAAIU,EAAe,IACf,OAAOA,EAEX,IAAIQ,EAAa5E,EAAe0D,EAAsB,KAAK,cAAe,GAC1E,OAAIK,IAAY,SACL,CACH,IAAK,CACD,KAAMvE,GAAQ,KAAK,OACnB,MAAOvR,EACP,QAASoT,GAAYsD,EAAc,GAAG,EACtC,SAAUC,CACb,EACD,IAAK,IAC7B,EAG2B,CACH,IAAK,CACD,KAAMpF,GAAQ,KAAK,OACnB,MAAOvR,EACP,QAASoT,GAAYsD,EAAc,GAAG,EACtC,OAAQD,EACR,WAAYX,IAAY,SAAW,WAAa,UAChD,SAAUa,CACb,EACD,IAAK,IAC7B,CAEa,CACD,QACI,OAAO,KAAK,MAAMrF,EAAQ,UAAU,sBAAuBS,EAAe8D,EAAmBE,CAAe,CAAC,CACpH,CACT,EACI3B,EAAO,UAAU,sBAAwB,SAAUqB,EAAsB,CAGrE,OAAI,KAAK,MAAO,GAAI,KAAK,KAAI,IAAO,IACzB,KAAK,MAAMnE,EAAQ,UAAU,8BAA+BS,EAAe0D,EAAsB,KAAK,cAAa,CAAE,CAAC,GAEjI,KAAK,KAAI,EACF,CAAE,IAAK,GAAM,IAAK,IAAI,EACrC,EAIIrB,EAAO,UAAU,8BAAgC,UAAY,CAGzD,QAFIwC,EAAe,EACflC,EAAgB,KAAK,gBAClB,CAAC,KAAK,SAAS,CAClB,IAAIc,EAAK,KAAK,OACd,OAAQA,EAAE,CACN,IAAK,IAAc,CAGf,KAAK,KAAI,EACT,IAAIqB,EAAqB,KAAK,gBAC9B,GAAI,CAAC,KAAK,UAAU,GAAG,EACnB,OAAO,KAAK,MAAMvF,EAAQ,UAAU,iCAAkCS,EAAe8E,EAAoB,KAAK,cAAa,CAAE,CAAC,EAElI,KAAK,KAAI,EACT,KACH,CACD,IAAK,KAAe,CAChBD,GAAgB,EAChB,KAAK,KAAI,EACT,KACH,CACD,IAAK,KAAe,CAChB,GAAIA,EAAe,EACfA,GAAgB,MAGhB,OAAO,CACH,IAAK,KAAK,QAAQ,MAAMlC,EAAc,OAAQ,KAAK,QAAQ,EAC3D,IAAK,IACjC,EAEoB,KACH,CACD,QACI,KAAK,KAAI,EACT,KACP,EAEL,MAAO,CACH,IAAK,KAAK,QAAQ,MAAMA,EAAc,OAAQ,KAAK,QAAQ,EAC3D,IAAK,IACjB,CACA,EACIN,EAAO,UAAU,8BAAgC,SAAU7G,EAAU+H,EAAU,CAC3E,IAAIrH,EAAS,GACb,GAAI,CACAA,KAAawD,GAAsB,+BAA+BlE,CAAQ,CAC7E,MACD,CACI,OAAO,KAAK,MAAM+D,EAAQ,UAAU,wBAAyBgE,CAAQ,CACxE,CACD,MAAO,CACH,IAAK,CACD,KAAM/D,GAAQ,cAAc,OAC5B,OAAQtD,EACR,SAAUqH,EACV,cAAe,KAAK,wBACV7D,GAAsB,qBAAqBxD,CAAM,EACrD,CAAE,CACX,EACD,IAAK,IACjB,CACA,EAWImG,EAAO,UAAU,8BAAgC,SAAUC,EAAcC,EAAewC,EAAgBC,EAAuB,CAS3H,QARIxI,EACAyI,EAAiB,GACjB9T,EAAU,GACV+T,EAAkB,IAAI,IACtBC,EAAWH,EAAsB,MAAOI,EAAmBJ,EAAsB,WAIxE,CACT,GAAIG,EAAS,SAAW,EAAG,CACvB,IAAIxC,EAAgB,KAAK,gBACzB,GAAIJ,IAAkB,UAAY,KAAK,OAAO,GAAG,EAAG,CAEhD,IAAIjS,EAAS,KAAK,uBAAuBiP,EAAQ,UAAU,gCAAiCA,EAAQ,UAAU,gCAAgC,EAC9I,GAAIjP,EAAO,IACP,OAAOA,EAEX8U,EAAmBpF,EAAe2C,EAAe,KAAK,cAAe,GACrEwC,EAAW,KAAK,QAAQ,MAAMxC,EAAc,OAAQ,KAAK,OAAM,CAAE,MAGjE,OAIR,GAAIuC,EAAgB,IAAIC,CAAQ,EAC5B,OAAO,KAAK,MAAM5C,IAAkB,SAC9BhD,EAAQ,UAAU,mCAClBA,EAAQ,UAAU,mCAAoC6F,CAAgB,EAE5ED,IAAa,UACbF,EAAiB,IAKrB,KAAK,UAAS,EACd,IAAIvB,EAAuB,KAAK,gBAChC,GAAI,CAAC,KAAK,OAAO,GAAG,EAChB,OAAO,KAAK,MAAMnB,IAAkB,SAC9BhD,EAAQ,UAAU,yCAClBA,EAAQ,UAAU,yCAA0CS,EAAe,KAAK,cAAa,EAAI,KAAK,cAAe,EAAC,EAEhI,IAAIqF,EAAiB,KAAK,aAAa/C,EAAe,EAAGC,EAAewC,CAAc,EACtF,GAAIM,EAAe,IACf,OAAOA,EAEX,IAAIjB,EAAiB,KAAK,sBAAsBV,CAAoB,EACpE,GAAIU,EAAe,IACf,OAAOA,EAEXjT,EAAQ,KAAK,CACTgU,EACA,CACI,MAAOE,EAAe,IACtB,SAAUrF,EAAe0D,EAAsB,KAAK,cAAa,CAAE,CACtE,CACjB,CAAa,EAEDwB,EAAgB,IAAIC,CAAQ,EAE5B,KAAK,UAAS,EACb3I,EAAK,KAAK,4BAA6B2I,EAAW3I,EAAG,MAAO4I,EAAmB5I,EAAG,SAEvF,OAAIrL,EAAQ,SAAW,EACZ,KAAK,MAAMoR,IAAkB,SAC9BhD,EAAQ,UAAU,gCAClBA,EAAQ,UAAU,gCAAiCS,EAAe,KAAK,cAAa,EAAI,KAAK,cAAe,EAAC,EAEnH,KAAK,qBAAuB,CAACiF,EACtB,KAAK,MAAM1F,EAAQ,UAAU,qBAAsBS,EAAe,KAAK,cAAa,EAAI,KAAK,cAAa,CAAE,CAAC,EAEjH,CAAE,IAAK7O,EAAS,IAAK,IAAI,CACxC,EACIkR,EAAO,UAAU,uBAAyB,SAAUiD,EAAmBC,EAAoB,CACvF,IAAIC,EAAO,EACP7B,EAAmB,KAAK,gBACxB,KAAK,OAAO,GAAG,GAEV,KAAK,OAAO,GAAG,IACpB6B,EAAO,IAIX,QAFIC,EAAY,GACZC,EAAU,EACP,CAAC,KAAK,SAAS,CAClB,IAAIjC,EAAK,KAAK,OACd,GAAIA,GAAM,IAAgBA,GAAM,GAC5BgC,EAAY,GACZC,EAAUA,EAAU,IAAMjC,EAAK,IAC/B,KAAK,KAAI,MAGT,OAGR,IAAIF,EAAWvD,EAAe2D,EAAkB,KAAK,cAAe,GACpE,OAAK8B,GAGLC,GAAWF,EACN/E,GAAciF,CAAO,EAGnB,CAAE,IAAKA,EAAS,IAAK,IAAI,EAFrB,KAAK,MAAMH,EAAoBhC,CAAQ,GAJvC,KAAK,MAAM+B,EAAmB/B,CAAQ,CAOzD,EACIlB,EAAO,UAAU,OAAS,UAAY,CAClC,OAAO,KAAK,SAAS,MAC7B,EACIA,EAAO,UAAU,MAAQ,UAAY,CACjC,OAAO,KAAK,OAAM,IAAO,KAAK,QAAQ,MAC9C,EACIA,EAAO,UAAU,cAAgB,UAAY,CAEzC,MAAO,CACH,OAAQ,KAAK,SAAS,OACtB,KAAM,KAAK,SAAS,KACpB,OAAQ,KAAK,SAAS,MAClC,CACA,EAKIA,EAAO,UAAU,KAAO,UAAY,CAChC,IAAIsD,EAAS,KAAK,SAAS,OAC3B,GAAIA,GAAU,KAAK,QAAQ,OACvB,MAAM,MAAM,cAAc,EAE9B,IAAIvE,EAAOK,GAAY,KAAK,QAASkE,CAAM,EAC3C,GAAIvE,IAAS,OACT,MAAM,MAAM,UAAU,OAAOuE,EAAQ,0CAA0C,CAAC,EAEpF,OAAOvE,CACf,EACIiB,EAAO,UAAU,MAAQ,SAAU1L,EAAM4M,EAAU,CAC/C,MAAO,CACH,IAAK,KACL,IAAK,CACD,KAAM5M,EACN,QAAS,KAAK,QACd,SAAU4M,CACb,CACb,CACA,EAEIlB,EAAO,UAAU,KAAO,UAAY,CAChC,GAAI,MAAK,QAGT,KAAIjB,EAAO,KAAK,OACZA,IAAS,IACT,KAAK,SAAS,MAAQ,EACtB,KAAK,SAAS,OAAS,EACvB,KAAK,SAAS,QAAU,IAGxB,KAAK,SAAS,QAAU,EAExB,KAAK,SAAS,QAAUA,EAAO,MAAU,EAAI,GAEzD,EAOIiB,EAAO,UAAU,OAAS,SAAUnL,EAAQ,CACxC,GAAI2J,GAAW,KAAK,QAAS3J,EAAQ,KAAK,OAAM,CAAE,EAAG,CACjD,QAAS1H,EAAI,EAAGA,EAAI0H,EAAO,OAAQ1H,IAC/B,KAAK,KAAI,EAEb,MAAO,GAEX,MAAO,EACf,EAKI6S,EAAO,UAAU,UAAY,SAAUuD,EAAS,CAC5C,IAAIC,EAAgB,KAAK,SACrBnE,EAAQ,KAAK,QAAQ,QAAQkE,EAASC,CAAa,EACvD,OAAInE,GAAS,GACT,KAAK,OAAOA,CAAK,EACV,KAGP,KAAK,OAAO,KAAK,QAAQ,MAAM,EACxB,GAEnB,EAKIW,EAAO,UAAU,OAAS,SAAUyD,EAAc,CAC9C,GAAI,KAAK,OAAQ,EAAGA,EAChB,MAAM,MAAM,gBAAgB,OAAOA,EAAc,uDAAuD,EAAE,OAAO,KAAK,OAAQ,EAAC,EAGnI,IADAA,EAAe,KAAK,IAAIA,EAAc,KAAK,QAAQ,MAAM,IAC5C,CACT,IAAIH,EAAS,KAAK,SAClB,GAAIA,IAAWG,EACX,MAEJ,GAAIH,EAASG,EACT,MAAM,MAAM,gBAAgB,OAAOA,EAAc,0CAA0C,CAAC,EAGhG,GADA,KAAK,KAAI,EACL,KAAK,QACL,MAGhB,EAEIzD,EAAO,UAAU,UAAY,UAAY,CACrC,KAAO,CAAC,KAAK,MAAO,GAAIF,GAAc,KAAK,KAAI,CAAE,GAC7C,KAAK,KAAI,CAErB,EAKIE,EAAO,UAAU,KAAO,UAAY,CAChC,GAAI,KAAK,QACL,OAAO,KAEX,IAAIjB,EAAO,KAAK,OACZuE,EAAS,KAAK,SACdI,EAAW,KAAK,QAAQ,WAAWJ,GAAUvE,GAAQ,MAAU,EAAI,EAAE,EACzE,OAAO2E,GAAsD,IACrE,EACW1D,CACX,EAAC,EACa/C,GAAA,OAAG+C,GAMjB,SAASK,GAASsD,EAAW,CACzB,OAASA,GAAa,IAAMA,GAAa,KACpCA,GAAa,IAAMA,GAAa,EACzC,CACA,SAASxC,GAAgBwC,EAAW,CAChC,OAAOtD,GAASsD,CAAS,GAAKA,IAAc,EAChD,CAEA,SAAS7C,GAA4BnN,EAAG,CACpC,OAAQA,IAAM,IACVA,IAAM,IACLA,GAAK,IAAMA,GAAK,IACjBA,IAAM,IACLA,GAAK,IAAMA,GAAK,KAChBA,GAAK,IAAMA,GAAK,IACjBA,GAAK,KACJA,GAAK,KAAQA,GAAK,KAClBA,GAAK,KAAQA,GAAK,KAClBA,GAAK,KAAQA,GAAK,KAClBA,GAAK,KAASA,GAAK,MACnBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAWA,GAAK,MAC9B,CAKA,SAASmM,GAAcnM,EAAG,CACtB,OAASA,GAAK,GAAUA,GAAK,IACzBA,IAAM,IACNA,IAAM,KACLA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACNA,IAAM,IACd,CAKA,SAASoM,GAAiBpM,EAAG,CACzB,OAASA,GAAK,IAAUA,GAAK,IACzBA,IAAM,IACLA,GAAK,IAAUA,GAAK,IACrBA,IAAM,IACNA,IAAM,IACNA,IAAM,IACNA,IAAM,IACNA,IAAM,IACNA,IAAM,IACLA,GAAK,IAAUA,GAAK,IACpBA,GAAK,IAAUA,GAAK,IACpBA,GAAK,IAAUA,GAAK,IACpBA,GAAK,IAAUA,GAAK,IACrBA,IAAM,IACNA,IAAM,IACNA,IAAM,IACNA,IAAM,IACNA,IAAM,IACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACLA,GAAK,KAAUA,GAAK,KACrBA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACNA,IAAM,KACLA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACNA,IAAM,MACNA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACNA,IAAM,MACNA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACNA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACNA,IAAM,MACNA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACNA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACNA,IAAM,MACNA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACNA,IAAM,MACNA,IAAM,MACNA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,KACrBA,IAAM,MACNA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACLA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACrBA,IAAM,MACLA,GAAK,MAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,OACrBA,IAAM,OACNA,IAAM,OACNA,IAAM,OACNA,IAAM,OACLA,GAAK,OAAUA,GAAK,KAC7B,cC7vCA,OAAO,eAAcf,EAAU,aAAc,CAAE,MAAO,EAAI,CAAE,EAC5DA,EAAkB,QAAAA,EAAA,MAAgB,OAClC,IAAI2G,EAAUC,GACV0D,EAAUxD,GACVkK,EAAW9H,GACXqB,EAAUC,EACd,SAASyG,EAAcC,EAAK,CACxBA,EAAI,QAAQ,SAAU7L,EAAI,CAEtB,GADA,OAAOA,EAAG,YACFkF,EAAQ,iBAAiBlF,CAAE,MAASkF,EAAQ,iBAAiBlF,CAAE,EACnE,QAASjC,KAAKiC,EAAG,QACb,OAAOA,EAAG,QAAQjC,CAAC,EAAE,SACrB6N,EAAc5L,EAAG,QAAQjC,CAAC,EAAE,KAAK,SAG5BmH,EAAQ,iBAAiBlF,CAAE,MAASkF,EAAQ,kBAAkBlF,EAAG,KAAK,OAGrEkF,EAAQ,eAAelF,CAAE,MAASkF,EAAQ,eAAelF,CAAE,OACjEkF,EAAQ,oBAAoBlF,EAAG,KAAK,EAHxC,OAAOA,EAAG,MAAM,YAMPkF,EAAQ,cAAclF,CAAE,GACjC4L,EAAc5L,EAAG,QAAQ,CAErC,CAAK,CACJ,CACD,SAAS8L,EAAMtM,EAASuM,EAAM,CACtBA,IAAS,SAAUA,EAAO,CAAE,GAChCA,EAAOzK,EAAQ,SAAS,CAAE,qBAAsB,GAAM,oBAAqB,IAAQyK,CAAI,EACvF,IAAI/V,EAAS,IAAI2V,EAAS,OAAOnM,EAASuM,CAAI,EAAE,QAChD,GAAI/V,EAAO,IAAK,CACZ,IAAIkI,EAAQ,YAAY+G,EAAQ,UAAUjP,EAAO,IAAI,IAAI,CAAC,EAE1D,MAAAkI,EAAM,SAAWlI,EAAO,IAAI,SAE5BkI,EAAM,gBAAkBlI,EAAO,IAAI,QAC7BkI,EAEV,OAAkD6N,GAAK,iBACnDH,EAAc5V,EAAO,GAAG,EAErBA,EAAO,GACjB,CACD2E,EAAA,MAAgBmR,EAChBxK,EAAQ,aAAa6D,EAAoBxK,CAAO,EAEhDA,EAAkB,QAAAgR,EAAS,aC7CpB,SAASK,GAAQnZ,EAAIgE,EAAS,CACjC,IAAIoV,EAAQpV,GAAWA,EAAQ,MAAQA,EAAQ,MAAQqV,GACnDC,EAAatV,GAAWA,EAAQ,WAAaA,EAAQ,WAAauV,GAClEC,EAAWxV,GAAWA,EAAQ,SAAWA,EAAQ,SAAWyV,GAChE,OAAOD,EAASxZ,EAAI,CAChB,MAAOoZ,EACP,WAAYE,CACpB,CAAK,CACL,CAIA,SAASI,GAAY5Y,EAAO,CACxB,OAAQA,GAAS,MAAQ,OAAOA,GAAU,UAAY,OAAOA,GAAU,SAC3E,CACA,SAAS6Y,GAAQ3Z,EAAIoZ,EAAOE,EAAYM,EAAK,CACzC,IAAIC,EAAWH,GAAYE,CAAG,EAAIA,EAAMN,EAAWM,CAAG,EAClDE,EAAgBV,EAAM,IAAIS,CAAQ,EACtC,OAAI,OAAOC,EAAkB,MACzBA,EAAgB9Z,EAAG,KAAK,KAAM4Z,CAAG,EACjCR,EAAM,IAAIS,EAAUC,CAAa,GAE9BA,CACX,CACA,SAASC,GAAS/Z,EAAIoZ,EAAOE,EAAY,CACrC,IAAIU,EAAO,MAAM,UAAU,MAAM,KAAK,UAAW,CAAC,EAC9CH,EAAWP,EAAWU,CAAI,EAC1BF,EAAgBV,EAAM,IAAIS,CAAQ,EACtC,OAAI,OAAOC,EAAkB,MACzBA,EAAgB9Z,EAAG,MAAM,KAAMga,CAAI,EACnCZ,EAAM,IAAIS,EAAUC,CAAa,GAE9BA,CACX,CACA,SAASG,GAASja,EAAI2J,EAAS6P,EAAUJ,EAAOc,EAAW,CACvD,OAAOV,EAAS,KAAK7P,EAAS3J,EAAIoZ,EAAOc,CAAS,CACtD,CACA,SAAST,GAAgBzZ,EAAIgE,EAAS,CAClC,IAAIwV,EAAWxZ,EAAG,SAAW,EAAI2Z,GAAUI,GAC3C,OAAOE,GAASja,EAAI,KAAMwZ,EAAUxV,EAAQ,MAAM,OAAQ,EAAEA,EAAQ,UAAU,CAClF,CACA,SAASmW,GAAiBna,EAAIgE,EAAS,CACnC,OAAOiW,GAASja,EAAI,KAAM+Z,GAAU/V,EAAQ,MAAM,OAAQ,EAAEA,EAAQ,UAAU,CAClF,CACA,SAASoW,GAAgBpa,EAAIgE,EAAS,CAClC,OAAOiW,GAASja,EAAI,KAAM2Z,GAAS3V,EAAQ,MAAM,OAAQ,EAAEA,EAAQ,UAAU,CACjF,CAIA,IAAIuV,GAAoB,UAAY,CAChC,OAAO,KAAK,UAAU,SAAS,CACnC,EAIA,SAASc,IAA8B,CACnC,KAAK,MAAQ,OAAO,OAAO,IAAI,CACnC,CACAA,GAA4B,UAAU,IAAM,SAAU9V,EAAK,CACvD,OAAO,KAAK,MAAMA,CAAG,CACzB,EACA8V,GAA4B,UAAU,IAAM,SAAU9V,EAAKzD,EAAO,CAC9D,KAAK,MAAMyD,CAAG,EAAIzD,CACtB,EACA,IAAIuY,GAAe,CACf,OAAQ,UAAkB,CAEtB,OAAO,IAAIgB,EACd,CACL,EACWC,GAAa,CACpB,SAAUH,GACV,QAASC,EACb,yJC5EA,OAAO,eAAe/O,GAAS,aAAc,CAAE,MAAO,EAAI,CAAE,EACnCA,GAAA,kBAAgCA,GAAA,sBAA4BA,GAAA,kBAAsBA,GAAA,YAAoBA,GAAA,UAAG,OAClI,IAAIoD,GAAUC,GACV6L,IACH,SAAUA,EAAW,CAElBA,EAAU,cAAmB,gBAE7BA,EAAU,cAAmB,gBAE7BA,EAAU,iBAAsB,kBACpC,GAAGA,KAA+BlP,GAAA,UAAGkP,GAAY,GAAG,EACpD,IAAIC,GAA6B,SAAUC,EAAQ,CAC/ChM,GAAQ,UAAU+L,EAAaC,CAAM,EACrC,SAASD,EAAYE,EAAKzG,EAAM0G,EAAiB,CAC7C,IAAIC,EAAQH,EAAO,KAAK,KAAMC,CAAG,GAAK,KACtC,OAAAE,EAAM,KAAO3G,EACb2G,EAAM,gBAAkBD,EACjBC,CACV,CACD,OAAAJ,EAAY,UAAU,SAAW,UAAY,CACzC,MAAO,oBAAoB,OAAO,KAAK,KAAM,IAAI,EAAE,OAAO,KAAK,OAAO,CAC9E,EACWA,CACX,EAAE,KAAK,EACYnP,GAAA,YAAGmP,GACtB,IAAIK,GAAmC,SAAUJ,EAAQ,CACrDhM,GAAQ,UAAUoM,EAAmBJ,CAAM,EAC3C,SAASI,EAAkBC,EAAYha,EAAOkD,EAAS2W,EAAiB,CACpE,OAAOF,EAAO,KAAK,KAAM,uBAAwB,OAAOK,EAAY,MAAQ,EAAE,OAAOha,EAAO,kBAAoB,EAAE,OAAO,OAAO,KAAKkD,CAAO,EAAE,KAAK,MAAM,EAAG,GAAI,EAAGuW,GAAU,cAAeI,CAAe,GAAK,IACnN,CACD,OAAOE,CACX,EAAEL,EAAW,EACYnP,GAAA,kBAAGwP,GAC5B,IAAIE,GAAuC,SAAUN,EAAQ,CACzDhM,GAAQ,UAAUsM,EAAuBN,CAAM,EAC/C,SAASM,EAAsBja,EAAOsW,EAAMuD,EAAiB,CACzD,OAAOF,EAAO,KAAK,KAAM,cAAe,OAAO3Z,EAAO,oBAAqB,EAAE,OAAOsW,CAAI,EAAGmD,GAAU,cAAeI,CAAe,GAAK,IAC3I,CACD,OAAOI,CACX,EAAEP,EAAW,EACgBnP,GAAA,sBAAG0P,GAChC,IAAIC,GAAmC,SAAUP,EAAQ,CACrDhM,GAAQ,UAAUuM,EAAmBP,CAAM,EAC3C,SAASO,EAAkBF,EAAYH,EAAiB,CACpD,OAAOF,EAAO,KAAK,KAAM,qCAAsC,OAAOK,EAAY,oCAAsC,EAAE,OAAOH,EAAiB,GAAI,EAAGJ,GAAU,cAAeI,CAAe,GAAK,IACzM,CACD,OAAOK,CACX,EAAER,EAAW,EACbnP,GAAA,kBAA4B2P,GCjD5B,OAAO,eAAeC,GAAS,aAAc,CAAE,MAAO,EAAI,CAAE,EAC5DA,GAAA,cAAoDA,GAAA,kCAAuB,OAC3E,IAAIC,GAA6BxM,GAC7B0D,GAAUxD,GACVuM,IACH,SAAUA,EAAW,CAClBA,EAAUA,EAAU,QAAa,CAAC,EAAI,UACtCA,EAAUA,EAAU,OAAY,CAAC,EAAI,QACzC,GAAGA,KAA+BF,GAAA,UAAGE,GAAY,GAAG,EACpD,SAASC,GAAaC,EAAO,CACzB,OAAIA,EAAM,OAAS,EACRA,EAEJA,EAAM,OAAO,SAAU1K,EAAK2K,EAAM,CACrC,IAAIC,EAAW5K,EAAIA,EAAI,OAAS,CAAC,EACjC,MAAI,CAAC4K,GACDA,EAAS,OAASJ,GAAU,SAC5BG,EAAK,OAASH,GAAU,QACxBxK,EAAI,KAAK2K,CAAI,EAGbC,EAAS,OAASD,EAAK,MAEpB3K,CACV,EAAE,CAAE,EACT,CACA,SAAS6K,GAAqBrO,EAAI,CAC9B,OAAO,OAAOA,GAAO,UACzB,CAC4B8N,GAAA,qBAAGO,GAE/B,SAASC,GAAczC,EAAK0C,EAAST,EAAYU,EAAS5Y,EAAQ6Y,EAElEjB,EAAiB,CAEb,GAAI3B,EAAI,SAAW,MAASkC,GAA2B,kBAAkBlC,EAAI,CAAC,CAAC,EAC3E,MAAO,CACH,CACI,KAAMmC,GAAU,QAChB,MAAOnC,EAAI,CAAC,EAAE,KACjB,CACb,EAGI,QADI7V,EAAS,GACJ6L,EAAK,EAAG6M,EAAQ7C,EAAKhK,EAAK6M,EAAM,OAAQ7M,IAAM,CACnD,IAAI7B,EAAK0O,EAAM7M,CAAE,EAEjB,MAAQkM,GAA2B,kBAAkB/N,CAAE,EAAG,CACtDhK,EAAO,KAAK,CACR,KAAMgY,GAAU,QAChB,MAAOhO,EAAG,KAC1B,CAAa,EACD,SAIJ,MAAQ+N,GAA2B,gBAAgB/N,CAAE,EAAG,CAChD,OAAOyO,GAAuB,UAC9BzY,EAAO,KAAK,CACR,KAAMgY,GAAU,QAChB,MAAOF,EAAW,gBAAgBS,CAAO,EAAE,OAAOE,CAAkB,CACxF,CAAiB,EAEL,SAEJ,IAAIE,EAAU3O,EAAG,MAEjB,GAAI,EAAEpK,GAAU+Y,KAAW/Y,GACvB,MAAM,IAAIqP,GAAQ,kBAAkB0J,EAASnB,CAAe,EAEhE,IAAI7Z,EAAQiC,EAAO+Y,CAAO,EAC1B,MAAQZ,GAA2B,mBAAmB/N,CAAE,EAAG,EACnD,CAACrM,GAAS,OAAOA,GAAU,UAAY,OAAOA,GAAU,YACxDA,EACI,OAAOA,GAAU,UAAY,OAAOA,GAAU,SACxC,OAAOA,CAAK,EACZ,IAEdqC,EAAO,KAAK,CACR,KAAM,OAAOrC,GAAU,SAAWqa,GAAU,QAAUA,GAAU,OAChE,MAAOra,CACvB,CAAa,EACD,SAKJ,MAAQoa,GAA2B,eAAe/N,CAAE,EAAG,CACnD,IAAIa,EAAQ,OAAOb,EAAG,OAAU,SAC1BwO,EAAQ,KAAKxO,EAAG,KAAK,KACjB+N,GAA2B,oBAAoB/N,EAAG,KAAK,EACvDA,EAAG,MAAM,cACT,OACVhK,EAAO,KAAK,CACR,KAAMgY,GAAU,QAChB,MAAOF,EACF,kBAAkBS,EAAS1N,CAAK,EAChC,OAAOlN,CAAK,CACjC,CAAa,EACD,SAEJ,MAAQoa,GAA2B,eAAe/N,CAAE,EAAG,CACnD,IAAIa,EAAQ,OAAOb,EAAG,OAAU,SAC1BwO,EAAQ,KAAKxO,EAAG,KAAK,KACjB+N,GAA2B,oBAAoB/N,EAAG,KAAK,EACvDA,EAAG,MAAM,cACTwO,EAAQ,KAAK,OACvBxY,EAAO,KAAK,CACR,KAAMgY,GAAU,QAChB,MAAOF,EACF,kBAAkBS,EAAS1N,CAAK,EAChC,OAAOlN,CAAK,CACjC,CAAa,EACD,SAEJ,MAAQoa,GAA2B,iBAAiB/N,CAAE,EAAG,CACrD,IAAIa,EAAQ,OAAOb,EAAG,OAAU,SAC1BwO,EAAQ,OAAOxO,EAAG,KAAK,KACnB+N,GAA2B,kBAAkB/N,EAAG,KAAK,EACrDA,EAAG,MAAM,cACT,OACNa,GAASA,EAAM,QACflN,EACIA,GACKkN,EAAM,OAAS,IAE5B7K,EAAO,KAAK,CACR,KAAMgY,GAAU,QAChB,MAAOF,EACF,gBAAgBS,EAAS1N,CAAK,EAC9B,OAAOlN,CAAK,CACjC,CAAa,EACD,SAEJ,MAAQoa,GAA2B,cAAc/N,CAAE,EAAG,CAClD,IAAIwI,EAAWxI,EAAG,SAAU4O,EAAU5O,EAAG,MACrC6O,EAAWjZ,EAAOgZ,CAAO,EAC7B,GAAI,CAACP,GAAqBQ,CAAQ,EAC9B,MAAM,IAAI5J,GAAQ,sBAAsB2J,EAAS,WAAYpB,CAAe,EAEhF,IAAIU,EAAQI,GAAc9F,EAAU+F,EAAST,EAAYU,EAAS5Y,EAAQ6Y,CAAkB,EACxFK,EAASD,EAASX,EAAM,IAAI,SAAUhT,EAAG,CAAE,OAAOA,EAAE,KAAM,CAAE,CAAC,EAC5D,MAAM,QAAQ4T,CAAM,IACrBA,EAAS,CAACA,CAAM,GAEpB9Y,EAAO,KAAK,MAAMA,EAAQ8Y,EAAO,IAAI,SAAUpT,EAAG,CAC9C,MAAO,CACH,KAAM,OAAOA,GAAM,SAAWsS,GAAU,QAAUA,GAAU,OAC5D,MAAOtS,CAC3B,CACa,EAAC,EAEN,MAAQqS,GAA2B,iBAAiB/N,CAAE,EAAG,CACrD,IAAImD,EAAMnD,EAAG,QAAQrM,CAAK,GAAKqM,EAAG,QAAQ,MAC1C,GAAI,CAACmD,EACD,MAAM,IAAI8B,GAAQ,kBAAkBjF,EAAG,MAAOrM,EAAO,OAAO,KAAKqM,EAAG,OAAO,EAAGwN,CAAe,EAEjGxX,EAAO,KAAK,MAAMA,EAAQsY,GAAcnL,EAAI,MAAOoL,EAAST,EAAYU,EAAS5Y,CAAM,CAAC,EACxF,SAEJ,MAAQmY,GAA2B,iBAAiB/N,CAAE,EAAG,CACrD,IAAImD,EAAMnD,EAAG,QAAQ,IAAI,OAAOrM,CAAK,CAAC,EACtC,GAAI,CAACwP,EAAK,CACN,GAAI,CAAC,KAAK,YACN,MAAM,IAAI8B,GAAQ,YAAY;AAAA;AAAA,EAAqHA,GAAQ,UAAU,iBAAkBuI,CAAe,EAE1M,IAAIuB,EAAOjB,EACN,eAAeS,EAAS,CAAE,KAAMvO,EAAG,UAAU,CAAE,EAC/C,OAAOrM,GAASqM,EAAG,QAAU,EAAE,EACpCmD,EAAMnD,EAAG,QAAQ+O,CAAI,GAAK/O,EAAG,QAAQ,MAEzC,GAAI,CAACmD,EACD,MAAM,IAAI8B,GAAQ,kBAAkBjF,EAAG,MAAOrM,EAAO,OAAO,KAAKqM,EAAG,OAAO,EAAGwN,CAAe,EAEjGxX,EAAO,KAAK,MAAMA,EAAQsY,GAAcnL,EAAI,MAAOoL,EAAST,EAAYU,EAAS5Y,EAAQjC,GAASqM,EAAG,QAAU,EAAE,CAAC,EAClH,UAGR,OAAOiO,GAAajY,CAAM,CAC9B,CACA8X,GAAA,cAAwBQ,GC/KxB,OAAO,eAAeU,GAAS,aAAc,CAAE,MAAO,EAAI,CAAE,EACnCA,GAAA,kBAAG,OAC5B,IAAI1N,GAAUC,GACVwM,GAA6BtM,GAC7BwN,GAAiBpL,GACjBqL,GAAe/J,GAEnB,SAASgK,GAAYC,EAAIC,EAAI,CACzB,OAAKA,EAGE/N,GAAQ,SAASA,GAAQ,SAASA,GAAQ,SAAS,GAAK8N,GAAM,CAAE,GAAKC,GAAM,CAAE,GAAI,OAAO,KAAKD,CAAE,EAAE,OAAO,SAAU5L,EAAKzF,EAAG,CAC7H,OAAAyF,EAAIzF,CAAC,EAAIuD,GAAQ,SAASA,GAAQ,SAAS,GAAI8N,EAAGrR,CAAC,CAAC,EAAIsR,EAAGtR,CAAC,GAAK,CAAE,GAC5DyF,CACf,EAAO,EAAE,CAAC,EALK4L,CAMf,CACA,SAASE,GAAaC,EAAeC,EAAS,CAC1C,OAAKA,EAGE,OAAO,KAAKD,CAAa,EAAE,OAAO,SAAU/L,EAAKzF,EAAG,CACvD,OAAAyF,EAAIzF,CAAC,EAAIoR,GAAYI,EAAcxR,CAAC,EAAGyR,EAAQzR,CAAC,CAAC,EAC1CyF,CACV,EAAElC,GAAQ,SAAS,GAAIiO,CAAa,CAAC,EAL3BA,CAMf,CACA,SAASE,GAAuBnc,EAAO,CACnC,MAAO,CACH,OAAQ,UAAY,CAChB,MAAO,CACH,IAAK,SAAU8D,EAAK,CAChB,OAAO9D,EAAM8D,CAAG,CACnB,EACD,IAAK,SAAUA,EAAKzD,EAAO,CACvBL,EAAM8D,CAAG,EAAIzD,CAChB,CACjB,CACS,CACT,CACA,CACA,SAAS+b,GAAwBzD,EAAO,CACpC,OAAIA,IAAU,SAAUA,EAAQ,CAC5B,OAAQ,CAAE,EACV,SAAU,CAAE,EACZ,YAAa,CAAE,CACvB,GACW,CACH,mBAAqBgD,GAAe,SAAS,UAAY,CAGrD,QAFI/M,EACA2K,EAAO,GACFhL,EAAK,EAAGA,EAAK,UAAU,OAAQA,IACpCgL,EAAKhL,CAAE,EAAI,UAAUA,CAAE,EAE3B,OAAO,KAAMK,EAAK,KAAK,cAAc,KAAK,MAAMA,EAAIZ,GAAQ,cAAc,CAAC,MAAM,EAAGuL,EAAM,EAAK,CAAC,EAC5G,EAAW,CACC,MAAO4C,GAAuBxD,EAAM,MAAM,EAC1C,SAAUgD,GAAe,WAAW,QAChD,CAAS,EACD,qBAAuBA,GAAe,SAAS,UAAY,CAGvD,QAFI/M,EACA2K,EAAO,GACFhL,EAAK,EAAGA,EAAK,UAAU,OAAQA,IACpCgL,EAAKhL,CAAE,EAAI,UAAUA,CAAE,EAE3B,OAAO,KAAMK,EAAK,KAAK,gBAAgB,KAAK,MAAMA,EAAIZ,GAAQ,cAAc,CAAC,MAAM,EAAGuL,EAAM,EAAK,CAAC,EAC9G,EAAW,CACC,MAAO4C,GAAuBxD,EAAM,QAAQ,EAC5C,SAAUgD,GAAe,WAAW,QAChD,CAAS,EACD,kBAAoBA,GAAe,SAAS,UAAY,CAGpD,QAFI/M,EACA2K,EAAO,GACFhL,EAAK,EAAGA,EAAK,UAAU,OAAQA,IACpCgL,EAAKhL,CAAE,EAAI,UAAUA,CAAE,EAE3B,OAAO,KAAMK,EAAK,KAAK,aAAa,KAAK,MAAMA,EAAIZ,GAAQ,cAAc,CAAC,MAAM,EAAGuL,EAAM,EAAK,CAAC,EAC3G,EAAW,CACC,MAAO4C,GAAuBxD,EAAM,WAAW,EAC/C,SAAUgD,GAAe,WAAW,QAChD,CAAS,CACT,CACA,CACA,IAAIU,GAAmC,UAAY,CAC/C,SAASA,EAAkBnQ,EAAS+O,EAASqB,EAAiB7D,EAAM,CAChE,IAAI0B,EAAQ,KA2CZ,GA1CIc,IAAY,SAAUA,EAAUoB,EAAkB,eACtD,KAAK,eAAiB,CAClB,OAAQ,CAAE,EACV,SAAU,CAAE,EACZ,YAAa,CAAE,CAC3B,EACQ,KAAK,OAAS,SAAU/Z,EAAQ,CAC5B,IAAIsY,EAAQT,EAAM,cAAc7X,CAAM,EAEtC,GAAIsY,EAAM,SAAW,EACjB,OAAOA,EAAM,CAAC,EAAE,MAEpB,IAAIlY,EAASkY,EAAM,OAAO,SAAU1K,EAAK2K,EAAM,CAC3C,MAAI,CAAC3K,EAAI,QACL2K,EAAK,OAASe,GAAa,UAAU,SACrC,OAAO1L,EAAIA,EAAI,OAAS,CAAC,GAAM,SAC/BA,EAAI,KAAK2K,EAAK,KAAK,EAGnB3K,EAAIA,EAAI,OAAS,CAAC,GAAK2K,EAAK,MAEzB3K,CACV,EAAE,CAAE,GACL,OAAIxN,EAAO,QAAU,EACVA,EAAO,CAAC,GAAK,GAEjBA,CACnB,EACQ,KAAK,cAAgB,SAAUJ,EAAQ,CACnC,SAAWsZ,GAAa,eAAezB,EAAM,IAAKA,EAAM,QAASA,EAAM,WAAYA,EAAM,QAAS7X,EAAQ,OAAW6X,EAAM,OAAO,CAC9I,EACQ,KAAK,gBAAkB,UAAY,CAC/B,IAAIvL,EACJ,MAAQ,CACJ,SAAUA,EAAKuL,EAAM,kBAAoB,MAAQvL,IAAO,OAAS,OAASA,EAAG,SAAU,IACnF,KAAK,aAAa,mBAAmBuL,EAAM,OAAO,EAAE,CAAC,CACzE,CACA,EACQ,KAAK,OAAS,UAAY,CAAE,OAAOA,EAAM,GAAI,EAE7C,KAAK,QAAUc,EACf,KAAK,eAAiBoB,EAAkB,cAAcpB,CAAO,EACzD,OAAO/O,GAAY,SAAU,CAE7B,GADA,KAAK,QAAUA,EACX,CAACmQ,EAAkB,QACnB,MAAM,IAAI,UAAU,6EAA6E,EAErG,IAAIzN,EAAK6J,GAAQ,GAAiB7J,EAAG,eAAY2N,EAAYvO,GAAQ,OAAOY,EAAI,CAAC,YAAY,CAAC,EAE9F,KAAK,IAAMyN,EAAkB,QAAQnQ,EAAS8B,GAAQ,SAASA,GAAQ,SAAS,CAAE,EAAEuO,CAAS,EAAG,CAAE,OAAQ,KAAK,cAAgB,EAAC,OAGhI,KAAK,IAAMrQ,EAEf,GAAI,CAAC,MAAM,QAAQ,KAAK,GAAG,EACvB,MAAM,IAAI,UAAU,gDAAgD,EAIxE,KAAK,QAAU8P,GAAaK,EAAkB,QAASC,CAAe,EACtE,KAAK,WACA7D,GAAQA,EAAK,YAAe2D,GAAwB,KAAK,cAAc,CAC/E,CACD,cAAO,eAAeC,EAAmB,gBAAiB,CACtD,IAAK,UAAY,CACb,OAAKA,EAAkB,wBACnBA,EAAkB,sBACd,IAAI,KAAK,aAAY,EAAG,gBAAe,EAAG,QAE3CA,EAAkB,qBAC5B,EACD,WAAY,GACZ,aAAc,EACtB,CAAK,EACDA,EAAkB,sBAAwB,KAC1CA,EAAkB,cAAgB,SAAUpB,EAAS,CACjD,GAAI,SAAO,KAAK,OAAW,KAG3B,KAAIuB,EAAmB,KAAK,aAAa,mBAAmBvB,CAAO,EACnE,OAAIuB,EAAiB,OAAS,EACnB,IAAI,KAAK,OAAOA,EAAiB,CAAC,CAAC,EAEvC,IAAI,KAAK,OAAO,OAAOvB,GAAY,SAAWA,EAAUA,EAAQ,CAAC,CAAC,EACjF,EACIoB,EAAkB,QAAU5B,GAA2B,MAIvD4B,EAAkB,QAAU,CACxB,OAAQ,CACJ,QAAS,CACL,sBAAuB,CAC1B,EACD,SAAU,CACN,MAAO,UACV,EACD,QAAS,CACL,MAAO,SACV,CACJ,EACD,KAAM,CACF,MAAO,CACH,MAAO,UACP,IAAK,UACL,KAAM,SACT,EACD,OAAQ,CACJ,MAAO,QACP,IAAK,UACL,KAAM,SACT,EACD,KAAM,CACF,MAAO,OACP,IAAK,UACL,KAAM,SACT,EACD,KAAM,CACF,QAAS,OACT,MAAO,OACP,IAAK,UACL,KAAM,SACT,CACJ,EACD,KAAM,CACF,MAAO,CACH,KAAM,UACN,OAAQ,SACX,EACD,OAAQ,CACJ,KAAM,UACN,OAAQ,UACR,OAAQ,SACX,EACD,KAAM,CACF,KAAM,UACN,OAAQ,UACR,OAAQ,UACR,aAAc,OACjB,EACD,KAAM,CACF,KAAM,UACN,OAAQ,UACR,OAAQ,UACR,aAAc,OACjB,CACJ,CACT,EACWA,CACX,EAAC,EACDX,GAAA,kBAA4BW,gBC1O5B,OAAO,eAAchV,EAAU,aAAc,CAAE,MAAO,EAAI,CAAE,EAC5D,IAAI2G,EAAUC,GACVwO,EAAStO,GACbH,EAAQ,aAAauC,GAA6BlJ,CAAO,EACzD2G,EAAQ,aAAaG,GAAuB9G,CAAO,EACnD2G,EAAQ,aAAa6D,GAAwBxK,CAAO,EACpDA,EAAkB,QAAAoV,EAAO,wBCRzB,SAASC,GAAM/I,EAAKgJ,EAAS,CAC3B,GAAIA,GAAW,KACb,OACF,GAAIA,KAAWhJ,EACb,OAAOA,EAAIgJ,CAAO,EAEpB,MAAMC,EAAOD,EAAQ,MAAM,GAAG,EAC9B,IAAIja,EAASiR,EACb,QAAS/L,EAAI,EAAGA,EAAIgV,EAAK,OAAQhV,IAC/B,GAAI,OAAOlF,GAAW,SAAU,CAC9B,GAAIkF,EAAI,EAAG,CACT,MAAMiV,EAAaD,EAAK,MAAMhV,EAAGgV,EAAK,MAAM,EAAE,KAAK,GAAG,EACtD,GAAIC,KAAcna,EAAQ,CACxBA,EAASA,EAAOma,CAAU,EAC1B,OAGJna,EAASA,EAAOka,EAAKhV,CAAC,CAAC,OAEvBlF,EAAS,OAGb,OAAOA,CACT,CAEA,MAAMoa,GAAc,GACdC,GAAa,CAACC,EAAMpM,EAAQ1E,IAC3BA,IAEC0E,KAAUkM,KACdA,GAAYlM,CAAM,EAAI,IAClBoM,KAAQF,GAAYlM,CAAM,IAC9BkM,GAAYlM,CAAM,EAAEoM,CAAI,EAAI9Q,GACvBA,GAEH+Q,GAAS,CAACD,EAAME,IAAc,CAClC,GAAIA,GAAa,KACf,OACF,GAAIA,KAAaJ,IAAeE,KAAQF,GAAYI,CAAS,EAC3D,OAAOJ,GAAYI,CAAS,EAAEF,CAAI,EAEpC,MAAM/B,EAAUkC,GAAmBD,CAAS,EAC5C,QAAStb,EAAI,EAAGA,EAAIqZ,EAAQ,OAAQrZ,IAAK,CACvC,MAAMgP,EAASqK,EAAQrZ,CAAC,EAClBsK,EAAUkR,GAAyBxM,EAAQoM,CAAI,EACrD,GAAI9Q,EACF,OAAO6Q,GAAWC,EAAME,EAAWhR,CAAO,EAIhD,EAEA,IAAImR,GACJ,MAAMC,GAAcjc,GAAS,EAAE,EAC/B,SAASkc,GAAoB3M,EAAQ,CACnC,OAAOyM,GAAWzM,CAAM,GAAK,IAC/B,CACA,SAAS4M,GAAoB5M,EAAQ,CACnC,OAAOA,KAAUyM,EACnB,CACA,SAASD,GAAyBxM,EAAQrJ,EAAI,CAC5C,GAAI,CAACiW,GAAoB5M,CAAM,EAC7B,OAAO,KAET,MAAM6M,EAAmBF,GAAoB3M,CAAM,EAEnD,OADc8L,GAAMe,EAAkBlW,CAAE,CAE1C,CACA,SAASmW,GAA0BR,EAAW,CAC5C,GAAIA,GAAa,KACf,OACF,MAAMS,EAAiBR,GAAmBD,CAAS,EACnD,QAAStb,EAAI,EAAGA,EAAI+b,EAAe,OAAQ/b,IAAK,CAC9C,MAAMgP,EAAS+M,EAAe/b,CAAC,EAC/B,GAAI4b,GAAoB5M,CAAM,EAC5B,OAAOA,EAIb,CACA,SAASgN,GAAYhN,KAAWiN,EAAU,CACxC,OAAOf,GAAYlM,CAAM,EACzB0M,GAAY,OAAQ3V,IAClBA,EAAEiJ,CAAM,EAAIpN,GAAU,IAAI,CAACmE,EAAEiJ,CAAM,GAAK,GAAI,GAAGiN,CAAQ,CAAC,EACjDlW,EACR,CACH,CACiB5F,GACf,CAACub,EAAW,EACZ,CAAC,CAACQ,CAAW,IAAM,OAAO,KAAKA,CAAW,CAC5C,EACAR,GAAY,UAAWS,GAAkBV,GAAaU,CAAa,EAEnE,MAAMC,GAAQ,GAId,SAASC,GAAsBrN,EAAQsN,EAAQ,CAC7CF,GAAMpN,CAAM,EAAE,OAAOsN,CAAM,EACvBF,GAAMpN,CAAM,EAAE,OAAS,GACzB,OAAOoN,GAAMpN,CAAM,CAEvB,CACA,SAASuN,GAAevN,EAAQ,CAC9B,OAAOoN,GAAMpN,CAAM,CACrB,CACA,SAASwN,GAAiBxN,EAAQ,CAChC,OAAOuM,GAAmBvM,CAAM,EAAE,IAAKyN,GAAe,CACpD,MAAMC,EAAcH,GAAeE,CAAU,EAC7C,MAAO,CAACA,EAAYC,EAAc,CAAC,GAAGA,CAAW,EAAI,EAAE,CAC3D,CAAG,EAAE,OAAO,CAAC,CAAG,CAAAA,CAAW,IAAMA,EAAY,OAAS,CAAC,CACvD,CACA,SAASC,GAAe3N,EAAQ,CAC9B,OAAIA,GAAU,KACL,GACFuM,GAAmBvM,CAAM,EAAE,KAC/B0N,GAAgB,CACf,IAAI1P,EACJ,OAAQA,EAAKuP,GAAeG,CAAW,IAAM,KAAO,OAAS1P,EAAG,IACjE,CACL,CACA,CACA,SAAS4P,GAAgB5N,EAAQ0N,EAAa,CAO5C,OAN0B,QAAQ,IAChCA,EAAY,IAAKJ,IACfD,GAAsBrN,EAAQsN,CAAM,EAC7BA,EAAQ,EAAC,KAAMO,GAAYA,EAAQ,SAAWA,CAAO,EAC7D,CACL,EAC2B,KAAMZ,GAAaD,GAAYhN,EAAQ,GAAGiN,CAAQ,CAAC,CAC9E,CACA,MAAMa,GAAgB,GACtB,SAASC,GAAM/N,EAAQ,CACrB,GAAI,CAAC2N,GAAe3N,CAAM,EACxB,OAAIA,KAAU8N,GACLA,GAAc9N,CAAM,EAEtB,QAAQ,UAEjB,MAAMgO,EAASR,GAAiBxN,CAAM,EACtC,OAAA8N,GAAc9N,CAAM,EAAI,QAAQ,IAC9BgO,EAAO,IACL,CAAC,CAACC,EAAYP,CAAW,IAAME,GAAgBK,EAAYP,CAAW,CACvE,CACF,EAAC,KAAK,IAAM,CACX,GAAIC,GAAe3N,CAAM,EACvB,OAAO+N,GAAM/N,CAAM,EAErB,OAAO8N,GAAc9N,CAAM,CAC/B,CAAG,EACM8N,GAAc9N,CAAM,CAC7B,CAgBA,IAAIkO,GAAwB,OAAO,sBAC/BC,GAAiB,OAAO,UAAU,eAClCC,GAAiB,OAAO,UAAU,qBAClCC,GAAc,CAACtb,EAAQub,IAAY,CACrC,IAAIxb,EAAS,GACb,QAASyb,KAAQxb,EACXob,GAAe,KAAKpb,EAAQwb,CAAI,GAAKD,EAAQ,QAAQC,CAAI,EAAI,IAC/Dzb,EAAOyb,CAAI,EAAIxb,EAAOwb,CAAI,GAC9B,GAAIxb,GAAU,MAAQmb,GACpB,QAASK,KAAQL,GAAsBnb,CAAM,EACvCub,EAAQ,QAAQC,CAAI,EAAI,GAAKH,GAAe,KAAKrb,EAAQwb,CAAI,IAC/Dzb,EAAOyb,CAAI,EAAIxb,EAAOwb,CAAI,GAEhC,OAAOzb,CACT,EACA,MAAM0b,GAAiB,CACrB,OAAQ,CACN,WAAY,CAAE,SAAU,YAAc,EACtC,YAAa,CAAE,SAAU,aAAe,EACxC,YAAa,CAAE,SAAU,UAAW,eAAgB,MAAQ,EAC5D,aAAc,CAAE,SAAU,UAAW,eAAgB,OAAS,CAC/D,EACD,KAAM,CACJ,MAAO,CAAE,MAAO,UAAW,IAAK,UAAW,KAAM,SAAW,EAC5D,OAAQ,CAAE,MAAO,QAAS,IAAK,UAAW,KAAM,SAAW,EAC3D,KAAM,CAAE,MAAO,OAAQ,IAAK,UAAW,KAAM,SAAW,EACxD,KAAM,CAAE,QAAS,OAAQ,MAAO,OAAQ,IAAK,UAAW,KAAM,SAAW,CAC1E,EACD,KAAM,CACJ,MAAO,CAAE,KAAM,UAAW,OAAQ,SAAW,EAC7C,OAAQ,CAAE,KAAM,UAAW,OAAQ,UAAW,OAAQ,SAAW,EACjE,KAAM,CACJ,KAAM,UACN,OAAQ,UACR,OAAQ,UACR,aAAc,OACf,EACD,KAAM,CACJ,KAAM,UACN,OAAQ,UACR,OAAQ,UACR,aAAc,OACf,CACF,CACH,EACA,SAASC,GAAyB,CAAE,OAAAzO,EAAQ,GAAArJ,GAAM,CAChD,QAAQ,KACN,8BAA8BA,wBAAyB4V,GACrDvM,CACN,EAAM,KAAK,MAAM,MAAM2N,GAAee,GAAkB,GAAI;AAAA;AAAA,2FAEiC,IAC7F,CACA,CACA,MAAMC,GAAiB,CACrB,eAAgB,KAChB,aAAc,IACd,QAASH,GACT,sBAAuB,GACvB,qBAAsB,OACtB,UAAW,EACb,EACM7b,GAAUgc,GAChB,SAASC,IAAa,CACpB,OAAOjc,EACT,CACA,SAASkc,GAAKhH,EAAM,CAClB,MAAM7J,EAAK6J,EAAM,CAAE,QAAAyC,GAAYtM,EAAI8Q,EAAOT,GAAYrQ,EAAI,CAAC,SAAS,CAAC,EACrE,IAAI+Q,EAAgBlH,EAAK,eACzB,GAAIA,EAAK,cACP,GAAI,CACE4D,qBAAkB,cAAc5D,EAAK,aAAa,IACpDkH,EAAgBlH,EAAK,cAExB,MAAC,CACA,QAAQ,KACN,qCAAqCA,EAAK,uCAClD,CACK,CAEH,OAAIiH,EAAK,wBACP,OAAOA,EAAK,sBACRA,EAAK,sBAAwB,KAC/BA,EAAK,qBAAuBL,GAE5B,QAAQ,KACN,uHACR,GAGE,OAAO,OAAO9b,GAASmc,EAAM,CAAE,cAAAC,CAAe,GAC1CzE,IACE,WAAYA,GACd,OAAO,OAAO3X,GAAQ,QAAQ,OAAQ2X,EAAQ,MAAM,EAElD,SAAUA,GACZ,OAAO,OAAO3X,GAAQ,QAAQ,KAAM2X,EAAQ,IAAI,EAE9C,SAAUA,GACZ,OAAO,OAAO3X,GAAQ,QAAQ,KAAM2X,EAAQ,IAAI,GAG7C0E,GAAQ,IAAID,CAAa,CAClC,CAEA,MAAME,GAAaxe,GAAS,EAAK,EAEjC,IAAIye,GAAc,OAAO,eACrBC,GAAa,OAAO,iBACpBC,GAAoB,OAAO,0BAC3BC,GAAwB,OAAO,sBAC/BC,GAAiB,OAAO,UAAU,eAClCC,GAAiB,OAAO,UAAU,qBAClCC,GAAoB,CAACzM,EAAK7P,EAAKzD,IAAUyD,KAAO6P,EAAMmM,GAAYnM,EAAK7P,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAAzD,CAAO,GAAIsT,EAAI7P,CAAG,EAAIzD,EAC1JggB,GAAmB,CAACxgB,EAAGC,IAAM,CAC/B,QAASqf,KAAQrf,IAAMA,EAAI,IACrBogB,GAAe,KAAKpgB,EAAGqf,CAAI,GAC7BiB,GAAkBvgB,EAAGsf,EAAMrf,EAAEqf,CAAI,CAAC,EACtC,GAAIc,GACF,QAASd,KAAQc,GAAsBngB,CAAC,EAClCqgB,GAAe,KAAKrgB,EAAGqf,CAAI,GAC7BiB,GAAkBvgB,EAAGsf,EAAMrf,EAAEqf,CAAI,CAAC,EAExC,OAAOtf,CACT,EACIygB,GAAgB,CAACzgB,EAAGC,IAAMigB,GAAWlgB,EAAGmgB,GAAkBlgB,CAAC,CAAC,EAChE,IAAIygB,GACJ,MAAMC,GAAiBnf,GAAS,IAAI,EACpC,SAASof,GAAcvD,EAAW,CAChC,OAAOA,EAAU,MAAM,GAAG,EAAE,IAAI,CAAC5c,EAAGsB,EAAG8e,IAAQA,EAAI,MAAM,EAAG9e,EAAI,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,SAChF,CACA,SAASub,GAAmBD,EAAWyD,EAAiBnB,GAAU,EAAG,eAAgB,CACnF,MAAMvE,EAAUwF,GAAcvD,CAAS,EACvC,OAAIyD,EACK,CAAC,GAAmB,IAAI,IAAI,CAAC,GAAG1F,EAAS,GAAGwF,GAAcE,CAAc,CAAC,CAAC,CAAC,EAE7E1F,CACT,CACA,SAASqE,IAAmB,CAC1B,OAAOiB,IAA4B,MACrC,CACAC,GAAe,UAAWI,GAAc,CACtCL,GAAUK,GAAgC,OACtC,OAAO,OAAW,KAAeA,GAAa,MAChD,SAAS,gBAAgB,aAAa,OAAQA,CAAS,CAE3D,CAAC,EACD,MAAMpf,GAAOof,GAAc,CACzB,GAAIA,GAAalD,GAA0BkD,CAAS,GAAKrC,GAAeqC,CAAS,EAAG,CAClF,KAAM,CAAE,aAAAC,GAAiBrB,KACzB,IAAIsB,EACJ,OAAI,OAAO,OAAW,KAAexB,GAAgB,GAAM,MAAQuB,EACjEC,EAAe,OAAO,WACpB,IAAMjB,GAAW,IAAI,EAAI,EACzBgB,CACR,EAEMhB,GAAW,IAAI,EAAI,EAEdlB,GAAMiC,CAAS,EAAE,KAAK,IAAM,CACjCJ,GAAe,IAAII,CAAS,CAClC,CAAK,EAAE,QAAQ,IAAM,CACf,aAAaE,CAAY,EACzBjB,GAAW,IAAI,EAAK,CAC1B,CAAK,EAEH,OAAOW,GAAe,IAAII,CAAS,CACrC,EACMhB,GAAUU,GAAcD,GAAiB,CAAE,EAAEG,EAAc,EAAG,CAClE,IAAAhf,EACF,CAAC,EAyBKuf,GAAyB,IACzB,OAAO,OAAW,IACb,KACF,OAAO,UAAU,UAAY,OAAO,UAAU,UAAU,CAAC,EAa5DC,GAAkBzhB,GAAO,CAC7B,MAAMoZ,EAAwB,OAAO,OAAO,IAAI,EAQhD,OAPoBQ,GAAQ,CAC1B,MAAMC,EAAW,KAAK,UAAUD,CAAG,EACnC,OAAIC,KAAYT,EACPA,EAAMS,CAAQ,EAEhBT,EAAMS,CAAQ,EAAI7Z,EAAG4Z,CAAG,CACnC,CAEA,EAEA,IAAI8H,GAAY,OAAO,eACnBC,GAAsB,OAAO,sBAC7BC,GAAe,OAAO,UAAU,eAChCC,GAAe,OAAO,UAAU,qBAChCC,GAAkB,CAAC1N,EAAK7P,EAAKzD,IAAUyD,KAAO6P,EAAMsN,GAAUtN,EAAK7P,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAAzD,CAAO,GAAIsT,EAAI7P,CAAG,EAAIzD,EACtJihB,GAAiB,CAACzhB,EAAGC,IAAM,CAC7B,QAASqf,KAAQrf,IAAMA,EAAI,IACrBqhB,GAAa,KAAKrhB,EAAGqf,CAAI,GAC3BkC,GAAgBxhB,EAAGsf,EAAMrf,EAAEqf,CAAI,CAAC,EACpC,GAAI+B,GACF,QAAS/B,KAAQ+B,GAAoBphB,CAAC,EAChCshB,GAAa,KAAKthB,EAAGqf,CAAI,GAC3BkC,GAAgBxhB,EAAGsf,EAAMrf,EAAEqf,CAAI,CAAC,EAEtC,OAAOtf,CACT,EACI0hB,GAAY,CAAC5d,EAAQub,IAAY,CACnC,IAAIxb,EAAS,GACb,QAASyb,KAAQxb,EACXwd,GAAa,KAAKxd,EAAQwb,CAAI,GAAKD,EAAQ,QAAQC,CAAI,EAAI,IAC7Dzb,EAAOyb,CAAI,EAAIxb,EAAOwb,CAAI,GAC9B,GAAIxb,GAAU,MAAQud,GACpB,QAAS/B,KAAQ+B,GAAoBvd,CAAM,EACrCub,EAAQ,QAAQC,CAAI,EAAI,GAAKiC,GAAa,KAAKzd,EAAQwb,CAAI,IAC7Dzb,EAAOyb,CAAI,EAAIxb,EAAOwb,CAAI,GAEhC,OAAOzb,CACT,EACA,MAAM8d,GAA0B,CAAC7K,EAAMtN,IAAS,CAC9C,KAAM,CAAE,QAAA6R,GAAYsE,KACpB,GAAI7I,KAAQuE,GAAW7R,KAAQ6R,EAAQvE,CAAI,EACzC,OAAOuE,EAAQvE,CAAI,EAAEtN,CAAI,EAE3B,MAAM,IAAI,MAAM,0BAA0BA,MAASsN,WAAc,CACnE,EACM8K,GAAwBT,GAC3BpS,GAAO,CACN,IAAI8S,EAAK9S,EAAI,CAAE,OAAAgC,EAAQ,OAAA+Q,CAAM,EAAKD,EAAIne,EAAUge,GAAUG,EAAI,CAAC,SAAU,QAAQ,CAAC,EAClF,GAAI9Q,GAAU,KACZ,MAAM,IAAI,MAAM,wDAAwD,EAE1E,OAAI+Q,IACFpe,EAAUie,GAAwB,SAAUG,CAAM,GAE7C,IAAI,KAAK,aAAa/Q,EAAQrN,CAAO,CAC7C,CACH,EACMqe,GAAsBZ,GACzBa,GAAO,CACN,IAAIC,EAAKD,EAAI,CAAE,OAAAjR,EAAQ,OAAA+Q,CAAM,EAAKG,EAAIve,EAAUge,GAAUO,EAAI,CAAC,SAAU,QAAQ,CAAC,EAClF,GAAIlR,GAAU,KACZ,MAAM,IAAI,MAAM,sDAAsD,EAExE,OAAI+Q,EACFpe,EAAUie,GAAwB,OAAQG,CAAM,EACvC,OAAO,KAAKpe,CAAO,EAAE,SAAW,IACzCA,EAAUie,GAAwB,OAAQ,OAAO,GAE5C,IAAI,KAAK,eAAe5Q,EAAQrN,CAAO,CAC/C,CACH,EACMwe,GAAsBf,GACzBgB,GAAO,CACN,IAAIC,EAAKD,EAAI,CAAE,OAAApR,EAAQ,OAAA+Q,CAAM,EAAKM,EAAI1e,EAAUge,GAAUU,EAAI,CAAC,SAAU,QAAQ,CAAC,EAClF,GAAIrR,GAAU,KACZ,MAAM,IAAI,MACR,4DACR,EAEI,OAAI+Q,EACFpe,EAAUie,GAAwB,OAAQG,CAAM,EACvC,OAAO,KAAKpe,CAAO,EAAE,SAAW,IACzCA,EAAUie,GAAwB,OAAQ,OAAO,GAE5C,IAAI,KAAK,eAAe5Q,EAAQrN,CAAO,CAC/C,CACH,EACM2e,GAAqB,CAACC,EAAK,KAAO,CACtC,IAAIC,EAAKD,EAAI,CACX,OAAAvR,EAAS0O,GAAkB,CAC5B,EAAG8C,EAAI7I,EAAOgI,GAAUa,EAAI,CAC3B,QACJ,CAAG,EACD,OAAOX,GAAsBH,GAAe,CAAE,OAAA1Q,CAAM,EAAI2I,CAAI,CAAC,CAC/D,EACM8I,GAAmB,CAAC9T,EAAK,KAAO,CACpC,IAAI+T,EAAK/T,EAAI,CACX,OAAAqC,EAAS0O,GAAkB,CAC5B,EAAGgD,EAAI/I,EAAOgI,GAAUe,EAAI,CAC3B,QACJ,CAAG,EACD,OAAOV,GAAoBN,GAAe,CAAE,OAAA1Q,CAAM,EAAI2I,CAAI,CAAC,CAC7D,EACMgJ,GAAmB,CAACC,EAAK,KAAO,CACpC,IAAIC,EAAKD,EAAI,CACX,OAAA5R,EAAS0O,GAAkB,CAC5B,EAAGmD,EAAIlJ,EAAOgI,GAAUkB,EAAI,CAC3B,QACJ,CAAG,EACD,OAAOV,GAAoBT,GAAe,CAAE,OAAA1Q,CAAM,EAAI2I,CAAI,CAAC,CAC7D,EACMmJ,GAAsB1B,GAE1B,CAAC9U,EAAS0E,EAAS0O,OAAuB,IAAIjD,qBAAkBnQ,EAAS0E,EAAQ4O,GAAY,EAAC,QAAS,CACrG,UAAWA,GAAU,EAAG,SAC5B,CAAG,CACH,EAEMmD,GAAgB,CAACpb,EAAIhE,EAAU,KAAO,CAC1C,IAAIqL,EAAI8S,EAAIG,EAAIC,EAChB,IAAIc,EAAarf,EACb,OAAOgE,GAAO,WAChBqb,EAAarb,EACbA,EAAKqb,EAAW,IAElB,KAAM,CACJ,OAAAtgB,EACA,OAAAsO,EAAS0O,GAAkB,EAC3B,QAASuD,CACV,EAAGD,EACJ,GAAIhS,GAAU,KACZ,MAAM,IAAI,MACR,iFACN,EAEE,IAAI1E,EAAU+Q,GAAO1V,EAAIqJ,CAAM,EAC/B,GAAI,CAAC1E,EACHA,GAAW4V,GAAMD,GAAMH,GAAM9S,EAAK4Q,GAAU,GAAI,uBAAyB,KAAO,OAASkC,EAAG,KAAK9S,EAAI,CAAE,OAAAgC,EAAQ,GAAArJ,EAAI,aAAAsb,CAAc,KAAM,KAAOhB,EAAKgB,IAAiB,KAAOf,EAAKva,UACvK,OAAO2E,GAAY,SAC5B,eAAQ,KACN,kCAAkC3E,wCAAyC,OAAO2E,iGACxF,EACWA,EAET,GAAI,CAAC5J,EACH,OAAO4J,EAET,IAAIxJ,EAASwJ,EACb,GAAI,CACFxJ,EAASggB,GAAoBxW,EAAS0E,CAAM,EAAE,OAAOtO,CAAM,CAC5D,OAAQ2F,EAAP,CACIA,aAAa,OACf,QAAQ,KACN,0BAA0BV,uBAC1BU,EAAE,OACV,CAEG,CACD,OAAOvF,CACT,EACMogB,GAAa,CAAChb,EAAGvE,IACdgf,GAAiBhf,CAAO,EAAE,OAAOuE,CAAC,EAErCib,GAAa,CAACpb,EAAGpE,IACd8e,GAAiB9e,CAAO,EAAE,OAAOoE,CAAC,EAErCqb,GAAe,CAAChb,EAAGzE,IAChB2e,GAAmB3e,CAAO,EAAE,OAAOyE,CAAC,EAEvCib,GAAU,CAAC1b,EAAIqJ,EAAS0O,GAAgB,IACrCrC,GAAO1V,EAAIqJ,CAAM,EAEpBsS,GAAUnhB,GAAQ,CAAC6d,GAAStC,EAAW,EAAG,IAAMqF,EAAa,EAC/C5gB,GAAQ,CAAC6d,EAAO,EAAG,IAAMkD,EAAU,EACnC/gB,GAAQ,CAAC6d,EAAO,EAAG,IAAMmD,EAAU,EACjChhB,GAAQ,CAAC6d,EAAO,EAAG,IAAMoD,EAAY,EAC1CjhB,GAAQ,CAAC6d,GAAStC,EAAW,EAAG,IAAM2F,EAAO,qYC9gBzDE,EAAAC,KAAG,mBAAmB,iBAItBC,EAAAD,KAAG,kBAAkB,8DARyCA,EAAK,6EAKd,GACvD,gHAKaxgB,qHAX4BwgB,EAAK,yUAF/CE,GAiBK5f,EAAA6f,EAAAC,CAAA,EAhBJC,EAGMF,EAAAG,CAAA,EAFLD,EACAC,EAAAC,CAAA,gBAEDF,EAGMF,EAAAK,CAAA,gBADLH,EAAsDG,EAAAC,CAAA,gBAEvDJ,EAOMF,EAAAO,CAAA,gBALLL,EAIAK,EAAAC,CAAA,qBAb+DX,EAAK,kDAA5BA,EAAK,oBAI5CY,EAAA,KAAAb,OAAAC,KAAG,mBAAmB,OAAAa,GAAAC,EAAAf,CAAA,EAItBa,EAAA,KAAAX,OAAAD,KAAG,kBAAkB,OAAAa,GAAAE,EAAAd,CAAA,gGAXpBD,EAAO,IAAIA,EAAK,IAAIA,EAAI,IAAAgB,GAAAhB,CAAA,sJARaA,EAAO,oDAHrCiB,GAAAC,EAAA,OAAAlB,OAAYA,EAAQ,2BACTA,EAAO,qBACbA,EAAI,sBAEHA,EAAM,GAAG,UAAYA,EAAc,qBACnCA,EAAO,GAAS,OAAN,GAAY,UAPzCE,GAiCK5f,EAAA4gB,EAAAd,CAAA,EAvBJC,EAEKa,EAAAC,CAAA,qIACAnB,EAAO,IAAIA,EAAK,IAAIA,EAAI,mHARaA,EAAO,oDAHrCiB,GAAAC,EAAA,OAAAlB,OAAYA,EAAQ,uCACTA,EAAO,iCACbA,EAAI,6BAEHA,EAAM,GAAG,UAAYA,EAAc,2BACnCA,EAAO,GAAS,OAAN,GAAY,0KAlB7B,QAAAoB,CAAuB,EAAAC,GACvB,QAAAC,CAAe,EAAAD,GACf,eAAAE,CAAsB,EAAAF,GACtB,SAAAG,CAAiB,EAAAH,GAEjB,MAAAI,CAAoB,EAAAJ,GACpB,QAAAK,CAAgB,EAAAL,GAChB,KAAAM,CAAa,EAAAN,GACb,OAAAO,CAAe,EAAAP,4CAIfD,EAAOS,ifCfZ,SAASC,GAAUC,EAAqB,CAC1C,IAAAC,EAAQ,CAAC,GAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,GAAG,EAC9CxjB,EAAI,EACR,KAAOujB,EAAM,KAAQvjB,EAAIwjB,EAAM,OAAS,GAChCD,GAAA,IACPvjB,IAEG,IAAAoN,EAAOoW,EAAMxjB,CAAC,EACV,cAAO,UAAUujB,CAAG,EAAIA,EAAMA,EAAI,QAAQ,CAAC,GAAKnW,CACzD,CCLO,SAASqW,GAAQ1R,EAAK,CAC5B,OAAO,OAAO,UAAU,SAAS,KAAKA,CAAG,IAAM,eAChD,CCMA,SAAS2R,GAAYlC,EAAKmC,EAAYC,EAAeC,EAAc,CAClE,GAAI,OAAOD,GAAkB,UAAYH,GAAQG,CAAa,EAAG,CAEhE,MAAME,EAAQD,EAAeD,EAEvBG,GAAYH,EAAgBD,IAAenC,EAAI,IAAM,EAAI,IACzDwC,EAASxC,EAAI,KAAK,UAAYsC,EAC9BG,EAASzC,EAAI,KAAK,QAAUuC,EAC5BG,GAAgBF,EAASC,GAAUzC,EAAI,SACvCzb,GAAKge,EAAWG,GAAgB1C,EAAI,GAC1C,OAAI,KAAK,IAAIzb,CAAC,EAAIyb,EAAI,KAAK,WAAa,KAAK,IAAIsC,CAAK,EAAItC,EAAI,KAAK,UAC3DqC,GAEPrC,EAAI,QAAU,GAEPiC,GAAQG,CAAa,EAAI,IAAI,KAAKA,EAAc,QAAO,EAAK7d,CAAC,EAAI6d,EAAgB7d,OAEnF,IAAI,MAAM,QAAQ6d,CAAa,EAErC,OAAOA,EAAc,IAAI,CAACllB,EAAG,IAC5BglB,GAAYlC,EAAKmC,EAAW,CAAC,EAAGC,EAAc,CAAC,EAAGC,EAAa,CAAC,CAAC,CACpE,EACQ,GAAI,OAAOD,GAAkB,SAAU,CAC7C,MAAMO,EAAa,GACnB,UAAWtb,KAAK+a,EAEfO,EAAWtb,CAAC,EAAI6a,GAAYlC,EAAKmC,EAAW9a,CAAC,EAAG+a,EAAc/a,CAAC,EAAGgb,EAAahb,CAAC,CAAC,EAGlF,OAAOsb,MAEP,OAAM,IAAI,MAAM,iBAAiB,OAAOP,UAAsB,EAEhE,CAWO,SAASI,GAAOvlB,EAAOoY,EAAO,GAAI,CACxC,MAAMzY,EAAQqB,GAAShB,CAAK,EACtB,CAAE,UAAA2lB,EAAY,IAAM,QAAAC,EAAU,GAAK,UAAAC,EAAY,GAAM,EAAGzN,EAE9D,IAAI0N,EAEAplB,EAEAqlB,EAEAb,EAAallB,EAEbolB,EAAeplB,EACfgmB,EAAW,EACXC,EAAyB,EACzBC,EAAc,GAMlB,SAAS/kB,EAAIC,EAAWgX,EAAO,GAAI,CAClCgN,EAAehkB,EACf,MAAMwO,EAASmW,EAAgB,GAC/B,OAAI/lB,GAAS,MAAQoY,EAAK,MAASmN,EAAO,WAAa,GAAKA,EAAO,SAAW,GAC7EW,EAAc,GACdJ,EAAYzlB,GAAG,EACf6kB,EAAa9jB,EACbzB,EAAM,IAAKK,EAAQolB,GACZ,QAAQ,YACLhN,EAAK,OAEf6N,EAAyB,IADZ7N,EAAK,OAAS,GAAO,GAAM,CAACA,EAAK,MACT,IACrC4N,EAAW,GAEPtlB,IACJolB,EAAYzlB,GAAG,EACf6lB,EAAc,GACdxlB,EAAOC,GAAMN,GAAQ,CACpB,GAAI6lB,EACH,OAAAA,EAAc,GACdxlB,EAAO,KACA,GAERslB,EAAW,KAAK,IAAIA,EAAWC,EAAwB,CAAC,EACxD,MAAMlD,EAAM,CACX,SAAAiD,EACA,KAAMT,EACN,QAAS,GACT,IAAMllB,EAAMylB,GAAa,GAAM,GACpC,EACUJ,EAAaT,GAAYlC,EAAKmC,EAAYllB,EAAOolB,CAAY,EACnE,OAAAU,EAAYzlB,EACZ6kB,EAAallB,EACbL,EAAM,IAAKK,EAAQ0lB,GACf3C,EAAI,UACPriB,EAAO,MAED,CAACqiB,EAAI,OAChB,CAAI,GAEK,IAAI,QAASoD,GAAW,CAC9BzlB,EAAK,QAAQ,KAAK,IAAM,CACnBkP,IAAUmW,GAAeI,GACjC,CAAI,CACJ,CAAG,EACD,CAED,MAAMZ,EAAS,CACd,IAAApkB,EACA,OAAQ,CAACjC,EAAIkZ,IAASjX,EAAIjC,EAAGkmB,EAAcplB,CAAK,EAAGoY,CAAI,EACvD,UAAWzY,EAAM,UACjB,UAAAgmB,EACA,QAAAC,EACA,UAAAC,CACF,EACC,OAAON,CACR,kNCpIU,SAAAa,EAAA,SAAuB,2xBAwCCC,GAAAC,EAAA,yBAAAvD,EAAK,IAAC,EAAO,OAAAA,KAAK,CAAC,qjBAoBnBsD,GAAAnX,EAAA,yBAAA6T,EAAQ,IAAC,EAAO,OAAAA,KAAQ,CAAC,mMA1B3DE,GA+CK5f,EAAA6f,EAAAC,CAAA,EA9CJC,GA6CKF,EAAAqD,CAAA,EAxCJnD,GAmBGmD,EAAAD,CAAA,EAlBFlD,GAICkD,EAAAE,CAAA,EACDpD,GAGCkD,EAAAG,CAAA,EACDrD,GAICkD,EAAAI,CAAA,EACDtD,GAGCkD,EAAAK,CAAA,EAEFvD,GAmBGmD,EAAArX,CAAA,EAlBFkU,GAIClU,EAAA0X,CAAA,EACDxD,GAGClU,EAAA2X,CAAA,EACDzD,GAIClU,EAAA4X,CAAA,EACD1D,GAGClU,EAAA6X,CAAA,iBAtC8BV,GAAAC,EAAA,yBAAAvD,EAAK,IAAC,EAAO,OAAAA,KAAK,CAAC,cAoBnBsD,GAAAnX,EAAA,yBAAA6T,EAAQ,IAAC,EAAO,OAAAA,KAAQ,CAAC,wFAzD/C,QAAAiE,EAAS,EAAI,EAAA5C,EAElB,MAAA6C,EAAM1B,GAAQ,GAAG,CAAC,uBAClB,MAAA2B,EAAS3B,GAAQ,GAAG,CAAC,2BAEvB4B,iBAEWC,GAAO,CACf,cAAQ,IAAG,CAAEH,EAAI,IAAK,KAAK,GAAG,GAAIC,EAAO,IAAM,UAAS,KACxD,cAAQ,IAAG,CAAED,EAAI,IAAM,MAAK,GAAG,GAAIC,EAAO,IAAK,SAAS,KACxD,cAAQ,IAAG,CAAED,EAAI,IAAM,MAAK,CAAC,GAAIC,EAAO,IAAK,OAAO,KACpD,cAAQ,IAAG,CAAED,EAAI,IAAK,KAAK,CAAC,GAAIC,EAAO,IAAM,MAAK,CAAC,oBAG3CjoB,GAAG,OACXmoB,EAAO,EACRD,GAAYloB,mBAGHooB,GAAO,CACf,cAAQ,IAAG,CAAEJ,EAAI,IAAK,KAAK,CAAC,GAAIC,EAAO,IAAM,MAAK,CAAC,KAEzDjoB,IAGDmnB,UAAO,KACNiB,IACuB,IAAAF,EAAa,kmBC9B5B,MAAAG,WAAoB,2BA8CpB,WAAAC,WAAyB,gMAkOZC,EAAAzE,KAAK,cAAc,8IAAxCE,EAAgD5f,EAAAokB,EAAAtE,CAAA,6CAA3B,CAAAjD,GAAAyD,EAAA,OAAA6D,OAAAzE,KAAK,cAAc,OAAAa,GAAA8D,EAAAF,CAAA,iMAxEnCG,EAAA5E,OAAY,WAAaA,EAAgB,KAAAA,OAAkB,QAAM6E,GAAA7E,CAAA,qBAWhEA,EAAQ,UAAA8E,MAWH9E,EAAc,KAAK,MAAQA,OAAe,QAAaA,EAAc,IAAI,EAAC,OAAA+E,GAE1E,GAAA/E,OAAmB,EAAC,OAAAgF,0BAIzBhF,EAAK,IAAAiF,GAAAjF,CAAA,uCAKN,OAAAA,OAAuB,KAAI,EA+BtBA,OAAkB,OAAM,wCAI5BA,EAAK,IAAAkF,GAAAlF,CAAA,sJA7DciB,GAAAd,EAAA,mBAAAH,OAAY,QAAQ,EAC3BiB,GAAAd,EAAA,YAAAH,OAAY,SAAS,+BAFvCE,EAyBK5f,EAAA6f,EAAAC,CAAA,4GA/BAJ,OAAY,WAAaA,EAAgB,KAAAA,OAAkB,4IA4B1DA,EAAK,6EArBciB,GAAAd,EAAA,mBAAAH,OAAY,QAAQ,kBAC3BiB,GAAAd,EAAA,YAAAH,OAAY,SAAS,sKA4DjCA,EAAK,2NAjEqBmF,EAAA,eAAAnF,EAAa,QAAK,IAAM,kGAFvDE,EAGC5f,EAAA6f,EAAAC,CAAA,UAD8BQ,EAAA,WAAAuE,OAAA,eAAAnF,EAAa,QAAK,IAAM,kFAqBzB,cAE9B,6DAHSoF,EAAApF,KAAiB,EAAC,0BADyD,SAC5E,aAAoB,GAAC,MAACA,EAAU,QAAC,IACzC,+DADSY,EAAA,MAAAwE,OAAApF,KAAiB,EAAC,KAAAa,GAAAwE,EAAAD,CAAA,eAAGpF,EAAU,oEAXhCA,EAAQ,yBAAb,OAAIxhB,GAAA,qKAACwhB,EAAQ,sBAAb,OAAIxhB,GAAA,6HAAJ,qDAOC4mB,EAAApF,MAAE,KAAI,SAAK,6BALPA,EAAC,IAAC,QAAU,KAAIsF,2DAKb,KAAG,8IAAV1E,EAAA,QAAAwE,OAAApF,MAAE,KAAI,KAAAa,GAAAwE,EAAAD,CAAA,yDAFL,IAAAG,EAAAzD,GAAU9B,EAAE,WAAS,CAAC,kDAAtBY,EAAA,QAAA2E,OAAAzD,GAAU9B,EAAE,WAAS,CAAC,OAAAa,GAAAnc,EAAA6gB,CAAA,iCAFtB,IAAAd,EAAA3C,GAAU9B,EAAE,WAAS,CAAC,WAAI8B,GAAU9B,EAAC,IAAC,MAAM,6BAApB,GAAC,oDAAzBY,EAAA,QAAA6D,OAAA3C,GAAU9B,EAAE,WAAS,CAAC,OAAAa,GAAA8D,EAAAF,CAAA,mBAAI3C,GAAU9B,EAAC,IAAC,MAAM,OAAAa,GAAAC,EAAAf,CAAA,qDAF1CC,EAAC,IAAC,OAAS,MAAIwF,GAAAxF,CAAA,kEAAfA,EAAC,IAAC,OAAS,wHAgBCA,EAAG,OAAOA,EAAa,MAAK,sBAA7CA,EAAe,gBAAiC,GAClD,gEADEA,EAAe,yBAAEA,EAAG,OAAOA,EAAa,MAAK,KAAEa,GAAAwE,EAAAD,CAAA,sEAoCjC,cAAApF,OAAY,SAAS,qEAArBY,EAAA,SAAA6E,EAAA,OAAAzF,OAAY,qIALX0F,EAAA,GAAA1F,MAAsB,OAxBhC2F,EAAA3F,MAAY,MAAI4F,GAAA5F,CAAA,oSAFvBE,EA6BK5f,EAAAulB,EAAAzF,CAAA,EA5BJC,GAmBKwF,EAAA1E,CAAA,yBAELd,GAMKwF,EAAAC,CAAA,EALJzF,GAICyF,EAAA5E,CAAA,mBAzBIlB,MAAY,+DAwBFY,EAAA,UAAA8E,OAAA,GAAA1F,MAAsB,kGAvB7BA,EAAQ,yBAAb,OAAIxhB,GAAA,uKAACwhB,EAAQ,sBAAb,OAAIxhB,GAAA,6HAAJ,2DAEKomB,EAAA5E,QAAM,GAAC+F,GAAA,IAGP/F,EAAC,IAAC,MAAQ,MAAIgG,GAAAhG,CAAA,IAGdA,EAAC,IAAC,MAAQ,MAAQA,EAAc,KAAIA,EAAc,IAACA,EAAC,MAAK,MAAIiG,GAAA,EAG7DC,EAAAlG,OAAkB,MAAImG,GAAAnG,CAAA,iLANtBA,EAAC,IAAC,MAAQ,uEAGVA,EAAC,IAAC,MAAQ,MAAQA,EAAc,KAAIA,EAAc,IAACA,EAAC,MAAK,8DAGzDA,OAAkB,oLATX,IAEZ,kDAEE,IAAAuF,EAAAvF,MAAE,KAAI,gDAANY,EAAA,QAAA2E,OAAAvF,MAAE,KAAI,KAAAa,GAAAnc,EAAA6gB,CAAA,sDAE0D,GAElE,yDAEG,KAAOvF,EAAe,IAAAA,QAAM,IAAI,QAAQ,CAAC,+BAAE,GAC9C,wDADG,KAAOA,EAAe,IAAAA,QAAM,IAAI,QAAQ,CAAC,OAAAa,GAAA8D,EAAAF,CAAA,iDAXxCzE,EAAC,IAAC,MAAQ,MAASA,EAAc,KAAIA,EAAc,IAACA,EAAC,MAAK,OAAIoG,GAAApG,CAAA,kEAA9DA,EAAC,IAAC,MAAQ,MAASA,EAAc,KAAIA,EAAc,IAACA,EAAC,MAAK,iJA+B/CA,EAAY,mDAAhCE,EAAoC5f,EAAA+lB,EAAAjG,CAAA,iCAAhBJ,EAAY,sFAtE7B,OAAAA,OAAW,UAAS,EAwEfA,OAAW,QAAO,iEApFfsG,GAAAnG,EAAA,QAAAoG,EAAA,QAAAvG,SAAUA,EAAa,sBACvBiB,GAAAd,EAAA,QAAAH,MAAUA,EAAM,KAAK,YAAcA,OAAkB,QAAQ,EACtDiB,GAAAd,EAAA,cAAAH,EAAY,gBAC9BA,OAAW,WAAaA,EAAM,KAAK,UACpCA,EACA,KAAAA,OAAkB,SAAS,EACViB,GAAAd,EAAA,aAAAH,OAAW,YAAY,uCAEzBA,EAAQ,IAAG,WAAa,QAAQ,iBACjCA,EAAQ,IAAG,IAAM,iBAAiB,UAVlDE,EAyFK5f,EAAA6f,EAAAC,CAAA,wMAxFS,CAAAjD,GAAAyD,EAAA,QAAA2F,OAAA,QAAAvG,SAAUA,EAAa,wDACvBiB,GAAAd,EAAA,QAAAH,MAAUA,EAAM,KAAK,YAAcA,OAAkB,QAAQ,mBACtDiB,GAAAd,EAAA,cAAAH,EAAY,gBAC9BA,OAAW,WAAaA,EAAM,KAAK,UACpCA,EACA,KAAAA,OAAkB,SAAS,kBACViB,GAAAd,EAAA,aAAAH,OAAW,YAAY,mEAEzBA,EAAQ,IAAG,WAAa,QAAQ,4BACjCA,EAAQ,IAAG,IAAM,iBAAiB,sFAjM7CwG,GAAK,GAELC,GAAS,GAEE,eAAAC,GACdpd,EACAqd,EAAyB,GAAI,CAG5B,YAAO,kBAAoB,WAC1B,OAAO,kBAAoB,OAASA,IAAW,QAKjDH,GAAM,KAAKld,CAAE,GACRmd,GAAQA,GAAS,OAAI,cAGpBlC,GAAI,EAEV,sBAAqB,SAChBqC,EAAG,CAAI,EAAG,CAAC,UAENpoB,EAAI,EAAGA,EAAIgoB,GAAM,OAAQhoB,IAAC,OAG5BqoB,EAFUL,GAAMhoB,CAAC,EAEH,yBAChBA,IAAM,GAAKqoB,EAAI,IAAM,OAAO,SAAWD,EAAI,CAAC,KAC/CA,EAAI,CAAC,EAAIC,EAAI,IAAM,OAAO,QAC1BD,EAAI,CAAC,EAAIpoB,GAIX,OAAO,SAAW,KAAKooB,EAAI,CAAC,EAAI,GAAI,SAAU,QAAQ,GAEtDH,GAAS,GACTD,GAAK,2DAYI,KAAAM,CAAmB,EAAAzF,EACnB,KAAA0F,EAAqB,IAAI,EAAA1F,GACzB,eAAA2F,CAA6B,EAAA3F,GAC7B,WAAA4F,CAAyB,EAAA5F,GACzB,OAAA6F,CAAuD,EAAA7F,EACvD,kBAAA8F,EAAmB,EAAK,EAAA9F,EACxB,OAAA+F,EAAQ,EAAI,EAAA/F,EACZ,eAAAgG,EAA+C,MAAM,EAAAhG,EACrD,SAAAvY,EAAyB,IAAI,EAAAuY,EAC7B,UAAAiG,EAAyD,IAAI,EAAAjG,EAC7D,SAAAkG,EAAgC,SAAS,EAAAlG,EACzC,cAAAmG,EAAe,YAAY,EAAAnG,EAC3B,UAAAoG,EAAW,EAAI,EAAApG,EACf,aAAAqG,EAAc,EAAK,EAAArG,EACnB,QAAAsG,EAAS,EAAK,EAAAtG,GACd,WAAAuG,CAAmB,EAAAvG,EAE1B/X,EAEAue,EAAS,GACTC,EAAc,EACdC,EAAa,EACbC,EAAyB,KACzBC,GAAgC,KAEhCC,GAA2B,EAC3BC,EAAgD,KAChDC,GACAC,EAAmC,KACnCC,EAAe,SAsCbC,GAAW,KAChBC,EAAA,EAAAzB,EAAMyB,EAAA,GAAAR,EAAUQ,EAAA,GAAAC,EAAgB,IAAI,SACpCX,EAAc,YAAY,IAAG,GAC7BU,EAAA,GAAAT,EAAa,CAAC,EACdF,EAAS,GACT3rB,eAGQA,IAAG,CACX,sBAAqB,KACpBssB,EAAA,GAAAT,GAAc,YAAY,MAAQD,GAAe,GAAI,EACjDD,GAAQ3rB,gBAILwsB,IAAU,CAClBF,EAAA,GAAAT,EAAa,CAAC,EACdS,EAAA,EAAAzB,EAAMyB,EAAA,GAAAR,EAAUQ,EAAA,GAAAC,EAAgB,IAAI,IAE/BZ,IACLA,EAAS,IAGVrD,GAAS,KACJqD,GAAQa,OAgBT,IAAAD,EAA+B,+CAmGnBJ,EAAYxG,8EA7DjBvY,EAAEuY,0qBApCRkF,IAAQ,MACXyB,EAAA,EAAAzB,EAAMiB,CAAO,EAEVjB,GAAO,MAAQiB,IAAYjB,SAC9BkB,IAAkB,YAAY,IAAG,EAAKH,GAAe,IAAOf,CAAG,EAC/DyB,EAAA,GAAAC,EAAgBR,GAAe,QAAQ,CAAC,GACxCO,EAAA,GAAAR,EAAUjB,CAAG,6BApFdyB,EAAA,GAAEN,GACFD,KAAmB,MAAQA,IAAkB,GAAM,CAAAF,EAChD,KACA,KAAK,IAAIA,EAAaE,GAAgB,CAAC,sBACpCX,GAAY,MAClBkB,EAAA,GAAAF,EAAe,EAAK,yBAIhBhB,GAAY,KACfkB,EAAA,GAAAL,EAAiBb,EAAS,IAAK9iB,GAAC,IAC3BA,EAAE,OAAS,MAAQA,EAAE,QAAU,KAC3B,OAAAA,EAAE,MAAQA,EAAE,UACTA,EAAE,UAAY,KACjB,OAAAA,EAAE,YAKXgkB,EAAA,GAAAL,EAAiB,IAAI,EAGlBA,GACHK,EAAA,GAAAJ,GAAsBD,EAAeA,EAAe,OAAS,CAAC,GAC1DE,IACCD,KAAwB,EAC3BI,EAAA,GAAAH,EAAa,MAAM,WAAa,IAAGA,CAAA,EAEnCG,EAAA,GAAAH,EAAa,MAAM,WAAa,QAAOA,CAAA,IAIzCG,EAAA,GAAAJ,GAAsB,MAAS,sBAgC5BlB,IAAW,UACdqB,KAEAG,8BAICpf,GACF6d,IACCD,IAAW,WAAaA,IAAW,aACpCR,GAAiBpd,EAAIse,CAAU,qDA0B7Be,EAAkBZ,EAAW,QAAQ,CAAC,gtrBCtLpCa,GAAQ,gCAAAC,GAAA,iBAAAC,GAAA,kBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,oBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,oBAAAC,GAAA,oBAAAC,KAWP,SAASC,IAA6B,CAC5C,IAAIC,EAAsB,GAE1B,UAAWC,KAAQ5B,GAAO,CACnB,MAAAxY,EAAQoa,EAAK,MAAM,GAAG,EAAE,IAAiB,QAAM,GAAG,EAAE,QAC1DD,EAAOna,CAAI,EAAKwY,GAAM4B,CAAI,EAA0B,QAG9C,OAAAD,CACR,CAEA,MAAME,GAAkBH,GAAc,EAEtC,UAAWE,KAAQC,GACNjQ,GAAAgQ,EAAMC,GAAgBD,CAAI,CAAC,EAGxC,eAAsBE,IAA2B,CAChD,MAAMrO,GAAK,CACV,eAAgB,KAChB,cAAesB,GAAuB,EACtC,CACF,CCnCA,MAAAgN,6BAAuC,kCAGjCC,GAA2B,2BAE1B,SAASC,GAAsBC,EAAgC,CACrEH,GAAWC,GAA0BE,CAAW,CACjD,CAEO,SAASC,IAAiD,CAChE,OAAOC,GAAWJ,EAAwB,CAC3C,2aCqDU,SAAAvH,GAAS,WAAAsH,GAAY,sBAAAM,EAAA,SAAqC,oFAgVtDjL,EAAQ,UACXA,EAAa,UACd,kBACS,gBACJ,iBACC,2BAEPA,EAAE,mJAPGA,EAAQ,0BACXA,EAAa,8DAMfA,EAAE,0MAoBFuF,EAAAvF,MAAG,4BAA4B,yEAAnCE,GAAwC5f,EAAAkE,EAAA4b,CAAA,kBAApCQ,EAAA,YAAA2E,OAAAvF,MAAG,4BAA4B,OAAAa,GAAAnc,EAAA6gB,CAAA,4EAbjC,SACM,iBAOP,iCACgC,OAC/B,oBACF,EATuCe,GAAA7pB,EAAA,OAAAyuB,EAAA,iCAAAlL,+BAA8BA,EAAkB,IAAC,MACrFA,EAAQ,6BACOA,EAAkB,IAAC,YAClCA,OAAQ,OACR,SAAS,2EANZE,GAWG5f,EAAAkE,EAAA4b,CAAA,UAVKC,GASN7b,EAAA/H,CAAA,0BARqCmkB,EAAA,SAAAsK,OAAA,iCAAAlL,+BAA8BA,EAAkB,IAAC,MACrFA,EAAQ,6BACOA,EAAkB,IAAC,YAClCA,OAAQ,OACR,SAAS,sEARDA,EAAM,KAAE,SAAW,IAAE,uBAC3B,OAAAA,EAAO,aAAW,eAAiBA,EAAO,aAAW,WAAaA,MAAO,oBAAmB4F,0LAFnG1F,GAkBK5f,EAAA6f,EAAAC,CAAA,EAjBJC,GAA8CF,EAAA3b,CAAA,EAA3C6b,GAAwC7b,EAAA2mB,CAAA,yDAA/BnL,EAAM,KAAE,SAAW,IAAE,KAAAa,GAAA8D,EAAAF,CAAA,yIA8B9BzE,EAAM,kBACIA,EAAQ,IAAIA,EAAM,IAAC,yBACrBA,EAAiB,uCAErBA,EAAO,oCAIDA,EAAQ,uaARlBA,EAAM,8BACIA,EAAQ,IAAIA,EAAM,IAAC,qCACrBA,EAAiB,0DAErBA,EAAO,qDAIDA,EAAQ,mTAhBR,aAAAA,MAAO,aACf,KAAAA,MAAO,cACHA,EAAK,qFAFDY,EAAA,UAAAwK,EAAA,aAAApL,MAAO,cACfY,EAAA,UAAAwK,EAAA,KAAApL,MAAO,4BACHA,EAAK,2JAtCXA,EAAa,MAAK,WAAaA,EAAa,MAAK,UAAO,EAAOA,EAAM,KAAIA,EAAM,KAAE,gBAAaqL,GAAArL,CAAA,8CAkC/FA,EAAM,KAAE,eAAiBA,EAAK,MAOzBA,EAAM,KAAIA,EAAM,KAAIA,EAAS,sJAzCjCA,EAAa,MAAK,WAAaA,EAAa,MAAK,UAAO,EAAOA,EAAM,KAAIA,EAAM,KAAE,icAT9E,QAAAA,MAAaA,EAAQ,iBAEtB,OAAAA,MAASA,EAAI,+CAIb,OAAAA,QAAkB,8MANjBY,EAAA,QAAA0K,EAAA,QAAAtL,MAAaA,EAAQ,+BAEtBY,EAAA,SAAA0K,EAAA,OAAAtL,MAASA,EAAI,sFAIbY,EAAA,WAAA0K,EAAA,OAAAtL,QAAkB,iNA3WtB,IAAA7b,eAEKonB,IAAyB,CAI3B,MAAAC,EAAevtB,GAAQ,IAEvBkX,MAAU,IAEVsW,EAAQ,IAAO,qBAAsBnb,GAAO,CACjDA,EAAQ,QAASob,GAAK,CACjB,GAAAA,EAAM,eAAc,CACnB,IAAAC,EAA0BxW,EAAI,IAAIuW,EAAM,MAAwB,EAChEC,IAAQ,QACXH,EAAa,OAAQ7mB,IAAY,IAAAA,EAAI,CAAAgnB,CAAa,EAAG,EAAI,kBAKpDC,EAASC,EAAaviB,EAAkB,CAChD6L,EAAI,IAAI7L,EAAIuiB,CAAG,EACfJ,EAAS,QAAQniB,CAAE,SAGX,SAAAsiB,EAAU,UAAWJ,EAAa,WAGtC,MAAAA,GAAeD,GAAyB,EAiG/B,eAAAO,GACdC,EAA0B,IAEtBA,EAAW,CACR,MAAAzd,MAAa,UACb0d,EAAmB,MAAM,KAC9B1d,EAAO,gBAAgByd,EAAa,WAAW,EAAE,KAAK,QAAQ,KAG3DC,EACM,QAAAC,KAAgBD,EAAgB,CACpC,IAAAE,EAAa,SAAS,cAAcD,EAAa,OAAO,KAC5D,MAAM,KAAKA,EAAa,UAAU,EAAE,QAAS3F,GAAI,CAChD4F,EAAW,aAAa5F,EAAK,KAAMA,EAAK,KAAK,IAE9C4F,EAAW,YAAcD,EAAa,YAGrCC,EAAW,SAAW,QACtBA,EAAW,aAAa,UAAU,GAK5B,MAAAC,EAHc,MAAM,KACzB,SAAS,KAAK,qBAAqB,MAAM,OAEd,KAAM7iB,GAEhCA,EAAG,aAAa,UAAU,GACzB4iB,EAAW,aAAa,UAAU,GAClC,CAAA5iB,EAAG,YAAY4iB,CAAU,MAGxBC,EAAO,CACV,SAAS,KAAK,aAAaD,EAAYC,CAAO,YAKhD,SAAS,KAAK,YAAYD,CAAU,2EAxHxCxB,KAEM,MAAA0B,EAAWnB,SAEN,WAAArD,CAAmB,EAAAvG,GACnB,QAAAC,CAAe,EAAAD,GACf,eAAAE,CAAsB,EAAAF,GACtB,SAAAgL,CAAiB,EAAAhL,GACjB,SAAAG,CAAiB,EAAAH,EACjB,YAAAiL,EAA+B,QAAQ,EAAAjL,GACvC,mBAAAkL,CAA2B,EAAAlL,GAC3B,UAAAmL,CAAkB,EAAAnL,GAClB,KAAAM,CAAa,EAAAN,GACb,MAAAoL,CAAc,EAAApL,EACrBqL,EAGO,WAAAC,EAAsCC,EAAiB,EAAAvL,GACvD,OAAAwL,CAAgD,EAAAxL,GAChD,aAAAyL,CAA4D,EAAAzL,EAC5D,cAAA0L,EAAwC,MAAS,EAAA1L,EACxD0L,IACHlC,GAAsBkC,CAAY,EAElCA,EAAa,iBAAiB,kBAAoBC,GAAK,CACtDxE,EAAA,GAAAhB,EAAgBwF,EAAsB,OAAS,KAAK,KAG3C,yBAAAC,EAAqC,KAAK,EAAA5L,EACrDsJ,GAAW,uBAAwBsC,CAAoB,EAC5C,wBAAAC,EAAkDC,GACxD,gBAAYA,CAAG,GAAA9L,EACpBsJ,GAAW,sBAAuBuC,CAAmB,MAE1C,MAAAzL,CAAoB,EAAAJ,GACpB,KAAA+L,CAAmB,EAAA/L,GACnB,IAAAgM,CAAkB,EAAAhM,EAEzBwK,EAAM1nB,KAENmpB,GACH,UAEGlM,GACAmM,EAAQ,GACRC,GAAkB,GAClBC,EACAjG,EAAekG,EAAG,gBAAgB,EAAI,MACtCC,GACAC,GAMAC,GAA+C,KACpC,eAAAC,EAAiBC,EAAyB,CACpDA,IACHF,GAAsBG,GACrBD,EACAzM,EACAuM,IAAuB,MAAS,SAG5BlB,EAAUc,EAAO,KAAO,aAAc,SAAS,IAAI,EACpDA,EAAO,mBAEN,QAAQ,IACbA,EAAO,YAAY,IAAKQ,GAEtBA,EAAW,WAAW,OAAO,GAAKA,EAAW,WAAW,QAAQ,EAEzDtB,EAAUsB,EAAY,SAAS,IAAI,EAGpC,MAAMR,EAAO,KAAO,IAAMQ,CAAU,EACzC,KAAMC,IAAaA,GAAS,KAC5B,QAAMH,IAAU,CAChBC,GAAWD,GAAYzM,CAAO,MAgD1B,SAAA6M,EAAkB7tB,EAAsB,CAC1C,MAAA8tB,EAAc,OAAO,kBAAoB,cAE3CC,KACAD,EACHC,EAAiB,aAGX,MAAAC,OADU,IAAI,OAAO,SAAS,SAAQ,GACC,aAAa,IACzD,SAAS,EAEVD,EAAiB/B,GAAcgC,IAAkB,SAG9C,OAAAD,IAAmB,QAAUA,IAAmB,QACnDE,EAAYjuB,EAAQ+tB,CAAc,EAElCA,EAAiBG,EAAkBluB,CAAM,EAEnC+tB,EAGC,SAAAG,EAAkBluB,EAAsB,CAC1C,MAAAmuB,EAAQC,IACd,QACG,WAAW,8BAA8B,GACzC,iBAAiB,SAAUA,CAAa,WAElCA,GAAa,CACjB,IAAAC,GAA2B,QAAQ,aACtC,8BAA8B,EAC7B,QACC,OACA,QAEH,OAAAJ,EAAYjuB,EAAQquB,EAAM,EACnBA,UAEDF,WAGCF,EAAYjuB,EAAwBmuB,EAAuB,OAC7DG,EAAqBpN,EAAWlhB,EAAO,cAAiB,SAAS,KACjEuuB,GAAarN,EAAWlhB,EAASA,EAAO,cAC9CuuB,GAAW,MAAM,WAAa,8BAC1BJ,IAAU,OACbG,EAAmB,UAAU,IAAI,MAAM,EAEvCA,EAAmB,UAAU,OAAO,MAAM,MAIxC1H,EAAM,CACT,QAAS,GACT,YAAa,UACb,OAAQ,WACR,OAAQ,YAGL4H,EACAC,EAAY,GACP,SAAAC,EAAcC,EAAoB,CAC1CzG,EAAA,GAAAtB,EAAS+H,CAAO,EAEjB5L,GAAO,eACNsK,GAAoBQ,EAAkB/M,EAAO,SAGvC8N,EAAkB,OAAO,eAEzBC,EAAc,OAAO,wBAE3BvB,GACyBsB,IAAoB,MAElC,2BAAAC,GAAgB,SAAWA,EAAc,OAEhD/B,GAAQ3L,GAAS4L,GAAO,SAAS,OAErC7E,EAAA,GAAAsG,QAAYjC,EAAOe,GAAO,CACzB,gBAAiBoB,CAAA,SAElBvB,EAASqB,EAAI,MAAM,EACnB,OAAO,iBAAmBrB,EAAO,cAEjCvG,EAAM,CACL,QAAS,GACT,YAAa,WACb,OAAQ,UACR,OAAQ,kBAGH4G,EAAiBL,EAAO,GAAG,QAC3B3B,GAAqB2B,EAAO,IAAI,EACtCjF,EAAA,GAAAuG,EAAY,EAAI,EAChB,OAAO,aAAetB,EAAO,SAE7BrB,EAAS,QAAQ,EAEbqB,EAAO,UACV,sBACS,KAAAL,CAAI,MAAS,IAAIQ,EAAO,MAC5BT,GAAG,IAAO,IAAG,UAAWC,cAAI,EAChCV,EAAW,IAAO,YAAYS,EAAG,EACjCT,EAAY,UAAS,eAAmBM,GAAK,CACxCA,GAAM,OAAS,WAClBxE,EAAA,GAAAsG,QAAYjC,EAAOe,GAAO,CACzB,gBAAiBoB,CAAA,SAGlBvB,EAASqB,EAAI,MAAM,EACnB,OAAO,iBAAmBrB,EAAO,eAC3BK,EAAiBL,EAAO,GAAG,KAGjC,OAIL9C,GAAW,eAAgBmC,CAAY,MAWnCsC,EACAC,iBAEWC,GAAU,MACxBF,GAAM,MAAAG,GAAA,WAAiB,sBAAiB,wNAAG,OAAO,iBAEpCC,GAAS,MACvBH,GAAK,MAAAE,GAAA,WAAiB,qBAAgB,wcAAG,OAAO,WAGxCE,GAAS,CACbhC,EAAO,cAAe+B,EAAS,EAC9BF,EAAU,QAWVI,GAAkB,CACvB,eAAc,CACb,YAAahC,EAAG,oBAAoB,EACpC,aAAcA,EAAG,qBAAqB,EACtC,YAAaA,EAAG,oBAAoB,EACpC,cAAeA,EAAG,sBAAsB,EACxC,OAAQA,EAAG,qBAAqB,GAEjC,MAAMlmB,EAAkB,QAChB,mBAAmBkmB,EAAG,0BAA0B,IAExD,YAAYlmB,EAAoBmoB,EAAY,QACpC,mBAAkB;AAAA;AAAA;AAAA;AAAA,oEAEvB,KAAK,eAAenoB,CAAK,GAAK;AAAA;AAAA,6FACoEmoB;AAAA;AAAA,QAAI,IAK1GtM,GAAO,UACNmI,GAAa,SAASK,EAAKzK,EAAO,+1BAzP5BqM,GAAQ,QACLA,EAAO,2BAmMhBjF,EAAA,GAAE8E,GACD,CAAAC,GAASrG,EAAO,cAAgB,QAC9B,UACC,CAAAqG,GAASrG,EAAO,cAAgB,QACjC,QACAA,EAAO,WAAW,0CAEnBuG,IAAWhB,GAASmD,EAAc/D,CAAG,IAAM4D,EAAS,sBAiDhDjC,IACNpM,GAAQ,cACH,gBAAY,UACf,QAAS,GACT,WAAY,GACZ,SAAU", "names": ["noop", "identity", "x", "run", "fn", "run_all", "fns", "is_function", "thing", "safe_not_equal", "a", "b", "subscribe", "store", "callbacks", "callback", "unsub", "get_store_value", "value", "_", "split_css_unit", "split", "is_client", "now", "raf", "cb", "tasks", "run_tasks", "task", "loop", "fulfill", "subscriber_queue", "readable", "start", "writable", "stop", "subscribers", "set", "new_value", "run_queue", "subscriber", "i", "update", "invalidate", "derived", "stores", "initial_value", "single", "stores_array", "auto", "started", "values", "pending", "cleanup", "sync", "result", "unsubscribers", "space_logo", "isMergeableObject", "isNonNullObject", "isSpecial", "stringValue", "isReactElement", "canUseSymbol", "REACT_ELEMENT_TYPE", "emptyTarget", "val", "cloneUnlessOtherwiseSpecified", "options", "deepmerge", "defaultArrayMerge", "target", "source", "element", "getMergeFunction", "key", "customMerge", "getEnumerableOwnPropertySymbols", "symbol", "get<PERSON><PERSON><PERSON>", "propertyIsOnObject", "object", "property", "propertyIsUnsafe", "mergeObject", "destination", "sourceIsArray", "targetIsArray", "sourceAndTargetTypesMatch", "array", "prev", "next", "deepmerge_1", "cjs", "__extends", "__assign", "__rest", "__decorate", "__param", "__esDecorate", "__runInitializers", "__prop<PERSON>ey", "__setFunctionName", "__metadata", "__awaiter", "__generator", "__exportStar", "__values", "__read", "__spread", "__spreadA<PERSON>ys", "__spread<PERSON><PERSON>y", "__await", "__asyncGenerator", "__asyncDelegator", "__asyncValues", "__makeTemplateObject", "__importStar", "__importDefault", "__classPrivateFieldGet", "__classPrivateFieldSet", "__classPrivateFieldIn", "__createBinding", "__addDisposableResource", "__disposeResources", "factory", "root", "global", "createExporter", "module", "exports", "previous", "id", "v", "exporter", "extendStatics", "d", "p", "__", "t", "s", "n", "e", "decorators", "desc", "c", "r", "paramIndex", "decorator", "ctor", "descriptorIn", "contextIn", "initializers", "extraInitializers", "accept", "f", "kind", "descriptor", "done", "context", "thisArg", "useValue", "name", "prefix", "metadataKey", "metadataValue", "_arguments", "P", "generator", "adopt", "resolve", "reject", "fulfilled", "step", "rejected", "body", "y", "g", "verb", "op", "m", "o", "k", "k2", "ar", "error", "il", "j", "jl", "to", "from", "pack", "l", "q", "resume", "settle", "cooked", "raw", "__setModuleDefault", "mod", "receiver", "state", "env", "async", "dispose", "_SuppressedError", "suppressed", "message", "fail", "rec", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "types", "TYPE", "SKELETON_TYPE", "isLiteralElement", "el", "isArgumentElement", "isNumberElement", "isDateElement", "isTimeElement", "isSelectElement", "isPluralElement", "isPoundElement", "isTagElement", "isNumberSkeleton", "isDateTimeSkeleton", "createLiteralElement", "createNumberElement", "style", "regex_generated", "dateTime", "DATE_TIME_REGEX", "parseDateTimeSkeleton", "skeleton", "match", "len", "number", "tslib_1", "require$$0", "regex_generated_1", "require$$1", "parseNumberSkeletonFromString", "stringTokens", "tokens", "_i", "stringTokens_1", "stringToken", "stemAndOptions", "stem", "_a", "options_1", "option", "icuUnitToEcma", "unit", "FRACTION_PRECISION_REGEX", "SIGNIFICANT_PRECISION_REGEX", "INTEGER_WIDTH_REGEX", "CONCISE_INTEGER_WIDTH_REGEX", "parseSignificantPrecision", "str", "g1", "g2", "parseSign", "parseConciseScientificAndEngineeringStem", "signDisplay", "parseNotationOptions", "opt", "signOpts", "parseNumberSkeleton", "tokens_1", "token", "all", "g3", "g4", "g5", "conciseScientificAndEngineeringOpts", "require$$2", "timeData_generated", "dateTimePatternGenerator", "time_data_generated_1", "getBestPattern", "locale", "skeletonCopy", "patternPos", "patternChar", "extraLength", "hourLen", "dayPeriodLen", "dayPeriodChar", "hourChar", "getDefaultHourSymbolFromLocale", "hourCycle", "languageTag", "regionTag", "hourCycles", "parser", "error_1", "types_1", "require$$3", "icu_skeleton_parser_1", "require$$4", "date_time_pattern_generator_1", "require$$5", "SPACE_SEPARATOR_START_REGEX", "SPACE_SEPARATOR_END_REGEX", "createLocation", "end", "hasNativeStartsWith", "hasNativeFromCodePoint", "hasNativeFromEntries", "hasNativeCodePointAt", "hasTrimStart", "hasTrimEnd", "hasNativeIsSafeInteger", "isSafeInteger", "REGEX_SUPPORTS_U_AND_Y", "re", "RE", "startsWith", "search", "position", "fromCodePoint", "codePoints", "elements", "length", "code", "fromEntries", "entries", "obj", "entries_1", "codePointAt", "index", "size", "first", "second", "trimStart", "trimEnd", "flag", "matchIdentifierAtIndex", "IDENTIFIER_PREFIX_RE_1", "_isWhiteSpace", "_isPatternSyntax", "<PERSON><PERSON><PERSON>", "nestingLevel", "parentArgType", "expectingCloseTag", "char", "_isAlpha", "startPosition", "tagName", "childrenResult", "children", "endTagStartPosition", "closingTagNameStartPosition", "closingTagName", "startOffset", "_isPotentialElementNameChar", "parseQuoteResult", "parseUnquotedResult", "parseLeftAngleResult", "location", "_isAlphaOrSlash", "ch", "openingBracePosition", "startingPosition", "endOffset", "endPosition", "typeStartPosition", "argType", "typeEndPosition", "styleAndLocation", "styleStartPosition", "styleLocation", "argCloseResult", "location_1", "dateTimePattern", "type", "typeEndPosition_1", "identifierAndLocation", "pluralOffset", "optionsResult", "location_2", "nestedBraces", "apostrophePosition", "expectCloseTag", "parsedFirstIdentifier", "has<PERSON>ther<PERSON><PERSON><PERSON>", "parsedSelectors", "selector", "selectorLocation", "fragmentResult", "expectNumberError", "invalidNumberError", "sign", "hasDigits", "decimal", "offset", "pattern", "currentOffset", "targetOffset", "nextCode", "codepoint", "parser_1", "pruneLocation", "els", "parse", "opts", "memoize", "cache", "cacheDefault", "serializer", "serializerDefault", "strategy", "strategyDefault", "isPrimitive", "monadic", "arg", "cache<PERSON>ey", "computedValue", "variadic", "args", "assemble", "serialize", "strategyVariadic", "strategyMonadic", "ObjectWithoutPrototypeCache", "strategies", "ErrorCode", "FormatError", "_super", "msg", "originalMessage", "_this", "InvalidValueError", "variableId", "InvalidValueTypeError", "Missing<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "formatters", "icu_messageformat_parser_1", "PART_TYPE", "mergeLiteral", "parts", "part", "lastPart", "isFormatXMLElementFn", "formatToParts", "locales", "formats", "currentPluralValue", "els_1", "varName", "value_1", "formatFn", "chunks", "rule", "core", "fast_memoize_1", "formatters_1", "mergeConfig", "c1", "c2", "mergeConfigs", "defaultConfig", "configs", "createFastMemoizeCache", "createDefaultFormatters", "IntlMessageFormat", "overrideFormats", "parseOpts", "supportedLocales", "core_1", "delve", "<PERSON><PERSON><PERSON>", "keys", "<PERSON><PERSON><PERSON>", "lookup<PERSON>ache", "addToCache", "path", "lookup", "refLocale", "getPossibleLocales", "getMessageFromDictionary", "dictionary", "$dictionary", "getLocaleDictionary", "hasLocaleDictionary", "localeDictionary", "getClosestAvailableLocale", "relatedLocales", "addMessages", "partials", "dictionary2", "newDictionary", "queue", "removeLoaderFromQ<PERSON>ue", "loader", "getLocaleQueue", "getLocalesQueues", "localeItem", "localeQueue", "hasLocaleQueue", "loadLocaleQueue", "partial", "activeFlushes", "flush", "queues", "localeName", "__getOwnPropSymbols$2", "__hasOwnProp$2", "__propIsEnum$2", "__objRest$1", "exclude", "prop", "defaultFormats", "defaultMissingKeyHandler", "getCurrentLocale", "defaultOptions", "getOptions", "init", "rest", "initialLocale", "$locale", "$isLoading", "__defProp$1", "__defProps", "__getOwnPropDescs", "__getOwnPropSymbols$1", "__hasOwnProp$1", "__propIsEnum$1", "__defNormalProp$1", "__spreadValues$1", "__spreadProps", "current", "internalLocale", "getSubLocales", "arr", "fallback<PERSON><PERSON><PERSON>", "newLocale", "loadingDelay", "loadingTimer", "getLocaleFromNavigator", "monadicMemoize", "__defProp", "__getOwnPropSymbols", "__hasOwnProp", "__propIsEnum", "__defNormalProp", "__spreadValues", "__objRest", "getIntlFormatterOptions", "createNumberFormatter", "_b", "format", "createDateFormatter", "_c", "_d", "createTimeFormatter", "_e", "_f", "getNumberFormatter", "_g", "_h", "getDate<PERSON><PERSON><PERSON><PERSON>", "_j", "getTimeFormatter", "_k", "_l", "getMessageFormatter", "formatMessage", "messageObj", "defaultValue", "formatTime", "formatDate", "formatNumber", "getJSON", "$format", "t2_value", "ctx", "t7_value", "insert", "div", "anchor", "append", "span0", "a0", "span1", "a1", "span3", "a2", "dirty", "set_data", "t2", "t7", "create_if_block", "toggle_class", "div1", "div0", "wrapper", "$$props", "version", "initial_height", "is_embed", "space", "display", "info", "loaded", "$$value", "pretty_si", "num", "units", "is_date", "tick_spring", "last_value", "current_value", "target_value", "delta", "velocity", "spring", "damper", "acceleration", "next_value", "stiffness", "damping", "precision", "last_time", "current_token", "inv_mass", "inv_mass_recovery_rate", "cancel_task", "fulfil", "onMount", "set_style", "g0", "svg", "path0", "path1", "path2", "path3", "path4", "path5", "path6", "path7", "margin", "top", "bottom", "dismounted", "animate", "loading", "tick", "onDestroy", "t0_value", "span", "t0", "if_block0", "create_if_block_16", "create_if_block_11", "create_if_block_14", "create_if_block_15", "create_if_block_10", "create_if_block_1", "style_transform", "t1_value", "t1", "create_if_block_13", "t_value", "create_if_block_12", "loader_changes", "style_width", "if_block", "create_if_block_3", "div3", "div2", "create_if_block_8", "create_if_block_7", "create_if_block_6", "if_block3", "create_if_block_5", "create_if_block_4", "p_1", "attr", "div_class_value", "items", "called", "scroll_into_view", "enable", "min", "box", "i18n", "eta", "queue_position", "queue_size", "status", "scroll_to_output", "timer", "show_progress", "progress", "variant", "loading_text", "absolute", "translucent", "border", "autoscroll", "_timer", "timer_start", "timer_diff", "old_eta", "eta_from_start", "eta_level", "progress_level", "last_progress_level", "progress_bar", "show_eta_bar", "start_timer", "$$invalidate", "formatted_eta", "stop_timer", "formatted_timer", "langs", "__vite_glob_0_0", "__vite_glob_0_1", "__vite_glob_0_2", "__vite_glob_0_3", "__vite_glob_0_4", "__vite_glob_0_5", "__vite_glob_0_6", "__vite_glob_0_7", "__vite_glob_0_8", "__vite_glob_0_9", "__vite_glob_0_10", "__vite_glob_0_11", "__vite_glob_0_12", "__vite_glob_0_13", "__vite_glob_0_14", "__vite_glob_0_15", "__vite_glob_0_16", "__vite_glob_0_17", "__vite_glob_0_18", "__vite_glob_0_19", "__vite_glob_0_20", "__vite_glob_0_21", "__vite_glob_0_22", "__vite_glob_0_23", "__vite_glob_0_24", "process_langs", "_langs", "lang", "processed_langs", "setupi18n", "setContext", "WORKER_PROXY_CONTEXT_KEY", "setWorkerProxyContext", "workerProxy", "getWorkerProxyContext", "getContext", "createEventDispatcher", "a_href_value", "strong", "login_changes", "create_if_block_2", "embed_changes", "create_intersection_store", "intersecting", "observer", "entry", "_el", "register", "_id", "add_custom_html_head", "head_string", "parsed_head_html", "head_element", "newElement", "matched", "dispatch", "app_mode", "theme_mode", "control_page_title", "container", "eager", "eventSource", "mount_css", "default_mount_css", "client", "upload_files", "worker_proxy", "event", "fetch_implementation", "EventSource_factory", "url", "host", "src", "loader_status", "ready", "render_complete", "config", "$_", "active_theme_mode", "api_url", "css_text_stylesheet", "mount_custom_css", "css_string", "prefix_css", "stylesheet", "response", "handle_theme_mode", "force_light", "new_theme_mode", "url_color_mode", "apply_theme", "sync_system_theme", "theme", "update_scheme", "_theme", "dark_class_element", "bg_element", "app", "css_ready", "handle_status", "_status", "gradio_dev_mode", "server_port", "Blocks", "<PERSON><PERSON>", "get_blocks", "__vitePreload", "get_login", "load_demo", "discussion_message", "site", "$intersecting"], "sources": ["../../../../node_modules/.pnpm/svelte@4.2.2/node_modules/svelte/src/runtime/internal/utils.js", "../../../../node_modules/.pnpm/svelte@4.2.2/node_modules/svelte/src/runtime/internal/environment.js", "../../../../node_modules/.pnpm/svelte@4.2.2/node_modules/svelte/src/runtime/internal/loop.js", "../../../../node_modules/.pnpm/svelte@4.2.2/node_modules/svelte/src/runtime/store/index.js", "../../../../js/app/src/images/spaces.svg", "../../../../node_modules/.pnpm/deepmerge@4.3.1/node_modules/deepmerge/dist/cjs.js", "../../../../node_modules/.pnpm/tslib@2.6.2/node_modules/tslib/tslib.js", "../../../../node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.7.1/node_modules/@formatjs/icu-messageformat-parser/lib/error.js", "../../../../node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.7.1/node_modules/@formatjs/icu-messageformat-parser/lib/types.js", "../../../../node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.7.1/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js", "../../../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.6.3/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js", "../../../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.6.3/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js", "../../../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.6.3/node_modules/@formatjs/icu-skeleton-parser/lib/number.js", "../../../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.6.3/node_modules/@formatjs/icu-skeleton-parser/lib/index.js", "../../../../node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.7.1/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js", "../../../../node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.7.1/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js", "../../../../node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.7.1/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js", "../../../../node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.7.1/node_modules/@formatjs/icu-messageformat-parser/lib/index.js", "../../../../node_modules/.pnpm/@formatjs+fast-memoize@2.2.0/node_modules/@formatjs/fast-memoize/lib/index.js", "../../../../node_modules/.pnpm/intl-messageformat@10.5.5/node_modules/intl-messageformat/lib/src/error.js", "../../../../node_modules/.pnpm/intl-messageformat@10.5.5/node_modules/intl-messageformat/lib/src/formatters.js", "../../../../node_modules/.pnpm/intl-messageformat@10.5.5/node_modules/intl-messageformat/lib/src/core.js", "../../../../node_modules/.pnpm/intl-messageformat@10.5.5/node_modules/intl-messageformat/lib/index.js", "../../../../node_modules/.pnpm/svelte-i18n@4.0.0_svelte@4.2.2/node_modules/svelte-i18n/dist/runtime.js", "../../../../js/app/src/Embed.svelte", "../../../../js/statustracker/static/utils.ts", "../../../../node_modules/.pnpm/svelte@4.2.2/node_modules/svelte/src/runtime/motion/utils.js", "../../../../node_modules/.pnpm/svelte@4.2.2/node_modules/svelte/src/runtime/motion/spring.js", "../../../../js/statustracker/static/Loader.svelte", "../../../../js/statustracker/static/index.svelte", "../../../../js/app/src/i18n.ts", "../../../../js/wasm/svelte/context.ts", "../../../../js/app/src/Index.svelte"], "sourcesContent": ["/** @returns {void} */\nexport function noop() {}\n\nexport const identity = (x) => x;\n\n/**\n * @template T\n * @template S\n * @param {T} tar\n * @param {S} src\n * @returns {T & S}\n */\nexport function assign(tar, src) {\n\t// @ts-ignore\n\tfor (const k in src) tar[k] = src[k];\n\treturn /** @type {T & S} */ (tar);\n}\n\n// Adapted from https://github.com/then/is-promise/blob/master/index.js\n// Distributed under MIT License https://github.com/then/is-promise/blob/master/LICENSE\n/**\n * @param {any} value\n * @returns {value is PromiseLike<any>}\n */\nexport function is_promise(value) {\n\treturn (\n\t\t!!value &&\n\t\t(typeof value === 'object' || typeof value === 'function') &&\n\t\ttypeof (/** @type {any} */ (value).then) === 'function'\n\t);\n}\n\n/** @returns {void} */\nexport function add_location(element, file, line, column, char) {\n\telement.__svelte_meta = {\n\t\tloc: { file, line, column, char }\n\t};\n}\n\nexport function run(fn) {\n\treturn fn();\n}\n\nexport function blank_object() {\n\treturn Object.create(null);\n}\n\n/**\n * @param {Function[]} fns\n * @returns {void}\n */\nexport function run_all(fns) {\n\tfns.forEach(run);\n}\n\n/**\n * @param {any} thing\n * @returns {thing is Function}\n */\nexport function is_function(thing) {\n\treturn typeof thing === 'function';\n}\n\n/** @returns {boolean} */\nexport function safe_not_equal(a, b) {\n\treturn a != a ? b == b : a !== b || (a && typeof a === 'object') || typeof a === 'function';\n}\n\nlet src_url_equal_anchor;\n\n/**\n * @param {string} element_src\n * @param {string} url\n * @returns {boolean}\n */\nexport function src_url_equal(element_src, url) {\n\tif (element_src === url) return true;\n\tif (!src_url_equal_anchor) {\n\t\tsrc_url_equal_anchor = document.createElement('a');\n\t}\n\t// This is actually faster than doing URL(..).href\n\tsrc_url_equal_anchor.href = url;\n\treturn element_src === src_url_equal_anchor.href;\n}\n\n/** @param {string} srcset */\nfunction split_srcset(srcset) {\n\treturn srcset.split(',').map((src) => src.trim().split(' ').filter(Boolean));\n}\n\n/**\n * @param {HTMLSourceElement | HTMLImageElement} element_srcset\n * @param {string | undefined | null} srcset\n * @returns {boolean}\n */\nexport function srcset_url_equal(element_srcset, srcset) {\n\tconst element_urls = split_srcset(element_srcset.srcset);\n\tconst urls = split_srcset(srcset || '');\n\n\treturn (\n\t\turls.length === element_urls.length &&\n\t\turls.every(\n\t\t\t([url, width], i) =>\n\t\t\t\twidth === element_urls[i][1] &&\n\t\t\t\t// We need to test both ways because Vite will create an a full URL with\n\t\t\t\t// `new URL(asset, import.meta.url).href` for the client when `base: './'`, and the\n\t\t\t\t// relative URLs inside srcset are not automatically resolved to absolute URLs by\n\t\t\t\t// browsers (in contrast to img.src). This means both SSR and DOM code could\n\t\t\t\t// contain relative or absolute URLs.\n\t\t\t\t(src_url_equal(element_urls[i][0], url) || src_url_equal(url, element_urls[i][0]))\n\t\t)\n\t);\n}\n\n/** @returns {boolean} */\nexport function not_equal(a, b) {\n\treturn a != a ? b == b : a !== b;\n}\n\n/** @returns {boolean} */\nexport function is_empty(obj) {\n\treturn Object.keys(obj).length === 0;\n}\n\n/** @returns {void} */\nexport function validate_store(store, name) {\n\tif (store != null && typeof store.subscribe !== 'function') {\n\t\tthrow new Error(`'${name}' is not a store with a 'subscribe' method`);\n\t}\n}\n\nexport function subscribe(store, ...callbacks) {\n\tif (store == null) {\n\t\tfor (const callback of callbacks) {\n\t\t\tcallback(undefined);\n\t\t}\n\t\treturn noop;\n\t}\n\tconst unsub = store.subscribe(...callbacks);\n\treturn unsub.unsubscribe ? () => unsub.unsubscribe() : unsub;\n}\n\n/**\n * Get the current value from a store by subscribing and immediately unsubscribing.\n *\n * https://svelte.dev/docs/svelte-store#get\n * @template T\n * @param {import('../store/public.js').Readable<T>} store\n * @returns {T}\n */\nexport function get_store_value(store) {\n\tlet value;\n\tsubscribe(store, (_) => (value = _))();\n\treturn value;\n}\n\n/** @returns {void} */\nexport function component_subscribe(component, store, callback) {\n\tcomponent.$$.on_destroy.push(subscribe(store, callback));\n}\n\nexport function create_slot(definition, ctx, $$scope, fn) {\n\tif (definition) {\n\t\tconst slot_ctx = get_slot_context(definition, ctx, $$scope, fn);\n\t\treturn definition[0](slot_ctx);\n\t}\n}\n\nfunction get_slot_context(definition, ctx, $$scope, fn) {\n\treturn definition[1] && fn ? assign($$scope.ctx.slice(), definition[1](fn(ctx))) : $$scope.ctx;\n}\n\nexport function get_slot_changes(definition, $$scope, dirty, fn) {\n\tif (definition[2] && fn) {\n\t\tconst lets = definition[2](fn(dirty));\n\t\tif ($$scope.dirty === undefined) {\n\t\t\treturn lets;\n\t\t}\n\t\tif (typeof lets === 'object') {\n\t\t\tconst merged = [];\n\t\t\tconst len = Math.max($$scope.dirty.length, lets.length);\n\t\t\tfor (let i = 0; i < len; i += 1) {\n\t\t\t\tmerged[i] = $$scope.dirty[i] | lets[i];\n\t\t\t}\n\t\t\treturn merged;\n\t\t}\n\t\treturn $$scope.dirty | lets;\n\t}\n\treturn $$scope.dirty;\n}\n\n/** @returns {void} */\nexport function update_slot_base(\n\tslot,\n\tslot_definition,\n\tctx,\n\t$$scope,\n\tslot_changes,\n\tget_slot_context_fn\n) {\n\tif (slot_changes) {\n\t\tconst slot_context = get_slot_context(slot_definition, ctx, $$scope, get_slot_context_fn);\n\t\tslot.p(slot_context, slot_changes);\n\t}\n}\n\n/** @returns {void} */\nexport function update_slot(\n\tslot,\n\tslot_definition,\n\tctx,\n\t$$scope,\n\tdirty,\n\tget_slot_changes_fn,\n\tget_slot_context_fn\n) {\n\tconst slot_changes = get_slot_changes(slot_definition, $$scope, dirty, get_slot_changes_fn);\n\tupdate_slot_base(slot, slot_definition, ctx, $$scope, slot_changes, get_slot_context_fn);\n}\n\n/** @returns {any[] | -1} */\nexport function get_all_dirty_from_scope($$scope) {\n\tif ($$scope.ctx.length > 32) {\n\t\tconst dirty = [];\n\t\tconst length = $$scope.ctx.length / 32;\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\tdirty[i] = -1;\n\t\t}\n\t\treturn dirty;\n\t}\n\treturn -1;\n}\n\n/** @returns {{}} */\nexport function exclude_internal_props(props) {\n\tconst result = {};\n\tfor (const k in props) if (k[0] !== '$') result[k] = props[k];\n\treturn result;\n}\n\n/** @returns {{}} */\nexport function compute_rest_props(props, keys) {\n\tconst rest = {};\n\tkeys = new Set(keys);\n\tfor (const k in props) if (!keys.has(k) && k[0] !== '$') rest[k] = props[k];\n\treturn rest;\n}\n\n/** @returns {{}} */\nexport function compute_slots(slots) {\n\tconst result = {};\n\tfor (const key in slots) {\n\t\tresult[key] = true;\n\t}\n\treturn result;\n}\n\n/** @returns {(this: any, ...args: any[]) => void} */\nexport function once(fn) {\n\tlet ran = false;\n\treturn function (...args) {\n\t\tif (ran) return;\n\t\tran = true;\n\t\tfn.call(this, ...args);\n\t};\n}\n\nexport function null_to_empty(value) {\n\treturn value == null ? '' : value;\n}\n\nexport function set_store_value(store, ret, value) {\n\tstore.set(value);\n\treturn ret;\n}\n\nexport const has_prop = (obj, prop) => Object.prototype.hasOwnProperty.call(obj, prop);\n\nexport function action_destroyer(action_result) {\n\treturn action_result && is_function(action_result.destroy) ? action_result.destroy : noop;\n}\n\n/** @param {number | string} value\n * @returns {[number, string]}\n */\nexport function split_css_unit(value) {\n\tconst split = typeof value === 'string' && value.match(/^\\s*(-?[\\d.]+)([^\\s]*)\\s*$/);\n\treturn split ? [parseFloat(split[1]), split[2] || 'px'] : [/** @type {number} */ (value), 'px'];\n}\n\nexport const contenteditable_truthy_values = ['', true, 1, 'true', 'contenteditable'];\n", "import { noop } from './utils.js';\n\nexport const is_client = typeof window !== 'undefined';\n\n/** @type {() => number} */\nexport let now = is_client ? () => window.performance.now() : () => Date.now();\n\nexport let raf = is_client ? (cb) => requestAnimationFrame(cb) : noop;\n\n// used internally for testing\n/** @returns {void} */\nexport function set_now(fn) {\n\tnow = fn;\n}\n\n/** @returns {void} */\nexport function set_raf(fn) {\n\traf = fn;\n}\n", "import { raf } from './environment.js';\n\nconst tasks = new Set();\n\n/**\n * @param {number} now\n * @returns {void}\n */\nfunction run_tasks(now) {\n\ttasks.forEach((task) => {\n\t\tif (!task.c(now)) {\n\t\t\ttasks.delete(task);\n\t\t\ttask.f();\n\t\t}\n\t});\n\tif (tasks.size !== 0) raf(run_tasks);\n}\n\n/**\n * For testing purposes only!\n * @returns {void}\n */\nexport function clear_loops() {\n\ttasks.clear();\n}\n\n/**\n * Creates a new task that runs on each raf frame\n * until it returns a falsy value or is aborted\n * @param {import('./private.js').TaskCallback} callback\n * @returns {import('./private.js').Task}\n */\nexport function loop(callback) {\n\t/** @type {import('./private.js').TaskEntry} */\n\tlet task;\n\tif (tasks.size === 0) raf(run_tasks);\n\treturn {\n\t\tpromise: new Promise((fulfill) => {\n\t\t\ttasks.add((task = { c: callback, f: fulfill }));\n\t\t}),\n\t\tabort() {\n\t\t\ttasks.delete(task);\n\t\t}\n\t};\n}\n", "import {\n\trun_all,\n\tsubscribe,\n\tnoop,\n\tsafe_not_equal,\n\tis_function,\n\tget_store_value\n} from '../internal/index.js';\n\nconst subscriber_queue = [];\n\n/**\n * Creates a `Readable` store that allows reading by subscription.\n *\n * https://svelte.dev/docs/svelte-store#readable\n * @template T\n * @param {T} [value] initial value\n * @param {import('./public.js').StartStopNotifier<T>} [start]\n * @returns {import('./public.js').Readable<T>}\n */\nexport function readable(value, start) {\n\treturn {\n\t\tsubscribe: writable(value, start).subscribe\n\t};\n}\n\n/**\n * Create a `Writable` store that allows both updating and reading by subscription.\n *\n * https://svelte.dev/docs/svelte-store#writable\n * @template T\n * @param {T} [value] initial value\n * @param {import('./public.js').StartStopNotifier<T>} [start]\n * @returns {import('./public.js').Writable<T>}\n */\nexport function writable(value, start = noop) {\n\t/** @type {import('./public.js').Unsubscriber} */\n\tlet stop;\n\t/** @type {Set<import('./private.js').SubscribeInvalidateTuple<T>>} */\n\tconst subscribers = new Set();\n\t/** @param {T} new_value\n\t * @returns {void}\n\t */\n\tfunction set(new_value) {\n\t\tif (safe_not_equal(value, new_value)) {\n\t\t\tvalue = new_value;\n\t\t\tif (stop) {\n\t\t\t\t// store is ready\n\t\t\t\tconst run_queue = !subscriber_queue.length;\n\t\t\t\tfor (const subscriber of subscribers) {\n\t\t\t\t\tsubscriber[1]();\n\t\t\t\t\tsubscriber_queue.push(subscriber, value);\n\t\t\t\t}\n\t\t\t\tif (run_queue) {\n\t\t\t\t\tfor (let i = 0; i < subscriber_queue.length; i += 2) {\n\t\t\t\t\t\tsubscriber_queue[i][0](subscriber_queue[i + 1]);\n\t\t\t\t\t}\n\t\t\t\t\tsubscriber_queue.length = 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @param {import('./public.js').Updater<T>} fn\n\t * @returns {void}\n\t */\n\tfunction update(fn) {\n\t\tset(fn(value));\n\t}\n\n\t/**\n\t * @param {import('./public.js').Subscriber<T>} run\n\t * @param {import('./private.js').Invalidator<T>} [invalidate]\n\t * @returns {import('./public.js').Unsubscriber}\n\t */\n\tfunction subscribe(run, invalidate = noop) {\n\t\t/** @type {import('./private.js').SubscribeInvalidateTuple<T>} */\n\t\tconst subscriber = [run, invalidate];\n\t\tsubscribers.add(subscriber);\n\t\tif (subscribers.size === 1) {\n\t\t\tstop = start(set, update) || noop;\n\t\t}\n\t\trun(value);\n\t\treturn () => {\n\t\t\tsubscribers.delete(subscriber);\n\t\t\tif (subscribers.size === 0 && stop) {\n\t\t\t\tstop();\n\t\t\t\tstop = null;\n\t\t\t}\n\t\t};\n\t}\n\treturn { set, update, subscribe };\n}\n\n/**\n * Derived value store by synchronizing one or more readable stores and\n * applying an aggregation function over its input values.\n *\n * https://svelte.dev/docs/svelte-store#derived\n * @template {import('./private.js').Stores} S\n * @template T\n * @overload\n * @param {S} stores - input stores\n * @param {(values: import('./private.js').StoresValues<S>, set: (value: T) => void, update: (fn: import('./public.js').Updater<T>) => void) => import('./public.js').Unsubscriber | void} fn - function callback that aggregates the values\n * @param {T} [initial_value] - initial value\n * @returns {import('./public.js').Readable<T>}\n */\n\n/**\n * Derived value store by synchronizing one or more readable stores and\n * applying an aggregation function over its input values.\n *\n * https://svelte.dev/docs/svelte-store#derived\n * @template {import('./private.js').Stores} S\n * @template T\n * @overload\n * @param {S} stores - input stores\n * @param {(values: import('./private.js').StoresValues<S>) => T} fn - function callback that aggregates the values\n * @param {T} [initial_value] - initial value\n * @returns {import('./public.js').Readable<T>}\n */\n\n/**\n * @template {import('./private.js').Stores} S\n * @template T\n * @param {S} stores\n * @param {Function} fn\n * @param {T} [initial_value]\n * @returns {import('./public.js').Readable<T>}\n */\nexport function derived(stores, fn, initial_value) {\n\tconst single = !Array.isArray(stores);\n\t/** @type {Array<import('./public.js').Readable<any>>} */\n\tconst stores_array = single ? [stores] : stores;\n\tif (!stores_array.every(Boolean)) {\n\t\tthrow new Error('derived() expects stores as input, got a falsy value');\n\t}\n\tconst auto = fn.length < 2;\n\treturn readable(initial_value, (set, update) => {\n\t\tlet started = false;\n\t\tconst values = [];\n\t\tlet pending = 0;\n\t\tlet cleanup = noop;\n\t\tconst sync = () => {\n\t\t\tif (pending) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tcleanup();\n\t\t\tconst result = fn(single ? values[0] : values, set, update);\n\t\t\tif (auto) {\n\t\t\t\tset(result);\n\t\t\t} else {\n\t\t\t\tcleanup = is_function(result) ? result : noop;\n\t\t\t}\n\t\t};\n\t\tconst unsubscribers = stores_array.map((store, i) =>\n\t\t\tsubscribe(\n\t\t\t\tstore,\n\t\t\t\t(value) => {\n\t\t\t\t\tvalues[i] = value;\n\t\t\t\t\tpending &= ~(1 << i);\n\t\t\t\t\tif (started) {\n\t\t\t\t\t\tsync();\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t() => {\n\t\t\t\t\tpending |= 1 << i;\n\t\t\t\t}\n\t\t\t)\n\t\t);\n\t\tstarted = true;\n\t\tsync();\n\t\treturn function stop() {\n\t\t\trun_all(unsubscribers);\n\t\t\tcleanup();\n\t\t\t// We need to set this to false because callbacks can still happen despite having unsubscribed:\n\t\t\t// Callbacks might already be placed in the queue which doesn't know it should no longer\n\t\t\t// invoke this derived store.\n\t\t\tstarted = false;\n\t\t};\n\t});\n}\n\n/**\n * Takes a store and returns a new one derived from the old one that is readable.\n *\n * https://svelte.dev/docs/svelte-store#readonly\n * @template T\n * @param {import('./public.js').Readable<T>} store  - store to make readonly\n * @returns {import('./public.js').Readable<T>}\n */\nexport function readonly(store) {\n\treturn {\n\t\tsubscribe: store.subscribe.bind(store)\n\t};\n}\n\nexport { get_store_value as get };\n", "export default \"__VITE_ASSET__d742374f__\"", "'use strict';\n\nvar isMergeableObject = function isMergeableObject(value) {\n\treturn isNonNullObject(value)\n\t\t&& !isSpecial(value)\n};\n\nfunction isNonNullObject(value) {\n\treturn !!value && typeof value === 'object'\n}\n\nfunction isSpecial(value) {\n\tvar stringValue = Object.prototype.toString.call(value);\n\n\treturn stringValue === '[object RegExp]'\n\t\t|| stringValue === '[object Date]'\n\t\t|| isReactElement(value)\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\n\nfunction isReactElement(value) {\n\treturn value.$$typeof === REACT_ELEMENT_TYPE\n}\n\nfunction emptyTarget(val) {\n\treturn Array.isArray(val) ? [] : {}\n}\n\nfunction cloneUnlessOtherwiseSpecified(value, options) {\n\treturn (options.clone !== false && options.isMergeableObject(value))\n\t\t? deepmerge(emptyTarget(value), value, options)\n\t\t: value\n}\n\nfunction defaultArrayMerge(target, source, options) {\n\treturn target.concat(source).map(function(element) {\n\t\treturn cloneUnlessOtherwiseSpecified(element, options)\n\t})\n}\n\nfunction getMergeFunction(key, options) {\n\tif (!options.customMerge) {\n\t\treturn deepmerge\n\t}\n\tvar customMerge = options.customMerge(key);\n\treturn typeof customMerge === 'function' ? customMerge : deepmerge\n}\n\nfunction getEnumerableOwnPropertySymbols(target) {\n\treturn Object.getOwnPropertySymbols\n\t\t? Object.getOwnPropertySymbols(target).filter(function(symbol) {\n\t\t\treturn Object.propertyIsEnumerable.call(target, symbol)\n\t\t})\n\t\t: []\n}\n\nfunction getKeys(target) {\n\treturn Object.keys(target).concat(getEnumerableOwnPropertySymbols(target))\n}\n\nfunction propertyIsOnObject(object, property) {\n\ttry {\n\t\treturn property in object\n\t} catch(_) {\n\t\treturn false\n\t}\n}\n\n// Protects from prototype poisoning and unexpected merging up the prototype chain.\nfunction propertyIsUnsafe(target, key) {\n\treturn propertyIsOnObject(target, key) // Properties are safe to merge if they don't exist in the target yet,\n\t\t&& !(Object.hasOwnProperty.call(target, key) // unsafe if they exist up the prototype chain,\n\t\t\t&& Object.propertyIsEnumerable.call(target, key)) // and also unsafe if they're nonenumerable.\n}\n\nfunction mergeObject(target, source, options) {\n\tvar destination = {};\n\tif (options.isMergeableObject(target)) {\n\t\tgetKeys(target).forEach(function(key) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(target[key], options);\n\t\t});\n\t}\n\tgetKeys(source).forEach(function(key) {\n\t\tif (propertyIsUnsafe(target, key)) {\n\t\t\treturn\n\t\t}\n\n\t\tif (propertyIsOnObject(target, key) && options.isMergeableObject(source[key])) {\n\t\t\tdestination[key] = getMergeFunction(key, options)(target[key], source[key], options);\n\t\t} else {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(source[key], options);\n\t\t}\n\t});\n\treturn destination\n}\n\nfunction deepmerge(target, source, options) {\n\toptions = options || {};\n\toptions.arrayMerge = options.arrayMerge || defaultArrayMerge;\n\toptions.isMergeableObject = options.isMergeableObject || isMergeableObject;\n\t// cloneUnlessOtherwiseSpecified is added to `options` so that custom arrayMerge()\n\t// implementations can use it. The caller may not replace it.\n\toptions.cloneUnlessOtherwiseSpecified = cloneUnlessOtherwiseSpecified;\n\n\tvar sourceIsArray = Array.isArray(source);\n\tvar targetIsArray = Array.isArray(target);\n\tvar sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n\n\tif (!sourceAndTargetTypesMatch) {\n\t\treturn cloneUnlessOtherwiseSpecified(source, options)\n\t} else if (sourceIsArray) {\n\t\treturn options.arrayMerge(target, source, options)\n\t} else {\n\t\treturn mergeObject(target, source, options)\n\t}\n}\n\ndeepmerge.all = function deepmergeAll(array, options) {\n\tif (!Array.isArray(array)) {\n\t\tthrow new Error('first argument should be an array')\n\t}\n\n\treturn array.reduce(function(prev, next) {\n\t\treturn deepmerge(prev, next, options)\n\t}, {})\n};\n\nvar deepmerge_1 = deepmerge;\n\nmodule.exports = deepmerge_1;\n", "/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global global, define, Symbol, Reflect, Promise, SuppressedError */\r\nvar __extends;\r\nvar __assign;\r\nvar __rest;\r\nvar __decorate;\r\nvar __param;\r\nvar __esDecorate;\r\nvar __runInitializers;\r\nvar __propKey;\r\nvar __setFunctionName;\r\nvar __metadata;\r\nvar __awaiter;\r\nvar __generator;\r\nvar __exportStar;\r\nvar __values;\r\nvar __read;\r\nvar __spread;\r\nvar __spreadArrays;\r\nvar __spreadArray;\r\nvar __await;\r\nvar __asyncGenerator;\r\nvar __asyncDelegator;\r\nvar __asyncValues;\r\nvar __makeTemplateObject;\r\nvar __importStar;\r\nvar __importDefault;\r\nvar __classPrivateFieldGet;\r\nvar __classPrivateFieldSet;\r\nvar __classPrivateFieldIn;\r\nvar __createBinding;\r\nvar __addDisposableResource;\r\nvar __disposeResources;\r\n(function (factory) {\r\n    var root = typeof global === \"object\" ? global : typeof self === \"object\" ? self : typeof this === \"object\" ? this : {};\r\n    if (typeof define === \"function\" && define.amd) {\r\n        define(\"tslib\", [\"exports\"], function (exports) { factory(createExporter(root, createExporter(exports))); });\r\n    }\r\n    else if (typeof module === \"object\" && typeof module.exports === \"object\") {\r\n        factory(createExporter(root, createExporter(module.exports)));\r\n    }\r\n    else {\r\n        factory(createExporter(root));\r\n    }\r\n    function createExporter(exports, previous) {\r\n        if (exports !== root) {\r\n            if (typeof Object.create === \"function\") {\r\n                Object.defineProperty(exports, \"__esModule\", { value: true });\r\n            }\r\n            else {\r\n                exports.__esModule = true;\r\n            }\r\n        }\r\n        return function (id, v) { return exports[id] = previous ? previous(id, v) : v; };\r\n    }\r\n})\r\n(function (exporter) {\r\n    var extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n\r\n    __extends = function (d, b) {\r\n        if (typeof b !== \"function\" && b !== null)\r\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n        extendStatics(d, b);\r\n        function __() { this.constructor = d; }\r\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n    };\r\n\r\n    __assign = Object.assign || function (t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n\r\n    __rest = function (s, e) {\r\n        var t = {};\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n            t[p] = s[p];\r\n        if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n            for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n                if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                    t[p[i]] = s[p[i]];\r\n            }\r\n        return t;\r\n    };\r\n\r\n    __decorate = function (decorators, target, key, desc) {\r\n        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n        if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n        return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n    };\r\n\r\n    __param = function (paramIndex, decorator) {\r\n        return function (target, key) { decorator(target, key, paramIndex); }\r\n    };\r\n\r\n    __esDecorate = function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n        function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n        var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n        var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n        var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n        var _, done = false;\r\n        for (var i = decorators.length - 1; i >= 0; i--) {\r\n            var context = {};\r\n            for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n            for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n            context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n            var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n            if (kind === \"accessor\") {\r\n                if (result === void 0) continue;\r\n                if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n                if (_ = accept(result.get)) descriptor.get = _;\r\n                if (_ = accept(result.set)) descriptor.set = _;\r\n                if (_ = accept(result.init)) initializers.unshift(_);\r\n            }\r\n            else if (_ = accept(result)) {\r\n                if (kind === \"field\") initializers.unshift(_);\r\n                else descriptor[key] = _;\r\n            }\r\n        }\r\n        if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n        done = true;\r\n    };\r\n\r\n    __runInitializers = function (thisArg, initializers, value) {\r\n        var useValue = arguments.length > 2;\r\n        for (var i = 0; i < initializers.length; i++) {\r\n            value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n        }\r\n        return useValue ? value : void 0;\r\n    };\r\n\r\n    __propKey = function (x) {\r\n        return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n    };\r\n\r\n    __setFunctionName = function (f, name, prefix) {\r\n        if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n        return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n    };\r\n\r\n    __metadata = function (metadataKey, metadataValue) {\r\n        if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n    };\r\n\r\n    __awaiter = function (thisArg, _arguments, P, generator) {\r\n        function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n        return new (P || (P = Promise))(function (resolve, reject) {\r\n            function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n            function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n            function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n            step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n        });\r\n    };\r\n\r\n    __generator = function (thisArg, body) {\r\n        var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n        return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n        function verb(n) { return function (v) { return step([n, v]); }; }\r\n        function step(op) {\r\n            if (f) throw new TypeError(\"Generator is already executing.\");\r\n            while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n                if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n                if (y = 0, t) op = [op[0] & 2, t.value];\r\n                switch (op[0]) {\r\n                    case 0: case 1: t = op; break;\r\n                    case 4: _.label++; return { value: op[1], done: false };\r\n                    case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                    case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                    default:\r\n                        if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                        if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                        if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                        if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                        if (t[2]) _.ops.pop();\r\n                        _.trys.pop(); continue;\r\n                }\r\n                op = body.call(thisArg, _);\r\n            } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n            if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n        }\r\n    };\r\n\r\n    __exportStar = function(m, o) {\r\n        for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n    };\r\n\r\n    __createBinding = Object.create ? (function(o, m, k, k2) {\r\n        if (k2 === undefined) k2 = k;\r\n        var desc = Object.getOwnPropertyDescriptor(m, k);\r\n        if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n            desc = { enumerable: true, get: function() { return m[k]; } };\r\n        }\r\n        Object.defineProperty(o, k2, desc);\r\n    }) : (function(o, m, k, k2) {\r\n        if (k2 === undefined) k2 = k;\r\n        o[k2] = m[k];\r\n    });\r\n\r\n    __values = function (o) {\r\n        var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n        if (m) return m.call(o);\r\n        if (o && typeof o.length === \"number\") return {\r\n            next: function () {\r\n                if (o && i >= o.length) o = void 0;\r\n                return { value: o && o[i++], done: !o };\r\n            }\r\n        };\r\n        throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n    };\r\n\r\n    __read = function (o, n) {\r\n        var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n        if (!m) return o;\r\n        var i = m.call(o), r, ar = [], e;\r\n        try {\r\n            while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n        }\r\n        catch (error) { e = { error: error }; }\r\n        finally {\r\n            try {\r\n                if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n            }\r\n            finally { if (e) throw e.error; }\r\n        }\r\n        return ar;\r\n    };\r\n\r\n    /** @deprecated */\r\n    __spread = function () {\r\n        for (var ar = [], i = 0; i < arguments.length; i++)\r\n            ar = ar.concat(__read(arguments[i]));\r\n        return ar;\r\n    };\r\n\r\n    /** @deprecated */\r\n    __spreadArrays = function () {\r\n        for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n        for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n            for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n                r[k] = a[j];\r\n        return r;\r\n    };\r\n\r\n    __spreadArray = function (to, from, pack) {\r\n        if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n            if (ar || !(i in from)) {\r\n                if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n                ar[i] = from[i];\r\n            }\r\n        }\r\n        return to.concat(ar || Array.prototype.slice.call(from));\r\n    };\r\n\r\n    __await = function (v) {\r\n        return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n    };\r\n\r\n    __asyncGenerator = function (thisArg, _arguments, generator) {\r\n        if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n        var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n        return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n        function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n        function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n        function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n        function fulfill(value) { resume(\"next\", value); }\r\n        function reject(value) { resume(\"throw\", value); }\r\n        function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n    };\r\n\r\n    __asyncDelegator = function (o) {\r\n        var i, p;\r\n        return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n        function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n    };\r\n\r\n    __asyncValues = function (o) {\r\n        if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n        var m = o[Symbol.asyncIterator], i;\r\n        return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n        function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n        function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n    };\r\n\r\n    __makeTemplateObject = function (cooked, raw) {\r\n        if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n        return cooked;\r\n    };\r\n\r\n    var __setModuleDefault = Object.create ? (function(o, v) {\r\n        Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n    }) : function(o, v) {\r\n        o[\"default\"] = v;\r\n    };\r\n\r\n    __importStar = function (mod) {\r\n        if (mod && mod.__esModule) return mod;\r\n        var result = {};\r\n        if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n        __setModuleDefault(result, mod);\r\n        return result;\r\n    };\r\n\r\n    __importDefault = function (mod) {\r\n        return (mod && mod.__esModule) ? mod : { \"default\": mod };\r\n    };\r\n\r\n    __classPrivateFieldGet = function (receiver, state, kind, f) {\r\n        if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n        if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n        return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n    };\r\n\r\n    __classPrivateFieldSet = function (receiver, state, value, kind, f) {\r\n        if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n        if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n        if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n        return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n    };\r\n\r\n    __classPrivateFieldIn = function (state, receiver) {\r\n        if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n        return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n    };\r\n\r\n    __addDisposableResource = function (env, value, async) {\r\n        if (value !== null && value !== void 0) {\r\n            if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n            var dispose;\r\n            if (async) {\r\n                if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n                dispose = value[Symbol.asyncDispose];\r\n            }\r\n            if (dispose === void 0) {\r\n                if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n                dispose = value[Symbol.dispose];\r\n            }\r\n            if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n            env.stack.push({ value: value, dispose: dispose, async: async });\r\n        }\r\n        else if (async) {\r\n            env.stack.push({ async: true });\r\n        }\r\n        return value;\r\n    };\r\n\r\n    var _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n        var e = new Error(message);\r\n        return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n    };\r\n\r\n    __disposeResources = function (env) {\r\n        function fail(e) {\r\n            env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n            env.hasError = true;\r\n        }\r\n        function next() {\r\n            while (env.stack.length) {\r\n                var rec = env.stack.pop();\r\n                try {\r\n                    var result = rec.dispose && rec.dispose.call(rec.value);\r\n                    if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n                }\r\n                catch (e) {\r\n                    fail(e);\r\n                }\r\n            }\r\n            if (env.hasError) throw env.error;\r\n        }\r\n        return next();\r\n    };\r\n\r\n    exporter(\"__extends\", __extends);\r\n    exporter(\"__assign\", __assign);\r\n    exporter(\"__rest\", __rest);\r\n    exporter(\"__decorate\", __decorate);\r\n    exporter(\"__param\", __param);\r\n    exporter(\"__esDecorate\", __esDecorate);\r\n    exporter(\"__runInitializers\", __runInitializers);\r\n    exporter(\"__propKey\", __propKey);\r\n    exporter(\"__setFunctionName\", __setFunctionName);\r\n    exporter(\"__metadata\", __metadata);\r\n    exporter(\"__awaiter\", __awaiter);\r\n    exporter(\"__generator\", __generator);\r\n    exporter(\"__exportStar\", __exportStar);\r\n    exporter(\"__createBinding\", __createBinding);\r\n    exporter(\"__values\", __values);\r\n    exporter(\"__read\", __read);\r\n    exporter(\"__spread\", __spread);\r\n    exporter(\"__spreadArrays\", __spreadArrays);\r\n    exporter(\"__spreadArray\", __spreadArray);\r\n    exporter(\"__await\", __await);\r\n    exporter(\"__asyncGenerator\", __asyncGenerator);\r\n    exporter(\"__asyncDelegator\", __asyncDelegator);\r\n    exporter(\"__asyncValues\", __asyncValues);\r\n    exporter(\"__makeTemplateObject\", __makeTemplateObject);\r\n    exporter(\"__importStar\", __importStar);\r\n    exporter(\"__importDefault\", __importDefault);\r\n    exporter(\"__classPrivateFieldGet\", __classPrivateFieldGet);\r\n    exporter(\"__classPrivateFieldSet\", __classPrivateFieldSet);\r\n    exporter(\"__classPrivateFieldIn\", __classPrivateFieldIn);\r\n    exporter(\"__addDisposableResource\", __addDisposableResource);\r\n    exporter(\"__disposeResources\", __disposeResources);\r\n});\r\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ErrorKind = void 0;\nvar ErrorKind;\n(function (ErrorKind) {\n    /** Argument is unclosed (e.g. `{0`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_CLOSING_BRACE\"] = 1] = \"EXPECT_ARGUMENT_CLOSING_BRACE\";\n    /** Argument is empty (e.g. `{}`). */\n    ErrorKind[ErrorKind[\"EMPTY_ARGUMENT\"] = 2] = \"EMPTY_ARGUMENT\";\n    /** Argument is malformed (e.g. `{foo!}``) */\n    ErrorKind[ErrorKind[\"MALFORMED_ARGUMENT\"] = 3] = \"MALFORMED_ARGUMENT\";\n    /** Expect an argument type (e.g. `{foo,}`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_TYPE\"] = 4] = \"EXPECT_ARGUMENT_TYPE\";\n    /** Unsupported argument type (e.g. `{foo,foo}`) */\n    ErrorKind[ErrorKind[\"INVALID_ARGUMENT_TYPE\"] = 5] = \"INVALID_ARGUMENT_TYPE\";\n    /** Expect an argument style (e.g. `{foo, number, }`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_STYLE\"] = 6] = \"EXPECT_ARGUMENT_STYLE\";\n    /** The number skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_NUMBER_SKELETON\"] = 7] = \"INVALID_NUMBER_SKELETON\";\n    /** The date time skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_DATE_TIME_SKELETON\"] = 8] = \"INVALID_DATE_TIME_SKELETON\";\n    /** Exepct a number skeleton following the `::` (e.g. `{foo, number, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_NUMBER_SKELETON\"] = 9] = \"EXPECT_NUMBER_SKELETON\";\n    /** Exepct a date time skeleton following the `::` (e.g. `{foo, date, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_DATE_TIME_SKELETON\"] = 10] = \"EXPECT_DATE_TIME_SKELETON\";\n    /** Unmatched apostrophes in the argument style (e.g. `{foo, number, 'test`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\"] = 11] = \"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\";\n    /** Missing select argument options (e.g. `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_OPTIONS\"] = 12] = \"EXPECT_SELECT_ARGUMENT_OPTIONS\";\n    /** Expecting an offset value in `plural` or `selectordinal` argument (e.g `{foo, plural, offset}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 13] = \"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Offset value in `plural` or `selectordinal` is invalid (e.g. `{foo, plural, offset: x}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 14] = \"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Expecting a selector in `select` argument (e.g `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR\"] = 15] = \"EXPECT_SELECT_ARGUMENT_SELECTOR\";\n    /** Expecting a selector in `plural` or `selectordinal` argument (e.g `{foo, plural}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR\"] = 16] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR\";\n    /** Expecting a message fragment after the `select` selector (e.g. `{foo, select, apple}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\"] = 17] = \"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\";\n    /**\n     * Expecting a message fragment after the `plural` or `selectordinal` selector\n     * (e.g. `{foo, plural, one}`)\n     */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\"] = 18] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\";\n    /** Selector in `plural` or `selectordinal` is malformed (e.g. `{foo, plural, =x {#}}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_SELECTOR\"] = 19] = \"INVALID_PLURAL_ARGUMENT_SELECTOR\";\n    /**\n     * Duplicate selectors in `plural` or `selectordinal` argument.\n     * (e.g. {foo, plural, one {#} one {#}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\"] = 20] = \"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\";\n    /** Duplicate selectors in `select` argument.\n     * (e.g. {foo, select, apple {apple} apple {apple}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_SELECT_ARGUMENT_SELECTOR\"] = 21] = \"DUPLICATE_SELECT_ARGUMENT_SELECTOR\";\n    /** Plural or select argument option must have `other` clause. */\n    ErrorKind[ErrorKind[\"MISSING_OTHER_CLAUSE\"] = 22] = \"MISSING_OTHER_CLAUSE\";\n    /** The tag is malformed. (e.g. `<bold!>foo</bold!>) */\n    ErrorKind[ErrorKind[\"INVALID_TAG\"] = 23] = \"INVALID_TAG\";\n    /** The tag name is invalid. (e.g. `<123>foo</123>`) */\n    ErrorKind[ErrorKind[\"INVALID_TAG_NAME\"] = 25] = \"INVALID_TAG_NAME\";\n    /** The closing tag does not match the opening tag. (e.g. `<bold>foo</italic>`) */\n    ErrorKind[ErrorKind[\"UNMATCHED_CLOSING_TAG\"] = 26] = \"UNMATCHED_CLOSING_TAG\";\n    /** The opening tag has unmatched closing tag. (e.g. `<bold>foo`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_TAG\"] = 27] = \"UNCLOSED_TAG\";\n})(ErrorKind || (exports.ErrorKind = ErrorKind = {}));\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.createNumberElement = exports.createLiteralElement = exports.isDateTimeSkeleton = exports.isNumberSkeleton = exports.isTagElement = exports.isPoundElement = exports.isPluralElement = exports.isSelectElement = exports.isTimeElement = exports.isDateElement = exports.isNumberElement = exports.isArgumentElement = exports.isLiteralElement = exports.SKELETON_TYPE = exports.TYPE = void 0;\nvar TYPE;\n(function (TYPE) {\n    /**\n     * Raw text\n     */\n    TYPE[TYPE[\"literal\"] = 0] = \"literal\";\n    /**\n     * Variable w/o any format, e.g `var` in `this is a {var}`\n     */\n    TYPE[TYPE[\"argument\"] = 1] = \"argument\";\n    /**\n     * Variable w/ number format\n     */\n    TYPE[TYPE[\"number\"] = 2] = \"number\";\n    /**\n     * Variable w/ date format\n     */\n    TYPE[TYPE[\"date\"] = 3] = \"date\";\n    /**\n     * Variable w/ time format\n     */\n    TYPE[TYPE[\"time\"] = 4] = \"time\";\n    /**\n     * Variable w/ select format\n     */\n    TYPE[TYPE[\"select\"] = 5] = \"select\";\n    /**\n     * Variable w/ plural format\n     */\n    TYPE[TYPE[\"plural\"] = 6] = \"plural\";\n    /**\n     * Only possible within plural argument.\n     * This is the `#` symbol that will be substituted with the count.\n     */\n    TYPE[TYPE[\"pound\"] = 7] = \"pound\";\n    /**\n     * XML-like tag\n     */\n    TYPE[TYPE[\"tag\"] = 8] = \"tag\";\n})(TYPE || (exports.TYPE = TYPE = {}));\nvar SKELETON_TYPE;\n(function (SKELETON_TYPE) {\n    SKELETON_TYPE[SKELETON_TYPE[\"number\"] = 0] = \"number\";\n    SKELETON_TYPE[SKELETON_TYPE[\"dateTime\"] = 1] = \"dateTime\";\n})(SKELETON_TYPE || (exports.SKELETON_TYPE = SKELETON_TYPE = {}));\n/**\n * Type Guards\n */\nfunction isLiteralElement(el) {\n    return el.type === TYPE.literal;\n}\nexports.isLiteralElement = isLiteralElement;\nfunction isArgumentElement(el) {\n    return el.type === TYPE.argument;\n}\nexports.isArgumentElement = isArgumentElement;\nfunction isNumberElement(el) {\n    return el.type === TYPE.number;\n}\nexports.isNumberElement = isNumberElement;\nfunction isDateElement(el) {\n    return el.type === TYPE.date;\n}\nexports.isDateElement = isDateElement;\nfunction isTimeElement(el) {\n    return el.type === TYPE.time;\n}\nexports.isTimeElement = isTimeElement;\nfunction isSelectElement(el) {\n    return el.type === TYPE.select;\n}\nexports.isSelectElement = isSelectElement;\nfunction isPluralElement(el) {\n    return el.type === TYPE.plural;\n}\nexports.isPluralElement = isPluralElement;\nfunction isPoundElement(el) {\n    return el.type === TYPE.pound;\n}\nexports.isPoundElement = isPoundElement;\nfunction isTagElement(el) {\n    return el.type === TYPE.tag;\n}\nexports.isTagElement = isTagElement;\nfunction isNumberSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.number);\n}\nexports.isNumberSkeleton = isNumberSkeleton;\nfunction isDateTimeSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.dateTime);\n}\nexports.isDateTimeSkeleton = isDateTimeSkeleton;\nfunction createLiteralElement(value) {\n    return {\n        type: TYPE.literal,\n        value: value,\n    };\n}\nexports.createLiteralElement = createLiteralElement;\nfunction createNumberElement(value, style) {\n    return {\n        type: TYPE.number,\n        value: value,\n        style: style,\n    };\n}\nexports.createNumberElement = createNumberElement;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.WHITE_SPACE_REGEX = exports.SPACE_SEPARATOR_REGEX = void 0;\n// @generated from regex-gen.ts\nexports.SPACE_SEPARATOR_REGEX = /[ \\xA0\\u1680\\u2000-\\u200A\\u202F\\u205F\\u3000]/;\nexports.WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseDateTimeSkeleton = void 0;\n/**\n * https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * Credit: https://github.com/caridy/intl-datetimeformat-pattern/blob/master/index.js\n * with some tweaks\n */\nvar DATE_TIME_REGEX = /(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;\n/**\n * Parse Date time skeleton into Intl.DateTimeFormatOptions\n * Ref: https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * @public\n * @param skeleton skeleton string\n */\nfunction parseDateTimeSkeleton(skeleton) {\n    var result = {};\n    skeleton.replace(DATE_TIME_REGEX, function (match) {\n        var len = match.length;\n        switch (match[0]) {\n            // Era\n            case 'G':\n                result.era = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            // Year\n            case 'y':\n                result.year = len === 2 ? '2-digit' : 'numeric';\n                break;\n            case 'Y':\n            case 'u':\n            case 'U':\n            case 'r':\n                throw new RangeError('`Y/u/U/r` (year) patterns are not supported, use `y` instead');\n            // Quarter\n            case 'q':\n            case 'Q':\n                throw new RangeError('`q/Q` (quarter) patterns are not supported');\n            // Month\n            case 'M':\n            case 'L':\n                result.month = ['numeric', '2-digit', 'short', 'long', 'narrow'][len - 1];\n                break;\n            // Week\n            case 'w':\n            case 'W':\n                throw new RangeError('`w/W` (week) patterns are not supported');\n            case 'd':\n                result.day = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'D':\n            case 'F':\n            case 'g':\n                throw new RangeError('`D/F/g` (day) patterns are not supported, use `d` instead');\n            // Weekday\n            case 'E':\n                result.weekday = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            case 'e':\n                if (len < 4) {\n                    throw new RangeError('`e..eee` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            case 'c':\n                if (len < 4) {\n                    throw new RangeError('`c..ccc` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            // Period\n            case 'a': // AM, PM\n                result.hour12 = true;\n                break;\n            case 'b': // am, pm, noon, midnight\n            case 'B': // flexible day periods\n                throw new RangeError('`b/B` (period) patterns are not supported, use `a` instead');\n            // Hour\n            case 'h':\n                result.hourCycle = 'h12';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'H':\n                result.hourCycle = 'h23';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'K':\n                result.hourCycle = 'h11';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'k':\n                result.hourCycle = 'h24';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'j':\n            case 'J':\n            case 'C':\n                throw new RangeError('`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead');\n            // Minute\n            case 'm':\n                result.minute = ['numeric', '2-digit'][len - 1];\n                break;\n            // Second\n            case 's':\n                result.second = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'S':\n            case 'A':\n                throw new RangeError('`S/A` (second) patterns are not supported, use `s` instead');\n            // Zone\n            case 'z': // 1..3, 4: specific non-location format\n                result.timeZoneName = len < 4 ? 'short' : 'long';\n                break;\n            case 'Z': // 1..3, 4, 5: The ISO8601 varios formats\n            case 'O': // 1, 4: miliseconds in day short, long\n            case 'v': // 1, 4: generic non-location format\n            case 'V': // 1, 2, 3, 4: time zone ID or city\n            case 'X': // 1, 2, 3, 4: The ISO8601 varios formats\n            case 'x': // 1, 2, 3, 4: The ISO8601 varios formats\n                throw new RangeError('`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead');\n        }\n        return '';\n    });\n    return result;\n}\nexports.parseDateTimeSkeleton = parseDateTimeSkeleton;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.WHITE_SPACE_REGEX = void 0;\n// @generated from regex-gen.ts\nexports.WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/i;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseNumberSkeleton = exports.parseNumberSkeletonFromString = void 0;\nvar tslib_1 = require(\"tslib\");\nvar regex_generated_1 = require(\"./regex.generated\");\nfunction parseNumberSkeletonFromString(skeleton) {\n    if (skeleton.length === 0) {\n        throw new Error('Number skeleton cannot be empty');\n    }\n    // Parse the skeleton\n    var stringTokens = skeleton\n        .split(regex_generated_1.WHITE_SPACE_REGEX)\n        .filter(function (x) { return x.length > 0; });\n    var tokens = [];\n    for (var _i = 0, stringTokens_1 = stringTokens; _i < stringTokens_1.length; _i++) {\n        var stringToken = stringTokens_1[_i];\n        var stemAndOptions = stringToken.split('/');\n        if (stemAndOptions.length === 0) {\n            throw new Error('Invalid number skeleton');\n        }\n        var stem = stemAndOptions[0], options = stemAndOptions.slice(1);\n        for (var _a = 0, options_1 = options; _a < options_1.length; _a++) {\n            var option = options_1[_a];\n            if (option.length === 0) {\n                throw new Error('Invalid number skeleton');\n            }\n        }\n        tokens.push({ stem: stem, options: options });\n    }\n    return tokens;\n}\nexports.parseNumberSkeletonFromString = parseNumberSkeletonFromString;\nfunction icuUnitToEcma(unit) {\n    return unit.replace(/^(.*?)-/, '');\n}\nvar FRACTION_PRECISION_REGEX = /^\\.(?:(0+)(\\*)?|(#+)|(0+)(#+))$/g;\nvar SIGNIFICANT_PRECISION_REGEX = /^(@+)?(\\+|#+)?[rs]?$/g;\nvar INTEGER_WIDTH_REGEX = /(\\*)(0+)|(#+)(0+)|(0+)/g;\nvar CONCISE_INTEGER_WIDTH_REGEX = /^(0+)$/;\nfunction parseSignificantPrecision(str) {\n    var result = {};\n    if (str[str.length - 1] === 'r') {\n        result.roundingPriority = 'morePrecision';\n    }\n    else if (str[str.length - 1] === 's') {\n        result.roundingPriority = 'lessPrecision';\n    }\n    str.replace(SIGNIFICANT_PRECISION_REGEX, function (_, g1, g2) {\n        // @@@ case\n        if (typeof g2 !== 'string') {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits = g1.length;\n        }\n        // @@@+ case\n        else if (g2 === '+') {\n            result.minimumSignificantDigits = g1.length;\n        }\n        // .### case\n        else if (g1[0] === '#') {\n            result.maximumSignificantDigits = g1.length;\n        }\n        // .@@## or .@@@ case\n        else {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits =\n                g1.length + (typeof g2 === 'string' ? g2.length : 0);\n        }\n        return '';\n    });\n    return result;\n}\nfunction parseSign(str) {\n    switch (str) {\n        case 'sign-auto':\n            return {\n                signDisplay: 'auto',\n            };\n        case 'sign-accounting':\n        case '()':\n            return {\n                currencySign: 'accounting',\n            };\n        case 'sign-always':\n        case '+!':\n            return {\n                signDisplay: 'always',\n            };\n        case 'sign-accounting-always':\n        case '()!':\n            return {\n                signDisplay: 'always',\n                currencySign: 'accounting',\n            };\n        case 'sign-except-zero':\n        case '+?':\n            return {\n                signDisplay: 'exceptZero',\n            };\n        case 'sign-accounting-except-zero':\n        case '()?':\n            return {\n                signDisplay: 'exceptZero',\n                currencySign: 'accounting',\n            };\n        case 'sign-never':\n        case '+_':\n            return {\n                signDisplay: 'never',\n            };\n    }\n}\nfunction parseConciseScientificAndEngineeringStem(stem) {\n    // Engineering\n    var result;\n    if (stem[0] === 'E' && stem[1] === 'E') {\n        result = {\n            notation: 'engineering',\n        };\n        stem = stem.slice(2);\n    }\n    else if (stem[0] === 'E') {\n        result = {\n            notation: 'scientific',\n        };\n        stem = stem.slice(1);\n    }\n    if (result) {\n        var signDisplay = stem.slice(0, 2);\n        if (signDisplay === '+!') {\n            result.signDisplay = 'always';\n            stem = stem.slice(2);\n        }\n        else if (signDisplay === '+?') {\n            result.signDisplay = 'exceptZero';\n            stem = stem.slice(2);\n        }\n        if (!CONCISE_INTEGER_WIDTH_REGEX.test(stem)) {\n            throw new Error('Malformed concise eng/scientific notation');\n        }\n        result.minimumIntegerDigits = stem.length;\n    }\n    return result;\n}\nfunction parseNotationOptions(opt) {\n    var result = {};\n    var signOpts = parseSign(opt);\n    if (signOpts) {\n        return signOpts;\n    }\n    return result;\n}\n/**\n * https://github.com/unicode-org/icu/blob/master/docs/userguide/format_parse/numbers/skeletons.md#skeleton-stems-and-options\n */\nfunction parseNumberSkeleton(tokens) {\n    var result = {};\n    for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {\n        var token = tokens_1[_i];\n        switch (token.stem) {\n            case 'percent':\n            case '%':\n                result.style = 'percent';\n                continue;\n            case '%x100':\n                result.style = 'percent';\n                result.scale = 100;\n                continue;\n            case 'currency':\n                result.style = 'currency';\n                result.currency = token.options[0];\n                continue;\n            case 'group-off':\n            case ',_':\n                result.useGrouping = false;\n                continue;\n            case 'precision-integer':\n            case '.':\n                result.maximumFractionDigits = 0;\n                continue;\n            case 'measure-unit':\n            case 'unit':\n                result.style = 'unit';\n                result.unit = icuUnitToEcma(token.options[0]);\n                continue;\n            case 'compact-short':\n            case 'K':\n                result.notation = 'compact';\n                result.compactDisplay = 'short';\n                continue;\n            case 'compact-long':\n            case 'KK':\n                result.notation = 'compact';\n                result.compactDisplay = 'long';\n                continue;\n            case 'scientific':\n                result = tslib_1.__assign(tslib_1.__assign(tslib_1.__assign({}, result), { notation: 'scientific' }), token.options.reduce(function (all, opt) { return (tslib_1.__assign(tslib_1.__assign({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'engineering':\n                result = tslib_1.__assign(tslib_1.__assign(tslib_1.__assign({}, result), { notation: 'engineering' }), token.options.reduce(function (all, opt) { return (tslib_1.__assign(tslib_1.__assign({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'notation-simple':\n                result.notation = 'standard';\n                continue;\n            // https://github.com/unicode-org/icu/blob/master/icu4c/source/i18n/unicode/unumberformatter.h\n            case 'unit-width-narrow':\n                result.currencyDisplay = 'narrowSymbol';\n                result.unitDisplay = 'narrow';\n                continue;\n            case 'unit-width-short':\n                result.currencyDisplay = 'code';\n                result.unitDisplay = 'short';\n                continue;\n            case 'unit-width-full-name':\n                result.currencyDisplay = 'name';\n                result.unitDisplay = 'long';\n                continue;\n            case 'unit-width-iso-code':\n                result.currencyDisplay = 'symbol';\n                continue;\n            case 'scale':\n                result.scale = parseFloat(token.options[0]);\n                continue;\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n            case 'integer-width':\n                if (token.options.length > 1) {\n                    throw new RangeError('integer-width stems only accept a single optional option');\n                }\n                token.options[0].replace(INTEGER_WIDTH_REGEX, function (_, g1, g2, g3, g4, g5) {\n                    if (g1) {\n                        result.minimumIntegerDigits = g2.length;\n                    }\n                    else if (g3 && g4) {\n                        throw new Error('We currently do not support maximum integer digits');\n                    }\n                    else if (g5) {\n                        throw new Error('We currently do not support exact integer digits');\n                    }\n                    return '';\n                });\n                continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n        if (CONCISE_INTEGER_WIDTH_REGEX.test(token.stem)) {\n            result.minimumIntegerDigits = token.stem.length;\n            continue;\n        }\n        if (FRACTION_PRECISION_REGEX.test(token.stem)) {\n            // Precision\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#fraction-precision\n            // precision-integer case\n            if (token.options.length > 1) {\n                throw new RangeError('Fraction-precision stems only accept a single optional option');\n            }\n            token.stem.replace(FRACTION_PRECISION_REGEX, function (_, g1, g2, g3, g4, g5) {\n                // .000* case (before ICU67 it was .000+)\n                if (g2 === '*') {\n                    result.minimumFractionDigits = g1.length;\n                }\n                // .### case\n                else if (g3 && g3[0] === '#') {\n                    result.maximumFractionDigits = g3.length;\n                }\n                // .00## case\n                else if (g4 && g5) {\n                    result.minimumFractionDigits = g4.length;\n                    result.maximumFractionDigits = g4.length + g5.length;\n                }\n                else {\n                    result.minimumFractionDigits = g1.length;\n                    result.maximumFractionDigits = g1.length;\n                }\n                return '';\n            });\n            var opt = token.options[0];\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#trailing-zero-display\n            if (opt === 'w') {\n                result = tslib_1.__assign(tslib_1.__assign({}, result), { trailingZeroDisplay: 'stripIfInteger' });\n            }\n            else if (opt) {\n                result = tslib_1.__assign(tslib_1.__assign({}, result), parseSignificantPrecision(opt));\n            }\n            continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#significant-digits-precision\n        if (SIGNIFICANT_PRECISION_REGEX.test(token.stem)) {\n            result = tslib_1.__assign(tslib_1.__assign({}, result), parseSignificantPrecision(token.stem));\n            continue;\n        }\n        var signOpts = parseSign(token.stem);\n        if (signOpts) {\n            result = tslib_1.__assign(tslib_1.__assign({}, result), signOpts);\n        }\n        var conciseScientificAndEngineeringOpts = parseConciseScientificAndEngineeringStem(token.stem);\n        if (conciseScientificAndEngineeringOpts) {\n            result = tslib_1.__assign(tslib_1.__assign({}, result), conciseScientificAndEngineeringOpts);\n        }\n    }\n    return result;\n}\nexports.parseNumberSkeleton = parseNumberSkeleton;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar tslib_1 = require(\"tslib\");\ntslib_1.__exportStar(require(\"./date-time\"), exports);\ntslib_1.__exportStar(require(\"./number\"), exports);\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.timeData = void 0;\n// @generated from time-data-gen.ts\n// prettier-ignore  \nexports.timeData = {\n    \"001\": [\n        \"H\",\n        \"h\"\n    ],\n    \"AC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"AF\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"AG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AL\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"AT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AX\": [\n        \"H\"\n    ],\n    \"AZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BD\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"BE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BG\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"BI\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BJ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BN\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"BO\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"BQ\": [\n        \"H\"\n    ],\n    \"BR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BT\": [\n        \"h\",\n        \"H\"\n    ],\n    \"BW\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"BY\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BZ\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CA\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"CC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CD\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"CF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CH\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"CI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CL\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CN\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"CO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CP\": [\n        \"H\"\n    ],\n    \"CR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CU\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CV\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CY\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CZ\": [\n        \"H\"\n    ],\n    \"DE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"DG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"DJ\": [\n        \"h\",\n        \"H\"\n    ],\n    \"DK\": [\n        \"H\"\n    ],\n    \"DM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"DO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"DZ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EC\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"EE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"EG\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ER\": [\n        \"h\",\n        \"H\"\n    ],\n    \"ES\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"ET\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"FI\": [\n        \"H\"\n    ],\n    \"FJ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"FM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"FR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GA\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GB\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GD\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GE\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"GF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GH\": [\n        \"h\",\n        \"H\"\n    ],\n    \"GI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"GM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GN\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GP\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GQ\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"GR\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GT\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"GU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"HK\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"HN\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"HR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"HU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"IC\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ID\": [\n        \"H\"\n    ],\n    \"IE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"IM\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IN\": [\n        \"h\",\n        \"H\"\n    ],\n    \"IO\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IQ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"IR\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"IS\": [\n        \"H\"\n    ],\n    \"IT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"JE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"JM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"JO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"JP\": [\n        \"H\",\n        \"K\",\n        \"h\"\n    ],\n    \"KE\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"KG\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KH\": [\n        \"hB\",\n        \"h\",\n        \"H\",\n        \"hb\"\n    ],\n    \"KI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KM\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KN\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KP\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KW\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"KY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"LA\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LB\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"LC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LI\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LK\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"LR\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"LT\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"LU\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"LV\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"LY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ME\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"MF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MG\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MH\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ML\": [\n        \"H\"\n    ],\n    \"MM\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"MN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MP\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MQ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MR\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MS\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MT\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MV\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MW\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MX\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MY\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"MZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NE\": [\n        \"H\"\n    ],\n    \"NF\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NI\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"NP\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"NR\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NU\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"OM\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PE\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"PF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"PG\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PK\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"PL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"PM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"PR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PS\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PW\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PY\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"QA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"RE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RS\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"RU\": [\n        \"H\"\n    ],\n    \"RW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"SA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SC\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SD\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SE\": [\n        \"H\"\n    ],\n    \"SG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SH\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SJ\": [\n        \"H\"\n    ],\n    \"SK\": [\n        \"H\"\n    ],\n    \"SL\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SN\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"SR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ST\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SV\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"SX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"TC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TD\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"TG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TH\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TJ\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TL\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"TM\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TN\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"TO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"TR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TT\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TW\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"TZ\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"UG\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"US\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"UY\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"UZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"VA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"VC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"VG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VN\": [\n        \"H\",\n        \"h\"\n    ],\n    \"VU\": [\n        \"h\",\n        \"H\"\n    ],\n    \"WF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"WS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"XK\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"YE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"YT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ZA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ZM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ZW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"af-ZA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ar-001\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ca-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"en-001\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"es-BO\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-BR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-EC\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-GQ\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-PE\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"fr-CA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gl-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gu-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"hi-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"it-CH\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"it-IT\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"kn-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"ml-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"mr-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"pa-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"ta-IN\": [\n        \"hB\",\n        \"h\",\n        \"hb\",\n        \"H\"\n    ],\n    \"te-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"zu-ZA\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ]\n};\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getBestPattern = void 0;\nvar time_data_generated_1 = require(\"./time-data.generated\");\n/**\n * Returns the best matching date time pattern if a date time skeleton\n * pattern is provided with a locale. Follows the Unicode specification:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#table-mapping-requested-time-skeletons-to-patterns\n * @param skeleton date time skeleton pattern that possibly includes j, J or C\n * @param locale\n */\nfunction getBestPattern(skeleton, locale) {\n    var skeletonCopy = '';\n    for (var patternPos = 0; patternPos < skeleton.length; patternPos++) {\n        var patternChar = skeleton.charAt(patternPos);\n        if (patternChar === 'j') {\n            var extraLength = 0;\n            while (patternPos + 1 < skeleton.length &&\n                skeleton.charAt(patternPos + 1) === patternChar) {\n                extraLength++;\n                patternPos++;\n            }\n            var hourLen = 1 + (extraLength & 1);\n            var dayPeriodLen = extraLength < 2 ? 1 : 3 + (extraLength >> 1);\n            var dayPeriodChar = 'a';\n            var hourChar = getDefaultHourSymbolFromLocale(locale);\n            if (hourChar == 'H' || hourChar == 'k') {\n                dayPeriodLen = 0;\n            }\n            while (dayPeriodLen-- > 0) {\n                skeletonCopy += dayPeriodChar;\n            }\n            while (hourLen-- > 0) {\n                skeletonCopy = hourChar + skeletonCopy;\n            }\n        }\n        else if (patternChar === 'J') {\n            skeletonCopy += 'H';\n        }\n        else {\n            skeletonCopy += patternChar;\n        }\n    }\n    return skeletonCopy;\n}\nexports.getBestPattern = getBestPattern;\n/**\n * Maps the [hour cycle type](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/hourCycle)\n * of the given `locale` to the corresponding time pattern.\n * @param locale\n */\nfunction getDefaultHourSymbolFromLocale(locale) {\n    var hourCycle = locale.hourCycle;\n    if (hourCycle === undefined &&\n        // @ts-ignore hourCycle(s) is not identified yet\n        locale.hourCycles &&\n        // @ts-ignore\n        locale.hourCycles.length) {\n        // @ts-ignore\n        hourCycle = locale.hourCycles[0];\n    }\n    if (hourCycle) {\n        switch (hourCycle) {\n            case 'h24':\n                return 'k';\n            case 'h23':\n                return 'H';\n            case 'h12':\n                return 'h';\n            case 'h11':\n                return 'K';\n            default:\n                throw new Error('Invalid hourCycle');\n        }\n    }\n    // TODO: Once hourCycle is fully supported remove the following with data generation\n    var languageTag = locale.language;\n    var regionTag;\n    if (languageTag !== 'root') {\n        regionTag = locale.maximize().region;\n    }\n    var hourCycles = time_data_generated_1.timeData[regionTag || ''] ||\n        time_data_generated_1.timeData[languageTag || ''] ||\n        time_data_generated_1.timeData[\"\".concat(languageTag, \"-001\")] ||\n        time_data_generated_1.timeData['001'];\n    return hourCycles[0];\n}\n", "\"use strict\";\nvar _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Parser = void 0;\nvar tslib_1 = require(\"tslib\");\nvar error_1 = require(\"./error\");\nvar types_1 = require(\"./types\");\nvar regex_generated_1 = require(\"./regex.generated\");\nvar icu_skeleton_parser_1 = require(\"@formatjs/icu-skeleton-parser\");\nvar date_time_pattern_generator_1 = require(\"./date-time-pattern-generator\");\nvar SPACE_SEPARATOR_START_REGEX = new RegExp(\"^\".concat(regex_generated_1.SPACE_SEPARATOR_REGEX.source, \"*\"));\nvar SPACE_SEPARATOR_END_REGEX = new RegExp(\"\".concat(regex_generated_1.SPACE_SEPARATOR_REGEX.source, \"*$\"));\nfunction createLocation(start, end) {\n    return { start: start, end: end };\n}\n// #region Ponyfills\n// Consolidate these variables up top for easier toggling during debugging\nvar hasNativeStartsWith = !!String.prototype.startsWith && '_a'.startsWith('a', 1);\nvar hasNativeFromCodePoint = !!String.fromCodePoint;\nvar hasNativeFromEntries = !!Object.fromEntries;\nvar hasNativeCodePointAt = !!String.prototype.codePointAt;\nvar hasTrimStart = !!String.prototype.trimStart;\nvar hasTrimEnd = !!String.prototype.trimEnd;\nvar hasNativeIsSafeInteger = !!Number.isSafeInteger;\nvar isSafeInteger = hasNativeIsSafeInteger\n    ? Number.isSafeInteger\n    : function (n) {\n        return (typeof n === 'number' &&\n            isFinite(n) &&\n            Math.floor(n) === n &&\n            Math.abs(n) <= 0x1fffffffffffff);\n    };\n// IE11 does not support y and u.\nvar REGEX_SUPPORTS_U_AND_Y = true;\ntry {\n    var re = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    /**\n     * legacy Edge or Xbox One browser\n     * Unicode flag support: supported\n     * Pattern_Syntax support: not supported\n     * See https://github.com/formatjs/formatjs/issues/2822\n     */\n    REGEX_SUPPORTS_U_AND_Y = ((_a = re.exec('a')) === null || _a === void 0 ? void 0 : _a[0]) === 'a';\n}\ncatch (_) {\n    REGEX_SUPPORTS_U_AND_Y = false;\n}\nvar startsWith = hasNativeStartsWith\n    ? // Native\n        function startsWith(s, search, position) {\n            return s.startsWith(search, position);\n        }\n    : // For IE11\n        function startsWith(s, search, position) {\n            return s.slice(position, position + search.length) === search;\n        };\nvar fromCodePoint = hasNativeFromCodePoint\n    ? String.fromCodePoint\n    : // IE11\n        function fromCodePoint() {\n            var codePoints = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                codePoints[_i] = arguments[_i];\n            }\n            var elements = '';\n            var length = codePoints.length;\n            var i = 0;\n            var code;\n            while (length > i) {\n                code = codePoints[i++];\n                if (code > 0x10ffff)\n                    throw RangeError(code + ' is not a valid code point');\n                elements +=\n                    code < 0x10000\n                        ? String.fromCharCode(code)\n                        : String.fromCharCode(((code -= 0x10000) >> 10) + 0xd800, (code % 0x400) + 0xdc00);\n            }\n            return elements;\n        };\nvar fromEntries = \n// native\nhasNativeFromEntries\n    ? Object.fromEntries\n    : // Ponyfill\n        function fromEntries(entries) {\n            var obj = {};\n            for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                var _a = entries_1[_i], k = _a[0], v = _a[1];\n                obj[k] = v;\n            }\n            return obj;\n        };\nvar codePointAt = hasNativeCodePointAt\n    ? // Native\n        function codePointAt(s, index) {\n            return s.codePointAt(index);\n        }\n    : // IE 11\n        function codePointAt(s, index) {\n            var size = s.length;\n            if (index < 0 || index >= size) {\n                return undefined;\n            }\n            var first = s.charCodeAt(index);\n            var second;\n            return first < 0xd800 ||\n                first > 0xdbff ||\n                index + 1 === size ||\n                (second = s.charCodeAt(index + 1)) < 0xdc00 ||\n                second > 0xdfff\n                ? first\n                : ((first - 0xd800) << 10) + (second - 0xdc00) + 0x10000;\n        };\nvar trimStart = hasTrimStart\n    ? // Native\n        function trimStart(s) {\n            return s.trimStart();\n        }\n    : // Ponyfill\n        function trimStart(s) {\n            return s.replace(SPACE_SEPARATOR_START_REGEX, '');\n        };\nvar trimEnd = hasTrimEnd\n    ? // Native\n        function trimEnd(s) {\n            return s.trimEnd();\n        }\n    : // Ponyfill\n        function trimEnd(s) {\n            return s.replace(SPACE_SEPARATOR_END_REGEX, '');\n        };\n// Prevent minifier to translate new RegExp to literal form that might cause syntax error on IE11.\nfunction RE(s, flag) {\n    return new RegExp(s, flag);\n}\n// #endregion\nvar matchIdentifierAtIndex;\nif (REGEX_SUPPORTS_U_AND_Y) {\n    // Native\n    var IDENTIFIER_PREFIX_RE_1 = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var _a;\n        IDENTIFIER_PREFIX_RE_1.lastIndex = index;\n        var match = IDENTIFIER_PREFIX_RE_1.exec(s);\n        return (_a = match[1]) !== null && _a !== void 0 ? _a : '';\n    };\n}\nelse {\n    // IE11\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var match = [];\n        while (true) {\n            var c = codePointAt(s, index);\n            if (c === undefined || _isWhiteSpace(c) || _isPatternSyntax(c)) {\n                break;\n            }\n            match.push(c);\n            index += c >= 0x10000 ? 2 : 1;\n        }\n        return fromCodePoint.apply(void 0, match);\n    };\n}\nvar Parser = /** @class */ (function () {\n    function Parser(message, options) {\n        if (options === void 0) { options = {}; }\n        this.message = message;\n        this.position = { offset: 0, line: 1, column: 1 };\n        this.ignoreTag = !!options.ignoreTag;\n        this.locale = options.locale;\n        this.requiresOtherClause = !!options.requiresOtherClause;\n        this.shouldParseSkeletons = !!options.shouldParseSkeletons;\n    }\n    Parser.prototype.parse = function () {\n        if (this.offset() !== 0) {\n            throw Error('parser can only be used once');\n        }\n        return this.parseMessage(0, '', false);\n    };\n    Parser.prototype.parseMessage = function (nestingLevel, parentArgType, expectingCloseTag) {\n        var elements = [];\n        while (!this.isEOF()) {\n            var char = this.char();\n            if (char === 123 /* `{` */) {\n                var result = this.parseArgument(nestingLevel, expectingCloseTag);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else if (char === 125 /* `}` */ && nestingLevel > 0) {\n                break;\n            }\n            else if (char === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) {\n                var position = this.clonePosition();\n                this.bump();\n                elements.push({\n                    type: types_1.TYPE.pound,\n                    location: createLocation(position, this.clonePosition()),\n                });\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                this.peek() === 47 // char code for '/'\n            ) {\n                if (expectingCloseTag) {\n                    break;\n                }\n                else {\n                    return this.error(error_1.ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(this.clonePosition(), this.clonePosition()));\n                }\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                _isAlpha(this.peek() || 0)) {\n                var result = this.parseTag(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else {\n                var result = this.parseLiteral(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n        }\n        return { val: elements, err: null };\n    };\n    /**\n     * A tag name must start with an ASCII lower/upper case letter. The grammar is based on the\n     * [custom element name][] except that a dash is NOT always mandatory and uppercase letters\n     * are accepted:\n     *\n     * ```\n     * tag ::= \"<\" tagName (whitespace)* \"/>\" | \"<\" tagName (whitespace)* \">\" message \"</\" tagName (whitespace)* \">\"\n     * tagName ::= [a-z] (PENChar)*\n     * PENChar ::=\n     *     \"-\" | \".\" | [0-9] | \"_\" | [a-z] | [A-Z] | #xB7 | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x37D] |\n     *     [#x37F-#x1FFF] | [#x200C-#x200D] | [#x203F-#x2040] | [#x2070-#x218F] | [#x2C00-#x2FEF] |\n     *     [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n     * ```\n     *\n     * [custom element name]: https://html.spec.whatwg.org/multipage/custom-elements.html#valid-custom-element-name\n     * NOTE: We're a bit more lax here since HTML technically does not allow uppercase HTML element but we do\n     * since other tag-based engines like React allow it\n     */\n    Parser.prototype.parseTag = function (nestingLevel, parentArgType) {\n        var startPosition = this.clonePosition();\n        this.bump(); // `<`\n        var tagName = this.parseTagName();\n        this.bumpSpace();\n        if (this.bumpIf('/>')) {\n            // Self closing tag\n            return {\n                val: {\n                    type: types_1.TYPE.literal,\n                    value: \"<\".concat(tagName, \"/>\"),\n                    location: createLocation(startPosition, this.clonePosition()),\n                },\n                err: null,\n            };\n        }\n        else if (this.bumpIf('>')) {\n            var childrenResult = this.parseMessage(nestingLevel + 1, parentArgType, true);\n            if (childrenResult.err) {\n                return childrenResult;\n            }\n            var children = childrenResult.val;\n            // Expecting a close tag\n            var endTagStartPosition = this.clonePosition();\n            if (this.bumpIf('</')) {\n                if (this.isEOF() || !_isAlpha(this.char())) {\n                    return this.error(error_1.ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                var closingTagNameStartPosition = this.clonePosition();\n                var closingTagName = this.parseTagName();\n                if (tagName !== closingTagName) {\n                    return this.error(error_1.ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(closingTagNameStartPosition, this.clonePosition()));\n                }\n                this.bumpSpace();\n                if (!this.bumpIf('>')) {\n                    return this.error(error_1.ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                return {\n                    val: {\n                        type: types_1.TYPE.tag,\n                        value: tagName,\n                        children: children,\n                        location: createLocation(startPosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            else {\n                return this.error(error_1.ErrorKind.UNCLOSED_TAG, createLocation(startPosition, this.clonePosition()));\n            }\n        }\n        else {\n            return this.error(error_1.ErrorKind.INVALID_TAG, createLocation(startPosition, this.clonePosition()));\n        }\n    };\n    /**\n     * This method assumes that the caller has peeked ahead for the first tag character.\n     */\n    Parser.prototype.parseTagName = function () {\n        var startOffset = this.offset();\n        this.bump(); // the first tag name character\n        while (!this.isEOF() && _isPotentialElementNameChar(this.char())) {\n            this.bump();\n        }\n        return this.message.slice(startOffset, this.offset());\n    };\n    Parser.prototype.parseLiteral = function (nestingLevel, parentArgType) {\n        var start = this.clonePosition();\n        var value = '';\n        while (true) {\n            var parseQuoteResult = this.tryParseQuote(parentArgType);\n            if (parseQuoteResult) {\n                value += parseQuoteResult;\n                continue;\n            }\n            var parseUnquotedResult = this.tryParseUnquoted(nestingLevel, parentArgType);\n            if (parseUnquotedResult) {\n                value += parseUnquotedResult;\n                continue;\n            }\n            var parseLeftAngleResult = this.tryParseLeftAngleBracket();\n            if (parseLeftAngleResult) {\n                value += parseLeftAngleResult;\n                continue;\n            }\n            break;\n        }\n        var location = createLocation(start, this.clonePosition());\n        return {\n            val: { type: types_1.TYPE.literal, value: value, location: location },\n            err: null,\n        };\n    };\n    Parser.prototype.tryParseLeftAngleBracket = function () {\n        if (!this.isEOF() &&\n            this.char() === 60 /* `<` */ &&\n            (this.ignoreTag ||\n                // If at the opening tag or closing tag position, bail.\n                !_isAlphaOrSlash(this.peek() || 0))) {\n            this.bump(); // `<`\n            return '<';\n        }\n        return null;\n    };\n    /**\n     * Starting with ICU 4.8, an ASCII apostrophe only starts quoted text if it immediately precedes\n     * a character that requires quoting (that is, \"only where needed\"), and works the same in\n     * nested messages as on the top level of the pattern. The new behavior is otherwise compatible.\n     */\n    Parser.prototype.tryParseQuote = function (parentArgType) {\n        if (this.isEOF() || this.char() !== 39 /* `'` */) {\n            return null;\n        }\n        // Parse escaped char following the apostrophe, or early return if there is no escaped char.\n        // Check if is valid escaped character\n        switch (this.peek()) {\n            case 39 /* `'` */:\n                // double quote, should return as a single quote.\n                this.bump();\n                this.bump();\n                return \"'\";\n            // '{', '<', '>', '}'\n            case 123:\n            case 60:\n            case 62:\n            case 125:\n                break;\n            case 35: // '#'\n                if (parentArgType === 'plural' || parentArgType === 'selectordinal') {\n                    break;\n                }\n                return null;\n            default:\n                return null;\n        }\n        this.bump(); // apostrophe\n        var codePoints = [this.char()]; // escaped char\n        this.bump();\n        // read chars until the optional closing apostrophe is found\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch === 39 /* `'` */) {\n                if (this.peek() === 39 /* `'` */) {\n                    codePoints.push(39);\n                    // Bump one more time because we need to skip 2 characters.\n                    this.bump();\n                }\n                else {\n                    // Optional closing apostrophe.\n                    this.bump();\n                    break;\n                }\n            }\n            else {\n                codePoints.push(ch);\n            }\n            this.bump();\n        }\n        return fromCodePoint.apply(void 0, codePoints);\n    };\n    Parser.prototype.tryParseUnquoted = function (nestingLevel, parentArgType) {\n        if (this.isEOF()) {\n            return null;\n        }\n        var ch = this.char();\n        if (ch === 60 /* `<` */ ||\n            ch === 123 /* `{` */ ||\n            (ch === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) ||\n            (ch === 125 /* `}` */ && nestingLevel > 0)) {\n            return null;\n        }\n        else {\n            this.bump();\n            return fromCodePoint(ch);\n        }\n    };\n    Parser.prototype.parseArgument = function (nestingLevel, expectingCloseTag) {\n        var openingBracePosition = this.clonePosition();\n        this.bump(); // `{`\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(error_1.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        if (this.char() === 125 /* `}` */) {\n            this.bump();\n            return this.error(error_1.ErrorKind.EMPTY_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        // argument name\n        var value = this.parseIdentifierIfPossible().value;\n        if (!value) {\n            return this.error(error_1.ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(error_1.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        switch (this.char()) {\n            // Simple argument: `{name}`\n            case 125 /* `}` */: {\n                this.bump(); // `}`\n                return {\n                    val: {\n                        type: types_1.TYPE.argument,\n                        // value does not include the opening and closing braces.\n                        value: value,\n                        location: createLocation(openingBracePosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            // Argument with options: `{name, format, ...}`\n            case 44 /* `,` */: {\n                this.bump(); // `,`\n                this.bumpSpace();\n                if (this.isEOF()) {\n                    return this.error(error_1.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n                }\n                return this.parseArgumentOptions(nestingLevel, expectingCloseTag, value, openingBracePosition);\n            }\n            default:\n                return this.error(error_1.ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n    };\n    /**\n     * Advance the parser until the end of the identifier, if it is currently on\n     * an identifier character. Return an empty string otherwise.\n     */\n    Parser.prototype.parseIdentifierIfPossible = function () {\n        var startingPosition = this.clonePosition();\n        var startOffset = this.offset();\n        var value = matchIdentifierAtIndex(this.message, startOffset);\n        var endOffset = startOffset + value.length;\n        this.bumpTo(endOffset);\n        var endPosition = this.clonePosition();\n        var location = createLocation(startingPosition, endPosition);\n        return { value: value, location: location };\n    };\n    Parser.prototype.parseArgumentOptions = function (nestingLevel, expectingCloseTag, value, openingBracePosition) {\n        var _a;\n        // Parse this range:\n        // {name, type, style}\n        //        ^---^\n        var typeStartPosition = this.clonePosition();\n        var argType = this.parseIdentifierIfPossible().value;\n        var typeEndPosition = this.clonePosition();\n        switch (argType) {\n            case '':\n                // Expecting a style string number, date, time, plural, selectordinal, or select.\n                return this.error(error_1.ErrorKind.EXPECT_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n            case 'number':\n            case 'date':\n            case 'time': {\n                // Parse this range:\n                // {name, number, style}\n                //              ^-------^\n                this.bumpSpace();\n                var styleAndLocation = null;\n                if (this.bumpIf(',')) {\n                    this.bumpSpace();\n                    var styleStartPosition = this.clonePosition();\n                    var result = this.parseSimpleArgStyleIfPossible();\n                    if (result.err) {\n                        return result;\n                    }\n                    var style = trimEnd(result.val);\n                    if (style.length === 0) {\n                        return this.error(error_1.ErrorKind.EXPECT_ARGUMENT_STYLE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    var styleLocation = createLocation(styleStartPosition, this.clonePosition());\n                    styleAndLocation = { style: style, styleLocation: styleLocation };\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_1 = createLocation(openingBracePosition, this.clonePosition());\n                // Extract style or skeleton\n                if (styleAndLocation && startsWith(styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style, '::', 0)) {\n                    // Skeleton starts with `::`.\n                    var skeleton = trimStart(styleAndLocation.style.slice(2));\n                    if (argType === 'number') {\n                        var result = this.parseNumberSkeletonFromString(skeleton, styleAndLocation.styleLocation);\n                        if (result.err) {\n                            return result;\n                        }\n                        return {\n                            val: { type: types_1.TYPE.number, value: value, location: location_1, style: result.val },\n                            err: null,\n                        };\n                    }\n                    else {\n                        if (skeleton.length === 0) {\n                            return this.error(error_1.ErrorKind.EXPECT_DATE_TIME_SKELETON, location_1);\n                        }\n                        var dateTimePattern = skeleton;\n                        // Get \"best match\" pattern only if locale is passed, if not, let it\n                        // pass as-is where `parseDateTimeSkeleton()` will throw an error\n                        // for unsupported patterns.\n                        if (this.locale) {\n                            dateTimePattern = (0, date_time_pattern_generator_1.getBestPattern)(skeleton, this.locale);\n                        }\n                        var style = {\n                            type: types_1.SKELETON_TYPE.dateTime,\n                            pattern: dateTimePattern,\n                            location: styleAndLocation.styleLocation,\n                            parsedOptions: this.shouldParseSkeletons\n                                ? (0, icu_skeleton_parser_1.parseDateTimeSkeleton)(dateTimePattern)\n                                : {},\n                        };\n                        var type = argType === 'date' ? types_1.TYPE.date : types_1.TYPE.time;\n                        return {\n                            val: { type: type, value: value, location: location_1, style: style },\n                            err: null,\n                        };\n                    }\n                }\n                // Regular style or no style.\n                return {\n                    val: {\n                        type: argType === 'number'\n                            ? types_1.TYPE.number\n                            : argType === 'date'\n                                ? types_1.TYPE.date\n                                : types_1.TYPE.time,\n                        value: value,\n                        location: location_1,\n                        style: (_a = styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style) !== null && _a !== void 0 ? _a : null,\n                    },\n                    err: null,\n                };\n            }\n            case 'plural':\n            case 'selectordinal':\n            case 'select': {\n                // Parse this range:\n                // {name, plural, options}\n                //              ^---------^\n                var typeEndPosition_1 = this.clonePosition();\n                this.bumpSpace();\n                if (!this.bumpIf(',')) {\n                    return this.error(error_1.ErrorKind.EXPECT_SELECT_ARGUMENT_OPTIONS, createLocation(typeEndPosition_1, tslib_1.__assign({}, typeEndPosition_1)));\n                }\n                this.bumpSpace();\n                // Parse offset:\n                // {name, plural, offset:1, options}\n                //                ^-----^\n                //\n                // or the first option:\n                //\n                // {name, plural, one {...} other {...}}\n                //                ^--^\n                var identifierAndLocation = this.parseIdentifierIfPossible();\n                var pluralOffset = 0;\n                if (argType !== 'select' && identifierAndLocation.value === 'offset') {\n                    if (!this.bumpIf(':')) {\n                        return this.error(error_1.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    this.bumpSpace();\n                    var result = this.tryParseDecimalInteger(error_1.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, error_1.ErrorKind.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);\n                    if (result.err) {\n                        return result;\n                    }\n                    // Parse another identifier for option parsing\n                    this.bumpSpace();\n                    identifierAndLocation = this.parseIdentifierIfPossible();\n                    pluralOffset = result.val;\n                }\n                var optionsResult = this.tryParsePluralOrSelectOptions(nestingLevel, argType, expectingCloseTag, identifierAndLocation);\n                if (optionsResult.err) {\n                    return optionsResult;\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_2 = createLocation(openingBracePosition, this.clonePosition());\n                if (argType === 'select') {\n                    return {\n                        val: {\n                            type: types_1.TYPE.select,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n                else {\n                    return {\n                        val: {\n                            type: types_1.TYPE.plural,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            offset: pluralOffset,\n                            pluralType: argType === 'plural' ? 'cardinal' : 'ordinal',\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n            }\n            default:\n                return this.error(error_1.ErrorKind.INVALID_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n        }\n    };\n    Parser.prototype.tryParseArgumentClose = function (openingBracePosition) {\n        // Parse: {value, number, ::currency/GBP }\n        //\n        if (this.isEOF() || this.char() !== 125 /* `}` */) {\n            return this.error(error_1.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bump(); // `}`\n        return { val: true, err: null };\n    };\n    /**\n     * See: https://github.com/unicode-org/icu/blob/af7ed1f6d2298013dc303628438ec4abe1f16479/icu4c/source/common/messagepattern.cpp#L659\n     */\n    Parser.prototype.parseSimpleArgStyleIfPossible = function () {\n        var nestedBraces = 0;\n        var startPosition = this.clonePosition();\n        while (!this.isEOF()) {\n            var ch = this.char();\n            switch (ch) {\n                case 39 /* `'` */: {\n                    // Treat apostrophe as quoting but include it in the style part.\n                    // Find the end of the quoted literal text.\n                    this.bump();\n                    var apostrophePosition = this.clonePosition();\n                    if (!this.bumpUntil(\"'\")) {\n                        return this.error(error_1.ErrorKind.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE, createLocation(apostrophePosition, this.clonePosition()));\n                    }\n                    this.bump();\n                    break;\n                }\n                case 123 /* `{` */: {\n                    nestedBraces += 1;\n                    this.bump();\n                    break;\n                }\n                case 125 /* `}` */: {\n                    if (nestedBraces > 0) {\n                        nestedBraces -= 1;\n                    }\n                    else {\n                        return {\n                            val: this.message.slice(startPosition.offset, this.offset()),\n                            err: null,\n                        };\n                    }\n                    break;\n                }\n                default:\n                    this.bump();\n                    break;\n            }\n        }\n        return {\n            val: this.message.slice(startPosition.offset, this.offset()),\n            err: null,\n        };\n    };\n    Parser.prototype.parseNumberSkeletonFromString = function (skeleton, location) {\n        var tokens = [];\n        try {\n            tokens = (0, icu_skeleton_parser_1.parseNumberSkeletonFromString)(skeleton);\n        }\n        catch (e) {\n            return this.error(error_1.ErrorKind.INVALID_NUMBER_SKELETON, location);\n        }\n        return {\n            val: {\n                type: types_1.SKELETON_TYPE.number,\n                tokens: tokens,\n                location: location,\n                parsedOptions: this.shouldParseSkeletons\n                    ? (0, icu_skeleton_parser_1.parseNumberSkeleton)(tokens)\n                    : {},\n            },\n            err: null,\n        };\n    };\n    /**\n     * @param nesting_level The current nesting level of messages.\n     *     This can be positive when parsing message fragment in select or plural argument options.\n     * @param parent_arg_type The parent argument's type.\n     * @param parsed_first_identifier If provided, this is the first identifier-like selector of\n     *     the argument. It is a by-product of a previous parsing attempt.\n     * @param expecting_close_tag If true, this message is directly or indirectly nested inside\n     *     between a pair of opening and closing tags. The nested message will not parse beyond\n     *     the closing tag boundary.\n     */\n    Parser.prototype.tryParsePluralOrSelectOptions = function (nestingLevel, parentArgType, expectCloseTag, parsedFirstIdentifier) {\n        var _a;\n        var hasOtherClause = false;\n        var options = [];\n        var parsedSelectors = new Set();\n        var selector = parsedFirstIdentifier.value, selectorLocation = parsedFirstIdentifier.location;\n        // Parse:\n        // one {one apple}\n        // ^--^\n        while (true) {\n            if (selector.length === 0) {\n                var startPosition = this.clonePosition();\n                if (parentArgType !== 'select' && this.bumpIf('=')) {\n                    // Try parse `={number}` selector\n                    var result = this.tryParseDecimalInteger(error_1.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, error_1.ErrorKind.INVALID_PLURAL_ARGUMENT_SELECTOR);\n                    if (result.err) {\n                        return result;\n                    }\n                    selectorLocation = createLocation(startPosition, this.clonePosition());\n                    selector = this.message.slice(startPosition.offset, this.offset());\n                }\n                else {\n                    break;\n                }\n            }\n            // Duplicate selector clauses\n            if (parsedSelectors.has(selector)) {\n                return this.error(parentArgType === 'select'\n                    ? error_1.ErrorKind.DUPLICATE_SELECT_ARGUMENT_SELECTOR\n                    : error_1.ErrorKind.DUPLICATE_PLURAL_ARGUMENT_SELECTOR, selectorLocation);\n            }\n            if (selector === 'other') {\n                hasOtherClause = true;\n            }\n            // Parse:\n            // one {one apple}\n            //     ^----------^\n            this.bumpSpace();\n            var openingBracePosition = this.clonePosition();\n            if (!this.bumpIf('{')) {\n                return this.error(parentArgType === 'select'\n                    ? error_1.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\n                    : error_1.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT, createLocation(this.clonePosition(), this.clonePosition()));\n            }\n            var fragmentResult = this.parseMessage(nestingLevel + 1, parentArgType, expectCloseTag);\n            if (fragmentResult.err) {\n                return fragmentResult;\n            }\n            var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n            if (argCloseResult.err) {\n                return argCloseResult;\n            }\n            options.push([\n                selector,\n                {\n                    value: fragmentResult.val,\n                    location: createLocation(openingBracePosition, this.clonePosition()),\n                },\n            ]);\n            // Keep track of the existing selectors\n            parsedSelectors.add(selector);\n            // Prep next selector clause.\n            this.bumpSpace();\n            (_a = this.parseIdentifierIfPossible(), selector = _a.value, selectorLocation = _a.location);\n        }\n        if (options.length === 0) {\n            return this.error(parentArgType === 'select'\n                ? error_1.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR\n                : error_1.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        if (this.requiresOtherClause && !hasOtherClause) {\n            return this.error(error_1.ErrorKind.MISSING_OTHER_CLAUSE, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        return { val: options, err: null };\n    };\n    Parser.prototype.tryParseDecimalInteger = function (expectNumberError, invalidNumberError) {\n        var sign = 1;\n        var startingPosition = this.clonePosition();\n        if (this.bumpIf('+')) {\n        }\n        else if (this.bumpIf('-')) {\n            sign = -1;\n        }\n        var hasDigits = false;\n        var decimal = 0;\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch >= 48 /* `0` */ && ch <= 57 /* `9` */) {\n                hasDigits = true;\n                decimal = decimal * 10 + (ch - 48);\n                this.bump();\n            }\n            else {\n                break;\n            }\n        }\n        var location = createLocation(startingPosition, this.clonePosition());\n        if (!hasDigits) {\n            return this.error(expectNumberError, location);\n        }\n        decimal *= sign;\n        if (!isSafeInteger(decimal)) {\n            return this.error(invalidNumberError, location);\n        }\n        return { val: decimal, err: null };\n    };\n    Parser.prototype.offset = function () {\n        return this.position.offset;\n    };\n    Parser.prototype.isEOF = function () {\n        return this.offset() === this.message.length;\n    };\n    Parser.prototype.clonePosition = function () {\n        // This is much faster than `Object.assign` or spread.\n        return {\n            offset: this.position.offset,\n            line: this.position.line,\n            column: this.position.column,\n        };\n    };\n    /**\n     * Return the code point at the current position of the parser.\n     * Throws if the index is out of bound.\n     */\n    Parser.prototype.char = function () {\n        var offset = this.position.offset;\n        if (offset >= this.message.length) {\n            throw Error('out of bound');\n        }\n        var code = codePointAt(this.message, offset);\n        if (code === undefined) {\n            throw Error(\"Offset \".concat(offset, \" is at invalid UTF-16 code unit boundary\"));\n        }\n        return code;\n    };\n    Parser.prototype.error = function (kind, location) {\n        return {\n            val: null,\n            err: {\n                kind: kind,\n                message: this.message,\n                location: location,\n            },\n        };\n    };\n    /** Bump the parser to the next UTF-16 code unit. */\n    Parser.prototype.bump = function () {\n        if (this.isEOF()) {\n            return;\n        }\n        var code = this.char();\n        if (code === 10 /* '\\n' */) {\n            this.position.line += 1;\n            this.position.column = 1;\n            this.position.offset += 1;\n        }\n        else {\n            this.position.column += 1;\n            // 0 ~ 0x10000 -> unicode BMP, otherwise skip the surrogate pair.\n            this.position.offset += code < 0x10000 ? 1 : 2;\n        }\n    };\n    /**\n     * If the substring starting at the current position of the parser has\n     * the given prefix, then bump the parser to the character immediately\n     * following the prefix and return true. Otherwise, don't bump the parser\n     * and return false.\n     */\n    Parser.prototype.bumpIf = function (prefix) {\n        if (startsWith(this.message, prefix, this.offset())) {\n            for (var i = 0; i < prefix.length; i++) {\n                this.bump();\n            }\n            return true;\n        }\n        return false;\n    };\n    /**\n     * Bump the parser until the pattern character is found and return `true`.\n     * Otherwise bump to the end of the file and return `false`.\n     */\n    Parser.prototype.bumpUntil = function (pattern) {\n        var currentOffset = this.offset();\n        var index = this.message.indexOf(pattern, currentOffset);\n        if (index >= 0) {\n            this.bumpTo(index);\n            return true;\n        }\n        else {\n            this.bumpTo(this.message.length);\n            return false;\n        }\n    };\n    /**\n     * Bump the parser to the target offset.\n     * If target offset is beyond the end of the input, bump the parser to the end of the input.\n     */\n    Parser.prototype.bumpTo = function (targetOffset) {\n        if (this.offset() > targetOffset) {\n            throw Error(\"targetOffset \".concat(targetOffset, \" must be greater than or equal to the current offset \").concat(this.offset()));\n        }\n        targetOffset = Math.min(targetOffset, this.message.length);\n        while (true) {\n            var offset = this.offset();\n            if (offset === targetOffset) {\n                break;\n            }\n            if (offset > targetOffset) {\n                throw Error(\"targetOffset \".concat(targetOffset, \" is at invalid UTF-16 code unit boundary\"));\n            }\n            this.bump();\n            if (this.isEOF()) {\n                break;\n            }\n        }\n    };\n    /** advance the parser through all whitespace to the next non-whitespace code unit. */\n    Parser.prototype.bumpSpace = function () {\n        while (!this.isEOF() && _isWhiteSpace(this.char())) {\n            this.bump();\n        }\n    };\n    /**\n     * Peek at the *next* Unicode codepoint in the input without advancing the parser.\n     * If the input has been exhausted, then this returns null.\n     */\n    Parser.prototype.peek = function () {\n        if (this.isEOF()) {\n            return null;\n        }\n        var code = this.char();\n        var offset = this.offset();\n        var nextCode = this.message.charCodeAt(offset + (code >= 0x10000 ? 2 : 1));\n        return nextCode !== null && nextCode !== void 0 ? nextCode : null;\n    };\n    return Parser;\n}());\nexports.Parser = Parser;\n/**\n * This check if codepoint is alphabet (lower & uppercase)\n * @param codepoint\n * @returns\n */\nfunction _isAlpha(codepoint) {\n    return ((codepoint >= 97 && codepoint <= 122) ||\n        (codepoint >= 65 && codepoint <= 90));\n}\nfunction _isAlphaOrSlash(codepoint) {\n    return _isAlpha(codepoint) || codepoint === 47; /* '/' */\n}\n/** See `parseTag` function docs. */\nfunction _isPotentialElementNameChar(c) {\n    return (c === 45 /* '-' */ ||\n        c === 46 /* '.' */ ||\n        (c >= 48 && c <= 57) /* 0..9 */ ||\n        c === 95 /* '_' */ ||\n        (c >= 97 && c <= 122) /** a..z */ ||\n        (c >= 65 && c <= 90) /* A..Z */ ||\n        c == 0xb7 ||\n        (c >= 0xc0 && c <= 0xd6) ||\n        (c >= 0xd8 && c <= 0xf6) ||\n        (c >= 0xf8 && c <= 0x37d) ||\n        (c >= 0x37f && c <= 0x1fff) ||\n        (c >= 0x200c && c <= 0x200d) ||\n        (c >= 0x203f && c <= 0x2040) ||\n        (c >= 0x2070 && c <= 0x218f) ||\n        (c >= 0x2c00 && c <= 0x2fef) ||\n        (c >= 0x3001 && c <= 0xd7ff) ||\n        (c >= 0xf900 && c <= 0xfdcf) ||\n        (c >= 0xfdf0 && c <= 0xfffd) ||\n        (c >= 0x10000 && c <= 0xeffff));\n}\n/**\n * Code point equivalent of regex `\\p{White_Space}`.\n * From: https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isWhiteSpace(c) {\n    return ((c >= 0x0009 && c <= 0x000d) ||\n        c === 0x0020 ||\n        c === 0x0085 ||\n        (c >= 0x200e && c <= 0x200f) ||\n        c === 0x2028 ||\n        c === 0x2029);\n}\n/**\n * Code point equivalent of regex `\\p{Pattern_Syntax}`.\n * See https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isPatternSyntax(c) {\n    return ((c >= 0x0021 && c <= 0x0023) ||\n        c === 0x0024 ||\n        (c >= 0x0025 && c <= 0x0027) ||\n        c === 0x0028 ||\n        c === 0x0029 ||\n        c === 0x002a ||\n        c === 0x002b ||\n        c === 0x002c ||\n        c === 0x002d ||\n        (c >= 0x002e && c <= 0x002f) ||\n        (c >= 0x003a && c <= 0x003b) ||\n        (c >= 0x003c && c <= 0x003e) ||\n        (c >= 0x003f && c <= 0x0040) ||\n        c === 0x005b ||\n        c === 0x005c ||\n        c === 0x005d ||\n        c === 0x005e ||\n        c === 0x0060 ||\n        c === 0x007b ||\n        c === 0x007c ||\n        c === 0x007d ||\n        c === 0x007e ||\n        c === 0x00a1 ||\n        (c >= 0x00a2 && c <= 0x00a5) ||\n        c === 0x00a6 ||\n        c === 0x00a7 ||\n        c === 0x00a9 ||\n        c === 0x00ab ||\n        c === 0x00ac ||\n        c === 0x00ae ||\n        c === 0x00b0 ||\n        c === 0x00b1 ||\n        c === 0x00b6 ||\n        c === 0x00bb ||\n        c === 0x00bf ||\n        c === 0x00d7 ||\n        c === 0x00f7 ||\n        (c >= 0x2010 && c <= 0x2015) ||\n        (c >= 0x2016 && c <= 0x2017) ||\n        c === 0x2018 ||\n        c === 0x2019 ||\n        c === 0x201a ||\n        (c >= 0x201b && c <= 0x201c) ||\n        c === 0x201d ||\n        c === 0x201e ||\n        c === 0x201f ||\n        (c >= 0x2020 && c <= 0x2027) ||\n        (c >= 0x2030 && c <= 0x2038) ||\n        c === 0x2039 ||\n        c === 0x203a ||\n        (c >= 0x203b && c <= 0x203e) ||\n        (c >= 0x2041 && c <= 0x2043) ||\n        c === 0x2044 ||\n        c === 0x2045 ||\n        c === 0x2046 ||\n        (c >= 0x2047 && c <= 0x2051) ||\n        c === 0x2052 ||\n        c === 0x2053 ||\n        (c >= 0x2055 && c <= 0x205e) ||\n        (c >= 0x2190 && c <= 0x2194) ||\n        (c >= 0x2195 && c <= 0x2199) ||\n        (c >= 0x219a && c <= 0x219b) ||\n        (c >= 0x219c && c <= 0x219f) ||\n        c === 0x21a0 ||\n        (c >= 0x21a1 && c <= 0x21a2) ||\n        c === 0x21a3 ||\n        (c >= 0x21a4 && c <= 0x21a5) ||\n        c === 0x21a6 ||\n        (c >= 0x21a7 && c <= 0x21ad) ||\n        c === 0x21ae ||\n        (c >= 0x21af && c <= 0x21cd) ||\n        (c >= 0x21ce && c <= 0x21cf) ||\n        (c >= 0x21d0 && c <= 0x21d1) ||\n        c === 0x21d2 ||\n        c === 0x21d3 ||\n        c === 0x21d4 ||\n        (c >= 0x21d5 && c <= 0x21f3) ||\n        (c >= 0x21f4 && c <= 0x22ff) ||\n        (c >= 0x2300 && c <= 0x2307) ||\n        c === 0x2308 ||\n        c === 0x2309 ||\n        c === 0x230a ||\n        c === 0x230b ||\n        (c >= 0x230c && c <= 0x231f) ||\n        (c >= 0x2320 && c <= 0x2321) ||\n        (c >= 0x2322 && c <= 0x2328) ||\n        c === 0x2329 ||\n        c === 0x232a ||\n        (c >= 0x232b && c <= 0x237b) ||\n        c === 0x237c ||\n        (c >= 0x237d && c <= 0x239a) ||\n        (c >= 0x239b && c <= 0x23b3) ||\n        (c >= 0x23b4 && c <= 0x23db) ||\n        (c >= 0x23dc && c <= 0x23e1) ||\n        (c >= 0x23e2 && c <= 0x2426) ||\n        (c >= 0x2427 && c <= 0x243f) ||\n        (c >= 0x2440 && c <= 0x244a) ||\n        (c >= 0x244b && c <= 0x245f) ||\n        (c >= 0x2500 && c <= 0x25b6) ||\n        c === 0x25b7 ||\n        (c >= 0x25b8 && c <= 0x25c0) ||\n        c === 0x25c1 ||\n        (c >= 0x25c2 && c <= 0x25f7) ||\n        (c >= 0x25f8 && c <= 0x25ff) ||\n        (c >= 0x2600 && c <= 0x266e) ||\n        c === 0x266f ||\n        (c >= 0x2670 && c <= 0x2767) ||\n        c === 0x2768 ||\n        c === 0x2769 ||\n        c === 0x276a ||\n        c === 0x276b ||\n        c === 0x276c ||\n        c === 0x276d ||\n        c === 0x276e ||\n        c === 0x276f ||\n        c === 0x2770 ||\n        c === 0x2771 ||\n        c === 0x2772 ||\n        c === 0x2773 ||\n        c === 0x2774 ||\n        c === 0x2775 ||\n        (c >= 0x2794 && c <= 0x27bf) ||\n        (c >= 0x27c0 && c <= 0x27c4) ||\n        c === 0x27c5 ||\n        c === 0x27c6 ||\n        (c >= 0x27c7 && c <= 0x27e5) ||\n        c === 0x27e6 ||\n        c === 0x27e7 ||\n        c === 0x27e8 ||\n        c === 0x27e9 ||\n        c === 0x27ea ||\n        c === 0x27eb ||\n        c === 0x27ec ||\n        c === 0x27ed ||\n        c === 0x27ee ||\n        c === 0x27ef ||\n        (c >= 0x27f0 && c <= 0x27ff) ||\n        (c >= 0x2800 && c <= 0x28ff) ||\n        (c >= 0x2900 && c <= 0x2982) ||\n        c === 0x2983 ||\n        c === 0x2984 ||\n        c === 0x2985 ||\n        c === 0x2986 ||\n        c === 0x2987 ||\n        c === 0x2988 ||\n        c === 0x2989 ||\n        c === 0x298a ||\n        c === 0x298b ||\n        c === 0x298c ||\n        c === 0x298d ||\n        c === 0x298e ||\n        c === 0x298f ||\n        c === 0x2990 ||\n        c === 0x2991 ||\n        c === 0x2992 ||\n        c === 0x2993 ||\n        c === 0x2994 ||\n        c === 0x2995 ||\n        c === 0x2996 ||\n        c === 0x2997 ||\n        c === 0x2998 ||\n        (c >= 0x2999 && c <= 0x29d7) ||\n        c === 0x29d8 ||\n        c === 0x29d9 ||\n        c === 0x29da ||\n        c === 0x29db ||\n        (c >= 0x29dc && c <= 0x29fb) ||\n        c === 0x29fc ||\n        c === 0x29fd ||\n        (c >= 0x29fe && c <= 0x2aff) ||\n        (c >= 0x2b00 && c <= 0x2b2f) ||\n        (c >= 0x2b30 && c <= 0x2b44) ||\n        (c >= 0x2b45 && c <= 0x2b46) ||\n        (c >= 0x2b47 && c <= 0x2b4c) ||\n        (c >= 0x2b4d && c <= 0x2b73) ||\n        (c >= 0x2b74 && c <= 0x2b75) ||\n        (c >= 0x2b76 && c <= 0x2b95) ||\n        c === 0x2b96 ||\n        (c >= 0x2b97 && c <= 0x2bff) ||\n        (c >= 0x2e00 && c <= 0x2e01) ||\n        c === 0x2e02 ||\n        c === 0x2e03 ||\n        c === 0x2e04 ||\n        c === 0x2e05 ||\n        (c >= 0x2e06 && c <= 0x2e08) ||\n        c === 0x2e09 ||\n        c === 0x2e0a ||\n        c === 0x2e0b ||\n        c === 0x2e0c ||\n        c === 0x2e0d ||\n        (c >= 0x2e0e && c <= 0x2e16) ||\n        c === 0x2e17 ||\n        (c >= 0x2e18 && c <= 0x2e19) ||\n        c === 0x2e1a ||\n        c === 0x2e1b ||\n        c === 0x2e1c ||\n        c === 0x2e1d ||\n        (c >= 0x2e1e && c <= 0x2e1f) ||\n        c === 0x2e20 ||\n        c === 0x2e21 ||\n        c === 0x2e22 ||\n        c === 0x2e23 ||\n        c === 0x2e24 ||\n        c === 0x2e25 ||\n        c === 0x2e26 ||\n        c === 0x2e27 ||\n        c === 0x2e28 ||\n        c === 0x2e29 ||\n        (c >= 0x2e2a && c <= 0x2e2e) ||\n        c === 0x2e2f ||\n        (c >= 0x2e30 && c <= 0x2e39) ||\n        (c >= 0x2e3a && c <= 0x2e3b) ||\n        (c >= 0x2e3c && c <= 0x2e3f) ||\n        c === 0x2e40 ||\n        c === 0x2e41 ||\n        c === 0x2e42 ||\n        (c >= 0x2e43 && c <= 0x2e4f) ||\n        (c >= 0x2e50 && c <= 0x2e51) ||\n        c === 0x2e52 ||\n        (c >= 0x2e53 && c <= 0x2e7f) ||\n        (c >= 0x3001 && c <= 0x3003) ||\n        c === 0x3008 ||\n        c === 0x3009 ||\n        c === 0x300a ||\n        c === 0x300b ||\n        c === 0x300c ||\n        c === 0x300d ||\n        c === 0x300e ||\n        c === 0x300f ||\n        c === 0x3010 ||\n        c === 0x3011 ||\n        (c >= 0x3012 && c <= 0x3013) ||\n        c === 0x3014 ||\n        c === 0x3015 ||\n        c === 0x3016 ||\n        c === 0x3017 ||\n        c === 0x3018 ||\n        c === 0x3019 ||\n        c === 0x301a ||\n        c === 0x301b ||\n        c === 0x301c ||\n        c === 0x301d ||\n        (c >= 0x301e && c <= 0x301f) ||\n        c === 0x3020 ||\n        c === 0x3030 ||\n        c === 0xfd3e ||\n        c === 0xfd3f ||\n        (c >= 0xfe45 && c <= 0xfe46));\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports._Parser = exports.parse = void 0;\nvar tslib_1 = require(\"tslib\");\nvar error_1 = require(\"./error\");\nvar parser_1 = require(\"./parser\");\nvar types_1 = require(\"./types\");\nfunction pruneLocation(els) {\n    els.forEach(function (el) {\n        delete el.location;\n        if ((0, types_1.isSelectElement)(el) || (0, types_1.isPluralElement)(el)) {\n            for (var k in el.options) {\n                delete el.options[k].location;\n                pruneLocation(el.options[k].value);\n            }\n        }\n        else if ((0, types_1.isNumberElement)(el) && (0, types_1.isNumberSkeleton)(el.style)) {\n            delete el.style.location;\n        }\n        else if (((0, types_1.isDateElement)(el) || (0, types_1.isTimeElement)(el)) &&\n            (0, types_1.isDateTimeSkeleton)(el.style)) {\n            delete el.style.location;\n        }\n        else if ((0, types_1.isTagElement)(el)) {\n            pruneLocation(el.children);\n        }\n    });\n}\nfunction parse(message, opts) {\n    if (opts === void 0) { opts = {}; }\n    opts = tslib_1.__assign({ shouldParseSkeletons: true, requiresOtherClause: true }, opts);\n    var result = new parser_1.Parser(message, opts).parse();\n    if (result.err) {\n        var error = SyntaxError(error_1.ErrorKind[result.err.kind]);\n        // @ts-expect-error Assign to error object\n        error.location = result.err.location;\n        // @ts-expect-error Assign to error object\n        error.originalMessage = result.err.message;\n        throw error;\n    }\n    if (!(opts === null || opts === void 0 ? void 0 : opts.captureLocation)) {\n        pruneLocation(result.val);\n    }\n    return result.val;\n}\nexports.parse = parse;\ntslib_1.__exportStar(require(\"./types\"), exports);\n// only for testing\nexports._Parser = parser_1.Parser;\n", "//\n// Main\n//\nexport function memoize(fn, options) {\n    var cache = options && options.cache ? options.cache : cacheDefault;\n    var serializer = options && options.serializer ? options.serializer : serializerDefault;\n    var strategy = options && options.strategy ? options.strategy : strategyDefault;\n    return strategy(fn, {\n        cache: cache,\n        serializer: serializer,\n    });\n}\n//\n// Strategy\n//\nfunction isPrimitive(value) {\n    return (value == null || typeof value === 'number' || typeof value === 'boolean'); // || typeof value === \"string\" 'unsafe' primitive for our needs\n}\nfunction monadic(fn, cache, serializer, arg) {\n    var cacheKey = isPrimitive(arg) ? arg : serializer(arg);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.call(this, arg);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction variadic(fn, cache, serializer) {\n    var args = Array.prototype.slice.call(arguments, 3);\n    var cacheKey = serializer(args);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.apply(this, args);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction assemble(fn, context, strategy, cache, serialize) {\n    return strategy.bind(context, fn, cache, serialize);\n}\nfunction strategyDefault(fn, options) {\n    var strategy = fn.length === 1 ? monadic : variadic;\n    return assemble(fn, this, strategy, options.cache.create(), options.serializer);\n}\nfunction strategyVariadic(fn, options) {\n    return assemble(fn, this, variadic, options.cache.create(), options.serializer);\n}\nfunction strategyMonadic(fn, options) {\n    return assemble(fn, this, monadic, options.cache.create(), options.serializer);\n}\n//\n// Serializer\n//\nvar serializerDefault = function () {\n    return JSON.stringify(arguments);\n};\n//\n// Cache\n//\nfunction ObjectWithoutPrototypeCache() {\n    this.cache = Object.create(null);\n}\nObjectWithoutPrototypeCache.prototype.get = function (key) {\n    return this.cache[key];\n};\nObjectWithoutPrototypeCache.prototype.set = function (key, value) {\n    this.cache[key] = value;\n};\nvar cacheDefault = {\n    create: function create() {\n        // @ts-ignore\n        return new ObjectWithoutPrototypeCache();\n    },\n};\nexport var strategies = {\n    variadic: strategyVariadic,\n    monadic: strategyMonadic,\n};\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MissingValueError = exports.InvalidValueTypeError = exports.InvalidValueError = exports.FormatError = exports.ErrorCode = void 0;\nvar tslib_1 = require(\"tslib\");\nvar ErrorCode;\n(function (ErrorCode) {\n    // When we have a placeholder but no value to format\n    ErrorCode[\"MISSING_VALUE\"] = \"MISSING_VALUE\";\n    // When value supplied is invalid\n    ErrorCode[\"INVALID_VALUE\"] = \"INVALID_VALUE\";\n    // When we need specific Intl API but it's not available\n    ErrorCode[\"MISSING_INTL_API\"] = \"MISSING_INTL_API\";\n})(ErrorCode || (exports.ErrorCode = ErrorCode = {}));\nvar FormatError = /** @class */ (function (_super) {\n    tslib_1.__extends(FormatError, _super);\n    function FormatError(msg, code, originalMessage) {\n        var _this = _super.call(this, msg) || this;\n        _this.code = code;\n        _this.originalMessage = originalMessage;\n        return _this;\n    }\n    FormatError.prototype.toString = function () {\n        return \"[formatjs Error: \".concat(this.code, \"] \").concat(this.message);\n    };\n    return FormatError;\n}(Error));\nexports.FormatError = FormatError;\nvar InvalidValueError = /** @class */ (function (_super) {\n    tslib_1.__extends(InvalidValueError, _super);\n    function InvalidValueError(variableId, value, options, originalMessage) {\n        return _super.call(this, \"Invalid values for \\\"\".concat(variableId, \"\\\": \\\"\").concat(value, \"\\\". Options are \\\"\").concat(Object.keys(options).join('\", \"'), \"\\\"\"), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueError;\n}(FormatError));\nexports.InvalidValueError = InvalidValueError;\nvar InvalidValueTypeError = /** @class */ (function (_super) {\n    tslib_1.__extends(InvalidValueTypeError, _super);\n    function InvalidValueTypeError(value, type, originalMessage) {\n        return _super.call(this, \"Value for \\\"\".concat(value, \"\\\" must be of type \").concat(type), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueTypeError;\n}(FormatError));\nexports.InvalidValueTypeError = InvalidValueTypeError;\nvar MissingValueError = /** @class */ (function (_super) {\n    tslib_1.__extends(MissingValueError, _super);\n    function MissingValueError(variableId, originalMessage) {\n        return _super.call(this, \"The intl string context variable \\\"\".concat(variableId, \"\\\" was not provided to the string \\\"\").concat(originalMessage, \"\\\"\"), ErrorCode.MISSING_VALUE, originalMessage) || this;\n    }\n    return MissingValueError;\n}(FormatError));\nexports.MissingValueError = MissingValueError;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.formatToParts = exports.isFormatXMLElementFn = exports.PART_TYPE = void 0;\nvar icu_messageformat_parser_1 = require(\"@formatjs/icu-messageformat-parser\");\nvar error_1 = require(\"./error\");\nvar PART_TYPE;\n(function (PART_TYPE) {\n    PART_TYPE[PART_TYPE[\"literal\"] = 0] = \"literal\";\n    PART_TYPE[PART_TYPE[\"object\"] = 1] = \"object\";\n})(PART_TYPE || (exports.PART_TYPE = PART_TYPE = {}));\nfunction mergeLiteral(parts) {\n    if (parts.length < 2) {\n        return parts;\n    }\n    return parts.reduce(function (all, part) {\n        var lastPart = all[all.length - 1];\n        if (!lastPart ||\n            lastPart.type !== PART_TYPE.literal ||\n            part.type !== PART_TYPE.literal) {\n            all.push(part);\n        }\n        else {\n            lastPart.value += part.value;\n        }\n        return all;\n    }, []);\n}\nfunction isFormatXMLElementFn(el) {\n    return typeof el === 'function';\n}\nexports.isFormatXMLElementFn = isFormatXMLElementFn;\n// TODO(skeleton): add skeleton support\nfunction formatToParts(els, locales, formatters, formats, values, currentPluralValue, \n// For debugging\noriginalMessage) {\n    // Hot path for straight simple msg translations\n    if (els.length === 1 && (0, icu_messageformat_parser_1.isLiteralElement)(els[0])) {\n        return [\n            {\n                type: PART_TYPE.literal,\n                value: els[0].value,\n            },\n        ];\n    }\n    var result = [];\n    for (var _i = 0, els_1 = els; _i < els_1.length; _i++) {\n        var el = els_1[_i];\n        // Exit early for string parts.\n        if ((0, icu_messageformat_parser_1.isLiteralElement)(el)) {\n            result.push({\n                type: PART_TYPE.literal,\n                value: el.value,\n            });\n            continue;\n        }\n        // TODO: should this part be literal type?\n        // Replace `#` in plural rules with the actual numeric value.\n        if ((0, icu_messageformat_parser_1.isPoundElement)(el)) {\n            if (typeof currentPluralValue === 'number') {\n                result.push({\n                    type: PART_TYPE.literal,\n                    value: formatters.getNumberFormat(locales).format(currentPluralValue),\n                });\n            }\n            continue;\n        }\n        var varName = el.value;\n        // Enforce that all required values are provided by the caller.\n        if (!(values && varName in values)) {\n            throw new error_1.MissingValueError(varName, originalMessage);\n        }\n        var value = values[varName];\n        if ((0, icu_messageformat_parser_1.isArgumentElement)(el)) {\n            if (!value || typeof value === 'string' || typeof value === 'number') {\n                value =\n                    typeof value === 'string' || typeof value === 'number'\n                        ? String(value)\n                        : '';\n            }\n            result.push({\n                type: typeof value === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                value: value,\n            });\n            continue;\n        }\n        // Recursively format plural and select parts' option — which can be a\n        // nested pattern structure. The choosing of the option to use is\n        // abstracted-by and delegated-to the part helper object.\n        if ((0, icu_messageformat_parser_1.isDateElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.date[el.style]\n                : (0, icu_messageformat_parser_1.isDateTimeSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0, icu_messageformat_parser_1.isTimeElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.time[el.style]\n                : (0, icu_messageformat_parser_1.isDateTimeSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : formats.time.medium;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0, icu_messageformat_parser_1.isNumberElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.number[el.style]\n                : (0, icu_messageformat_parser_1.isNumberSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            if (style && style.scale) {\n                value =\n                    value *\n                        (style.scale || 1);\n            }\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getNumberFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0, icu_messageformat_parser_1.isTagElement)(el)) {\n            var children = el.children, value_1 = el.value;\n            var formatFn = values[value_1];\n            if (!isFormatXMLElementFn(formatFn)) {\n                throw new error_1.InvalidValueTypeError(value_1, 'function', originalMessage);\n            }\n            var parts = formatToParts(children, locales, formatters, formats, values, currentPluralValue);\n            var chunks = formatFn(parts.map(function (p) { return p.value; }));\n            if (!Array.isArray(chunks)) {\n                chunks = [chunks];\n            }\n            result.push.apply(result, chunks.map(function (c) {\n                return {\n                    type: typeof c === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                    value: c,\n                };\n            }));\n        }\n        if ((0, icu_messageformat_parser_1.isSelectElement)(el)) {\n            var opt = el.options[value] || el.options.other;\n            if (!opt) {\n                throw new error_1.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values));\n            continue;\n        }\n        if ((0, icu_messageformat_parser_1.isPluralElement)(el)) {\n            var opt = el.options[\"=\".concat(value)];\n            if (!opt) {\n                if (!Intl.PluralRules) {\n                    throw new error_1.FormatError(\"Intl.PluralRules is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-pluralrules\\\"\\n\", error_1.ErrorCode.MISSING_INTL_API, originalMessage);\n                }\n                var rule = formatters\n                    .getPluralRules(locales, { type: el.pluralType })\n                    .select(value - (el.offset || 0));\n                opt = el.options[rule] || el.options.other;\n            }\n            if (!opt) {\n                throw new error_1.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values, value - (el.offset || 0)));\n            continue;\n        }\n    }\n    return mergeLiteral(result);\n}\nexports.formatToParts = formatToParts;\n", "\"use strict\";\n/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.IntlMessageFormat = void 0;\nvar tslib_1 = require(\"tslib\");\nvar icu_messageformat_parser_1 = require(\"@formatjs/icu-messageformat-parser\");\nvar fast_memoize_1 = require(\"@formatjs/fast-memoize\");\nvar formatters_1 = require(\"./formatters\");\n// -- MessageFormat --------------------------------------------------------\nfunction mergeConfig(c1, c2) {\n    if (!c2) {\n        return c1;\n    }\n    return tslib_1.__assign(tslib_1.__assign(tslib_1.__assign({}, (c1 || {})), (c2 || {})), Object.keys(c1).reduce(function (all, k) {\n        all[k] = tslib_1.__assign(tslib_1.__assign({}, c1[k]), (c2[k] || {}));\n        return all;\n    }, {}));\n}\nfunction mergeConfigs(defaultConfig, configs) {\n    if (!configs) {\n        return defaultConfig;\n    }\n    return Object.keys(defaultConfig).reduce(function (all, k) {\n        all[k] = mergeConfig(defaultConfig[k], configs[k]);\n        return all;\n    }, tslib_1.__assign({}, defaultConfig));\n}\nfunction createFastMemoizeCache(store) {\n    return {\n        create: function () {\n            return {\n                get: function (key) {\n                    return store[key];\n                },\n                set: function (key, value) {\n                    store[key] = value;\n                },\n            };\n        },\n    };\n}\nfunction createDefaultFormatters(cache) {\n    if (cache === void 0) { cache = {\n        number: {},\n        dateTime: {},\n        pluralRules: {},\n    }; }\n    return {\n        getNumberFormat: (0, fast_memoize_1.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.NumberFormat).bind.apply(_a, tslib_1.__spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.number),\n            strategy: fast_memoize_1.strategies.variadic,\n        }),\n        getDateTimeFormat: (0, fast_memoize_1.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.DateTimeFormat).bind.apply(_a, tslib_1.__spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.dateTime),\n            strategy: fast_memoize_1.strategies.variadic,\n        }),\n        getPluralRules: (0, fast_memoize_1.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.PluralRules).bind.apply(_a, tslib_1.__spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.pluralRules),\n            strategy: fast_memoize_1.strategies.variadic,\n        }),\n    };\n}\nvar IntlMessageFormat = /** @class */ (function () {\n    function IntlMessageFormat(message, locales, overrideFormats, opts) {\n        var _this = this;\n        if (locales === void 0) { locales = IntlMessageFormat.defaultLocale; }\n        this.formatterCache = {\n            number: {},\n            dateTime: {},\n            pluralRules: {},\n        };\n        this.format = function (values) {\n            var parts = _this.formatToParts(values);\n            // Hot path for straight simple msg translations\n            if (parts.length === 1) {\n                return parts[0].value;\n            }\n            var result = parts.reduce(function (all, part) {\n                if (!all.length ||\n                    part.type !== formatters_1.PART_TYPE.literal ||\n                    typeof all[all.length - 1] !== 'string') {\n                    all.push(part.value);\n                }\n                else {\n                    all[all.length - 1] += part.value;\n                }\n                return all;\n            }, []);\n            if (result.length <= 1) {\n                return result[0] || '';\n            }\n            return result;\n        };\n        this.formatToParts = function (values) {\n            return (0, formatters_1.formatToParts)(_this.ast, _this.locales, _this.formatters, _this.formats, values, undefined, _this.message);\n        };\n        this.resolvedOptions = function () {\n            var _a;\n            return ({\n                locale: ((_a = _this.resolvedLocale) === null || _a === void 0 ? void 0 : _a.toString()) ||\n                    Intl.NumberFormat.supportedLocalesOf(_this.locales)[0],\n            });\n        };\n        this.getAst = function () { return _this.ast; };\n        // Defined first because it's used to build the format pattern.\n        this.locales = locales;\n        this.resolvedLocale = IntlMessageFormat.resolveLocale(locales);\n        if (typeof message === 'string') {\n            this.message = message;\n            if (!IntlMessageFormat.__parse) {\n                throw new TypeError('IntlMessageFormat.__parse must be set to process `message` of type `string`');\n            }\n            var _a = opts || {}, formatters = _a.formatters, parseOpts = tslib_1.__rest(_a, [\"formatters\"]);\n            // Parse string messages into an AST.\n            this.ast = IntlMessageFormat.__parse(message, tslib_1.__assign(tslib_1.__assign({}, parseOpts), { locale: this.resolvedLocale }));\n        }\n        else {\n            this.ast = message;\n        }\n        if (!Array.isArray(this.ast)) {\n            throw new TypeError('A message must be provided as a String or AST.');\n        }\n        // Creates a new object with the specified `formats` merged with the default\n        // formats.\n        this.formats = mergeConfigs(IntlMessageFormat.formats, overrideFormats);\n        this.formatters =\n            (opts && opts.formatters) || createDefaultFormatters(this.formatterCache);\n    }\n    Object.defineProperty(IntlMessageFormat, \"defaultLocale\", {\n        get: function () {\n            if (!IntlMessageFormat.memoizedDefaultLocale) {\n                IntlMessageFormat.memoizedDefaultLocale =\n                    new Intl.NumberFormat().resolvedOptions().locale;\n            }\n            return IntlMessageFormat.memoizedDefaultLocale;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    IntlMessageFormat.memoizedDefaultLocale = null;\n    IntlMessageFormat.resolveLocale = function (locales) {\n        if (typeof Intl.Locale === 'undefined') {\n            return;\n        }\n        var supportedLocales = Intl.NumberFormat.supportedLocalesOf(locales);\n        if (supportedLocales.length > 0) {\n            return new Intl.Locale(supportedLocales[0]);\n        }\n        return new Intl.Locale(typeof locales === 'string' ? locales : locales[0]);\n    };\n    IntlMessageFormat.__parse = icu_messageformat_parser_1.parse;\n    // Default format options used as the prototype of the `formats` provided to the\n    // constructor. These are used when constructing the internal Intl.NumberFormat\n    // and Intl.DateTimeFormat instances.\n    IntlMessageFormat.formats = {\n        number: {\n            integer: {\n                maximumFractionDigits: 0,\n            },\n            currency: {\n                style: 'currency',\n            },\n            percent: {\n                style: 'percent',\n            },\n        },\n        date: {\n            short: {\n                month: 'numeric',\n                day: 'numeric',\n                year: '2-digit',\n            },\n            medium: {\n                month: 'short',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            long: {\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            full: {\n                weekday: 'long',\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n        },\n        time: {\n            short: {\n                hour: 'numeric',\n                minute: 'numeric',\n            },\n            medium: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n            },\n            long: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n            full: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n        },\n    };\n    return IntlMessageFormat;\n}());\nexports.IntlMessageFormat = IntlMessageFormat;\n", "\"use strict\";\n/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar tslib_1 = require(\"tslib\");\nvar core_1 = require(\"./src/core\");\ntslib_1.__exportStar(require(\"./src/formatters\"), exports);\ntslib_1.__exportStar(require(\"./src/core\"), exports);\ntslib_1.__exportStar(require(\"./src/error\"), exports);\nexports.default = core_1.IntlMessageFormat;\n", "import { writable, derived } from 'svelte/store';\nimport deepmerge from 'deepmerge';\nimport { IntlMessageFormat } from 'intl-messageformat';\n\nfunction delve(obj, fullKey) {\n  if (fullKey == null)\n    return void 0;\n  if (fullKey in obj) {\n    return obj[fullKey];\n  }\n  const keys = fullKey.split(\".\");\n  let result = obj;\n  for (let p = 0; p < keys.length; p++) {\n    if (typeof result === \"object\") {\n      if (p > 0) {\n        const partialKey = keys.slice(p, keys.length).join(\".\");\n        if (partialKey in result) {\n          result = result[partialKey];\n          break;\n        }\n      }\n      result = result[keys[p]];\n    } else {\n      result = void 0;\n    }\n  }\n  return result;\n}\n\nconst lookupCache = {};\nconst addToCache = (path, locale, message) => {\n  if (!message)\n    return message;\n  if (!(locale in lookupCache))\n    lookupCache[locale] = {};\n  if (!(path in lookupCache[locale]))\n    lookupCache[locale][path] = message;\n  return message;\n};\nconst lookup = (path, refLocale) => {\n  if (refLocale == null)\n    return void 0;\n  if (refLocale in lookupCache && path in lookupCache[refLocale]) {\n    return lookupCache[refLocale][path];\n  }\n  const locales = getPossibleLocales(refLocale);\n  for (let i = 0; i < locales.length; i++) {\n    const locale = locales[i];\n    const message = getMessageFromDictionary(locale, path);\n    if (message) {\n      return addToCache(path, refLocale, message);\n    }\n  }\n  return void 0;\n};\n\nlet dictionary;\nconst $dictionary = writable({});\nfunction getLocaleDictionary(locale) {\n  return dictionary[locale] || null;\n}\nfunction hasLocaleDictionary(locale) {\n  return locale in dictionary;\n}\nfunction getMessageFromDictionary(locale, id) {\n  if (!hasLocaleDictionary(locale)) {\n    return null;\n  }\n  const localeDictionary = getLocaleDictionary(locale);\n  const match = delve(localeDictionary, id);\n  return match;\n}\nfunction getClosestAvailableLocale(refLocale) {\n  if (refLocale == null)\n    return void 0;\n  const relatedLocales = getPossibleLocales(refLocale);\n  for (let i = 0; i < relatedLocales.length; i++) {\n    const locale = relatedLocales[i];\n    if (hasLocaleDictionary(locale)) {\n      return locale;\n    }\n  }\n  return void 0;\n}\nfunction addMessages(locale, ...partials) {\n  delete lookupCache[locale];\n  $dictionary.update((d) => {\n    d[locale] = deepmerge.all([d[locale] || {}, ...partials]);\n    return d;\n  });\n}\nconst $locales = derived(\n  [$dictionary],\n  ([dictionary2]) => Object.keys(dictionary2)\n);\n$dictionary.subscribe((newDictionary) => dictionary = newDictionary);\n\nconst queue = {};\nfunction createLocaleQueue(locale) {\n  queue[locale] = /* @__PURE__ */ new Set();\n}\nfunction removeLoaderFromQueue(locale, loader) {\n  queue[locale].delete(loader);\n  if (queue[locale].size === 0) {\n    delete queue[locale];\n  }\n}\nfunction getLocaleQueue(locale) {\n  return queue[locale];\n}\nfunction getLocalesQueues(locale) {\n  return getPossibleLocales(locale).map((localeItem) => {\n    const localeQueue = getLocaleQueue(localeItem);\n    return [localeItem, localeQueue ? [...localeQueue] : []];\n  }).filter(([, localeQueue]) => localeQueue.length > 0);\n}\nfunction hasLocaleQueue(locale) {\n  if (locale == null)\n    return false;\n  return getPossibleLocales(locale).some(\n    (localeQueue) => {\n      var _a;\n      return (_a = getLocaleQueue(localeQueue)) == null ? void 0 : _a.size;\n    }\n  );\n}\nfunction loadLocaleQueue(locale, localeQueue) {\n  const allLoadersPromise = Promise.all(\n    localeQueue.map((loader) => {\n      removeLoaderFromQueue(locale, loader);\n      return loader().then((partial) => partial.default || partial);\n    })\n  );\n  return allLoadersPromise.then((partials) => addMessages(locale, ...partials));\n}\nconst activeFlushes = {};\nfunction flush(locale) {\n  if (!hasLocaleQueue(locale)) {\n    if (locale in activeFlushes) {\n      return activeFlushes[locale];\n    }\n    return Promise.resolve();\n  }\n  const queues = getLocalesQueues(locale);\n  activeFlushes[locale] = Promise.all(\n    queues.map(\n      ([localeName, localeQueue]) => loadLocaleQueue(localeName, localeQueue)\n    )\n  ).then(() => {\n    if (hasLocaleQueue(locale)) {\n      return flush(locale);\n    }\n    delete activeFlushes[locale];\n  });\n  return activeFlushes[locale];\n}\nfunction registerLocaleLoader(locale, loader) {\n  if (!getLocaleQueue(locale))\n    createLocaleQueue(locale);\n  const localeQueue = getLocaleQueue(locale);\n  if (getLocaleQueue(locale).has(loader))\n    return;\n  if (!hasLocaleDictionary(locale)) {\n    $dictionary.update((d) => {\n      d[locale] = {};\n      return d;\n    });\n  }\n  localeQueue.add(loader);\n}\n\nvar __getOwnPropSymbols$2 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$2 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$2 = Object.prototype.propertyIsEnumerable;\nvar __objRest$1 = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp$2.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols$2)\n    for (var prop of __getOwnPropSymbols$2(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum$2.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nconst defaultFormats = {\n  number: {\n    scientific: { notation: \"scientific\" },\n    engineering: { notation: \"engineering\" },\n    compactLong: { notation: \"compact\", compactDisplay: \"long\" },\n    compactShort: { notation: \"compact\", compactDisplay: \"short\" }\n  },\n  date: {\n    short: { month: \"numeric\", day: \"numeric\", year: \"2-digit\" },\n    medium: { month: \"short\", day: \"numeric\", year: \"numeric\" },\n    long: { month: \"long\", day: \"numeric\", year: \"numeric\" },\n    full: { weekday: \"long\", month: \"long\", day: \"numeric\", year: \"numeric\" }\n  },\n  time: {\n    short: { hour: \"numeric\", minute: \"numeric\" },\n    medium: { hour: \"numeric\", minute: \"numeric\", second: \"numeric\" },\n    long: {\n      hour: \"numeric\",\n      minute: \"numeric\",\n      second: \"numeric\",\n      timeZoneName: \"short\"\n    },\n    full: {\n      hour: \"numeric\",\n      minute: \"numeric\",\n      second: \"numeric\",\n      timeZoneName: \"short\"\n    }\n  }\n};\nfunction defaultMissingKeyHandler({ locale, id }) {\n  console.warn(\n    `[svelte-i18n] The message \"${id}\" was not found in \"${getPossibleLocales(\n      locale\n    ).join('\", \"')}\".${hasLocaleQueue(getCurrentLocale()) ? `\n\nNote: there are at least one loader still registered to this locale that wasn't executed.` : \"\"}`\n  );\n}\nconst defaultOptions = {\n  fallbackLocale: null,\n  loadingDelay: 200,\n  formats: defaultFormats,\n  warnOnMissingMessages: true,\n  handleMissingMessage: void 0,\n  ignoreTag: true\n};\nconst options = defaultOptions;\nfunction getOptions() {\n  return options;\n}\nfunction init(opts) {\n  const _a = opts, { formats } = _a, rest = __objRest$1(_a, [\"formats\"]);\n  let initialLocale = opts.fallbackLocale;\n  if (opts.initialLocale) {\n    try {\n      if (IntlMessageFormat.resolveLocale(opts.initialLocale)) {\n        initialLocale = opts.initialLocale;\n      }\n    } catch (e) {\n      console.warn(\n        `[svelte-i18n] The initial locale \"${opts.initialLocale}\" is not a valid locale.`\n      );\n    }\n  }\n  if (rest.warnOnMissingMessages) {\n    delete rest.warnOnMissingMessages;\n    if (rest.handleMissingMessage == null) {\n      rest.handleMissingMessage = defaultMissingKeyHandler;\n    } else {\n      console.warn(\n        '[svelte-i18n] The \"warnOnMissingMessages\" option is deprecated. Please use the \"handleMissingMessage\" option instead.'\n      );\n    }\n  }\n  Object.assign(options, rest, { initialLocale });\n  if (formats) {\n    if (\"number\" in formats) {\n      Object.assign(options.formats.number, formats.number);\n    }\n    if (\"date\" in formats) {\n      Object.assign(options.formats.date, formats.date);\n    }\n    if (\"time\" in formats) {\n      Object.assign(options.formats.time, formats.time);\n    }\n  }\n  return $locale.set(initialLocale);\n}\n\nconst $isLoading = writable(false);\n\nvar __defProp$1 = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$1 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$1 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$1 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$1 = (obj, key, value) => key in obj ? __defProp$1(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$1 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$1.call(b, prop))\n      __defNormalProp$1(a, prop, b[prop]);\n  if (__getOwnPropSymbols$1)\n    for (var prop of __getOwnPropSymbols$1(b)) {\n      if (__propIsEnum$1.call(b, prop))\n        __defNormalProp$1(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nlet current;\nconst internalLocale = writable(null);\nfunction getSubLocales(refLocale) {\n  return refLocale.split(\"-\").map((_, i, arr) => arr.slice(0, i + 1).join(\"-\")).reverse();\n}\nfunction getPossibleLocales(refLocale, fallbackLocale = getOptions().fallbackLocale) {\n  const locales = getSubLocales(refLocale);\n  if (fallbackLocale) {\n    return [.../* @__PURE__ */ new Set([...locales, ...getSubLocales(fallbackLocale)])];\n  }\n  return locales;\n}\nfunction getCurrentLocale() {\n  return current != null ? current : void 0;\n}\ninternalLocale.subscribe((newLocale) => {\n  current = newLocale != null ? newLocale : void 0;\n  if (typeof window !== \"undefined\" && newLocale != null) {\n    document.documentElement.setAttribute(\"lang\", newLocale);\n  }\n});\nconst set = (newLocale) => {\n  if (newLocale && getClosestAvailableLocale(newLocale) && hasLocaleQueue(newLocale)) {\n    const { loadingDelay } = getOptions();\n    let loadingTimer;\n    if (typeof window !== \"undefined\" && getCurrentLocale() != null && loadingDelay) {\n      loadingTimer = window.setTimeout(\n        () => $isLoading.set(true),\n        loadingDelay\n      );\n    } else {\n      $isLoading.set(true);\n    }\n    return flush(newLocale).then(() => {\n      internalLocale.set(newLocale);\n    }).finally(() => {\n      clearTimeout(loadingTimer);\n      $isLoading.set(false);\n    });\n  }\n  return internalLocale.set(newLocale);\n};\nconst $locale = __spreadProps(__spreadValues$1({}, internalLocale), {\n  set\n});\n\nconst getFromQueryString = (queryString, key) => {\n  const keyVal = queryString.split(\"&\").find((i) => i.indexOf(`${key}=`) === 0);\n  if (keyVal) {\n    return keyVal.split(\"=\").pop();\n  }\n  return null;\n};\nconst getFirstMatch = (base, pattern) => {\n  const match = pattern.exec(base);\n  if (!match)\n    return null;\n  return match[1] || null;\n};\nconst getLocaleFromHostname = (hostname) => {\n  if (typeof window === \"undefined\")\n    return null;\n  return getFirstMatch(window.location.hostname, hostname);\n};\nconst getLocaleFromPathname = (pathname) => {\n  if (typeof window === \"undefined\")\n    return null;\n  return getFirstMatch(window.location.pathname, pathname);\n};\nconst getLocaleFromNavigator = () => {\n  if (typeof window === \"undefined\")\n    return null;\n  return window.navigator.language || window.navigator.languages[0];\n};\nconst getLocaleFromQueryString = (search) => {\n  if (typeof window === \"undefined\")\n    return null;\n  return getFromQueryString(window.location.search.substr(1), search);\n};\nconst getLocaleFromHash = (hash) => {\n  if (typeof window === \"undefined\")\n    return null;\n  return getFromQueryString(window.location.hash.substr(1), hash);\n};\n\nconst monadicMemoize = (fn) => {\n  const cache = /* @__PURE__ */ Object.create(null);\n  const memoizedFn = (arg) => {\n    const cacheKey = JSON.stringify(arg);\n    if (cacheKey in cache) {\n      return cache[cacheKey];\n    }\n    return cache[cacheKey] = fn(arg);\n  };\n  return memoizedFn;\n};\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nconst getIntlFormatterOptions = (type, name) => {\n  const { formats } = getOptions();\n  if (type in formats && name in formats[type]) {\n    return formats[type][name];\n  }\n  throw new Error(`[svelte-i18n] Unknown \"${name}\" ${type} format.`);\n};\nconst createNumberFormatter = monadicMemoize(\n  (_a) => {\n    var _b = _a, { locale, format } = _b, options = __objRest(_b, [\"locale\", \"format\"]);\n    if (locale == null) {\n      throw new Error('[svelte-i18n] A \"locale\" must be set to format numbers');\n    }\n    if (format) {\n      options = getIntlFormatterOptions(\"number\", format);\n    }\n    return new Intl.NumberFormat(locale, options);\n  }\n);\nconst createDateFormatter = monadicMemoize(\n  (_c) => {\n    var _d = _c, { locale, format } = _d, options = __objRest(_d, [\"locale\", \"format\"]);\n    if (locale == null) {\n      throw new Error('[svelte-i18n] A \"locale\" must be set to format dates');\n    }\n    if (format) {\n      options = getIntlFormatterOptions(\"date\", format);\n    } else if (Object.keys(options).length === 0) {\n      options = getIntlFormatterOptions(\"date\", \"short\");\n    }\n    return new Intl.DateTimeFormat(locale, options);\n  }\n);\nconst createTimeFormatter = monadicMemoize(\n  (_e) => {\n    var _f = _e, { locale, format } = _f, options = __objRest(_f, [\"locale\", \"format\"]);\n    if (locale == null) {\n      throw new Error(\n        '[svelte-i18n] A \"locale\" must be set to format time values'\n      );\n    }\n    if (format) {\n      options = getIntlFormatterOptions(\"time\", format);\n    } else if (Object.keys(options).length === 0) {\n      options = getIntlFormatterOptions(\"time\", \"short\");\n    }\n    return new Intl.DateTimeFormat(locale, options);\n  }\n);\nconst getNumberFormatter = (_g = {}) => {\n  var _h = _g, {\n    locale = getCurrentLocale()\n  } = _h, args = __objRest(_h, [\n    \"locale\"\n  ]);\n  return createNumberFormatter(__spreadValues({ locale }, args));\n};\nconst getDateFormatter = (_i = {}) => {\n  var _j = _i, {\n    locale = getCurrentLocale()\n  } = _j, args = __objRest(_j, [\n    \"locale\"\n  ]);\n  return createDateFormatter(__spreadValues({ locale }, args));\n};\nconst getTimeFormatter = (_k = {}) => {\n  var _l = _k, {\n    locale = getCurrentLocale()\n  } = _l, args = __objRest(_l, [\n    \"locale\"\n  ]);\n  return createTimeFormatter(__spreadValues({ locale }, args));\n};\nconst getMessageFormatter = monadicMemoize(\n  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n  (message, locale = getCurrentLocale()) => new IntlMessageFormat(message, locale, getOptions().formats, {\n    ignoreTag: getOptions().ignoreTag\n  })\n);\n\nconst formatMessage = (id, options = {}) => {\n  var _a, _b, _c, _d;\n  let messageObj = options;\n  if (typeof id === \"object\") {\n    messageObj = id;\n    id = messageObj.id;\n  }\n  const {\n    values,\n    locale = getCurrentLocale(),\n    default: defaultValue\n  } = messageObj;\n  if (locale == null) {\n    throw new Error(\n      \"[svelte-i18n] Cannot format a message without first setting the initial locale.\"\n    );\n  }\n  let message = lookup(id, locale);\n  if (!message) {\n    message = (_d = (_c = (_b = (_a = getOptions()).handleMissingMessage) == null ? void 0 : _b.call(_a, { locale, id, defaultValue })) != null ? _c : defaultValue) != null ? _d : id;\n  } else if (typeof message !== \"string\") {\n    console.warn(\n      `[svelte-i18n] Message with id \"${id}\" must be of type \"string\", found: \"${typeof message}\". Gettin its value through the \"$format\" method is deprecated; use the \"json\" method instead.`\n    );\n    return message;\n  }\n  if (!values) {\n    return message;\n  }\n  let result = message;\n  try {\n    result = getMessageFormatter(message, locale).format(values);\n  } catch (e) {\n    if (e instanceof Error) {\n      console.warn(\n        `[svelte-i18n] Message \"${id}\" has syntax error:`,\n        e.message\n      );\n    }\n  }\n  return result;\n};\nconst formatTime = (t, options) => {\n  return getTimeFormatter(options).format(t);\n};\nconst formatDate = (d, options) => {\n  return getDateFormatter(options).format(d);\n};\nconst formatNumber = (n, options) => {\n  return getNumberFormatter(options).format(n);\n};\nconst getJSON = (id, locale = getCurrentLocale()) => {\n  return lookup(id, locale);\n};\nconst $format = derived([$locale, $dictionary], () => formatMessage);\nconst $formatTime = derived([$locale], () => formatTime);\nconst $formatDate = derived([$locale], () => formatDate);\nconst $formatNumber = derived([$locale], () => formatNumber);\nconst $getJSON = derived([$locale, $dictionary], () => getJSON);\n\nfunction unwrapFunctionStore(store) {\n  let localReference;\n  const cancel = store.subscribe((value) => localReference = value);\n  const fn = (...args) => localReference(...args);\n  fn.freeze = cancel;\n  return fn;\n}\n\nfunction defineMessages(i) {\n  return i;\n}\nfunction waitLocale(locale) {\n  return flush(locale || getCurrentLocale() || getOptions().initialLocale);\n}\n\nexport { $format as _, addMessages, $formatDate as date, defineMessages, $dictionary as dictionary, $format as format, getDateFormatter, getLocaleFromHash, getLocaleFromHostname, getLocaleFromNavigator, getLocaleFromPathname, getLocaleFromQueryString, getMessageFormatter, getNumberFormatter, getTimeFormatter, init, $isLoading as isLoading, $getJSON as json, $locale as locale, $locales as locales, $formatNumber as number, registerLocaleLoader as register, $format as t, $formatTime as time, unwrapFunctionStore, waitLocale };\n", "<script lang=\"ts\">\n\timport space_logo from \"./images/spaces.svg\";\n\timport { _ } from \"svelte-i18n\";\n\texport let wrapper: HTMLDivElement;\n\texport let version: string;\n\texport let initial_height: string;\n\texport let is_embed: boolean;\n\n\texport let space: string | null;\n\texport let display: boolean;\n\texport let info: boolean;\n\texport let loaded: boolean;\n</script>\n\n<div\n\tbind:this={wrapper}\n\tclass:app={!display && !is_embed}\n\tclass:embed-container={display}\n\tclass:with-info={info}\n\tclass=\"gradio-container gradio-container-{version}\"\n\tstyle:min-height={loaded ? \"initial\" : initial_height}\n\tstyle:flex-grow={!display ? \"1\" : \"auto\"}\n\tdata-iframe-height\n>\n\t<div class=\"main\">\n\t\t<slot />\n\t</div>\n\t{#if display && space && info}\n\t\t<div class=\"info\">\n\t\t\t<span>\n\t\t\t\t<a href=\"https://huggingface.co/spaces/{space}\" class=\"title\">{space}</a\n\t\t\t\t>\n\t\t\t</span>\n\t\t\t<span>\n\t\t\t\t{$_(\"common.built_with\")}\n\t\t\t\t<a class=\"gradio\" href=\"https://gradio.app\">Gradio</a>.\n\t\t\t</span>\n\t\t\t<span>\n\t\t\t\t{$_(\"common.hosted_on\")}\n\t\t\t\t<a class=\"hf\" href=\"https://huggingface.co/spaces\"\n\t\t\t\t\t><span class=\"space-logo\">\n\t\t\t\t\t\t<img src={space_logo} alt=\"Hugging Face Space\" />\n\t\t\t\t\t</span> Spaces</a\n\t\t\t\t>\n\t\t\t</span>\n\t\t</div>\n\t{/if}\n</div>\n\n<style>\n\t.gradio-container {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-direction: column;\n\t\tpadding: 0;\n\t\tmin-height: 1px;\n\t\toverflow: hidden;\n\t\tcolor: var(--button-secondary-text-color);\n\t}\n\n\t.embed-container {\n\t\tmargin: var(--size-4) 0px;\n\t\tborder: 1px solid var(--button-secondary-border-color);\n\t\tborder-radius: var(--embed-radius);\n\t}\n\n\t.with-info {\n\t\tpadding-bottom: var(--size-7);\n\t}\n\n\t.embed-container > .main {\n\t\tpadding: var(--size-4);\n\t}\n\n\t.app > .main {\n\t\tdisplay: flex;\n\t\tflex-grow: 1;\n\t\tflex-direction: column;\n\t}\n\n\t.app {\n\t\tposition: relative;\n\t\tmargin: auto;\n\t\tpadding: var(--size-4) var(--size-8);\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t@media (--screen-sm) {\n\t\t.app {\n\t\t\tmax-width: 640px;\n\t\t}\n\t}\n\t@media (--screen-md) {\n\t\t.app {\n\t\t\tmax-width: 768px;\n\t\t}\n\t}\n\t@media (--screen-lg) {\n\t\t.app {\n\t\t\tmax-width: 1024px;\n\t\t}\n\t}\n\t@media (--screen-xl) {\n\t\t.app {\n\t\t\tmax-width: 1280px;\n\t\t}\n\t}\n\t@media (--screen-xxl) {\n\t\t.app {\n\t\t\tmax-width: 1536px;\n\t\t}\n\t}\n\n\t.info {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tjustify-content: flex-start;\n\t\tborder-top: 1px solid var(--button-secondary-border-color);\n\t\tpadding: var(--size-1) var(--size-5);\n\t\twidth: 100%;\n\t\tcolor: var(--body-text-color-subdued);\n\t\tfont-size: var(--text-md);\n\t\twhite-space: nowrap;\n\t}\n\n\t.info > span {\n\t\tword-wrap: break-word;\n\t\t-break: keep-all;\n\t\tdisplay: block;\n\t\tword-break: keep-all;\n\t}\n\n\t.info > span:nth-child(1) {\n\t\tmargin-right: 4px;\n\t\tmin-width: 0px;\n\t\tmax-width: max-content;\n\t\toverflow: hidden;\n\t\tcolor: var(--body-text-color);\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t}\n\n\t.info > span:nth-child(2) {\n\t\tmargin-right: 3px;\n\t}\n\n\t.info > span:nth-child(2),\n\t.info > span:nth-child(3) {\n\t\twidth: max-content;\n\t}\n\n\t.info > span:nth-child(3) {\n\t\talign-self: flex-end;\n\t\tjustify-self: flex-end;\n\t\tmargin-left: auto;\n\t\ttext-align: right;\n\t}\n\n\t.info > span:nth-child(1) {\n\t\tflex-shrink: 9;\n\t}\n\n\t.hidden-title {\n\t\tposition: absolute;\n\t\tleft: var(--size-5);\n\t\topacity: 0;\n\t\tbackground: var(--button-secondary-background-fill);\n\t\tpadding-right: 4px;\n\t}\n\n\t.info a {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.title {\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t}\n\n\t.hf {\n\t\tmargin-left: 5px;\n\t}\n\n\t.space-logo img {\n\t\tdisplay: inline-block;\n\t\tmargin-bottom: 4px;\n\t\theight: 12px;\n\t}\n\n\ta:hover {\n\t\ttext-decoration: underline;\n\t}\n</style>\n", "export function pretty_si(num: number): string {\n\tlet units = [\"\", \"k\", \"M\", \"G\", \"T\", \"P\", \"E\", \"Z\"];\n\tlet i = 0;\n\twhile (num > 1000 && i < units.length - 1) {\n\t\tnum /= 1000;\n\t\ti++;\n\t}\n\tlet unit = units[i];\n\treturn (Number.isInteger(num) ? num : num.toFixed(1)) + unit;\n}\n", "/**\n * @param {any} obj\n * @returns {boolean}\n */\nexport function is_date(obj) {\n\treturn Object.prototype.toString.call(obj) === '[object Date]';\n}\n", "import { writable } from '../store/index.js';\nimport { loop, now } from '../internal/index.js';\nimport { is_date } from './utils.js';\n\n/**\n * @template T\n * @param {import('./private.js').TickContext<T>} ctx\n * @param {T} last_value\n * @param {T} current_value\n * @param {T} target_value\n * @returns {T}\n */\nfunction tick_spring(ctx, last_value, current_value, target_value) {\n\tif (typeof current_value === 'number' || is_date(current_value)) {\n\t\t// @ts-ignore\n\t\tconst delta = target_value - current_value;\n\t\t// @ts-ignore\n\t\tconst velocity = (current_value - last_value) / (ctx.dt || 1 / 60); // guard div by 0\n\t\tconst spring = ctx.opts.stiffness * delta;\n\t\tconst damper = ctx.opts.damping * velocity;\n\t\tconst acceleration = (spring - damper) * ctx.inv_mass;\n\t\tconst d = (velocity + acceleration) * ctx.dt;\n\t\tif (Math.abs(d) < ctx.opts.precision && Math.abs(delta) < ctx.opts.precision) {\n\t\t\treturn target_value; // settled\n\t\t} else {\n\t\t\tctx.settled = false; // signal loop to keep ticking\n\t\t\t// @ts-ignore\n\t\t\treturn is_date(current_value) ? new Date(current_value.getTime() + d) : current_value + d;\n\t\t}\n\t} else if (Array.isArray(current_value)) {\n\t\t// @ts-ignore\n\t\treturn current_value.map((_, i) =>\n\t\t\ttick_spring(ctx, last_value[i], current_value[i], target_value[i])\n\t\t);\n\t} else if (typeof current_value === 'object') {\n\t\tconst next_value = {};\n\t\tfor (const k in current_value) {\n\t\t\t// @ts-ignore\n\t\t\tnext_value[k] = tick_spring(ctx, last_value[k], current_value[k], target_value[k]);\n\t\t}\n\t\t// @ts-ignore\n\t\treturn next_value;\n\t} else {\n\t\tthrow new Error(`Cannot spring ${typeof current_value} values`);\n\t}\n}\n\n/**\n * The spring function in Svelte creates a store whose value is animated, with a motion that simulates the behavior of a spring. This means when the value changes, instead of transitioning at a steady rate, it \"bounces\" like a spring would, depending on the physics parameters provided. This adds a level of realism to the transitions and can enhance the user experience.\n *\n * https://svelte.dev/docs/svelte-motion#spring\n * @template [T=any]\n * @param {T} [value]\n * @param {import('./private.js').SpringOpts} [opts]\n * @returns {import('./public.js').Spring<T>}\n */\nexport function spring(value, opts = {}) {\n\tconst store = writable(value);\n\tconst { stiffness = 0.15, damping = 0.8, precision = 0.01 } = opts;\n\t/** @type {number} */\n\tlet last_time;\n\t/** @type {import('../internal/private.js').Task} */\n\tlet task;\n\t/** @type {object} */\n\tlet current_token;\n\t/** @type {T} */\n\tlet last_value = value;\n\t/** @type {T} */\n\tlet target_value = value;\n\tlet inv_mass = 1;\n\tlet inv_mass_recovery_rate = 0;\n\tlet cancel_task = false;\n\t/**\n\t * @param {T} new_value\n\t * @param {import('./private.js').SpringUpdateOpts} opts\n\t * @returns {Promise<void>}\n\t */\n\tfunction set(new_value, opts = {}) {\n\t\ttarget_value = new_value;\n\t\tconst token = (current_token = {});\n\t\tif (value == null || opts.hard || (spring.stiffness >= 1 && spring.damping >= 1)) {\n\t\t\tcancel_task = true; // cancel any running animation\n\t\t\tlast_time = now();\n\t\t\tlast_value = new_value;\n\t\t\tstore.set((value = target_value));\n\t\t\treturn Promise.resolve();\n\t\t} else if (opts.soft) {\n\t\t\tconst rate = opts.soft === true ? 0.5 : +opts.soft;\n\t\t\tinv_mass_recovery_rate = 1 / (rate * 60);\n\t\t\tinv_mass = 0; // infinite mass, unaffected by spring forces\n\t\t}\n\t\tif (!task) {\n\t\t\tlast_time = now();\n\t\t\tcancel_task = false;\n\t\t\ttask = loop((now) => {\n\t\t\t\tif (cancel_task) {\n\t\t\t\t\tcancel_task = false;\n\t\t\t\t\ttask = null;\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tinv_mass = Math.min(inv_mass + inv_mass_recovery_rate, 1);\n\t\t\t\tconst ctx = {\n\t\t\t\t\tinv_mass,\n\t\t\t\t\topts: spring,\n\t\t\t\t\tsettled: true,\n\t\t\t\t\tdt: ((now - last_time) * 60) / 1000\n\t\t\t\t};\n\t\t\t\tconst next_value = tick_spring(ctx, last_value, value, target_value);\n\t\t\t\tlast_time = now;\n\t\t\t\tlast_value = value;\n\t\t\t\tstore.set((value = next_value));\n\t\t\t\tif (ctx.settled) {\n\t\t\t\t\ttask = null;\n\t\t\t\t}\n\t\t\t\treturn !ctx.settled;\n\t\t\t});\n\t\t}\n\t\treturn new Promise((fulfil) => {\n\t\t\ttask.promise.then(() => {\n\t\t\t\tif (token === current_token) fulfil();\n\t\t\t});\n\t\t});\n\t}\n\t/** @type {import('./public.js').Spring<T>} */\n\tconst spring = {\n\t\tset,\n\t\tupdate: (fn, opts) => set(fn(target_value, value), opts),\n\t\tsubscribe: store.subscribe,\n\t\tstiffness,\n\t\tdamping,\n\t\tprecision\n\t};\n\treturn spring;\n}\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport { spring } from \"svelte/motion\";\n\n\texport let margin = true;\n\n\tconst top = spring([0, 0]);\n\tconst bottom = spring([0, 0]);\n\n\tlet dismounted: boolean;\n\n\tasync function animate(): Promise<void> {\n\t\tawait Promise.all([top.set([125, 140]), bottom.set([-125, -140])]);\n\t\tawait Promise.all([top.set([-125, 140]), bottom.set([125, -140])]);\n\t\tawait Promise.all([top.set([-125, 0]), bottom.set([125, -0])]);\n\t\tawait Promise.all([top.set([125, 0]), bottom.set([-125, 0])]);\n\t}\n\n\tasync function run(): Promise<void> {\n\t\tawait animate();\n\t\tif (!dismounted) run();\n\t}\n\n\tasync function loading(): Promise<void> {\n\t\tawait Promise.all([top.set([125, 0]), bottom.set([-125, 0])]);\n\n\t\trun();\n\t}\n\n\tonMount(() => {\n\t\tloading();\n\t\treturn (): boolean => (dismounted = true);\n\t});\n</script>\n\n<div class:margin>\n\t<svg\n\t\tviewBox=\"-1200 -1200 3000 3000\"\n\t\tfill=\"none\"\n\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t>\n\t\t<g style=\"transform: translate({$top[0]}px, {$top[1]}px);\">\n\t\t\t<path\n\t\t\t\td=\"M255.926 0.754768L509.702 139.936V221.027L255.926 81.8465V0.754768Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t\tfill-opacity=\"0.4\"\n\t\t\t/>\n\t\t\t<path\n\t\t\t\td=\"M509.69 139.936L254.981 279.641V361.255L509.69 221.55V139.936Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t/>\n\t\t\t<path\n\t\t\t\td=\"M0.250138 139.937L254.981 279.641V361.255L0.250138 221.55V139.937Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t\tfill-opacity=\"0.4\"\n\t\t\t/>\n\t\t\t<path\n\t\t\t\td=\"M255.923 0.232622L0.236328 139.936V221.55L255.923 81.8469V0.232622Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t/>\n\t\t</g>\n\t\t<g style=\"transform: translate({$bottom[0]}px, {$bottom[1]}px);\">\n\t\t\t<path\n\t\t\t\td=\"M255.926 141.5L509.702 280.681V361.773L255.926 222.592V141.5Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t\tfill-opacity=\"0.4\"\n\t\t\t/>\n\t\t\t<path\n\t\t\t\td=\"M509.69 280.679L254.981 420.384V501.998L509.69 362.293V280.679Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t/>\n\t\t\t<path\n\t\t\t\td=\"M0.250138 280.681L254.981 420.386V502L0.250138 362.295V280.681Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t\tfill-opacity=\"0.4\"\n\t\t\t/>\n\t\t\t<path\n\t\t\t\td=\"M255.923 140.977L0.236328 280.68V362.294L255.923 222.591V140.977Z\"\n\t\t\t\tfill=\"#FF7C00\"\n\t\t\t/>\n\t\t</g>\n\t</svg>\n</div>\n\n<style>\n\tsvg {\n\t\twidth: var(--size-20);\n\t\theight: var(--size-20);\n\t}\n\n\tsvg path {\n\t\tfill: var(--loader-color);\n\t}\n\n\tdiv {\n\t\tz-index: var(--layer-2);\n\t}\n\n\t.margin {\n\t\tmargin: var(--size-4);\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\timport { tick } from \"svelte\";\n\timport { pretty_si } from \"./utils\";\n\n\tlet items: HTMLDivElement[] = [];\n\n\tlet called = false;\n\n\tasync function scroll_into_view(\n\t\tel: HTMLDivElement,\n\t\tenable: boolean | null = true\n\t): Promise<void> {\n\t\tif (\n\t\t\twindow.__gradio_mode__ === \"website\" ||\n\t\t\t(window.__gradio_mode__ !== \"app\" && enable !== true)\n\t\t) {\n\t\t\treturn;\n\t\t}\n\n\t\titems.push(el);\n\t\tif (!called) called = true;\n\t\telse return;\n\n\t\tawait tick();\n\n\t\trequestAnimationFrame(() => {\n\t\t\tlet min = [0, 0];\n\n\t\t\tfor (let i = 0; i < items.length; i++) {\n\t\t\t\tconst element = items[i];\n\n\t\t\t\tconst box = element.getBoundingClientRect();\n\t\t\t\tif (i === 0 || box.top + window.scrollY <= min[0]) {\n\t\t\t\t\tmin[0] = box.top + window.scrollY;\n\t\t\t\t\tmin[1] = i;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\twindow.scrollTo({ top: min[0] - 20, behavior: \"smooth\" });\n\n\t\t\tcalled = false;\n\t\t\titems = [];\n\t\t});\n\t}\n</script>\n\n<script lang=\"ts\">\n\timport { onDestroy } from \"svelte\";\n\n\timport Loader from \"./Loader.svelte\";\n\timport type { LoadingStatus } from \"./types\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\n\texport let i18n: I18nFormatter;\n\texport let eta: number | null = null;\n\texport let queue_position: number | null;\n\texport let queue_size: number | null;\n\texport let status: \"complete\" | \"pending\" | \"error\" | \"generating\";\n\texport let scroll_to_output = false;\n\texport let timer = true;\n\texport let show_progress: \"full\" | \"minimal\" | \"hidden\" = \"full\";\n\texport let message: string | null = null;\n\texport let progress: LoadingStatus[\"progress\"] | null | undefined = null;\n\texport let variant: \"default\" | \"center\" = \"default\";\n\texport let loading_text = \"Loading...\";\n\texport let absolute = true;\n\texport let translucent = false;\n\texport let border = false;\n\texport let autoscroll: boolean;\n\n\tlet el: HTMLDivElement;\n\n\tlet _timer = false;\n\tlet timer_start = 0;\n\tlet timer_diff = 0;\n\tlet old_eta: number | null = null;\n\tlet eta_from_start: number | null = null;\n\tlet message_visible = false;\n\tlet eta_level: number | null = 0;\n\tlet progress_level: (number | undefined)[] | null = null;\n\tlet last_progress_level: number | undefined = undefined;\n\tlet progress_bar: HTMLElement | null = null;\n\tlet show_eta_bar = true;\n\n\t$: eta_level =\n\t\teta_from_start === null || eta_from_start <= 0 || !timer_diff\n\t\t\t? null\n\t\t\t: Math.min(timer_diff / eta_from_start, 1);\n\t$: if (progress != null) {\n\t\tshow_eta_bar = false;\n\t}\n\n\t$: {\n\t\tif (progress != null) {\n\t\t\tprogress_level = progress.map((p) => {\n\t\t\t\tif (p.index != null && p.length != null) {\n\t\t\t\t\treturn p.index / p.length;\n\t\t\t\t} else if (p.progress != null) {\n\t\t\t\t\treturn p.progress;\n\t\t\t\t}\n\t\t\t\treturn undefined;\n\t\t\t});\n\t\t} else {\n\t\t\tprogress_level = null;\n\t\t}\n\n\t\tif (progress_level) {\n\t\t\tlast_progress_level = progress_level[progress_level.length - 1];\n\t\t\tif (progress_bar) {\n\t\t\t\tif (last_progress_level === 0) {\n\t\t\t\t\tprogress_bar.style.transition = \"0\";\n\t\t\t\t} else {\n\t\t\t\t\tprogress_bar.style.transition = \"150ms\";\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tlast_progress_level = undefined;\n\t\t}\n\t}\n\n\tconst start_timer = (): void => {\n\t\teta = old_eta = formatted_eta = null;\n\t\ttimer_start = performance.now();\n\t\ttimer_diff = 0;\n\t\t_timer = true;\n\t\trun();\n\t};\n\n\tfunction run(): void {\n\t\trequestAnimationFrame(() => {\n\t\t\ttimer_diff = (performance.now() - timer_start) / 1000;\n\t\t\tif (_timer) run();\n\t\t});\n\t}\n\n\tfunction stop_timer(): void {\n\t\ttimer_diff = 0;\n\t\teta = old_eta = formatted_eta = null;\n\n\t\tif (!_timer) return;\n\t\t_timer = false;\n\t}\n\n\tonDestroy(() => {\n\t\tif (_timer) stop_timer();\n\t});\n\n\t$: {\n\t\tif (status === \"pending\") {\n\t\t\tstart_timer();\n\t\t} else {\n\t\t\tstop_timer();\n\t\t}\n\t}\n\n\t$: el &&\n\t\tscroll_to_output &&\n\t\t(status === \"pending\" || status === \"complete\") &&\n\t\tscroll_into_view(el, autoscroll);\n\n\tlet formatted_eta: string | null = null;\n\t$: {\n\t\tif (eta === null) {\n\t\t\teta = old_eta;\n\t\t}\n\t\tif (eta != null && old_eta !== eta) {\n\t\t\teta_from_start = (performance.now() - timer_start) / 1000 + eta;\n\t\t\tformatted_eta = eta_from_start.toFixed(1);\n\t\t\told_eta = eta;\n\t\t}\n\t}\n\tlet show_message_timeout: NodeJS.Timeout | null = null;\n\tfunction close_message(): void {\n\t\tmessage_visible = false;\n\t\tif (show_message_timeout !== null) {\n\t\t\tclearTimeout(show_message_timeout);\n\t\t}\n\t}\n\t$: {\n\t\tclose_message();\n\t\tif (status === \"error\" && message) {\n\t\t\tmessage_visible = true;\n\t\t}\n\t}\n\t$: formatted_timer = timer_diff.toFixed(1);\n</script>\n\n<div\n\tclass=\"wrap {variant} {show_progress}\"\n\tclass:hide={!status || status === \"complete\" || show_progress === \"hidden\"}\n\tclass:translucent={(variant === \"center\" &&\n\t\t(status === \"pending\" || status === \"error\")) ||\n\t\ttranslucent ||\n\t\tshow_progress === \"minimal\"}\n\tclass:generating={status === \"generating\"}\n\tclass:border\n\tstyle:position={absolute ? \"absolute\" : \"static\"}\n\tstyle:padding={absolute ? \"0\" : \"var(--size-8) 0\"}\n\tbind:this={el}\n>\n\t{#if status === \"pending\"}\n\t\t{#if variant === \"default\" && show_eta_bar && show_progress === \"full\"}\n\t\t\t<div\n\t\t\t\tclass=\"eta-bar\"\n\t\t\t\tstyle:transform=\"translateX({(eta_level || 0) * 100 - 100}%)\"\n\t\t\t/>\n\t\t{/if}\n\t\t<div\n\t\t\tclass:meta-text-center={variant === \"center\"}\n\t\t\tclass:meta-text={variant === \"default\"}\n\t\t\tclass=\"progress-text\"\n\t\t>\n\t\t\t{#if progress}\n\t\t\t\t{#each progress as p}\n\t\t\t\t\t{#if p.index != null}\n\t\t\t\t\t\t{#if p.length != null}\n\t\t\t\t\t\t\t{pretty_si(p.index || 0)}/{pretty_si(p.length)}\n\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t{pretty_si(p.index || 0)}\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t{p.unit} | {\" \"}\n\t\t\t\t\t{/if}\n\t\t\t\t{/each}\n\t\t\t{:else if queue_position !== null && queue_size !== undefined && queue_position >= 0}\n\t\t\t\tqueue: {queue_position + 1}/{queue_size} |\n\t\t\t{:else if queue_position === 0}\n\t\t\t\tprocessing |\n\t\t\t{/if}\n\n\t\t\t{#if timer}\n\t\t\t\t{formatted_timer}{eta ? `/${formatted_eta}` : \"\"}s\n\t\t\t{/if}\n\t\t</div>\n\n\t\t{#if last_progress_level != null}\n\t\t\t<div class=\"progress-level\">\n\t\t\t\t<div class=\"progress-level-inner\">\n\t\t\t\t\t{#if progress != null}\n\t\t\t\t\t\t{#each progress as p, i}\n\t\t\t\t\t\t\t{#if p.desc != null || (progress_level && progress_level[i] != null)}\n\t\t\t\t\t\t\t\t{#if i !== 0}\n\t\t\t\t\t\t\t\t\t&nbsp;/\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t{#if p.desc != null}\n\t\t\t\t\t\t\t\t\t{p.desc}\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t{#if p.desc != null && progress_level && progress_level[i] != null}\n\t\t\t\t\t\t\t\t\t-\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t{#if progress_level != null}\n\t\t\t\t\t\t\t\t\t{(100 * (progress_level[i] || 0)).toFixed(1)}%\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t{/each}\n\t\t\t\t\t{/if}\n\t\t\t\t</div>\n\n\t\t\t\t<div class=\"progress-bar-wrap\">\n\t\t\t\t\t<div\n\t\t\t\t\t\tbind:this={progress_bar}\n\t\t\t\t\t\tclass=\"progress-bar\"\n\t\t\t\t\t\tstyle:width=\"{last_progress_level * 100}%\"\n\t\t\t\t\t/>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t{:else if show_progress === \"full\"}\n\t\t\t<Loader margin={variant === \"default\"} />\n\t\t{/if}\n\n\t\t{#if !timer}\n\t\t\t<p class=\"loading\">{loading_text}</p>\n\t\t{/if}\n\t{:else if status === \"error\"}\n\t\t<span class=\"error\">{i18n(\"common.error\")}</span>\n\t\t<slot name=\"error\" />\n\t{/if}\n</div>\n\n<style>\n\t.wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tz-index: var(--layer-top);\n\t\ttransition: opacity 0.1s ease-in-out;\n\t\tborder-radius: var(--block-radius);\n\t\tbackground: var(--block-background-fill);\n\t\tpadding: 0 var(--size-6);\n\t\tmax-height: var(--size-screen-h);\n\t\toverflow: hidden;\n\t\tpointer-events: none;\n\t}\n\n\t.wrap.center {\n\t\ttop: 0;\n\t\tright: 0px;\n\t\tleft: 0px;\n\t}\n\n\t.wrap.default {\n\t\ttop: 0px;\n\t\tright: 0px;\n\t\tbottom: 0px;\n\t\tleft: 0px;\n\t}\n\n\t.hide {\n\t\topacity: 0;\n\t\tpointer-events: none;\n\t}\n\n\t.generating {\n\t\tanimation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n\t\tborder: 2px solid var(--color-accent);\n\t\tbackground: transparent;\n\t\tz-index: var(--layer-1);\n\t}\n\n\t.translucent {\n\t\tbackground: none;\n\t}\n\n\t@keyframes pulse {\n\t\t0%,\n\t\t100% {\n\t\t\topacity: 1;\n\t\t}\n\t\t50% {\n\t\t\topacity: 0.5;\n\t\t}\n\t}\n\n\t.loading {\n\t\tz-index: var(--layer-2);\n\t\tcolor: var(--body-text-color);\n\t}\n\t.eta-bar {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\ttransform-origin: left;\n\t\topacity: 0.8;\n\t\tz-index: var(--layer-1);\n\t\ttransition: 10ms;\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\t.progress-bar-wrap {\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tbackground: var(--background-fill-primary);\n\t\twidth: 55.5%;\n\t\theight: var(--size-4);\n\t}\n\t.progress-bar {\n\t\ttransform-origin: left;\n\t\tbackground-color: var(--loader-color);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\t.progress-level {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tgap: 1;\n\t\tz-index: var(--layer-2);\n\t\twidth: var(--size-full);\n\t}\n\n\t.progress-level-inner {\n\t\tmargin: var(--size-2) auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t}\n\n\t.meta-text {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tz-index: var(--layer-2);\n\t\tpadding: var(--size-1) var(--size-2);\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t}\n\n\t.meta-text-center {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\ttransform: translateY(var(--size-6));\n\t\tz-index: var(--layer-2);\n\t\tpadding: var(--size-1) var(--size-2);\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t\ttext-align: center;\n\t}\n\n\t.error {\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: solid 1px var(--error-border-color);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--error-background-fill);\n\t\tpadding-right: var(--size-4);\n\t\tpadding-left: var(--size-4);\n\t\tcolor: var(--error-text-color);\n\t\tfont-weight: var(--weight-semibold);\n\t\tfont-size: var(--text-lg);\n\t\tline-height: var(--line-lg);\n\t\tfont-family: var(--font);\n\t}\n\n\t.minimal .progress-text {\n\t\tbackground: var(--block-background-fill);\n\t}\n\n\t.border {\n\t\tborder: 1px solid var(--border-color-primary);\n\t}\n</style>\n", "import { addMessages, init, getLocaleFromNavigator } from \"svelte-i18n\";\n\nconst langs = import.meta.glob(\"./lang/*.json\", {\n\teager: true\n});\n\ntype LangsRecord = Record<\n\tstring,\n\t{\n\t\t[key: string]: any;\n\t}\n>;\n\nexport function process_langs(): LangsRecord {\n\tlet _langs: LangsRecord = {};\n\n\tfor (const lang in langs) {\n\t\tconst code = (lang.split(\"/\").pop() as string).split(\".\").shift() as string;\n\t\t_langs[code] = (langs[lang] as Record<string, any>).default;\n\t}\n\n\treturn _langs;\n}\n\nconst processed_langs = process_langs();\n\nfor (const lang in processed_langs) {\n\taddMessages(lang, processed_langs[lang]);\n}\n\nexport async function setupi18n(): Promise<void> {\n\tawait init({\n\t\tfallbackLocale: \"en\",\n\t\tinitialLocale: getLocaleFromNavigator()\n\t});\n}\n", "import { setContext, getContext } from \"svelte\";\nimport type { WorkerProxy } from \"../dist\";\n\nconst WORKER_PROXY_CONTEXT_KEY = \"WORKER_PROXY_CONTEXT_KEY\";\n\nexport function setWorkerProxyContext(workerProxy: WorkerProxy): void {\n\tsetContext(WORKER_PROXY_CONTEXT_KEY, workerProxy);\n}\n\nexport function getWorkerProxyContext(): WorkerProxy | undefined {\n\treturn getContext(WORKER_PROXY_CONTEXT_KEY);\n}\n", "<script context=\"module\" lang=\"ts\">\n\timport { writable } from \"svelte/store\";\n\timport { mount_css as default_mount_css, prefix_css } from \"./css\";\n\n\timport type { ComponentMeta, Dependency, LayoutNode } from \"./types\";\n\n\tdeclare let BUILD_MODE: string;\n\tinterface Config {\n\t\tauth_required: boolean | undefined;\n\t\tauth_message: string;\n\t\tcomponents: ComponentMeta[];\n\t\tcss: string | null;\n\t\tjs: string | null;\n\t\thead: string | null;\n\t\tdependencies: Dependency[];\n\t\tdev_mode: boolean;\n\t\tenable_queue: boolean;\n\t\tlayout: LayoutNode;\n\t\tmode: \"blocks\" | \"interface\";\n\t\troot: string;\n\t\ttheme: string;\n\t\ttitle: string;\n\t\tversion: string;\n\t\tspace_id: string | null;\n\t\tis_colab: boolean;\n\t\tshow_api: boolean;\n\t\tstylesheets?: string[];\n\t\tpath: string;\n\t\tapp_id?: string;\n\t\tfill_height?: boolean;\n\t}\n\n\tlet id = -1;\n\n\tfunction create_intersection_store(): {\n\t\tregister: (n: number, el: HTMLDivElement) => void;\n\t\tsubscribe: (typeof intersecting)[\"subscribe\"];\n\t} {\n\t\tconst intersecting = writable<Record<string, boolean>>({});\n\n\t\tconst els = new Map<HTMLDivElement, number>();\n\n\t\tconst observer = new IntersectionObserver((entries) => {\n\t\t\tentries.forEach((entry) => {\n\t\t\t\tif (entry.isIntersecting) {\n\t\t\t\t\tlet _el: number | undefined = els.get(entry.target as HTMLDivElement);\n\t\t\t\t\tif (_el !== undefined)\n\t\t\t\t\t\tintersecting.update((s) => ({ ...s, [_el as number]: true }));\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\n\t\tfunction register(_id: number, el: HTMLDivElement): void {\n\t\t\tels.set(el, _id);\n\t\t\tobserver.observe(el);\n\t\t}\n\n\t\treturn { register, subscribe: intersecting.subscribe };\n\t}\n\n\tconst intersecting = create_intersection_store();\n</script>\n\n<script lang=\"ts\">\n\timport { onMount, setContext, createEventDispatcher } from \"svelte\";\n\timport type { api_factory, SpaceStatus } from \"@gradio/client\";\n\timport Embed from \"./Embed.svelte\";\n\timport type { ThemeMode } from \"./types\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport { _ } from \"svelte-i18n\";\n\timport { setupi18n } from \"./i18n\";\n\timport type { WorkerProxy } from \"@gradio/wasm\";\n\timport { setWorkerProxyContext } from \"@gradio/wasm/svelte\";\n\n\tsetupi18n();\n\n\tconst dispatch = createEventDispatcher();\n\n\texport let autoscroll: boolean;\n\texport let version: string;\n\texport let initial_height: string;\n\texport let app_mode: boolean;\n\texport let is_embed: boolean;\n\texport let theme_mode: ThemeMode | null = \"system\";\n\texport let control_page_title: boolean;\n\texport let container: boolean;\n\texport let info: boolean;\n\texport let eager: boolean;\n\tlet eventSource: EventSource;\n\n\t// These utilities are exported to be injectable for the Wasm version.\n\texport let mount_css: typeof default_mount_css = default_mount_css;\n\texport let client: ReturnType<typeof api_factory>[\"client\"];\n\texport let upload_files: ReturnType<typeof api_factory>[\"upload_files\"];\n\texport let worker_proxy: WorkerProxy | undefined = undefined;\n\tif (worker_proxy) {\n\t\tsetWorkerProxyContext(worker_proxy);\n\n\t\tworker_proxy.addEventListener(\"progress-update\", (event) => {\n\t\t\tloading_text = (event as CustomEvent).detail + \"...\";\n\t\t});\n\t}\n\texport let fetch_implementation: typeof fetch = fetch;\n\tsetContext(\"fetch_implementation\", fetch_implementation);\n\texport let EventSource_factory: (url: URL) => EventSource = (url) =>\n\t\tnew EventSource(url);\n\tsetContext(\"EventSource_factory\", EventSource_factory);\n\n\texport let space: string | null;\n\texport let host: string | null;\n\texport let src: string | null;\n\n\tlet _id = id++;\n\n\tlet loader_status: \"pending\" | \"error\" | \"complete\" | \"generating\" =\n\t\t\"pending\";\n\tlet app_id: string | null = null;\n\tlet wrapper: HTMLDivElement;\n\tlet ready = false;\n\tlet render_complete = false;\n\tlet config: Config;\n\tlet loading_text = $_(\"common.loading\") + \"...\";\n\tlet active_theme_mode: ThemeMode;\n\tlet api_url: string;\n\n\t$: if (config?.app_id) {\n\t\tapp_id = config.app_id;\n\t}\n\n\tlet css_text_stylesheet: HTMLStyleElement | null = null;\n\tasync function mount_custom_css(css_string: string | null): Promise<void> {\n\t\tif (css_string) {\n\t\t\tcss_text_stylesheet = prefix_css(\n\t\t\t\tcss_string,\n\t\t\t\tversion,\n\t\t\t\tcss_text_stylesheet || undefined\n\t\t\t);\n\t\t}\n\t\tawait mount_css(config.root + \"/theme.css\", document.head);\n\t\tif (!config.stylesheets) return;\n\n\t\tawait Promise.all(\n\t\t\tconfig.stylesheets.map((stylesheet) => {\n\t\t\t\tlet absolute_link =\n\t\t\t\t\tstylesheet.startsWith(\"http:\") || stylesheet.startsWith(\"https:\");\n\t\t\t\tif (absolute_link) {\n\t\t\t\t\treturn mount_css(stylesheet, document.head);\n\t\t\t\t}\n\n\t\t\t\treturn fetch(config.root + \"/\" + stylesheet)\n\t\t\t\t\t.then((response) => response.text())\n\t\t\t\t\t.then((css_string) => {\n\t\t\t\t\t\tprefix_css(css_string, version);\n\t\t\t\t\t});\n\t\t\t})\n\t\t);\n\t}\n\tasync function add_custom_html_head(\n\t\thead_string: string | null\n\t): Promise<void> {\n\t\tif (head_string) {\n\t\t\tconst parser = new DOMParser();\n\t\t\tconst parsed_head_html = Array.from(\n\t\t\t\tparser.parseFromString(head_string, \"text/html\").head.children\n\t\t\t);\n\n\t\t\tif (parsed_head_html) {\n\t\t\t\tfor (let head_element of parsed_head_html) {\n\t\t\t\t\tlet newElement = document.createElement(head_element.tagName);\n\t\t\t\t\tArray.from(head_element.attributes).forEach((attr) => {\n\t\t\t\t\t\tnewElement.setAttribute(attr.name, attr.value);\n\t\t\t\t\t});\n\t\t\t\t\tnewElement.textContent = head_element.textContent;\n\n\t\t\t\t\tif (\n\t\t\t\t\t\tnewElement.tagName == \"META\" &&\n\t\t\t\t\t\tnewElement.getAttribute(\"property\")\n\t\t\t\t\t) {\n\t\t\t\t\t\tconst domMetaList = Array.from(\n\t\t\t\t\t\t\tdocument.head.getElementsByTagName(\"meta\") ?? []\n\t\t\t\t\t\t);\n\t\t\t\t\t\tconst matched = domMetaList.find((el) => {\n\t\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t\tel.getAttribute(\"property\") ==\n\t\t\t\t\t\t\t\t\tnewElement.getAttribute(\"property\") &&\n\t\t\t\t\t\t\t\t!el.isEqualNode(newElement)\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t});\n\t\t\t\t\t\tif (matched) {\n\t\t\t\t\t\t\tdocument.head.replaceChild(newElement, matched);\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tdocument.head.appendChild(newElement);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction handle_theme_mode(target: HTMLDivElement): \"light\" | \"dark\" {\n\t\tconst force_light = window.__gradio_mode__ === \"website\";\n\n\t\tlet new_theme_mode: ThemeMode;\n\t\tif (force_light) {\n\t\t\tnew_theme_mode = \"light\";\n\t\t} else {\n\t\t\tconst url = new URL(window.location.toString());\n\t\t\tconst url_color_mode: ThemeMode | null = url.searchParams.get(\n\t\t\t\t\"__theme\"\n\t\t\t) as ThemeMode | null;\n\t\t\tnew_theme_mode = theme_mode || url_color_mode || \"system\";\n\t\t}\n\n\t\tif (new_theme_mode === \"dark\" || new_theme_mode === \"light\") {\n\t\t\tapply_theme(target, new_theme_mode);\n\t\t} else {\n\t\t\tnew_theme_mode = sync_system_theme(target);\n\t\t}\n\t\treturn new_theme_mode;\n\t}\n\n\tfunction sync_system_theme(target: HTMLDivElement): \"light\" | \"dark\" {\n\t\tconst theme = update_scheme();\n\t\twindow\n\t\t\t?.matchMedia(\"(prefers-color-scheme: dark)\")\n\t\t\t?.addEventListener(\"change\", update_scheme);\n\n\t\tfunction update_scheme(): \"light\" | \"dark\" {\n\t\t\tlet _theme: \"light\" | \"dark\" = window?.matchMedia?.(\n\t\t\t\t\"(prefers-color-scheme: dark)\"\n\t\t\t).matches\n\t\t\t\t? \"dark\"\n\t\t\t\t: \"light\";\n\n\t\t\tapply_theme(target, _theme);\n\t\t\treturn _theme;\n\t\t}\n\t\treturn theme;\n\t}\n\n\tfunction apply_theme(target: HTMLDivElement, theme: \"dark\" | \"light\"): void {\n\t\tconst dark_class_element = is_embed ? target.parentElement! : document.body;\n\t\tconst bg_element = is_embed ? target : target.parentElement!;\n\t\tbg_element.style.background = \"var(--body-background-fill)\";\n\t\tif (theme === \"dark\") {\n\t\t\tdark_class_element.classList.add(\"dark\");\n\t\t} else {\n\t\t\tdark_class_element.classList.remove(\"dark\");\n\t\t}\n\t}\n\n\tlet status: SpaceStatus = {\n\t\tmessage: \"\",\n\t\tload_status: \"pending\",\n\t\tstatus: \"sleeping\",\n\t\tdetail: \"SLEEPING\"\n\t};\n\n\tlet app: Awaited<ReturnType<typeof client>>;\n\tlet css_ready = false;\n\tfunction handle_status(_status: SpaceStatus): void {\n\t\tstatus = _status;\n\t}\n\tonMount(async () => {\n\t\tactive_theme_mode = handle_theme_mode(wrapper);\n\n\t\t//@ts-ignore\n\t\tconst gradio_dev_mode = window.__GRADIO_DEV__;\n\t\t//@ts-ignore\n\t\tconst server_port = window.__GRADIO__SERVER_PORT__;\n\n\t\tapi_url =\n\t\t\tBUILD_MODE === \"dev\" || gradio_dev_mode === \"dev\"\n\t\t\t\t? `http://localhost:${\n\t\t\t\t\t\ttypeof server_port === \"number\" ? server_port : 7860\n\t\t\t\t  }`\n\t\t\t\t: host || space || src || location.origin;\n\n\t\tapp = await client(api_url, {\n\t\t\tstatus_callback: handle_status\n\t\t});\n\t\tconfig = app.config;\n\t\twindow.__gradio_space__ = config.space_id;\n\n\t\tstatus = {\n\t\t\tmessage: \"\",\n\t\t\tload_status: \"complete\",\n\t\t\tstatus: \"running\",\n\t\t\tdetail: \"RUNNING\"\n\t\t};\n\n\t\tawait mount_custom_css(config.css);\n\t\tawait add_custom_html_head(config.head);\n\t\tcss_ready = true;\n\t\twindow.__is_colab__ = config.is_colab;\n\n\t\tdispatch(\"loaded\");\n\n\t\tif (config.dev_mode) {\n\t\t\tsetTimeout(() => {\n\t\t\t\tconst { host } = new URL(api_url);\n\t\t\t\tlet url = new URL(`http://${host}/dev/reload`);\n\t\t\t\teventSource = new EventSource(url);\n\t\t\t\teventSource.onmessage = async function (event) {\n\t\t\t\t\tif (event.data === \"CHANGE\") {\n\t\t\t\t\t\tapp = await client(api_url, {\n\t\t\t\t\t\t\tstatus_callback: handle_status\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tconfig = app.config;\n\t\t\t\t\t\twindow.__gradio_space__ = config.space_id;\n\t\t\t\t\t\tawait mount_custom_css(config.css);\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t}, 200);\n\t\t}\n\t});\n\n\tsetContext(\"upload_files\", upload_files);\n\n\t$: loader_status =\n\t\t!ready && status.load_status !== \"error\"\n\t\t\t? \"pending\"\n\t\t\t: !ready && status.load_status === \"error\"\n\t\t\t? \"error\"\n\t\t\t: status.load_status;\n\n\t$: config && (eager || $intersecting[_id]) && load_demo();\n\n\tlet Blocks: typeof import(\"./Blocks.svelte\").default;\n\tlet Login: typeof import(\"./Login.svelte\").default;\n\n\tasync function get_blocks(): Promise<void> {\n\t\tBlocks = (await import(\"./Blocks.svelte\")).default;\n\t}\n\tasync function get_login(): Promise<void> {\n\t\tLogin = (await import(\"./Login.svelte\")).default;\n\t}\n\n\tfunction load_demo(): void {\n\t\tif (config.auth_required) get_login();\n\t\telse get_blocks();\n\t}\n\n\ttype error_types =\n\t\t| \"NO_APP_FILE\"\n\t\t| \"CONFIG_ERROR\"\n\t\t| \"BUILD_ERROR\"\n\t\t| \"RUNTIME_ERROR\"\n\t\t| \"PAUSED\";\n\n\t// todo @hannahblair: translate these messages\n\tconst discussion_message = {\n\t\treadable_error: {\n\t\t\tNO_APP_FILE: $_(\"errors.no_app_file\"),\n\t\t\tCONFIG_ERROR: $_(\"errors.config_error\"),\n\t\t\tBUILD_ERROR: $_(\"errors.build_error\"),\n\t\t\tRUNTIME_ERROR: $_(\"errors.runtime_error\"),\n\t\t\tPAUSED: $_(\"errors.space_paused\")\n\t\t} as const,\n\t\ttitle(error: error_types): string {\n\t\t\treturn encodeURIComponent($_(\"errors.space_not_working\"));\n\t\t},\n\t\tdescription(error: error_types, site: string): string {\n\t\t\treturn encodeURIComponent(\n\t\t\t\t`Hello,\\n\\nFirstly, thanks for creating this space!\\n\\nI noticed that the space isn't working correctly because there is ${\n\t\t\t\t\tthis.readable_error[error] || \"an error\"\n\t\t\t\t}.\\n\\nIt would be great if you could take a look at this because this space is being embedded on ${site}.\\n\\nThanks!`\n\t\t\t);\n\t\t}\n\t};\n\n\tonMount(async () => {\n\t\tintersecting.register(_id, wrapper);\n\t});\n\n\t$: if (render_complete) {\n\t\twrapper.dispatchEvent(\n\t\t\tnew CustomEvent(\"render\", {\n\t\t\t\tbubbles: true,\n\t\t\t\tcancelable: false,\n\t\t\t\tcomposed: true\n\t\t\t})\n\t\t);\n\t}\n</script>\n\n<Embed\n\tdisplay={container && is_embed}\n\t{is_embed}\n\tinfo={!!space && info}\n\t{version}\n\t{initial_height}\n\t{space}\n\tloaded={loader_status === \"complete\"}\n\tbind:wrapper\n>\n\t{#if (loader_status === \"pending\" || loader_status === \"error\") && !(config && config?.auth_required)}\n\t\t<StatusTracker\n\t\t\tabsolute={!is_embed}\n\t\t\tstatus={loader_status}\n\t\t\ttimer={false}\n\t\t\tqueue_position={null}\n\t\t\tqueue_size={null}\n\t\t\ttranslucent={true}\n\t\t\t{loading_text}\n\t\t\ti18n={$_}\n\t\t\t{autoscroll}\n\t\t>\n\t\t\t<!-- todo: translate message text -->\n\t\t\t<div class=\"error\" slot=\"error\">\n\t\t\t\t<p><strong>{status?.message || \"\"}</strong></p>\n\t\t\t\t{#if (status.status === \"space_error\" || status.status === \"paused\") && status.discussions_enabled}\n\t\t\t\t\t<p>\n\t\t\t\t\t\tPlease <a\n\t\t\t\t\t\t\thref=\"https://huggingface.co/spaces/{space}/discussions/new?title={discussion_message.title(\n\t\t\t\t\t\t\t\tstatus?.detail\n\t\t\t\t\t\t\t)}&description={discussion_message.description(\n\t\t\t\t\t\t\t\tstatus?.detail,\n\t\t\t\t\t\t\t\tlocation.origin\n\t\t\t\t\t\t\t)}\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\tcontact the author of the space</a\n\t\t\t\t\t\t> to let them know.\n\t\t\t\t\t</p>\n\t\t\t\t{:else}\n\t\t\t\t\t<p>{$_(\"errors.contact_page_author\")}</p>\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t</StatusTracker>\n\t{/if}\n\t{#if config?.auth_required && Login}\n\t\t<Login\n\t\t\tauth_message={config.auth_message}\n\t\t\troot={config.root}\n\t\t\tspace_id={space}\n\t\t\t{app_mode}\n\t\t/>\n\t{:else if config && Blocks && css_ready}\n\t\t<Blocks\n\t\t\t{app}\n\t\t\t{...config}\n\t\t\tfill_height={!is_embed && config.fill_height}\n\t\t\ttheme_mode={active_theme_mode}\n\t\t\t{control_page_title}\n\t\t\ttarget={wrapper}\n\t\t\t{autoscroll}\n\t\t\tbind:ready\n\t\t\tbind:render_complete\n\t\t\tshow_footer={!is_embed}\n\t\t\t{app_mode}\n\t\t\t{version}\n\t\t/>\n\t{/if}\n</Embed>\n\n<style>\n\t.error {\n\t\tposition: relative;\n\t\tpadding: var(--size-4);\n\t\tcolor: var(--body-text-color);\n\t\ttext-align: center;\n\t}\n\n\t.error > * {\n\t\tmargin-top: var(--size-4);\n\t}\n\n\ta {\n\t\tcolor: var(--link-text-color);\n\t}\n\n\ta:hover {\n\t\tcolor: var(--link-text-color-hover);\n\t\ttext-decoration: underline;\n\t}\n\n\ta:visited {\n\t\tcolor: var(--link-text-color-visited);\n\t}\n\n\ta:active {\n\t\tcolor: var(--link-text-color-active);\n\t}\n</style>\n"], "file": "assets/Index-26cfc80a.js"}