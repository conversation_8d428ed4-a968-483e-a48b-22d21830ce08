const{SvelteComponent:d,attr:o,create_slot:g,detach:v,element:r,get_all_dirty_from_scope:q,get_slot_changes:b,init:j,insert:w,null_to_empty:h,safe_not_equal:I,toggle_class:u,transition_in:C,transition_out:S,update_slot_base:k}=window.__gradio__svelte__internal;function z(s){let e,_,t;const f=s[6].default,i=g(f,s,s[5],null);return{c(){e=r("div"),i&&i.c(),o(e,"id",s[1]),o(e,"class",_=h(s[2].join(" "))+" svelte-15lo0d8"),u(e,"compact",s[4]==="compact"),u(e,"panel",s[4]==="panel"),u(e,"unequal-height",s[0]===!1),u(e,"stretch",s[0]),u(e,"hide",!s[3])},m(l,n){w(l,e,n),i&&i.m(e,null),t=!0},p(l,[n]){i&&i.p&&(!t||n&32)&&k(i,f,l,l[5],t?b(f,l[5],n,null):q(l[5]),null),(!t||n&2)&&o(e,"id",l[1]),(!t||n&4&&_!==(_=h(l[2].join(" "))+" svelte-15lo0d8"))&&o(e,"class",_),(!t||n&20)&&u(e,"compact",l[4]==="compact"),(!t||n&20)&&u(e,"panel",l[4]==="panel"),(!t||n&5)&&u(e,"unequal-height",l[0]===!1),(!t||n&5)&&u(e,"stretch",l[0]),(!t||n&12)&&u(e,"hide",!l[3])},i(l){t||(C(i,l),t=!0)},o(l){S(i,l),t=!1},d(l){l&&v(e),i&&i.d(l)}}}function A(s,e,_){let{$$slots:t={},$$scope:f}=e,{equal_height:i=!0}=e,{elem_id:l}=e,{elem_classes:n=[]}=e,{visible:m=!0}=e,{variant:c="default"}=e;return s.$$set=a=>{"equal_height"in a&&_(0,i=a.equal_height),"elem_id"in a&&_(1,l=a.elem_id),"elem_classes"in a&&_(2,n=a.elem_classes),"visible"in a&&_(3,m=a.visible),"variant"in a&&_(4,c=a.variant),"$$scope"in a&&_(5,f=a.$$scope)},[i,l,n,m,c,f,t]}class B extends d{constructor(e){super(),j(this,e,A,z,I,{equal_height:0,elem_id:1,elem_classes:2,visible:3,variant:4})}}export{B as default};
//# sourceMappingURL=Index-0a208ea4.js.map
