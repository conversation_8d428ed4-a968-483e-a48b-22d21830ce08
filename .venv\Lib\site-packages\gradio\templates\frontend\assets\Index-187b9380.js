import{B as Ye}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{S as Ze}from"./Index-26cfc80a.js";import{U as xe}from"./UploadText-39c67ae9.js";import{B as $e}from"./BlockLabel-f27805b1.js";import{I as el}from"./IconButton-7294c90b.js";import{E as ll}from"./Empty-28f63bf0.js";import{S as tl}from"./ShareButton-7dae44e7.js";import{a as nl}from"./DownloadLink-7ff36416.js";import{I as Re}from"./Image-eaba773f.js";import"./index-a80d931b.js";import"./ModifyUpload.svelte_svelte_type_style_lang-4ba51100.js";import{M as Ce}from"./ModifyUpload-66b0c302.js";import{I as me}from"./Image-21c02477.js";import{u as il}from"./utils-572af92b.js";import{B as ol}from"./FileUpload-1878093d.js";/* empty css                                              */import"./Upload-351cc897.js";import"./file-url-bef2dc1b.js";import"./svelte/svelte.js";import"./Clear-2c7bae91.js";import"./Undo-b088de14.js";import"./File-d0b52941.js";var he=Object.prototype.hasOwnProperty;function ge(t,e,l){for(l of t.keys())if(x(l,e))return l}function x(t,e){var l,n,i;if(t===e)return!0;if(t&&e&&(l=t.constructor)===e.constructor){if(l===Date)return t.getTime()===e.getTime();if(l===RegExp)return t.toString()===e.toString();if(l===Array){if((n=t.length)===e.length)for(;n--&&x(t[n],e[n]););return n===-1}if(l===Set){if(t.size!==e.size)return!1;for(n of t)if(i=n,i&&typeof i=="object"&&(i=ge(e,i),!i)||!e.has(i))return!1;return!0}if(l===Map){if(t.size!==e.size)return!1;for(n of t)if(i=n[0],i&&typeof i=="object"&&(i=ge(e,i),!i)||!x(n[1],e.get(i)))return!1;return!0}if(l===ArrayBuffer)t=new Uint8Array(t),e=new Uint8Array(e);else if(l===DataView){if((n=t.byteLength)===e.byteLength)for(;n--&&t.getInt8(n)===e.getInt8(n););return n===-1}if(ArrayBuffer.isView(t)){if((n=t.byteLength)===e.byteLength)for(;n--&&t[n]===e[n];);return n===-1}if(!l||typeof t=="object"){n=0;for(l in t)if(he.call(t,l)&&++n&&!he.call(e,l)||!(l in e)||!x(t[l],e[l]))return!1;return Object.keys(e).length===n}}return t!==t&&e!==e}async function al(t){return t?`<div style="display: flex; flex-wrap: wrap; gap: 16px">${(await Promise.all(t.map(async([l,n])=>l===null||!l.url?"":await il(l.url,"url")))).map(l=>`<img src="${l}" style="height: 400px" />`).join("")}</div>`:""}const{SvelteComponent:rl,add_iframe_resize_listener:fl,add_render_callback:Ge,append:T,attr:A,binding_callbacks:de,bubble:be,check_outros:V,create_component:q,destroy_component:N,destroy_each:Oe,detach:U,element:S,empty:ul,ensure_array_like:re,globals:sl,group_outros:X,init:_l,insert:D,listen:$,mount_component:H,run_all:cl,safe_not_equal:ml,set_data:qe,set_style:O,space:E,text:Ne,toggle_class:W,transition_in:v,transition_out:j}=window.__gradio__svelte__internal,{window:He}=sl,{createEventDispatcher:hl,getContext:gl}=window.__gradio__svelte__internal,{tick:dl}=window.__gradio__svelte__internal;function we(t,e,l){const n=t.slice();return n[43]=e[l],n[45]=l,n}function pe(t,e,l){const n=t.slice();return n[46]=e[l],n[47]=e,n[45]=l,n}function ke(t){let e,l;return e=new $e({props:{show_label:t[2],Icon:Re,label:t[3]||"Gallery"}}),{c(){q(e.$$.fragment)},m(n,i){H(e,n,i),l=!0},p(n,i){const r={};i[0]&4&&(r.show_label=n[2]),i[0]&8&&(r.label=n[3]||"Gallery"),e.$set(r)},i(n){l||(v(e.$$.fragment,n),l=!0)},o(n){j(e.$$.fragment,n),l=!1},d(n){N(e,n)}}}function bl(t){let e,l,n,i,r,m,b,a=t[18]&&t[7]&&ve(t),s=t[12]&&Be(t),u=t[9]&&Ae(t),d=re(t[13]),o=[];for(let f=0;f<d.length;f+=1)o[f]=Le(we(t,d,f));const g=f=>j(o[f],1,1,()=>{o[f]=null});return{c(){a&&a.c(),e=E(),l=S("div"),n=S("div"),s&&s.c(),i=E(),u&&u.c(),r=E();for(let f=0;f<o.length;f+=1)o[f].c();A(n,"class","grid-container svelte-hpz95u"),O(n,"--grid-cols",t[4]),O(n,"--grid-rows",t[5]),O(n,"--object-fit",t[8]),O(n,"height",t[6]),W(n,"pt-6",t[2]),A(l,"class","grid-wrap svelte-hpz95u"),Ge(()=>t[37].call(l)),W(l,"fixed-height",!t[6]||t[6]=="auto")},m(f,h){a&&a.m(f,h),D(f,e,h),D(f,l,h),T(l,n),s&&s.m(n,null),T(n,i),u&&u.m(n,null),T(n,r);for(let p=0;p<o.length;p+=1)o[p]&&o[p].m(n,null);m=fl(l,t[37].bind(l)),b=!0},p(f,h){if(f[18]&&f[7]?a?(a.p(f,h),h[0]&262272&&v(a,1)):(a=ve(f),a.c(),v(a,1),a.m(e.parentNode,e)):a&&(X(),j(a,1,1,()=>{a=null}),V()),f[12]?s?(s.p(f,h),h[0]&4096&&v(s,1)):(s=Be(f),s.c(),v(s,1),s.m(n,i)):s&&(X(),j(s,1,1,()=>{s=null}),V()),f[9]?u?(u.p(f,h),h[0]&512&&v(u,1)):(u=Ae(f),u.c(),v(u,1),u.m(n,r)):u&&(X(),j(u,1,1,()=>{u=null}),V()),h[0]&8194){d=re(f[13]);let p;for(p=0;p<d.length;p+=1){const z=we(f,d,p);o[p]?(o[p].p(z,h),v(o[p],1)):(o[p]=Le(z),o[p].c(),v(o[p],1),o[p].m(n,null))}for(X(),p=d.length;p<o.length;p+=1)g(p);V()}(!b||h[0]&16)&&O(n,"--grid-cols",f[4]),(!b||h[0]&32)&&O(n,"--grid-rows",f[5]),(!b||h[0]&256)&&O(n,"--object-fit",f[8]),(!b||h[0]&64)&&O(n,"height",f[6]),(!b||h[0]&4)&&W(n,"pt-6",f[2]),(!b||h[0]&64)&&W(l,"fixed-height",!f[6]||f[6]=="auto")},i(f){if(!b){v(a),v(s),v(u);for(let h=0;h<d.length;h+=1)v(o[h]);b=!0}},o(f){j(a),j(s),j(u),o=o.filter(Boolean);for(let h=0;h<o.length;h+=1)j(o[h]);b=!1},d(f){f&&(U(e),U(l)),a&&a.d(f),s&&s.d(),u&&u.d(),Oe(o,f),m()}}}function wl(t){let e,l;return e=new ll({props:{unpadded_box:!0,size:"large",$$slots:{default:[pl]},$$scope:{ctx:t}}}),{c(){q(e.$$.fragment)},m(n,i){H(e,n,i),l=!0},p(n,i){const r={};i[1]&131072&&(r.$$scope={dirty:i,ctx:n}),e.$set(r)},i(n){l||(v(e.$$.fragment,n),l=!0)},o(n){j(e.$$.fragment,n),l=!1},d(n){N(e,n)}}}function ve(t){let e,l,n,i,r,m,b,a,s,u,d,o,g,f=t[10]&&ze(t);i=new Ce({props:{i18n:t[11],absolute:!1}}),i.$on("clear",t[28]),b=new me({props:{"data-testid":"detailed-image",src:t[18].image.url,alt:t[18].caption||"",title:t[18].caption||null,class:t[18].caption&&"with-caption",loading:"lazy"}});let h=t[18]?.caption&&ye(t),p=re(t[13]),z=[];for(let w=0;w<p.length;w+=1)z[w]=je(pe(t,p,w));const k=w=>j(z[w],1,1,()=>{z[w]=null});return{c(){e=S("button"),l=S("div"),f&&f.c(),n=E(),q(i.$$.fragment),r=E(),m=S("button"),q(b.$$.fragment),a=E(),h&&h.c(),s=E(),u=S("div");for(let w=0;w<z.length;w+=1)z[w].c();A(l,"class","icon-buttons svelte-hpz95u"),A(m,"class","image-button svelte-hpz95u"),O(m,"height","calc(100% - "+(t[18].caption?"80px":"60px")+")"),A(m,"aria-label","detailed view of selected image"),A(u,"class","thumbnails scroll-hide svelte-hpz95u"),A(u,"data-testid","container_el"),A(e,"class","preview svelte-hpz95u")},m(w,y){D(w,e,y),T(e,l),f&&f.m(l,null),T(l,n),H(i,l,null),T(e,r),T(e,m),H(b,m,null),T(e,a),h&&h.m(e,null),T(e,s),T(e,u);for(let L=0;L<z.length;L+=1)z[L]&&z[L].m(u,null);t[32](u),d=!0,o||(g=[$(m,"click",t[29]),$(e,"keydown",t[20])],o=!0)},p(w,y){w[10]?f?(f.p(w,y),y[0]&1024&&v(f,1)):(f=ze(w),f.c(),v(f,1),f.m(l,n)):f&&(X(),j(f,1,1,()=>{f=null}),V());const L={};y[0]&2048&&(L.i18n=w[11]),i.$set(L);const I={};if(y[0]&262144&&(I.src=w[18].image.url),y[0]&262144&&(I.alt=w[18].caption||""),y[0]&262144&&(I.title=w[18].caption||null),y[0]&262144&&(I.class=w[18].caption&&"with-caption"),b.$set(I),(!d||y[0]&262144)&&O(m,"height","calc(100% - "+(w[18].caption?"80px":"60px")+")"),w[18]?.caption?h?h.p(w,y):(h=ye(w),h.c(),h.m(e,s)):h&&(h.d(1),h=null),y[0]&24578){p=re(w[13]);let B;for(B=0;B<p.length;B+=1){const M=pe(w,p,B);z[B]?(z[B].p(M,y),v(z[B],1)):(z[B]=je(M),z[B].c(),v(z[B],1),z[B].m(u,null))}for(X(),B=p.length;B<z.length;B+=1)k(B);V()}},i(w){if(!d){v(f),v(i.$$.fragment,w),v(b.$$.fragment,w);for(let y=0;y<p.length;y+=1)v(z[y]);d=!0}},o(w){j(f),j(i.$$.fragment,w),j(b.$$.fragment,w),z=z.filter(Boolean);for(let y=0;y<z.length;y+=1)j(z[y]);d=!1},d(w){w&&U(e),f&&f.d(),N(i),N(b),h&&h.d(),Oe(z,w),t[32](null),o=!1,cl(g)}}}function ze(t){let e,l,n;return l=new el({props:{Icon:nl,label:t[11]("common.download")}}),l.$on("click",t[27]),{c(){e=S("div"),q(l.$$.fragment),A(e,"class","download-button-container svelte-hpz95u")},m(i,r){D(i,e,r),H(l,e,null),n=!0},p(i,r){const m={};r[0]&2048&&(m.label=i[11]("common.download")),l.$set(m)},i(i){n||(v(l.$$.fragment,i),n=!0)},o(i){j(l.$$.fragment,i),n=!1},d(i){i&&U(e),N(l)}}}function ye(t){let e,l=t[18].caption+"",n;return{c(){e=S("caption"),n=Ne(l),A(e,"class","caption svelte-hpz95u")},m(i,r){D(i,e,r),T(e,n)},p(i,r){r[0]&262144&&l!==(l=i[18].caption+"")&&qe(n,l)},d(i){i&&U(e)}}}function je(t){let e,l,n,i,r=t[45],m,b,a;l=new me({props:{src:t[46].image.url,title:t[46].caption||null,"data-testid":"thumbnail "+(t[45]+1),alt:"",loading:"lazy"}});const s=()=>t[30](e,r),u=()=>t[30](null,r);function d(){return t[31](t[45])}return{c(){e=S("button"),q(l.$$.fragment),n=E(),A(e,"class","thumbnail-item thumbnail-small svelte-hpz95u"),A(e,"aria-label",i="Thumbnail "+(t[45]+1)+" of "+t[13].length),W(e,"selected",t[1]===t[45])},m(o,g){D(o,e,g),H(l,e,null),T(e,n),s(),m=!0,b||(a=$(e,"click",d),b=!0)},p(o,g){t=o;const f={};g[0]&8192&&(f.src=t[46].image.url),g[0]&8192&&(f.title=t[46].caption||null),l.$set(f),(!m||g[0]&8192&&i!==(i="Thumbnail "+(t[45]+1)+" of "+t[13].length))&&A(e,"aria-label",i),r!==t[45]&&(u(),r=t[45],s()),(!m||g[0]&2)&&W(e,"selected",t[1]===t[45])},i(o){m||(v(l.$$.fragment,o),m=!0)},o(o){j(l.$$.fragment,o),m=!1},d(o){o&&U(e),N(l),u(),b=!1,a()}}}function Be(t){let e,l,n;return l=new Ce({props:{i18n:t[11],absolute:!1}}),l.$on("clear",t[33]),{c(){e=S("div"),q(l.$$.fragment),A(e,"class","icon-button svelte-hpz95u")},m(i,r){D(i,e,r),H(l,e,null),n=!0},p(i,r){const m={};r[0]&2048&&(m.i18n=i[11]),l.$set(m)},i(i){n||(v(l.$$.fragment,i),n=!0)},o(i){j(l.$$.fragment,i),n=!1},d(i){i&&U(e),N(l)}}}function Ae(t){let e,l,n;return l=new tl({props:{i18n:t[11],value:t[13],formatter:al}}),l.$on("share",t[34]),l.$on("error",t[35]),{c(){e=S("div"),q(l.$$.fragment),A(e,"class","icon-button svelte-hpz95u")},m(i,r){D(i,e,r),H(l,e,null),n=!0},p(i,r){const m={};r[0]&2048&&(m.i18n=i[11]),r[0]&8192&&(m.value=i[13]),l.$set(m)},i(i){n||(v(l.$$.fragment,i),n=!0)},o(i){j(l.$$.fragment,i),n=!1},d(i){i&&U(e),N(l)}}}function Ie(t){let e,l=t[43].caption+"",n;return{c(){e=S("div"),n=Ne(l),A(e,"class","caption-label svelte-hpz95u")},m(i,r){D(i,e,r),T(e,n)},p(i,r){r[0]&8192&&l!==(l=i[43].caption+"")&&qe(n,l)},d(i){i&&U(e)}}}function Le(t){let e,l,n,i,r,m,b,a;l=new me({props:{alt:t[43].caption||"",src:typeof t[43].image=="string"?t[43].image:t[43].image.url,loading:"lazy"}});let s=t[43].caption&&Ie(t);function u(){return t[36](t[45])}return{c(){e=S("button"),q(l.$$.fragment),n=E(),s&&s.c(),i=E(),A(e,"class","thumbnail-item thumbnail-lg svelte-hpz95u"),A(e,"aria-label",r="Thumbnail "+(t[45]+1)+" of "+t[13].length),W(e,"selected",t[1]===t[45])},m(d,o){D(d,e,o),H(l,e,null),T(e,n),s&&s.m(e,null),T(e,i),m=!0,b||(a=$(e,"click",u),b=!0)},p(d,o){t=d;const g={};o[0]&8192&&(g.alt=t[43].caption||""),o[0]&8192&&(g.src=typeof t[43].image=="string"?t[43].image:t[43].image.url),l.$set(g),t[43].caption?s?s.p(t,o):(s=Ie(t),s.c(),s.m(e,i)):s&&(s.d(1),s=null),(!m||o[0]&8192&&r!==(r="Thumbnail "+(t[45]+1)+" of "+t[13].length))&&A(e,"aria-label",r),(!m||o[0]&2)&&W(e,"selected",t[1]===t[45])},i(d){m||(v(l.$$.fragment,d),m=!0)},o(d){j(l.$$.fragment,d),m=!1},d(d){d&&U(e),N(l),s&&s.d(),b=!1,a()}}}function pl(t){let e,l;return e=new Re({}),{c(){q(e.$$.fragment)},m(n,i){H(e,n,i),l=!0},i(n){l||(v(e.$$.fragment,n),l=!0)},o(n){j(e.$$.fragment,n),l=!1},d(n){N(e,n)}}}function kl(t){let e,l,n,i,r,m,b;Ge(t[26]);let a=t[2]&&ke(t);const s=[wl,bl],u=[];function d(o,g){return o[0]==null||o[13]==null||o[13].length===0?0:1}return l=d(t),n=u[l]=s[l](t),{c(){a&&a.c(),e=E(),n.c(),i=ul()},m(o,g){a&&a.m(o,g),D(o,e,g),u[l].m(o,g),D(o,i,g),r=!0,m||(b=$(He,"resize",t[26]),m=!0)},p(o,g){o[2]?a?(a.p(o,g),g[0]&4&&v(a,1)):(a=ke(o),a.c(),v(a,1),a.m(e.parentNode,e)):a&&(X(),j(a,1,1,()=>{a=null}),V());let f=l;l=d(o),l===f?u[l].p(o,g):(X(),j(u[f],1,1,()=>{u[f]=null}),V(),n=u[l],n?n.p(o,g):(n=u[l]=s[l](o),n.c()),v(n,1),n.m(i.parentNode,i))},i(o){r||(v(a),v(n),r=!0)},o(o){j(a),j(n),r=!1},d(o){o&&(U(e),U(i)),a&&a.d(o),u[l].d(o),m=!1,b()}}}function vl(t,e,l){let n,i,r,{show_label:m=!0}=e,{label:b}=e,{value:a=null}=e,{columns:s=[2]}=e,{rows:u=void 0}=e,{height:d="auto"}=e,{preview:o}=e,{allow_preview:g=!0}=e,{object_fit:f="cover"}=e,{show_share_button:h=!1}=e,{show_download_button:p=!1}=e,{i18n:z}=e,{selected_index:k=null}=e,{interactive:w}=e;const y=hl();let L=!0,I=null,B=a;k==null&&o&&a?.length&&(k=0);let M=k;function R(_){const G=_.target,P=_.offsetX,Q=G.offsetWidth/2;P<Q?l(1,k=n):l(1,k=i)}function fe(_){switch(_.code){case"Escape":_.preventDefault(),l(1,k=null);break;case"ArrowLeft":_.preventDefault(),l(1,k=n);break;case"ArrowRight":_.preventDefault(),l(1,k=i);break}}let F=[],C;async function ue(_){if(typeof _!="number"||(await dl(),F[_]===void 0))return;F[_]?.focus();const{left:G,width:P}=C.getBoundingClientRect(),{left:oe,width:Q}=F[_].getBoundingClientRect(),Z=oe-G+Q/2-P/2+C.scrollLeft;C&&typeof C.scrollTo=="function"&&C.scrollTo({left:Z<0?0:Z,behavior:"smooth"})}let Y=0,ne=0;const se=gl("fetch_implementation");async function ie(_,G){let P;try{P=await se(_)}catch(Z){if(Z instanceof TypeError){window.open(_,"_blank","noreferrer");return}throw Z}const oe=await P.blob(),Q=URL.createObjectURL(oe),ae=document.createElement("a");ae.href=Q,ae.download=G,ae.click(),URL.revokeObjectURL(Q)}function c(){l(17,ne=He.innerHeight)}const _e=()=>{const _=r?.image;if(_==null)return;const{url:G,orig_name:P}=_;G&&ie(G,P??"image")},ce=()=>l(1,k=null),Me=_=>R(_);function Fe(_,G){de[_?"unshift":"push"](()=>{F[G]=_,l(14,F)})}const Pe=_=>l(1,k=_);function Ve(_){de[_?"unshift":"push"](()=>{C=_,l(15,C)})}const Xe=()=>l(0,a=null);function We(_){be.call(this,t,_)}function Je(_){be.call(this,t,_)}const Ke=_=>l(1,k=_);function Qe(){Y=this.clientHeight,l(16,Y)}return t.$$set=_=>{"show_label"in _&&l(2,m=_.show_label),"label"in _&&l(3,b=_.label),"value"in _&&l(0,a=_.value),"columns"in _&&l(4,s=_.columns),"rows"in _&&l(5,u=_.rows),"height"in _&&l(6,d=_.height),"preview"in _&&l(22,o=_.preview),"allow_preview"in _&&l(7,g=_.allow_preview),"object_fit"in _&&l(8,f=_.object_fit),"show_share_button"in _&&l(9,h=_.show_share_button),"show_download_button"in _&&l(10,p=_.show_download_button),"i18n"in _&&l(11,z=_.i18n),"selected_index"in _&&l(1,k=_.selected_index),"interactive"in _&&l(12,w=_.interactive)},t.$$.update=()=>{t.$$.dirty[0]&8388609&&l(23,L=a==null||a.length===0?!0:L),t.$$.dirty[0]&1&&l(13,I=a==null?null:a.map(_=>({image:_.image,caption:_.caption}))),t.$$.dirty[0]&29360131&&(x(B,a)||(L?(l(1,k=o&&a?.length?0:null),l(23,L=!1)):l(1,k=k!=null&&a!=null&&k<a.length?k:null),y("change"),l(24,B=a))),t.$$.dirty[0]&8194&&(n=((k??0)+(I?.length??0)-1)%(I?.length??0)),t.$$.dirty[0]&8194&&(i=((k??0)+1)%(I?.length??0)),t.$$.dirty[0]&33562626&&k!==M&&(l(25,M=k),k!==null&&y("select",{index:k,value:I?.[k]})),t.$$.dirty[0]&130&&g&&ue(k),t.$$.dirty[0]&8194&&l(18,r=k!=null&&I!=null?I[k]:null)},[a,k,m,b,s,u,d,g,f,h,p,z,w,I,F,C,Y,ne,r,R,fe,ie,o,L,B,M,c,_e,ce,Me,Fe,Pe,Ve,Xe,We,Je,Ke,Qe]}class zl extends rl{constructor(e){super(),_l(this,e,vl,kl,ml,{show_label:2,label:3,value:0,columns:4,rows:5,height:6,preview:22,allow_preview:7,object_fit:8,show_share_button:9,show_download_button:10,i18n:11,selected_index:1,interactive:12},null,[-1,-1])}}const yl=zl,{SvelteComponent:jl,add_flush_callback:Te,assign:Bl,bind:Se,binding_callbacks:Ue,check_outros:Al,create_component:ee,destroy_component:le,detach:De,empty:Il,get_spread_object:Ll,get_spread_update:Tl,group_outros:Sl,init:Ul,insert:Ee,mount_component:te,safe_not_equal:Dl,space:El,transition_in:J,transition_out:K}=window.__gradio__svelte__internal,{createEventDispatcher:Rl}=window.__gradio__svelte__internal;function Cl(t){let e,l,n,i;function r(a){t[24](a)}function m(a){t[25](a)}let b={label:t[4],show_label:t[3],columns:t[12],rows:t[13],height:t[14],preview:t[15],object_fit:t[17],interactive:t[19],allow_preview:t[16],show_share_button:t[18],show_download_button:t[20],i18n:t[21].i18n};return t[1]!==void 0&&(b.selected_index=t[1]),t[0]!==void 0&&(b.value=t[0]),e=new yl({props:b}),Ue.push(()=>Se(e,"selected_index",r)),Ue.push(()=>Se(e,"value",m)),e.$on("change",t[26]),e.$on("select",t[27]),e.$on("share",t[28]),e.$on("error",t[29]),{c(){ee(e.$$.fragment)},m(a,s){te(e,a,s),i=!0},p(a,s){const u={};s[0]&16&&(u.label=a[4]),s[0]&8&&(u.show_label=a[3]),s[0]&4096&&(u.columns=a[12]),s[0]&8192&&(u.rows=a[13]),s[0]&16384&&(u.height=a[14]),s[0]&32768&&(u.preview=a[15]),s[0]&131072&&(u.object_fit=a[17]),s[0]&524288&&(u.interactive=a[19]),s[0]&65536&&(u.allow_preview=a[16]),s[0]&262144&&(u.show_share_button=a[18]),s[0]&1048576&&(u.show_download_button=a[20]),s[0]&2097152&&(u.i18n=a[21].i18n),!l&&s[0]&2&&(l=!0,u.selected_index=a[1],Te(()=>l=!1)),!n&&s[0]&1&&(n=!0,u.value=a[0],Te(()=>n=!1)),e.$set(u)},i(a){i||(J(e.$$.fragment,a),i=!0)},o(a){K(e.$$.fragment,a),i=!1},d(a){le(e,a)}}}function Gl(t){let e,l;return e=new ol({props:{value:null,root:t[5],label:t[4],file_count:"multiple",file_types:["image"],i18n:t[21].i18n,$$slots:{default:[Ol]},$$scope:{ctx:t}}}),e.$on("upload",t[23]),{c(){ee(e.$$.fragment)},m(n,i){te(e,n,i),l=!0},p(n,i){const r={};i[0]&32&&(r.root=n[5]),i[0]&16&&(r.label=n[4]),i[0]&2097152&&(r.i18n=n[21].i18n),i[0]&2097152|i[1]&1&&(r.$$scope={dirty:i,ctx:n}),e.$set(r)},i(n){l||(J(e.$$.fragment,n),l=!0)},o(n){K(e.$$.fragment,n),l=!1},d(n){le(e,n)}}}function Ol(t){let e,l;return e=new xe({props:{i18n:t[21].i18n,type:"gallery"}}),{c(){ee(e.$$.fragment)},m(n,i){te(e,n,i),l=!0},p(n,i){const r={};i[0]&2097152&&(r.i18n=n[21].i18n),e.$set(r)},i(n){l||(J(e.$$.fragment,n),l=!0)},o(n){K(e.$$.fragment,n),l=!1},d(n){le(e,n)}}}function ql(t){let e,l,n,i,r,m;const b=[{autoscroll:t[21].autoscroll},{i18n:t[21].i18n},t[2]];let a={};for(let o=0;o<b.length;o+=1)a=Bl(a,b[o]);e=new Ze({props:a});const s=[Gl,Cl],u=[];function d(o,g){return o[19]&&o[22]?0:1}return n=d(t),i=u[n]=s[n](t),{c(){ee(e.$$.fragment),l=El(),i.c(),r=Il()},m(o,g){te(e,o,g),Ee(o,l,g),u[n].m(o,g),Ee(o,r,g),m=!0},p(o,g){const f=g[0]&2097156?Tl(b,[g[0]&2097152&&{autoscroll:o[21].autoscroll},g[0]&2097152&&{i18n:o[21].i18n},g[0]&4&&Ll(o[2])]):{};e.$set(f);let h=n;n=d(o),n===h?u[n].p(o,g):(Sl(),K(u[h],1,1,()=>{u[h]=null}),Al(),i=u[n],i?i.p(o,g):(i=u[n]=s[n](o),i.c()),J(i,1),i.m(r.parentNode,r))},i(o){m||(J(e.$$.fragment,o),J(i),m=!0)},o(o){K(e.$$.fragment,o),K(i),m=!1},d(o){o&&(De(l),De(r)),le(e,o),u[n].d(o)}}}function Nl(t){let e,l;return e=new Ye({props:{visible:t[8],variant:"solid",padding:!1,elem_id:t[6],elem_classes:t[7],container:t[9],scale:t[10],min_width:t[11],allow_overflow:!1,height:typeof t[14]=="number"?t[14]:void 0,$$slots:{default:[ql]},$$scope:{ctx:t}}}),{c(){ee(e.$$.fragment)},m(n,i){te(e,n,i),l=!0},p(n,i){const r={};i[0]&256&&(r.visible=n[8]),i[0]&64&&(r.elem_id=n[6]),i[0]&128&&(r.elem_classes=n[7]),i[0]&512&&(r.container=n[9]),i[0]&1024&&(r.scale=n[10]),i[0]&2048&&(r.min_width=n[11]),i[0]&16384&&(r.height=typeof n[14]=="number"?n[14]:void 0),i[0]&8384575|i[1]&1&&(r.$$scope={dirty:i,ctx:n}),e.$set(r)},i(n){l||(J(e.$$.fragment,n),l=!0)},o(n){K(e.$$.fragment,n),l=!1},d(n){le(e,n)}}}function Hl(t,e,l){let n,{loading_status:i}=e,{show_label:r}=e,{label:m}=e,{root:b}=e,{elem_id:a=""}=e,{elem_classes:s=[]}=e,{visible:u=!0}=e,{value:d=null}=e,{container:o=!0}=e,{scale:g=null}=e,{min_width:f=void 0}=e,{columns:h=[2]}=e,{rows:p=void 0}=e,{height:z="auto"}=e,{preview:k}=e,{allow_preview:w=!0}=e,{selected_index:y=null}=e,{object_fit:L="cover"}=e,{show_share_button:I=!1}=e,{interactive:B}=e,{show_download_button:M=!1}=e,{gradio:R}=e;const fe=Rl(),F=c=>{const _e=Array.isArray(c.detail)?c.detail:[c.detail];l(0,d=_e.map(ce=>({image:ce,caption:null}))),R.dispatch("upload",d)};function C(c){y=c,l(1,y)}function ue(c){d=c,l(0,d)}const Y=()=>R.dispatch("change",d),ne=c=>R.dispatch("select",c.detail),se=c=>R.dispatch("share",c.detail),ie=c=>R.dispatch("error",c.detail);return t.$$set=c=>{"loading_status"in c&&l(2,i=c.loading_status),"show_label"in c&&l(3,r=c.show_label),"label"in c&&l(4,m=c.label),"root"in c&&l(5,b=c.root),"elem_id"in c&&l(6,a=c.elem_id),"elem_classes"in c&&l(7,s=c.elem_classes),"visible"in c&&l(8,u=c.visible),"value"in c&&l(0,d=c.value),"container"in c&&l(9,o=c.container),"scale"in c&&l(10,g=c.scale),"min_width"in c&&l(11,f=c.min_width),"columns"in c&&l(12,h=c.columns),"rows"in c&&l(13,p=c.rows),"height"in c&&l(14,z=c.height),"preview"in c&&l(15,k=c.preview),"allow_preview"in c&&l(16,w=c.allow_preview),"selected_index"in c&&l(1,y=c.selected_index),"object_fit"in c&&l(17,L=c.object_fit),"show_share_button"in c&&l(18,I=c.show_share_button),"interactive"in c&&l(19,B=c.interactive),"show_download_button"in c&&l(20,M=c.show_download_button),"gradio"in c&&l(21,R=c.gradio)},t.$$.update=()=>{t.$$.dirty[0]&1&&l(22,n=Array.isArray(d)?d.length===0:!d),t.$$.dirty[0]&2&&fe("prop_change",{selected_index:y})},[d,y,i,r,m,b,a,s,u,o,g,f,h,p,z,k,w,L,I,B,M,R,n,F,C,ue,Y,ne,se,ie]}class ut extends jl{constructor(e){super(),Ul(this,e,Hl,Nl,Dl,{loading_status:2,show_label:3,label:4,root:5,elem_id:6,elem_classes:7,visible:8,value:0,container:9,scale:10,min_width:11,columns:12,rows:13,height:14,preview:15,allow_preview:16,selected_index:1,object_fit:17,show_share_button:18,interactive:19,show_download_button:20,gradio:21},null,[-1,-1])}}export{yl as BaseGallery,ut as default};
//# sourceMappingURL=Index-187b9380.js.map
