import{I as ue,S as _e}from"./ImageUploader-528d6ef1.js";import{W as Ye}from"./ImageUploader-528d6ef1.js";import{B as G}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{E as fe}from"./Empty-28f63bf0.js";import{S as H}from"./Index-26cfc80a.js";import{I as ce}from"./Image-eaba773f.js";import{U as K}from"./UploadText-39c67ae9.js";import{I as ye}from"./Image-21c02477.js";import{default as et}from"./Example-092ffb31.js";import"./utils-572af92b.js";import"./BlockLabel-f27805b1.js";import"./IconButton-7294c90b.js";import"./ShareButton-7dae44e7.js";import"./DownloadLink-7ff36416.js";import"./file-url-bef2dc1b.js";import"./index-a80d931b.js";import"./svelte/svelte.js";import"./SelectSource-f5281119.js";import"./Upload-351cc897.js";import"./DropdownArrow-bb2afb7e.js";import"./ModifyUpload.svelte_svelte_type_style_lang-4ba51100.js";import"./Clear-2c7bae91.js";/* empty css                                              */const{SvelteComponent:me,add_flush_callback:A,assign:L,bind:D,binding_callbacks:F,check_outros:M,create_component:k,destroy_component:v,detach:B,empty:P,flush:c,get_spread_object:Q,get_spread_update:R,group_outros:V,init:he,insert:N,mount_component:p,safe_not_equal:ge,space:X,transition_in:d,transition_out:b}=window.__gradio__svelte__internal;function de(n){let e,s;return e=new G({props:{visible:n[4],variant:n[0]===null?"dashed":"solid",border_mode:n[22]?"focus":"base",padding:!1,elem_id:n[2],elem_classes:n[3],height:n[9]||void 0,width:n[10],allow_overflow:!1,container:n[12],scale:n[13],min_width:n[14],$$slots:{default:[Ie]},$$scope:{ctx:n}}}),{c(){k(e.$$.fragment)},m(t,i){p(e,t,i),s=!0},p(t,i){const r={};i[0]&16&&(r.visible=t[4]),i[0]&1&&(r.variant=t[0]===null?"dashed":"solid"),i[0]&4194304&&(r.border_mode=t[22]?"focus":"base"),i[0]&4&&(r.elem_id=t[2]),i[0]&8&&(r.elem_classes=t[3]),i[0]&512&&(r.height=t[9]||void 0),i[0]&1024&&(r.width=t[10]),i[0]&4096&&(r.container=t[12]),i[0]&8192&&(r.scale=t[13]),i[0]&16384&&(r.min_width=t[14]),i[0]&16583011|i[1]&128&&(r.$$scope={dirty:i,ctx:t}),e.$set(r)},i(t){s||(d(e.$$.fragment,t),s=!0)},o(t){b(e.$$.fragment,t),s=!1},d(t){v(e,t)}}}function be(n){let e,s;return e=new G({props:{visible:n[4],variant:"solid",border_mode:n[22]?"focus":"base",padding:!1,elem_id:n[2],elem_classes:n[3],height:n[9]||void 0,width:n[10],allow_overflow:!1,container:n[12],scale:n[13],min_width:n[14],$$slots:{default:[Se]},$$scope:{ctx:n}}}),{c(){k(e.$$.fragment)},m(t,i){p(e,t,i),s=!0},p(t,i){const r={};i[0]&16&&(r.visible=t[4]),i[0]&4194304&&(r.border_mode=t[22]?"focus":"base"),i[0]&4&&(r.elem_id=t[2]),i[0]&8&&(r.elem_classes=t[3]),i[0]&512&&(r.height=t[9]||void 0),i[0]&1024&&(r.width=t[10]),i[0]&4096&&(r.container=t[12]),i[0]&8192&&(r.scale=t[13]),i[0]&16384&&(r.min_width=t[14]),i[0]&2132195|i[1]&128&&(r.$$scope={dirty:i,ctx:t}),e.$set(r)},i(t){s||(d(e.$$.fragment,t),s=!0)},o(t){b(e.$$.fragment,t),s=!1},d(t){v(e,t)}}}function we(n){let e,s;return e=new fe({props:{unpadded_box:!0,size:"large",$$slots:{default:[pe]},$$scope:{ctx:n}}}),{c(){k(e.$$.fragment)},m(t,i){p(e,t,i),s=!0},p(t,i){const r={};i[1]&128&&(r.$$scope={dirty:i,ctx:t}),e.$set(r)},i(t){s||(d(e.$$.fragment,t),s=!0)},o(t){b(e.$$.fragment,t),s=!1},d(t){v(e,t)}}}function ke(n){let e,s;return e=new K({props:{i18n:n[21].i18n,type:"clipboard",mode:"short"}}),{c(){k(e.$$.fragment)},m(t,i){p(e,t,i),s=!0},p(t,i){const r={};i[0]&2097152&&(r.i18n=t[21].i18n),e.$set(r)},i(t){s||(d(e.$$.fragment,t),s=!0)},o(t){b(e.$$.fragment,t),s=!1},d(t){v(e,t)}}}function ve(n){let e,s;return e=new K({props:{i18n:n[21].i18n,type:"image"}}),{c(){k(e.$$.fragment)},m(t,i){p(e,t,i),s=!0},p(t,i){const r={};i[0]&2097152&&(r.i18n=t[21].i18n),e.$set(r)},i(t){s||(d(e.$$.fragment,t),s=!0)},o(t){b(e.$$.fragment,t),s=!1},d(t){v(e,t)}}}function pe(n){let e,s;return e=new ce({}),{c(){k(e.$$.fragment)},m(t,i){p(e,t,i),s=!0},i(t){s||(d(e.$$.fragment,t),s=!0)},o(t){b(e.$$.fragment,t),s=!1},d(t){v(e,t)}}}function $e(n){let e,s,t,i;const r=[ve,ke,we],_=[];function o(a,h){return a[23]==="upload"||!a[23]?0:a[23]==="clipboard"?1:2}return e=o(n),s=_[e]=r[e](n),{c(){s.c(),t=P()},m(a,h){_[e].m(a,h),N(a,t,h),i=!0},p(a,h){let m=e;e=o(a),e===m?_[e].p(a,h):(V(),b(_[m],1,1,()=>{_[m]=null}),M(),s=_[e],s?s.p(a,h):(s=_[e]=r[e](a),s.c()),d(s,1),s.m(t.parentNode,t))},i(a){i||(d(s),i=!0)},o(a){b(s),i=!1},d(a){a&&B(t),_[e].d(a)}}}function Ie(n){let e,s,t,i,r,_;const o=[{autoscroll:n[21].autoscroll},{i18n:n[21].i18n},n[1]];let a={};for(let u=0;u<o.length;u+=1)a=L(a,o[u]);e=new H({props:a});function h(u){n[28](u)}function m(u){n[29](u)}let $={selectable:n[11],root:n[8],sources:n[16],label:n[5],show_label:n[6],pending:n[19],streaming:n[18],mirror_webcam:n[20],i18n:n[21].i18n,$$slots:{default:[$e]},$$scope:{ctx:n}};return n[23]!==void 0&&($.active_source=n[23]),n[0]!==void 0&&($.value=n[0]),t=new ue({props:$}),F.push(()=>D(t,"active_source",h)),F.push(()=>D(t,"value",m)),t.$on("edit",n[30]),t.$on("clear",n[31]),t.$on("stream",n[32]),t.$on("drag",n[33]),t.$on("upload",n[34]),t.$on("select",n[35]),t.$on("share",n[36]),t.$on("error",n[37]),{c(){k(e.$$.fragment),s=X(),k(t.$$.fragment)},m(u,f){p(e,u,f),N(u,s,f),p(t,u,f),_=!0},p(u,f){const S=f[0]&2097154?R(o,[f[0]&2097152&&{autoscroll:u[21].autoscroll},f[0]&2097152&&{i18n:u[21].i18n},f[0]&2&&Q(u[1])]):{};e.$set(S);const g={};f[0]&2048&&(g.selectable=u[11]),f[0]&256&&(g.root=u[8]),f[0]&65536&&(g.sources=u[16]),f[0]&32&&(g.label=u[5]),f[0]&64&&(g.show_label=u[6]),f[0]&524288&&(g.pending=u[19]),f[0]&262144&&(g.streaming=u[18]),f[0]&1048576&&(g.mirror_webcam=u[20]),f[0]&2097152&&(g.i18n=u[21].i18n),f[0]&10485760|f[1]&128&&(g.$$scope={dirty:f,ctx:u}),!i&&f[0]&8388608&&(i=!0,g.active_source=u[23],A(()=>i=!1)),!r&&f[0]&1&&(r=!0,g.value=u[0],A(()=>r=!1)),t.$set(g)},i(u){_||(d(e.$$.fragment,u),d(t.$$.fragment,u),_=!0)},o(u){b(e.$$.fragment,u),b(t.$$.fragment,u),_=!1},d(u){u&&B(s),v(e,u),v(t,u)}}}function Se(n){let e,s,t,i;const r=[{autoscroll:n[21].autoscroll},{i18n:n[21].i18n},n[1]];let _={};for(let o=0;o<r.length;o+=1)_=L(_,r[o]);return e=new H({props:_}),t=new _e({props:{value:n[0],label:n[5],show_label:n[6],show_download_button:n[7],selectable:n[11],show_share_button:n[15],i18n:n[21].i18n}}),t.$on("select",n[25]),t.$on("share",n[26]),t.$on("error",n[27]),{c(){k(e.$$.fragment),s=X(),k(t.$$.fragment)},m(o,a){p(e,o,a),N(o,s,a),p(t,o,a),i=!0},p(o,a){const h=a[0]&2097154?R(r,[a[0]&2097152&&{autoscroll:o[21].autoscroll},a[0]&2097152&&{i18n:o[21].i18n},a[0]&2&&Q(o[1])]):{};e.$set(h);const m={};a[0]&1&&(m.value=o[0]),a[0]&32&&(m.label=o[5]),a[0]&64&&(m.show_label=o[6]),a[0]&128&&(m.show_download_button=o[7]),a[0]&2048&&(m.selectable=o[11]),a[0]&32768&&(m.show_share_button=o[15]),a[0]&2097152&&(m.i18n=o[21].i18n),t.$set(m)},i(o){i||(d(e.$$.fragment,o),d(t.$$.fragment,o),i=!0)},o(o){b(e.$$.fragment,o),b(t.$$.fragment,o),i=!1},d(o){o&&B(s),v(e,o),v(t,o)}}}function Be(n){let e,s,t,i;const r=[be,de],_=[];function o(a,h){return a[17]?1:0}return e=o(n),s=_[e]=r[e](n),{c(){s.c(),t=P()},m(a,h){_[e].m(a,h),N(a,t,h),i=!0},p(a,h){let m=e;e=o(a),e===m?_[e].p(a,h):(V(),b(_[m],1,1,()=>{_[m]=null}),M(),s=_[e],s?s.p(a,h):(s=_[e]=r[e](a),s.c()),d(s,1),s.m(t.parentNode,t))},i(a){i||(d(s),i=!0)},o(a){b(s),i=!1},d(a){a&&B(t),_[e].d(a)}}}function Ne(n,e,s){let{elem_id:t=""}=e,{elem_classes:i=[]}=e,{visible:r=!0}=e,{value:_=null}=e,o=null,{label:a}=e,{show_label:h}=e,{show_download_button:m}=e,{root:$}=e,{height:u}=e,{width:f}=e,{_selectable:S=!1}=e,{container:g=!0}=e,{scale:E=null}=e,{min_width:J=void 0}=e,{loading_status:I}=e,{show_share_button:O=!1}=e,{sources:W=["upload","clipboard","webcam"]}=e,{interactive:j}=e,{streaming:q}=e,{pending:z}=e,{mirror_webcam:C}=e,{gradio:w}=e,T,U=null;const Y=({detail:l})=>w.dispatch("select",l),Z=({detail:l})=>w.dispatch("share",l),y=({detail:l})=>w.dispatch("error",l);function x(l){U=l,s(23,U)}function ee(l){_=l,s(0,_)}const te=()=>w.dispatch("edit"),se=()=>{w.dispatch("clear")},ne=()=>w.dispatch("stream"),ie=({detail:l})=>s(22,T=l),le=()=>w.dispatch("upload"),ae=({detail:l})=>w.dispatch("select",l),re=({detail:l})=>w.dispatch("share",l),oe=({detail:l})=>{s(1,I=I||{}),s(1,I.status="error",I),w.dispatch("error",l)};return n.$$set=l=>{"elem_id"in l&&s(2,t=l.elem_id),"elem_classes"in l&&s(3,i=l.elem_classes),"visible"in l&&s(4,r=l.visible),"value"in l&&s(0,_=l.value),"label"in l&&s(5,a=l.label),"show_label"in l&&s(6,h=l.show_label),"show_download_button"in l&&s(7,m=l.show_download_button),"root"in l&&s(8,$=l.root),"height"in l&&s(9,u=l.height),"width"in l&&s(10,f=l.width),"_selectable"in l&&s(11,S=l._selectable),"container"in l&&s(12,g=l.container),"scale"in l&&s(13,E=l.scale),"min_width"in l&&s(14,J=l.min_width),"loading_status"in l&&s(1,I=l.loading_status),"show_share_button"in l&&s(15,O=l.show_share_button),"sources"in l&&s(16,W=l.sources),"interactive"in l&&s(17,j=l.interactive),"streaming"in l&&s(18,q=l.streaming),"pending"in l&&s(19,z=l.pending),"mirror_webcam"in l&&s(20,C=l.mirror_webcam),"gradio"in l&&s(21,w=l.gradio)},n.$$.update=()=>{n.$$.dirty[0]&18874369&&JSON.stringify(_)!==JSON.stringify(o)&&(s(24,o=_),w.dispatch("change"))},[_,I,t,i,r,a,h,m,$,u,f,S,g,E,J,O,W,j,q,z,C,w,T,U,o,Y,Z,y,x,ee,te,se,ne,ie,le,ae,re,oe]}class Re extends me{constructor(e){super(),he(this,e,Ne,Be,ge,{elem_id:2,elem_classes:3,visible:4,value:0,label:5,show_label:6,show_download_button:7,root:8,height:9,width:10,_selectable:11,container:12,scale:13,min_width:14,loading_status:1,show_share_button:15,sources:16,interactive:17,streaming:18,pending:19,mirror_webcam:20,gradio:21},null,[-1,-1])}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),c()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),c()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),c()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),c()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),c()}get show_label(){return this.$$.ctx[6]}set show_label(e){this.$$set({show_label:e}),c()}get show_download_button(){return this.$$.ctx[7]}set show_download_button(e){this.$$set({show_download_button:e}),c()}get root(){return this.$$.ctx[8]}set root(e){this.$$set({root:e}),c()}get height(){return this.$$.ctx[9]}set height(e){this.$$set({height:e}),c()}get width(){return this.$$.ctx[10]}set width(e){this.$$set({width:e}),c()}get _selectable(){return this.$$.ctx[11]}set _selectable(e){this.$$set({_selectable:e}),c()}get container(){return this.$$.ctx[12]}set container(e){this.$$set({container:e}),c()}get scale(){return this.$$.ctx[13]}set scale(e){this.$$set({scale:e}),c()}get min_width(){return this.$$.ctx[14]}set min_width(e){this.$$set({min_width:e}),c()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),c()}get show_share_button(){return this.$$.ctx[15]}set show_share_button(e){this.$$set({show_share_button:e}),c()}get sources(){return this.$$.ctx[16]}set sources(e){this.$$set({sources:e}),c()}get interactive(){return this.$$.ctx[17]}set interactive(e){this.$$set({interactive:e}),c()}get streaming(){return this.$$.ctx[18]}set streaming(e){this.$$set({streaming:e}),c()}get pending(){return this.$$.ctx[19]}set pending(e){this.$$set({pending:e}),c()}get mirror_webcam(){return this.$$.ctx[20]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),c()}get gradio(){return this.$$.ctx[21]}set gradio(e){this.$$set({gradio:e}),c()}}export{et as BaseExample,ye as BaseImage,ue as BaseImageUploader,_e as BaseStaticImage,Ye as Webcam,Re as default};
//# sourceMappingURL=Index-bc5996aa.js.map
