/* empty css                                              */const{SvelteComponent:y,add_iframe_resize_listener:b,add_render_callback:v,append:m,attr:h,binding_callbacks:w,detach:z,element:p,init:k,insert:S,noop:f,safe_not_equal:q,set_data:C,text:E,toggle_class:r}=window.__gradio__svelte__internal,{onMount:M}=window.__gradio__svelte__internal;function P(t){let e,n=(t[0]?t[0]:"")+"",_,d;return{c(){e=p("div"),_=E(n),h(e,"class","svelte-1viwdyg"),v(()=>t[5].call(e)),r(e,"table",t[1]==="table"),r(e,"gallery",t[1]==="gallery"),r(e,"selected",t[2])},m(l,i){S(l,e,i),m(e,_),d=b(e,t[5].bind(e)),t[6](e)},p(l,[i]){i&1&&n!==(n=(l[0]?l[0]:"")+"")&&C(_,n),i&2&&r(e,"table",l[1]==="table"),i&2&&r(e,"gallery",l[1]==="gallery"),i&4&&r(e,"selected",l[2])},i:f,o:f,d(l){l&&z(e),d(),t[6](null)}}}function W(t,e,n){let{value:_}=e,{type:d}=e,{selected:l=!1}=e,i,a;function o(s,u){!s||!u||(a.style.setProperty("--local-text-width",`${u<150?u:200}px`),n(4,a.style.whiteSpace="unset",a))}M(()=>{o(a,i)});function c(){i=this.clientWidth,n(3,i)}function g(s){w[s?"unshift":"push"](()=>{a=s,n(4,a)})}return t.$$set=s=>{"value"in s&&n(0,_=s.value),"type"in s&&n(1,d=s.type),"selected"in s&&n(2,l=s.selected)},[_,d,l,i,a,c,g]}class A extends y{constructor(e){super(),k(this,e,W,P,q,{value:0,type:1,selected:2})}}export{A as default};
//# sourceMappingURL=Example-baada562.js.map
