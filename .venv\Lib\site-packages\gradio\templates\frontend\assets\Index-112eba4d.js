import{T as h}from"./Tabs-9b11644c.js";import{a as P}from"./Tabs-9b11644c.js";import"./Index-26cfc80a.js";import"./index-a80d931b.js";import"./svelte/svelte.js";const{SvelteComponent:v,add_flush_callback:w,bind:p,binding_callbacks:T,create_component:k,create_slot:S,destroy_component:q,get_all_dirty_from_scope:A,get_slot_changes:B,init:C,mount_component:D,safe_not_equal:E,transition_in:r,transition_out:u,update_slot_base:I}=window.__gradio__svelte__internal,{createEventDispatcher:j}=window.__gradio__svelte__internal;function z(t){let e;const a=t[5].default,s=S(a,t,t[9],null);return{c(){s&&s.c()},m(n,o){s&&s.m(n,o),e=!0},p(n,o){s&&s.p&&(!e||o&512)&&I(s,a,n,n[9],e?B(a,n[9],o,null):A(n[9]),null)},i(n){e||(r(s,n),e=!0)},o(n){u(s,n),e=!1},d(n){s&&s.d(n)}}}function F(t){let e,a,s;function n(l){t[6](l)}let o={visible:t[1],elem_id:t[2],elem_classes:t[3],$$slots:{default:[z]},$$scope:{ctx:t}};return t[0]!==void 0&&(o.selected=t[0]),e=new h({props:o}),T.push(()=>p(e,"selected",n)),e.$on("change",t[7]),e.$on("select",t[8]),{c(){k(e.$$.fragment)},m(l,_){D(e,l,_),s=!0},p(l,[_]){const c={};_&2&&(c.visible=l[1]),_&4&&(c.elem_id=l[2]),_&8&&(c.elem_classes=l[3]),_&512&&(c.$$scope={dirty:_,ctx:l}),!a&&_&1&&(a=!0,c.selected=l[0],w(()=>a=!1)),e.$set(c)},i(l){s||(r(e.$$.fragment,l),s=!0)},o(l){u(e.$$.fragment,l),s=!1},d(l){q(e,l)}}}function G(t,e,a){let{$$slots:s={},$$scope:n}=e;const o=j();let{visible:l=!0}=e,{elem_id:_=""}=e,{elem_classes:c=[]}=e,{selected:d}=e,{gradio:f}=e;function m(i){d=i,a(0,d)}const g=()=>f.dispatch("change"),b=i=>f.dispatch("select",i.detail);return t.$$set=i=>{"visible"in i&&a(1,l=i.visible),"elem_id"in i&&a(2,_=i.elem_id),"elem_classes"in i&&a(3,c=i.elem_classes),"selected"in i&&a(0,d=i.selected),"gradio"in i&&a(4,f=i.gradio),"$$scope"in i&&a(9,n=i.$$scope)},t.$$.update=()=>{t.$$.dirty&1&&o("prop_change",{selected:d})},[d,l,_,c,f,s,m,g,b,n]}class M extends v{constructor(e){super(),C(this,e,G,F,E,{visible:1,elem_id:2,elem_classes:3,selected:0,gradio:4})}}export{P as TABS,M as default};
//# sourceMappingURL=Index-112eba4d.js.map
