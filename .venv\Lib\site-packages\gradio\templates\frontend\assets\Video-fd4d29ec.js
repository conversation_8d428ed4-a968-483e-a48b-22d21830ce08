import{r as z}from"./file-url-bef2dc1b.js";const B=new Error("failed to get response body reader"),C=new Error("failed to complete download"),S="Content-Length",M=async(t,i)=>{const a=await fetch(t);let e;try{const p=parseInt(a.headers.get(S)||"-1"),c=a.body?.getReader();if(!c)throw B;const u=[];let d=0;for(;;){const{done:n,value:x}=await c.read(),f=x?x.length:0;if(n){if(p!=-1&&p!==d)throw C;i&&i({url:t,total:p,received:d,delta:f,done:n});break}u.push(x),d+=f,i&&i({url:t,total:p,received:d,delta:f,done:n})}const v=new Uint8Array(d);let g=0;for(const n of u)v.set(n,g),g+=n.length;e=v.buffer}catch(p){console.log("failed to send download progress event: ",p),e=await a.arrayBuffer(),i&&i({url:t,total:e.byteLength,received:e.byteLength,delta:0,done:!0})}return e},y=async(t,i,a=!1,e)=>{const p=a?await M(t,e):await(await fetch(t)).arrayBuffer(),c=new Blob([p],{type:i});return URL.createObjectURL(c)};var s;(function(t){t.LOAD="LOAD",t.EXEC="EXEC",t.WRITE_FILE="WRITE_FILE",t.READ_FILE="READ_FILE",t.DELETE_FILE="DELETE_FILE",t.RENAME="RENAME",t.CREATE_DIR="CREATE_DIR",t.LIST_DIR="LIST_DIR",t.DELETE_DIR="DELETE_DIR",t.ERROR="ERROR",t.DOWNLOAD="DOWNLOAD",t.PROGRESS="PROGRESS",t.LOG="LOG",t.MOUNT="MOUNT",t.UNMOUNT="UNMOUNT"})(s||(s={}));const P=(()=>{let t=0;return()=>t++})(),V=new Error("ffmpeg is not loaded, call `await ffmpeg.load()` first"),W=new Error("called FFmpeg.terminate()");class G{#a=null;#e={};#t={};#o=[];#l=[];loaded=!1;#p=()=>{this.#a&&(this.#a.onmessage=({data:{id:i,type:a,data:e}})=>{switch(a){case s.LOAD:this.loaded=!0,this.#e[i](e);break;case s.MOUNT:case s.UNMOUNT:case s.EXEC:case s.WRITE_FILE:case s.READ_FILE:case s.DELETE_FILE:case s.RENAME:case s.CREATE_DIR:case s.LIST_DIR:case s.DELETE_DIR:this.#e[i](e);break;case s.LOG:this.#o.forEach(p=>p(e));break;case s.PROGRESS:this.#l.forEach(p=>p(e));break;case s.ERROR:this.#t[i](e);break}delete this.#e[i],delete this.#t[i]})};#i=({type:i,data:a},e=[],p)=>this.#a?new Promise((c,u)=>{const d=P();this.#a&&this.#a.postMessage({id:d,type:i,data:a},e),this.#e[d]=c,this.#t[d]=u,p?.addEventListener("abort",()=>{u(new DOMException(`Message # ${d} was aborted`,"AbortError"))},{once:!0})}):Promise.reject(V);on(i,a){i==="log"?this.#o.push(a):i==="progress"&&this.#l.push(a)}off(i,a){i==="log"?this.#o=this.#o.filter(e=>e!==a):i==="progress"&&(this.#l=this.#l.filter(e=>e!==a))}load=(i={},{signal:a}={})=>(this.#a||(this.#a=new Worker(new URL(""+new URL("worker-1779ba70.js",import.meta.url).href,self.location),{type:"module"}),this.#p()),this.#i({type:s.LOAD,data:i},void 0,a));exec=(i,a=-1,{signal:e}={})=>this.#i({type:s.EXEC,data:{args:i,timeout:a}},void 0,e);terminate=()=>{const i=Object.keys(this.#t);for(const a of i)this.#t[a](W),delete this.#t[a],delete this.#e[a];this.#a&&(this.#a.terminate(),this.#a=null,this.loaded=!1)};writeFile=(i,a,{signal:e}={})=>{const p=[];return a instanceof Uint8Array&&p.push(a.buffer),this.#i({type:s.WRITE_FILE,data:{path:i,data:a}},p,e)};mount=(i,a,e)=>{const p=[];return this.#i({type:s.MOUNT,data:{fsType:i,options:a,mountPoint:e}},p)};unmount=i=>{const a=[];return this.#i({type:s.UNMOUNT,data:{mountPoint:i}},a)};readFile=(i,a="binary",{signal:e}={})=>this.#i({type:s.READ_FILE,data:{path:i,encoding:a}},void 0,e);deleteFile=(i,{signal:a}={})=>this.#i({type:s.DELETE_FILE,data:{path:i}},void 0,a);rename=(i,a,{signal:e}={})=>this.#i({type:s.RENAME,data:{oldPath:i,newPath:a}},void 0,e);createDir=(i,{signal:a}={})=>this.#i({type:s.CREATE_DIR,data:{path:i}},void 0,a);listDir=(i,{signal:a}={})=>this.#i({type:s.LIST_DIR,data:{path:i}},void 0,a);deleteDir=(i,{signal:a}={})=>this.#i({type:s.DELETE_DIR,data:{path:i}},void 0,a)}const X={"3g2":"video/3gpp2","3gp":"video/3gpp","3gpp":"video/3gpp","3mf":"model/3mf",aac:"audio/aac",ac:"application/pkix-attr-cert",adp:"audio/adpcm",adts:"audio/aac",ai:"application/postscript",aml:"application/automationml-aml+xml",amlx:"application/automationml-amlx+zip",amr:"audio/amr",apng:"image/apng",appcache:"text/cache-manifest",appinstaller:"application/appinstaller",appx:"application/appx",appxbundle:"application/appxbundle",asc:"application/pgp-keys",atom:"application/atom+xml",atomcat:"application/atomcat+xml",atomdeleted:"application/atomdeleted+xml",atomsvc:"application/atomsvc+xml",au:"audio/basic",avci:"image/avci",avcs:"image/avcs",avif:"image/avif",aw:"application/applixware",bdoc:"application/bdoc",bin:"application/octet-stream",bmp:"image/bmp",bpk:"application/octet-stream",btf:"image/prs.btif",btif:"image/prs.btif",buffer:"application/octet-stream",ccxml:"application/ccxml+xml",cdfx:"application/cdfx+xml",cdmia:"application/cdmi-capability",cdmic:"application/cdmi-container",cdmid:"application/cdmi-domain",cdmio:"application/cdmi-object",cdmiq:"application/cdmi-queue",cer:"application/pkix-cert",cgm:"image/cgm",cjs:"application/node",class:"application/java-vm",coffee:"text/coffeescript",conf:"text/plain",cpl:"application/cpl+xml",cpt:"application/mac-compactpro",crl:"application/pkix-crl",css:"text/css",csv:"text/csv",cu:"application/cu-seeme",cwl:"application/cwl",cww:"application/prs.cww",davmount:"application/davmount+xml",dbk:"application/docbook+xml",deb:"application/octet-stream",def:"text/plain",deploy:"application/octet-stream",dib:"image/bmp","disposition-notification":"message/disposition-notification",dist:"application/octet-stream",distz:"application/octet-stream",dll:"application/octet-stream",dmg:"application/octet-stream",dms:"application/octet-stream",doc:"application/msword",dot:"application/msword",dpx:"image/dpx",drle:"image/dicom-rle",dsc:"text/prs.lines.tag",dssc:"application/dssc+der",dtd:"application/xml-dtd",dump:"application/octet-stream",dwd:"application/atsc-dwd+xml",ear:"application/java-archive",ecma:"application/ecmascript",elc:"application/octet-stream",emf:"image/emf",eml:"message/rfc822",emma:"application/emma+xml",emotionml:"application/emotionml+xml",eps:"application/postscript",epub:"application/epub+zip",exe:"application/octet-stream",exi:"application/exi",exp:"application/express",exr:"image/aces",ez:"application/andrew-inset",fdf:"application/fdf",fdt:"application/fdt+xml",fits:"image/fits",g3:"image/g3fax",gbr:"application/rpki-ghostbusters",geojson:"application/geo+json",gif:"image/gif",glb:"model/gltf-binary",gltf:"model/gltf+json",gml:"application/gml+xml",gpx:"application/gpx+xml",gram:"application/srgs",grxml:"application/srgs+xml",gxf:"application/gxf",gz:"application/gzip",h261:"video/h261",h263:"video/h263",h264:"video/h264",heic:"image/heic",heics:"image/heic-sequence",heif:"image/heif",heifs:"image/heif-sequence",hej2:"image/hej2k",held:"application/atsc-held+xml",hjson:"application/hjson",hlp:"application/winhlp",hqx:"application/mac-binhex40",hsj2:"image/hsj2",htm:"text/html",html:"text/html",ics:"text/calendar",ief:"image/ief",ifb:"text/calendar",iges:"model/iges",igs:"model/iges",img:"application/octet-stream",in:"text/plain",ini:"text/plain",ink:"application/inkml+xml",inkml:"application/inkml+xml",ipfix:"application/ipfix",iso:"application/octet-stream",its:"application/its+xml",jade:"text/jade",jar:"application/java-archive",jhc:"image/jphc",jls:"image/jls",jp2:"image/jp2",jpe:"image/jpeg",jpeg:"image/jpeg",jpf:"image/jpx",jpg:"image/jpeg",jpg2:"image/jp2",jpgm:"image/jpm",jpgv:"video/jpeg",jph:"image/jph",jpm:"image/jpm",jpx:"image/jpx",js:"text/javascript",json:"application/json",json5:"application/json5",jsonld:"application/ld+json",jsonml:"application/jsonml+json",jsx:"text/jsx",jt:"model/jt",jxr:"image/jxr",jxra:"image/jxra",jxrs:"image/jxrs",jxs:"image/jxs",jxsc:"image/jxsc",jxsi:"image/jxsi",jxss:"image/jxss",kar:"audio/midi",ktx:"image/ktx",ktx2:"image/ktx2",less:"text/less",lgr:"application/lgr+xml",list:"text/plain",litcoffee:"text/coffeescript",log:"text/plain",lostxml:"application/lost+xml",lrf:"application/octet-stream",m1v:"video/mpeg",m21:"application/mp21",m2a:"audio/mpeg",m2v:"video/mpeg",m3a:"audio/mpeg",m4a:"audio/mp4",m4p:"application/mp4",m4s:"video/iso.segment",ma:"application/mathematica",mads:"application/mads+xml",maei:"application/mmt-aei+xml",man:"text/troff",manifest:"text/cache-manifest",map:"application/json",mar:"application/octet-stream",markdown:"text/markdown",mathml:"application/mathml+xml",mb:"application/mathematica",mbox:"application/mbox",md:"text/markdown",mdx:"text/mdx",me:"text/troff",mesh:"model/mesh",meta4:"application/metalink4+xml",metalink:"application/metalink+xml",mets:"application/mets+xml",mft:"application/rpki-manifest",mid:"audio/midi",midi:"audio/midi",mime:"message/rfc822",mj2:"video/mj2",mjp2:"video/mj2",mjs:"text/javascript",mml:"text/mathml",mods:"application/mods+xml",mov:"video/quicktime",mp2:"audio/mpeg",mp21:"application/mp21",mp2a:"audio/mpeg",mp3:"audio/mpeg",mp4:"video/mp4",mp4a:"audio/mp4",mp4s:"application/mp4",mp4v:"video/mp4",mpd:"application/dash+xml",mpe:"video/mpeg",mpeg:"video/mpeg",mpf:"application/media-policy-dataset+xml",mpg:"video/mpeg",mpg4:"video/mp4",mpga:"audio/mpeg",mpp:"application/dash-patch+xml",mrc:"application/marc",mrcx:"application/marcxml+xml",ms:"text/troff",mscml:"application/mediaservercontrol+xml",msh:"model/mesh",msi:"application/octet-stream",msix:"application/msix",msixbundle:"application/msixbundle",msm:"application/octet-stream",msp:"application/octet-stream",mtl:"model/mtl",musd:"application/mmt-usd+xml",mxf:"application/mxf",mxmf:"audio/mobile-xmf",mxml:"application/xv+xml",n3:"text/n3",nb:"application/mathematica",nq:"application/n-quads",nt:"application/n-triples",obj:"model/obj",oda:"application/oda",oga:"audio/ogg",ogg:"audio/ogg",ogv:"video/ogg",ogx:"application/ogg",omdoc:"application/omdoc+xml",onepkg:"application/onenote",onetmp:"application/onenote",onetoc:"application/onenote",onetoc2:"application/onenote",opf:"application/oebps-package+xml",opus:"audio/ogg",otf:"font/otf",owl:"application/rdf+xml",oxps:"application/oxps",p10:"application/pkcs10",p7c:"application/pkcs7-mime",p7m:"application/pkcs7-mime",p7s:"application/pkcs7-signature",p8:"application/pkcs8",pdf:"application/pdf",pfr:"application/font-tdpfr",pgp:"application/pgp-encrypted",pkg:"application/octet-stream",pki:"application/pkixcmp",pkipath:"application/pkix-pkipath",pls:"application/pls+xml",png:"image/png",prc:"model/prc",prf:"application/pics-rules",provx:"application/provenance+xml",ps:"application/postscript",pskcxml:"application/pskc+xml",pti:"image/prs.pti",qt:"video/quicktime",raml:"application/raml+yaml",rapd:"application/route-apd+xml",rdf:"application/rdf+xml",relo:"application/p2p-overlay+xml",rif:"application/reginfo+xml",rl:"application/resource-lists+xml",rld:"application/resource-lists-diff+xml",rmi:"audio/midi",rnc:"application/relax-ng-compact-syntax",rng:"application/xml",roa:"application/rpki-roa",roff:"text/troff",rq:"application/sparql-query",rs:"application/rls-services+xml",rsat:"application/atsc-rsat+xml",rsd:"application/rsd+xml",rsheet:"application/urc-ressheet+xml",rss:"application/rss+xml",rtf:"text/rtf",rtx:"text/richtext",rusd:"application/route-usd+xml",s3m:"audio/s3m",sbml:"application/sbml+xml",scq:"application/scvp-cv-request",scs:"application/scvp-cv-response",sdp:"application/sdp",senmlx:"application/senml+xml",sensmlx:"application/sensml+xml",ser:"application/java-serialized-object",setpay:"application/set-payment-initiation",setreg:"application/set-registration-initiation",sgi:"image/sgi",sgm:"text/sgml",sgml:"text/sgml",shex:"text/shex",shf:"application/shf+xml",shtml:"text/html",sieve:"application/sieve",sig:"application/pgp-signature",sil:"audio/silk",silo:"model/mesh",siv:"application/sieve",slim:"text/slim",slm:"text/slim",sls:"application/route-s-tsid+xml",smi:"application/smil+xml",smil:"application/smil+xml",snd:"audio/basic",so:"application/octet-stream",spdx:"text/spdx",spp:"application/scvp-vp-response",spq:"application/scvp-vp-request",spx:"audio/ogg",sql:"application/sql",sru:"application/sru+xml",srx:"application/sparql-results+xml",ssdl:"application/ssdl+xml",ssml:"application/ssml+xml",stk:"application/hyperstudio",stl:"model/stl",stpx:"model/step+xml",stpxz:"model/step-xml+zip",stpz:"model/step+zip",styl:"text/stylus",stylus:"text/stylus",svg:"image/svg+xml",svgz:"image/svg+xml",swidtag:"application/swid+xml",t:"text/troff",t38:"image/t38",td:"application/urc-targetdesc+xml",tei:"application/tei+xml",teicorpus:"application/tei+xml",text:"text/plain",tfi:"application/thraud+xml",tfx:"image/tiff-fx",tif:"image/tiff",tiff:"image/tiff",toml:"application/toml",tr:"text/troff",trig:"application/trig",ts:"video/mp2t",tsd:"application/timestamped-data",tsv:"text/tab-separated-values",ttc:"font/collection",ttf:"font/ttf",ttl:"text/turtle",ttml:"application/ttml+xml",txt:"text/plain",u3d:"model/u3d",u8dsn:"message/global-delivery-status",u8hdr:"message/global-headers",u8mdn:"message/global-disposition-notification",u8msg:"message/global",ubj:"application/ubjson",uri:"text/uri-list",uris:"text/uri-list",urls:"text/uri-list",vcard:"text/vcard",vrml:"model/vrml",vtt:"text/vtt",vxml:"application/voicexml+xml",war:"application/java-archive",wasm:"application/wasm",wav:"audio/wav",weba:"audio/webm",webm:"video/webm",webmanifest:"application/manifest+json",webp:"image/webp",wgsl:"text/wgsl",wgt:"application/widget",wif:"application/watcherinfo+xml",wmf:"image/wmf",woff:"font/woff",woff2:"font/woff2",wrl:"model/vrml",wsdl:"application/wsdl+xml",wspolicy:"application/wspolicy+xml",x3d:"model/x3d+xml",x3db:"model/x3d+fastinfoset",x3dbz:"model/x3d+binary",x3dv:"model/x3d-vrml",x3dvz:"model/x3d+vrml",x3dz:"model/x3d+xml",xaml:"application/xaml+xml",xav:"application/xcap-att+xml",xca:"application/xcap-caps+xml",xcs:"application/calendar+xml",xdf:"application/xcap-diff+xml",xdssc:"application/dssc+xml",xel:"application/xcap-el+xml",xenc:"application/xenc+xml",xer:"application/patch-ops-error+xml",xfdf:"application/xfdf",xht:"application/xhtml+xml",xhtml:"application/xhtml+xml",xhvml:"application/xv+xml",xlf:"application/xliff+xml",xm:"audio/xm",xml:"text/xml",xns:"application/xcap-ns+xml",xop:"application/xop+xml",xpl:"application/xproc+xml",xsd:"application/xml",xsf:"application/prs.xsf+xml",xsl:"application/xml",xslt:"application/xml",xspf:"application/xspf+xml",xvm:"application/xv+xml",xvml:"application/xv+xml",yaml:"text/yaml",yang:"application/yang",yin:"application/yin+xml",yml:"text/yaml",zip:"application/zip"};function H(t){let i=(""+t).trim().toLowerCase(),a=i.lastIndexOf(".");return X[~a?i.substring(++a):i]}const ui=t=>{let i=["B","KB","MB","GB","PB"],a=0;for(;t>1024;)t/=1024,a++;let e=i[a];return t.toFixed(1)+" "+e},gi=()=>!0;function F(t,{autoplay:i}){async function a(){i&&await t.play()}return t.addEventListener("loadeddata",a),{destroy(){t.removeEventListener("loadeddata",a)}}}async function vi(){const t=new G,i="https://unpkg.com/@ffmpeg/core@0.12.4/dist/esm";return await t.load({coreURL:await y(`${i}/ffmpeg-core.js`,"text/javascript"),wasmURL:await y(`${i}/ffmpeg-core.wasm`,"application/wasm")}),t}async function hi(t,i,a,e){const p=e.src,c=H(e.src)||"video/mp4",u=await y(p,c),v=await(await fetch(u)).blob(),g=K(c)||"mp4",n=`input.${g}`,x=`output.${g}`;try{if(i===0&&a===0)return v;await t.writeFile(n,new Uint8Array(await v.arrayBuffer()));let f=["-i",n,...i!==0?["-ss",i.toString()]:[],...a!==0?["-to",a.toString()]:[],"-c:a","copy",x];await t.exec(f);const b=await t.readFile(x);return new Blob([b],{type:`video/${g}`})}catch(f){return console.error("Error initializing FFmpeg:",f),v}}const K=t=>({"video/mp4":"mp4","video/webm":"webm","video/ogg":"ogv","video/quicktime":"mov","video/x-msvideo":"avi","video/x-matroska":"mkv","video/mpeg":"mpeg","video/3gpp":"3gp","video/3gpp2":"3g2","video/h261":"h261","video/h263":"h263","video/h264":"h264","video/jpeg":"jpgv","video/jpm":"jpm","video/mj2":"mj2","video/mpv":"mpv","video/vnd.ms-playready.media.pyv":"pyv","video/vnd.uvvu.mp4":"uvu","video/vnd.vivo":"viv","video/x-f4v":"f4v","video/x-fli":"fli","video/x-flv":"flv","video/x-m4v":"m4v","video/x-ms-asf":"asf","video/x-ms-wm":"wm","video/x-ms-wmv":"wmv","video/x-ms-wmx":"wmx","video/x-ms-wvx":"wvx","video/x-sgi-movie":"movie","video/x-smv":"smv"})[t]||null;const{SvelteComponent:Y,action_destroyer:J,add_render_callback:Q,assign:R,attr:E,binding_callbacks:Z,create_slot:$,detach:w,element:k,exclude_internal_props:L,get_all_dirty_from_scope:ii,get_slot_changes:ai,init:ti,insert:_,is_function:ei,listen:h,raf:oi,run_all:li,safe_not_equal:pi,space:ni,src_url_equal:D,toggle_class:I,transition_in:si,transition_out:mi,update_slot_base:ci}=window.__gradio__svelte__internal,{createEventDispatcher:di}=window.__gradio__svelte__internal;function ri(t){let i,a,e,p,c,u=!1,d,v=!0,g,n,x,f;const b=t[16].default,r=$(b,t,t[15],null);function j(){cancelAnimationFrame(d),e.paused||(d=oi(j),u=!0),t[17].call(e)}return{c(){i=k("div"),i.innerHTML='<span class="load-wrap svelte-1y0s5gv"><span class="loader svelte-1y0s5gv"></span></span>',a=ni(),e=k("video"),r&&r.c(),E(i,"class","overlay svelte-1y0s5gv"),I(i,"hidden",!t[9]),D(e.src,p=t[10])||E(e,"src",p),e.muted=t[4],e.playsInline=t[5],E(e,"preload",t[6]),e.autoplay=t[7],e.controls=t[8],E(e,"data-testid",c=t[12]["data-testid"]),E(e,"crossorigin","anonymous"),t[1]===void 0&&Q(()=>t[18].call(e))},m(o,m){_(o,i,m),_(o,a,m),_(o,e,m),r&&r.m(e,null),t[20](e),n=!0,x||(f=[h(e,"loadeddata",t[11].bind(null,"loadeddata")),h(e,"click",t[11].bind(null,"click")),h(e,"play",t[11].bind(null,"play")),h(e,"pause",t[11].bind(null,"pause")),h(e,"ended",t[11].bind(null,"ended")),h(e,"mouseover",t[11].bind(null,"mouseover")),h(e,"mouseout",t[11].bind(null,"mouseout")),h(e,"focus",t[11].bind(null,"focus")),h(e,"blur",t[11].bind(null,"blur")),h(e,"timeupdate",j),h(e,"durationchange",t[18]),h(e,"play",t[19]),h(e,"pause",t[19]),J(g=F.call(null,e,{autoplay:t[7]??!1}))],x=!0)},p(o,[m]){(!n||m&512)&&I(i,"hidden",!o[9]),r&&r.p&&(!n||m&32768)&&ci(r,b,o,o[15],n?ai(b,o[15],m,null):ii(o[15]),null),(!n||m&1024&&!D(e.src,p=o[10]))&&E(e,"src",p),(!n||m&16)&&(e.muted=o[4]),(!n||m&32)&&(e.playsInline=o[5]),(!n||m&64)&&E(e,"preload",o[6]),(!n||m&128)&&(e.autoplay=o[7]),(!n||m&256)&&(e.controls=o[8]),(!n||m&4096&&c!==(c=o[12]["data-testid"]))&&E(e,"data-testid",c),!u&&m&1&&!isNaN(o[0])&&(e.currentTime=o[0]),u=!1,m&4&&v!==(v=o[2])&&e[v?"pause":"play"](),g&&ei(g.update)&&m&128&&g.update.call(null,{autoplay:o[7]??!1})},i(o){n||(si(r,o),n=!0)},o(o){mi(r,o),n=!1},d(o){o&&(w(i),w(a),w(e)),r&&r.d(o),t[20](null),x=!1,li(f)}}}function xi(t,i,a){let{$$slots:e={},$$scope:p}=i,{src:c=void 0}=i,{muted:u=void 0}=i,{playsinline:d=void 0}=i,{preload:v=void 0}=i,{autoplay:g=void 0}=i,{controls:n=void 0}=i,{currentTime:x=void 0}=i,{duration:f=void 0}=i,{paused:b=void 0}=i,{node:r=void 0}=i,{processingVideo:j=!1}=i,o,m;const O=di();function A(){x=this.currentTime,a(0,x)}function N(){f=this.duration,a(1,f)}function T(){b=this.paused,a(2,b)}function U(l){Z[l?"unshift":"push"](()=>{r=l,a(3,r)})}return t.$$set=l=>{a(12,i=R(R({},i),L(l))),"src"in l&&a(13,c=l.src),"muted"in l&&a(4,u=l.muted),"playsinline"in l&&a(5,d=l.playsinline),"preload"in l&&a(6,v=l.preload),"autoplay"in l&&a(7,g=l.autoplay),"controls"in l&&a(8,n=l.controls),"currentTime"in l&&a(0,x=l.currentTime),"duration"in l&&a(1,f=l.duration),"paused"in l&&a(2,b=l.paused),"node"in l&&a(3,r=l.node),"processingVideo"in l&&a(9,j=l.processingVideo),"$$scope"in l&&a(15,p=l.$$scope)},t.$$.update=()=>{if(t.$$.dirty&24576){a(10,o=c),a(14,m=c);const l=c;z(l).then(q=>{m===l&&a(10,o=q)})}},i=L(i),[x,f,b,r,u,d,v,g,n,j,o,O,i,c,m,p,e,A,N,T,U]}class bi extends Y{constructor(i){super(),ti(this,i,xi,ri,pi,{src:13,muted:4,playsinline:5,preload:6,autoplay:7,controls:8,currentTime:0,duration:1,paused:2,node:3,processingVideo:9})}}export{bi as V,gi as a,F as b,vi as l,ui as p,hi as t};
//# sourceMappingURL=Video-fd4d29ec.js.map
