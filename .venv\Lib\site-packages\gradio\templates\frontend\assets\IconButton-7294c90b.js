import"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";const{SvelteComponent:S,append:_,attr:g,bubble:j,create_component:v,destroy_component:A,detach:q,element:m,init:D,insert:B,listen:E,mount_component:F,safe_not_equal:G,set_data:H,set_style:h,space:J,text:K,toggle_class:b,transition_in:L,transition_out:M}=window.__gradio__svelte__internal;function P(n){let e,i;return{c(){e=m("span"),i=K(n[1]),g(e,"class","svelte-p87ime")},m(f,u){B(f,e,u),_(e,i)},p(f,u){u&2&&H(i,f[1])},d(f){f&&q(e)}}}function N(n){let e,i,f,u,o,d,r,s=n[2]&&P(n);return u=new n[0]({}),{c(){e=m("button"),s&&s.c(),i=J(),f=m("div"),v(u.$$.fragment),g(f,"class","svelte-p87ime"),b(f,"small",n[4]==="small"),b(f,"large",n[4]==="large"),e.disabled=n[7],g(e,"aria-label",n[1]),g(e,"aria-haspopup",n[8]),g(e,"title",n[1]),g(e,"class","svelte-p87ime"),b(e,"pending",n[3]),b(e,"padded",n[5]),b(e,"highlight",n[6]),b(e,"transparent",n[9]),h(e,"color",!n[7]&&n[11]?n[11]:"var(--block-label-text-color)"),h(e,"--bg-color",n[7]?"auto":n[10])},m(l,t){B(l,e,t),s&&s.m(e,null),_(e,i),_(e,f),F(u,f,null),o=!0,d||(r=E(e,"click",n[13]),d=!0)},p(l,[t]){l[2]?s?s.p(l,t):(s=P(l),s.c(),s.m(e,i)):s&&(s.d(1),s=null),(!o||t&16)&&b(f,"small",l[4]==="small"),(!o||t&16)&&b(f,"large",l[4]==="large"),(!o||t&128)&&(e.disabled=l[7]),(!o||t&2)&&g(e,"aria-label",l[1]),(!o||t&256)&&g(e,"aria-haspopup",l[8]),(!o||t&2)&&g(e,"title",l[1]),(!o||t&8)&&b(e,"pending",l[3]),(!o||t&32)&&b(e,"padded",l[5]),(!o||t&64)&&b(e,"highlight",l[6]),(!o||t&512)&&b(e,"transparent",l[9]),t&2176&&h(e,"color",!l[7]&&l[11]?l[11]:"var(--block-label-text-color)"),t&1152&&h(e,"--bg-color",l[7]?"auto":l[10])},i(l){o||(L(u.$$.fragment,l),o=!0)},o(l){M(u.$$.fragment,l),o=!1},d(l){l&&q(e),s&&s.d(),A(u),d=!1,r()}}}function O(n,e,i){let f,{Icon:u}=e,{label:o=""}=e,{show_label:d=!1}=e,{pending:r=!1}=e,{size:s="small"}=e,{padded:l=!0}=e,{highlight:t=!1}=e,{disabled:k=!1}=e,{hasPopup:w=!1}=e,{color:c="var(--block-label-text-color)"}=e,{transparent:I=!1}=e,{background:z="var(--background-fill-primary)"}=e;function C(a){j.call(this,n,a)}return n.$$set=a=>{"Icon"in a&&i(0,u=a.Icon),"label"in a&&i(1,o=a.label),"show_label"in a&&i(2,d=a.show_label),"pending"in a&&i(3,r=a.pending),"size"in a&&i(4,s=a.size),"padded"in a&&i(5,l=a.padded),"highlight"in a&&i(6,t=a.highlight),"disabled"in a&&i(7,k=a.disabled),"hasPopup"in a&&i(8,w=a.hasPopup),"color"in a&&i(12,c=a.color),"transparent"in a&&i(9,I=a.transparent),"background"in a&&i(10,z=a.background)},n.$$.update=()=>{n.$$.dirty&4160&&i(11,f=t?"var(--color-accent)":c)},[u,o,d,r,s,l,t,k,w,I,z,f,c,C]}class R extends S{constructor(e){super(),D(this,e,O,N,G,{Icon:0,label:1,show_label:2,pending:3,size:4,padded:5,highlight:6,disabled:7,hasPopup:8,color:12,transparent:9,background:10})}}export{R as I};
//# sourceMappingURL=IconButton-7294c90b.js.map
