import*as oe from"./svelte/svelte.js";(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))o(n);new MutationObserver(n=>{for(const r of n)if(r.type==="childList")for(const i of r.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&o(i)}).observe(document,{childList:!0,subtree:!0});function t(n){const r={};return n.integrity&&(r.integrity=n.integrity),n.referrerPolicy&&(r.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?r.credentials="include":n.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function o(n){if(n.ep)return;n.ep=!0;const r=t(n);fetch(n.href,r)}})();const Ie="modulepreload",ze=function(e,s){return new URL(e,s).href},ye={},ce=function(s,t,o){if(!t||t.length===0)return s();const n=document.getElementsByTagName("link");return Promise.all(t.map(r=>{if(r=ze(r,o),r in ye)return;ye[r]=!0;const i=r.endsWith(".css"),f=i?'[rel="stylesheet"]':"";if(!!o)for(let u=n.length-1;u>=0;u--){const p=n[u];if(p.href===r&&(!i||p.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${r}"]${f}`))return;const l=document.createElement("link");if(l.rel=i?"stylesheet":Ie,i||(l.as="script",l.crossOrigin=""),l.href=r,document.head.appendChild(l),i)return new Promise((u,p)=>{l.addEventListener("load",u),l.addEventListener("error",()=>p(new Error(`Unable to preload CSS for ${r}`)))})})).then(()=>s())};var ie=new Intl.Collator(0,{numeric:1}).compare;function be(e,s,t){return e=e.split("."),s=s.split("."),ie(e[0],s[0])||ie(e[1],s[1])||(s[2]=s.slice(2).join("."),t=/[.-]/.test(e[2]=e.slice(2).join(".")),t==/[.-]/.test(s[2])?ie(e[2],s[2]):t?-1:1)}function Oe(e,s,t){return s.startsWith("http://")||s.startsWith("https://")?t?e:s:e+s}function ae(e){if(e.startsWith("http")){const{protocol:s,host:t}=new URL(e);return t.endsWith("hf.space")?{ws_protocol:"wss",host:t,http_protocol:s}:{ws_protocol:s==="https:"?"wss":"ws",http_protocol:s,host:t}}else if(e.startsWith("file:"))return{ws_protocol:"ws",http_protocol:"http:",host:"lite.local"};return{ws_protocol:"wss",http_protocol:"https:",host:e}}const Ne=/^[^\/]*\/[^\/]*$/,Fe=/.*hf\.space\/{0,1}$/;async function We(e,s){const t={};s&&(t.Authorization=`Bearer ${s}`);const o=e.trim();if(Ne.test(o))try{const n=await fetch(`https://huggingface.co/api/spaces/${o}/host`,{headers:t});if(n.status!==200)throw new Error("Space metadata could not be loaded.");const r=(await n.json()).host;return{space_id:e,...ae(r)}}catch(n){throw new Error("Space metadata could not be loaded."+n.message)}if(Fe.test(o)){const{ws_protocol:n,http_protocol:r,host:i}=ae(o);return{space_id:i.replace(".hf.space",""),ws_protocol:n,http_protocol:r,host:i}}return{space_id:!1,...ae(o)}}function Je(e){let s={};return e.forEach(({api_name:t},o)=>{t&&(s[t]=o)}),s}const Ge=/^(?=[^]*\b[dD]iscussions{0,1}\b)(?=[^]*\b[dD]isabled\b)[^]*$/;async function ve(e){try{const t=(await fetch(`https://huggingface.co/api/spaces/${e}/discussions`,{method:"HEAD"})).headers.get("x-error-message");return!(t&&Ge.test(t))}catch{return!1}}function Me(e,s,t,o){if(s.length===0){if(t==="replace")return o;if(t==="append")return e+o;throw new Error(`Unsupported action: ${t}`)}let n=e;for(let i=0;i<s.length-1;i++)n=n[s[i]];const r=s[s.length-1];switch(t){case"replace":n[r]=o;break;case"append":n[r]+=o;break;case"add":Array.isArray(n)?n.splice(Number(r),0,o):n[r]=o;break;case"delete":Array.isArray(n)?n.splice(Number(r),1):delete n[r];break;default:throw new Error(`Unknown action: ${t}`)}return e}function Ke(e,s){return s.forEach(([t,o,n])=>{e=Me(e,o,t,n)}),e}async function st(e,s,t,o=ue){let n=(Array.isArray(e)?e:[e]).map(r=>r.blob);return await Promise.all(await o(s,n,void 0,t).then(async r=>{if(r.error)throw new Error(r.error);return r.files?r.files.map((i,f)=>new ge({...e[f],path:i,url:s+"/file="+i})):[]}))}async function rt(e,s){return e.map((t,o)=>new ge({path:t.name,orig_name:t.name,blob:t,size:t.size,mime_type:t.type,is_stream:s}))}class ge{constructor({path:s,url:t,orig_name:o,size:n,blob:r,is_stream:i,mime_type:f,alt_text:w}){this.meta={_type:"gradio.FileData"},this.path=s,this.url=t,this.orig_name=o,this.size=n,this.blob=t?void 0:r,this.is_stream=i,this.mime_type=f,this.alt_text=w}}const ke="This application is too busy. Keep trying!",M="Connection errored out.";let Te;function Ve(e,s){return{post_data:t,upload_files:o,client:n,handle_blob:r};async function t(i,f,w){const l={"Content-Type":"application/json"};w&&(l.Authorization=`Bearer ${w}`);try{var u=await e(i,{method:"POST",body:JSON.stringify(f),headers:l})}catch{return[{error:M},500]}let p,h;try{p=await u.json(),h=u.status}catch(D){p={error:`Could not parse server response: ${D}`},h=500}return[p,h]}async function o(i,f,w,l){const u={};w&&(u.Authorization=`Bearer ${w}`);const p=1e3,h=[];for(let A=0;A<f.length;A+=p){const U=f.slice(A,A+p),O=new FormData;U.forEach(z=>{O.append("files",z)});try{const z=l?`${i}/upload?upload_id=${l}`:`${i}/upload`;var D=await e(z,{method:"POST",body:O,headers:u})}catch{return{error:M}}const W=await D.json();h.push(...W)}return{files:h}}async function n(i,f={}){return new Promise(async w=>{const{status_callback:l,hf_token:u}=f,p={predict:Pe,submit:_e,view_api:we,component_server:Ue};if((typeof window>"u"||!("WebSocket"in window))&&!global.Websocket){const d=await ce(()=>import("./wrapper-6f348d45-ccf15bad.js"),["./wrapper-6f348d45-ccf15bad.js","./__vite-browser-external-d8e3c0c7.js"],import.meta.url);Te=(await ce(()=>import("./__vite-browser-external-d8e3c0c7.js").then(C=>C._),[],import.meta.url)).Blob,global.WebSocket=d.WebSocket}const{ws_protocol:h,http_protocol:D,host:A,space_id:U}=await We(i,u),O=Math.random().toString(36).substring(2),W={};let z=!1,K={},J={},V=null;const I={},ee=new Set;let m,Q={},te=!1;u&&U&&(te=await Ye(U,u));async function me(d){if(m=d,window.location.protocol==="https:"&&(m.root=m.root.replace("http://","https://")),Q=Je(d?.dependencies||[]),m.auth_required)return{config:m,...p};try{H=await we(m)}catch(C){console.error(`Could not get api details: ${C.message}`)}return{config:m,...p}}let H;async function Re(d){if(l&&l(d),d.status==="running")try{m=await Ce(e,`${D}//${A}`,u);const C=await me(m);w(C)}catch(C){console.error(C),l&&l({status:"error",message:"Could not load this space.",load_status:"error",detail:"NOT_FOUND"})}}try{m=await Ce(e,`${D}//${A}`,u);const d=await me(m),C=new EventSource(`${m.root}/heartbeat/${O}`);w(d)}catch(d){console.error(d),U?de(U,Ne.test(U)?"space_name":"subdomain",Re):l&&l({status:"error",message:"Could not load this space.",load_status:"error",detail:"NOT_FOUND"})}function Pe(d,C,P){let b=!1,a=!1,T;if(typeof d=="number")T=m.dependencies[d];else{const E=d.replace(/^\//,"");T=m.dependencies[Q[E]]}if(T.types.continuous)throw new Error("Cannot call predict on this function as it may run forever. Use submit instead");return new Promise((E,j)=>{const x=_e(d,C,P);let c;x.on("data",B=>{a&&(x.destroy(),E(B)),b=!0,c=B}).on("status",B=>{B.stage==="error"&&j(B),B.stage==="complete"&&(a=!0,b&&(x.destroy(),E(c)))})})}function _e(d,C,P,b=null){let a,T;if(typeof d=="number")a=d,T=H.unnamed_endpoints[a];else{const S=d.replace(/^\//,"");a=Q[S],T=H.named_endpoints[d.trim()]}if(typeof a!="number")throw new Error("There is no endpoint matching that name of fn_index matching that number.");let E,j,x=m.protocol??"ws";const c=typeof d=="number"?"/predict":d;let B,R=null,L=!1;const Y={};let F="";typeof window<"u"&&(F=new URLSearchParams(window.location.search).toString()),r(`${m.root}`,C,T,u).then(S=>{if(B={data:S||[],event_data:P,fn_index:a,trigger_id:b},Qe(a,m))g({type:"status",endpoint:c,stage:"pending",queue:!1,fn_index:a,time:new Date}),t(`${m.root}/run${c.startsWith("/")?c:`/${c}`}${F?"?"+F:""}`,{...B,session_hash:O},u).then(([$,v])=>{const N=$.data;v==200?(g({type:"data",endpoint:c,fn_index:a,data:N,time:new Date}),g({type:"status",endpoint:c,fn_index:a,stage:"complete",eta:$.average_duration,queue:!1,time:new Date})):g({type:"status",stage:"error",endpoint:c,fn_index:a,message:$.error,queue:!1,time:new Date})}).catch($=>{g({type:"status",stage:"error",message:$.message,endpoint:c,fn_index:a,queue:!1,time:new Date})});else if(x=="ws"){g({type:"status",stage:"pending",queue:!0,endpoint:c,fn_index:a,time:new Date});let $=new URL(`${h}://${Oe(A,m.path,!0)}
							/queue/join${F?"?"+F:""}`);te&&$.searchParams.set("__sign",te),E=new WebSocket($),E.onclose=v=>{v.wasClean||g({type:"status",stage:"error",broken:!0,message:M,queue:!0,endpoint:c,fn_index:a,time:new Date})},E.onmessage=function(v){const N=JSON.parse(v.data),{type:q,status:_,data:y}=le(N,W[a]);if(q==="update"&&_&&!L)g({type:"status",endpoint:c,fn_index:a,time:new Date,..._}),_.stage==="error"&&E.close();else if(q==="hash"){E.send(JSON.stringify({fn_index:a,session_hash:O}));return}else q==="data"?E.send(JSON.stringify({...B,session_hash:O})):q==="complete"?L=_:q==="log"?g({type:"log",log:y.log,level:y.level,endpoint:c,fn_index:a}):q==="generating"&&g({type:"status",time:new Date,..._,stage:_?.stage,queue:!0,endpoint:c,fn_index:a});y&&(g({type:"data",time:new Date,data:y.data,endpoint:c,fn_index:a}),L&&(g({type:"status",time:new Date,...L,stage:_?.stage,queue:!0,endpoint:c,fn_index:a}),E.close()))},be(m.version||"2.0.0","3.6")<0&&addEventListener("open",()=>E.send(JSON.stringify({hash:O})))}else if(x=="sse"){g({type:"status",stage:"pending",queue:!0,endpoint:c,fn_index:a,time:new Date});var k=new URLSearchParams({fn_index:a.toString(),session_hash:O}).toString();let $=new URL(`${m.root}/queue/join?${F?F+"&":""}${k}`);j=s($),j.onmessage=async function(v){const N=JSON.parse(v.data),{type:q,status:_,data:y}=le(N,W[a]);if(q==="update"&&_&&!L)g({type:"status",endpoint:c,fn_index:a,time:new Date,..._}),_.stage==="error"&&j.close();else if(q==="data"){R=N.event_id;let[G,Be]=await t(`${m.root}/queue/data`,{...B,session_hash:O,event_id:R},u);Be!==200&&(g({type:"status",stage:"error",message:M,queue:!0,endpoint:c,fn_index:a,time:new Date}),j.close())}else q==="complete"?L=_:q==="log"?g({type:"log",log:y.log,level:y.level,endpoint:c,fn_index:a}):q==="generating"&&g({type:"status",time:new Date,..._,stage:_?.stage,queue:!0,endpoint:c,fn_index:a});y&&(g({type:"data",time:new Date,data:y.data,endpoint:c,fn_index:a}),L&&(g({type:"status",time:new Date,...L,stage:_?.stage,queue:!0,endpoint:c,fn_index:a}),j.close()))}}else(x=="sse_v1"||x=="sse_v2"||x=="sse_v2.1"||x=="sse_v3")&&(g({type:"status",stage:"pending",queue:!0,endpoint:c,fn_index:a,time:new Date}),t(`${m.root}/queue/join?${F}`,{...B,session_hash:O},u).then(([$,v])=>{if(v===503)g({type:"status",stage:"error",message:ke,queue:!0,endpoint:c,fn_index:a,time:new Date});else if(v!==200)g({type:"status",stage:"error",message:M,queue:!0,endpoint:c,fn_index:a,time:new Date});else{R=$.event_id;let N=async function(q){try{const{type:_,status:y,data:G}=le(q,W[a]);if(_=="heartbeat")return;if(_==="update"&&y&&!L)g({type:"status",endpoint:c,fn_index:a,time:new Date,...y});else if(_==="complete")L=y;else if(_=="unexpected_error")console.error("Unexpected error",y?.message),g({type:"status",stage:"error",message:y?.message||"An Unexpected Error Occurred!",queue:!0,endpoint:c,fn_index:a,time:new Date});else if(_==="log"){g({type:"log",log:G.log,level:G.level,endpoint:c,fn_index:a});return}else _==="generating"&&(g({type:"status",time:new Date,...y,stage:y?.stage,queue:!0,endpoint:c,fn_index:a}),G&&["sse_v2","sse_v2.1","sse_v3"].includes(x)&&je(R,G));G&&(g({type:"data",time:new Date,data:G.data,endpoint:c,fn_index:a}),L&&g({type:"status",time:new Date,...L,stage:y?.stage,queue:!0,endpoint:c,fn_index:a})),(y?.stage==="complete"||y?.stage==="error")&&(I[R]&&delete I[R],R in J&&delete J[R])}catch(_){console.error("Unexpected client exception",_),g({type:"status",stage:"error",message:"An Unexpected Error Occurred!",queue:!0,endpoint:c,fn_index:a,time:new Date}),["sse_v2","sse_v2.1"].includes(x)&&X()}};R in K&&(K[R].forEach(q=>N(q)),delete K[R]),I[R]=N,ee.add(R),z||Le()}}))});function je(S,k){!J[S]?(J[S]=[],k.data.forEach((v,N)=>{J[S][N]=v})):k.data.forEach((v,N)=>{let q=Ke(J[S][N],v);J[S][N]=q,k.data[N]=q})}function g(S){const $=Y[S.type]||[];$?.forEach(v=>v(S))}function se(S,k){const $=Y,v=$[S]||[];return $[S]=v,v?.push(k),{on:se,off:Z,cancel:re,destroy:ne}}function Z(S,k){const $=Y;let v=$[S]||[];return v=v?.filter(N=>N!==k),$[S]=v,{on:se,off:Z,cancel:re,destroy:ne}}async function re(){const S={stage:"complete",queue:!1,time:new Date};L=S,g({...S,type:"status",endpoint:c,fn_index:a});let k={};x==="ws"?(E&&E.readyState===0?E.addEventListener("open",()=>{E.close()}):E.close(),k={fn_index:a,session_hash:O}):(j.close(),k={event_id:R});try{await e(`${m.root}/reset`,{headers:{"Content-Type":"application/json"},method:"POST",body:JSON.stringify(k)})}catch{console.warn("The `/reset` endpoint could not be called. Subsequent endpoint results may be unreliable.")}}function ne(){for(const S in Y)Y[S].forEach(k=>{Z(S,k)})}return{on:se,off:Z,cancel:re,destroy:ne}}function Le(){z=!0;let d=new URLSearchParams({session_hash:O}).toString(),C=new URL(`${m.root}/queue/data?${d}`);V=s(C),V.onmessage=async function(P){let b=JSON.parse(P.data);const a=b.event_id;if(!a)await Promise.all(Object.keys(I).map(T=>I[T](b)));else if(I[a]){b.msg==="process_completed"&&["sse","sse_v1","sse_v2","sse_v2.1"].includes(m.protocol)&&(ee.delete(a),ee.size===0&&X());let T=I[a];window.setTimeout(T,0,b)}else K[a]||(K[a]=[]),K[a].push(b);b.msg==="close_stream"&&X()},V.onerror=async function(P){await Promise.all(Object.keys(I).map(b=>I[b]({msg:"unexpected_error",message:M}))),X()}}function X(){z=!1,V?.close()}async function Ue(d,C,P){var b;const a={"Content-Type":"application/json"};u&&(a.Authorization=`Bearer ${u}`);let T,E=m.components.find(c=>c.id===d);(b=E?.props)!=null&&b.root_url?T=E.props.root_url:T=m.root;const j=await e(`${T}/component_server/`,{method:"POST",body:JSON.stringify({data:P,component_id:d,fn_name:C,session_hash:O}),headers:a});if(!j.ok)throw new Error("Could not connect to component server: "+j.statusText);return await j.json()}async function we(d){if(H)return H;const C={"Content-Type":"application/json"};u&&(C.Authorization=`Bearer ${u}`);let P;if(be(d.version||"2.0.0","3.30")<0?P=await e("https://gradio-space-api-fetcher-v2.hf.space/api",{method:"POST",body:JSON.stringify({serialize:!1,config:JSON.stringify(d)}),headers:C}):P=await e(`${d.root}/info`,{headers:C}),!P.ok)throw new Error(M);let b=await P.json();return"api"in b&&(b=b.api),b.named_endpoints["/predict"]&&!b.unnamed_endpoints[0]&&(b.unnamed_endpoints[0]=b.named_endpoints["/predict"]),He(b,d,Q)}})}async function r(i,f,w,l){const u=await pe(f,void 0,[],!0,w);return Promise.all(u.map(async({path:p,blob:h,type:D})=>{if(h){const A=(await o(i,[h],l)).files[0];return{path:p,file_url:A,type:D,name:h?.name}}return{path:p,type:D}})).then(p=>(p.forEach(({path:h,file_url:D,type:A,name:U})=>{if(A==="Gallery")De(f,D,h);else if(D){const O=new ge({path:D,orig_name:U});De(f,O,h)}}),f))}}const{post_data:nt,upload_files:ue,client:Se,handle_blob:ot}=Ve(fetch,(...e)=>new EventSource(...e));function $e(e,s,t,o){switch(e.type){case"string":return"string";case"boolean":return"boolean";case"number":return"number"}if(t==="JSONSerializable"||t==="StringSerializable")return"any";if(t==="ListStringSerializable")return"string[]";if(s==="Image")return o==="parameter"?"Blob | File | Buffer":"string";if(t==="FileSerializable")return e?.type==="array"?o==="parameter"?"(Blob | File | Buffer)[]":"{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}[]":o==="parameter"?"Blob | File | Buffer":"{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}";if(t==="GallerySerializable")return o==="parameter"?"[(Blob | File | Buffer), (string | null)][]":"[{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}, (string | null))][]"}function Ee(e,s){return s==="GallerySerializable"?"array of [file, label] tuples":s==="ListStringSerializable"?"array of strings":s==="FileSerializable"?"array of files or single file":e.description}function He(e,s,t){const o={named_endpoints:{},unnamed_endpoints:{}};for(const n in e){const r=e[n];for(const i in r){const f=s.dependencies[i]?i:t[i.replace("/","")],w=r[i];o[n][i]={},o[n][i].parameters={},o[n][i].returns={},o[n][i].type=s.dependencies[f].types,o[n][i].parameters=w.parameters.map(({label:l,component:u,type:p,serializer:h})=>({label:l,component:u,type:$e(p,u,h,"parameter"),description:Ee(p,h)})),o[n][i].returns=w.returns.map(({label:l,component:u,type:p,serializer:h})=>({label:l,component:u,type:$e(p,u,h,"return"),description:Ee(p,h)}))}}return o}async function Ye(e,s){try{return(await(await fetch(`https://huggingface.co/api/spaces/${e}/jwt`,{headers:{Authorization:`Bearer ${s}`}})).json()).token||!1}catch(t){return console.error(t),!1}}function De(e,s,t){for(;t.length>1;)e=e[t.shift()];e[t.shift()]=s}async function pe(e,s=void 0,t=[],o=!1,n=void 0){if(Array.isArray(e)){let r=[];return await Promise.all(e.map(async(i,f)=>{var w;let l=t.slice();l.push(f);const u=await pe(e[f],o?((w=n?.parameters[f])==null?void 0:w.component)||void 0:s,l,!1,n);r=r.concat(u)})),r}else{if(globalThis.Buffer&&e instanceof globalThis.Buffer)return[{path:t,blob:s==="Image"?!1:new Te([e]),type:s}];if(typeof e=="object"){let r=[];for(let i in e)if(e.hasOwnProperty(i)){let f=t.slice();f.push(i),r=r.concat(await pe(e[i],void 0,f,!1,n))}return r}}return[]}function Qe(e,s){var t,o,n,r;return!(((o=(t=s?.dependencies)==null?void 0:t[e])==null?void 0:o.queue)===null?s.enable_queue:(r=(n=s?.dependencies)==null?void 0:n[e])!=null&&r.queue)||!1}async function Ce(e,s,t){const o={};if(t&&(o.Authorization=`Bearer ${t}`),typeof window<"u"&&window.gradio_config&&location.origin!=="http://localhost:9876"&&!window.gradio_config.dev_mode){const n=window.gradio_config.root,r=window.gradio_config;return r.root=Oe(s,r.root,!1),{...r,path:n}}else if(s){let n=await e(`${s}/config`,{headers:o});if(n.status===200){const r=await n.json();return r.path=r.path??"",r.root=s,r}throw new Error("Could not get config.")}throw new Error("No config or app endpoint found")}async function de(e,s,t){let o=s==="subdomain"?`https://huggingface.co/api/spaces/by-subdomain/${e}`:`https://huggingface.co/api/spaces/${e}`,n,r;try{if(n=await fetch(o),r=n.status,r!==200)throw new Error;n=await n.json()}catch{t({status:"error",load_status:"error",message:"Could not get space status",detail:"NOT_FOUND"});return}if(!n||r!==200)return;const{runtime:{stage:i},id:f}=n;switch(i){case"STOPPED":case"SLEEPING":t({status:"sleeping",load_status:"pending",message:"Space is asleep. Waking it up...",detail:i}),setTimeout(()=>{de(e,s,t)},1e3);break;case"PAUSED":t({status:"paused",load_status:"error",message:"This space has been paused by the author. If you would like to try this demo, consider duplicating the space.",detail:i,discussions_enabled:await ve(f)});break;case"RUNNING":case"RUNNING_BUILDING":t({status:"running",load_status:"complete",message:"",detail:i});break;case"BUILDING":t({status:"building",load_status:"pending",message:"Space is building...",detail:i}),setTimeout(()=>{de(e,s,t)},1e3);break;default:t({status:"space_error",load_status:"error",message:"This space is experiencing an issue.",detail:i,discussions_enabled:await ve(f)});break}}function le(e,s){switch(e.msg){case"send_data":return{type:"data"};case"send_hash":return{type:"hash"};case"queue_full":return{type:"update",status:{queue:!0,message:ke,stage:"error",code:e.code,success:e.success}};case"heartbeat":return{type:"heartbeat"};case"unexpected_error":return{type:"unexpected_error",status:{queue:!0,message:e.message,stage:"error",success:!1}};case"estimation":return{type:"update",status:{queue:!0,stage:s||"pending",code:e.code,size:e.queue_size,position:e.rank,eta:e.rank_eta,success:e.success}};case"progress":return{type:"update",status:{queue:!0,stage:"pending",code:e.code,progress_data:e.progress_data,success:e.success}};case"log":return{type:"log",data:e};case"process_generating":return{type:"generating",status:{queue:!0,message:e.success?null:e.output.error,stage:e.success?"generating":"error",code:e.code,progress_data:e.progress_data,eta:e.average_duration},data:e.success?e.output:null};case"process_completed":return"error"in e.output?{type:"update",status:{queue:!0,message:e.output.error,stage:"error",code:e.code,success:e.success}}:{type:"complete",status:{queue:!0,message:e.success?void 0:e.output.error,stage:e.success?"complete":"error",code:e.code,progress_data:e.progress_data},data:e.success?e.output:null};case"process_starts":return{type:"update",status:{queue:!0,stage:"pending",code:e.code,size:e.rank,position:0,success:e.success,eta:e.eta}}}return{type:"none",status:{stage:"error",queue:!0}}}let Ae=!1;"attachShadow"in Element.prototype&&"adoptedStyleSheets"in Document.prototype&&(Ae="adoptedStyleSheets"in document.createElement("div").attachShadow({mode:"open"}));function qe(e,s){const t=new URL(import.meta.url).origin,o=new URL(e,t).href;if(document.querySelector(`link[href='${o}']`))return Promise.resolve();const r=document.createElement("link");return r.rel="stylesheet",r.href=o,new Promise((i,f)=>{r.addEventListener("load",()=>i()),r.addEventListener("error",()=>{console.error(`Unable to preload CSS for ${o}`),i()}),s.appendChild(r)})}function it(e,s,t=document.createElement("style")){if(!Ae)return null;t.remove();const o=new CSSStyleSheet;o.replaceSync(e);let n="";e=e.replace(/@import\s+url\((.*?)\);\s*/g,(w,l)=>(n+=`@import url(${l});
`,""));const r=o.cssRules;let i="",f=`gradio-app .gradio-container.gradio-container-${s} .contain `;for(let w=0;w<r.length;w++){const l=r[w];let u=l.cssText.includes(".dark");if(l instanceof CSSStyleRule){const p=l.selectorText;if(p){const h=p.replace(".dark","").split(",").map(D=>`${u?".dark":""} ${f} ${D.trim()} `).join(",");i+=l.cssText,i+=l.cssText.replace(p,h)}}else if(l instanceof CSSMediaRule){let p=`@media ${l.media.mediaText} {`;for(let h=0;h<l.cssRules.length;h++){const D=l.cssRules[h];if(D instanceof CSSStyleRule){let A=D.cssText.includes(".dark ");const U=D.selectorText,O=U.replace(".dark","").split(",").map(W=>`${A?".dark":""} ${f} ${W.trim()} `).join(",");p+=D.cssText.replace(U,O)}}p+="}",i+=p}else if(l instanceof CSSKeyframesRule){i+=`@keyframes ${l.name} {`;for(let p=0;p<l.cssRules.length;p++){const h=l.cssRules[p];h instanceof CSSKeyframeRule&&(i+=`${h.keyText} { ${h.style.cssText} }`)}i+="}"}else l instanceof CSSFontFaceRule&&(i+=`@font-face { ${l.style.cssText} }`)}return i=n+i,t.textContent=i,document.head.appendChild(t),t}const Xe="./assets/index-a889f790.css";let fe;fe=[];let he,xe,Ze=new Promise(e=>{xe=e});async function et(){he=(await ce(()=>import("./Index-26cfc80a.js").then(e=>e.I),["./Index-26cfc80a.js","./Index-cf60e5e6.css"],import.meta.url)).default,xe()}function tt(){const e={SvelteComponent:oe.SvelteComponent};for(const t in oe)t!=="SvelteComponent"&&(t==="SvelteComponentDev"?e[t]=e.SvelteComponent:e[t]=oe[t]);window.__gradio__svelte__internal=e;class s extends HTMLElement{constructor(){super(),this.host=this.getAttribute("host"),this.space=this.getAttribute("space"),this.src=this.getAttribute("src"),this.control_page_title=this.getAttribute("control_page_title"),this.initial_height=this.getAttribute("initial_height")??"300px",this.is_embed=this.getAttribute("embed")??"true",this.container=this.getAttribute("container")??"true",this.info=this.getAttribute("info")??!0,this.autoscroll=this.getAttribute("autoscroll"),this.eager=this.getAttribute("eager"),this.theme_mode=this.getAttribute("theme_mode"),this.updating=!1,this.loading=!1}async connectedCallback(){await et(),this.loading=!0,this.app&&this.app.$destroy(),typeof fe!="string"&&fe.forEach(r=>qe(r,document.head)),await qe(Xe,document.head);const o=new CustomEvent("domchange",{bubbles:!0,cancelable:!1,composed:!0});new MutationObserver(r=>{this.dispatchEvent(o)}).observe(this,{childList:!0}),this.app=new he({target:this,props:{space:this.space?this.space.trim():this.space,src:this.src?this.src.trim():this.src,host:this.host?this.host.trim():this.host,info:this.info!=="false",container:this.container!=="false",is_embed:this.is_embed!=="false",initial_height:this.initial_height,eager:this.eager==="true",version:"4-25-0",theme_mode:this.theme_mode,autoscroll:this.autoscroll==="true",control_page_title:this.control_page_title==="true",client:Se,upload_files:ue,app_mode:window.__gradio_mode__==="app"}}),this.updating&&this.setAttribute(this.updating.name,this.updating.value),this.loading=!1}static get observedAttributes(){return["src","space","host"]}async attributeChangedCallback(o,n,r){if(await Ze,(o==="host"||o==="space"||o==="src")&&r!==n){if(this.updating={name:o,value:r},this.loading)return;this.app&&this.app.$destroy(),this.space=null,this.host=null,this.src=null,o==="host"?this.host=r:o==="space"?this.space=r:o==="src"&&(this.src=r),this.app=new he({target:this,props:{space:this.space?this.space.trim():this.space,src:this.src?this.src.trim():this.src,host:this.host?this.host.trim():this.host,info:this.info!=="false",container:this.container!=="false",is_embed:this.is_embed!=="false",initial_height:this.initial_height,eager:this.eager==="true",version:"4-25-0",theme_mode:this.theme_mode,autoscroll:this.autoscroll==="true",control_page_title:this.control_page_title==="true",client:Se,upload_files:ue,app_mode:window.__gradio_mode__==="app"}}),this.updating=!1}}}customElements.get("gradio-app")||customElements.define("gradio-app",s)}tt();export{ce as _,it as a,qe as m,rt as p,st as u};
//# sourceMappingURL=index-a80d931b.js.map
