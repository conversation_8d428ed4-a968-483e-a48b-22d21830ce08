class i extends Error{constructor(e){super(e),this.name="ShareError"}}async function f(o,e){if(window.__gradio_space__==null)throw new i("Must be on Spaces to share.");let t,n,r;if(e==="url"){const c=await fetch(o);t=await c.blob(),n=c.headers.get("content-type")||"",r=c.headers.get("content-disposition")||""}else t=d(o),n=o.split(";")[0].split(":")[1],r="file"+n.split("/")[1];const a=new File([t],r,{type:n}),s=await fetch("https://huggingface.co/uploads",{method:"POST",body:a,headers:{"Content-Type":a.type,"X-Requested-With":"XMLHttpRequest"}});if(!s.ok){if(s.headers.get("content-type")?.includes("application/json")){const c=await s.json();throw new i(`Upload failed: ${c.error}`)}throw new i("Upload failed.")}return await s.text()}function d(o){for(var e=o.split(","),t=e[0].match(/:(.*?);/)[1],n=atob(e[1]),r=n.length,a=new Uint8Array(r);r--;)a[r]=n.charCodeAt(r);return new Blob([a],{type:t})}function y(o){o.addEventListener("click",e);async function e(t){const n=t.composedPath(),[r]=n.filter(a=>a?.tagName==="BUTTON"&&a.classList.contains("copy_code_button"));if(r){let a=function(p){p.style.opacity="1",setTimeout(()=>{p.style.opacity="0"},2e3)};t.stopImmediatePropagation();const s=r.parentElement.innerText.trim(),l=Array.from(r.children)[1];await u(s)&&a(l)}}return{destroy(){o.removeEventListener("click",e)}}}async function u(o){let e=!1;if("clipboard"in navigator)await navigator.clipboard.writeText(o),e=!0;else{const t=document.createElement("textarea");t.value=o,t.style.position="absolute",t.style.left="-999999px",document.body.prepend(t),t.select();try{document.execCommand("copy"),e=!0}catch(n){console.error(n),e=!1}finally{t.remove()}}return e}const h=o=>{const e=Math.floor(o/3600),t=Math.floor(o%3600/60),n=Math.round(o)%60,r=`${t<10?"0":""}${t}`,a=`${n<10?"0":""}${n}`;return e>0?`${e}:${r}:${a}`:`${t}:${a}`};export{i as S,y as c,h as f,f as u};
//# sourceMappingURL=utils-572af92b.js.map
