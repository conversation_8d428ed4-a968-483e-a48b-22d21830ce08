import{a as A}from"./Tabs-9b11644c.js";import B from"./Index-ab6a99fa.js";import"./Index-26cfc80a.js";import"./index-a80d931b.js";import"./svelte/svelte.js";const{SvelteComponent:D,attr:b,component_subscribe:w,create_component:E,create_slot:M,destroy_component:y,detach:z,element:F,get_all_dirty_from_scope:G,get_slot_changes:H,init:J,insert:K,mount_component:L,safe_not_equal:N,set_style:C,transition_in:I,transition_out:S,update_slot_base:O}=window.__gradio__svelte__internal,{getContext:P,onMount:Q,createEventDispatcher:R,tick:U}=window.__gradio__svelte__internal;function V(s){let e;const i=s[11].default,t=M(i,s,s[12],null);return{c(){t&&t.c()},m(n,l){t&&t.m(n,l),e=!0},p(n,l){t&&t.p&&(!e||l&4096)&&O(t,i,n,n[12],e?H(i,n[12],l,null):G(n[12]),null)},i(n){e||(I(t,n),e=!0)},o(n){S(t,n),e=!1},d(n){t&&t.d(n)}}}function W(s){let e,i,t,n;return i=new B({props:{$$slots:{default:[V]},$$scope:{ctx:s}}}),{c(){e=F("div"),E(i.$$.fragment),b(e,"id",s[0]),b(e,"class",t="tabitem "+s[1].join(" ")+" svelte-19hvt5v"),b(e,"role","tabpanel"),C(e,"display",s[4]===s[2]&&s[3]?"block":"none")},m(l,o){K(l,e,o),L(i,e,null),n=!0},p(l,[o]){const c={};o&4096&&(c.$$scope={dirty:o,ctx:l}),i.$set(c),(!n||o&1)&&b(e,"id",l[0]),(!n||o&2&&t!==(t="tabitem "+l[1].join(" ")+" svelte-19hvt5v"))&&b(e,"class",t),o&28&&C(e,"display",l[4]===l[2]&&l[3]?"block":"none")},i(l){n||(I(i.$$.fragment,l),n=!0)},o(l){S(i.$$.fragment,l),n=!1},d(l){l&&z(e),y(i)}}}function X(s,e,i){let t,n,{$$slots:l={},$$scope:o}=e,{elem_id:c=""}=e,{elem_classes:r=[]}=e,{name:m}=e,{id:f={}}=e,{visible:u}=e,{interactive:d}=e;const _=R(),{register_tab:T,unregister_tab:j,selected_tab:v,selected_tab_index:h}=P(A);w(s,v,a=>i(4,n=a)),w(s,h,a=>i(10,t=a));let g;return Q(()=>()=>j({name:m,id:f,elem_id:c})),s.$$set=a=>{"elem_id"in a&&i(0,c=a.elem_id),"elem_classes"in a&&i(1,r=a.elem_classes),"name"in a&&i(7,m=a.name),"id"in a&&i(2,f=a.id),"visible"in a&&i(3,u=a.visible),"interactive"in a&&i(8,d=a.interactive),"$$scope"in a&&i(12,o=a.$$scope)},s.$$.update=()=>{s.$$.dirty&397&&i(9,g=T({name:m,id:f,elem_id:c,visible:u,interactive:d})),s.$$.dirty&1664&&t===g&&U().then(()=>_("select",{value:m,index:g}))},[c,r,f,u,n,v,h,m,d,g,t,l,o]}class Y extends D{constructor(e){super(),J(this,e,X,W,N,{elem_id:0,elem_classes:1,name:7,id:2,visible:3,interactive:8})}}const{SvelteComponent:Z,create_component:x,create_slot:p,destroy_component:$,get_all_dirty_from_scope:ee,get_slot_changes:te,init:ne,mount_component:ie,safe_not_equal:le,transition_in:k,transition_out:q,update_slot_base:se}=window.__gradio__svelte__internal;function _e(s){let e;const i=s[7].default,t=p(i,s,s[9],null);return{c(){t&&t.c()},m(n,l){t&&t.m(n,l),e=!0},p(n,l){t&&t.p&&(!e||l&512)&&se(t,i,n,n[9],e?te(i,n[9],l,null):ee(n[9]),null)},i(n){e||(k(t,n),e=!0)},o(n){q(t,n),e=!1},d(n){t&&t.d(n)}}}function ae(s){let e,i;return e=new Y({props:{elem_id:s[0],elem_classes:s[1],name:s[2],visible:s[5],interactive:s[6],id:s[3],$$slots:{default:[_e]},$$scope:{ctx:s}}}),e.$on("select",s[8]),{c(){x(e.$$.fragment)},m(t,n){ie(e,t,n),i=!0},p(t,[n]){const l={};n&1&&(l.elem_id=t[0]),n&2&&(l.elem_classes=t[1]),n&4&&(l.name=t[2]),n&32&&(l.visible=t[5]),n&64&&(l.interactive=t[6]),n&8&&(l.id=t[3]),n&512&&(l.$$scope={dirty:n,ctx:t}),e.$set(l)},i(t){i||(k(e.$$.fragment,t),i=!0)},o(t){q(e.$$.fragment,t),i=!1},d(t){$(e,t)}}}function oe(s,e,i){let{$$slots:t={},$$scope:n}=e,{elem_id:l=""}=e,{elem_classes:o=[]}=e,{label:c}=e,{id:r}=e,{gradio:m}=e,{visible:f=!0}=e,{interactive:u=!0}=e;const d=({detail:_})=>m.dispatch("select",_);return s.$$set=_=>{"elem_id"in _&&i(0,l=_.elem_id),"elem_classes"in _&&i(1,o=_.elem_classes),"label"in _&&i(2,c=_.label),"id"in _&&i(3,r=_.id),"gradio"in _&&i(4,m=_.gradio),"visible"in _&&i(5,f=_.visible),"interactive"in _&&i(6,u=_.interactive),"$$scope"in _&&i(9,n=_.$$scope)},[l,o,c,r,m,f,u,t,d,n]}class de extends Z{constructor(e){super(),ne(this,e,oe,ae,le,{elem_id:0,elem_classes:1,label:2,id:3,gradio:4,visible:5,interactive:6})}}export{de as default};
//# sourceMappingURL=Index-5bbd95b2.js.map
