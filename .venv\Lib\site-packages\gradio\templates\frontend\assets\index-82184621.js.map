{"version": 3, "file": "index-82184621.js", "sources": ["../../../../js/icons/src/Maximise.svelte", "../../../../js/video/shared/VideoTimeline.svelte", "../../../../js/video/shared/VideoControls.svelte", "../../../../js/video/shared/Player.svelte", "../../../../js/video/shared/InteractiveVideo.svelte", "../../../../js/video/shared/VideoPreview.svelte", "../../../../js/video/Index.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n>\n\t<path\n\t\td=\"M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\timport { onMount, onDestroy } from \"svelte\";\n\n\texport let videoElement: HTMLVideoElement;\n\texport let trimmedDuration: number | null;\n\texport let dragStart: number;\n\texport let dragEnd: number;\n\texport let loadingTimeline: boolean;\n\n\tlet thumbnails: string[] = [];\n\tlet numberOfThumbnails = 10;\n\tlet intervalId: number | NodeJS.Timer;\n\tlet videoDuration: number;\n\n\tlet leftHandlePosition = 0;\n\tlet rightHandlePosition = 100;\n\n\tlet dragging: string | null = null;\n\n\tconst startDragging = (side: string | null): void => {\n\t\tdragging = side;\n\t};\n\n\t$: loadingTimeline = thumbnails.length !== numberOfThumbnails;\n\n\tconst stopDragging = (): void => {\n\t\tdragging = null;\n\t};\n\n\tconst drag = (event: { clientX: number }, distance?: number): void => {\n\t\tif (dragging) {\n\t\t\tconst timeline = document.getElementById(\"timeline\");\n\n\t\t\tif (!timeline) return;\n\n\t\t\tconst rect = timeline.getBoundingClientRect();\n\t\t\tlet newPercentage = ((event.clientX - rect.left) / rect.width) * 100;\n\n\t\t\tif (distance) {\n\t\t\t\t// Move handle based on arrow key press\n\t\t\t\tnewPercentage =\n\t\t\t\t\tdragging === \"left\"\n\t\t\t\t\t\t? leftHandlePosition + distance\n\t\t\t\t\t\t: rightHandlePosition + distance;\n\t\t\t} else {\n\t\t\t\t// Move handle based on mouse drag\n\t\t\t\tnewPercentage = ((event.clientX - rect.left) / rect.width) * 100;\n\t\t\t}\n\n\t\t\tnewPercentage = Math.max(0, Math.min(newPercentage, 100)); // Keep within 0 and 100\n\n\t\t\tif (dragging === \"left\") {\n\t\t\t\tleftHandlePosition = Math.min(newPercentage, rightHandlePosition);\n\n\t\t\t\t// Calculate the new time and set it for the videoElement\n\t\t\t\tconst newTimeLeft = (leftHandlePosition / 100) * videoDuration;\n\t\t\t\tvideoElement.currentTime = newTimeLeft;\n\n\t\t\t\tdragStart = newTimeLeft;\n\t\t\t} else if (dragging === \"right\") {\n\t\t\t\trightHandlePosition = Math.max(newPercentage, leftHandlePosition);\n\n\t\t\t\tconst newTimeRight = (rightHandlePosition / 100) * videoDuration;\n\t\t\t\tvideoElement.currentTime = newTimeRight;\n\n\t\t\t\tdragEnd = newTimeRight;\n\t\t\t}\n\n\t\t\tconst startTime = (leftHandlePosition / 100) * videoDuration;\n\t\t\tconst endTime = (rightHandlePosition / 100) * videoDuration;\n\t\t\ttrimmedDuration = endTime - startTime;\n\n\t\t\tleftHandlePosition = leftHandlePosition;\n\t\t\trightHandlePosition = rightHandlePosition;\n\t\t}\n\t};\n\n\tconst moveHandle = (e: KeyboardEvent): void => {\n\t\tif (dragging) {\n\t\t\t// Calculate the movement distance as a percentage of the video duration\n\t\t\tconst distance = (1 / videoDuration) * 100;\n\n\t\t\tif (e.key === \"ArrowLeft\") {\n\t\t\t\tdrag({ clientX: 0 }, -distance);\n\t\t\t} else if (e.key === \"ArrowRight\") {\n\t\t\t\tdrag({ clientX: 0 }, distance);\n\t\t\t}\n\t\t}\n\t};\n\n\tconst generateThumbnail = (): void => {\n\t\tconst canvas = document.createElement(\"canvas\");\n\t\tconst ctx = canvas.getContext(\"2d\");\n\t\tif (!ctx) return;\n\n\t\tcanvas.width = videoElement.videoWidth;\n\t\tcanvas.height = videoElement.videoHeight;\n\n\t\tctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);\n\n\t\tconst thumbnail: string = canvas.toDataURL(\"image/jpeg\", 0.7);\n\t\tthumbnails = [...thumbnails, thumbnail];\n\t};\n\n\tonMount(() => {\n\t\tconst loadMetadata = (): void => {\n\t\t\tvideoDuration = videoElement.duration;\n\n\t\t\tconst interval = videoDuration / numberOfThumbnails;\n\t\t\tlet captures = 0;\n\n\t\t\tconst onSeeked = (): void => {\n\t\t\t\tgenerateThumbnail();\n\t\t\t\tcaptures++;\n\n\t\t\t\tif (captures < numberOfThumbnails) {\n\t\t\t\t\tvideoElement.currentTime += interval;\n\t\t\t\t} else {\n\t\t\t\t\tvideoElement.removeEventListener(\"seeked\", onSeeked);\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tvideoElement.addEventListener(\"seeked\", onSeeked);\n\t\t\tvideoElement.currentTime = 0;\n\t\t};\n\n\t\tif (videoElement.readyState >= 1) {\n\t\t\tloadMetadata();\n\t\t} else {\n\t\t\tvideoElement.addEventListener(\"loadedmetadata\", loadMetadata);\n\t\t}\n\t});\n\n\tonDestroy(() => {\n\t\twindow.removeEventListener(\"mousemove\", drag);\n\t\twindow.removeEventListener(\"mouseup\", stopDragging);\n\t\twindow.removeEventListener(\"keydown\", moveHandle);\n\n\t\tif (intervalId) {\n\t\t\tclearInterval(intervalId);\n\t\t}\n\t});\n\n\tonMount(() => {\n\t\twindow.addEventListener(\"mousemove\", drag);\n\t\twindow.addEventListener(\"mouseup\", stopDragging);\n\t\twindow.addEventListener(\"keydown\", moveHandle);\n\t});\n</script>\n\n<div class=\"container\">\n\t{#if loadingTimeline}\n\t\t<div class=\"load-wrap\">\n\t\t\t<span aria-label=\"loading timeline\" class=\"loader\" />\n\t\t</div>\n\t{:else}\n\t\t<div id=\"timeline\" class=\"thumbnail-wrapper\">\n\t\t\t<button\n\t\t\t\taria-label=\"start drag handle for trimming video\"\n\t\t\t\tclass=\"handle left\"\n\t\t\t\ton:mousedown={() => startDragging(\"left\")}\n\t\t\t\ton:blur={stopDragging}\n\t\t\t\ton:keydown={(e) => {\n\t\t\t\t\tif (e.key === \"ArrowLeft\" || e.key == \"ArrowRight\") {\n\t\t\t\t\t\tstartDragging(\"left\");\n\t\t\t\t\t}\n\t\t\t\t}}\n\t\t\t\tstyle=\"left: {leftHandlePosition}%;\"\n\t\t\t/>\n\n\t\t\t<div\n\t\t\t\tclass=\"opaque-layer\"\n\t\t\t\tstyle=\"left: {leftHandlePosition}%; right: {100 - rightHandlePosition}%\"\n\t\t\t/>\n\n\t\t\t{#each thumbnails as thumbnail, i (i)}\n\t\t\t\t<img src={thumbnail} alt={`frame-${i}`} draggable=\"false\" />\n\t\t\t{/each}\n\t\t\t<button\n\t\t\t\taria-label=\"end drag handle for trimming video\"\n\t\t\t\tclass=\"handle right\"\n\t\t\t\ton:mousedown={() => startDragging(\"right\")}\n\t\t\t\ton:blur={stopDragging}\n\t\t\t\ton:keydown={(e) => {\n\t\t\t\t\tif (e.key === \"ArrowLeft\" || e.key == \"ArrowRight\") {\n\t\t\t\t\t\tstartDragging(\"right\");\n\t\t\t\t\t}\n\t\t\t\t}}\n\t\t\t\tstyle=\"left: {rightHandlePosition}%;\"\n\t\t\t/>\n\t\t</div>\n\t{/if}\n</div>\n\n<style>\n\t.load-wrap {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\theight: 100%;\n\t}\n\t.loader {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tbackground-color: var(--border-color-accent-subdued);\n\t\tanimation: shadowPulse 2s linear infinite;\n\t\tbox-shadow:\n\t\t\t-24px 0 var(--border-color-accent-subdued),\n\t\t\t24px 0 var(--border-color-accent-subdued);\n\t\tmargin: var(--spacing-md);\n\t\tborder-radius: 50%;\n\t\twidth: 10px;\n\t\theight: 10px;\n\t\tscale: 0.5;\n\t}\n\n\t@keyframes shadowPulse {\n\t\t33% {\n\t\t\tbox-shadow:\n\t\t\t\t-24px 0 var(--border-color-accent-subdued),\n\t\t\t\t24px 0 #fff;\n\t\t\tbackground: #fff;\n\t\t}\n\t\t66% {\n\t\t\tbox-shadow:\n\t\t\t\t-24px 0 #fff,\n\t\t\t\t24px 0 #fff;\n\t\t\tbackground: var(--border-color-accent-subdued);\n\t\t}\n\t\t100% {\n\t\t\tbox-shadow:\n\t\t\t\t-24px 0 #fff,\n\t\t\t\t24px 0 var(--border-color-accent-subdued);\n\t\t\tbackground: #fff;\n\t\t}\n\t}\n\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin: var(--spacing-lg) var(--spacing-lg) 0 var(--spacing-lg);\n\t}\n\n\t#timeline {\n\t\tdisplay: flex;\n\t\theight: var(--size-10);\n\t\tflex: 1;\n\t\tposition: relative;\n\t}\n\n\timg {\n\t\tflex: 1 1 auto;\n\t\tmin-width: 0;\n\t\tobject-fit: cover;\n\t\theight: var(--size-12);\n\t\tborder: 1px solid var(--block-border-color);\n\t\tuser-select: none;\n\t\tz-index: 1;\n\t}\n\n\t.handle {\n\t\twidth: 3px;\n\t\tbackground-color: var(--color-accent);\n\t\tcursor: ew-resize;\n\t\theight: var(--size-12);\n\t\tz-index: 3;\n\t\tposition: absolute;\n\t}\n\n\t.opaque-layer {\n\t\tbackground-color: rgba(230, 103, 40, 0.25);\n\t\tborder: 1px solid var(--color-accent);\n\t\theight: var(--size-12);\n\t\tposition: absolute;\n\t\tz-index: 2;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { Undo, Trim } from \"@gradio/icons\";\n\timport VideoTimeline from \"./VideoTimeline.svelte\";\n\timport { trimVideo } from \"./utils\";\n\timport { FFmpeg } from \"@ffmpeg/ffmpeg\";\n\timport loadFfmpeg from \"./utils\";\n\timport { onMount } from \"svelte\";\n\timport { format_time } from \"@gradio/utils\";\n\n\texport let videoElement: HTMLVideoElement;\n\n\texport let showRedo = false;\n\texport let interactive = true;\n\texport let mode = \"\";\n\texport let handle_reset_value: () => void;\n\texport let handle_trim_video: (videoBlob: Blob) => void;\n\texport let processingVideo = false;\n\n\tlet ffmpeg: FFmpeg;\n\n\tonMount(async () => {\n\t\tffmpeg = await loadFfmpeg();\n\t});\n\n\t$: if (mode === \"edit\" && trimmedDuration === null && videoElement)\n\t\ttrimmedDuration = videoElement.duration;\n\n\tlet trimmedDuration: number | null = null;\n\tlet dragStart = 0;\n\tlet dragEnd = 0;\n\n\tlet loadingTimeline = false;\n\n\tconst toggleTrimmingMode = (): void => {\n\t\tif (mode === \"edit\") {\n\t\t\tmode = \"\";\n\t\t\ttrimmedDuration = videoElement.duration;\n\t\t} else {\n\t\t\tmode = \"edit\";\n\t\t}\n\t};\n</script>\n\n<div class=\"container\">\n\t{#if mode === \"edit\"}\n\t\t<div class=\"timeline-wrapper\">\n\t\t\t<VideoTimeline\n\t\t\t\t{videoElement}\n\t\t\t\tbind:dragStart\n\t\t\t\tbind:dragEnd\n\t\t\t\tbind:trimmedDuration\n\t\t\t\tbind:loadingTimeline\n\t\t\t/>\n\t\t</div>\n\t{/if}\n\n\t<div class=\"controls\" data-testid=\"waveform-controls\">\n\t\t{#if mode === \"edit\" && trimmedDuration !== null}\n\t\t\t<time\n\t\t\t\taria-label=\"duration of selected region in seconds\"\n\t\t\t\tclass:hidden={loadingTimeline}>{format_time(trimmedDuration)}</time\n\t\t\t>\n\t\t{:else}\n\t\t\t<div />\n\t\t{/if}\n\n\t\t<div class=\"settings-wrapper\">\n\t\t\t{#if showRedo && mode === \"\"}\n\t\t\t\t<button\n\t\t\t\t\tclass=\"action icon\"\n\t\t\t\t\tdisabled={processingVideo}\n\t\t\t\t\taria-label=\"Reset video to initial value\"\n\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\thandle_reset_value();\n\t\t\t\t\t\tmode = \"\";\n\t\t\t\t\t}}\n\t\t\t\t>\n\t\t\t\t\t<Undo />\n\t\t\t\t</button>\n\t\t\t{/if}\n\n\t\t\t{#if interactive}\n\t\t\t\t{#if mode === \"\"}\n\t\t\t\t\t<button\n\t\t\t\t\t\tdisabled={processingVideo}\n\t\t\t\t\t\tclass=\"action icon\"\n\t\t\t\t\t\taria-label=\"Trim video to selection\"\n\t\t\t\t\t\ton:click={toggleTrimmingMode}\n\t\t\t\t\t>\n\t\t\t\t\t\t<Trim />\n\t\t\t\t\t</button>\n\t\t\t\t{:else}\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass:hidden={loadingTimeline}\n\t\t\t\t\t\tclass=\"text-button\"\n\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\tmode = \"\";\n\t\t\t\t\t\t\tprocessingVideo = true;\n\t\t\t\t\t\t\ttrimVideo(ffmpeg, dragStart, dragEnd, videoElement)\n\t\t\t\t\t\t\t\t.then((videoBlob) => {\n\t\t\t\t\t\t\t\t\thandle_trim_video(videoBlob);\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t.then(() => {\n\t\t\t\t\t\t\t\t\tprocessingVideo = false;\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}}>Trim</button\n\t\t\t\t\t>\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass=\"text-button\"\n\t\t\t\t\t\tclass:hidden={loadingTimeline}\n\t\t\t\t\t\ton:click={toggleTrimmingMode}>Cancel</button\n\t\t\t\t\t>\n\t\t\t\t{/if}\n\t\t\t{/if}\n\t\t</div>\n\t</div>\n</div>\n\n<style>\n\t.container {\n\t\twidth: 100%;\n\t}\n\ttime {\n\t\tcolor: var(--color-accent);\n\t\tfont-weight: bold;\n\t\tpadding-left: var(--spacing-xs);\n\t}\n\n\t.timeline-wrapper {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\twidth: 100%;\n\t}\n\t.settings-wrapper {\n\t\tdisplay: flex;\n\t\tjustify-self: self-end;\n\t}\n\t.text-button {\n\t\tborder: 1px solid var(--neutral-400);\n\t\tborder-radius: var(--radius-sm);\n\t\tfont-weight: 300;\n\t\tfont-size: var(--size-3);\n\t\ttext-align: center;\n\t\tcolor: var(--neutral-400);\n\t\theight: var(--size-5);\n\t\tfont-weight: bold;\n\t\tpadding: 0 5px;\n\t\tmargin-left: 5px;\n\t}\n\t.hidden {\n\t\tdisplay: none;\n\t}\n\n\t.text-button:hover,\n\t.text-button:focus {\n\t\tcolor: var(--color-accent);\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.controls {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: 1fr 1fr;\n\t\tmargin: var(--spacing-lg);\n\t\toverflow: hidden;\n\t\ttext-align: left;\n\t}\n\n\t@media (max-width: 320px) {\n\t\t.controls {\n\t\t\tdisplay: flex;\n\t\t\tflex-wrap: wrap;\n\t\t}\n\n\t\t.controls * {\n\t\t\tmargin: var(--spacing-sm);\n\t\t}\n\n\t\t.controls .text-button {\n\t\t\tmargin-left: 0;\n\t\t}\n\t}\n\t.action {\n\t\twidth: var(--size-5);\n\t\twidth: var(--size-5);\n\t\tcolor: var(--neutral-400);\n\t\tmargin-left: var(--spacing-md);\n\t}\n\n\t.action:disabled {\n\t\tcursor: not-allowed;\n\t\tcolor: var(--border-color-accent-subdued);\n\t}\n\n\t.action:disabled:hover {\n\t\tcolor: var(--border-color-accent-subdued);\n\t}\n\n\t.icon:hover,\n\t.icon:focus {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { <PERSON>, Pause, <PERSON>ise, Undo } from \"@gradio/icons\";\n\timport Video from \"./Video.svelte\";\n\timport VideoControls from \"./VideoControls.svelte\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { prepare_files, upload } from \"@gradio/client\";\n\timport { format_time } from \"@gradio/utils\";\n\n\texport let root = \"\";\n\texport let src: string;\n\texport let subtitle: string | null = null;\n\texport let mirror: boolean;\n\texport let autoplay: boolean;\n\texport let label = \"test\";\n\texport let interactive = false;\n\texport let handle_change: (video: FileData) => void = () => {};\n\texport let handle_reset_value: () => void = () => {};\n\n\tconst dispatch = createEventDispatcher<{\n\t\tplay: undefined;\n\t\tpause: undefined;\n\t\tstop: undefined;\n\t\tend: undefined;\n\t}>();\n\n\tlet time = 0;\n\tlet duration: number;\n\tlet paused = true;\n\tlet video: HTMLVideoElement;\n\tlet processingVideo = false;\n\n\tfunction handleMove(e: TouchEvent | MouseEvent): void {\n\t\tif (!duration) return;\n\n\t\tif (e.type === \"click\") {\n\t\t\thandle_click(e as MouseEvent);\n\t\t\treturn;\n\t\t}\n\n\t\tif (e.type !== \"touchmove\" && !((e as MouseEvent).buttons & 1)) return;\n\n\t\tconst clientX =\n\t\t\te.type === \"touchmove\"\n\t\t\t\t? (e as TouchEvent).touches[0].clientX\n\t\t\t\t: (e as MouseEvent).clientX;\n\t\tconst { left, right } = (\n\t\t\te.currentTarget as HTMLProgressElement\n\t\t).getBoundingClientRect();\n\t\ttime = (duration * (clientX - left)) / (right - left);\n\t}\n\n\tasync function play_pause(): Promise<void> {\n\t\tif (document.fullscreenElement != video) {\n\t\t\tconst isPlaying =\n\t\t\t\tvideo.currentTime > 0 &&\n\t\t\t\t!video.paused &&\n\t\t\t\t!video.ended &&\n\t\t\t\tvideo.readyState > video.HAVE_CURRENT_DATA;\n\n\t\t\tif (!isPlaying) {\n\t\t\t\tawait video.play();\n\t\t\t} else video.pause();\n\t\t}\n\t}\n\n\tfunction handle_click(e: MouseEvent): void {\n\t\tconst { left, right } = (\n\t\t\te.currentTarget as HTMLProgressElement\n\t\t).getBoundingClientRect();\n\t\ttime = (duration * (e.clientX - left)) / (right - left);\n\t}\n\n\tfunction handle_end(): void {\n\t\tdispatch(\"stop\");\n\t\tdispatch(\"end\");\n\t}\n\n\tconst handle_trim_video = async (videoBlob: Blob): Promise<void> => {\n\t\tlet _video_blob = new File([videoBlob], \"video.mp4\");\n\t\tconst val = await prepare_files([_video_blob]);\n\t\tlet value = ((await upload(val, root))?.filter(Boolean) as FileData[])[0];\n\n\t\thandle_change(value);\n\t};\n\n\tfunction open_full_screen(): void {\n\t\tvideo.requestFullscreen();\n\t}\n</script>\n\n<div class=\"wrap\">\n\t<div class=\"mirror-wrap\" class:mirror>\n\t\t<Video\n\t\t\t{src}\n\t\t\tpreload=\"auto\"\n\t\t\t{autoplay}\n\t\t\ton:click={play_pause}\n\t\t\ton:play\n\t\t\ton:pause\n\t\t\ton:ended={handle_end}\n\t\t\tbind:currentTime={time}\n\t\t\tbind:duration\n\t\t\tbind:paused\n\t\t\tbind:node={video}\n\t\t\tdata-testid={`${label}-player`}\n\t\t\t{processingVideo}\n\t\t>\n\t\t\t<track kind=\"captions\" src={subtitle} default />\n\t\t</Video>\n\t</div>\n\n\t<div class=\"controls\">\n\t\t<div class=\"inner\">\n\t\t\t<span\n\t\t\t\trole=\"button\"\n\t\t\t\ttabindex=\"0\"\n\t\t\t\tclass=\"icon\"\n\t\t\t\taria-label=\"play-pause-replay-button\"\n\t\t\t\ton:click={play_pause}\n\t\t\t\ton:keydown={play_pause}\n\t\t\t>\n\t\t\t\t{#if time === duration}\n\t\t\t\t\t<Undo />\n\t\t\t\t{:else if paused}\n\t\t\t\t\t<Play />\n\t\t\t\t{:else}\n\t\t\t\t\t<Pause />\n\t\t\t\t{/if}\n\t\t\t</span>\n\n\t\t\t<span class=\"time\">{format_time(time)} / {format_time(duration)}</span>\n\n\t\t\t<!-- TODO: implement accessible video timeline for 4.0 -->\n\t\t\t<!-- svelte-ignore a11y-click-events-have-key-events -->\n\t\t\t<!-- svelte-ignore a11y-no-noninteractive-element-interactions -->\n\t\t\t<progress\n\t\t\t\tvalue={time / duration || 0}\n\t\t\t\ton:mousemove={handleMove}\n\t\t\t\ton:touchmove|preventDefault={handleMove}\n\t\t\t\ton:click|stopPropagation|preventDefault={handle_click}\n\t\t\t/>\n\n\t\t\t<div\n\t\t\t\trole=\"button\"\n\t\t\t\ttabindex=\"0\"\n\t\t\t\tclass=\"icon\"\n\t\t\t\taria-label=\"full-screen\"\n\t\t\t\ton:click={open_full_screen}\n\t\t\t\ton:keypress={open_full_screen}\n\t\t\t>\n\t\t\t\t<Maximise />\n\t\t\t</div>\n\t\t</div>\n\t</div>\n</div>\n{#if interactive}\n\t<VideoControls\n\t\tvideoElement={video}\n\t\tshowRedo\n\t\t{handle_trim_video}\n\t\t{handle_reset_value}\n\t\tbind:processingVideo\n\t/>\n{/if}\n\n<style lang=\"postcss\">\n\tspan {\n\t\ttext-shadow: 0 0 8px rgba(0, 0, 0, 0.5);\n\t}\n\n\tprogress {\n\t\tmargin-right: var(--size-3);\n\t\tborder-radius: var(--radius-sm);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-2);\n\t}\n\n\tprogress::-webkit-progress-bar {\n\t\tborder-radius: 2px;\n\t\tbackground-color: rgba(255, 255, 255, 0.2);\n\t\toverflow: hidden;\n\t}\n\n\tprogress::-webkit-progress-value {\n\t\tbackground-color: rgba(255, 255, 255, 0.9);\n\t}\n\n\t.mirror {\n\t\ttransform: scaleX(-1);\n\t}\n\n\t.mirror-wrap {\n\t\tposition: relative;\n\t\theight: 100%;\n\t\twidth: 100%;\n\t}\n\n\t.controls {\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\topacity: 0;\n\t\ttransition: 500ms;\n\t\tmargin: var(--size-2);\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--color-grey-800);\n\t\tpadding: var(--size-2) var(--size-1);\n\t\twidth: calc(100% - 0.375rem * 2);\n\t\twidth: calc(100% - var(--size-2) * 2);\n\t}\n\t.wrap:hover .controls {\n\t\topacity: 1;\n\t}\n\n\t.inner {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding-right: var(--size-2);\n\t\tpadding-left: var(--size-2);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\t.icon {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tcursor: pointer;\n\t\twidth: var(--size-6);\n\t\tcolor: white;\n\t}\n\n\t.time {\n\t\tflex-shrink: 0;\n\t\tmargin-right: var(--size-3);\n\t\tmargin-left: var(--size-3);\n\t\tcolor: white;\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t}\n\t.wrap {\n\t\tposition: relative;\n\t\tbackground-color: var(--background-fill-secondary);\n\t\theight: var(--size-full);\n\t\twidth: var(--size-full);\n\t\tborder-radius: var(--radius-xl);\n\t}\n\t.wrap :global(video) {\n\t\theight: var(--size-full);\n\t\twidth: var(--size-full);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { Upload, ModifyUpload } from \"@gradio/upload\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { BlockLabel } from \"@gradio/atoms\";\n\timport { Webcam } from \"@gradio/image\";\n\timport { Video } from \"@gradio/icons\";\n\n\timport { prettyBytes, playable } from \"./utils\";\n\timport Player from \"./Player.svelte\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport { SelectSource } from \"@gradio/atoms\";\n\n\texport let value: FileData | null = null;\n\texport let subtitle: FileData | null = null;\n\texport let sources:\n\t\t| [\"webcam\"]\n\t\t| [\"upload\"]\n\t\t| [\"webcam\", \"upload\"]\n\t\t| [\"upload\", \"webcam\"] = [\"webcam\", \"upload\"];\n\texport let label: string | undefined = undefined;\n\texport let show_download_button = false;\n\texport let show_label = true;\n\texport let mirror_webcam = false;\n\texport let include_audio: boolean;\n\texport let autoplay: boolean;\n\texport let root: string;\n\texport let i18n: I18nFormatter;\n\texport let active_source: \"webcam\" | \"upload\" = \"webcam\";\n\texport let handle_reset_value: () => void = () => {};\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: FileData | null;\n\t\tclear?: never;\n\t\tplay?: never;\n\t\tpause?: never;\n\t\tend?: never;\n\t\tdrag: boolean;\n\t\terror: string;\n\t\tupload: FileData;\n\t\tstart_recording?: never;\n\t\tstop_recording?: never;\n\t}>();\n\n\tfunction handle_load({ detail }: CustomEvent<FileData | null>): void {\n\t\tvalue = detail;\n\t\tdispatch(\"change\", detail);\n\t\tdispatch(\"upload\", detail!);\n\t}\n\n\tfunction handle_clear(): void {\n\t\tvalue = null;\n\t\tdispatch(\"change\", null);\n\t\tdispatch(\"clear\");\n\t}\n\n\tfunction handle_change(video: FileData): void {\n\t\tdispatch(\"change\", video);\n\t}\n\n\tfunction handle_capture({\n\t\tdetail\n\t}: CustomEvent<FileData | any | null>): void {\n\t\tdispatch(\"change\", detail);\n\t}\n\n\tlet dragging = false;\n\t$: dispatch(\"drag\", dragging);\n</script>\n\n<BlockLabel {show_label} Icon={Video} label={label || \"Video\"} />\n<div data-testid=\"video\" class=\"video-container\">\n\t{#if value === null || value.url === undefined}\n\t\t<div class=\"upload-container\">\n\t\t\t{#if active_source === \"upload\"}\n\t\t\t\t<Upload\n\t\t\t\t\tbind:dragging\n\t\t\t\t\tfiletype=\"video/x-m4v,video/*\"\n\t\t\t\t\ton:load={handle_load}\n\t\t\t\t\ton:error={({ detail }) => dispatch(\"error\", detail)}\n\t\t\t\t\t{root}\n\t\t\t\t>\n\t\t\t\t\t<slot />\n\t\t\t\t</Upload>\n\t\t\t{:else if active_source === \"webcam\"}\n\t\t\t\t<Webcam\n\t\t\t\t\t{root}\n\t\t\t\t\t{mirror_webcam}\n\t\t\t\t\t{include_audio}\n\t\t\t\t\tmode=\"video\"\n\t\t\t\t\ton:error\n\t\t\t\t\ton:capture={handle_capture}\n\t\t\t\t\ton:start_recording\n\t\t\t\t\ton:stop_recording\n\t\t\t\t\t{i18n}\n\t\t\t\t/>\n\t\t\t{/if}\n\t\t</div>\n\t{:else}\n\t\t<ModifyUpload\n\t\t\t{i18n}\n\t\t\ton:clear={handle_clear}\n\t\t\tdownload={show_download_button ? value.url : null}\n\t\t/>\n\t\t{#if playable()}\n\t\t\t{#key value?.url}\n\t\t\t\t<Player\n\t\t\t\t\t{root}\n\t\t\t\t\tinteractive\n\t\t\t\t\t{autoplay}\n\t\t\t\t\tsrc={value.url}\n\t\t\t\t\tsubtitle={subtitle?.url}\n\t\t\t\t\ton:play\n\t\t\t\t\ton:pause\n\t\t\t\t\ton:stop\n\t\t\t\t\ton:end\n\t\t\t\t\tmirror={mirror_webcam && active_source === \"webcam\"}\n\t\t\t\t\t{label}\n\t\t\t\t\t{handle_change}\n\t\t\t\t\t{handle_reset_value}\n\t\t\t\t/>\n\t\t\t{/key}\n\t\t{:else if value.size}\n\t\t\t<div class=\"file-name\">{value.orig_name || value.url}</div>\n\t\t\t<div class=\"file-size\">\n\t\t\t\t{prettyBytes(value.size)}\n\t\t\t</div>\n\t\t{/if}\n\t{/if}\n\n\t<SelectSource {sources} bind:active_source {handle_clear} />\n</div>\n\n<style>\n\t.file-name {\n\t\tpadding: var(--size-6);\n\t\tfont-size: var(--text-xxl);\n\t\tword-break: break-all;\n\t}\n\n\t.file-size {\n\t\tpadding: var(--size-2);\n\t\tfont-size: var(--text-xl);\n\t}\n\n\t.upload-container {\n\t\theight: 100%;\n\t}\n\n\t.video-container {\n\t\tdisplay: flex;\n\t\theight: 100%;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher, afterUpdate, tick } from \"svelte\";\n\timport { BlockLabel, Empty, IconButton, ShareButton } from \"@gradio/atoms\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { Video, Download } from \"@gradio/icons\";\n\timport { uploadToHuggingFace } from \"@gradio/utils\";\n\timport { DownloadLink } from \"@gradio/wasm/svelte\";\n\n\timport Player from \"./Player.svelte\";\n\timport type { I18nFormatter } from \"js/app/src/gradio_helper\";\n\n\texport let value: FileData | null = null;\n\texport let subtitle: FileData | null = null;\n\texport let label: string | undefined = undefined;\n\texport let show_label = true;\n\texport let autoplay: boolean;\n\texport let show_share_button = true;\n\texport let show_download_button = true;\n\texport let i18n: I18nFormatter;\n\n\tlet old_value: FileData | null = null;\n\tlet old_subtitle: FileData | null = null;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: FileData;\n\t\tplay: undefined;\n\t\tpause: undefined;\n\t\tend: undefined;\n\t\tstop: undefined;\n\t}>();\n\n\t$: value && dispatch(\"change\", value);\n\n\tafterUpdate(async () => {\n\t\t// needed to bust subtitle caching issues on Chrome\n\t\tif (\n\t\t\tvalue !== old_value &&\n\t\t\tsubtitle !== old_subtitle &&\n\t\t\told_subtitle !== null\n\t\t) {\n\t\t\told_value = value;\n\t\t\tvalue = null;\n\t\t\tawait tick();\n\t\t\tvalue = old_value;\n\t\t}\n\t\told_value = value;\n\t\told_subtitle = subtitle;\n\t});\n</script>\n\n<BlockLabel {show_label} Icon={Video} label={label || \"Video\"} />\n{#if value === null || value.url === undefined}\n\t<Empty unpadded_box={true} size=\"large\"><Video /></Empty>\n{:else}\n\t{#key value.url}\n\t\t<Player\n\t\t\tsrc={value.url}\n\t\t\tsubtitle={subtitle?.url}\n\t\t\t{autoplay}\n\t\t\ton:play\n\t\t\ton:pause\n\t\t\ton:stop\n\t\t\ton:end\n\t\t\tmirror={false}\n\t\t\t{label}\n\t\t\tinteractive={false}\n\t\t/>\n\t{/key}\n\t<div class=\"icon-buttons\" data-testid=\"download-div\">\n\t\t{#if show_download_button}\n\t\t\t<DownloadLink href={value.url} download={value.orig_name || value.path}>\n\t\t\t\t<IconButton Icon={Download} label=\"Download\" />\n\t\t\t</DownloadLink>\n\t\t{/if}\n\t\t{#if show_share_button}\n\t\t\t<ShareButton\n\t\t\t\t{i18n}\n\t\t\t\ton:error\n\t\t\t\ton:share\n\t\t\t\t{value}\n\t\t\t\tformatter={async (value) => {\n\t\t\t\t\tif (!value) return \"\";\n\t\t\t\t\tlet url = await uploadToHuggingFace(value.data, \"url\");\n\t\t\t\t\treturn url;\n\t\t\t\t}}\n\t\t\t/>\n\t\t{/if}\n\t</div>\n{/if}\n\n<style>\n\t.icon-buttons {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: 6px;\n\t\tright: 6px;\n\t\tgap: var(--size-1);\n\t}\n</style>\n", "<svelte:options accessors={true} />\n\n<script lang=\"ts\">\n\timport type { Gradio, ShareData } from \"@gradio/utils\";\n\n\timport type { FileData } from \"@gradio/client\";\n\timport { Block, UploadText } from \"@gradio/atoms\";\n\timport StaticVideo from \"./shared/VideoPreview.svelte\";\n\timport Video from \"./shared/InteractiveVideo.svelte\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: { video: FileData; subtitles: FileData | null } | null =\n\t\tnull;\n\tlet old_value: { video: FileData; subtitles: FileData | null } | null = null;\n\n\texport let label: string;\n\texport let sources:\n\t\t| [\"webcam\"]\n\t\t| [\"upload\"]\n\t\t| [\"webcam\", \"upload\"]\n\t\t| [\"upload\", \"webcam\"];\n\texport let root: string;\n\texport let show_label: boolean;\n\texport let loading_status: LoadingStatus;\n\texport let height: number | undefined;\n\texport let width: number | undefined;\n\n\texport let container = false;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let autoplay = false;\n\texport let show_share_button = true;\n\texport let show_download_button: boolean;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tclear: never;\n\t\tplay: never;\n\t\tpause: never;\n\t\tupload: never;\n\t\tstop: never;\n\t\tend: never;\n\t\tstart_recording: never;\n\t\tstop_recording: never;\n\t\tshare: ShareData;\n\t\terror: string;\n\t\twarning: string;\n\t}>;\n\texport let interactive: boolean;\n\texport let mirror_webcam: boolean;\n\texport let include_audio: boolean;\n\n\tlet _video: FileData | null = null;\n\tlet _subtitle: FileData | null = null;\n\n\tlet active_source: \"webcam\" | \"upload\";\n\n\tlet initial_value: { video: FileData; subtitles: FileData | null } | null =\n\t\tvalue;\n\n\t$: if (value && initial_value === null) {\n\t\tinitial_value = value;\n\t}\n\n\tconst handle_reset_value = (): void => {\n\t\tif (initial_value === null || value === initial_value) {\n\t\t\treturn;\n\t\t}\n\n\t\tvalue = initial_value;\n\t};\n\n\t$: if (sources && !active_source) {\n\t\tactive_source = sources[0];\n\t}\n\n\t$: {\n\t\tif (value != null) {\n\t\t\t_video = value.video;\n\t\t\t_subtitle = value.subtitles;\n\t\t} else {\n\t\t\t_video = null;\n\t\t\t_subtitle = null;\n\t\t}\n\t}\n\n\tlet dragging = false;\n\n\t$: {\n\t\tif (JSON.stringify(value) !== JSON.stringify(old_value)) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t}\n\t}\n\n\tfunction handle_change({ detail }: CustomEvent<FileData | null>): void {\n\t\tif (detail != null) {\n\t\t\tvalue = { video: detail, subtitles: null } as {\n\t\t\t\tvideo: FileData;\n\t\t\t\tsubtitles: FileData | null;\n\t\t\t} | null;\n\t\t} else {\n\t\t\tvalue = null;\n\t\t}\n\t}\n\n\tfunction handle_error({ detail }: CustomEvent<string>): void {\n\t\tconst [level, status] = detail.includes(\"Invalid file type\")\n\t\t\t? [\"warning\", \"complete\"]\n\t\t\t: [\"error\", \"error\"];\n\t\tloading_status = loading_status || {};\n\t\tloading_status.status = status as LoadingStatus[\"status\"];\n\t\tloading_status.message = detail;\n\t\tgradio.dispatch(level as \"error\" | \"warning\", detail);\n\t}\n</script>\n\n{#if !interactive}\n\t<Block\n\t\t{visible}\n\t\tvariant={value === null && active_source === \"upload\" ? \"dashed\" : \"solid\"}\n\t\tborder_mode={dragging ? \"focus\" : \"base\"}\n\t\tpadding={false}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\t{height}\n\t\t{width}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t\tallow_overflow={false}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t/>\n\n\t\t<StaticVideo\n\t\t\tvalue={_video}\n\t\t\tsubtitle={_subtitle}\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{autoplay}\n\t\t\t{show_share_button}\n\t\t\t{show_download_button}\n\t\t\ton:play={() => gradio.dispatch(\"play\")}\n\t\t\ton:pause={() => gradio.dispatch(\"pause\")}\n\t\t\ton:stop={() => gradio.dispatch(\"stop\")}\n\t\t\ton:end={() => gradio.dispatch(\"end\")}\n\t\t\ton:share={({ detail }) => gradio.dispatch(\"share\", detail)}\n\t\t\ton:error={({ detail }) => gradio.dispatch(\"error\", detail)}\n\t\t\ti18n={gradio.i18n}\n\t\t/>\n\t</Block>\n{:else}\n\t<Block\n\t\t{visible}\n\t\tvariant={value === null && active_source === \"upload\" ? \"dashed\" : \"solid\"}\n\t\tborder_mode={dragging ? \"focus\" : \"base\"}\n\t\tpadding={false}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\t{height}\n\t\t{width}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t\tallow_overflow={false}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t/>\n\n\t\t<Video\n\t\t\tvalue={_video}\n\t\t\tsubtitle={_subtitle}\n\t\t\ton:change={handle_change}\n\t\t\ton:drag={({ detail }) => (dragging = detail)}\n\t\t\ton:error={handle_error}\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{show_download_button}\n\t\t\t{sources}\n\t\t\t{active_source}\n\t\t\t{mirror_webcam}\n\t\t\t{include_audio}\n\t\t\t{autoplay}\n\t\t\t{root}\n\t\t\t{handle_reset_value}\n\t\t\ton:clear={() => gradio.dispatch(\"clear\")}\n\t\t\ton:play={() => gradio.dispatch(\"play\")}\n\t\t\ton:pause={() => gradio.dispatch(\"pause\")}\n\t\t\ton:upload={() => gradio.dispatch(\"upload\")}\n\t\t\ton:stop={() => gradio.dispatch(\"stop\")}\n\t\t\ton:end={() => gradio.dispatch(\"end\")}\n\t\t\ton:start_recording={() => gradio.dispatch(\"start_recording\")}\n\t\t\ton:stop_recording={() => gradio.dispatch(\"stop_recording\")}\n\t\t\ti18n={gradio.i18n}\n\t\t>\n\t\t\t<UploadText i18n={gradio.i18n} type=\"video\" />\n\t\t</Video>\n\t</Block>\n{/if}\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "onDestroy", "ctx", "i", "set_style", "div0", "div1", "button0", "button1", "div", "attr", "img", "img_src_value", "create_if_block", "numberOfThumbnails", "videoElement", "$$props", "trimmedDuration", "dragStart", "dragEnd", "loadingTimeline", "thumbnails", "videoDuration", "leftHandlePosition", "rightHandlePosition", "dragging", "startDragging", "side", "stopDragging", "drag", "event", "distance", "timeline", "rect", "newPercentage", "$$invalidate", "newTimeLeft", "newTimeRight", "startTime", "endTime", "moveHandle", "e", "generateThumbnail", "canvas", "thumbnail", "onMount", "loadMetadata", "interval", "captures", "onSeeked", "mousedown_handler", "mousedown_handler_1", "t_value", "format_time", "time", "dirty", "set_data", "button", "if_block0", "create_if_block_4", "create_if_block_3", "create_if_block_2", "div2", "showRedo", "interactive", "mode", "handle_reset_value", "handle_trim_video", "processingVideo", "ffmpeg", "loadFfmpeg", "toggleTrimmingMode", "trimVideo", "videoBlob", "createEventDispatcher", "track", "track_src_value", "t2_value", "t4_value", "div4", "div3", "span0", "span1", "progress", "current", "t2", "t4", "root", "src", "subtitle", "mirror", "autoplay", "label", "handle_change", "dispatch", "duration", "paused", "video", "handleMove", "handle_click", "clientX", "left", "right", "play_pause", "handle_end", "_video_blob", "val", "prepare_files", "value", "upload", "open_full_screen", "playable", "t0_value", "prettyBytes", "t0", "previous_key", "safe_not_equal", "player_changes", "Video", "blocklabel_changes", "sources", "show_download_button", "show_label", "mirror_webcam", "include_audio", "i18n", "active_source", "handle_load", "detail", "handle_clear", "handle_capture", "error_handler_1", "afterUpdate", "tick", "create_if_block_1", "downloadlink_changes", "Download", "show_share_button", "old_value", "old_subtitle", "uploadToHuggingFace", "block_changes", "uploadtext_changes", "video_changes", "staticvideo_changes", "elem_id", "elem_classes", "visible", "loading_status", "height", "width", "container", "scale", "min_width", "gradio", "_video", "_subtitle", "initial_value", "handle_error", "level", "status", "share_handler", "error_handler"], "mappings": "4zDAAAA,GAcKC,EAAAC,EAAAC,CAAA,EAHJC,GAECF,EAAAG,CAAA,wXCZe,UAAAC,IAAW,OAAgB,qJA8KlCC,EAAU,CAAA,CAAA,aAAkBA,EAAC,EAAA,kBAAlC,OAAIC,GAAA,EAAA,wRARSD,EAAkB,CAAA,EAAA,GAAA,yDAKlBA,EAAkB,CAAA,EAAA,GAAA,EAAYE,GAAAC,EAAA,QAAA,IAAMH,EAAmB,CAAA,EAAA,GAAA,gHAgBvDA,EAAmB,CAAA,EAAA,GAAA,+EAhCnCP,GAkCKC,EAAAU,EAAAR,CAAA,EAjCJC,GAWCO,EAAAC,CAAA,UAEDR,GAGCO,EAAAD,CAAA,mEAKDN,GAWCO,EAAAE,CAAA,6CA5BSN,EAAY,CAAA,CAAA,4DAqBZA,EAAY,CAAA,CAAA,wDAfPA,EAAkB,CAAA,EAAA,GAAA,mBAKlBA,EAAkB,CAAA,EAAA,GAAA,OAAYE,GAAAC,EAAA,QAAA,IAAMH,EAAmB,CAAA,EAAA,GAAA,aAG/DA,EAAU,CAAA,CAAA,qDAaFA,EAAmB,CAAA,EAAA,GAAA,uPApCnCP,GAEKC,EAAAa,EAAAX,CAAA,sGAsBOI,EAAS,EAAA,CAAA,GAAAQ,EAAAC,EAAA,MAAAC,CAAA,uBAAgBV,EAAC,EAAA,GAAA,8EAApCP,GAA2DC,EAAAe,EAAAb,CAAA,+BAAjDI,EAAS,EAAA,CAAA,qCAAgBA,EAAC,EAAA,gFAzBlCA,EAAe,CAAA,EAAAW,qGADrBlB,GA0CKC,EAAAa,EAAAX,CAAA,yHAtLA,IAAAgB,GAAqB,0BAPd,aAAAC,CAA8B,EAAAC,GAC9B,gBAAAC,CAA8B,EAAAD,GAC9B,UAAAE,CAAiB,EAAAF,GACjB,QAAAG,CAAe,EAAAH,GACf,gBAAAI,CAAwB,EAAAJ,EAE/BK,EAAU,CAAA,EAGVC,EAEAC,EAAqB,EACrBC,EAAsB,IAEtBC,EAA0B,KAExB,MAAAC,EAAiBC,GAAmB,CACzCF,EAAWE,GAKNC,EAAY,IAAA,CACjBH,EAAW,MAGNI,EAAI,CAAIC,EAA4BC,IAAiB,IACtDN,EAAQ,CACL,MAAAO,EAAW,SAAS,eAAe,UAAU,MAE9CA,EAAQ,aAEPC,EAAOD,EAAS,wBAClB,IAAAE,GAAkBJ,EAAM,QAAUG,EAAK,MAAQA,EAAK,MAAS,IAe7D,GAbAF,EAEHG,EACCT,IAAa,OACVF,EAAqBQ,EACrBP,EAAsBO,EAG1BG,GAAkBJ,EAAM,QAAUG,EAAK,MAAQA,EAAK,MAAS,IAG9DC,EAAgB,KAAK,IAAI,EAAG,KAAK,IAAIA,EAAe,GAAG,CAAA,EAEnDT,IAAa,OAAM,CACtBU,EAAA,EAAAZ,EAAqB,KAAK,IAAIW,EAAeV,CAAmB,CAAA,EAG1D,MAAAY,EAAeb,EAAqB,IAAOD,MACjDP,EAAa,YAAcqB,EAAWrB,CAAA,EAEtCoB,EAAA,EAAAjB,EAAYkB,CAAW,UACbX,IAAa,QAAO,CAC9BU,EAAA,EAAAX,EAAsB,KAAK,IAAIU,EAAeX,CAAkB,CAAA,EAE1D,MAAAc,EAAgBb,EAAsB,IAAOF,MACnDP,EAAa,YAAcsB,EAAYtB,CAAA,EAEvCoB,EAAA,EAAAhB,EAAUkB,CAAY,EAGjB,MAAAC,EAAaf,EAAqB,IAAOD,EACzCiB,EAAWf,EAAsB,IAAOF,MAC9CL,EAAkBsB,EAAUD,CAAS,kBAOjCE,EAAcC,GAAgB,IAC/BhB,EAAQ,CAEL,MAAAM,EAAY,EAAIT,EAAiB,IAEnCmB,EAAE,MAAQ,YACbZ,EAAO,CAAA,QAAS,CAAC,GAAKE,CAAQ,EACpBU,EAAE,MAAQ,cACpBZ,EAAO,CAAA,QAAS,GAAKE,CAAQ,IAK1BW,EAAiB,IAAA,CAChB,MAAAC,EAAS,SAAS,cAAc,QAAQ,EACxCzC,EAAMyC,EAAO,WAAW,IAAI,MAC7BzC,EAAG,OAERyC,EAAO,MAAQ5B,EAAa,WAC5B4B,EAAO,OAAS5B,EAAa,YAE7Bb,EAAI,UAAUa,EAAc,EAAG,EAAG4B,EAAO,MAAOA,EAAO,MAAM,EAEvD,MAAAC,EAAoBD,EAAO,UAAU,aAAc,EAAG,MAC5DtB,EAAU,CAAA,GAAOA,EAAYuB,CAAS,CAAA,GAGvCC,GAAO,IAAA,OACAC,EAAY,IAAA,CACjBxB,EAAgBP,EAAa,eAEvBgC,EAAWzB,EAAgBR,GAC7B,IAAAkC,EAAW,QAETC,EAAQ,IAAA,CACbP,IACAM,IAEIA,EAAWlC,OACdC,EAAa,aAAegC,EAAQhC,CAAA,EAEpCA,EAAa,oBAAoB,SAAUkC,CAAQ,GAIrDlC,EAAa,iBAAiB,SAAUkC,CAAQ,MAChDlC,EAAa,YAAc,EAACA,CAAA,GAGzBA,EAAa,YAAc,EAC9B+B,IAEA/B,EAAa,iBAAiB,iBAAkB+B,CAAY,IAI9D7C,GAAS,IAAA,CACR,OAAO,oBAAoB,YAAa4B,CAAI,EAC5C,OAAO,oBAAoB,UAAWD,CAAY,EAClD,OAAO,oBAAoB,UAAWY,CAAU,IAOjDK,GAAO,IAAA,CACN,OAAO,iBAAiB,YAAahB,CAAI,EACzC,OAAO,iBAAiB,UAAWD,CAAY,EAC/C,OAAO,iBAAiB,UAAWY,CAAU,IAcvB,MAAAU,EAAA,IAAAxB,EAAc,MAAM,IAE3Be,GAAC,EACTA,EAAE,MAAQ,aAAeA,EAAE,KAAO,eACrCf,EAAc,MAAM,GAiBFyB,EAAA,IAAAzB,EAAc,OAAO,IAE5Be,GAAC,EACTA,EAAE,MAAQ,aAAeA,EAAE,KAAO,eACrCf,EAAc,OAAO,wQAlKvBN,EAAkBC,EAAW,SAAWP,EAAkB,qjBCjBpD,CAAA,QAAA+B,WAAuB,0kBAuC/BlD,GAQKC,EAAAa,EAAAX,CAAA,mcAUJH,GAAMC,EAAAa,EAAAX,CAAA,6CAH2BsD,EAAAC,GAAYnD,EAAe,CAAA,CAAA,EAAA,2IAA7CA,EAAe,EAAA,CAAA,UAF9BP,GAGAC,EAAA0D,EAAAxD,CAAA,kBADiCyD,EAAA,KAAAH,KAAAA,EAAAC,GAAYnD,EAAe,CAAA,CAAA,EAAA,KAAAsD,GAAA,EAAAJ,CAAA,wBAA7ClD,EAAe,EAAA,CAAA,8JAUlBA,EAAe,CAAA,2DAF1BP,GAUQC,EAAA6D,EAAA3D,CAAA,mFARGI,EAAe,CAAA,oKAYrB,OAAAA,OAAS,GAAE,2dAWAA,EAAe,EAAA,CAAA,yDAgBfA,EAAe,EAAA,CAAA,UAjB9BP,GAcAC,EAAAW,EAAAT,CAAA,YACAH,GAIAC,EAAAY,EAAAV,CAAA,0CADWI,EAAkB,EAAA,CAAA,uCAjBdA,EAAe,EAAA,CAAA,wBAgBfA,EAAe,EAAA,CAAA,wJAzBnBA,EAAe,CAAA,+FAD1BP,GAOQC,EAAA6D,EAAA3D,CAAA,uCAHGI,EAAkB,EAAA,CAAA,uCAHlBA,EAAe,CAAA,qIAxCzBwD,EAAAxD,OAAS,QAAMyD,GAAAzD,CAAA,kBAad,OAAAA,EAAS,CAAA,IAAA,QAAUA,OAAoB,KAAI0D,0BAU1C1D,EAAQ,CAAA,GAAIA,EAAI,CAAA,IAAK,IAAE2D,GAAA3D,CAAA,IAcvBA,EAAW,CAAA,GAAAW,GAAAX,CAAA,iRAtCnBP,GAyEKC,EAAAkE,EAAAhE,CAAA,yBA5DJC,GA2DK+D,EAAAxD,CAAA,sBAjDJP,GAgDKO,EAAAD,CAAA,uDAtEDH,OAAS,kKAuBPA,EAAQ,CAAA,GAAIA,EAAI,CAAA,IAAK,iGAcrBA,EAAW,CAAA,sOAxEP,aAAAa,CAA8B,EAAAC,EAE9B,CAAA,SAAA+C,EAAW,EAAK,EAAA/C,EAChB,CAAA,YAAAgD,EAAc,EAAI,EAAAhD,EAClB,CAAA,KAAAiD,EAAO,EAAE,EAAAjD,GACT,mBAAAkD,CAA8B,EAAAlD,GAC9B,kBAAAmD,CAA4C,EAAAnD,EAC5C,CAAA,gBAAAoD,EAAkB,EAAK,EAAApD,EAE9BqD,EAEJxB,GAAO,SAAA,CACNV,EAAA,EAAAkC,QAAeC,GAAU,CAAA,IAMtB,IAAArD,EAAiC,KACjCC,EAAY,EACZC,EAAU,EAEVC,EAAkB,SAEhBmD,EAAkB,IAAA,CACnBN,IAAS,QACZ9B,EAAA,EAAA8B,EAAO,EAAE,MACThD,EAAkBF,EAAa,QAAQ,GAEvCoB,EAAA,EAAA8B,EAAO,MAAM,oIAmCVC,IACA/B,EAAA,EAAA8B,EAAO,EAAE,UAsBR9B,EAAA,EAAA8B,EAAO,EAAE,EACT9B,EAAA,EAAAiC,EAAkB,EAAI,EACtBI,GAAUH,EAAQnD,EAAWC,EAASJ,CAAY,EAChD,KAAM0D,GAAS,CACfN,EAAkBM,CAAS,IAE3B,KAAI,IAAA,CACJtC,EAAA,EAAAiC,EAAkB,EAAK,kWA/ExBH,IAAS,QAAUhD,IAAoB,MAAQF,GACrDoB,EAAA,EAAAlB,EAAkBF,EAAa,QAAQ,ypBCxB/B,uBAAA2D,EAAA,SAAqC,6GA2GhBxE,EAAQ,CAAA,CAAA,GAAAQ,EAAAiE,EAAA,MAAAC,CAAA,uBAApCjF,GAA+CC,EAAA+E,EAAA7E,CAAA,2BAAnBI,EAAQ,CAAA,CAAA,mmBAkDvBA,EAAK,EAAA,oQAALA,EAAK,EAAA,yOA3BE2E,EAAAxB,GAAYnD,EAAI,CAAA,CAAA,EAAA,OAAM4E,EAAAzB,GAAYnD,EAAQ,CAAA,CAAA,EAAA,uLA1B9CA,EAAK,CAAA,yEAJHA,EAAI,CAAA,IAAA,wBAAJA,EAAI,CAAA,qEAGXA,EAAK,EAAA,IAAA,iBAALA,EAAK,EAAA,oKAPNA,EAAU,EAAA,CAAA,yDAGVA,EAAU,EAAA,CAAA,6CAsBd,OAAAA,OAASA,EAAQ,CAAA,EAAA,EAEZA,EAAM,CAAA,EAAA,gDAgCfA,EAAW,CAAA,GAAAW,GAAAX,CAAA,0IAzByB,KAAG,+TAMjCA,EAAI,CAAA,EAAGA,EAAQ,CAAA,GAAI,6PA9C9BP,GAgEKC,EAAAmF,EAAAjF,CAAA,EA/DJC,EAkBKgF,EAAA1E,CAAA,sBAELN,EA0CKgF,EAAAC,CAAA,EAzCJjF,EAwCKiF,EAAAlB,CAAA,EAvCJ/D,EAeM+D,EAAAmB,CAAA,yBAENlF,EAAsE+D,EAAAoB,CAAA,8BAKtEnF,EAKC+D,EAAAqB,CAAA,SAEDpF,EASK+D,EAAAxD,CAAA,wEAjCMJ,EAAU,EAAA,CAAA,iBACRA,EAAU,EAAA,CAAA,mBAkBRA,EAAU,EAAA,CAAA,sBACKA,EAAU,EAAA,CAAA,CAAA,qBACEA,EAAY,EAAA,CAAA,CAAA,CAAA,eAQ3CA,EAAgB,EAAA,CAAA,kBACbA,EAAgB,EAAA,CAAA,kGA5CdA,EAAK,CAAA,sHAJHA,EAAI,CAAA,qIAGXA,EAAK,EAAA,sLA2BI,CAAAkF,GAAA7B,EAAA,MAAAsB,KAAAA,EAAAxB,GAAYnD,EAAI,CAAA,CAAA,EAAA,KAAAsD,GAAA6B,EAAAR,CAAA,GAAM,CAAAO,GAAA7B,EAAA,MAAAuB,KAAAA,EAAAzB,GAAYnD,EAAQ,CAAA,CAAA,EAAA,KAAAsD,GAAA8B,EAAAR,CAAA,qBAMtD5E,EAAI,CAAA,EAAGA,EAAQ,CAAA,GAAI,iBAmBzBA,EAAW,CAAA,oUAnJJ,GAAA,CAAA,KAAAqF,EAAO,EAAE,EAAAvE,GACT,IAAAwE,CAAW,EAAAxE,EACX,CAAA,SAAAyE,EAA0B,IAAI,EAAAzE,GAC9B,OAAA0E,CAAe,EAAA1E,GACf,SAAA2E,CAAiB,EAAA3E,EACjB,CAAA,MAAA4E,EAAQ,MAAM,EAAA5E,EACd,CAAA,YAAAgD,EAAc,EAAK,EAAAhD,GACnB,cAAA6E,EAAa,IAAA,OACb,mBAAA3B,EAAkB,IAAA,MAEvB,MAAA4B,EAAWpB,KAOb,IAAApB,EAAO,EACPyC,EACAC,EAAS,GACTC,EACA7B,EAAkB,GAEb,SAAA8B,EAAWzD,EAA0B,KACxCsD,EAAQ,UAETtD,EAAE,OAAS,QAAO,CACrB0D,EAAa1D,CAAe,YAIzBA,EAAE,OAAS,eAAkBA,EAAiB,QAAU,GAAC,OAEvD,MAAA2D,EACL3D,EAAE,OAAS,YACPA,EAAiB,QAAQ,CAAC,EAAE,QAC5BA,EAAiB,QACd,CAAA,KAAA4D,EAAM,MAAAC,CAAK,EAClB7D,EAAE,cACD,4BACFa,EAAQyC,GAAYK,EAAUC,IAAUC,EAAQD,EAAI,iBAGtCE,GAAU,CACpB,SAAS,mBAAqBN,IAEhCA,EAAM,YAAc,GAAC,CACpBA,EAAM,SACNA,EAAM,OACPA,EAAM,WAAaA,EAAM,kBAInBA,EAAM,QADN,MAAAA,EAAM,QAKN,SAAAE,EAAa1D,EAAa,CAC1B,KAAA,CAAA,KAAA4D,EAAM,MAAAC,CAAK,EAClB7D,EAAE,cACD,4BACFa,EAAQyC,GAAYtD,EAAE,QAAU4D,IAAUC,EAAQD,EAAI,WAG9CG,GAAU,CAClBV,EAAS,MAAM,EACfA,EAAS,KAAK,EAGT,MAAA3B,QAA2BM,GAAe,CAC3C,IAAAgC,EAAkB,IAAA,KAAM,CAAAhC,CAAS,EAAG,WAAW,QAC7CiC,EAAG,MAASC,GAAa,CAAEF,CAAW,CAAA,MACxCG,GAAK,MAAWC,GAAOH,EAAKnB,CAAI,IAAI,OAAO,OAAO,EAAiB,CAAC,EAExEM,EAAce,CAAK,YAGXE,GAAgB,CACxBb,EAAM,kBAAiB,gBAcJ3C,EAAIsD,yEAGXX,EAAKW,qmCCvGT,uBAAAlC,EAAA,SAAqC,iGAqGlCxE,EAAoB,CAAA,EAAGA,KAAM,IAAM,sBADnCA,EAAY,EAAA,CAAA,4DAGlB6G,GAAQ,OAkBH7G,KAAM,KAAI,4MApBTA,EAAoB,CAAA,EAAGA,KAAM,IAAM,4WA5BxC,OAAAA,OAAkB,SAAQ,EAUrBA,OAAkB,SAAQ,yHAXrCP,GAwBKC,EAAAa,EAAAX,CAAA,oRA0BoBkH,GAAA9G,EAAM,CAAA,EAAA,WAAaA,KAAM,KAAG,WAElD+G,GAAY/G,EAAK,CAAA,EAAC,IAAI,EAAA,yJAFxBP,GAA0DC,EAAAS,EAAAP,CAAA,oBAC1DH,GAEKC,EAAAU,EAAAR,CAAA,kBAHmByD,EAAA,GAAAyD,KAAAA,GAAA9G,EAAM,CAAA,EAAA,WAAaA,KAAM,KAAG,KAAAsD,GAAA0D,EAAAF,CAAA,cAElDC,GAAY/G,EAAK,CAAA,EAAC,IAAI,EAAA,KAAAsD,GAAA6B,EAAAR,CAAA,0DApBlB,IAAAsC,EAAAjH,MAAO,gFAAPqD,EAAA,GAAA6D,GAAAD,EAAAA,EAAAjH,MAAO,GAAG,gOAKT,IAAAA,KAAM,IACD,SAAAA,MAAU,WAKZA,EAAa,CAAA,GAAIA,EAAa,CAAA,IAAK,qQANtCqD,EAAA,IAAA8D,EAAA,IAAAnH,KAAM,KACDqD,EAAA,IAAA8D,EAAA,SAAAnH,MAAU,sBAKZA,EAAa,CAAA,GAAIA,EAAa,CAAA,IAAK,+TAzB/BA,EAAc,EAAA,CAAA,yjBAbjBA,EAAW,EAAA,CAAA,gnBARMoH,GAAc,MAAApH,MAAS,gDAEhD,OAAAA,OAAU,MAAQA,EAAM,CAAA,EAAA,MAAQ,OAAS,+WAD/CP,GA4DKC,EAAAa,EAAAX,CAAA,0FA7DwCyD,EAAA,KAAAgE,EAAA,MAAArH,MAAS,ocAzD1C,CAAA,MAAA0G,EAAyB,IAAI,EAAA5F,EAC7B,CAAA,SAAAyE,EAA4B,IAAI,EAAAzE,GAChC,QAAAwG,EAAO,CAIS,SAAU,QAAQ,CAAA,EAAAxG,EAClC,CAAA,MAAA4E,EAA4B,MAAS,EAAA5E,EACrC,CAAA,qBAAAyG,EAAuB,EAAK,EAAAzG,EAC5B,CAAA,WAAA0G,EAAa,EAAI,EAAA1G,EACjB,CAAA,cAAA2G,EAAgB,EAAK,EAAA3G,GACrB,cAAA4G,CAAsB,EAAA5G,GACtB,SAAA2E,CAAiB,EAAA3E,GACjB,KAAAuE,CAAY,EAAAvE,GACZ,KAAA6G,CAAmB,EAAA7G,EACnB,CAAA,cAAA8G,EAAqC,QAAQ,EAAA9G,GAC7C,mBAAAkD,EAAkB,IAAA,MAEvB,MAAA4B,EAAWpB,KAaR,SAAAqD,GAAc,OAAAC,GAAM,CAC5B7F,EAAA,EAAAyE,EAAQoB,CAAM,EACdlC,EAAS,SAAUkC,CAAM,EACzBlC,EAAS,SAAUkC,CAAO,WAGlBC,GAAY,CACpB9F,EAAA,EAAAyE,EAAQ,IAAI,EACZd,EAAS,SAAU,IAAI,EACvBA,EAAS,OAAO,EAGR,SAAAD,EAAcI,EAAe,CACrCH,EAAS,SAAUG,CAAK,EAGhB,SAAAiC,GACR,OAAAF,GAAM,CAENlC,EAAS,SAAUkC,CAAM,EAGtB,IAAAvG,EAAW,6BAaE,MAAA0G,EAAA,CAAA,CAAA,OAAAH,CAAM,IAAOlC,EAAS,QAASkC,CAAM,+zBAZnDlC,EAAS,OAAQrE,CAAQ,mmBClEnB,CAAA,sBAAAiD,GAAuB,YAAA0D,GAAa,KAAAC,EAAA,SAAoB,0CAqD3D,IAAAlB,EAAAjH,KAAM,sBAeNA,EAAoB,CAAA,GAAA2D,GAAA3D,CAAA,IAKpBA,EAAiB,CAAA,GAAAoI,GAAApI,CAAA,wKANvBP,GAmBKC,EAAAa,EAAAX,CAAA,qDAjCCyD,EAAA,GAAA6D,GAAAD,EAAAA,EAAAjH,KAAM,GAAG,4EAeTA,EAAoB,CAAA,iGAKpBA,EAAiB,CAAA,2QAtBF,2SAId,IAAAA,KAAM,IACD,SAAAA,MAAU,yBAMZ,0BAEK,qJATRqD,EAAA,IAAA8D,EAAA,IAAAnH,KAAM,KACDqD,EAAA,IAAA8D,EAAA,SAAAnH,MAAU,2LAaA,KAAAA,KAAM,IAAe,SAAAA,EAAM,CAAA,EAAA,WAAaA,KAAM,gHAA9CqD,EAAA,IAAAgF,EAAA,KAAArI,KAAM,KAAeqD,EAAA,IAAAgF,EAAA,SAAArI,EAAM,CAAA,EAAA,WAAaA,KAAM,2LAC/CsI,GAAQ,MAAA,UAAA,6rBArBClB,GAAc,MAAApH,MAAS,gDACjD,OAAAA,OAAU,MAAQA,EAAM,CAAA,EAAA,MAAQ,OAAS,iLADDqD,EAAA,IAAAgE,EAAA,MAAArH,MAAS,ySAvC1C,GAAA,CAAA,MAAA0G,EAAyB,IAAI,EAAA5F,EAC7B,CAAA,SAAAyE,EAA4B,IAAI,EAAAzE,EAChC,CAAA,MAAA4E,EAA4B,MAAS,EAAA5E,EACrC,CAAA,WAAA0G,EAAa,EAAI,EAAA1G,GACjB,SAAA2E,CAAiB,EAAA3E,EACjB,CAAA,kBAAAyH,EAAoB,EAAI,EAAAzH,EACxB,CAAA,qBAAAyG,EAAuB,EAAI,EAAAzG,GAC3B,KAAA6G,CAAmB,EAAA7G,EAE1B0H,EAA6B,KAC7BC,EAAgC,KAE9B,MAAA7C,EAAWpB,KAUjB0D,GAAW,SAAA,CAGTxB,IAAU8B,GACVjD,IAAakD,GACbA,IAAiB,OAEjBD,EAAY9B,EACZzE,EAAA,EAAAyE,EAAQ,IAAI,QACNyB,GAAI,EACVlG,EAAA,EAAAyE,EAAQ8B,CAAS,GAElBA,EAAY9B,EACZ+B,EAAelD,kJAkCKmB,GACZA,QACWgC,GAAoBhC,EAAM,KAAM,KAAK,EADlC,gaAlDpBA,GAASd,EAAS,SAAUc,CAAK,okBCkI1B,QAAA1G,EAAU,CAAA,IAAA,MAAQA,QAAkB,SAAW,SAAW,oBACtDA,EAAQ,EAAA,EAAG,QAAU,eACzB,qHAQO,wIAVPqD,EAAA,CAAA,EAAA,UAAAsF,EAAA,QAAA3I,EAAU,CAAA,IAAA,MAAQA,QAAkB,SAAW,SAAW,uCACtDA,EAAQ,EAAA,EAAG,QAAU,4ZAvCzB,QAAAA,EAAU,CAAA,IAAA,MAAQA,QAAkB,SAAW,SAAW,oBACtDA,EAAQ,EAAA,EAAG,QAAU,eACzB,qHAQO,wIAVPqD,EAAA,CAAA,EAAA,UAAAsF,EAAA,QAAA3I,EAAU,CAAA,IAAA,MAAQA,QAAkB,SAAW,SAAW,uCACtDA,EAAQ,EAAA,EAAG,QAAU,+YAiFf,KAAAA,MAAO,sFAAPqD,EAAA,CAAA,EAAA,SAAAuF,EAAA,KAAA5I,MAAO,wIA/Bb,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,uGAIXA,EAAM,EAAA,WACHA,EAAS,EAAA,mLAsBb,KAAAA,MAAO,8DArBFA,EAAa,EAAA,CAAA,oCAEdA,EAAY,EAAA,CAAA,qUAVV,WAAAA,MAAO,YACbqD,EAAA,CAAA,EAAA,QAAA,CAAA,KAAArD,MAAO,IAAI,aACbA,EAAc,CAAA,CAAA,mDAIXA,EAAM,EAAA,6BACHA,EAAS,EAAA,sSAsBbqD,EAAA,CAAA,EAAA,SAAAwF,EAAA,KAAA7I,MAAO,qPAnED,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,uGAIXA,EAAM,EAAA,WACHA,EAAS,EAAA,+FAYb,KAAAA,MAAO,6QAnBD,WAAAA,MAAO,YACbqD,EAAA,CAAA,EAAA,QAAA,CAAA,KAAArD,MAAO,IAAI,aACbA,EAAc,CAAA,CAAA,mDAIXA,EAAM,EAAA,6BACHA,EAAS,EAAA,2KAYbqD,EAAA,CAAA,EAAA,SAAAyF,EAAA,KAAA9I,MAAO,mOAnCVA,EAAW,EAAA,IAAA,gUA5GL,GAAA,CAAA,QAAA+I,EAAU,EAAE,EAAAjI,GACZ,aAAAkI,EAAY,EAAA,EAAAlI,EACZ,CAAA,QAAAmI,EAAU,EAAI,EAAAnI,EACd,CAAA,MAAA4F,EACV,IAAI,EAAA5F,EACD0H,EAAoE,MAE7D,MAAA9C,CAAa,EAAA5E,GACb,QAAAwG,CAIY,EAAAxG,GACZ,KAAAuE,CAAY,EAAAvE,GACZ,WAAA0G,CAAmB,EAAA1G,GACnB,eAAAoI,CAA6B,EAAApI,GAC7B,OAAAqI,CAA0B,EAAArI,GAC1B,MAAAsI,CAAyB,EAAAtI,EAEzB,CAAA,UAAAuI,EAAY,EAAK,EAAAvI,EACjB,CAAA,MAAAwI,EAAuB,IAAI,EAAAxI,EAC3B,CAAA,UAAAyI,EAAgC,MAAS,EAAAzI,EACzC,CAAA,SAAA2E,EAAW,EAAK,EAAA3E,EAChB,CAAA,kBAAAyH,EAAoB,EAAI,EAAAzH,GACxB,qBAAAyG,CAA6B,EAAAzG,GAC7B,OAAA0I,CAaT,EAAA1I,GACS,YAAAgD,CAAoB,EAAAhD,GACpB,cAAA2G,CAAsB,EAAA3G,GACtB,cAAA4G,CAAsB,EAAA5G,EAE7B2I,EAA0B,KAC1BC,EAA6B,KAE7B9B,EAEA+B,EACHjD,QAMK1C,EAAkB,IAAA,CACnB2F,IAAkB,MAAQjD,IAAUiD,GAIxC1H,EAAA,EAAAyE,EAAQiD,CAAa,GAiBlB,IAAApI,EAAW,GASN,SAAAoE,GAAgB,OAAAmC,GAAM,CAC1BA,GAAU,KACb7F,EAAA,EAAAyE,GAAU,MAAOoB,EAAQ,UAAW,IAAI,CAAA,EAKxC7F,EAAA,EAAAyE,EAAQ,IAAI,EAIL,SAAAkD,GAAe,OAAA9B,GAAM,CACtB,KAAA,CAAA+B,GAAOC,EAAM,EAAIhC,EAAO,SAAS,mBAAmB,EACvD,CAAA,UAAW,UAAU,EACrB,CAAA,QAAS,OAAO,EACpB7F,EAAA,EAAAiH,EAAiBA,GAAc,CAAA,CAAA,MAC/BA,EAAe,OAASY,GAAiCZ,CAAA,MACzDA,EAAe,QAAUpB,EAAMoB,CAAA,EAC/BM,EAAO,SAASK,GAA8B/B,CAAM,cAiCpC0B,EAAO,SAAS,MAAM,QACrBA,EAAO,SAAS,OAAO,SACxBA,EAAO,SAAS,MAAM,SACvBA,EAAO,SAAS,KAAK,EACtBO,GAAA,CAAA,CAAA,OAAAjC,KAAa0B,EAAO,SAAS,QAAS1B,CAAM,EAC5CkC,GAAA,CAAA,CAAA,OAAAlC,KAAa0B,EAAO,SAAS,QAAS1B,CAAM,OA6B7C,OAAAA,CAAM,IAAA7F,EAAA,GAAQV,EAAWuG,CAAM,QAY3B0B,EAAO,SAAS,OAAO,QACxBA,EAAO,SAAS,MAAM,QACrBA,EAAO,SAAS,OAAO,QACtBA,EAAO,SAAS,QAAQ,SAC1BA,EAAO,SAAS,MAAM,SACvBA,EAAO,SAAS,KAAK,SACTA,EAAO,SAAS,iBAAiB,SAClCA,EAAO,SAAS,gBAAgB,u0BA3IpD9C,GAASiD,IAAkB,MACjC1H,EAAA,GAAA0H,EAAgBjD,CAAK,yBAWfY,GAAO,CAAKM,QAClBA,EAAgBN,EAAQ,CAAC,CAAA,oBAIrBZ,GAAS,WACZ+C,EAAS/C,EAAM,KAAK,OACpBgD,EAAYhD,EAAM,SAAS,IAE3BzE,EAAA,GAAAwH,EAAS,IAAI,EACbxH,EAAA,GAAAyH,EAAY,IAAI,6BAOb,KAAK,UAAUhD,CAAK,IAAM,KAAK,UAAU8B,CAAS,IACrDvG,EAAA,GAAAuG,EAAY9B,CAAK,EACjB8C,EAAO,SAAS,QAAQ"}