{"version": 3, "file": "Index-01713e13.js", "sources": ["../../../../js/icons/src/JSON.svelte", "../../../../js/json/shared/JSONNode.svelte", "../../../../js/json/shared/JSON.svelte", "../../../../js/json/Index.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\taria-hidden=\"true\"\n\trole=\"img\"\n\tclass=\"iconify iconify--mdi\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tpreserveAspectRatio=\"xMidYMid meet\"\n\tviewBox=\"0 0 24 24\"\n>\n\t<path\n\t\tfill=\"currentColor\"\n\t\td=\"M5 3h2v2H5v5a2 2 0 0 1-2 2a2 2 0 0 1 2 2v5h2v2H5c-1.07-.27-2-.9-2-2v-4a2 2 0 0 0-2-2H0v-2h1a2 2 0 0 0 2-2V5a2 2 0 0 1 2-2m14 0a2 2 0 0 1 2 2v4a2 2 0 0 0 2 2h1v2h-1a2 2 0 0 0-2 2v4a2 2 0 0 1-2 2h-2v-2h2v-5a2 2 0 0 1 2-2a2 2 0 0 1-2-2V5h-2V3h2m-7 12a1 1 0 0 1 1 1a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1m-4 0a1 1 0 0 1 1 1a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1m8 0a1 1 0 0 1 1 1a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1Z\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\texport let value: any;\n\texport let depth: number;\n\texport let collapsed = depth > 4;\n</script>\n\n<span class=\"spacer\" class:mt-10={depth === 0} />\n<div class=\"json-node\">\n\t{#if value instanceof Array}\n\t\t{#if collapsed}\n\t\t\t<button\n\t\t\t\ton:click={() => {\n\t\t\t\t\tcollapsed = false;\n\t\t\t\t}}\n\t\t\t>\n\t\t\t\t<span class=\"expand-array\">expand {value.length} children</span>\n\t\t\t</button>\n\t\t{:else}\n\t\t\t[\n\t\t\t<div class=\"children\">\n\t\t\t\t{#each value as node, i}\n\t\t\t\t\t<div>\n\t\t\t\t\t\t{i}: <svelte:self value={node} depth={depth + 1} />\n\t\t\t\t\t\t{#if i !== value.length - 1}\n\t\t\t\t\t\t\t,\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</div>\n\t\t\t\t{/each}\n\t\t\t</div>\n\t\t\t]\n\t\t{/if}\n\t{:else if value instanceof Object}\n\t\t{#if collapsed}\n\t\t\t<button\n\t\t\t\ton:click={() => {\n\t\t\t\t\tcollapsed = false;\n\t\t\t\t}}\n\t\t\t>\n\t\t\t\t&#123;+{Object.keys(value).length} items&#125;\n\t\t\t</button>\n\t\t{:else}\n\t\t\t&#123;\n\t\t\t<div class=\"children\">\n\t\t\t\t{#each Object.entries(value) as node, i}\n\t\t\t\t\t<div>\n\t\t\t\t\t\t{node[0]}: <svelte:self\n\t\t\t\t\t\t\tvalue={node[1]}\n\t\t\t\t\t\t\tdepth={depth + 1}\n\t\t\t\t\t\t\tkey={i}\n\t\t\t\t\t\t/><!--\n\t\t-->{#if i !== Object.keys(value).length - 1}<!--\n\t\t-->,\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</div>\n\t\t\t\t{/each}\n\t\t\t</div>\n\t\t\t&#125;\n\t\t{/if}\n\t{:else if value === null}\n\t\t<div class=\"json-item null\">null</div>\n\t{:else if typeof value === \"string\"}\n\t\t<div class=\"json-item string\">\n\t\t\t\"{value}\"\n\t\t</div>\n\t{:else if typeof value === \"boolean\"}\n\t\t<div class=\"json-item bool\">\n\t\t\t{value.toLocaleString()}\n\t\t</div>\n\t{:else if typeof value === \"number\"}\n\t\t<div class=\"json-item number\">\n\t\t\t{value}\n\t\t</div>\n\t{:else}\n\t\t<div class=\"json-item\">\n\t\t\t{value}\n\t\t</div>\n\t{/if}\n</div>\n\n<style>\n\t.spacer {\n\t\tdisplay: inline-block;\n\t\twidth: 0;\n\t\theight: 0;\n\t}\n\n\t.json-node {\n\t\tdisplay: inline;\n\t\tcolor: var(--body-text-color);\n\t\tline-height: var(--line-sm);\n\t\tfont-family: var(--font-mono);\n\t}\n\n\t.expand-array {\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tbackground: var(--background-fill-secondary);\n\t\tpadding: 0 var(--size-1);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.expand-array:hover {\n\t\tbackground: var(--background-fill-primary);\n\t}\n\n\t.children {\n\t\tpadding-left: var(--size-4);\n\t}\n\n\t.json-item {\n\t\tdisplay: inline;\n\t}\n\n\t.null {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.string {\n\t\tcolor: var(--color-green-500);\n\t}\n\t.number {\n\t\tcolor: var(--color-blue-500);\n\t}\n\t.bool {\n\t\tcolor: var(--color-red-500);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onD<PERSON>roy } from \"svelte\";\n\timport { fade } from \"svelte/transition\";\n\timport { JSON as JSONI<PERSON> } from \"@gradio/icons\";\n\timport { Empty } from \"@gradio/atoms\";\n\timport JSONNode from \"./JSONNode.svelte\";\n\timport { Copy, Check } from \"@gradio/icons\";\n\n\texport let value: any = {};\n\n\tlet copied = false;\n\tlet timer: NodeJS.Timeout;\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 1000);\n\t}\n\n\tasync function handle_copy(): Promise<void> {\n\t\tif (\"clipboard\" in navigator) {\n\t\t\tawait navigator.clipboard.writeText(JSON.stringify(value, null, 2));\n\t\t\tcopy_feedback();\n\t\t}\n\t}\n\n\tfunction is_empty(obj: object): boolean {\n\t\treturn (\n\t\t\tobj &&\n\t\t\tObject.keys(obj).length === 0 &&\n\t\t\tObject.getPrototypeOf(obj) === Object.prototype &&\n\t\t\tJSON.stringify(obj) === JSON.stringify({})\n\t\t);\n\t}\n\n\tonDestroy(() => {\n\t\tif (timer) clearTimeout(timer);\n\t});\n</script>\n\n{#if value && value !== '\"\"' && !is_empty(value)}\n\t<button\n\t\ton:click={handle_copy}\n\t\ttitle=\"copy\"\n\t\tclass={copied ? \"\" : \"copy-text\"}\n\t\taria-roledescription={copied ? \"Copied value\" : \"Copy value\"}\n\t\taria-label={copied ? \"Copied\" : \"Copy\"}\n\t>\n\t\t{#if copied}\n\t\t\t<span in:fade={{ duration: 300 }}>\n\t\t\t\t<Check />\n\t\t\t</span>\n\t\t{:else}\n\t\t\t<Copy />\n\t\t{/if}\n\t</button>\n\t<div class=\"json-holder\">\n\t\t<JSONNode {value} depth={0} />\n\t</div>\n{:else}\n\t<div class=\"empty-wrapper\">\n\t\t<Empty>\n\t\t\t<JSONIcon />\n\t\t</Empty>\n\t</div>\n{/if}\n\n<style>\n\t.json-holder {\n\t\tpadding: var(--size-2);\n\t}\n\n\t.empty-wrapper {\n\t\tmin-height: calc(var(--size-32) - 20px);\n\t}\n\tbutton {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: var(--block-label-margin);\n\t\tright: var(--block-label-margin);\n\t\talign-items: center;\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-top: none;\n\t\tborder-right: none;\n\t\tborder-radius: var(--block-label-right-radius);\n\t\tbackground: var(--block-label-background-fill);\n\t\tpadding: 5px;\n\t\twidth: 22px;\n\t\theight: 22px;\n\t\toverflow: hidden;\n\t\tcolor: var(--block-label-text-color);\n\t\tfont: var(--font);\n\t\tfont-size: var(--button-small-text-size);\n\t}\n</style>\n", "<script lang=\"ts\" context=\"module\">\n\texport { default as BaseJSON } from \"./shared/JSON.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport JSO<PERSON> from \"./shared/JSON.svelte\";\n\timport { Block, BlockLabel } from \"@gradio/atoms\";\n\timport { JSON as JSONIcon } from \"@gradio/icons\";\n\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: any;\n\tlet old_value: any;\n\texport let loading_status: LoadingStatus;\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t}>;\n\n\t$: {\n\t\tif (value !== old_value) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t}\n\t}\n</script>\n\n<Block\n\t{visible}\n\ttest_id=\"json\"\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n\tpadding={false}\n>\n\t{#if label}\n\t\t<BlockLabel\n\t\t\tIcon={JSONIcon}\n\t\t\t{show_label}\n\t\t\t{label}\n\t\t\tfloat={false}\n\t\t\tdisable={container === false}\n\t\t/>\n\t{/if}\n\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t/>\n\n\t<JSON {value} />\n</Block>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "ctx", "div", "t_value", "dirty", "set_data", "t", "i", "each_blocks", "t1_value", "button", "t1", "t0_value", "current", "t0", "jsonnode_changes", "span", "if_block", "create_if_block_2", "toggle_class", "value", "$$props", "depth", "collapsed", "$$invalidate", "onDestroy", "span_intro", "create_in_transition", "fade", "show_if", "is_empty", "obj", "copied", "timer", "copy_feedback", "handle_copy", "JSONIcon", "blocklabel_changes", "create_if_block", "elem_id", "elem_classes", "visible", "old_value", "loading_status", "label", "show_label", "container", "scale", "min_width", "gradio"], "mappings": "0xCAAAA,GAeKC,EAAAC,EAAAC,CAAA,EAJJC,GAGCF,EAAAG,CAAA,2nBC4DEC,EAAK,CAAA,CAAA,iDADPN,EAEKC,EAAAM,EAAAJ,CAAA,0BADHG,EAAK,CAAA,CAAA,2EAJLA,EAAK,CAAA,CAAA,wDADPN,EAEKC,EAAAM,EAAAJ,CAAA,0BADHG,EAAK,CAAA,CAAA,+CAJLE,EAAAF,KAAM,eAAc,EAAA,sFADtBN,EAEKC,EAAAM,EAAAJ,CAAA,iBADHM,EAAA,GAAAD,KAAAA,EAAAF,KAAM,eAAc,EAAA,KAAAI,EAAAC,EAAAH,CAAA,+EALO,GAC3B,MAACF,EAAK,CAAA,CAAA,MAAC,GACT,wDAFAN,EAEKC,EAAAM,EAAAJ,CAAA,wCADFG,EAAK,CAAA,CAAA,8IAHRN,EAAqCC,EAAAM,EAAAJ,CAAA,qGA3BhCG,EAAS,CAAA,EAAA,8WAvBTA,EAAS,CAAA,EAAA,sUAkCL,OAAO,QAAQA,EAAK,CAAA,CAAA,CAAA,uBAAzB,OAAIM,GAAA,0EAHF;AAAA,IAEL,sDAaK;AAAA,KAEN,yDAfCZ,EAaKC,EAAAM,EAAAJ,CAAA,qFAZG,OAAO,QAAQG,EAAK,CAAA,CAAA,CAAA,oBAAzB,OAAIM,GAAA,EAAA,0GAAJ,OAAIA,EAAAC,EAAA,OAAAD,GAAA,yCAAJ,OAAIA,GAAA,8IALEE,EAAA,OAAO,KAAKR,MAAO,OAAM,wCADlC,IACQ,aAA2B,SACnC,UANAN,EAMQC,EAAAc,EAAAZ,CAAA,8DADCM,EAAA,GAAAK,KAAAA,EAAA,OAAO,KAAKR,MAAO,OAAM,KAAAI,EAAAM,EAAAF,CAAA,uEAahC,GACC,wDAPCG,EAAAX,KAAK,CAAC,EAAA,WAKHA,EAAC,CAAA,IAAK,OAAO,KAAKA,EAAK,CAAA,CAAA,EAAE,OAAS,sBAJ9B,MAAAA,KAAK,CAAC,EACN,MAAAA,KAAQ,MACVA,EAAC,CAAA,oDAHE,IAAE,2CADZN,EASKC,EAAAM,EAAAJ,CAAA,iEARH,CAAAe,GAAAT,EAAA,IAAAQ,KAAAA,EAAAX,KAAK,CAAC,EAAA,KAAAI,EAAAS,EAAAF,CAAA,aACCR,EAAA,IAAAW,EAAA,MAAAd,KAAK,CAAC,GACNG,EAAA,IAAAW,EAAA,MAAAd,KAAQ,qBAGZA,EAAC,CAAA,IAAK,OAAO,KAAKA,EAAK,CAAA,CAAA,EAAE,OAAS,kLA9BjCA,EAAK,CAAA,CAAA,uBAAV,OAAIM,GAAA,2EAHF;AAAA,IAEL,sDASK;AAAA,KAEN,yDAXCZ,EASKC,EAAAM,EAAAJ,CAAA,qFARGG,EAAK,CAAA,CAAA,oBAAV,OAAIM,GAAA,EAAA,2GAAJ,OAAIA,EAAAC,EAAA,OAAAD,GAAA,yCAAJ,OAAIA,GAAA,gJAL6BE,EAAAR,KAAM,OAAM,oDAApB,SAAO,aAAc,WAAS,oDAL1DN,EAMQC,EAAAc,EAAAZ,CAAA,EADPC,EAA+DW,EAAAM,CAAA,8DAA5BZ,EAAA,GAAAK,KAAAA,EAAAR,KAAM,OAAM,KAAAI,EAAAM,EAAAF,CAAA,uEAQlB,GAE3B,0FAHyBR,EAAI,CAAA,EAAS,MAAAA,KAAQ,KACzC,IAAAgB,EAAAhB,EAAM,CAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,GAACiB,GAAA,4BAD1BjB,EAAC,CAAA,CAAA,MAAC,IAAE,iDADNN,EAKKC,EAAAM,EAAAJ,CAAA,gGAJqBG,EAAI,CAAA,GAASG,EAAA,IAAAW,EAAA,MAAAd,KAAQ,aACzCA,EAAM,CAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,mOAf1B,OAAAA,eAAiB,MAAK,EAuBjBA,eAAiB,OAAM,EA2BvBA,OAAU,KAAI,EAEP,OAAAA,MAAU,SAAQ,EAIlB,OAAAA,MAAU,UAAS,EAInB,OAAAA,MAAU,SAAQ,6GA9DFkB,EAAAH,EAAA,QAAAf,OAAU,CAAC,iDAA7CN,EAAgDC,EAAAoB,EAAAlB,CAAA,WAChDH,EAsEKC,EAAAM,EAAAJ,CAAA,2CAvE6BqB,EAAAH,EAAA,QAAAf,OAAU,CAAC,sOALjC,MAAAmB,CAAU,EAAAC,GACV,MAAAC,CAAa,EAAAD,GACb,UAAAE,EAAYD,EAAQ,CAAC,EAAAD,eAS5BG,EAAA,EAAAD,EAAY,EAAK,UAuBjBC,EAAA,EAAAD,EAAY,EAAK,2iBClCZ,CAAA,UAAAE,EAAA,SAAyB,0MA6DlC9B,EAIKC,EAAAM,EAAAJ,CAAA,+PAhBCG,EAAM,CAAA,EAAA,kEASc,CAAC,qGAbnBA,EAAM,CAAA,EAAG,GAAK,WAAW,EAAA,gBAAA,+BACVA,EAAM,CAAA,EAAG,eAAiB,YAAY,qBAChDA,EAAM,CAAA,EAAG,SAAW,MAAM,mDALvCN,EAcQC,EAAAc,EAAAZ,CAAA,0BACRH,EAEKC,EAAAM,EAAAJ,CAAA,sCAhBMG,EAAW,CAAA,CAAA,sJAEdA,EAAM,CAAA,EAAG,GAAK,WAAW,EAAA,oDACVA,EAAM,CAAA,EAAG,eAAiB,+DACpCA,EAAM,CAAA,EAAG,SAAW,4mBAG/BN,EAEMC,EAAAoB,EAAAlB,CAAA,+DAFW4B,EAAAC,GAAAX,EAAAY,GAAA,CAAA,SAAU,GAAG,CAAA,+JAT5BC,GAAA,OAAAA,EAAA,CAAA,EAAA5B,MAASA,EAAK,CAAA,IAAK,MAAS,CAAA6B,GAAS7B,EAAK,CAAA,CAAA,sTAdrC,SAAA6B,GAASC,EAAW,CAE3B,OAAAA,GACA,OAAO,KAAKA,CAAG,EAAE,SAAW,GAC5B,OAAO,eAAeA,CAAG,IAAM,OAAO,WACtC,KAAK,UAAUA,CAAG,IAAM,KAAK,UAAS,CAAA,CAAA,yBAzB7B,MAAAX,EAAK,EAAA,EAAAC,EAEZW,EAAS,GACTC,WAEKC,GAAa,CACrBV,EAAA,EAAAQ,EAAS,EAAI,EACTC,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,gBACPT,EAAA,EAAAQ,EAAS,EAAK,GACZ,oBAGWG,GAAW,CACrB,cAAe,YACZ,MAAA,UAAU,UAAU,UAAU,KAAK,UAAUf,EAAO,KAAM,CAAC,CAAA,EACjEc,KAaF,OAAAT,GAAS,IAAA,CACJQ,GAAO,aAAaA,CAAK,2dCUtBG,oCAGC,GACE,QAAAnC,OAAc,oHAAdG,EAAA,MAAAiC,EAAA,QAAApC,OAAc,6HANpBA,EAAK,CAAA,GAAAqC,GAAArC,CAAA,YAWG,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,oPAbdA,EAAK,CAAA,0IAWG,WAAAA,MAAO,YACbG,EAAA,MAAA,CAAA,KAAAH,MAAO,IAAI,WACbA,EAAc,CAAA,CAAA,uXAfV,gZA/BE,GAAA,CAAA,QAAAsC,EAAU,EAAE,EAAAlB,GACZ,aAAAmB,EAAY,EAAA,EAAAnB,EACZ,CAAA,QAAAoB,EAAU,EAAI,EAAApB,GACd,MAAAD,CAAU,EAAAC,EACjBqB,GACO,eAAAC,CAA6B,EAAAtB,GAC7B,MAAAuB,CAAa,EAAAvB,GACb,WAAAwB,CAAmB,EAAAxB,EACnB,CAAA,UAAAyB,EAAY,EAAI,EAAAzB,EAChB,CAAA,MAAA0B,EAAuB,IAAI,EAAA1B,EAC3B,CAAA,UAAA2B,EAAgC,MAAS,EAAA3B,GACzC,OAAA4B,CAET,EAAA5B,ibAGGD,IAAUsB,IACblB,EAAA,GAAAkB,EAAYtB,CAAK,EACjB6B,EAAO,SAAS,QAAQ"}