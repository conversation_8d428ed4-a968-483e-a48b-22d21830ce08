import{B as P}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{B as Q}from"./BlockTitle-7f7c9ef8.js";import{S as R}from"./Index-26cfc80a.js";import"./index-a80d931b.js";import"./svelte/svelte.js";import"./Info-84f5385d.js";const{SvelteComponent:V,append:U,assign:W,attr:o,create_component:B,destroy_component:j,detach:S,element:z,get_spread_object:X,get_spread_update:Y,init:Z,insert:I,listen:k,mount_component:N,run_all:y,safe_not_equal:p,set_data:x,set_input_value:A,space:F,text:$,to_number:H,toggle_class:G,transition_in:q,transition_out:C}=window.__gradio__svelte__internal,{afterUpdate:ee,tick:le}=window.__gradio__svelte__internal;function ne(i){let e;return{c(){e=$(i[2])},m(n,a){I(n,e,a)},p(n,a){a&4&&x(e,n[2])},d(n){n&&S(e)}}}function te(i){let e,n,a,s,_,f,m,r,g;const b=[{autoscroll:i[1].autoscroll},{i18n:i[1].i18n},i[13]];let d={};for(let t=0;t<b.length;t+=1)d=W(d,b[t]);return e=new R({props:d}),s=new Q({props:{show_label:i[10],info:i[3],$$slots:{default:[ne]},$$scope:{ctx:i}}}),{c(){B(e.$$.fragment),n=F(),a=z("label"),B(s.$$.fragment),_=F(),f=z("input"),o(f,"aria-label",i[2]),o(f,"type","number"),o(f,"min",i[11]),o(f,"max",i[12]),o(f,"step",i[14]),f.disabled=i[15],o(f,"class","svelte-pjtc3"),o(a,"class","block svelte-pjtc3"),G(a,"container",i[7])},m(t,u){N(e,t,u),I(t,n,u),I(t,a,u),N(s,a,null),U(a,_),U(a,f),A(f,i[0]),m=!0,r||(g=[k(f,"input",i[19]),k(f,"keypress",i[16]),k(f,"blur",i[20]),k(f,"focus",i[21])],r=!0)},p(t,u){const h=u&8194?Y(b,[u&2&&{autoscroll:t[1].autoscroll},u&2&&{i18n:t[1].i18n},u&8192&&X(t[13])]):{};e.$set(h);const c={};u&1024&&(c.show_label=t[10]),u&8&&(c.info=t[3]),u&8388612&&(c.$$scope={dirty:u,ctx:t}),s.$set(c),(!m||u&4)&&o(f,"aria-label",t[2]),(!m||u&2048)&&o(f,"min",t[11]),(!m||u&4096)&&o(f,"max",t[12]),(!m||u&16384)&&o(f,"step",t[14]),(!m||u&32768)&&(f.disabled=t[15]),u&1&&H(f.value)!==t[0]&&A(f,t[0]),(!m||u&128)&&G(a,"container",t[7])},i(t){m||(q(e.$$.fragment,t),q(s.$$.fragment,t),m=!0)},o(t){C(e.$$.fragment,t),C(s.$$.fragment,t),m=!1},d(t){t&&(S(n),S(a)),j(e,t),j(s),r=!1,y(g)}}}function ie(i){let e,n;return e=new P({props:{visible:i[6],elem_id:i[4],elem_classes:i[5],padding:i[7],allow_overflow:!1,scale:i[8],min_width:i[9],$$slots:{default:[te]},$$scope:{ctx:i}}}),{c(){B(e.$$.fragment)},m(a,s){N(e,a,s),n=!0},p(a,[s]){const _={};s&64&&(_.visible=a[6]),s&16&&(_.elem_id=a[4]),s&32&&(_.elem_classes=a[5]),s&128&&(_.padding=a[7]),s&256&&(_.scale=a[8]),s&512&&(_.min_width=a[9]),s&8453263&&(_.$$scope={dirty:s,ctx:a}),e.$set(_)},i(a){n||(q(e.$$.fragment,a),n=!0)},o(a){C(e.$$.fragment,a),n=!1},d(a){j(e,a)}}}function ae(i,e,n){let a,{gradio:s}=e,{label:_=s.i18n("number.number")}=e,{info:f=void 0}=e,{elem_id:m=""}=e,{elem_classes:r=[]}=e,{visible:g=!0}=e,{container:b=!0}=e,{scale:d=null}=e,{min_width:t=void 0}=e,{value:u=0}=e,{show_label:h}=e,{minimum:c=void 0}=e,{maximum:D=void 0}=e,{loading_status:E}=e,{value_is_output:w=!1}=e,{step:T=null}=e,{interactive:v}=e;function J(){!isNaN(u)&&u!==null&&(s.dispatch("change"),w||s.dispatch("input"))}ee(()=>{n(17,w=!1)});async function K(l){await le(),l.key==="Enter"&&(l.preventDefault(),s.dispatch("submit"))}function L(){u=H(this.value),n(0,u)}const M=()=>s.dispatch("blur"),O=()=>s.dispatch("focus");return i.$$set=l=>{"gradio"in l&&n(1,s=l.gradio),"label"in l&&n(2,_=l.label),"info"in l&&n(3,f=l.info),"elem_id"in l&&n(4,m=l.elem_id),"elem_classes"in l&&n(5,r=l.elem_classes),"visible"in l&&n(6,g=l.visible),"container"in l&&n(7,b=l.container),"scale"in l&&n(8,d=l.scale),"min_width"in l&&n(9,t=l.min_width),"value"in l&&n(0,u=l.value),"show_label"in l&&n(10,h=l.show_label),"minimum"in l&&n(11,c=l.minimum),"maximum"in l&&n(12,D=l.maximum),"loading_status"in l&&n(13,E=l.loading_status),"value_is_output"in l&&n(17,w=l.value_is_output),"step"in l&&n(14,T=l.step),"interactive"in l&&n(18,v=l.interactive)},i.$$.update=()=>{i.$$.dirty&1&&J(),i.$$.dirty&262144&&n(15,a=!v)},[u,s,_,f,m,r,g,b,d,t,h,c,D,E,T,a,K,w,v,L,M,O]}class ce extends V{constructor(e){super(),Z(this,e,ae,ie,p,{gradio:1,label:2,info:3,elem_id:4,elem_classes:5,visible:6,container:7,scale:8,min_width:9,value:0,show_label:10,minimum:11,maximum:12,loading_status:13,value_is_output:17,step:14,interactive:18})}}export{ce as default};
//# sourceMappingURL=Index-3c20b113.js.map
