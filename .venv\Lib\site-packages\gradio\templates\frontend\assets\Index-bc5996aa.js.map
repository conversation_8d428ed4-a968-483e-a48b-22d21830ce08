{"version": 3, "file": "Index-bc5996aa.js", "sources": ["../../../../js/image/Index.svelte"], "sourcesContent": ["<svelte:options accessors={true} />\n\n<script context=\"module\" lang=\"ts\">\n\texport { default as Webcam } from \"./shared/Webcam.svelte\";\n\texport { default as BaseImageUploader } from \"./shared/ImageUploader.svelte\";\n\texport { default as BaseStaticImage } from \"./shared/ImagePreview.svelte\";\n\texport { default as BaseExample } from \"./Example.svelte\";\n\texport { default as BaseImage } from \"./shared/Image.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport StaticImage from \"./shared/ImagePreview.svelte\";\n\timport ImageUploader from \"./shared/ImageUploader.svelte\";\n\n\timport { Block, Empty, UploadText } from \"@gradio/atoms\";\n\timport { Image } from \"@gradio/icons\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { FileData } from \"@gradio/client\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\ttype sources = \"upload\" | \"webcam\" | \"clipboard\" | null;\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: null | FileData = null;\n\tlet old_value: null | FileData = null;\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let show_download_button: boolean;\n\texport let root: string;\n\n\texport let height: number | undefined;\n\texport let width: number | undefined;\n\n\texport let _selectable = false;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let show_share_button = false;\n\texport let sources: (\"clipboard\" | \"webcam\" | \"upload\")[] = [\n\t\t\"upload\",\n\t\t\"clipboard\",\n\t\t\"webcam\"\n\t];\n\texport let interactive: boolean;\n\texport let streaming: boolean;\n\texport let pending: boolean;\n\texport let mirror_webcam: boolean;\n\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\terror: string;\n\t\tedit: never;\n\t\tstream: never;\n\t\tdrag: never;\n\t\tupload: never;\n\t\tclear: never;\n\t\tselect: SelectData;\n\t\tshare: ShareData;\n\t}>;\n\n\t$: {\n\t\tif (JSON.stringify(value) !== JSON.stringify(old_value)) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t}\n\t}\n\n\tlet dragging: boolean;\n\tlet active_source: sources = null;\n</script>\n\n{#if !interactive}\n\t<Block\n\t\t{visible}\n\t\tvariant={\"solid\"}\n\t\tborder_mode={dragging ? \"focus\" : \"base\"}\n\t\tpadding={false}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\theight={height || undefined}\n\t\t{width}\n\t\tallow_overflow={false}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t/>\n\t\t<StaticImage\n\t\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t\t\ton:share={({ detail }) => gradio.dispatch(\"share\", detail)}\n\t\t\ton:error={({ detail }) => gradio.dispatch(\"error\", detail)}\n\t\t\t{value}\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{show_download_button}\n\t\t\tselectable={_selectable}\n\t\t\t{show_share_button}\n\t\t\ti18n={gradio.i18n}\n\t\t/>\n\t</Block>\n{:else}\n\t<Block\n\t\t{visible}\n\t\tvariant={value === null ? \"dashed\" : \"solid\"}\n\t\tborder_mode={dragging ? \"focus\" : \"base\"}\n\t\tpadding={false}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\theight={height || undefined}\n\t\t{width}\n\t\tallow_overflow={false}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t/>\n\n\t\t<ImageUploader\n\t\t\tbind:active_source\n\t\t\tbind:value\n\t\t\tselectable={_selectable}\n\t\t\t{root}\n\t\t\t{sources}\n\t\t\ton:edit={() => gradio.dispatch(\"edit\")}\n\t\t\ton:clear={() => {\n\t\t\t\tgradio.dispatch(\"clear\");\n\t\t\t}}\n\t\t\ton:stream={() => gradio.dispatch(\"stream\")}\n\t\t\ton:drag={({ detail }) => (dragging = detail)}\n\t\t\ton:upload={() => gradio.dispatch(\"upload\")}\n\t\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t\t\ton:share={({ detail }) => gradio.dispatch(\"share\", detail)}\n\t\t\ton:error={({ detail }) => {\n\t\t\t\tloading_status = loading_status || {};\n\t\t\t\tloading_status.status = \"error\";\n\t\t\t\tgradio.dispatch(\"error\", detail);\n\t\t\t}}\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{pending}\n\t\t\t{streaming}\n\t\t\t{mirror_webcam}\n\t\t\ti18n={gradio.i18n}\n\t\t>\n\t\t\t{#if active_source === \"upload\" || !active_source}\n\t\t\t\t<UploadText i18n={gradio.i18n} type=\"image\" />\n\t\t\t{:else if active_source === \"clipboard\"}\n\t\t\t\t<UploadText i18n={gradio.i18n} type=\"clipboard\" mode=\"short\" />\n\t\t\t{:else}\n\t\t\t\t<Empty unpadded_box={true} size=\"large\"><Image /></Empty>\n\t\t\t{/if}\n\t\t</ImageUploader>\n\t</Block>\n{/if}\n"], "names": ["ctx", "dirty", "block_changes", "uploadtext_changes", "imageuploader_changes", "staticimage_changes", "elem_id", "$$props", "elem_classes", "visible", "value", "old_value", "label", "show_label", "show_download_button", "root", "height", "width", "_selectable", "container", "scale", "min_width", "loading_status", "show_share_button", "sources", "interactive", "streaming", "pending", "mirror_webcam", "gradio", "dragging", "active_source", "select_handler", "detail", "share_handler", "error_handler", "$$invalidate", "select_handler_1", "share_handler_1"], "mappings": "k0CA+GW,QAAAA,EAAU,CAAA,IAAA,KAAO,SAAW,oBACxBA,EAAQ,EAAA,EAAG,QAAU,eACzB,kCAGD,OAAAA,MAAU,kCAEF,kLAPPC,EAAA,CAAA,EAAA,IAAAC,EAAA,QAAAF,EAAU,CAAA,IAAA,KAAO,SAAW,sCACxBA,EAAQ,EAAA,EAAG,QAAU,+DAI1BC,EAAA,CAAA,EAAA,MAAAC,EAAA,OAAAF,MAAU,8UAtCT,oBACIA,EAAQ,EAAA,EAAG,QAAU,eACzB,kCAGD,OAAAA,MAAU,kCAEF,+MANHA,EAAQ,EAAA,EAAG,QAAU,+DAI1BC,EAAA,CAAA,EAAA,MAAAC,EAAA,OAAAF,MAAU,sUA8EK,uSAFH,KAAAA,MAAO,qGAAPC,EAAA,CAAA,EAAA,UAAAE,EAAA,KAAAH,MAAO,8IAFP,KAAAA,MAAO,oFAAPC,EAAA,CAAA,EAAA,UAAAE,EAAA,KAAAH,MAAO,iVADrBA,EAAa,EAAA,IAAK,UAAQ,CAAKA,EAAa,EAAA,EAAA,EAEvCA,QAAkB,YAAW,+UAlC3B,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,uIAMNA,EAAW,EAAA,uGAsBjB,KAAAA,MAAO,2fA9BD,WAAAA,MAAO,YACbC,EAAA,CAAA,EAAA,SAAA,CAAA,KAAAD,MAAO,IAAI,YACbA,EAAc,CAAA,CAAA,qDAMNA,EAAW,EAAA,oNAsBjBC,EAAA,CAAA,EAAA,UAAAG,EAAA,KAAAJ,MAAO,qVA/DD,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,0KAUNA,EAAW,EAAA,0BAEjB,KAAAA,MAAO,8MAdD,WAAAA,MAAO,YACbC,EAAA,CAAA,EAAA,SAAA,CAAA,KAAAD,MAAO,IAAI,YACbA,EAAc,CAAA,CAAA,yKAUNA,EAAW,EAAA,2CAEjBC,EAAA,CAAA,EAAA,UAAAI,EAAA,KAAAL,MAAO,4NA9BVA,EAAW,EAAA,IAAA,uTApDL,GAAA,CAAA,QAAAM,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,EACZ,CAAA,QAAAE,EAAU,EAAI,EAAAF,EACd,CAAA,MAAAG,EAAyB,IAAI,EAAAH,EACpCI,EAA6B,MACtB,MAAAC,CAAa,EAAAL,GACb,WAAAM,CAAmB,EAAAN,GACnB,qBAAAO,CAA6B,EAAAP,GAC7B,KAAAQ,CAAY,EAAAR,GAEZ,OAAAS,CAA0B,EAAAT,GAC1B,MAAAU,CAAyB,EAAAV,EAEzB,CAAA,YAAAW,EAAc,EAAK,EAAAX,EACnB,CAAA,UAAAY,EAAY,EAAI,EAAAZ,EAChB,CAAA,MAAAa,EAAuB,IAAI,EAAAb,EAC3B,CAAA,UAAAc,EAAgC,MAAS,EAAAd,GACzC,eAAAe,CAA6B,EAAAf,EAC7B,CAAA,kBAAAgB,EAAoB,EAAK,EAAAhB,EACzB,CAAA,QAAAiB,EACV,CAAA,SACA,YACA,QAAA,CAAA,EAAAjB,GAEU,YAAAkB,CAAoB,EAAAlB,GACpB,UAAAmB,CAAkB,EAAAnB,GAClB,QAAAoB,CAAgB,EAAApB,GAChB,cAAAqB,CAAsB,EAAArB,GAEtB,OAAAsB,CAUT,EAAAtB,EASEuB,EACAC,EAAyB,KAwBb,MAAAC,EAAA,CAAA,CAAA,OAAAC,KAAaJ,EAAO,SAAS,SAAUI,CAAM,EAC9CC,EAAA,CAAA,CAAA,OAAAD,KAAaJ,EAAO,SAAS,QAASI,CAAM,EAC5CE,EAAA,CAAA,CAAA,OAAAF,KAAaJ,EAAO,SAAS,QAASI,CAAM,mEAqC1CJ,EAAO,SAAS,MAAM,UAEpCA,EAAO,SAAS,OAAO,UAEPA,EAAO,SAAS,QAAQ,OAC7B,OAAAI,CAAM,IAAAG,EAAA,GAAQN,EAAWG,CAAM,SAC1BJ,EAAO,SAAS,QAAQ,EAC3BQ,GAAA,CAAA,CAAA,OAAAJ,KAAaJ,EAAO,SAAS,SAAUI,CAAM,EAC9CK,GAAA,CAAA,CAAA,OAAAL,KAAaJ,EAAO,SAAS,QAASI,CAAM,OAC5C,OAAAA,KAAM,CAClBG,EAAA,EAAAd,EAAiBA,GAAc,CAAA,CAAA,MAC/BA,EAAe,OAAS,QAAOA,CAAA,EAC/BO,EAAO,SAAS,QAASI,CAAM,s2BAlF7B,KAAK,UAAUvB,CAAK,IAAM,KAAK,UAAUC,CAAS,IACrDyB,EAAA,GAAAzB,EAAYD,CAAK,EACjBmB,EAAO,SAAS,QAAQ"}