{"name": "@gradio/wasm", "version": "0.10.0", "description": "Gradio Wasm package", "type": "module", "main": "./dist/index.js", "exports": {".": "./dist/index.js", "./package.json": "./package.json", "./svelte": "./svelte/index.ts", "./network": "./network/index.ts"}, "keywords": [], "author": "", "license": "ISC", "main_changeset": true, "scripts": {"dev:client": "tsc -w --incremental", "dev:worker": "vite build --config vite.worker.config.js --watch --emptyOutDir=false", "dev": "run-p dev:*", "build:client": "tsc", "build:worker": "vite build --config vite.worker.config.js", "build": "run-s build:worker build:client"}, "devDependencies": {"pyodide": "^0.25.0"}, "dependencies": {"@types/path-browserify": "^1.0.0", "path-browserify": "^1.0.1"}}