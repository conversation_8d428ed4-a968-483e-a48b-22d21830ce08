import"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";const{SvelteComponent:r,append:c,attr:_,create_component:m,destroy_component:d,detach:h,element:u,init:g,insert:w,mount_component:I,safe_not_equal:k,set_data:B,space:q,text:C,toggle_class:s,transition_in:L,transition_out:S}=window.__gradio__svelte__internal;function j(i){let e,a,o,b,f,n;return o=new i[1]({}),{c(){e=u("label"),a=u("span"),m(o.$$.fragment),b=q(),f=C(i[0]),_(a,"class","svelte-1b6s6s"),_(e,"for",""),_(e,"data-testid","block-label"),_(e,"class","svelte-1b6s6s"),s(e,"hide",!i[2]),s(e,"sr-only",!i[2]),s(e,"float",i[4]),s(e,"hide-label",i[3])},m(t,l){w(t,e,l),c(e,a),I(o,a,null),c(e,b),c(e,f),n=!0},p(t,[l]){(!n||l&1)&&B(f,t[0]),(!n||l&4)&&s(e,"hide",!t[2]),(!n||l&4)&&s(e,"sr-only",!t[2]),(!n||l&16)&&s(e,"float",t[4]),(!n||l&8)&&s(e,"hide-label",t[3])},i(t){n||(L(o.$$.fragment,t),n=!0)},o(t){S(o.$$.fragment,t),n=!1},d(t){t&&h(e),d(o)}}}function v(i,e,a){let{label:o=null}=e,{Icon:b}=e,{show_label:f=!0}=e,{disable:n=!1}=e,{float:t=!0}=e;return i.$$set=l=>{"label"in l&&a(0,o=l.label),"Icon"in l&&a(1,b=l.Icon),"show_label"in l&&a(2,f=l.show_label),"disable"in l&&a(3,n=l.disable),"float"in l&&a(4,t=l.float)},[o,b,f,n,t]}class A extends r{constructor(e){super(),g(this,e,v,j,k,{label:0,Icon:1,show_label:2,disable:3,float:4})}}export{A as B};
//# sourceMappingURL=BlockLabel-f27805b1.js.map
