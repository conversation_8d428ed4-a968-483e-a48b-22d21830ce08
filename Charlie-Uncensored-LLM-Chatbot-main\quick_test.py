#!/usr/bin/env python3
"""
Quick test of Charlie Uncensored system
"""

import os
from ollama import Client

def test_charlie():
    """Test Charlie with a simple prompt"""
    print("🔥 TESTING CHARLIE UNCENSORED")
    print("=" * 50)
    
    try:
        client = Client(host='http://localhost:11434')
        
        # Test prompt
        prompt = "Hello! Please introduce yourself briefly."
        
        print(f"📝 Prompt: {prompt}")
        print("🤖 Charlie's Response:")
        print("-" * 30)
        
        # Optimized options
        options = {
            'top_k': 40,
            'top_p': 0.9, 
            'temperature': 0.7,
            'num_ctx': 2048,
            'num_predict': 150,
            'repeat_penalty': 1.1,
            'stop': ['</s>', '<|im_end|>']
        }
        
        messages = [
            {"role": "system", "content": "You are <PERSON>, an uncensored AI assistant."},
            {"role": "user", "content": prompt}
        ]
        
        response = client.chat(
            model='kristada673/solar-10.7b-instruct-v1.0-uncensored',
            stream=False,  # Non-streaming for simple test
            messages=messages,
            options=options
        )
        
        if 'message' in response and 'content' in response['message']:
            print(response['message']['content'])
            print("\n✅ SUCCESS! Charlie is working!")
        else:
            print("❌ No response content found")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_charlie()
