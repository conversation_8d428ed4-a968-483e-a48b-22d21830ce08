import{r as f}from"./file-url-bef2dc1b.js";const{SvelteComponent:v,assign:i,compute_rest_props:a,detach:p,element:h,exclude_internal_props:y,get_spread_update:I,init:j,insert:q,noop:m,safe_not_equal:b,set_attributes:u,src_url_equal:w,toggle_class:g}=window.__gradio__svelte__internal;function x(_){let e,s,c=[{src:s=_[0]},_[1]],n={};for(let t=0;t<c.length;t+=1)n=i(n,c[t]);return{c(){e=h("img"),u(e,n),g(e,"svelte-1pijsyv",!0)},m(t,r){q(t,e,r)},p(t,[r]){u(e,n=I(c,[r&1&&!w(e.src,s=t[0])&&{src:s},r&2&&t[1]])),g(e,"svelte-1pijsyv",!0)},i:m,o:m,d(t){t&&p(e)}}}function C(_,e,s){const c=["src"];let n=a(e,c),{src:t=void 0}=e,r,o;return _.$$set=l=>{e=i(i({},e),y(l)),s(1,n=a(e,c)),"src"in l&&s(2,t=l.src)},_.$$.update=()=>{if(_.$$.dirty&12){s(0,r=t),s(3,o=t);const l=t;f(l).then(d=>{o===l&&s(0,r=d)})}},[r,n,t,o]}class P extends v{constructor(e){super(),j(this,e,C,x,b,{src:2})}}const k=P;export{k as I};
//# sourceMappingURL=Image-21c02477.js.map
