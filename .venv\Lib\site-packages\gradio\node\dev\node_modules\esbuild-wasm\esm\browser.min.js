var ze=Object.defineProperty;var Ke=(e,t)=>{for(var n in t)ze(e,n,{get:t[n],enumerable:!0})};var ve={};Ke(ve,{analyzeMetafile:()=>at,analyzeMetafileSync:()=>dt,build:()=>it,buildSync:()=>ut,context:()=>lt,default:()=>yt,formatMessages:()=>ot,formatMessagesSync:()=>ct,initialize:()=>gt,stop:()=>pt,transform:()=>st,transformSync:()=>ft,version:()=>rt});function Oe(e){let t=i=>{if(i===null)n.write8(0);else if(typeof i=="boolean")n.write8(1),n.write8(+i);else if(typeof i=="number")n.write8(2),n.write32(i|0);else if(typeof i=="string")n.write8(3),n.write(Z(i));else if(i instanceof Uint8Array)n.write8(4),n.write(i);else if(i instanceof Array){n.write8(5),n.write32(i.length);for(let u of i)t(u)}else{let u=Object.keys(i);n.write8(6),n.write32(u.length);for(let l of u)n.write(Z(l)),t(i[l])}},n=new de;return n.write32(0),n.write32(e.id<<1|+!e.isRequest),t(e.value),Re(n.buf,n.len-4,0),n.buf.subarray(0,n.len)}function Se(e){let t=()=>{switch(n.read8()){case 0:return null;case 1:return!!n.read8();case 2:return n.read32();case 3:return re(n.read());case 4:return n.read();case 5:{let m=n.read32(),s=[];for(let d=0;d<m;d++)s.push(t());return s}case 6:{let m=n.read32(),s={};for(let d=0;d<m;d++)s[re(n.read())]=t();return s}default:throw new Error("Invalid packet")}},n=new de(e),i=n.read32(),u=(i&1)===0;i>>>=1;let l=t();if(n.ptr!==e.length)throw new Error("Invalid packet");return{id:i,isRequest:u,value:l}}var de=class{constructor(t=new Uint8Array(1024)){this.buf=t;this.len=0;this.ptr=0}_write(t){if(this.len+t>this.buf.length){let n=new Uint8Array((this.len+t)*2);n.set(this.buf),this.buf=n}return this.len+=t,this.len-t}write8(t){let n=this._write(1);this.buf[n]=t}write32(t){let n=this._write(4);Re(this.buf,t,n)}write(t){let n=this._write(4+t.length);Re(this.buf,t.length,n),this.buf.set(t,n+4)}_read(t){if(this.ptr+t>this.buf.length)throw new Error("Invalid packet");return this.ptr+=t,this.ptr-t}read8(){return this.buf[this._read(1)]}read32(){return Ee(this.buf,this._read(4))}read(){let t=this.read32(),n=new Uint8Array(t),i=this._read(n.length);return n.set(this.buf.subarray(i,i+t)),n}},Z,re,xe;if(typeof TextEncoder!="undefined"&&typeof TextDecoder!="undefined"){let e=new TextEncoder,t=new TextDecoder;Z=n=>e.encode(n),re=n=>t.decode(n),xe='new TextEncoder().encode("")'}else if(typeof Buffer!="undefined")Z=e=>Buffer.from(e),re=e=>{let{buffer:t,byteOffset:n,byteLength:i}=e;return Buffer.from(t,n,i).toString()},xe='Buffer.from("")';else throw new Error("No UTF-8 codec found");if(!(Z("")instanceof Uint8Array))throw new Error(`Invariant violation: "${xe} instanceof Uint8Array" is incorrectly false

This indicates that your JavaScript environment is broken. You cannot use
esbuild in this environment because esbuild relies on this invariant. This
is not a problem with esbuild. You need to fix your environment instead.
`);function Ee(e,t){return e[t++]|e[t++]<<8|e[t++]<<16|e[t++]<<24}function Re(e,t,n){e[n++]=t,e[n++]=t>>8,e[n++]=t>>16,e[n++]=t>>24}var J=JSON.stringify,ke="warning",$e="silent";function Me(e){if(Y(e,"target"),e.indexOf(",")>=0)throw new Error(`Invalid target: ${e}`);return e}var me=()=>null,I=e=>typeof e=="boolean"?null:"a boolean",y=e=>typeof e=="string"?null:"a string",ye=e=>e instanceof RegExp?null:"a RegExp object",le=e=>typeof e=="number"&&e===(e|0)?null:"an integer",Fe=e=>typeof e=="function"?null:"a function",W=e=>Array.isArray(e)?null:"an array",ee=e=>typeof e=="object"&&e!==null&&!Array.isArray(e)?null:"an object",Ve=e=>typeof e=="object"&&e!==null?null:"an array or an object",Ye=e=>e instanceof WebAssembly.Module?null:"a WebAssembly.Module",Ce=e=>typeof e=="object"&&!Array.isArray(e)?null:"an object or null",Te=e=>typeof e=="string"||typeof e=="boolean"?null:"a string or a boolean",Je=e=>typeof e=="string"||typeof e=="object"&&e!==null&&!Array.isArray(e)?null:"a string or an object",Ge=e=>typeof e=="string"||Array.isArray(e)?null:"a string or an array",Pe=e=>typeof e=="string"||e instanceof Uint8Array?null:"a string or a Uint8Array",Qe=e=>typeof e=="string"||e instanceof URL?null:"a string or a URL";function r(e,t,n,i){let u=e[n];if(t[n+""]=!0,u===void 0)return;let l=i(u);if(l!==null)throw new Error(`${J(n)} must be ${l}`);return u}function K(e,t,n){for(let i in e)if(!(i in t))throw new Error(`Invalid option ${n}: ${J(i)}`)}function De(e){let t=Object.create(null),n=r(e,t,"wasmURL",Qe),i=r(e,t,"wasmModule",Ye),u=r(e,t,"worker",I);return K(e,t,"in initialize() call"),{wasmURL:n,wasmModule:i,worker:u}}function Be(e){let t;if(e!==void 0){t=Object.create(null);for(let n in e){let i=e[n];if(typeof i=="string"||i===!1)t[n]=i;else throw new Error(`Expected ${J(n)} in mangle cache to map to either a string or false`)}}return t}function he(e,t,n,i,u){let l=r(t,n,"color",I),m=r(t,n,"logLevel",y),s=r(t,n,"logLimit",le);l!==void 0?e.push(`--color=${l}`):i&&e.push("--color=true"),e.push(`--log-level=${m||u}`),e.push(`--log-limit=${s||0}`)}function Y(e,t,n){if(typeof e!="string")throw new Error(`Expected value for ${t}${n!==void 0?" "+J(n):""} to be a string, got ${typeof e} instead`);return e}function Ue(e,t,n){let i=r(t,n,"legalComments",y),u=r(t,n,"sourceRoot",y),l=r(t,n,"sourcesContent",I),m=r(t,n,"target",Ge),s=r(t,n,"format",y),d=r(t,n,"globalName",y),b=r(t,n,"mangleProps",ye),w=r(t,n,"reserveProps",ye),M=r(t,n,"mangleQuoted",I),L=r(t,n,"minify",I),B=r(t,n,"minifySyntax",I),q=r(t,n,"minifyWhitespace",I),G=r(t,n,"minifyIdentifiers",I),U=r(t,n,"lineLimit",le),z=r(t,n,"drop",W),_=r(t,n,"dropLabels",W),v=r(t,n,"charset",y),g=r(t,n,"treeShaking",I),c=r(t,n,"ignoreAnnotations",I),o=r(t,n,"jsx",y),x=r(t,n,"jsxFactory",y),E=r(t,n,"jsxFragment",y),C=r(t,n,"jsxImportSource",y),S=r(t,n,"jsxDev",I),a=r(t,n,"jsxSideEffects",I),f=r(t,n,"define",ee),h=r(t,n,"logOverride",ee),A=r(t,n,"supported",ee),T=r(t,n,"pure",W),k=r(t,n,"keepNames",I),O=r(t,n,"platform",y),F=r(t,n,"tsconfigRaw",Je);if(i&&e.push(`--legal-comments=${i}`),u!==void 0&&e.push(`--source-root=${u}`),l!==void 0&&e.push(`--sources-content=${l}`),m&&(Array.isArray(m)?e.push(`--target=${Array.from(m).map(Me).join(",")}`):e.push(`--target=${Me(m)}`)),s&&e.push(`--format=${s}`),d&&e.push(`--global-name=${d}`),O&&e.push(`--platform=${O}`),F&&e.push(`--tsconfig-raw=${typeof F=="string"?F:JSON.stringify(F)}`),L&&e.push("--minify"),B&&e.push("--minify-syntax"),q&&e.push("--minify-whitespace"),G&&e.push("--minify-identifiers"),U&&e.push(`--line-limit=${U}`),v&&e.push(`--charset=${v}`),g!==void 0&&e.push(`--tree-shaking=${g}`),c&&e.push("--ignore-annotations"),z)for(let R of z)e.push(`--drop:${Y(R,"drop")}`);if(_&&e.push(`--drop-labels=${Array.from(_).map(R=>Y(R,"dropLabels")).join(",")}`),b&&e.push(`--mangle-props=${b.source}`),w&&e.push(`--reserve-props=${w.source}`),M!==void 0&&e.push(`--mangle-quoted=${M}`),o&&e.push(`--jsx=${o}`),x&&e.push(`--jsx-factory=${x}`),E&&e.push(`--jsx-fragment=${E}`),C&&e.push(`--jsx-import-source=${C}`),S&&e.push("--jsx-dev"),a&&e.push("--jsx-side-effects"),f)for(let R in f){if(R.indexOf("=")>=0)throw new Error(`Invalid define: ${R}`);e.push(`--define:${R}=${Y(f[R],"define",R)}`)}if(h)for(let R in h){if(R.indexOf("=")>=0)throw new Error(`Invalid log override: ${R}`);e.push(`--log-override:${R}=${Y(h[R],"log override",R)}`)}if(A)for(let R in A){if(R.indexOf("=")>=0)throw new Error(`Invalid supported: ${R}`);let $=A[R];if(typeof $!="boolean")throw new Error(`Expected value for supported ${J(R)} to be a boolean, got ${typeof $} instead`);e.push(`--supported:${R}=${$}`)}if(T)for(let R of T)e.push(`--pure:${Y(R,"pure")}`);k&&e.push("--keep-names")}function He(e,t,n,i,u){var oe;let l=[],m=[],s=Object.create(null),d=null,b=null;he(l,t,s,n,i),Ue(l,t,s);let w=r(t,s,"sourcemap",Te),M=r(t,s,"bundle",I),L=r(t,s,"splitting",I),B=r(t,s,"preserveSymlinks",I),q=r(t,s,"metafile",I),G=r(t,s,"outfile",y),U=r(t,s,"outdir",y),z=r(t,s,"outbase",y),_=r(t,s,"tsconfig",y),v=r(t,s,"resolveExtensions",W),g=r(t,s,"nodePaths",W),c=r(t,s,"mainFields",W),o=r(t,s,"conditions",W),x=r(t,s,"external",W),E=r(t,s,"packages",y),C=r(t,s,"alias",ee),S=r(t,s,"loader",ee),a=r(t,s,"outExtension",ee),f=r(t,s,"publicPath",y),h=r(t,s,"entryNames",y),A=r(t,s,"chunkNames",y),T=r(t,s,"assetNames",y),k=r(t,s,"inject",W),O=r(t,s,"banner",ee),F=r(t,s,"footer",ee),R=r(t,s,"entryPoints",Ve),$=r(t,s,"absWorkingDir",y),D=r(t,s,"stdin",ee),P=(oe=r(t,s,"write",I))!=null?oe:u,j=r(t,s,"allowOverwrite",I),V=r(t,s,"mangleCache",ee);if(s.plugins=!0,K(t,s,`in ${e}() call`),w&&l.push(`--sourcemap${w===!0?"":`=${w}`}`),M&&l.push("--bundle"),j&&l.push("--allow-overwrite"),L&&l.push("--splitting"),B&&l.push("--preserve-symlinks"),q&&l.push("--metafile"),G&&l.push(`--outfile=${G}`),U&&l.push(`--outdir=${U}`),z&&l.push(`--outbase=${z}`),_&&l.push(`--tsconfig=${_}`),E&&l.push(`--packages=${E}`),v){let p=[];for(let N of v){if(Y(N,"resolve extension"),N.indexOf(",")>=0)throw new Error(`Invalid resolve extension: ${N}`);p.push(N)}l.push(`--resolve-extensions=${p.join(",")}`)}if(f&&l.push(`--public-path=${f}`),h&&l.push(`--entry-names=${h}`),A&&l.push(`--chunk-names=${A}`),T&&l.push(`--asset-names=${T}`),c){let p=[];for(let N of c){if(Y(N,"main field"),N.indexOf(",")>=0)throw new Error(`Invalid main field: ${N}`);p.push(N)}l.push(`--main-fields=${p.join(",")}`)}if(o){let p=[];for(let N of o){if(Y(N,"condition"),N.indexOf(",")>=0)throw new Error(`Invalid condition: ${N}`);p.push(N)}l.push(`--conditions=${p.join(",")}`)}if(x)for(let p of x)l.push(`--external:${Y(p,"external")}`);if(C)for(let p in C){if(p.indexOf("=")>=0)throw new Error(`Invalid package name in alias: ${p}`);l.push(`--alias:${p}=${Y(C[p],"alias",p)}`)}if(O)for(let p in O){if(p.indexOf("=")>=0)throw new Error(`Invalid banner file type: ${p}`);l.push(`--banner:${p}=${Y(O[p],"banner",p)}`)}if(F)for(let p in F){if(p.indexOf("=")>=0)throw new Error(`Invalid footer file type: ${p}`);l.push(`--footer:${p}=${Y(F[p],"footer",p)}`)}if(k)for(let p of k)l.push(`--inject:${Y(p,"inject")}`);if(S)for(let p in S){if(p.indexOf("=")>=0)throw new Error(`Invalid loader extension: ${p}`);l.push(`--loader:${p}=${Y(S[p],"loader",p)}`)}if(a)for(let p in a){if(p.indexOf("=")>=0)throw new Error(`Invalid out extension: ${p}`);l.push(`--out-extension:${p}=${Y(a[p],"out extension",p)}`)}if(R)if(Array.isArray(R))for(let p=0,N=R.length;p<N;p++){let X=R[p];if(typeof X=="object"&&X!==null){let te=Object.create(null),H=r(X,te,"in",y),ue=r(X,te,"out",y);if(K(X,te,"in entry point at index "+p),H===void 0)throw new Error('Missing property "in" for entry point at index '+p);if(ue===void 0)throw new Error('Missing property "out" for entry point at index '+p);m.push([ue,H])}else m.push(["",Y(X,"entry point at index "+p)])}else for(let p in R)m.push([p,Y(R[p],"entry point",p)]);if(D){let p=Object.create(null),N=r(D,p,"contents",Pe),X=r(D,p,"resolveDir",y),te=r(D,p,"sourcefile",y),H=r(D,p,"loader",y);K(D,p,'in "stdin" object'),te&&l.push(`--sourcefile=${te}`),H&&l.push(`--loader=${H}`),X&&(b=X),typeof N=="string"?d=Z(N):N instanceof Uint8Array&&(d=N)}let Q=[];if(g)for(let p of g)p+="",Q.push(p);return{entries:m,flags:l,write:P,stdinContents:d,stdinResolveDir:b,absWorkingDir:$,nodePaths:Q,mangleCache:Be(V)}}function Xe(e,t,n,i){let u=[],l=Object.create(null);he(u,t,l,n,i),Ue(u,t,l);let m=r(t,l,"sourcemap",Te),s=r(t,l,"sourcefile",y),d=r(t,l,"loader",y),b=r(t,l,"banner",y),w=r(t,l,"footer",y),M=r(t,l,"mangleCache",ee);return K(t,l,`in ${e}() call`),m&&u.push(`--sourcemap=${m===!0?"external":m}`),s&&u.push(`--sourcefile=${s}`),d&&u.push(`--loader=${d}`),b&&u.push(`--banner=${b}`),w&&u.push(`--footer=${w}`),{flags:u,mangleCache:Be(M)}}function je(e){let t={},n={didClose:!1,reason:""},i={},u=0,l=0,m=new Uint8Array(16*1024),s=0,d=v=>{let g=s+v.length;if(g>m.length){let o=new Uint8Array(g*2);o.set(m),m=o}m.set(v,s),s+=v.length;let c=0;for(;c+4<=s;){let o=Ee(m,c);if(c+4+o>s)break;c+=4,q(m.subarray(c,c+o)),c+=o}c>0&&(m.copyWithin(0,c,s),s-=c)},b=v=>{n.didClose=!0,v&&(n.reason=": "+(v.message||v));let g="The service was stopped"+n.reason;for(let c in i)i[c](g,null);i={}},w=(v,g,c)=>{if(n.didClose)return c("The service is no longer running"+n.reason,null);let o=u++;i[o]=(x,E)=>{try{c(x,E)}finally{v&&v.unref()}},v&&v.ref(),e.writeToStdin(Oe({id:o,isRequest:!0,value:g}))},M=(v,g)=>{if(n.didClose)throw new Error("The service is no longer running"+n.reason);e.writeToStdin(Oe({id:v,isRequest:!1,value:g}))},L=async(v,g)=>{try{if(g.command==="ping"){M(v,{});return}if(typeof g.key=="number"){let c=t[g.key];if(!c)return;let o=c[g.command];if(o){await o(v,g);return}}throw new Error("Invalid command: "+g.command)}catch(c){let o=[ie(c,e,null,void 0,"")];try{M(v,{errors:o})}catch(x){}}},B=!0,q=v=>{if(B){B=!1;let c=String.fromCharCode(...v);if(c!=="0.20.0")throw new Error(`Cannot start service: Host version "0.20.0" does not match binary version ${J(c)}`);return}let g=Se(v);if(g.isRequest)L(g.id,g.value);else{let c=i[g.id];delete i[g.id],g.value.error?c(g.value.error,{}):c(null,g.value)}};return{readFromStdout:d,afterClose:b,service:{buildOrContext:({callName:v,refs:g,options:c,isTTY:o,defaultWD:x,callback:E})=>{let C=0,S=l++,a={},f={ref(){++C===1&&g&&g.ref()},unref(){--C===0&&(delete t[S],g&&g.unref())}};t[S]=a,f.ref(),Ze(v,S,w,M,f,e,a,c,o,x,(h,A)=>{try{E(h,A)}finally{f.unref()}})},transform:({callName:v,refs:g,input:c,options:o,isTTY:x,fs:E,callback:C})=>{let S=Le(),a=f=>{try{if(typeof c!="string"&&!(c instanceof Uint8Array))throw new Error('The input to "transform" must be a string or a Uint8Array');let{flags:h,mangleCache:A}=Xe(v,o,x,$e),T={command:"transform",flags:h,inputFS:f!==null,input:f!==null?Z(f):typeof c=="string"?Z(c):c};A&&(T.mangleCache=A),w(g,T,(k,O)=>{if(k)return C(new Error(k),null);let F=ae(O.errors,S),R=ae(O.warnings,S),$=1,D=()=>{if(--$===0){let P={warnings:R,code:O.code,map:O.map,mangleCache:void 0,legalComments:void 0};"legalComments"in O&&(P.legalComments=O==null?void 0:O.legalComments),O.mangleCache&&(P.mangleCache=O==null?void 0:O.mangleCache),C(null,P)}};if(F.length>0)return C(fe("Transform failed",F,R),null);O.codeFS&&($++,E.readFile(O.code,(P,j)=>{P!==null?C(P,null):(O.code=j,D())})),O.mapFS&&($++,E.readFile(O.map,(P,j)=>{P!==null?C(P,null):(O.map=j,D())})),D()})}catch(h){let A=[];try{he(A,o,{},x,$e)}catch(k){}let T=ie(h,e,S,void 0,"");w(g,{command:"error",flags:A,error:T},()=>{T.detail=S.load(T.detail),C(fe("Transform failed",[T],[]),null)})}};if((typeof c=="string"||c instanceof Uint8Array)&&c.length>1024*1024){let f=a;a=()=>E.writeFile(c,f)}a(null)},formatMessages:({callName:v,refs:g,messages:c,options:o,callback:x})=>{if(!o)throw new Error(`Missing second argument in ${v}() call`);let E={},C=r(o,E,"kind",y),S=r(o,E,"color",I),a=r(o,E,"terminalWidth",le);if(K(o,E,`in ${v}() call`),C===void 0)throw new Error(`Missing "kind" in ${v}() call`);if(C!=="error"&&C!=="warning")throw new Error(`Expected "kind" to be "error" or "warning" in ${v}() call`);let f={command:"format-msgs",messages:ne(c,"messages",null,"",a),isWarning:C==="warning"};S!==void 0&&(f.color=S),a!==void 0&&(f.terminalWidth=a),w(g,f,(h,A)=>{if(h)return x(new Error(h),null);x(null,A.messages)})},analyzeMetafile:({callName:v,refs:g,metafile:c,options:o,callback:x})=>{o===void 0&&(o={});let E={},C=r(o,E,"color",I),S=r(o,E,"verbose",I);K(o,E,`in ${v}() call`);let a={command:"analyze-metafile",metafile:c};C!==void 0&&(a.color=C),S!==void 0&&(a.verbose=S),w(g,a,(f,h)=>{if(f)return x(new Error(f),null);x(null,h.result)})}}}}function Ze(e,t,n,i,u,l,m,s,d,b,w){let M=Le(),L=e==="context",B=(U,z)=>{let _=[];try{he(_,s,{},d,ke)}catch(g){}let v=ie(U,l,M,void 0,z);n(u,{command:"error",flags:_,error:v},()=>{v.detail=M.load(v.detail),w(fe(L?"Context failed":"Build failed",[v],[]),null)})},q;if(typeof s=="object"){let U=s.plugins;if(U!==void 0){if(!Array.isArray(U))return B(new Error('"plugins" must be an array'),"");q=U}}if(q&&q.length>0){if(l.isSync)return B(new Error("Cannot use plugins in synchronous API calls"),"");et(t,n,i,u,l,m,s,q,M).then(U=>{if(!U.ok)return B(U.error,U.pluginName);try{G(U.requestPlugins,U.runOnEndCallbacks,U.scheduleOnDisposeCallbacks)}catch(z){B(z,"")}},U=>B(U,""));return}try{G(null,(U,z)=>z([],[]),()=>{})}catch(U){B(U,"")}function G(U,z,_){let v=l.hasFS,{entries:g,flags:c,write:o,stdinContents:x,stdinResolveDir:E,absWorkingDir:C,nodePaths:S,mangleCache:a}=He(e,s,d,ke,v);if(o&&!l.hasFS)throw new Error('The "write" option is unavailable in this environment');let f={command:"build",key:t,entries:g,flags:c,write:o,stdinContents:x,stdinResolveDir:E,absWorkingDir:C||b,nodePaths:S,context:L};U&&(f.plugins=U),a&&(f.mangleCache=a);let h=(k,O)=>{let F={errors:ae(k.errors,M),warnings:ae(k.warnings,M),outputFiles:void 0,metafile:void 0,mangleCache:void 0},R=F.errors.slice(),$=F.warnings.slice();k.outputFiles&&(F.outputFiles=k.outputFiles.map(tt)),k.metafile&&(F.metafile=JSON.parse(k.metafile)),k.mangleCache&&(F.mangleCache=k.mangleCache),k.writeToStdout!==void 0&&console.log(re(k.writeToStdout).replace(/\n$/,"")),z(F,(D,P)=>{if(R.length>0||D.length>0){let j=fe("Build failed",R.concat(D),$.concat(P));return O(j,null,D,P)}O(null,F,D,P)})},A,T;L&&(m["on-end"]=(k,O)=>new Promise(F=>{h(O,(R,$,D,P)=>{let j={errors:D,warnings:P};T&&T(R,$),A=void 0,T=void 0,i(k,j),F()})})),n(u,f,(k,O)=>{if(k)return w(new Error(k),null);if(!L)return h(O,($,D)=>(_(),w($,D)));if(O.errors.length>0)return w(fe("Context failed",O.errors,O.warnings),null);let F=!1,R={rebuild:()=>(A||(A=new Promise(($,D)=>{let P;T=(V,Q)=>{P||(P=()=>V?D(V):$(Q))};let j=()=>{n(u,{command:"rebuild",key:t},(Q,oe)=>{Q?D(new Error(Q)):P?P():j()})};j()})),A),watch:($={})=>new Promise((D,P)=>{if(!l.hasFS)throw new Error('Cannot use the "watch" API in this environment');K($,{},"in watch() call"),n(u,{command:"watch",key:t},Q=>{Q?P(new Error(Q)):D(void 0)})}),serve:($={})=>new Promise((D,P)=>{if(!l.hasFS)throw new Error('Cannot use the "serve" API in this environment');let j={},V=r($,j,"port",le),Q=r($,j,"host",y),oe=r($,j,"servedir",y),p=r($,j,"keyfile",y),N=r($,j,"certfile",y),X=r($,j,"fallback",y),te=r($,j,"onRequest",Fe);K($,j,"in serve() call");let H={command:"serve",key:t,onRequest:!!te};V!==void 0&&(H.port=V),Q!==void 0&&(H.host=Q),oe!==void 0&&(H.servedir=oe),p!==void 0&&(H.keyfile=p),N!==void 0&&(H.certfile=N),X!==void 0&&(H.fallback=X),n(u,H,(ue,Ne)=>{if(ue)return P(new Error(ue));te&&(m["serve-request"]=(Ie,We)=>{te(We.args),i(Ie,{})}),D(Ne)})}),cancel:()=>new Promise($=>{if(F)return $();n(u,{command:"cancel",key:t},()=>{$()})}),dispose:()=>new Promise($=>{if(F)return $();F=!0,n(u,{command:"dispose",key:t},()=>{$(),_(),u.unref()})})};u.ref(),w(null,R)})}}var et=async(e,t,n,i,u,l,m,s,d)=>{let b=[],w=[],M={},L={},B=[],q=0,G=0,U=[],z=!1;s=[...s];for(let g of s){let c={};if(typeof g!="object")throw new Error(`Plugin at index ${G} must be an object`);let o=r(g,c,"name",y);if(typeof o!="string"||o==="")throw new Error(`Plugin at index ${G} is missing a name`);try{let x=r(g,c,"setup",Fe);if(typeof x!="function")throw new Error("Plugin is missing a setup function");K(g,c,`on plugin ${J(o)}`);let E={name:o,onStart:!1,onEnd:!1,onResolve:[],onLoad:[]};G++;let S=x({initialOptions:m,resolve:(a,f={})=>{if(!z)throw new Error('Cannot call "resolve" before plugin setup has completed');if(typeof a!="string")throw new Error("The path to resolve must be a string");let h=Object.create(null),A=r(f,h,"pluginName",y),T=r(f,h,"importer",y),k=r(f,h,"namespace",y),O=r(f,h,"resolveDir",y),F=r(f,h,"kind",y),R=r(f,h,"pluginData",me);return K(f,h,"in resolve() call"),new Promise(($,D)=>{let P={command:"resolve",path:a,key:e,pluginName:o};if(A!=null&&(P.pluginName=A),T!=null&&(P.importer=T),k!=null&&(P.namespace=k),O!=null&&(P.resolveDir=O),F!=null)P.kind=F;else throw new Error('Must specify "kind" when calling "resolve"');R!=null&&(P.pluginData=d.store(R)),t(i,P,(j,V)=>{j!==null?D(new Error(j)):$({errors:ae(V.errors,d),warnings:ae(V.warnings,d),path:V.path,external:V.external,sideEffects:V.sideEffects,namespace:V.namespace,suffix:V.suffix,pluginData:d.load(V.pluginData)})})})},onStart(a){let f='This error came from the "onStart" callback registered here:',h=pe(new Error(f),u,"onStart");b.push({name:o,callback:a,note:h}),E.onStart=!0},onEnd(a){let f='This error came from the "onEnd" callback registered here:',h=pe(new Error(f),u,"onEnd");w.push({name:o,callback:a,note:h}),E.onEnd=!0},onResolve(a,f){let h='This error came from the "onResolve" callback registered here:',A=pe(new Error(h),u,"onResolve"),T={},k=r(a,T,"filter",ye),O=r(a,T,"namespace",y);if(K(a,T,`in onResolve() call for plugin ${J(o)}`),k==null)throw new Error("onResolve() call is missing a filter");let F=q++;M[F]={name:o,callback:f,note:A},E.onResolve.push({id:F,filter:k.source,namespace:O||""})},onLoad(a,f){let h='This error came from the "onLoad" callback registered here:',A=pe(new Error(h),u,"onLoad"),T={},k=r(a,T,"filter",ye),O=r(a,T,"namespace",y);if(K(a,T,`in onLoad() call for plugin ${J(o)}`),k==null)throw new Error("onLoad() call is missing a filter");let F=q++;L[F]={name:o,callback:f,note:A},E.onLoad.push({id:F,filter:k.source,namespace:O||""})},onDispose(a){B.push(a)},esbuild:u.esbuild});S&&await S,U.push(E)}catch(x){return{ok:!1,error:x,pluginName:o}}}l["on-start"]=async(g,c)=>{let o={errors:[],warnings:[]};await Promise.all(b.map(async({name:x,callback:E,note:C})=>{try{let S=await E();if(S!=null){if(typeof S!="object")throw new Error(`Expected onStart() callback in plugin ${J(x)} to return an object`);let a={},f=r(S,a,"errors",W),h=r(S,a,"warnings",W);K(S,a,`from onStart() callback in plugin ${J(x)}`),f!=null&&o.errors.push(...ne(f,"errors",d,x,void 0)),h!=null&&o.warnings.push(...ne(h,"warnings",d,x,void 0))}}catch(S){o.errors.push(ie(S,u,d,C&&C(),x))}})),n(g,o)},l["on-resolve"]=async(g,c)=>{let o={},x="",E,C;for(let S of c.ids)try{({name:x,callback:E,note:C}=M[S]);let a=await E({path:c.path,importer:c.importer,namespace:c.namespace,resolveDir:c.resolveDir,kind:c.kind,pluginData:d.load(c.pluginData)});if(a!=null){if(typeof a!="object")throw new Error(`Expected onResolve() callback in plugin ${J(x)} to return an object`);let f={},h=r(a,f,"pluginName",y),A=r(a,f,"path",y),T=r(a,f,"namespace",y),k=r(a,f,"suffix",y),O=r(a,f,"external",I),F=r(a,f,"sideEffects",I),R=r(a,f,"pluginData",me),$=r(a,f,"errors",W),D=r(a,f,"warnings",W),P=r(a,f,"watchFiles",W),j=r(a,f,"watchDirs",W);K(a,f,`from onResolve() callback in plugin ${J(x)}`),o.id=S,h!=null&&(o.pluginName=h),A!=null&&(o.path=A),T!=null&&(o.namespace=T),k!=null&&(o.suffix=k),O!=null&&(o.external=O),F!=null&&(o.sideEffects=F),R!=null&&(o.pluginData=d.store(R)),$!=null&&(o.errors=ne($,"errors",d,x,void 0)),D!=null&&(o.warnings=ne(D,"warnings",d,x,void 0)),P!=null&&(o.watchFiles=ge(P,"watchFiles")),j!=null&&(o.watchDirs=ge(j,"watchDirs"));break}}catch(a){o={id:S,errors:[ie(a,u,d,C&&C(),x)]};break}n(g,o)},l["on-load"]=async(g,c)=>{let o={},x="",E,C;for(let S of c.ids)try{({name:x,callback:E,note:C}=L[S]);let a=await E({path:c.path,namespace:c.namespace,suffix:c.suffix,pluginData:d.load(c.pluginData),with:c.with});if(a!=null){if(typeof a!="object")throw new Error(`Expected onLoad() callback in plugin ${J(x)} to return an object`);let f={},h=r(a,f,"pluginName",y),A=r(a,f,"contents",Pe),T=r(a,f,"resolveDir",y),k=r(a,f,"pluginData",me),O=r(a,f,"loader",y),F=r(a,f,"errors",W),R=r(a,f,"warnings",W),$=r(a,f,"watchFiles",W),D=r(a,f,"watchDirs",W);K(a,f,`from onLoad() callback in plugin ${J(x)}`),o.id=S,h!=null&&(o.pluginName=h),A instanceof Uint8Array?o.contents=A:A!=null&&(o.contents=Z(A)),T!=null&&(o.resolveDir=T),k!=null&&(o.pluginData=d.store(k)),O!=null&&(o.loader=O),F!=null&&(o.errors=ne(F,"errors",d,x,void 0)),R!=null&&(o.warnings=ne(R,"warnings",d,x,void 0)),$!=null&&(o.watchFiles=ge($,"watchFiles")),D!=null&&(o.watchDirs=ge(D,"watchDirs"));break}}catch(a){o={id:S,errors:[ie(a,u,d,C&&C(),x)]};break}n(g,o)};let _=(g,c)=>c([],[]);w.length>0&&(_=(g,c)=>{(async()=>{let o=[],x=[];for(let{name:E,callback:C,note:S}of w){let a,f;try{let h=await C(g);if(h!=null){if(typeof h!="object")throw new Error(`Expected onEnd() callback in plugin ${J(E)} to return an object`);let A={},T=r(h,A,"errors",W),k=r(h,A,"warnings",W);K(h,A,`from onEnd() callback in plugin ${J(E)}`),T!=null&&(a=ne(T,"errors",d,E,void 0)),k!=null&&(f=ne(k,"warnings",d,E,void 0))}}catch(h){a=[ie(h,u,d,S&&S(),E)]}if(a){o.push(...a);try{g.errors.push(...a)}catch(h){}}if(f){x.push(...f);try{g.warnings.push(...f)}catch(h){}}}c(o,x)})()});let v=()=>{for(let g of B)setTimeout(()=>g(),0)};return z=!0,{ok:!0,requestPlugins:U,runOnEndCallbacks:_,scheduleOnDisposeCallbacks:v}};function Le(){let e=new Map,t=0;return{load(n){return e.get(n)},store(n){if(n===void 0)return-1;let i=t++;return e.set(i,n),i}}}function pe(e,t,n){let i,u=!1;return()=>{if(u)return i;u=!0;try{let l=(e.stack+"").split(`
`);l.splice(1,1);let m=qe(t,l,n);if(m)return i={text:e.message,location:m},i}catch(l){}}}function ie(e,t,n,i,u){let l="Internal error",m=null;try{l=(e&&e.message||e)+""}catch(s){}try{m=qe(t,(e.stack+"").split(`
`),"")}catch(s){}return{id:"",pluginName:u,text:l,location:m,notes:i?[i]:[],detail:n?n.store(e):-1}}function qe(e,t,n){let i="    at ";if(e.readFileSync&&!t[0].startsWith(i)&&t[1].startsWith(i))for(let u=1;u<t.length;u++){let l=t[u];if(l.startsWith(i))for(l=l.slice(i.length);;){let m=/^(?:new |async )?\S+ \((.*)\)$/.exec(l);if(m){l=m[1];continue}if(m=/^eval at \S+ \((.*)\)(?:, \S+:\d+:\d+)?$/.exec(l),m){l=m[1];continue}if(m=/^(\S+):(\d+):(\d+)$/.exec(l),m){let s;try{s=e.readFileSync(m[1],"utf8")}catch(M){break}let d=s.split(/\r\n|\r|\n|\u2028|\u2029/)[+m[2]-1]||"",b=+m[3]-1,w=d.slice(b,b+n.length)===n?n.length:0;return{file:m[1],namespace:"file",line:+m[2],column:Z(d.slice(0,b)).length,length:Z(d.slice(b,b+w)).length,lineText:d+`
`+t.slice(1).join(`
`),suggestion:""}}break}}return null}function fe(e,t,n){let i=5;e+=t.length<1?"":` with ${t.length} error${t.length<2?"":"s"}:`+t.slice(0,i+1).map((l,m)=>{if(m===i)return`
...`;if(!l.location)return`
error: ${l.text}`;let{file:s,line:d,column:b}=l.location,w=l.pluginName?`[plugin: ${l.pluginName}] `:"";return`
${s}:${d}:${b}: ERROR: ${w}${l.text}`}).join("");let u=new Error(e);for(let[l,m]of[["errors",t],["warnings",n]])Object.defineProperty(u,l,{configurable:!0,enumerable:!0,get:()=>m,set:s=>Object.defineProperty(u,l,{configurable:!0,enumerable:!0,value:s})});return u}function ae(e,t){for(let n of e)n.detail=t.load(n.detail);return e}function Ae(e,t,n){if(e==null)return null;let i={},u=r(e,i,"file",y),l=r(e,i,"namespace",y),m=r(e,i,"line",le),s=r(e,i,"column",le),d=r(e,i,"length",le),b=r(e,i,"lineText",y),w=r(e,i,"suggestion",y);if(K(e,i,t),b){let M=b.slice(0,(s&&s>0?s:0)+(d&&d>0?d:0)+(n&&n>0?n:80));!/[\x7F-\uFFFF]/.test(M)&&!/\n/.test(b)&&(b=M)}return{file:u||"",namespace:l||"",line:m||0,column:s||0,length:d||0,lineText:b||"",suggestion:w||""}}function ne(e,t,n,i,u){let l=[],m=0;for(let s of e){let d={},b=r(s,d,"id",y),w=r(s,d,"pluginName",y),M=r(s,d,"text",y),L=r(s,d,"location",Ce),B=r(s,d,"notes",W),q=r(s,d,"detail",me),G=`in element ${m} of "${t}"`;K(s,d,G);let U=[];if(B)for(let z of B){let _={},v=r(z,_,"text",y),g=r(z,_,"location",Ce);K(z,_,G),U.push({text:v||"",location:Ae(g,G,u)})}l.push({id:b||"",pluginName:w||i,text:M||"",location:Ae(L,G,u),notes:U,detail:n?n.store(q):-1}),m++}return l}function ge(e,t){let n=[];for(let i of e){if(typeof i!="string")throw new Error(`${J(t)} must be an array of strings`);n.push(i)}return n}function tt({path:e,contents:t,hash:n}){let i=null;return{path:e,contents:t,hash:n,get text(){let u=this.contents;return(i===null||u!==t)&&(t=u,i=re(u)),i}}}var rt="0.20.0",it=e=>ce().build(e),lt=e=>ce().context(e),st=(e,t)=>ce().transform(e,t),ot=(e,t)=>ce().formatMessages(e,t),at=(e,t)=>ce().analyzeMetafile(e,t),ut=()=>{throw new Error('The "buildSync" API only works in node')},ft=()=>{throw new Error('The "transformSync" API only works in node')},ct=()=>{throw new Error('The "formatMessagesSync" API only works in node')},dt=()=>{throw new Error('The "analyzeMetafileSync" API only works in node')},pt=()=>(be&&be(),Promise.resolve()),se,be,we,ce=()=>{if(we)return we;throw se?new Error('You need to wait for the promise returned from "initialize" to be resolved before calling this'):new Error('You need to call "initialize" before calling this')},gt=e=>{e=De(e||{});let t=e.wasmURL,n=e.wasmModule,i=e.worker!==!1;if(!t&&!n)throw new Error('Must provide either the "wasmURL" option or the "wasmModule" option');if(se)throw new Error('Cannot call "initialize" more than once');return se=mt(t||"",n,i),se.catch(()=>{se=void 0}),se},mt=async(e,t,n)=>{let i;if(n){let b=new Blob(['onmessage=(postMessage=>{\n// Copyright 2018 The Go Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style\n// license that can be found in the LICENSE file.\nlet onmessage,globalThis={};for(let r=self;r;r=Object.getPrototypeOf(r))for(let f of Object.getOwnPropertyNames(r))f in globalThis||Object.defineProperty(globalThis,f,{get:()=>self[f]});(()=>{const r=()=>{const c=new Error("not implemented");return c.code="ENOSYS",c};if(!globalThis.fs){let c="";globalThis.fs={constants:{O_WRONLY:-1,O_RDWR:-1,O_CREAT:-1,O_TRUNC:-1,O_APPEND:-1,O_EXCL:-1},writeSync(n,s){c+=g.decode(s);const i=c.lastIndexOf(`\n`);return i!=-1&&(console.log(c.substring(0,i)),c=c.substring(i+1)),s.length},write(n,s,i,a,u,h){if(i!==0||a!==s.length||u!==null){h(r());return}const d=this.writeSync(n,s);h(null,d)},chmod(n,s,i){i(r())},chown(n,s,i,a){a(r())},close(n,s){s(r())},fchmod(n,s,i){i(r())},fchown(n,s,i,a){a(r())},fstat(n,s){s(r())},fsync(n,s){s(null)},ftruncate(n,s,i){i(r())},lchown(n,s,i,a){a(r())},link(n,s,i){i(r())},lstat(n,s){s(r())},mkdir(n,s,i){i(r())},open(n,s,i,a){a(r())},read(n,s,i,a,u,h){h(r())},readdir(n,s){s(r())},readlink(n,s){s(r())},rename(n,s,i){i(r())},rmdir(n,s){s(r())},stat(n,s){s(r())},symlink(n,s,i){i(r())},truncate(n,s,i){i(r())},unlink(n,s){s(r())},utimes(n,s,i,a){a(r())}}}if(globalThis.process||(globalThis.process={getuid(){return-1},getgid(){return-1},geteuid(){return-1},getegid(){return-1},getgroups(){throw r()},pid:-1,ppid:-1,umask(){throw r()},cwd(){throw r()},chdir(){throw r()}}),!globalThis.crypto)throw new Error("globalThis.crypto is not available, polyfill required (crypto.getRandomValues only)");if(!globalThis.performance)throw new Error("globalThis.performance is not available, polyfill required (performance.now only)");if(!globalThis.TextEncoder)throw new Error("globalThis.TextEncoder is not available, polyfill required");if(!globalThis.TextDecoder)throw new Error("globalThis.TextDecoder is not available, polyfill required");const f=new TextEncoder("utf-8"),g=new TextDecoder("utf-8");globalThis.Go=class{constructor(){this.argv=["js"],this.env={},this.exit=t=>{t!==0&&console.warn("exit code:",t)},this._exitPromise=new Promise(t=>{this._resolveExitPromise=t}),this._pendingEvent=null,this._scheduledTimeouts=new Map,this._nextCallbackTimeoutID=1;const c=(t,e)=>{this.mem.setUint32(t+0,e,!0),this.mem.setUint32(t+4,Math.floor(e/4294967296),!0)},n=t=>{const e=this.mem.getUint32(t+0,!0),o=this.mem.getInt32(t+4,!0);return e+o*4294967296},s=t=>{const e=this.mem.getFloat64(t,!0);if(e===0)return;if(!isNaN(e))return e;const o=this.mem.getUint32(t,!0);return this._values[o]},i=(t,e)=>{if(typeof e=="number"&&e!==0){if(isNaN(e)){this.mem.setUint32(t+4,2146959360,!0),this.mem.setUint32(t,0,!0);return}this.mem.setFloat64(t,e,!0);return}if(e===void 0){this.mem.setFloat64(t,0,!0);return}let l=this._ids.get(e);l===void 0&&(l=this._idPool.pop(),l===void 0&&(l=this._values.length),this._values[l]=e,this._goRefCounts[l]=0,this._ids.set(e,l)),this._goRefCounts[l]++;let m=0;switch(typeof e){case"object":e!==null&&(m=1);break;case"string":m=2;break;case"symbol":m=3;break;case"function":m=4;break}this.mem.setUint32(t+4,2146959360|m,!0),this.mem.setUint32(t,l,!0)},a=t=>{const e=n(t+0),o=n(t+8);return new Uint8Array(this._inst.exports.mem.buffer,e,o)},u=t=>{const e=n(t+0),o=n(t+8),l=new Array(o);for(let m=0;m<o;m++)l[m]=s(e+m*8);return l},h=t=>{const e=n(t+0),o=n(t+8);return g.decode(new DataView(this._inst.exports.mem.buffer,e,o))},d=Date.now()-performance.now();this.importObject={go:{"runtime.wasmExit":t=>{t>>>=0;const e=this.mem.getInt32(t+8,!0);this.exited=!0,delete this._inst,delete this._values,delete this._goRefCounts,delete this._ids,delete this._idPool,this.exit(e)},"runtime.wasmWrite":t=>{t>>>=0;const e=n(t+8),o=n(t+16),l=this.mem.getInt32(t+24,!0);globalThis.fs.writeSync(e,new Uint8Array(this._inst.exports.mem.buffer,o,l))},"runtime.resetMemoryDataView":t=>{t>>>=0,this.mem=new DataView(this._inst.exports.mem.buffer)},"runtime.nanotime1":t=>{t>>>=0,c(t+8,(d+performance.now())*1e6)},"runtime.walltime":t=>{t>>>=0;const e=new Date().getTime();c(t+8,e/1e3),this.mem.setInt32(t+16,e%1e3*1e6,!0)},"runtime.scheduleTimeoutEvent":t=>{t>>>=0;const e=this._nextCallbackTimeoutID;this._nextCallbackTimeoutID++,this._scheduledTimeouts.set(e,setTimeout(()=>{for(this._resume();this._scheduledTimeouts.has(e);)console.warn("scheduleTimeoutEvent: missed timeout event"),this._resume()},n(t+8)+1)),this.mem.setInt32(t+16,e,!0)},"runtime.clearTimeoutEvent":t=>{t>>>=0;const e=this.mem.getInt32(t+8,!0);clearTimeout(this._scheduledTimeouts.get(e)),this._scheduledTimeouts.delete(e)},"runtime.getRandomData":t=>{t>>>=0,crypto.getRandomValues(a(t+8))},"syscall/js.finalizeRef":t=>{t>>>=0;const e=this.mem.getUint32(t+8,!0);if(this._goRefCounts[e]--,this._goRefCounts[e]===0){const o=this._values[e];this._values[e]=null,this._ids.delete(o),this._idPool.push(e)}},"syscall/js.stringVal":t=>{t>>>=0,i(t+24,h(t+8))},"syscall/js.valueGet":t=>{t>>>=0;const e=Reflect.get(s(t+8),h(t+16));t=this._inst.exports.getsp()>>>0,i(t+32,e)},"syscall/js.valueSet":t=>{t>>>=0,Reflect.set(s(t+8),h(t+16),s(t+32))},"syscall/js.valueDelete":t=>{t>>>=0,Reflect.deleteProperty(s(t+8),h(t+16))},"syscall/js.valueIndex":t=>{t>>>=0,i(t+24,Reflect.get(s(t+8),n(t+16)))},"syscall/js.valueSetIndex":t=>{t>>>=0,Reflect.set(s(t+8),n(t+16),s(t+24))},"syscall/js.valueCall":t=>{t>>>=0;try{const e=s(t+8),o=Reflect.get(e,h(t+16)),l=u(t+32),m=Reflect.apply(o,e,l);t=this._inst.exports.getsp()>>>0,i(t+56,m),this.mem.setUint8(t+64,1)}catch(e){t=this._inst.exports.getsp()>>>0,i(t+56,e),this.mem.setUint8(t+64,0)}},"syscall/js.valueInvoke":t=>{t>>>=0;try{const e=s(t+8),o=u(t+16),l=Reflect.apply(e,void 0,o);t=this._inst.exports.getsp()>>>0,i(t+40,l),this.mem.setUint8(t+48,1)}catch(e){t=this._inst.exports.getsp()>>>0,i(t+40,e),this.mem.setUint8(t+48,0)}},"syscall/js.valueNew":t=>{t>>>=0;try{const e=s(t+8),o=u(t+16),l=Reflect.construct(e,o);t=this._inst.exports.getsp()>>>0,i(t+40,l),this.mem.setUint8(t+48,1)}catch(e){t=this._inst.exports.getsp()>>>0,i(t+40,e),this.mem.setUint8(t+48,0)}},"syscall/js.valueLength":t=>{t>>>=0,c(t+16,parseInt(s(t+8).length))},"syscall/js.valuePrepareString":t=>{t>>>=0;const e=f.encode(String(s(t+8)));i(t+16,e),c(t+24,e.length)},"syscall/js.valueLoadString":t=>{t>>>=0;const e=s(t+8);a(t+16).set(e)},"syscall/js.valueInstanceOf":t=>{t>>>=0,this.mem.setUint8(t+24,s(t+8)instanceof s(t+16)?1:0)},"syscall/js.copyBytesToGo":t=>{t>>>=0;const e=a(t+8),o=s(t+32);if(!(o instanceof Uint8Array||o instanceof Uint8ClampedArray)){this.mem.setUint8(t+48,0);return}const l=o.subarray(0,e.length);e.set(l),c(t+40,l.length),this.mem.setUint8(t+48,1)},"syscall/js.copyBytesToJS":t=>{t>>>=0;const e=s(t+8),o=a(t+16);if(!(e instanceof Uint8Array||e instanceof Uint8ClampedArray)){this.mem.setUint8(t+48,0);return}const l=o.subarray(0,e.length);e.set(l),c(t+40,l.length),this.mem.setUint8(t+48,1)},debug:t=>{console.log(t)}}}}async run(c){if(!(c instanceof WebAssembly.Instance))throw new Error("Go.run: WebAssembly.Instance expected");this._inst=c,this.mem=new DataView(this._inst.exports.mem.buffer),this._values=[NaN,0,null,!0,!1,globalThis,this],this._goRefCounts=new Array(this._values.length).fill(1/0),this._ids=new Map([[0,1],[null,2],[!0,3],[!1,4],[globalThis,5],[this,6]]),this._idPool=[],this.exited=!1;let n=4096;const s=t=>{const e=n,o=f.encode(t+"\\0");return new Uint8Array(this.mem.buffer,n,o.length).set(o),n+=o.length,n%8!==0&&(n+=8-n%8),e},i=this.argv.length,a=[];this.argv.forEach(t=>{a.push(s(t))}),a.push(0),Object.keys(this.env).sort().forEach(t=>{a.push(s(`${t}=${this.env[t]}`))}),a.push(0);const h=n;if(a.forEach(t=>{this.mem.setUint32(n,t,!0),this.mem.setUint32(n+4,0,!0),n+=8}),n>=12288)throw new Error("total length of command line and environment variables exceeds limit");this._inst.exports.run(i,h),this.exited&&this._resolveExitPromise(),await this._exitPromise}_resume(){if(this.exited)throw new Error("Go program has already exited");this._inst.exports.resume(),this.exited&&this._resolveExitPromise()}_makeFuncWrapper(c){const n=this;return function(){const s={id:c,this:this,args:arguments};return n._pendingEvent=s,n._resume(),s.result}}}})(),onmessage=({data:r})=>{let f=new TextDecoder,g=globalThis.fs,c="";g.writeSync=(u,h)=>{if(u===1)postMessage(h);else if(u===2){c+=f.decode(h);let d=c.split(`\n`);d.length>1&&console.log(d.slice(0,-1).join(`\n`)),c=d[d.length-1]}else throw new Error("Bad write");return h.length};let n=[],s,i=0;onmessage=({data:u})=>(u.length>0&&(n.push(u),s&&s()),a),g.read=(u,h,d,t,e,o)=>{if(u!==0||d!==0||t!==h.length||e!==null)throw new Error("Bad read");if(n.length===0){s=()=>g.read(u,h,d,t,e,o);return}let l=n[0],m=Math.max(0,Math.min(t,l.length-i));h.set(l.subarray(i,i+m),d),i+=m,i===l.length&&(n.shift(),i=0),o(null,m)};let a=new globalThis.Go;return a.argv=["","--service=0.20.0"],tryToInstantiateModule(r,a).then(u=>{postMessage(null),a.run(u)},u=>{postMessage(u)}),a};async function tryToInstantiateModule(r,f){if(r instanceof WebAssembly.Module)return WebAssembly.instantiate(r,f.importObject);const g=await fetch(r);if(!g.ok)throw new Error(`Failed to download ${JSON.stringify(r)}`);if("instantiateStreaming"in WebAssembly&&/^application\\/wasm($|;)/i.test(g.headers.get("Content-Type")||""))return(await WebAssembly.instantiateStreaming(g,f.importObject)).instance;const c=await g.arrayBuffer();return(await WebAssembly.instantiate(c,f.importObject)).instance}return r=>onmessage(r);})(postMessage)'],{type:"text/javascript"});i=new Worker(URL.createObjectURL(b))}else{let b=(postMessage=>{
// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.
let onmessage,globalThis={};for(let r=self;r;r=Object.getPrototypeOf(r))for(let f of Object.getOwnPropertyNames(r))f in globalThis||Object.defineProperty(globalThis,f,{get:()=>self[f]});(()=>{const r=()=>{const c=new Error("not implemented");return c.code="ENOSYS",c};if(!globalThis.fs){let c="";globalThis.fs={constants:{O_WRONLY:-1,O_RDWR:-1,O_CREAT:-1,O_TRUNC:-1,O_APPEND:-1,O_EXCL:-1},writeSync(n,s){c+=g.decode(s);const i=c.lastIndexOf(`
`);return i!=-1&&(console.log(c.substring(0,i)),c=c.substring(i+1)),s.length},write(n,s,i,a,u,h){if(i!==0||a!==s.length||u!==null){h(r());return}const d=this.writeSync(n,s);h(null,d)},chmod(n,s,i){i(r())},chown(n,s,i,a){a(r())},close(n,s){s(r())},fchmod(n,s,i){i(r())},fchown(n,s,i,a){a(r())},fstat(n,s){s(r())},fsync(n,s){s(null)},ftruncate(n,s,i){i(r())},lchown(n,s,i,a){a(r())},link(n,s,i){i(r())},lstat(n,s){s(r())},mkdir(n,s,i){i(r())},open(n,s,i,a){a(r())},read(n,s,i,a,u,h){h(r())},readdir(n,s){s(r())},readlink(n,s){s(r())},rename(n,s,i){i(r())},rmdir(n,s){s(r())},stat(n,s){s(r())},symlink(n,s,i){i(r())},truncate(n,s,i){i(r())},unlink(n,s){s(r())},utimes(n,s,i,a){a(r())}}}if(globalThis.process||(globalThis.process={getuid(){return-1},getgid(){return-1},geteuid(){return-1},getegid(){return-1},getgroups(){throw r()},pid:-1,ppid:-1,umask(){throw r()},cwd(){throw r()},chdir(){throw r()}}),!globalThis.crypto)throw new Error("globalThis.crypto is not available, polyfill required (crypto.getRandomValues only)");if(!globalThis.performance)throw new Error("globalThis.performance is not available, polyfill required (performance.now only)");if(!globalThis.TextEncoder)throw new Error("globalThis.TextEncoder is not available, polyfill required");if(!globalThis.TextDecoder)throw new Error("globalThis.TextDecoder is not available, polyfill required");const f=new TextEncoder("utf-8"),g=new TextDecoder("utf-8");globalThis.Go=class{constructor(){this.argv=["js"],this.env={},this.exit=t=>{t!==0&&console.warn("exit code:",t)},this._exitPromise=new Promise(t=>{this._resolveExitPromise=t}),this._pendingEvent=null,this._scheduledTimeouts=new Map,this._nextCallbackTimeoutID=1;const c=(t,e)=>{this.mem.setUint32(t+0,e,!0),this.mem.setUint32(t+4,Math.floor(e/4294967296),!0)},n=t=>{const e=this.mem.getUint32(t+0,!0),o=this.mem.getInt32(t+4,!0);return e+o*4294967296},s=t=>{const e=this.mem.getFloat64(t,!0);if(e===0)return;if(!isNaN(e))return e;const o=this.mem.getUint32(t,!0);return this._values[o]},i=(t,e)=>{if(typeof e=="number"&&e!==0){if(isNaN(e)){this.mem.setUint32(t+4,2146959360,!0),this.mem.setUint32(t,0,!0);return}this.mem.setFloat64(t,e,!0);return}if(e===void 0){this.mem.setFloat64(t,0,!0);return}let l=this._ids.get(e);l===void 0&&(l=this._idPool.pop(),l===void 0&&(l=this._values.length),this._values[l]=e,this._goRefCounts[l]=0,this._ids.set(e,l)),this._goRefCounts[l]++;let m=0;switch(typeof e){case"object":e!==null&&(m=1);break;case"string":m=2;break;case"symbol":m=3;break;case"function":m=4;break}this.mem.setUint32(t+4,2146959360|m,!0),this.mem.setUint32(t,l,!0)},a=t=>{const e=n(t+0),o=n(t+8);return new Uint8Array(this._inst.exports.mem.buffer,e,o)},u=t=>{const e=n(t+0),o=n(t+8),l=new Array(o);for(let m=0;m<o;m++)l[m]=s(e+m*8);return l},h=t=>{const e=n(t+0),o=n(t+8);return g.decode(new DataView(this._inst.exports.mem.buffer,e,o))},d=Date.now()-performance.now();this.importObject={go:{"runtime.wasmExit":t=>{t>>>=0;const e=this.mem.getInt32(t+8,!0);this.exited=!0,delete this._inst,delete this._values,delete this._goRefCounts,delete this._ids,delete this._idPool,this.exit(e)},"runtime.wasmWrite":t=>{t>>>=0;const e=n(t+8),o=n(t+16),l=this.mem.getInt32(t+24,!0);globalThis.fs.writeSync(e,new Uint8Array(this._inst.exports.mem.buffer,o,l))},"runtime.resetMemoryDataView":t=>{t>>>=0,this.mem=new DataView(this._inst.exports.mem.buffer)},"runtime.nanotime1":t=>{t>>>=0,c(t+8,(d+performance.now())*1e6)},"runtime.walltime":t=>{t>>>=0;const e=new Date().getTime();c(t+8,e/1e3),this.mem.setInt32(t+16,e%1e3*1e6,!0)},"runtime.scheduleTimeoutEvent":t=>{t>>>=0;const e=this._nextCallbackTimeoutID;this._nextCallbackTimeoutID++,this._scheduledTimeouts.set(e,setTimeout(()=>{for(this._resume();this._scheduledTimeouts.has(e);)console.warn("scheduleTimeoutEvent: missed timeout event"),this._resume()},n(t+8)+1)),this.mem.setInt32(t+16,e,!0)},"runtime.clearTimeoutEvent":t=>{t>>>=0;const e=this.mem.getInt32(t+8,!0);clearTimeout(this._scheduledTimeouts.get(e)),this._scheduledTimeouts.delete(e)},"runtime.getRandomData":t=>{t>>>=0,crypto.getRandomValues(a(t+8))},"syscall/js.finalizeRef":t=>{t>>>=0;const e=this.mem.getUint32(t+8,!0);if(this._goRefCounts[e]--,this._goRefCounts[e]===0){const o=this._values[e];this._values[e]=null,this._ids.delete(o),this._idPool.push(e)}},"syscall/js.stringVal":t=>{t>>>=0,i(t+24,h(t+8))},"syscall/js.valueGet":t=>{t>>>=0;const e=Reflect.get(s(t+8),h(t+16));t=this._inst.exports.getsp()>>>0,i(t+32,e)},"syscall/js.valueSet":t=>{t>>>=0,Reflect.set(s(t+8),h(t+16),s(t+32))},"syscall/js.valueDelete":t=>{t>>>=0,Reflect.deleteProperty(s(t+8),h(t+16))},"syscall/js.valueIndex":t=>{t>>>=0,i(t+24,Reflect.get(s(t+8),n(t+16)))},"syscall/js.valueSetIndex":t=>{t>>>=0,Reflect.set(s(t+8),n(t+16),s(t+24))},"syscall/js.valueCall":t=>{t>>>=0;try{const e=s(t+8),o=Reflect.get(e,h(t+16)),l=u(t+32),m=Reflect.apply(o,e,l);t=this._inst.exports.getsp()>>>0,i(t+56,m),this.mem.setUint8(t+64,1)}catch(e){t=this._inst.exports.getsp()>>>0,i(t+56,e),this.mem.setUint8(t+64,0)}},"syscall/js.valueInvoke":t=>{t>>>=0;try{const e=s(t+8),o=u(t+16),l=Reflect.apply(e,void 0,o);t=this._inst.exports.getsp()>>>0,i(t+40,l),this.mem.setUint8(t+48,1)}catch(e){t=this._inst.exports.getsp()>>>0,i(t+40,e),this.mem.setUint8(t+48,0)}},"syscall/js.valueNew":t=>{t>>>=0;try{const e=s(t+8),o=u(t+16),l=Reflect.construct(e,o);t=this._inst.exports.getsp()>>>0,i(t+40,l),this.mem.setUint8(t+48,1)}catch(e){t=this._inst.exports.getsp()>>>0,i(t+40,e),this.mem.setUint8(t+48,0)}},"syscall/js.valueLength":t=>{t>>>=0,c(t+16,parseInt(s(t+8).length))},"syscall/js.valuePrepareString":t=>{t>>>=0;const e=f.encode(String(s(t+8)));i(t+16,e),c(t+24,e.length)},"syscall/js.valueLoadString":t=>{t>>>=0;const e=s(t+8);a(t+16).set(e)},"syscall/js.valueInstanceOf":t=>{t>>>=0,this.mem.setUint8(t+24,s(t+8)instanceof s(t+16)?1:0)},"syscall/js.copyBytesToGo":t=>{t>>>=0;const e=a(t+8),o=s(t+32);if(!(o instanceof Uint8Array||o instanceof Uint8ClampedArray)){this.mem.setUint8(t+48,0);return}const l=o.subarray(0,e.length);e.set(l),c(t+40,l.length),this.mem.setUint8(t+48,1)},"syscall/js.copyBytesToJS":t=>{t>>>=0;const e=s(t+8),o=a(t+16);if(!(e instanceof Uint8Array||e instanceof Uint8ClampedArray)){this.mem.setUint8(t+48,0);return}const l=o.subarray(0,e.length);e.set(l),c(t+40,l.length),this.mem.setUint8(t+48,1)},debug:t=>{console.log(t)}}}}async run(c){if(!(c instanceof WebAssembly.Instance))throw new Error("Go.run: WebAssembly.Instance expected");this._inst=c,this.mem=new DataView(this._inst.exports.mem.buffer),this._values=[NaN,0,null,!0,!1,globalThis,this],this._goRefCounts=new Array(this._values.length).fill(1/0),this._ids=new Map([[0,1],[null,2],[!0,3],[!1,4],[globalThis,5],[this,6]]),this._idPool=[],this.exited=!1;let n=4096;const s=t=>{const e=n,o=f.encode(t+"\0");return new Uint8Array(this.mem.buffer,n,o.length).set(o),n+=o.length,n%8!==0&&(n+=8-n%8),e},i=this.argv.length,a=[];this.argv.forEach(t=>{a.push(s(t))}),a.push(0),Object.keys(this.env).sort().forEach(t=>{a.push(s(`${t}=${this.env[t]}`))}),a.push(0);const h=n;if(a.forEach(t=>{this.mem.setUint32(n,t,!0),this.mem.setUint32(n+4,0,!0),n+=8}),n>=12288)throw new Error("total length of command line and environment variables exceeds limit");this._inst.exports.run(i,h),this.exited&&this._resolveExitPromise(),await this._exitPromise}_resume(){if(this.exited)throw new Error("Go program has already exited");this._inst.exports.resume(),this.exited&&this._resolveExitPromise()}_makeFuncWrapper(c){const n=this;return function(){const s={id:c,this:this,args:arguments};return n._pendingEvent=s,n._resume(),s.result}}}})(),onmessage=({data:r})=>{let f=new TextDecoder,g=globalThis.fs,c="";g.writeSync=(u,h)=>{if(u===1)postMessage(h);else if(u===2){c+=f.decode(h);let d=c.split(`
`);d.length>1&&console.log(d.slice(0,-1).join(`
`)),c=d[d.length-1]}else throw new Error("Bad write");return h.length};let n=[],s,i=0;onmessage=({data:u})=>(u.length>0&&(n.push(u),s&&s()),a),g.read=(u,h,d,t,e,o)=>{if(u!==0||d!==0||t!==h.length||e!==null)throw new Error("Bad read");if(n.length===0){s=()=>g.read(u,h,d,t,e,o);return}let l=n[0],m=Math.max(0,Math.min(t,l.length-i));h.set(l.subarray(i,i+m),d),i+=m,i===l.length&&(n.shift(),i=0),o(null,m)};let a=new globalThis.Go;return a.argv=["","--service=0.20.0"],tryToInstantiateModule(r,a).then(u=>{postMessage(null),a.run(u)},u=>{postMessage(u)}),a};async function tryToInstantiateModule(r,f){if(r instanceof WebAssembly.Module)return WebAssembly.instantiate(r,f.importObject);const g=await fetch(r);if(!g.ok)throw new Error(`Failed to download ${JSON.stringify(r)}`);if("instantiateStreaming"in WebAssembly&&/^application\/wasm($|;)/i.test(g.headers.get("Content-Type")||""))return(await WebAssembly.instantiateStreaming(g,f.importObject)).instance;const c=await g.arrayBuffer();return(await WebAssembly.instantiate(c,f.importObject)).instance}return r=>onmessage(r);})(M=>i.onmessage({data:M})),w;i={onmessage:null,postMessage:M=>setTimeout(()=>w=b({data:M})),terminate(){if(w)for(let M of w._scheduledTimeouts.values())clearTimeout(M)}}}let u,l,m=new Promise((b,w)=>{u=b,l=w});i.onmessage=({data:b})=>{i.onmessage=({data:w})=>s(w),b?l(b):u()},i.postMessage(t||new URL(e,location.href).toString());let{readFromStdout:s,service:d}=je({writeToStdin(b){i.postMessage(b)},isSync:!1,hasFS:!1,esbuild:ve});await m,be=()=>{i.terminate(),se=void 0,be=void 0,we=void 0},we={build:b=>new Promise((w,M)=>d.buildOrContext({callName:"build",refs:null,options:b,isTTY:!1,defaultWD:"/",callback:(L,B)=>L?M(L):w(B)})),context:b=>new Promise((w,M)=>d.buildOrContext({callName:"context",refs:null,options:b,isTTY:!1,defaultWD:"/",callback:(L,B)=>L?M(L):w(B)})),transform:(b,w)=>new Promise((M,L)=>d.transform({callName:"transform",refs:null,input:b,options:w||{},isTTY:!1,fs:{readFile(B,q){q(new Error("Internal error"),null)},writeFile(B,q){q(null)}},callback:(B,q)=>B?L(B):M(q)})),formatMessages:(b,w)=>new Promise((M,L)=>d.formatMessages({callName:"formatMessages",refs:null,messages:b,options:w,callback:(B,q)=>B?L(B):M(q)})),analyzeMetafile:(b,w)=>new Promise((M,L)=>d.analyzeMetafile({callName:"analyzeMetafile",refs:null,metafile:typeof b=="string"?b:JSON.stringify(b),options:w,callback:(B,q)=>B?L(B):M(q)}))}},yt=ve;export{at as analyzeMetafile,dt as analyzeMetafileSync,it as build,ut as buildSync,lt as context,yt as default,ot as formatMessages,ct as formatMessagesSync,gt as initialize,pt as stop,st as transform,ft as transformSync,rt as version};
