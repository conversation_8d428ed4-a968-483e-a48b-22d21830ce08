import{f as Ce}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{B as <PERSON>}from"./BlockTitle-7f7c9ef8.js";import{C as Ee}from"./Check-965babbe.js";import{C as ze}from"./Copy-b365948f.js";import"./Index-26cfc80a.js";const{SvelteComponent:De,action_destroyer:Ne,add_render_callback:Be,append:Ke,attr:a,binding_callbacks:L,bubble:C,check_outros:G,create_component:I,create_in_transition:Le,destroy_component:J,detach:g,element:E,empty:x,group_outros:M,init:Se,insert:k,is_function:Ue,listen:b,mount_component:O,noop:S,run_all:U,safe_not_equal:qe,set_data:Ye,set_input_value:H,space:$,text:je,toggle_class:W,transition_in:y,transition_out:v}=window.__gradio__svelte__internal,{beforeUpdate:Ae,afterUpdate:Fe,createEventDispatcher:Ge,tick:X}=window.__gradio__svelte__internal;function Ie(t){let e;return{c(){e=je(t[3])},m(l,u){k(l,e,u)},p(l,u){u[0]&8&&Ye(e,l[3])},d(l){l&&g(e)}}}function Je(t){let e,l,u,i,o,s,d,_,r=t[6]&&t[10]&&Z(t);return{c(){r&&r.c(),e=$(),l=E("textarea"),a(l,"data-testid","textbox"),a(l,"class","scroll-hide svelte-1f354aw"),a(l,"dir",u=t[11]?"rtl":"ltr"),a(l,"placeholder",t[2]),a(l,"rows",t[1]),l.disabled=t[5],l.autofocus=t[12],a(l,"style",i=t[13]?"text-align: "+t[13]:"")},m(f,c){r&&r.m(f,c),k(f,e,c),k(f,l,c),H(l,t[0]),t[38](l),s=!0,t[12]&&l.focus(),d||(_=[Ne(o=t[20].call(null,l,t[0])),b(l,"input",t[37]),b(l,"keypress",t[18]),b(l,"blur",t[29]),b(l,"select",t[17]),b(l,"focus",t[30]),b(l,"scroll",t[19])],d=!0)},p(f,c){f[6]&&f[10]?r?(r.p(f,c),c[0]&1088&&y(r,1)):(r=Z(f),r.c(),y(r,1),r.m(e.parentNode,e)):r&&(M(),v(r,1,1,()=>{r=null}),G()),(!s||c[0]&2048&&u!==(u=f[11]?"rtl":"ltr"))&&a(l,"dir",u),(!s||c[0]&4)&&a(l,"placeholder",f[2]),(!s||c[0]&2)&&a(l,"rows",f[1]),(!s||c[0]&32)&&(l.disabled=f[5]),(!s||c[0]&4096)&&(l.autofocus=f[12]),(!s||c[0]&8192&&i!==(i=f[13]?"text-align: "+f[13]:""))&&a(l,"style",i),o&&Ue(o.update)&&c[0]&1&&o.update.call(null,f[0]),c[0]&1&&H(l,f[0])},i(f){s||(y(r),s=!0)},o(f){v(r),s=!1},d(f){f&&(g(e),g(l)),r&&r.d(f),t[38](null),d=!1,U(_)}}}function Me(t){let e;function l(o,s){if(o[9]==="text")return Ve;if(o[9]==="password")return Re;if(o[9]==="email")return Qe}let u=l(t),i=u&&u(t);return{c(){i&&i.c(),e=x()},m(o,s){i&&i.m(o,s),k(o,e,s)},p(o,s){u===(u=l(o))&&i?i.p(o,s):(i&&i.d(1),i=u&&u(o),i&&(i.c(),i.m(e.parentNode,e)))},i:S,o:S,d(o){o&&g(e),i&&i.d(o)}}}function Z(t){let e,l,u,i;const o=[Pe,Oe],s=[];function d(_,r){return _[15]?0:1}return e=d(t),l=s[e]=o[e](t),{c(){l.c(),u=x()},m(_,r){s[e].m(_,r),k(_,u,r),i=!0},p(_,r){let f=e;e=d(_),e===f?s[e].p(_,r):(M(),v(s[f],1,1,()=>{s[f]=null}),G(),l=s[e],l?l.p(_,r):(l=s[e]=o[e](_),l.c()),y(l,1),l.m(u.parentNode,u))},i(_){i||(y(l),i=!0)},o(_){v(l),i=!1},d(_){_&&g(u),s[e].d(_)}}}function Oe(t){let e,l,u,i,o;return l=new ze({}),{c(){e=E("button"),I(l.$$.fragment),a(e,"aria-label","Copy"),a(e,"aria-roledescription","Copy text"),a(e,"class","svelte-1f354aw")},m(s,d){k(s,e,d),O(l,e,null),u=!0,i||(o=b(e,"click",t[16]),i=!0)},p:S,i(s){u||(y(l.$$.fragment,s),u=!0)},o(s){v(l.$$.fragment,s),u=!1},d(s){s&&g(e),J(l),i=!1,o()}}}function Pe(t){let e,l,u,i;return l=new Ee({}),{c(){e=E("button"),I(l.$$.fragment),a(e,"aria-label","Copied"),a(e,"aria-roledescription","Text copied"),a(e,"class","svelte-1f354aw")},m(o,s){k(o,e,s),O(l,e,null),i=!0},p:S,i(o){i||(y(l.$$.fragment,o),o&&(u||Be(()=>{u=Le(e,Ce,{duration:300}),u.start()})),i=!0)},o(o){v(l.$$.fragment,o),i=!1},d(o){o&&g(e),J(l)}}}function Qe(t){let e,l,u;return{c(){e=E("input"),a(e,"data-testid","textbox"),a(e,"type","email"),a(e,"class","scroll-hide svelte-1f354aw"),a(e,"placeholder",t[2]),e.disabled=t[5],e.autofocus=t[12],a(e,"autocomplete","email")},m(i,o){k(i,e,o),H(e,t[0]),t[36](e),t[12]&&e.focus(),l||(u=[b(e,"input",t[35]),b(e,"keypress",t[18]),b(e,"blur",t[27]),b(e,"select",t[17]),b(e,"focus",t[28])],l=!0)},p(i,o){o[0]&4&&a(e,"placeholder",i[2]),o[0]&32&&(e.disabled=i[5]),o[0]&4096&&(e.autofocus=i[12]),o[0]&1&&e.value!==i[0]&&H(e,i[0])},d(i){i&&g(e),t[36](null),l=!1,U(u)}}}function Re(t){let e,l,u;return{c(){e=E("input"),a(e,"data-testid","password"),a(e,"type","password"),a(e,"class","scroll-hide svelte-1f354aw"),a(e,"placeholder",t[2]),e.disabled=t[5],e.autofocus=t[12],a(e,"autocomplete","")},m(i,o){k(i,e,o),H(e,t[0]),t[34](e),t[12]&&e.focus(),l||(u=[b(e,"input",t[33]),b(e,"keypress",t[18]),b(e,"blur",t[25]),b(e,"select",t[17]),b(e,"focus",t[26])],l=!0)},p(i,o){o[0]&4&&a(e,"placeholder",i[2]),o[0]&32&&(e.disabled=i[5]),o[0]&4096&&(e.autofocus=i[12]),o[0]&1&&e.value!==i[0]&&H(e,i[0])},d(i){i&&g(e),t[34](null),l=!1,U(u)}}}function Ve(t){let e,l,u,i,o;return{c(){e=E("input"),a(e,"data-testid","textbox"),a(e,"type","text"),a(e,"class","scroll-hide svelte-1f354aw"),a(e,"dir",l=t[11]?"rtl":"ltr"),a(e,"placeholder",t[2]),e.disabled=t[5],e.autofocus=t[12],a(e,"style",u=t[13]?"text-align: "+t[13]:"")},m(s,d){k(s,e,d),H(e,t[0]),t[32](e),t[12]&&e.focus(),i||(o=[b(e,"input",t[31]),b(e,"keypress",t[18]),b(e,"blur",t[23]),b(e,"select",t[17]),b(e,"focus",t[24])],i=!0)},p(s,d){d[0]&2048&&l!==(l=s[11]?"rtl":"ltr")&&a(e,"dir",l),d[0]&4&&a(e,"placeholder",s[2]),d[0]&32&&(e.disabled=s[5]),d[0]&4096&&(e.autofocus=s[12]),d[0]&8192&&u!==(u=s[13]?"text-align: "+s[13]:"")&&a(e,"style",u),d[0]&1&&e.value!==s[0]&&H(e,s[0])},d(s){s&&g(e),t[32](null),i=!1,U(o)}}}function We(t){let e,l,u,i,o,s;l=new He({props:{show_label:t[6],info:t[4],$$slots:{default:[Ie]},$$scope:{ctx:t}}});const d=[Me,Je],_=[];function r(f,c){return f[1]===1&&f[8]===1?0:1}return i=r(t),o=_[i]=d[i](t),{c(){e=E("label"),I(l.$$.fragment),u=$(),o.c(),a(e,"class","svelte-1f354aw"),W(e,"container",t[7])},m(f,c){k(f,e,c),O(l,e,null),Ke(e,u),_[i].m(e,null),s=!0},p(f,c){const m={};c[0]&64&&(m.show_label=f[6]),c[0]&16&&(m.info=f[4]),c[0]&8|c[1]&131072&&(m.$$scope={dirty:c,ctx:f}),l.$set(m);let z=i;i=r(f),i===z?_[i].p(f,c):(M(),v(_[z],1,1,()=>{_[z]=null}),G(),o=_[i],o?o.p(f,c):(o=_[i]=d[i](f),o.c()),y(o,1),o.m(e,null)),(!s||c[0]&128)&&W(e,"container",f[7])},i(f){s||(y(l.$$.fragment,f),y(o),s=!0)},o(f){v(l.$$.fragment,f),v(o),s=!1},d(f){f&&g(e),J(l),_[i].d()}}}function Xe(t,e,l){let{value:u=""}=e,{value_is_output:i=!1}=e,{lines:o=1}=e,{placeholder:s="Type here..."}=e,{label:d}=e,{info:_=void 0}=e,{disabled:r=!1}=e,{show_label:f=!0}=e,{container:c=!0}=e,{max_lines:m}=e,{type:z="text"}=e,{show_copy_button:P=!1}=e,{rtl:Q=!1}=e,{autofocus:q=!1}=e,{text_align:R=void 0}=e,{autoscroll:B=!0}=e,h,Y=!1,j,A,V=0,F=!1;const D=Ge();Ae(()=>{A=h&&h.offsetHeight+h.scrollTop>h.scrollHeight-100});const ee=()=>{A&&B&&!F&&h.scrollTo(0,h.scrollHeight)};function le(){D("change",u),i||D("input")}Fe(()=>{q&&h.focus(),A&&B&&ee(),l(21,i=!1)});async function te(){"clipboard"in navigator&&(await navigator.clipboard.writeText(u),ne())}function ne(){l(15,Y=!0),j&&clearTimeout(j),j=setTimeout(()=>{l(15,Y=!1)},1e3)}function ie(n){const p=n.target,T=p.value,w=[p.selectionStart,p.selectionEnd];D("select",{value:T.substring(...w),index:w})}async function oe(n){await X(),(n.key==="Enter"&&n.shiftKey&&o>1||n.key==="Enter"&&!n.shiftKey&&o===1&&m>=1)&&(n.preventDefault(),D("submit"))}function ue(n){const p=n.target,T=p.scrollTop;T<V&&(F=!0),V=T;const w=p.scrollHeight-p.clientHeight;T>=w&&(F=!1)}async function K(n){if(await X(),o===m)return;let p=m===void 0?!1:m===void 0?21*11:21*(m+1),T=21*(o+1);const w=n.target;w.style.height="1px";let N;p&&w.scrollHeight>p?N=p:w.scrollHeight<T?N=T:N=w.scrollHeight,w.style.height=`${N}px`}function se(n,p){if(o!==m&&(n.style.overflowY="scroll",n.addEventListener("input",K),!!p.trim()))return K({target:n}),{destroy:()=>n.removeEventListener("input",K)}}function fe(n){C.call(this,t,n)}function ae(n){C.call(this,t,n)}function re(n){C.call(this,t,n)}function _e(n){C.call(this,t,n)}function ce(n){C.call(this,t,n)}function de(n){C.call(this,t,n)}function be(n){C.call(this,t,n)}function he(n){C.call(this,t,n)}function me(){u=this.value,l(0,u)}function pe(n){L[n?"unshift":"push"](()=>{h=n,l(14,h)})}function ge(){u=this.value,l(0,u)}function ke(n){L[n?"unshift":"push"](()=>{h=n,l(14,h)})}function we(){u=this.value,l(0,u)}function ye(n){L[n?"unshift":"push"](()=>{h=n,l(14,h)})}function ve(){u=this.value,l(0,u)}function Te(n){L[n?"unshift":"push"](()=>{h=n,l(14,h)})}return t.$$set=n=>{"value"in n&&l(0,u=n.value),"value_is_output"in n&&l(21,i=n.value_is_output),"lines"in n&&l(1,o=n.lines),"placeholder"in n&&l(2,s=n.placeholder),"label"in n&&l(3,d=n.label),"info"in n&&l(4,_=n.info),"disabled"in n&&l(5,r=n.disabled),"show_label"in n&&l(6,f=n.show_label),"container"in n&&l(7,c=n.container),"max_lines"in n&&l(8,m=n.max_lines),"type"in n&&l(9,z=n.type),"show_copy_button"in n&&l(10,P=n.show_copy_button),"rtl"in n&&l(11,Q=n.rtl),"autofocus"in n&&l(12,q=n.autofocus),"text_align"in n&&l(13,R=n.text_align),"autoscroll"in n&&l(22,B=n.autoscroll)},t.$$.update=()=>{t.$$.dirty[0]&1&&u===null&&l(0,u=""),t.$$.dirty[0]&16643&&h&&o!==m&&K({target:h}),t.$$.dirty[0]&1&&le()},[u,o,s,d,_,r,f,c,m,z,P,Q,q,R,h,Y,te,ie,oe,ue,se,i,B,fe,ae,re,_e,ce,de,be,he,me,pe,ge,ke,we,ye,ve,Te]}class tl extends De{constructor(e){super(),Se(this,e,Xe,We,qe,{value:0,value_is_output:21,lines:1,placeholder:2,label:3,info:4,disabled:5,show_label:6,container:7,max_lines:8,type:9,show_copy_button:10,rtl:11,autofocus:12,text_align:13,autoscroll:22},null,[-1,-1])}}export{tl as T};
//# sourceMappingURL=Textbox-1709c01b.js.map
