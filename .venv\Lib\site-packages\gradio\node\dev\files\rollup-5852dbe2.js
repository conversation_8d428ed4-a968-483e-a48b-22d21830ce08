export { version$1 as VERSION, rollup, watch } from './index-39ce8d1a.js';
import 'node:path';
import 'path';
import 'node:process';
import 'node:perf_hooks';
import 'node:crypto';
import 'node:fs/promises';
import 'tty';
import 'node:child_process';
import 'net';
import 'fs';
import 'node:fs';
import 'node:url';
import 'node:util';
import 'node:module';
import 'esbuild-wasm';
import 'events';
import 'assert';
import 'util';
import 'url';
import 'http';
import 'stream';
import 'os';
import 'child_process';
import 'node:os';
import 'node:dns';
import 'crypto';
import 'node:buffer';
import 'module';
import 'node:assert';
import 'node:v8';
import 'worker_threads';
import 'zlib';
import 'buffer';
import 'https';
import 'tls';
import 'node:http';
import 'node:https';
import 'querystring';
import 'node:readline';
import 'node:zlib';
import '../compiler.js';
