import{B as G}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{I as H}from"./Info-84f5385d.js";import{S as J}from"./Index-26cfc80a.js";import"./index-a80d931b.js";import"./svelte/svelte.js";const{SvelteComponent:K,append:w,attr:k,detach:L,element:q,init:M,insert:O,listen:z,noop:E,run_all:P,safe_not_equal:Q,set_data:R,space:V,text:W,toggle_class:j}=window.__gradio__svelte__internal,{createEventDispatcher:X}=window.__gradio__svelte__internal;function Y(i){let e,t,n,l,_,o,d;return{c(){e=q("label"),t=q("input"),n=V(),l=q("span"),_=W(i[1]),t.disabled=i[2],k(t,"type","checkbox"),k(t,"name","test"),k(t,"data-testid","checkbox"),k(t,"class","svelte-3pzdsv"),k(l,"class","ml-2 svelte-3pzdsv"),k(e,"class","svelte-3pzdsv"),j(e,"disabled",i[2])},m(r,s){O(r,e,s),w(e,t),t.checked=i[0],w(e,n),w(e,l),w(l,_),o||(d=[z(t,"change",i[6]),z(t,"keydown",i[3]),z(t,"input",i[4])],o=!0)},p(r,[s]){s&4&&(t.disabled=r[2]),s&1&&(t.checked=r[0]),s&2&&R(_,r[1]),s&4&&j(e,"disabled",r[2])},i:E,o:E,d(r){r&&L(e),o=!1,P(d)}}}function Z(i,e,t){let n,{value:l=!1}=e,{label:_="Checkbox"}=e,{interactive:o}=e;const d=X();async function r(u){u.key==="Enter"&&(t(0,l=!l),d("select",{index:0,value:u.currentTarget.checked,selected:u.currentTarget.checked}))}async function s(u){t(0,l=u.currentTarget.checked),d("select",{index:0,value:u.currentTarget.checked,selected:u.currentTarget.checked})}function g(){l=this.checked,t(0,l)}return i.$$set=u=>{"value"in u&&t(0,l=u.value),"label"in u&&t(1,_=u.label),"interactive"in u&&t(5,o=u.interactive)},i.$$.update=()=>{i.$$.dirty&1&&d("change",l),i.$$.dirty&32&&t(2,n=!o)},[l,_,n,r,s,o,g]}class y extends K{constructor(e){super(),M(this,e,Z,Y,Q,{value:0,label:1,interactive:5})}}const p=y,{SvelteComponent:x,add_flush_callback:$,assign:ee,bind:te,binding_callbacks:ne,check_outros:le,create_component:C,destroy_component:S,detach:B,get_spread_object:ie,get_spread_update:ae,group_outros:se,init:ce,insert:I,mount_component:T,safe_not_equal:_e,set_data:ue,space:D,text:fe,transition_in:h,transition_out:v}=window.__gradio__svelte__internal,{afterUpdate:oe}=window.__gradio__svelte__internal;function N(i){let e,t;return e=new H({props:{$$slots:{default:[re]},$$scope:{ctx:i}}}),{c(){C(e.$$.fragment)},m(n,l){T(e,n,l),t=!0},p(n,l){const _={};l&65568&&(_.$$scope={dirty:l,ctx:n}),e.$set(_)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){v(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function re(i){let e;return{c(){e=fe(i[5])},m(t,n){I(t,e,n)},p(t,n){n&32&&ue(e,t[5])},d(t){t&&B(e)}}}function de(i){let e,t,n,l,_,o;const d=[{autoscroll:i[10].autoscroll},{i18n:i[10].i18n},i[9]];let r={};for(let a=0;a<d.length;a+=1)r=ee(r,d[a]);e=new J({props:r});let s=i[5]&&N(i);function g(a){i[14](a)}let u={label:i[4],interactive:i[11]};return i[0]!==void 0&&(u.value=i[0]),l=new p({props:u}),ne.push(()=>te(l,"value",g)),l.$on("change",i[12]),l.$on("select",i[15]),{c(){C(e.$$.fragment),t=D(),s&&s.c(),n=D(),C(l.$$.fragment)},m(a,f){T(e,a,f),I(a,t,f),s&&s.m(a,f),I(a,n,f),T(l,a,f),o=!0},p(a,f){const m=f&1536?ae(d,[f&1024&&{autoscroll:a[10].autoscroll},f&1024&&{i18n:a[10].i18n},f&512&&ie(a[9])]):{};e.$set(m),a[5]?s?(s.p(a,f),f&32&&h(s,1)):(s=N(a),s.c(),h(s,1),s.m(n.parentNode,n)):s&&(se(),v(s,1,1,()=>{s=null}),le());const b={};f&16&&(b.label=a[4]),f&2048&&(b.interactive=a[11]),!_&&f&1&&(_=!0,b.value=a[0],$(()=>_=!1)),l.$set(b)},i(a){o||(h(e.$$.fragment,a),h(s),h(l.$$.fragment,a),o=!0)},o(a){v(e.$$.fragment,a),v(s),v(l.$$.fragment,a),o=!1},d(a){a&&(B(t),B(n)),S(e,a),s&&s.d(a),S(l,a)}}}function me(i){let e,t;return e=new G({props:{visible:i[3],elem_id:i[1],elem_classes:i[2],container:i[6],scale:i[7],min_width:i[8],$$slots:{default:[de]},$$scope:{ctx:i}}}),{c(){C(e.$$.fragment)},m(n,l){T(e,n,l),t=!0},p(n,[l]){const _={};l&8&&(_.visible=n[3]),l&2&&(_.elem_id=n[1]),l&4&&(_.elem_classes=n[2]),l&64&&(_.container=n[6]),l&128&&(_.scale=n[7]),l&256&&(_.min_width=n[8]),l&69169&&(_.$$scope={dirty:l,ctx:n}),e.$set(_)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){v(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function be(i,e,t){let{elem_id:n=""}=e,{elem_classes:l=[]}=e,{visible:_=!0}=e,{value:o=!1}=e,{value_is_output:d=!1}=e,{label:r="Checkbox"}=e,{info:s=void 0}=e,{container:g=!0}=e,{scale:u=null}=e,{min_width:a=void 0}=e,{loading_status:f}=e,{gradio:m}=e,{interactive:b}=e;function U(){m.dispatch("change"),d||m.dispatch("input")}oe(()=>{t(13,d=!1)});function A(c){o=c,t(0,o)}const F=c=>m.dispatch("select",c.detail);return i.$$set=c=>{"elem_id"in c&&t(1,n=c.elem_id),"elem_classes"in c&&t(2,l=c.elem_classes),"visible"in c&&t(3,_=c.visible),"value"in c&&t(0,o=c.value),"value_is_output"in c&&t(13,d=c.value_is_output),"label"in c&&t(4,r=c.label),"info"in c&&t(5,s=c.info),"container"in c&&t(6,g=c.container),"scale"in c&&t(7,u=c.scale),"min_width"in c&&t(8,a=c.min_width),"loading_status"in c&&t(9,f=c.loading_status),"gradio"in c&&t(10,m=c.gradio),"interactive"in c&&t(11,b=c.interactive)},[o,n,l,_,r,s,g,u,a,f,m,b,U,d,A,F]}class Ce extends x{constructor(e){super(),ce(this,e,be,me,_e,{elem_id:1,elem_classes:2,visible:3,value:0,value_is_output:13,label:4,info:5,container:6,scale:7,min_width:8,loading_status:9,gradio:10,interactive:11})}}export{p as BaseCheckbox,Ce as default};
//# sourceMappingURL=Index-0bdb78da.js.map
