import{B as zl}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{u as Be,c as Sl}from"./utils-572af92b.js";import{d as ql}from"./index-2f00b72c.js";import{S as Bl}from"./ShareButton-7dae44e7.js";import{S as Hl}from"./Index-26cfc80a.js";import{r as Ml}from"./file-url-bef2dc1b.js";import{I as hl}from"./Image-21c02477.js";import{V as Ll}from"./Video-fd4d29ec.js";import"./Index.svelte_svelte_type_style_lang-e1d4a36d.js";import{M as gl}from"./Example.svelte_svelte_type_style_lang-648fc18a.js";import"./index-a80d931b.js";import{C as jl}from"./Check-965babbe.js";import{C as Pl}from"./Copy-b365948f.js";import{B as Tl}from"./BlockLabel-f27805b1.js";import"./IconButton-7294c90b.js";import"./prism-python-b0b31d02.js";import"./svelte/svelte.js";const{SvelteComponent:Dl,append:He,attr:j,detach:El,init:Vl,insert:Al,noop:ge,safe_not_equal:Nl,svg_element:ke}=window.__gradio__svelte__internal;function Il(l){let e,i,t;return{c(){e=ke("svg"),i=ke("path"),t=ke("path"),j(i,"fill","currentColor"),j(i,"d","M17.74 30L16 29l4-7h6a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h9v2H6a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4h20a4 4 0 0 1 4 4v12a4 4 0 0 1-4 4h-4.84Z"),j(t,"fill","currentColor"),j(t,"d","M8 10h16v2H8zm0 6h10v2H8z"),j(e,"xmlns","http://www.w3.org/2000/svg"),j(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),j(e,"aria-hidden","true"),j(e,"role","img"),j(e,"class","iconify iconify--carbon"),j(e,"width","100%"),j(e,"height","100%"),j(e,"preserveAspectRatio","xMidYMid meet"),j(e,"viewBox","0 0 32 32")},m(n,a){Al(n,e,a),He(e,i),He(e,t)},p:ge,i:ge,o:ge,d(n){n&&El(e)}}}class Ul extends Dl{constructor(e){super(),Vl(this,e,null,Il,Nl,{})}}const{SvelteComponent:Zl,append:Me,attr:H,detach:Fl,init:Ol,insert:Rl,noop:Le,safe_not_equal:Yl,svg_element:we}=window.__gradio__svelte__internal;function Gl(l){let e,i,t,n;return{c(){e=we("svg"),i=we("path"),t=we("path"),H(i,"stroke","currentColor"),H(i,"stroke-width","1.5"),H(i,"stroke-linecap","round"),H(i,"d","M16.472 3.5H4.1a.6.6 0 0 0-.6.6v9.8a.6.6 0 0 0 .6.6h2.768a2 2 0 0 1 1.715.971l2.71 4.517a1.631 1.631 0 0 0 2.961-1.308l-1.022-3.408a.6.6 0 0 1 .574-.772h4.575a2 2 0 0 0 1.93-2.526l-1.91-7A2 2 0 0 0 16.473 3.5Z"),H(t,"stroke","currentColor"),H(t,"stroke-width","1.5"),H(t,"stroke-linecap","round"),H(t,"stroke-linejoin","round"),H(t,"d","M7 14.5v-11"),H(e,"xmlns","http://www.w3.org/2000/svg"),H(e,"viewBox","0 0 24 24"),H(e,"fill",n=l[0]?"currentColor":"none"),H(e,"stroke-width","1.5"),H(e,"color","currentColor")},m(a,r){Rl(a,e,r),Me(e,i),Me(e,t)},p(a,[r]){r&1&&n!==(n=a[0]?"currentColor":"none")&&H(e,"fill",n)},i:Le,o:Le,d(a){a&&Fl(e)}}}function Jl(l,e,i){let{selected:t}=e;return l.$$set=n=>{"selected"in n&&i(0,t=n.selected)},[t]}class Kl extends Zl{constructor(e){super(),Ol(this,e,Jl,Gl,Yl,{selected:0})}}const{SvelteComponent:Ql,append:je,attr:M,detach:Wl,init:Xl,insert:$l,noop:Pe,safe_not_equal:xl,svg_element:pe}=window.__gradio__svelte__internal;function et(l){let e,i,t,n;return{c(){e=pe("svg"),i=pe("path"),t=pe("path"),M(i,"stroke","currentColor"),M(i,"stroke-width","1.5"),M(i,"stroke-linecap","round"),M(i,"d","M16.472 20H4.1a.6.6 0 0 1-.6-.6V9.6a.6.6 0 0 1 .6-.6h2.768a2 2 0 0 0 1.715-.971l2.71-4.517a1.631 1.631 0 0 1 2.961 1.308l-1.022 3.408a.6.6 0 0 0 .574.772h4.575a2 2 0 0 1 1.93 2.526l-1.91 7A2 2 0 0 1 16.473 20Z"),M(t,"stroke","currentColor"),M(t,"stroke-width","1.5"),M(t,"stroke-linecap","round"),M(t,"stroke-linejoin","round"),M(t,"d","M7 20V9"),M(e,"xmlns","http://www.w3.org/2000/svg"),M(e,"viewBox","0 0 24 24"),M(e,"fill",n=l[0]?"currentColor":"none"),M(e,"stroke-width","1.5"),M(e,"color","currentColor")},m(a,r){$l(a,e,r),je(e,i),je(e,t)},p(a,[r]){r&1&&n!==(n=a[0]?"currentColor":"none")&&M(e,"fill",n)},i:Pe,o:Pe,d(a){a&&Wl(e)}}}function lt(l,e,i){let{selected:t}=e;return l.$$set=n=>{"selected"in n&&i(0,t=n.selected)},[t]}class tt extends Ql{constructor(e){super(),Xl(this,e,lt,et,xl,{selected:0})}}const nt=async l=>(await Promise.all(l.map(async i=>await Promise.all(i.map(async(t,n)=>{if(t===null)return"";let a=n===0?"😃":"🤖",r="";if(typeof t=="string"){const s={audio:/<audio.*?src="(\/file=.*?)"/g,video:/<video.*?src="(\/file=.*?)"/g,image:/<img.*?src="(\/file=.*?)".*?\/>|!\[.*?\]\((\/file=.*?)\)/g};r=t;for(let[o,u]of Object.entries(s)){let f;for(;(f=u.exec(t))!==null;){const b=f[1]||f[2],p=await Be(b,"url");r=r.replace(b,p)}}}else{if(!t?.url)return"";const s=await Be(t.url,"url");t.mime_type?.includes("audio")?r=`<audio controls src="${s}"></audio>`:t.mime_type?.includes("video")?r=s:t.mime_type?.includes("image")&&(r=`<img src="${s}" />`)}return`${a}: ${r}`}))))).map(i=>i.join(i[0]!==""&&i[1]!==""?`
`:"")).join(`
`),{SvelteComponent:it,assign:ze,compute_rest_props:Te,detach:at,element:ot,exclude_internal_props:rt,get_spread_update:st,init:ut,insert:ft,listen:ve,noop:De,run_all:_t,safe_not_equal:ct,set_attributes:Ee,src_url_equal:dt}=window.__gradio__svelte__internal,{createEventDispatcher:mt}=window.__gradio__svelte__internal;function bt(l){let e,i,t,n,a=[{src:i=l[0]},l[2]],r={};for(let s=0;s<a.length;s+=1)r=ze(r,a[s]);return{c(){e=ot("audio"),Ee(e,r)},m(s,o){ft(s,e,o),t||(n=[ve(e,"play",l[1].bind(null,"play")),ve(e,"pause",l[1].bind(null,"pause")),ve(e,"ended",l[1].bind(null,"ended"))],t=!0)},p(s,[o]){Ee(e,r=st(a,[o&1&&!dt(e.src,i=s[0])&&{src:i},o&4&&s[2]]))},i:De,o:De,d(s){s&&at(e),t=!1,_t(n)}}}function ht(l,e,i){const t=["src"];let n=Te(e,t),{src:a=void 0}=e,r,s;const o=mt();return l.$$set=u=>{e=ze(ze({},e),rt(u)),i(2,n=Te(e,t)),"src"in u&&i(3,a=u.src)},l.$$.update=()=>{if(l.$$.dirty&24){i(0,r=a),i(4,s=a);const u=a;Ml(u).then(f=>{s===u&&i(0,r=f)})}},[r,o,n,a,s]}class gt extends it{constructor(e){super(),ut(this,e,ht,bt,ct,{src:3})}}const{SvelteComponent:kt,append:wt,attr:oe,check_outros:Ve,create_component:kl,destroy_component:wl,detach:pt,element:vt,group_outros:Ae,init:yt,insert:Ct,listen:zt,mount_component:pl,safe_not_equal:St,space:qt,transition_in:K,transition_out:$}=window.__gradio__svelte__internal,{onDestroy:Bt}=window.__gradio__svelte__internal;function Ne(l){let e,i;return e=new Pl({}),{c(){kl(e.$$.fragment)},m(t,n){pl(e,t,n),i=!0},i(t){i||(K(e.$$.fragment,t),i=!0)},o(t){$(e.$$.fragment,t),i=!1},d(t){wl(e,t)}}}function Ie(l){let e,i;return e=new jl({}),{c(){kl(e.$$.fragment)},m(t,n){pl(e,t,n),i=!0},i(t){i||(K(e.$$.fragment,t),i=!0)},o(t){$(e.$$.fragment,t),i=!1},d(t){wl(e,t)}}}function Ht(l){let e,i,t,n,a,r,s=!l[0]&&Ne(),o=l[0]&&Ie();return{c(){e=vt("button"),s&&s.c(),i=qt(),o&&o.c(),oe(e,"class","action svelte-rvlubk"),oe(e,"title","copy"),oe(e,"aria-label",t=l[0]?"Copied message":"Copy message")},m(u,f){Ct(u,e,f),s&&s.m(e,null),wt(e,i),o&&o.m(e,null),n=!0,a||(r=zt(e,"click",l[1]),a=!0)},p(u,[f]){u[0]?s&&(Ae(),$(s,1,1,()=>{s=null}),Ve()):s?f&1&&K(s,1):(s=Ne(),s.c(),K(s,1),s.m(e,i)),u[0]?o?f&1&&K(o,1):(o=Ie(),o.c(),K(o,1),o.m(e,null)):o&&(Ae(),$(o,1,1,()=>{o=null}),Ve()),(!n||f&1&&t!==(t=u[0]?"Copied message":"Copy message"))&&oe(e,"aria-label",t)},i(u){n||(K(s),K(o),n=!0)},o(u){$(s),$(o),n=!1},d(u){u&&pt(e),s&&s.d(),o&&o.d(),a=!1,r()}}}function Mt(l,e,i){let t=!1,{value:n}=e,a;function r(){i(0,t=!0),a&&clearTimeout(a),a=setTimeout(()=>{i(0,t=!1)},2e3)}async function s(){if("clipboard"in navigator)await navigator.clipboard.writeText(n),r();else{const o=document.createElement("textarea");o.value=n,o.style.position="absolute",o.style.left="-999999px",document.body.prepend(o),o.select();try{document.execCommand("copy"),r()}catch(u){console.error(u)}finally{o.remove()}}}return Bt(()=>{a&&clearTimeout(a)}),l.$$set=o=>{"value"in o&&i(2,n=o.value)},[t,s,n]}class Lt extends kt{constructor(e){super(),yt(this,e,Mt,Ht,St,{value:2})}}const{SvelteComponent:jt,attr:X,create_component:Ue,destroy_component:Ze,detach:ye,element:Fe,init:Pt,insert:Ce,listen:Oe,mount_component:Re,run_all:Tt,safe_not_equal:Dt,space:Et,transition_in:Ye,transition_out:Ge}=window.__gradio__svelte__internal;function Vt(l){let e,i,t,n,a,r,s,o,u,f;return i=new tt({props:{selected:l[1]==="like"}}),r=new Kl({props:{selected:l[1]==="dislike"}}),{c(){e=Fe("button"),Ue(i.$$.fragment),n=Et(),a=Fe("button"),Ue(r.$$.fragment),X(e,"aria-label",t=l[1]==="like"?"clicked like":"like"),X(e,"class","svelte-3snf3m"),X(a,"aria-label",s=l[1]==="dislike"?"clicked dislike":"dislike"),X(a,"class","svelte-3snf3m")},m(b,p){Ce(b,e,p),Re(i,e,null),Ce(b,n,p),Ce(b,a,p),Re(r,a,null),o=!0,u||(f=[Oe(e,"click",l[2]),Oe(a,"click",l[3])],u=!0)},p(b,[p]){const S={};p&2&&(S.selected=b[1]==="like"),i.$set(S),(!o||p&2&&t!==(t=b[1]==="like"?"clicked like":"like"))&&X(e,"aria-label",t);const d={};p&2&&(d.selected=b[1]==="dislike"),r.$set(d),(!o||p&2&&s!==(s=b[1]==="dislike"?"clicked dislike":"dislike"))&&X(a,"aria-label",s)},i(b){o||(Ye(i.$$.fragment,b),Ye(r.$$.fragment,b),o=!0)},o(b){Ge(i.$$.fragment,b),Ge(r.$$.fragment,b),o=!1},d(b){b&&(ye(e),ye(n),ye(a)),Ze(i),Ze(r),u=!1,Tt(f)}}}function At(l,e,i){let{handle_action:t}=e,n=null;const a=()=>{i(1,n="like"),t(n)},r=()=>{i(1,n="dislike"),t(n)};return l.$$set=s=>{"handle_action"in s&&i(0,t=s.handle_action)},[t,n,a,r]}class Nt extends jt{constructor(e){super(),Pt(this,e,At,Vt,Dt,{handle_action:0})}}const{SvelteComponent:It,attr:re,detach:Ut,element:Zt,init:Ft,insert:Ot,noop:Je,safe_not_equal:Rt,set_style:Ke}=window.__gradio__svelte__internal;function Yt(l){let e;return{c(){e=Zt("div"),e.innerHTML=`<span class="sr-only">Loading content</span> <div class="dot-flashing svelte-1gpwetz"></div>
	 
	<div class="dot-flashing svelte-1gpwetz"></div>
	 
	<div class="dot-flashing svelte-1gpwetz"></div>`,re(e,"class","message pending svelte-1gpwetz"),re(e,"role","status"),re(e,"aria-label","Loading response"),re(e,"aria-live","polite"),Ke(e,"border-radius",l[0]==="bubble"?"var(--radius-xxl)":"none")},m(i,t){Ot(i,e,t)},p(i,[t]){t&1&&Ke(e,"border-radius",i[0]==="bubble"?"var(--radius-xxl)":"none")},i:Je,o:Je,d(i){i&&Ut(e)}}}function Gt(l,e,i){let{layout:t="bubble"}=e;return l.$$set=n=>{"layout"in n&&i(0,t=n.layout)},[t]}class Jt extends It{constructor(e){super(),Ft(this,e,Gt,Yt,Rt,{layout:0})}}const{SvelteComponent:Kt,action_destroyer:Qt,append:W,attr:w,binding_callbacks:Wt,bubble:J,check_outros:V,create_component:U,destroy_component:Z,destroy_each:vl,detach:P,element:A,empty:qe,ensure_array_like:se,group_outros:N,init:Xt,insert:T,listen:Qe,mount_component:F,noop:Se,null_to_empty:We,run_all:$t,safe_not_equal:xt,set_data:en,set_style:le,space:te,text:ln,toggle_class:z,transition_in:m,transition_out:g}=window.__gradio__svelte__internal,{beforeUpdate:tn,afterUpdate:nn,createEventDispatcher:an}=window.__gradio__svelte__internal;function Xe(l,e,i){const t=l.slice();return t[36]=e[i],t[38]=i,t}function $e(l,e,i){const t=l.slice();return t[39]=e[i],t[41]=i,t}function xe(l){let e,i,t;return i=new Bl({props:{i18n:l[13],formatter:nt,value:l[0]}}),i.$on("error",l[22]),i.$on("share",l[23]),{c(){e=A("div"),U(i.$$.fragment),w(e,"class","share-button svelte-1ylopk1")},m(n,a){T(n,e,a),F(i,e,null),t=!0},p(n,a){const r={};a[0]&8192&&(r.i18n=n[13]),a[0]&1&&(r.value=n[0]),i.$set(r)},i(n){t||(m(i.$$.fragment,n),t=!0)},o(n){g(i.$$.fragment,n),t=!1},d(n){n&&P(e),Z(i)}}}function on(l){let e,i,t;return i=new gl({props:{message:l[15],latex_delimiters:l[1]}}),{c(){e=A("center"),U(i.$$.fragment),w(e,"class","svelte-1ylopk1")},m(n,a){T(n,e,a),F(i,e,null),t=!0},p(n,a){const r={};a[0]&32768&&(r.message=n[15]),a[0]&2&&(r.latex_delimiters=n[1]),i.$set(r)},i(n){t||(m(i.$$.fragment,n),t=!0)},o(n){g(i.$$.fragment,n),t=!1},d(n){n&&P(e),Z(i)}}}function rn(l){let e,i,t,n=se(l[0]),a=[];for(let o=0;o<n.length;o+=1)a[o]=ol(Xe(l,n,o));const r=o=>g(a[o],1,1,()=>{a[o]=null});let s=l[2]&&rl(l);return{c(){for(let o=0;o<a.length;o+=1)a[o].c();e=te(),s&&s.c(),i=qe()},m(o,u){for(let f=0;f<a.length;f+=1)a[f]&&a[f].m(o,u);T(o,e,u),s&&s.m(o,u),T(o,i,u),t=!0},p(o,u){if(u[0]&942043){n=se(o[0]);let f;for(f=0;f<n.length;f+=1){const b=Xe(o,n,f);a[f]?(a[f].p(b,u),m(a[f],1)):(a[f]=ol(b),a[f].c(),m(a[f],1),a[f].m(e.parentNode,e))}for(N(),f=n.length;f<a.length;f+=1)r(f);V()}o[2]?s?(s.p(o,u),u[0]&4&&m(s,1)):(s=rl(o),s.c(),m(s,1),s.m(i.parentNode,i)):s&&(N(),g(s,1,1,()=>{s=null}),V())},i(o){if(!t){for(let u=0;u<n.length;u+=1)m(a[u]);m(s),t=!0}},o(o){a=a.filter(Boolean);for(let u=0;u<a.length;u+=1)g(a[u]);g(s),t=!1},d(o){o&&(P(e),P(i)),vl(a,o),s&&s.d(o)}}}function el(l){let e,i,t,n,a,r,s,o,u,f,b,p,S,d,C,D,y=l[8][l[41]]!==null&&ll(l);const R=[cn,_n,fn,un,sn],L=[];function Y(v,h){return h[0]&1&&(a=null),h[0]&1&&(r=null),h[0]&1&&(s=null),typeof v[39]=="string"?0:(a==null&&(a=!!(v[39]!==null&&v[39].file?.mime_type?.includes("audio"))),a?1:(r==null&&(r=!!(v[39]!==null&&v[39].file?.mime_type?.includes("video"))),r?2:(s==null&&(s=!!(v[39]!==null&&v[39].file?.mime_type?.includes("image"))),s?3:v[39]!==null&&v[39].file?.url!==null?4:-1)))}~(o=Y(l,[-1,-1]))&&(u=L[o]=R[o](l));function q(){return l[30](l[38],l[41],l[39])}function G(...v){return l[31](l[38],l[41],l[39],...v)}let k=(l[4]&&l[41]!==0||l[7]&&l[39]&&typeof l[39]=="string")&&tl(l);return{c(){e=A("div"),y&&y.c(),i=te(),t=A("div"),n=A("button"),u&&u.c(),p=te(),k&&k.c(),w(n,"data-testid",l[41]==0?"user":"bot"),w(n,"dir",f=l[6]?"rtl":"ltr"),w(n,"aria-label",b=(l[41]==0?"user":"bot")+"'s message: "+(typeof l[39]=="string"?l[39]:`a file of type ${l[39].file?.mime_type}, ${l[39].file?.alt_text??l[39].file?.orig_name??""}`)),w(n,"class","svelte-1ylopk1"),z(n,"latest",l[38]===l[0].length-1),z(n,"message-markdown-disabled",!l[11]),z(n,"selectable",l[3]),le(n,"user-select","text"),le(n,"text-align",l[6]?"right":"left"),w(t,"class","message "+(l[41]==0?"user":"bot")+" svelte-1ylopk1"),z(t,"message-fit",l[14]==="bubble"&&!l[10]),z(t,"panel-full-width",l[14]==="panel"),z(t,"message-bubble-border",l[14]==="bubble"),z(t,"message-markdown-disabled",!l[11]),le(t,"text-align",l[6]&&l[41]==0?"left":"right"),w(e,"class",S="message-row "+l[14]+" "+(l[41]==0?"user-row":"bot-row")+" svelte-1ylopk1")},m(v,h){T(v,e,h),y&&y.m(e,null),W(e,i),W(e,t),W(t,n),~o&&L[o].m(n,null),W(e,p),k&&k.m(e,null),d=!0,C||(D=[Qe(n,"click",q),Qe(n,"keydown",G)],C=!0)},p(v,h){l=v,l[8][l[41]]!==null?y?(y.p(l,h),h[0]&256&&m(y,1)):(y=ll(l),y.c(),m(y,1),y.m(e,i)):y&&(N(),g(y,1,1,()=>{y=null}),V());let O=o;o=Y(l,h),o===O?~o&&L[o].p(l,h):(u&&(N(),g(L[O],1,1,()=>{L[O]=null}),V()),~o?(u=L[o],u?u.p(l,h):(u=L[o]=R[o](l),u.c()),m(u,1),u.m(n,null)):u=null),(!d||h[0]&64&&f!==(f=l[6]?"rtl":"ltr"))&&w(n,"dir",f),(!d||h[0]&1&&b!==(b=(l[41]==0?"user":"bot")+"'s message: "+(typeof l[39]=="string"?l[39]:`a file of type ${l[39].file?.mime_type}, ${l[39].file?.alt_text??l[39].file?.orig_name??""}`)))&&w(n,"aria-label",b),(!d||h[0]&1)&&z(n,"latest",l[38]===l[0].length-1),(!d||h[0]&2048)&&z(n,"message-markdown-disabled",!l[11]),(!d||h[0]&8)&&z(n,"selectable",l[3]),h[0]&64&&le(n,"text-align",l[6]?"right":"left"),(!d||h[0]&17408)&&z(t,"message-fit",l[14]==="bubble"&&!l[10]),(!d||h[0]&16384)&&z(t,"panel-full-width",l[14]==="panel"),(!d||h[0]&16384)&&z(t,"message-bubble-border",l[14]==="bubble"),(!d||h[0]&2048)&&z(t,"message-markdown-disabled",!l[11]),h[0]&64&&le(t,"text-align",l[6]&&l[41]==0?"left":"right"),l[4]&&l[41]!==0||l[7]&&l[39]&&typeof l[39]=="string"?k?(k.p(l,h),h[0]&145&&m(k,1)):(k=tl(l),k.c(),m(k,1),k.m(e,null)):k&&(N(),g(k,1,1,()=>{k=null}),V()),(!d||h[0]&16384&&S!==(S="message-row "+l[14]+" "+(l[41]==0?"user-row":"bot-row")+" svelte-1ylopk1"))&&w(e,"class",S)},i(v){d||(m(y),m(u),m(k),d=!0)},o(v){g(y),g(u),g(k),d=!1},d(v){v&&P(e),y&&y.d(),~o&&L[o].d(),k&&k.d(),C=!1,$t(D)}}}function ll(l){let e,i,t;return i=new hl({props:{class:"avatar-image",src:l[8][l[41]]?.url,alt:(l[41]==0?"user":"bot")+" avatar"}}),{c(){e=A("div"),U(i.$$.fragment),w(e,"class","avatar-container svelte-1ylopk1")},m(n,a){T(n,e,a),F(i,e,null),t=!0},p(n,a){const r={};a[0]&256&&(r.src=n[8][n[41]]?.url),i.$set(r)},i(n){t||(m(i.$$.fragment,n),t=!0)},o(n){g(i.$$.fragment,n),t=!1},d(n){n&&P(e),Z(i)}}}function sn(l){let e,i=(l[39].file?.orig_name||l[39].file?.path)+"",t,n,a;return{c(){e=A("a"),t=ln(i),w(e,"data-testid","chatbot-file"),w(e,"href",n=l[39].file?.url),w(e,"target","_blank"),w(e,"download",a=window.__is_colab__?null:l[39].file?.orig_name||l[39].file?.path),w(e,"class","svelte-1ylopk1")},m(r,s){T(r,e,s),W(e,t)},p(r,s){s[0]&1&&i!==(i=(r[39].file?.orig_name||r[39].file?.path)+"")&&en(t,i),s[0]&1&&n!==(n=r[39].file?.url)&&w(e,"href",n),s[0]&1&&a!==(a=window.__is_colab__?null:r[39].file?.orig_name||r[39].file?.path)&&w(e,"download",a)},i:Se,o:Se,d(r){r&&P(e)}}}function un(l){let e,i;return e=new hl({props:{"data-testid":"chatbot-image",src:l[39].file?.url,alt:l[39].alt_text}}),{c(){U(e.$$.fragment)},m(t,n){F(e,t,n),i=!0},p(t,n){const a={};n[0]&1&&(a.src=t[39].file?.url),n[0]&1&&(a.alt=t[39].alt_text),e.$set(a)},i(t){i||(m(e.$$.fragment,t),i=!0)},o(t){g(e.$$.fragment,t),i=!1},d(t){Z(e,t)}}}function fn(l){let e,i;return e=new Ll({props:{"data-testid":"chatbot-video",controls:!0,src:l[39].file?.url,title:l[39].alt_text,preload:"auto",$$slots:{default:[dn]},$$scope:{ctx:l}}}),e.$on("play",l[27]),e.$on("pause",l[28]),e.$on("ended",l[29]),{c(){U(e.$$.fragment)},m(t,n){F(e,t,n),i=!0},p(t,n){const a={};n[0]&1&&(a.src=t[39].file?.url),n[0]&1&&(a.title=t[39].alt_text),n[1]&2048&&(a.$$scope={dirty:n,ctx:t}),e.$set(a)},i(t){i||(m(e.$$.fragment,t),i=!0)},o(t){g(e.$$.fragment,t),i=!1},d(t){Z(e,t)}}}function _n(l){let e,i;return e=new gt({props:{"data-testid":"chatbot-audio",controls:!0,preload:"metadata",src:l[39].file?.url,title:l[39].alt_text}}),e.$on("play",l[24]),e.$on("pause",l[25]),e.$on("ended",l[26]),{c(){U(e.$$.fragment)},m(t,n){F(e,t,n),i=!0},p(t,n){const a={};n[0]&1&&(a.src=t[39].file?.url),n[0]&1&&(a.title=t[39].alt_text),e.$set(a)},i(t){i||(m(e.$$.fragment,t),i=!0)},o(t){g(e.$$.fragment,t),i=!1},d(t){Z(e,t)}}}function cn(l){let e,i;return e=new gl({props:{message:l[39],latex_delimiters:l[1],sanitize_html:l[9],render_markdown:l[11],line_breaks:l[12]}}),e.$on("load",l[17]),{c(){U(e.$$.fragment)},m(t,n){F(e,t,n),i=!0},p(t,n){const a={};n[0]&1&&(a.message=t[39]),n[0]&2&&(a.latex_delimiters=t[1]),n[0]&512&&(a.sanitize_html=t[9]),n[0]&2048&&(a.render_markdown=t[11]),n[0]&4096&&(a.line_breaks=t[12]),e.$set(a)},i(t){i||(m(e.$$.fragment,t),i=!0)},o(t){g(e.$$.fragment,t),i=!1},d(t){Z(e,t)}}}function dn(l){let e;return{c(){e=A("track"),w(e,"kind","captions"),w(e,"class","svelte-1ylopk1")},m(i,t){T(i,e,t)},p:Se,d(i){i&&P(e)}}}function tl(l){let e,i,t,n,a=l[4]&&l[41]==1&&nl(l),r=l[7]&&l[39]&&typeof l[39]=="string"&&il(l);return{c(){e=A("div"),a&&a.c(),i=te(),r&&r.c(),w(e,"class",t="message-buttons-"+(l[41]==0?"user":"bot")+" message-buttons-"+l[14]+" "+(l[8][l[41]]!==null&&"with-avatar")+" svelte-1ylopk1"),z(e,"message-buttons-fit",l[14]==="bubble"&&!l[10]),z(e,"bubble-buttons-user",l[14]==="bubble")},m(s,o){T(s,e,o),a&&a.m(e,null),W(e,i),r&&r.m(e,null),n=!0},p(s,o){s[4]&&s[41]==1?a?(a.p(s,o),o[0]&16&&m(a,1)):(a=nl(s),a.c(),m(a,1),a.m(e,i)):a&&(N(),g(a,1,1,()=>{a=null}),V()),s[7]&&s[39]&&typeof s[39]=="string"?r?(r.p(s,o),o[0]&129&&m(r,1)):(r=il(s),r.c(),m(r,1),r.m(e,null)):r&&(N(),g(r,1,1,()=>{r=null}),V()),(!n||o[0]&16640&&t!==(t="message-buttons-"+(s[41]==0?"user":"bot")+" message-buttons-"+s[14]+" "+(s[8][s[41]]!==null&&"with-avatar")+" svelte-1ylopk1"))&&w(e,"class",t),(!n||o[0]&17664)&&z(e,"message-buttons-fit",s[14]==="bubble"&&!s[10]),(!n||o[0]&16640)&&z(e,"bubble-buttons-user",s[14]==="bubble")},i(s){n||(m(a),m(r),n=!0)},o(s){g(a),g(r),n=!1},d(s){s&&P(e),a&&a.d(),r&&r.d()}}}function nl(l){let e,i;function t(...n){return l[32](l[38],l[41],l[39],...n)}return e=new Nt({props:{handle_action:t}}),{c(){U(e.$$.fragment)},m(n,a){F(e,n,a),i=!0},p(n,a){l=n;const r={};a[0]&1&&(r.handle_action=t),e.$set(r)},i(n){i||(m(e.$$.fragment,n),i=!0)},o(n){g(e.$$.fragment,n),i=!1},d(n){Z(e,n)}}}function il(l){let e,i;return e=new Lt({props:{value:l[39]}}),{c(){U(e.$$.fragment)},m(t,n){F(e,t,n),i=!0},p(t,n){const a={};n[0]&1&&(a.value=t[39]),e.$set(a)},i(t){i||(m(e.$$.fragment,t),i=!0)},o(t){g(e.$$.fragment,t),i=!1},d(t){Z(e,t)}}}function al(l){let e,i,t=l[39]!==null&&el(l);return{c(){t&&t.c(),e=qe()},m(n,a){t&&t.m(n,a),T(n,e,a),i=!0},p(n,a){n[39]!==null?t?(t.p(n,a),a[0]&1&&m(t,1)):(t=el(n),t.c(),m(t,1),t.m(e.parentNode,e)):t&&(N(),g(t,1,1,()=>{t=null}),V())},i(n){i||(m(t),i=!0)},o(n){g(t),i=!1},d(n){n&&P(e),t&&t.d(n)}}}function ol(l){let e,i,t=se(l[36]),n=[];for(let r=0;r<t.length;r+=1)n[r]=al($e(l,t,r));const a=r=>g(n[r],1,1,()=>{n[r]=null});return{c(){for(let r=0;r<n.length;r+=1)n[r].c();e=qe()},m(r,s){for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(r,s);T(r,e,s),i=!0},p(r,s){if(s[0]&942043){t=se(r[36]);let o;for(o=0;o<t.length;o+=1){const u=$e(r,t,o);n[o]?(n[o].p(u,s),m(n[o],1)):(n[o]=al(u),n[o].c(),m(n[o],1),n[o].m(e.parentNode,e))}for(N(),o=t.length;o<n.length;o+=1)a(o);V()}},i(r){if(!i){for(let s=0;s<t.length;s+=1)m(n[s]);i=!0}},o(r){n=n.filter(Boolean);for(let s=0;s<n.length;s+=1)g(n[s]);i=!1},d(r){r&&P(e),vl(n,r)}}}function rl(l){let e,i;return e=new Jt({props:{layout:l[14]}}),{c(){U(e.$$.fragment)},m(t,n){F(e,t,n),i=!0},p(t,n){const a={};n[0]&16384&&(a.layout=t[14]),e.$set(a)},i(t){i||(m(e.$$.fragment,t),i=!0)},o(t){g(e.$$.fragment,t),i=!1},d(t){Z(e,t)}}}function mn(l){let e,i,t,n,a,r,s,o,u,f=l[5]&&l[0]!==null&&l[0].length>0&&xe(l);const b=[rn,on],p=[];function S(d,C){return d[0]!==null&&d[0].length>0?0:d[15]!==null?1:-1}return~(n=S(l))&&(a=p[n]=b[n](l)),{c(){f&&f.c(),e=te(),i=A("div"),t=A("div"),a&&a.c(),w(t,"class","message-wrap svelte-1ylopk1"),z(t,"bubble-gap",l[14]==="bubble"),w(i,"class",r=We(l[14]==="bubble"?"bubble-wrap":"panel-wrap")+" svelte-1ylopk1"),w(i,"role","log"),w(i,"aria-label","chatbot conversation"),w(i,"aria-live","polite"),z(i,"placeholder-container",l[0]===null||l[0].length===0)},m(d,C){f&&f.m(d,C),T(d,e,C),T(d,i,C),W(i,t),~n&&p[n].m(t,null),l[33](i),s=!0,o||(u=Qt(Sl.call(null,t)),o=!0)},p(d,C){d[5]&&d[0]!==null&&d[0].length>0?f?(f.p(d,C),C[0]&33&&m(f,1)):(f=xe(d),f.c(),m(f,1),f.m(e.parentNode,e)):f&&(N(),g(f,1,1,()=>{f=null}),V());let D=n;n=S(d),n===D?~n&&p[n].p(d,C):(a&&(N(),g(p[D],1,1,()=>{p[D]=null}),V()),~n?(a=p[n],a?a.p(d,C):(a=p[n]=b[n](d),a.c()),m(a,1),a.m(t,null)):a=null),(!s||C[0]&16384)&&z(t,"bubble-gap",d[14]==="bubble"),(!s||C[0]&16384&&r!==(r=We(d[14]==="bubble"?"bubble-wrap":"panel-wrap")+" svelte-1ylopk1"))&&w(i,"class",r),(!s||C[0]&16385)&&z(i,"placeholder-container",d[0]===null||d[0].length===0)},i(d){s||(m(f),m(a),s=!0)},o(d){g(f),g(a),s=!1},d(d){d&&(P(e),P(i)),f&&f.d(d),~n&&p[n].d(),l[33](null),o=!1,u()}}}function bn(l,e,i){let t,{value:n}=e,a=null,{latex_delimiters:r}=e,{pending_message:s=!1}=e,{selectable:o=!1}=e,{likeable:u=!1}=e,{show_share_button:f=!1}=e,{rtl:b=!1}=e,{show_copy_button:p=!1}=e,{avatar_images:S=[null,null]}=e,{sanitize_html:d=!0}=e,{bubble_full_width:C=!0}=e,{render_markdown:D=!0}=e,{line_breaks:y=!0}=e,{i18n:R}=e,{layout:L="bubble"}=e,{placeholder:Y=null}=e,q,G;const k=an();tn(()=>{G=q&&q.offsetHeight+q.scrollTop>q.scrollHeight-100});const v=()=>{G&&q.scrollTo(0,q.scrollHeight)};nn(()=>{G&&(v(),q.querySelectorAll("img").forEach(c=>{c.addEventListener("load",()=>{v()})}))});function h(c,E,B){k("select",{index:[c,E],value:B})}function O(c,E,B,ee){k("like",{index:[c,E],value:B,liked:ee==="like"})}function ne(c){J.call(this,l,c)}function ie(c){J.call(this,l,c)}function ae(c){J.call(this,l,c)}function ce(c){J.call(this,l,c)}function de(c){J.call(this,l,c)}function me(c){J.call(this,l,c)}function be(c){J.call(this,l,c)}function he(c){J.call(this,l,c)}const _=(c,E,B)=>h(c,E,B),x=(c,E,B,ee)=>{ee.key==="Enter"&&h(c,E,B)},yl=(c,E,B,ee)=>O(c,E,B,ee);function Cl(c){Wt[c?"unshift":"push"](()=>{q=c,i(16,q)})}return l.$$set=c=>{"value"in c&&i(0,n=c.value),"latex_delimiters"in c&&i(1,r=c.latex_delimiters),"pending_message"in c&&i(2,s=c.pending_message),"selectable"in c&&i(3,o=c.selectable),"likeable"in c&&i(4,u=c.likeable),"show_share_button"in c&&i(5,f=c.show_share_button),"rtl"in c&&i(6,b=c.rtl),"show_copy_button"in c&&i(7,p=c.show_copy_button),"avatar_images"in c&&i(8,S=c.avatar_images),"sanitize_html"in c&&i(9,d=c.sanitize_html),"bubble_full_width"in c&&i(10,C=c.bubble_full_width),"render_markdown"in c&&i(11,D=c.render_markdown),"line_breaks"in c&&i(12,y=c.line_breaks),"i18n"in c&&i(13,R=c.i18n),"layout"in c&&i(14,L=c.layout),"placeholder"in c&&i(15,Y=c.placeholder)},l.$$.update=()=>{l.$$.dirty[0]&2097152&&t(),l.$$.dirty[0]&1048577&&(ql(n,a)||(i(20,a=n),k("change")))},i(21,t=()=>{let E=getComputedStyle(document.body).getPropertyValue("--body-text-size"),B;switch(E){case"13px":B=14;break;case"14px":B=16;break;case"16px":B=20;break;default:B=14;break}document.body.style.setProperty("--chatbot-body-text-size",B+"px")}),[n,r,s,o,u,f,b,p,S,d,C,D,y,R,L,Y,q,v,h,O,a,t,ne,ie,ae,ce,de,me,be,he,_,x,yl,Cl]}class hn extends Kt{constructor(e){super(),Xt(this,e,bn,mn,xt,{value:0,latex_delimiters:1,pending_message:2,selectable:3,likeable:4,show_share_button:5,rtl:6,show_copy_button:7,avatar_images:8,sanitize_html:9,bubble_full_width:10,render_markdown:11,line_breaks:12,i18n:13,layout:14,placeholder:15},null,[-1,-1])}}const gn=hn;const{SvelteComponent:kn,append:wn,assign:pn,attr:vn,check_outros:sl,create_component:ue,destroy_component:fe,detach:ul,element:yn,get_spread_object:Cn,get_spread_update:zn,group_outros:fl,init:Sn,insert:_l,mount_component:_e,safe_not_equal:qn,space:cl,transition_in:I,transition_out:Q}=window.__gradio__svelte__internal;function dl(l){let e,i;const t=[{autoscroll:l[19].autoscroll},{i18n:l[19].i18n},l[21],{show_progress:l[21].show_progress==="hidden"?"hidden":"minimal"}];let n={};for(let a=0;a<t.length;a+=1)n=pn(n,t[a]);return e=new Hl({props:n}),{c(){ue(e.$$.fragment)},m(a,r){_e(e,a,r),i=!0},p(a,r){const s=r[0]&2621440?zn(t,[r[0]&524288&&{autoscroll:a[19].autoscroll},r[0]&524288&&{i18n:a[19].i18n},r[0]&2097152&&Cn(a[21]),r[0]&2097152&&{show_progress:a[21].show_progress==="hidden"?"hidden":"minimal"}]):{};e.$set(s)},i(a){i||(I(e.$$.fragment,a),i=!0)},o(a){Q(e.$$.fragment,a),i=!1},d(a){fe(e,a)}}}function ml(l){let e,i;return e=new Tl({props:{show_label:l[7],Icon:Ul,float:!1,label:l[6]||"Chatbot"}}),{c(){ue(e.$$.fragment)},m(t,n){_e(e,t,n),i=!0},p(t,n){const a={};n[0]&128&&(a.show_label=t[7]),n[0]&64&&(a.label=t[6]||"Chatbot"),e.$set(a)},i(t){i||(I(e.$$.fragment,t),i=!0)},o(t){Q(e.$$.fragment,t),i=!1},d(t){fe(e,t)}}}function Bn(l){let e,i,t,n,a,r=l[21]&&dl(l),s=l[7]&&ml(l);return n=new gn({props:{i18n:l[19].i18n,selectable:l[8],likeable:l[9],show_share_button:l[10],value:l[24],latex_delimiters:l[18],render_markdown:l[16],pending_message:l[21]?.status==="pending",rtl:l[11],show_copy_button:l[12],avatar_images:l[20],sanitize_html:l[13],bubble_full_width:l[14],line_breaks:l[17],layout:l[15],placeholder:l[23]}}),n.$on("change",l[26]),n.$on("select",l[27]),n.$on("like",l[28]),n.$on("share",l[29]),n.$on("error",l[30]),{c(){r&&r.c(),e=cl(),i=yn("div"),s&&s.c(),t=cl(),ue(n.$$.fragment),vn(i,"class","wrapper svelte-nab2ao")},m(o,u){r&&r.m(o,u),_l(o,e,u),_l(o,i,u),s&&s.m(i,null),wn(i,t),_e(n,i,null),a=!0},p(o,u){o[21]?r?(r.p(o,u),u[0]&2097152&&I(r,1)):(r=dl(o),r.c(),I(r,1),r.m(e.parentNode,e)):r&&(fl(),Q(r,1,1,()=>{r=null}),sl()),o[7]?s?(s.p(o,u),u[0]&128&&I(s,1)):(s=ml(o),s.c(),I(s,1),s.m(i,t)):s&&(fl(),Q(s,1,1,()=>{s=null}),sl());const f={};u[0]&524288&&(f.i18n=o[19].i18n),u[0]&256&&(f.selectable=o[8]),u[0]&512&&(f.likeable=o[9]),u[0]&1024&&(f.show_share_button=o[10]),u[0]&16777216&&(f.value=o[24]),u[0]&262144&&(f.latex_delimiters=o[18]),u[0]&65536&&(f.render_markdown=o[16]),u[0]&2097152&&(f.pending_message=o[21]?.status==="pending"),u[0]&2048&&(f.rtl=o[11]),u[0]&4096&&(f.show_copy_button=o[12]),u[0]&1048576&&(f.avatar_images=o[20]),u[0]&8192&&(f.sanitize_html=o[13]),u[0]&16384&&(f.bubble_full_width=o[14]),u[0]&131072&&(f.line_breaks=o[17]),u[0]&32768&&(f.layout=o[15]),u[0]&8388608&&(f.placeholder=o[23]),n.$set(f)},i(o){a||(I(r),I(s),I(n.$$.fragment,o),a=!0)},o(o){Q(r),Q(s),Q(n.$$.fragment,o),a=!1},d(o){o&&(ul(e),ul(i)),r&&r.d(o),s&&s.d(),fe(n)}}}function Hn(l){let e,i;return e=new zl({props:{elem_id:l[0],elem_classes:l[1],visible:l[2],padding:!1,scale:l[4],min_width:l[5],height:l[22],allow_overflow:!1,$$slots:{default:[Bn]},$$scope:{ctx:l}}}),{c(){ue(e.$$.fragment)},m(t,n){_e(e,t,n),i=!0},p(t,n){const a={};n[0]&1&&(a.elem_id=t[0]),n[0]&2&&(a.elem_classes=t[1]),n[0]&4&&(a.visible=t[2]),n[0]&16&&(a.scale=t[4]),n[0]&32&&(a.min_width=t[5]),n[0]&4194304&&(a.height=t[22]),n[0]&29360072|n[1]&2&&(a.$$scope={dirty:n,ctx:t}),e.$set(a)},i(t){i||(I(e.$$.fragment,t),i=!0)},o(t){Q(e.$$.fragment,t),i=!1},d(t){fe(e,t)}}}function bl(l){return l===null?l:{file:l?.file,alt_text:l?.alt_text}}function Mn(l,e,i){let{elem_id:t=""}=e,{elem_classes:n=[]}=e,{visible:a=!0}=e,{value:r=[]}=e,{scale:s=null}=e,{min_width:o=void 0}=e,{label:u}=e,{show_label:f=!0}=e,{root:b}=e,{_selectable:p=!1}=e,{likeable:S=!1}=e,{show_share_button:d=!1}=e,{rtl:C=!1}=e,{show_copy_button:D=!1}=e,{sanitize_html:y=!0}=e,{bubble_full_width:R=!0}=e,{layout:L="bubble"}=e,{render_markdown:Y=!0}=e,{line_breaks:q=!0}=e,{latex_delimiters:G}=e,{gradio:k}=e,{avatar_images:v=[null,null]}=e,h;const O=_=>_.replace('src="/file',`src="${b}file`);let{loading_status:ne=void 0}=e,{height:ie=400}=e,{placeholder:ae=null}=e;const ce=()=>k.dispatch("change",r),de=_=>k.dispatch("select",_.detail),me=_=>k.dispatch("like",_.detail),be=_=>k.dispatch("share",_.detail),he=_=>k.dispatch("error",_.detail);return l.$$set=_=>{"elem_id"in _&&i(0,t=_.elem_id),"elem_classes"in _&&i(1,n=_.elem_classes),"visible"in _&&i(2,a=_.visible),"value"in _&&i(3,r=_.value),"scale"in _&&i(4,s=_.scale),"min_width"in _&&i(5,o=_.min_width),"label"in _&&i(6,u=_.label),"show_label"in _&&i(7,f=_.show_label),"root"in _&&i(25,b=_.root),"_selectable"in _&&i(8,p=_._selectable),"likeable"in _&&i(9,S=_.likeable),"show_share_button"in _&&i(10,d=_.show_share_button),"rtl"in _&&i(11,C=_.rtl),"show_copy_button"in _&&i(12,D=_.show_copy_button),"sanitize_html"in _&&i(13,y=_.sanitize_html),"bubble_full_width"in _&&i(14,R=_.bubble_full_width),"layout"in _&&i(15,L=_.layout),"render_markdown"in _&&i(16,Y=_.render_markdown),"line_breaks"in _&&i(17,q=_.line_breaks),"latex_delimiters"in _&&i(18,G=_.latex_delimiters),"gradio"in _&&i(19,k=_.gradio),"avatar_images"in _&&i(20,v=_.avatar_images),"loading_status"in _&&i(21,ne=_.loading_status),"height"in _&&i(22,ie=_.height),"placeholder"in _&&i(23,ae=_.placeholder)},l.$$.update=()=>{l.$$.dirty[0]&8&&i(24,h=r?r.map(([_,x])=>[typeof _=="string"?O(_):bl(_),typeof x=="string"?O(x):bl(x)]):[])},[t,n,a,r,s,o,u,f,p,S,d,C,D,y,R,L,Y,q,G,k,v,ne,ie,ae,h,b,ce,de,me,be,he]}class Jn extends kn{constructor(e){super(),Sn(this,e,Mn,Hn,qn,{elem_id:0,elem_classes:1,visible:2,value:3,scale:4,min_width:5,label:6,show_label:7,root:25,_selectable:8,likeable:9,show_share_button:10,rtl:11,show_copy_button:12,sanitize_html:13,bubble_full_width:14,layout:15,render_markdown:16,line_breaks:17,latex_delimiters:18,gradio:19,avatar_images:20,loading_status:21,height:22,placeholder:23},null,[-1,-1])}}export{gn as BaseChatBot,Jn as default};
//# sourceMappingURL=Index-76012e4b.js.map
