import{V as k}from"./Video-fd4d29ec.js";import"./index-a80d931b.js";import"./file-url-bef2dc1b.js";import"./Index-26cfc80a.js";import"./svelte/svelte.js";const{SvelteComponent:h,add_flush_callback:w,append:E,attr:V,bind:q,binding_callbacks:z,check_outros:A,create_component:C,destroy_component:I,detach:d,element:v,empty:g,group_outros:N,init:S,insert:p,is_function:m,mount_component:j,noop:b,safe_not_equal:B,set_data:D,text:F,toggle_class:_,transition_in:f,transition_out:c}=window.__gradio__svelte__internal;function y(l){let t,n,e,i;const a=[H,G],r=[];function u(o,s){return 0}return t=u(),n=r[t]=a[t](l),{c(){n.c(),e=g()},m(o,s){r[t].m(o,s),p(o,e,s),i=!0},p(o,s){n.p(o,s)},i(o){i||(f(n),i=!0)},o(o){c(n),i=!1},d(o){o&&d(e),r[t].d(o)}}}function G(l){let t,n;return{c(){t=v("div"),n=F(l[2])},m(e,i){p(e,t,i),E(t,n)},p(e,i){i&4&&D(n,e[2])},i:b,o:b,d(e){e&&d(t)}}}function H(l){let t,n,e,i;function a(u){l[5](u)}let r={muted:!0,playsinline:!0,src:l[2]?.video.url};return l[3]!==void 0&&(r.node=l[3]),n=new k({props:r}),z.push(()=>q(n,"node",a)),n.$on("loadeddata",l[4]),n.$on("mouseover",function(){m(l[3].play.bind(l[3]))&&l[3].play.bind(l[3]).apply(this,arguments)}),n.$on("mouseout",function(){m(l[3].pause.bind(l[3]))&&l[3].pause.bind(l[3]).apply(this,arguments)}),{c(){t=v("div"),C(n.$$.fragment),V(t,"class","container svelte-1de9zxs"),_(t,"table",l[0]==="table"),_(t,"gallery",l[0]==="gallery"),_(t,"selected",l[1])},m(u,o){p(u,t,o),j(n,t,null),i=!0},p(u,o){l=u;const s={};o&4&&(s.src=l[2]?.video.url),!e&&o&8&&(e=!0,s.node=l[3],w(()=>e=!1)),n.$set(s),(!i||o&1)&&_(t,"table",l[0]==="table"),(!i||o&1)&&_(t,"gallery",l[0]==="gallery"),(!i||o&2)&&_(t,"selected",l[1])},i(u){i||(f(n.$$.fragment,u),i=!0)},o(u){c(n.$$.fragment,u),i=!1},d(u){u&&d(t),I(n)}}}function J(l){let t,n,e=l[2]&&y(l);return{c(){e&&e.c(),t=g()},m(i,a){e&&e.m(i,a),p(i,t,a),n=!0},p(i,[a]){i[2]?e?(e.p(i,a),a&4&&f(e,1)):(e=y(i),e.c(),f(e,1),e.m(t.parentNode,t)):e&&(N(),c(e,1,1,()=>{e=null}),A())},i(i){n||(f(e),n=!0)},o(i){c(e),n=!1},d(i){i&&d(t),e&&e.d(i)}}}function K(l,t,n){let{type:e}=t,{selected:i=!1}=t,{value:a}=t,r;async function u(){n(3,r.muted=!0,r),n(3,r.playsInline=!0,r),n(3,r.controls=!1,r),r.setAttribute("muted",""),await r.play(),r.pause()}function o(s){r=s,n(3,r)}return l.$$set=s=>{"type"in s&&n(0,e=s.type),"selected"in s&&n(1,i=s.selected),"value"in s&&n(2,a=s.value)},[e,i,a,r,u,o]}class R extends h{constructor(t){super(),S(this,t,K,J,B,{type:0,selected:1,value:2})}}export{R as default};
//# sourceMappingURL=Example-f78abbcc.js.map
