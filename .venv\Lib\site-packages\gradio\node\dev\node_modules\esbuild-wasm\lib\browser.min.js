(module=>{
"use strict";var Re=Object.defineProperty;var _e=Object.getOwnPropertyDescriptor;var Ve=Object.getOwnPropertyNames;var Ye=Object.prototype.hasOwnProperty;var Je=(e,t)=>{for(var n in t)Re(e,n,{get:t[n],enumerable:!0})},Ge=(e,t,n,i)=>{if(t&&typeof t=="object"||typeof t=="function")for(let u of Ve(t))!Ye.call(e,u)&&u!==n&&Re(e,u,{get:()=>t[u],enumerable:!(i=_e(t,u))||i.enumerable});return e};var Qe=e=>Ge(Re({},"__esModule",{value:!0}),e);var ne=(e,t,n)=>new Promise((i,u)=>{var l=c=>{try{s(n.next(c))}catch(h){u(h)}},g=c=>{try{s(n.throw(c))}catch(h){u(h)}},s=c=>c.done?i(c.value):Promise.resolve(c.value).then(l,g);s((n=n.apply(e,t)).next())});var xe={};Je(xe,{analyzeMetafile:()=>gt,analyzeMetafileSync:()=>bt,build:()=>ft,buildSync:()=>mt,context:()=>ct,default:()=>Rt,formatMessages:()=>pt,formatMessagesSync:()=>ht,initialize:()=>vt,stop:()=>wt,transform:()=>dt,transformSync:()=>yt,version:()=>ut});module.exports=Qe(xe);function Se(e){let t=i=>{if(i===null)n.write8(0);else if(typeof i=="boolean")n.write8(1),n.write8(+i);else if(typeof i=="number")n.write8(2),n.write32(i|0);else if(typeof i=="string")n.write8(3),n.write(Z(i));else if(i instanceof Uint8Array)n.write8(4),n.write(i);else if(i instanceof Array){n.write8(5),n.write32(i.length);for(let u of i)t(u)}else{let u=Object.keys(i);n.write8(6),n.write32(u.length);for(let l of u)n.write(Z(l)),t(i[l])}},n=new pe;return n.write32(0),n.write32(e.id<<1|+!e.isRequest),t(e.value),Ee(n.buf,n.len-4,0),n.buf.subarray(0,n.len)}function $e(e){let t=()=>{switch(n.read8()){case 0:return null;case 1:return!!n.read8();case 2:return n.read32();case 3:return ie(n.read());case 4:return n.read();case 5:{let g=n.read32(),s=[];for(let c=0;c<g;c++)s.push(t());return s}case 6:{let g=n.read32(),s={};for(let c=0;c<g;c++)s[ie(n.read())]=t();return s}default:throw new Error("Invalid packet")}},n=new pe(e),i=n.read32(),u=(i&1)===0;i>>>=1;let l=t();if(n.ptr!==e.length)throw new Error("Invalid packet");return{id:i,isRequest:u,value:l}}var pe=class{constructor(t=new Uint8Array(1024)){this.buf=t;this.len=0;this.ptr=0}_write(t){if(this.len+t>this.buf.length){let n=new Uint8Array((this.len+t)*2);n.set(this.buf),this.buf=n}return this.len+=t,this.len-t}write8(t){let n=this._write(1);this.buf[n]=t}write32(t){let n=this._write(4);Ee(this.buf,t,n)}write(t){let n=this._write(4+t.length);Ee(this.buf,t.length,n),this.buf.set(t,n+4)}_read(t){if(this.ptr+t>this.buf.length)throw new Error("Invalid packet");return this.ptr+=t,this.ptr-t}read8(){return this.buf[this._read(1)]}read32(){return ke(this.buf,this._read(4))}read(){let t=this.read32(),n=new Uint8Array(t),i=this._read(n.length);return n.set(this.buf.subarray(i,i+t)),n}},Z,ie,Oe;if(typeof TextEncoder!="undefined"&&typeof TextDecoder!="undefined"){let e=new TextEncoder,t=new TextDecoder;Z=n=>e.encode(n),ie=n=>t.decode(n),Oe='new TextEncoder().encode("")'}else if(typeof Buffer!="undefined")Z=e=>Buffer.from(e),ie=e=>{let{buffer:t,byteOffset:n,byteLength:i}=e;return Buffer.from(t,n,i).toString()},Oe='Buffer.from("")';else throw new Error("No UTF-8 codec found");if(!(Z("")instanceof Uint8Array))throw new Error(`Invariant violation: "${Oe} instanceof Uint8Array" is incorrectly false

This indicates that your JavaScript environment is broken. You cannot use
esbuild in this environment because esbuild relies on this invariant. This
is not a problem with esbuild. You need to fix your environment instead.
`);function ke(e,t){return e[t++]|e[t++]<<8|e[t++]<<16|e[t++]<<24}function Ee(e,t,n){e[n++]=t,e[n++]=t>>8,e[n++]=t>>16,e[n++]=t>>24}var J=JSON.stringify,Me="warning",Ce="silent";function Ae(e){if(Y(e,"target"),e.indexOf(",")>=0)throw new Error(`Invalid target: ${e}`);return e}var ye=()=>null,I=e=>typeof e=="boolean"?null:"a boolean",y=e=>typeof e=="string"?null:"a string",he=e=>e instanceof RegExp?null:"a RegExp object",se=e=>typeof e=="number"&&e===(e|0)?null:"an integer",Pe=e=>typeof e=="function"?null:"a function",W=e=>Array.isArray(e)?null:"an array",ee=e=>typeof e=="object"&&e!==null&&!Array.isArray(e)?null:"an object",Xe=e=>typeof e=="object"&&e!==null?null:"an array or an object",Ze=e=>e instanceof WebAssembly.Module?null:"a WebAssembly.Module",Fe=e=>typeof e=="object"&&!Array.isArray(e)?null:"an object or null",De=e=>typeof e=="string"||typeof e=="boolean"?null:"a string or a boolean",et=e=>typeof e=="string"||typeof e=="object"&&e!==null&&!Array.isArray(e)?null:"a string or an object",tt=e=>typeof e=="string"||Array.isArray(e)?null:"a string or an array",Be=e=>typeof e=="string"||e instanceof Uint8Array?null:"a string or a Uint8Array",nt=e=>typeof e=="string"||e instanceof URL?null:"a string or a URL";function r(e,t,n,i){let u=e[n];if(t[n+""]=!0,u===void 0)return;let l=i(u);if(l!==null)throw new Error(`${J(n)} must be ${l}`);return u}function K(e,t,n){for(let i in e)if(!(i in t))throw new Error(`Invalid option ${n}: ${J(i)}`)}function Ue(e){let t=Object.create(null),n=r(e,t,"wasmURL",nt),i=r(e,t,"wasmModule",Ze),u=r(e,t,"worker",I);return K(e,t,"in initialize() call"),{wasmURL:n,wasmModule:i,worker:u}}function je(e){let t;if(e!==void 0){t=Object.create(null);for(let n in e){let i=e[n];if(typeof i=="string"||i===!1)t[n]=i;else throw new Error(`Expected ${J(n)} in mangle cache to map to either a string or false`)}}return t}function be(e,t,n,i,u){let l=r(t,n,"color",I),g=r(t,n,"logLevel",y),s=r(t,n,"logLimit",se);l!==void 0?e.push(`--color=${l}`):i&&e.push("--color=true"),e.push(`--log-level=${g||u}`),e.push(`--log-limit=${s||0}`)}function Y(e,t,n){if(typeof e!="string")throw new Error(`Expected value for ${t}${n!==void 0?" "+J(n):""} to be a string, got ${typeof e} instead`);return e}function Le(e,t,n){let i=r(t,n,"legalComments",y),u=r(t,n,"sourceRoot",y),l=r(t,n,"sourcesContent",I),g=r(t,n,"target",tt),s=r(t,n,"format",y),c=r(t,n,"globalName",y),h=r(t,n,"mangleProps",he),w=r(t,n,"reserveProps",he),M=r(t,n,"mangleQuoted",I),L=r(t,n,"minify",I),B=r(t,n,"minifySyntax",I),q=r(t,n,"minifyWhitespace",I),G=r(t,n,"minifyIdentifiers",I),U=r(t,n,"lineLimit",se),z=r(t,n,"drop",W),_=r(t,n,"dropLabels",W),v=r(t,n,"charset",y),m=r(t,n,"treeShaking",I),d=r(t,n,"ignoreAnnotations",I),o=r(t,n,"jsx",y),x=r(t,n,"jsxFactory",y),E=r(t,n,"jsxFragment",y),C=r(t,n,"jsxImportSource",y),P=r(t,n,"jsxDev",I),a=r(t,n,"jsxSideEffects",I),f=r(t,n,"define",ee),b=r(t,n,"logOverride",ee),S=r(t,n,"supported",ee),F=r(t,n,"pure",W),k=r(t,n,"keepNames",I),O=r(t,n,"platform",y),A=r(t,n,"tsconfigRaw",et);if(i&&e.push(`--legal-comments=${i}`),u!==void 0&&e.push(`--source-root=${u}`),l!==void 0&&e.push(`--sources-content=${l}`),g&&(Array.isArray(g)?e.push(`--target=${Array.from(g).map(Ae).join(",")}`):e.push(`--target=${Ae(g)}`)),s&&e.push(`--format=${s}`),c&&e.push(`--global-name=${c}`),O&&e.push(`--platform=${O}`),A&&e.push(`--tsconfig-raw=${typeof A=="string"?A:JSON.stringify(A)}`),L&&e.push("--minify"),B&&e.push("--minify-syntax"),q&&e.push("--minify-whitespace"),G&&e.push("--minify-identifiers"),U&&e.push(`--line-limit=${U}`),v&&e.push(`--charset=${v}`),m!==void 0&&e.push(`--tree-shaking=${m}`),d&&e.push("--ignore-annotations"),z)for(let R of z)e.push(`--drop:${Y(R,"drop")}`);if(_&&e.push(`--drop-labels=${Array.from(_).map(R=>Y(R,"dropLabels")).join(",")}`),h&&e.push(`--mangle-props=${h.source}`),w&&e.push(`--reserve-props=${w.source}`),M!==void 0&&e.push(`--mangle-quoted=${M}`),o&&e.push(`--jsx=${o}`),x&&e.push(`--jsx-factory=${x}`),E&&e.push(`--jsx-fragment=${E}`),C&&e.push(`--jsx-import-source=${C}`),P&&e.push("--jsx-dev"),a&&e.push("--jsx-side-effects"),f)for(let R in f){if(R.indexOf("=")>=0)throw new Error(`Invalid define: ${R}`);e.push(`--define:${R}=${Y(f[R],"define",R)}`)}if(b)for(let R in b){if(R.indexOf("=")>=0)throw new Error(`Invalid log override: ${R}`);e.push(`--log-override:${R}=${Y(b[R],"log override",R)}`)}if(S)for(let R in S){if(R.indexOf("=")>=0)throw new Error(`Invalid supported: ${R}`);let $=S[R];if(typeof $!="boolean")throw new Error(`Expected value for supported ${J(R)} to be a boolean, got ${typeof $} instead`);e.push(`--supported:${R}=${$}`)}if(F)for(let R of F)e.push(`--pure:${Y(R,"pure")}`);k&&e.push("--keep-names")}function rt(e,t,n,i,u){var ae;let l=[],g=[],s=Object.create(null),c=null,h=null;be(l,t,s,n,i),Le(l,t,s);let w=r(t,s,"sourcemap",De),M=r(t,s,"bundle",I),L=r(t,s,"splitting",I),B=r(t,s,"preserveSymlinks",I),q=r(t,s,"metafile",I),G=r(t,s,"outfile",y),U=r(t,s,"outdir",y),z=r(t,s,"outbase",y),_=r(t,s,"tsconfig",y),v=r(t,s,"resolveExtensions",W),m=r(t,s,"nodePaths",W),d=r(t,s,"mainFields",W),o=r(t,s,"conditions",W),x=r(t,s,"external",W),E=r(t,s,"packages",y),C=r(t,s,"alias",ee),P=r(t,s,"loader",ee),a=r(t,s,"outExtension",ee),f=r(t,s,"publicPath",y),b=r(t,s,"entryNames",y),S=r(t,s,"chunkNames",y),F=r(t,s,"assetNames",y),k=r(t,s,"inject",W),O=r(t,s,"banner",ee),A=r(t,s,"footer",ee),R=r(t,s,"entryPoints",Xe),$=r(t,s,"absWorkingDir",y),D=r(t,s,"stdin",ee),T=(ae=r(t,s,"write",I))!=null?ae:u,j=r(t,s,"allowOverwrite",I),V=r(t,s,"mangleCache",ee);if(s.plugins=!0,K(t,s,`in ${e}() call`),w&&l.push(`--sourcemap${w===!0?"":`=${w}`}`),M&&l.push("--bundle"),j&&l.push("--allow-overwrite"),L&&l.push("--splitting"),B&&l.push("--preserve-symlinks"),q&&l.push("--metafile"),G&&l.push(`--outfile=${G}`),U&&l.push(`--outdir=${U}`),z&&l.push(`--outbase=${z}`),_&&l.push(`--tsconfig=${_}`),E&&l.push(`--packages=${E}`),v){let p=[];for(let N of v){if(Y(N,"resolve extension"),N.indexOf(",")>=0)throw new Error(`Invalid resolve extension: ${N}`);p.push(N)}l.push(`--resolve-extensions=${p.join(",")}`)}if(f&&l.push(`--public-path=${f}`),b&&l.push(`--entry-names=${b}`),S&&l.push(`--chunk-names=${S}`),F&&l.push(`--asset-names=${F}`),d){let p=[];for(let N of d){if(Y(N,"main field"),N.indexOf(",")>=0)throw new Error(`Invalid main field: ${N}`);p.push(N)}l.push(`--main-fields=${p.join(",")}`)}if(o){let p=[];for(let N of o){if(Y(N,"condition"),N.indexOf(",")>=0)throw new Error(`Invalid condition: ${N}`);p.push(N)}l.push(`--conditions=${p.join(",")}`)}if(x)for(let p of x)l.push(`--external:${Y(p,"external")}`);if(C)for(let p in C){if(p.indexOf("=")>=0)throw new Error(`Invalid package name in alias: ${p}`);l.push(`--alias:${p}=${Y(C[p],"alias",p)}`)}if(O)for(let p in O){if(p.indexOf("=")>=0)throw new Error(`Invalid banner file type: ${p}`);l.push(`--banner:${p}=${Y(O[p],"banner",p)}`)}if(A)for(let p in A){if(p.indexOf("=")>=0)throw new Error(`Invalid footer file type: ${p}`);l.push(`--footer:${p}=${Y(A[p],"footer",p)}`)}if(k)for(let p of k)l.push(`--inject:${Y(p,"inject")}`);if(P)for(let p in P){if(p.indexOf("=")>=0)throw new Error(`Invalid loader extension: ${p}`);l.push(`--loader:${p}=${Y(P[p],"loader",p)}`)}if(a)for(let p in a){if(p.indexOf("=")>=0)throw new Error(`Invalid out extension: ${p}`);l.push(`--out-extension:${p}=${Y(a[p],"out extension",p)}`)}if(R)if(Array.isArray(R))for(let p=0,N=R.length;p<N;p++){let X=R[p];if(typeof X=="object"&&X!==null){let te=Object.create(null),H=r(X,te,"in",y),fe=r(X,te,"out",y);if(K(X,te,"in entry point at index "+p),H===void 0)throw new Error('Missing property "in" for entry point at index '+p);if(fe===void 0)throw new Error('Missing property "out" for entry point at index '+p);g.push([fe,H])}else g.push(["",Y(X,"entry point at index "+p)])}else for(let p in R)g.push([p,Y(R[p],"entry point",p)]);if(D){let p=Object.create(null),N=r(D,p,"contents",Be),X=r(D,p,"resolveDir",y),te=r(D,p,"sourcefile",y),H=r(D,p,"loader",y);K(D,p,'in "stdin" object'),te&&l.push(`--sourcefile=${te}`),H&&l.push(`--loader=${H}`),X&&(h=X),typeof N=="string"?c=Z(N):N instanceof Uint8Array&&(c=N)}let Q=[];if(m)for(let p of m)p+="",Q.push(p);return{entries:g,flags:l,write:T,stdinContents:c,stdinResolveDir:h,absWorkingDir:$,nodePaths:Q,mangleCache:je(V)}}function it(e,t,n,i){let u=[],l=Object.create(null);be(u,t,l,n,i),Le(u,t,l);let g=r(t,l,"sourcemap",De),s=r(t,l,"sourcefile",y),c=r(t,l,"loader",y),h=r(t,l,"banner",y),w=r(t,l,"footer",y),M=r(t,l,"mangleCache",ee);return K(t,l,`in ${e}() call`),g&&u.push(`--sourcemap=${g===!0?"external":g}`),s&&u.push(`--sourcefile=${s}`),c&&u.push(`--loader=${c}`),h&&u.push(`--banner=${h}`),w&&u.push(`--footer=${w}`),{flags:u,mangleCache:je(M)}}function qe(e){let t={},n={didClose:!1,reason:""},i={},u=0,l=0,g=new Uint8Array(16*1024),s=0,c=v=>{let m=s+v.length;if(m>g.length){let o=new Uint8Array(m*2);o.set(g),g=o}g.set(v,s),s+=v.length;let d=0;for(;d+4<=s;){let o=ke(g,d);if(d+4+o>s)break;d+=4,q(g.subarray(d,d+o)),d+=o}d>0&&(g.copyWithin(0,d,s),s-=d)},h=v=>{n.didClose=!0,v&&(n.reason=": "+(v.message||v));let m="The service was stopped"+n.reason;for(let d in i)i[d](m,null);i={}},w=(v,m,d)=>{if(n.didClose)return d("The service is no longer running"+n.reason,null);let o=u++;i[o]=(x,E)=>{try{d(x,E)}finally{v&&v.unref()}},v&&v.ref(),e.writeToStdin(Se({id:o,isRequest:!0,value:m}))},M=(v,m)=>{if(n.didClose)throw new Error("The service is no longer running"+n.reason);e.writeToStdin(Se({id:v,isRequest:!1,value:m}))},L=(v,m)=>ne(this,null,function*(){try{if(m.command==="ping"){M(v,{});return}if(typeof m.key=="number"){let d=t[m.key];if(!d)return;let o=d[m.command];if(o){yield o(v,m);return}}throw new Error("Invalid command: "+m.command)}catch(d){let o=[le(d,e,null,void 0,"")];try{M(v,{errors:o})}catch(x){}}}),B=!0,q=v=>{if(B){B=!1;let d=String.fromCharCode(...v);if(d!=="0.20.0")throw new Error(`Cannot start service: Host version "0.20.0" does not match binary version ${J(d)}`);return}let m=$e(v);if(m.isRequest)L(m.id,m.value);else{let d=i[m.id];delete i[m.id],m.value.error?d(m.value.error,{}):d(null,m.value)}};return{readFromStdout:c,afterClose:h,service:{buildOrContext:({callName:v,refs:m,options:d,isTTY:o,defaultWD:x,callback:E})=>{let C=0,P=l++,a={},f={ref(){++C===1&&m&&m.ref()},unref(){--C===0&&(delete t[P],m&&m.unref())}};t[P]=a,f.ref(),lt(v,P,w,M,f,e,a,d,o,x,(b,S)=>{try{E(b,S)}finally{f.unref()}})},transform:({callName:v,refs:m,input:d,options:o,isTTY:x,fs:E,callback:C})=>{let P=Ne(),a=f=>{try{if(typeof d!="string"&&!(d instanceof Uint8Array))throw new Error('The input to "transform" must be a string or a Uint8Array');let{flags:b,mangleCache:S}=it(v,o,x,Ce),F={command:"transform",flags:b,inputFS:f!==null,input:f!==null?Z(f):typeof d=="string"?Z(d):d};S&&(F.mangleCache=S),w(m,F,(k,O)=>{if(k)return C(new Error(k),null);let A=ue(O.errors,P),R=ue(O.warnings,P),$=1,D=()=>{if(--$===0){let T={warnings:R,code:O.code,map:O.map,mangleCache:void 0,legalComments:void 0};"legalComments"in O&&(T.legalComments=O==null?void 0:O.legalComments),O.mangleCache&&(T.mangleCache=O==null?void 0:O.mangleCache),C(null,T)}};if(A.length>0)return C(ce("Transform failed",A,R),null);O.codeFS&&($++,E.readFile(O.code,(T,j)=>{T!==null?C(T,null):(O.code=j,D())})),O.mapFS&&($++,E.readFile(O.map,(T,j)=>{T!==null?C(T,null):(O.map=j,D())})),D()})}catch(b){let S=[];try{be(S,o,{},x,Ce)}catch(k){}let F=le(b,e,P,void 0,"");w(m,{command:"error",flags:S,error:F},()=>{F.detail=P.load(F.detail),C(ce("Transform failed",[F],[]),null)})}};if((typeof d=="string"||d instanceof Uint8Array)&&d.length>1024*1024){let f=a;a=()=>E.writeFile(d,f)}a(null)},formatMessages:({callName:v,refs:m,messages:d,options:o,callback:x})=>{if(!o)throw new Error(`Missing second argument in ${v}() call`);let E={},C=r(o,E,"kind",y),P=r(o,E,"color",I),a=r(o,E,"terminalWidth",se);if(K(o,E,`in ${v}() call`),C===void 0)throw new Error(`Missing "kind" in ${v}() call`);if(C!=="error"&&C!=="warning")throw new Error(`Expected "kind" to be "error" or "warning" in ${v}() call`);let f={command:"format-msgs",messages:re(d,"messages",null,"",a),isWarning:C==="warning"};P!==void 0&&(f.color=P),a!==void 0&&(f.terminalWidth=a),w(m,f,(b,S)=>{if(b)return x(new Error(b),null);x(null,S.messages)})},analyzeMetafile:({callName:v,refs:m,metafile:d,options:o,callback:x})=>{o===void 0&&(o={});let E={},C=r(o,E,"color",I),P=r(o,E,"verbose",I);K(o,E,`in ${v}() call`);let a={command:"analyze-metafile",metafile:d};C!==void 0&&(a.color=C),P!==void 0&&(a.verbose=P),w(m,a,(f,b)=>{if(f)return x(new Error(f),null);x(null,b.result)})}}}}function lt(e,t,n,i,u,l,g,s,c,h,w){let M=Ne(),L=e==="context",B=(U,z)=>{let _=[];try{be(_,s,{},c,Me)}catch(m){}let v=le(U,l,M,void 0,z);n(u,{command:"error",flags:_,error:v},()=>{v.detail=M.load(v.detail),w(ce(L?"Context failed":"Build failed",[v],[]),null)})},q;if(typeof s=="object"){let U=s.plugins;if(U!==void 0){if(!Array.isArray(U))return B(new Error('"plugins" must be an array'),"");q=U}}if(q&&q.length>0){if(l.isSync)return B(new Error("Cannot use plugins in synchronous API calls"),"");st(t,n,i,u,l,g,s,q,M).then(U=>{if(!U.ok)return B(U.error,U.pluginName);try{G(U.requestPlugins,U.runOnEndCallbacks,U.scheduleOnDisposeCallbacks)}catch(z){B(z,"")}},U=>B(U,""));return}try{G(null,(U,z)=>z([],[]),()=>{})}catch(U){B(U,"")}function G(U,z,_){let v=l.hasFS,{entries:m,flags:d,write:o,stdinContents:x,stdinResolveDir:E,absWorkingDir:C,nodePaths:P,mangleCache:a}=rt(e,s,c,Me,v);if(o&&!l.hasFS)throw new Error('The "write" option is unavailable in this environment');let f={command:"build",key:t,entries:m,flags:d,write:o,stdinContents:x,stdinResolveDir:E,absWorkingDir:C||h,nodePaths:P,context:L};U&&(f.plugins=U),a&&(f.mangleCache=a);let b=(k,O)=>{let A={errors:ue(k.errors,M),warnings:ue(k.warnings,M),outputFiles:void 0,metafile:void 0,mangleCache:void 0},R=A.errors.slice(),$=A.warnings.slice();k.outputFiles&&(A.outputFiles=k.outputFiles.map(ot)),k.metafile&&(A.metafile=JSON.parse(k.metafile)),k.mangleCache&&(A.mangleCache=k.mangleCache),k.writeToStdout!==void 0&&console.log(ie(k.writeToStdout).replace(/\n$/,"")),z(A,(D,T)=>{if(R.length>0||D.length>0){let j=ce("Build failed",R.concat(D),$.concat(T));return O(j,null,D,T)}O(null,A,D,T)})},S,F;L&&(g["on-end"]=(k,O)=>new Promise(A=>{b(O,(R,$,D,T)=>{let j={errors:D,warnings:T};F&&F(R,$),S=void 0,F=void 0,i(k,j),A()})})),n(u,f,(k,O)=>{if(k)return w(new Error(k),null);if(!L)return b(O,($,D)=>(_(),w($,D)));if(O.errors.length>0)return w(ce("Context failed",O.errors,O.warnings),null);let A=!1,R={rebuild:()=>(S||(S=new Promise(($,D)=>{let T;F=(V,Q)=>{T||(T=()=>V?D(V):$(Q))};let j=()=>{n(u,{command:"rebuild",key:t},(Q,ae)=>{Q?D(new Error(Q)):T?T():j()})};j()})),S),watch:($={})=>new Promise((D,T)=>{if(!l.hasFS)throw new Error('Cannot use the "watch" API in this environment');K($,{},"in watch() call"),n(u,{command:"watch",key:t},Q=>{Q?T(new Error(Q)):D(void 0)})}),serve:($={})=>new Promise((D,T)=>{if(!l.hasFS)throw new Error('Cannot use the "serve" API in this environment');let j={},V=r($,j,"port",se),Q=r($,j,"host",y),ae=r($,j,"servedir",y),p=r($,j,"keyfile",y),N=r($,j,"certfile",y),X=r($,j,"fallback",y),te=r($,j,"onRequest",Pe);K($,j,"in serve() call");let H={command:"serve",key:t,onRequest:!!te};V!==void 0&&(H.port=V),Q!==void 0&&(H.host=Q),ae!==void 0&&(H.servedir=ae),p!==void 0&&(H.keyfile=p),N!==void 0&&(H.certfile=N),X!==void 0&&(H.fallback=X),n(u,H,(fe,We)=>{if(fe)return T(new Error(fe));te&&(g["serve-request"]=(ze,Ke)=>{te(Ke.args),i(ze,{})}),D(We)})}),cancel:()=>new Promise($=>{if(A)return $();n(u,{command:"cancel",key:t},()=>{$()})}),dispose:()=>new Promise($=>{if(A)return $();A=!0,n(u,{command:"dispose",key:t},()=>{$(),_(),u.unref()})})};u.ref(),w(null,R)})}}var st=(e,t,n,i,u,l,g,s,c)=>ne(void 0,null,function*(){let h=[],w=[],M={},L={},B=[],q=0,G=0,U=[],z=!1;s=[...s];for(let m of s){let d={};if(typeof m!="object")throw new Error(`Plugin at index ${G} must be an object`);let o=r(m,d,"name",y);if(typeof o!="string"||o==="")throw new Error(`Plugin at index ${G} is missing a name`);try{let x=r(m,d,"setup",Pe);if(typeof x!="function")throw new Error("Plugin is missing a setup function");K(m,d,`on plugin ${J(o)}`);let E={name:o,onStart:!1,onEnd:!1,onResolve:[],onLoad:[]};G++;let P=x({initialOptions:g,resolve:(a,f={})=>{if(!z)throw new Error('Cannot call "resolve" before plugin setup has completed');if(typeof a!="string")throw new Error("The path to resolve must be a string");let b=Object.create(null),S=r(f,b,"pluginName",y),F=r(f,b,"importer",y),k=r(f,b,"namespace",y),O=r(f,b,"resolveDir",y),A=r(f,b,"kind",y),R=r(f,b,"pluginData",ye);return K(f,b,"in resolve() call"),new Promise(($,D)=>{let T={command:"resolve",path:a,key:e,pluginName:o};if(S!=null&&(T.pluginName=S),F!=null&&(T.importer=F),k!=null&&(T.namespace=k),O!=null&&(T.resolveDir=O),A!=null)T.kind=A;else throw new Error('Must specify "kind" when calling "resolve"');R!=null&&(T.pluginData=c.store(R)),t(i,T,(j,V)=>{j!==null?D(new Error(j)):$({errors:ue(V.errors,c),warnings:ue(V.warnings,c),path:V.path,external:V.external,sideEffects:V.sideEffects,namespace:V.namespace,suffix:V.suffix,pluginData:c.load(V.pluginData)})})})},onStart(a){let f='This error came from the "onStart" callback registered here:',b=ge(new Error(f),u,"onStart");h.push({name:o,callback:a,note:b}),E.onStart=!0},onEnd(a){let f='This error came from the "onEnd" callback registered here:',b=ge(new Error(f),u,"onEnd");w.push({name:o,callback:a,note:b}),E.onEnd=!0},onResolve(a,f){let b='This error came from the "onResolve" callback registered here:',S=ge(new Error(b),u,"onResolve"),F={},k=r(a,F,"filter",he),O=r(a,F,"namespace",y);if(K(a,F,`in onResolve() call for plugin ${J(o)}`),k==null)throw new Error("onResolve() call is missing a filter");let A=q++;M[A]={name:o,callback:f,note:S},E.onResolve.push({id:A,filter:k.source,namespace:O||""})},onLoad(a,f){let b='This error came from the "onLoad" callback registered here:',S=ge(new Error(b),u,"onLoad"),F={},k=r(a,F,"filter",he),O=r(a,F,"namespace",y);if(K(a,F,`in onLoad() call for plugin ${J(o)}`),k==null)throw new Error("onLoad() call is missing a filter");let A=q++;L[A]={name:o,callback:f,note:S},E.onLoad.push({id:A,filter:k.source,namespace:O||""})},onDispose(a){B.push(a)},esbuild:u.esbuild});P&&(yield P),U.push(E)}catch(x){return{ok:!1,error:x,pluginName:o}}}l["on-start"]=(m,d)=>ne(void 0,null,function*(){let o={errors:[],warnings:[]};yield Promise.all(h.map(P=>ne(void 0,[P],function*({name:x,callback:E,note:C}){try{let a=yield E();if(a!=null){if(typeof a!="object")throw new Error(`Expected onStart() callback in plugin ${J(x)} to return an object`);let f={},b=r(a,f,"errors",W),S=r(a,f,"warnings",W);K(a,f,`from onStart() callback in plugin ${J(x)}`),b!=null&&o.errors.push(...re(b,"errors",c,x,void 0)),S!=null&&o.warnings.push(...re(S,"warnings",c,x,void 0))}}catch(a){o.errors.push(le(a,u,c,C&&C(),x))}}))),n(m,o)}),l["on-resolve"]=(m,d)=>ne(void 0,null,function*(){let o={},x="",E,C;for(let P of d.ids)try{({name:x,callback:E,note:C}=M[P]);let a=yield E({path:d.path,importer:d.importer,namespace:d.namespace,resolveDir:d.resolveDir,kind:d.kind,pluginData:c.load(d.pluginData)});if(a!=null){if(typeof a!="object")throw new Error(`Expected onResolve() callback in plugin ${J(x)} to return an object`);let f={},b=r(a,f,"pluginName",y),S=r(a,f,"path",y),F=r(a,f,"namespace",y),k=r(a,f,"suffix",y),O=r(a,f,"external",I),A=r(a,f,"sideEffects",I),R=r(a,f,"pluginData",ye),$=r(a,f,"errors",W),D=r(a,f,"warnings",W),T=r(a,f,"watchFiles",W),j=r(a,f,"watchDirs",W);K(a,f,`from onResolve() callback in plugin ${J(x)}`),o.id=P,b!=null&&(o.pluginName=b),S!=null&&(o.path=S),F!=null&&(o.namespace=F),k!=null&&(o.suffix=k),O!=null&&(o.external=O),A!=null&&(o.sideEffects=A),R!=null&&(o.pluginData=c.store(R)),$!=null&&(o.errors=re($,"errors",c,x,void 0)),D!=null&&(o.warnings=re(D,"warnings",c,x,void 0)),T!=null&&(o.watchFiles=me(T,"watchFiles")),j!=null&&(o.watchDirs=me(j,"watchDirs"));break}}catch(a){o={id:P,errors:[le(a,u,c,C&&C(),x)]};break}n(m,o)}),l["on-load"]=(m,d)=>ne(void 0,null,function*(){let o={},x="",E,C;for(let P of d.ids)try{({name:x,callback:E,note:C}=L[P]);let a=yield E({path:d.path,namespace:d.namespace,suffix:d.suffix,pluginData:c.load(d.pluginData),with:d.with});if(a!=null){if(typeof a!="object")throw new Error(`Expected onLoad() callback in plugin ${J(x)} to return an object`);let f={},b=r(a,f,"pluginName",y),S=r(a,f,"contents",Be),F=r(a,f,"resolveDir",y),k=r(a,f,"pluginData",ye),O=r(a,f,"loader",y),A=r(a,f,"errors",W),R=r(a,f,"warnings",W),$=r(a,f,"watchFiles",W),D=r(a,f,"watchDirs",W);K(a,f,`from onLoad() callback in plugin ${J(x)}`),o.id=P,b!=null&&(o.pluginName=b),S instanceof Uint8Array?o.contents=S:S!=null&&(o.contents=Z(S)),F!=null&&(o.resolveDir=F),k!=null&&(o.pluginData=c.store(k)),O!=null&&(o.loader=O),A!=null&&(o.errors=re(A,"errors",c,x,void 0)),R!=null&&(o.warnings=re(R,"warnings",c,x,void 0)),$!=null&&(o.watchFiles=me($,"watchFiles")),D!=null&&(o.watchDirs=me(D,"watchDirs"));break}}catch(a){o={id:P,errors:[le(a,u,c,C&&C(),x)]};break}n(m,o)});let _=(m,d)=>d([],[]);w.length>0&&(_=(m,d)=>{ne(void 0,null,function*(){let o=[],x=[];for(let{name:E,callback:C,note:P}of w){let a,f;try{let b=yield C(m);if(b!=null){if(typeof b!="object")throw new Error(`Expected onEnd() callback in plugin ${J(E)} to return an object`);let S={},F=r(b,S,"errors",W),k=r(b,S,"warnings",W);K(b,S,`from onEnd() callback in plugin ${J(E)}`),F!=null&&(a=re(F,"errors",c,E,void 0)),k!=null&&(f=re(k,"warnings",c,E,void 0))}}catch(b){a=[le(b,u,c,P&&P(),E)]}if(a){o.push(...a);try{m.errors.push(...a)}catch(b){}}if(f){x.push(...f);try{m.warnings.push(...f)}catch(b){}}}d(o,x)})});let v=()=>{for(let m of B)setTimeout(()=>m(),0)};return z=!0,{ok:!0,requestPlugins:U,runOnEndCallbacks:_,scheduleOnDisposeCallbacks:v}});function Ne(){let e=new Map,t=0;return{load(n){return e.get(n)},store(n){if(n===void 0)return-1;let i=t++;return e.set(i,n),i}}}function ge(e,t,n){let i,u=!1;return()=>{if(u)return i;u=!0;try{let l=(e.stack+"").split(`
`);l.splice(1,1);let g=Ie(t,l,n);if(g)return i={text:e.message,location:g},i}catch(l){}}}function le(e,t,n,i,u){let l="Internal error",g=null;try{l=(e&&e.message||e)+""}catch(s){}try{g=Ie(t,(e.stack+"").split(`
`),"")}catch(s){}return{id:"",pluginName:u,text:l,location:g,notes:i?[i]:[],detail:n?n.store(e):-1}}function Ie(e,t,n){let i="    at ";if(e.readFileSync&&!t[0].startsWith(i)&&t[1].startsWith(i))for(let u=1;u<t.length;u++){let l=t[u];if(l.startsWith(i))for(l=l.slice(i.length);;){let g=/^(?:new |async )?\S+ \((.*)\)$/.exec(l);if(g){l=g[1];continue}if(g=/^eval at \S+ \((.*)\)(?:, \S+:\d+:\d+)?$/.exec(l),g){l=g[1];continue}if(g=/^(\S+):(\d+):(\d+)$/.exec(l),g){let s;try{s=e.readFileSync(g[1],"utf8")}catch(M){break}let c=s.split(/\r\n|\r|\n|\u2028|\u2029/)[+g[2]-1]||"",h=+g[3]-1,w=c.slice(h,h+n.length)===n?n.length:0;return{file:g[1],namespace:"file",line:+g[2],column:Z(c.slice(0,h)).length,length:Z(c.slice(h,h+w)).length,lineText:c+`
`+t.slice(1).join(`
`),suggestion:""}}break}}return null}function ce(e,t,n){let i=5;e+=t.length<1?"":` with ${t.length} error${t.length<2?"":"s"}:`+t.slice(0,i+1).map((l,g)=>{if(g===i)return`
...`;if(!l.location)return`
error: ${l.text}`;let{file:s,line:c,column:h}=l.location,w=l.pluginName?`[plugin: ${l.pluginName}] `:"";return`
${s}:${c}:${h}: ERROR: ${w}${l.text}`}).join("");let u=new Error(e);for(let[l,g]of[["errors",t],["warnings",n]])Object.defineProperty(u,l,{configurable:!0,enumerable:!0,get:()=>g,set:s=>Object.defineProperty(u,l,{configurable:!0,enumerable:!0,value:s})});return u}function ue(e,t){for(let n of e)n.detail=t.load(n.detail);return e}function Te(e,t,n){if(e==null)return null;let i={},u=r(e,i,"file",y),l=r(e,i,"namespace",y),g=r(e,i,"line",se),s=r(e,i,"column",se),c=r(e,i,"length",se),h=r(e,i,"lineText",y),w=r(e,i,"suggestion",y);if(K(e,i,t),h){let M=h.slice(0,(s&&s>0?s:0)+(c&&c>0?c:0)+(n&&n>0?n:80));!/[\x7F-\uFFFF]/.test(M)&&!/\n/.test(h)&&(h=M)}return{file:u||"",namespace:l||"",line:g||0,column:s||0,length:c||0,lineText:h||"",suggestion:w||""}}function re(e,t,n,i,u){let l=[],g=0;for(let s of e){let c={},h=r(s,c,"id",y),w=r(s,c,"pluginName",y),M=r(s,c,"text",y),L=r(s,c,"location",Fe),B=r(s,c,"notes",W),q=r(s,c,"detail",ye),G=`in element ${g} of "${t}"`;K(s,c,G);let U=[];if(B)for(let z of B){let _={},v=r(z,_,"text",y),m=r(z,_,"location",Fe);K(z,_,G),U.push({text:v||"",location:Te(m,G,u)})}l.push({id:h||"",pluginName:w||i,text:M||"",location:Te(L,G,u),notes:U,detail:n?n.store(q):-1}),g++}return l}function me(e,t){let n=[];for(let i of e){if(typeof i!="string")throw new Error(`${J(t)} must be an array of strings`);n.push(i)}return n}function ot({path:e,contents:t,hash:n}){let i=null;return{path:e,contents:t,hash:n,get text(){let u=this.contents;return(i===null||u!==t)&&(t=u,i=ie(u)),i}}}var ut="0.20.0",ft=e=>de().build(e),ct=e=>de().context(e),dt=(e,t)=>de().transform(e,t),pt=(e,t)=>de().formatMessages(e,t),gt=(e,t)=>de().analyzeMetafile(e,t),mt=()=>{throw new Error('The "buildSync" API only works in node')},yt=()=>{throw new Error('The "transformSync" API only works in node')},ht=()=>{throw new Error('The "formatMessagesSync" API only works in node')},bt=()=>{throw new Error('The "analyzeMetafileSync" API only works in node')},wt=()=>(we&&we(),Promise.resolve()),oe,we,ve,de=()=>{if(ve)return ve;throw oe?new Error('You need to wait for the promise returned from "initialize" to be resolved before calling this'):new Error('You need to call "initialize" before calling this')},vt=e=>{e=Ue(e||{});let t=e.wasmURL,n=e.wasmModule,i=e.worker!==!1;if(!t&&!n)throw new Error('Must provide either the "wasmURL" option or the "wasmModule" option');if(oe)throw new Error('Cannot call "initialize" more than once');return oe=xt(t||"",n,i),oe.catch(()=>{oe=void 0}),oe},xt=(e,t,n)=>ne(void 0,null,function*(){let i;if(n){let h=new Blob(['onmessage=(postMessage=>{\n// Copyright 2018 The Go Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style\n// license that can be found in the LICENSE file.\nvar y=(r,f,m)=>new Promise((c,n)=>{var s=u=>{try{l(m.next(u))}catch(h){n(h)}},i=u=>{try{l(m.throw(u))}catch(h){n(h)}},l=u=>u.done?c(u.value):Promise.resolve(u.value).then(s,i);l((m=m.apply(r,f)).next())});let onmessage,globalThis={};for(let r=self;r;r=Object.getPrototypeOf(r))for(let f of Object.getOwnPropertyNames(r))f in globalThis||Object.defineProperty(globalThis,f,{get:()=>self[f]});(()=>{const r=()=>{const c=new Error("not implemented");return c.code="ENOSYS",c};if(!globalThis.fs){let c="";globalThis.fs={constants:{O_WRONLY:-1,O_RDWR:-1,O_CREAT:-1,O_TRUNC:-1,O_APPEND:-1,O_EXCL:-1},writeSync(n,s){c+=m.decode(s);const i=c.lastIndexOf(`\n`);return i!=-1&&(console.log(c.substring(0,i)),c=c.substring(i+1)),s.length},write(n,s,i,l,u,h){if(i!==0||l!==s.length||u!==null){h(r());return}const g=this.writeSync(n,s);h(null,g)},chmod(n,s,i){i(r())},chown(n,s,i,l){l(r())},close(n,s){s(r())},fchmod(n,s,i){i(r())},fchown(n,s,i,l){l(r())},fstat(n,s){s(r())},fsync(n,s){s(null)},ftruncate(n,s,i){i(r())},lchown(n,s,i,l){l(r())},link(n,s,i){i(r())},lstat(n,s){s(r())},mkdir(n,s,i){i(r())},open(n,s,i,l){l(r())},read(n,s,i,l,u,h){h(r())},readdir(n,s){s(r())},readlink(n,s){s(r())},rename(n,s,i){i(r())},rmdir(n,s){s(r())},stat(n,s){s(r())},symlink(n,s,i){i(r())},truncate(n,s,i){i(r())},unlink(n,s){s(r())},utimes(n,s,i,l){l(r())}}}if(globalThis.process||(globalThis.process={getuid(){return-1},getgid(){return-1},geteuid(){return-1},getegid(){return-1},getgroups(){throw r()},pid:-1,ppid:-1,umask(){throw r()},cwd(){throw r()},chdir(){throw r()}}),!globalThis.crypto)throw new Error("globalThis.crypto is not available, polyfill required (crypto.getRandomValues only)");if(!globalThis.performance)throw new Error("globalThis.performance is not available, polyfill required (performance.now only)");if(!globalThis.TextEncoder)throw new Error("globalThis.TextEncoder is not available, polyfill required");if(!globalThis.TextDecoder)throw new Error("globalThis.TextDecoder is not available, polyfill required");const f=new TextEncoder("utf-8"),m=new TextDecoder("utf-8");globalThis.Go=class{constructor(){this.argv=["js"],this.env={},this.exit=t=>{t!==0&&console.warn("exit code:",t)},this._exitPromise=new Promise(t=>{this._resolveExitPromise=t}),this._pendingEvent=null,this._scheduledTimeouts=new Map,this._nextCallbackTimeoutID=1;const c=(t,e)=>{this.mem.setUint32(t+0,e,!0),this.mem.setUint32(t+4,Math.floor(e/4294967296),!0)},n=t=>{const e=this.mem.getUint32(t+0,!0),o=this.mem.getInt32(t+4,!0);return e+o*4294967296},s=t=>{const e=this.mem.getFloat64(t,!0);if(e===0)return;if(!isNaN(e))return e;const o=this.mem.getUint32(t,!0);return this._values[o]},i=(t,e)=>{if(typeof e=="number"&&e!==0){if(isNaN(e)){this.mem.setUint32(t+4,2146959360,!0),this.mem.setUint32(t,0,!0);return}this.mem.setFloat64(t,e,!0);return}if(e===void 0){this.mem.setFloat64(t,0,!0);return}let a=this._ids.get(e);a===void 0&&(a=this._idPool.pop(),a===void 0&&(a=this._values.length),this._values[a]=e,this._goRefCounts[a]=0,this._ids.set(e,a)),this._goRefCounts[a]++;let d=0;switch(typeof e){case"object":e!==null&&(d=1);break;case"string":d=2;break;case"symbol":d=3;break;case"function":d=4;break}this.mem.setUint32(t+4,2146959360|d,!0),this.mem.setUint32(t,a,!0)},l=t=>{const e=n(t+0),o=n(t+8);return new Uint8Array(this._inst.exports.mem.buffer,e,o)},u=t=>{const e=n(t+0),o=n(t+8),a=new Array(o);for(let d=0;d<o;d++)a[d]=s(e+d*8);return a},h=t=>{const e=n(t+0),o=n(t+8);return m.decode(new DataView(this._inst.exports.mem.buffer,e,o))},g=Date.now()-performance.now();this.importObject={go:{"runtime.wasmExit":t=>{t>>>=0;const e=this.mem.getInt32(t+8,!0);this.exited=!0,delete this._inst,delete this._values,delete this._goRefCounts,delete this._ids,delete this._idPool,this.exit(e)},"runtime.wasmWrite":t=>{t>>>=0;const e=n(t+8),o=n(t+16),a=this.mem.getInt32(t+24,!0);globalThis.fs.writeSync(e,new Uint8Array(this._inst.exports.mem.buffer,o,a))},"runtime.resetMemoryDataView":t=>{t>>>=0,this.mem=new DataView(this._inst.exports.mem.buffer)},"runtime.nanotime1":t=>{t>>>=0,c(t+8,(g+performance.now())*1e6)},"runtime.walltime":t=>{t>>>=0;const e=new Date().getTime();c(t+8,e/1e3),this.mem.setInt32(t+16,e%1e3*1e6,!0)},"runtime.scheduleTimeoutEvent":t=>{t>>>=0;const e=this._nextCallbackTimeoutID;this._nextCallbackTimeoutID++,this._scheduledTimeouts.set(e,setTimeout(()=>{for(this._resume();this._scheduledTimeouts.has(e);)console.warn("scheduleTimeoutEvent: missed timeout event"),this._resume()},n(t+8)+1)),this.mem.setInt32(t+16,e,!0)},"runtime.clearTimeoutEvent":t=>{t>>>=0;const e=this.mem.getInt32(t+8,!0);clearTimeout(this._scheduledTimeouts.get(e)),this._scheduledTimeouts.delete(e)},"runtime.getRandomData":t=>{t>>>=0,crypto.getRandomValues(l(t+8))},"syscall/js.finalizeRef":t=>{t>>>=0;const e=this.mem.getUint32(t+8,!0);if(this._goRefCounts[e]--,this._goRefCounts[e]===0){const o=this._values[e];this._values[e]=null,this._ids.delete(o),this._idPool.push(e)}},"syscall/js.stringVal":t=>{t>>>=0,i(t+24,h(t+8))},"syscall/js.valueGet":t=>{t>>>=0;const e=Reflect.get(s(t+8),h(t+16));t=this._inst.exports.getsp()>>>0,i(t+32,e)},"syscall/js.valueSet":t=>{t>>>=0,Reflect.set(s(t+8),h(t+16),s(t+32))},"syscall/js.valueDelete":t=>{t>>>=0,Reflect.deleteProperty(s(t+8),h(t+16))},"syscall/js.valueIndex":t=>{t>>>=0,i(t+24,Reflect.get(s(t+8),n(t+16)))},"syscall/js.valueSetIndex":t=>{t>>>=0,Reflect.set(s(t+8),n(t+16),s(t+24))},"syscall/js.valueCall":t=>{t>>>=0;try{const e=s(t+8),o=Reflect.get(e,h(t+16)),a=u(t+32),d=Reflect.apply(o,e,a);t=this._inst.exports.getsp()>>>0,i(t+56,d),this.mem.setUint8(t+64,1)}catch(e){t=this._inst.exports.getsp()>>>0,i(t+56,e),this.mem.setUint8(t+64,0)}},"syscall/js.valueInvoke":t=>{t>>>=0;try{const e=s(t+8),o=u(t+16),a=Reflect.apply(e,void 0,o);t=this._inst.exports.getsp()>>>0,i(t+40,a),this.mem.setUint8(t+48,1)}catch(e){t=this._inst.exports.getsp()>>>0,i(t+40,e),this.mem.setUint8(t+48,0)}},"syscall/js.valueNew":t=>{t>>>=0;try{const e=s(t+8),o=u(t+16),a=Reflect.construct(e,o);t=this._inst.exports.getsp()>>>0,i(t+40,a),this.mem.setUint8(t+48,1)}catch(e){t=this._inst.exports.getsp()>>>0,i(t+40,e),this.mem.setUint8(t+48,0)}},"syscall/js.valueLength":t=>{t>>>=0,c(t+16,parseInt(s(t+8).length))},"syscall/js.valuePrepareString":t=>{t>>>=0;const e=f.encode(String(s(t+8)));i(t+16,e),c(t+24,e.length)},"syscall/js.valueLoadString":t=>{t>>>=0;const e=s(t+8);l(t+16).set(e)},"syscall/js.valueInstanceOf":t=>{t>>>=0,this.mem.setUint8(t+24,s(t+8)instanceof s(t+16)?1:0)},"syscall/js.copyBytesToGo":t=>{t>>>=0;const e=l(t+8),o=s(t+32);if(!(o instanceof Uint8Array||o instanceof Uint8ClampedArray)){this.mem.setUint8(t+48,0);return}const a=o.subarray(0,e.length);e.set(a),c(t+40,a.length),this.mem.setUint8(t+48,1)},"syscall/js.copyBytesToJS":t=>{t>>>=0;const e=s(t+8),o=l(t+16);if(!(e instanceof Uint8Array||e instanceof Uint8ClampedArray)){this.mem.setUint8(t+48,0);return}const a=o.subarray(0,e.length);e.set(a),c(t+40,a.length),this.mem.setUint8(t+48,1)},debug:t=>{console.log(t)}}}}run(c){return y(this,null,function*(){if(!(c instanceof WebAssembly.Instance))throw new Error("Go.run: WebAssembly.Instance expected");this._inst=c,this.mem=new DataView(this._inst.exports.mem.buffer),this._values=[NaN,0,null,!0,!1,globalThis,this],this._goRefCounts=new Array(this._values.length).fill(1/0),this._ids=new Map([[0,1],[null,2],[!0,3],[!1,4],[globalThis,5],[this,6]]),this._idPool=[],this.exited=!1;let n=4096;const s=t=>{const e=n,o=f.encode(t+"\\0");return new Uint8Array(this.mem.buffer,n,o.length).set(o),n+=o.length,n%8!==0&&(n+=8-n%8),e},i=this.argv.length,l=[];this.argv.forEach(t=>{l.push(s(t))}),l.push(0),Object.keys(this.env).sort().forEach(t=>{l.push(s(`${t}=${this.env[t]}`))}),l.push(0);const h=n;if(l.forEach(t=>{this.mem.setUint32(n,t,!0),this.mem.setUint32(n+4,0,!0),n+=8}),n>=12288)throw new Error("total length of command line and environment variables exceeds limit");this._inst.exports.run(i,h),this.exited&&this._resolveExitPromise(),yield this._exitPromise})}_resume(){if(this.exited)throw new Error("Go program has already exited");this._inst.exports.resume(),this.exited&&this._resolveExitPromise()}_makeFuncWrapper(c){const n=this;return function(){const s={id:c,this:this,args:arguments};return n._pendingEvent=s,n._resume(),s.result}}}})(),onmessage=({data:r})=>{let f=new TextDecoder,m=globalThis.fs,c="";m.writeSync=(u,h)=>{if(u===1)postMessage(h);else if(u===2){c+=f.decode(h);let g=c.split(`\n`);g.length>1&&console.log(g.slice(0,-1).join(`\n`)),c=g[g.length-1]}else throw new Error("Bad write");return h.length};let n=[],s,i=0;onmessage=({data:u})=>(u.length>0&&(n.push(u),s&&s()),l),m.read=(u,h,g,t,e,o)=>{if(u!==0||g!==0||t!==h.length||e!==null)throw new Error("Bad read");if(n.length===0){s=()=>m.read(u,h,g,t,e,o);return}let a=n[0],d=Math.max(0,Math.min(t,a.length-i));h.set(a.subarray(i,i+d),g),i+=d,i===a.length&&(n.shift(),i=0),o(null,d)};let l=new globalThis.Go;return l.argv=["","--service=0.20.0"],tryToInstantiateModule(r,l).then(u=>{postMessage(null),l.run(u)},u=>{postMessage(u)}),l};function tryToInstantiateModule(r,f){return y(this,null,function*(){if(r instanceof WebAssembly.Module)return WebAssembly.instantiate(r,f.importObject);const m=yield fetch(r);if(!m.ok)throw new Error(`Failed to download ${JSON.stringify(r)}`);if("instantiateStreaming"in WebAssembly&&/^application\\/wasm($|;)/i.test(m.headers.get("Content-Type")||""))return(yield WebAssembly.instantiateStreaming(m,f.importObject)).instance;const c=yield m.arrayBuffer();return(yield WebAssembly.instantiate(c,f.importObject)).instance})}return r=>onmessage(r);})(postMessage)'],{type:"text/javascript"});i=new Worker(URL.createObjectURL(h))}else{let h=(postMessage=>{
// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.
var y=(r,f,m)=>new Promise((c,n)=>{var s=u=>{try{l(m.next(u))}catch(h){n(h)}},i=u=>{try{l(m.throw(u))}catch(h){n(h)}},l=u=>u.done?c(u.value):Promise.resolve(u.value).then(s,i);l((m=m.apply(r,f)).next())});let onmessage,globalThis={};for(let r=self;r;r=Object.getPrototypeOf(r))for(let f of Object.getOwnPropertyNames(r))f in globalThis||Object.defineProperty(globalThis,f,{get:()=>self[f]});(()=>{const r=()=>{const c=new Error("not implemented");return c.code="ENOSYS",c};if(!globalThis.fs){let c="";globalThis.fs={constants:{O_WRONLY:-1,O_RDWR:-1,O_CREAT:-1,O_TRUNC:-1,O_APPEND:-1,O_EXCL:-1},writeSync(n,s){c+=m.decode(s);const i=c.lastIndexOf(`
`);return i!=-1&&(console.log(c.substring(0,i)),c=c.substring(i+1)),s.length},write(n,s,i,l,u,h){if(i!==0||l!==s.length||u!==null){h(r());return}const g=this.writeSync(n,s);h(null,g)},chmod(n,s,i){i(r())},chown(n,s,i,l){l(r())},close(n,s){s(r())},fchmod(n,s,i){i(r())},fchown(n,s,i,l){l(r())},fstat(n,s){s(r())},fsync(n,s){s(null)},ftruncate(n,s,i){i(r())},lchown(n,s,i,l){l(r())},link(n,s,i){i(r())},lstat(n,s){s(r())},mkdir(n,s,i){i(r())},open(n,s,i,l){l(r())},read(n,s,i,l,u,h){h(r())},readdir(n,s){s(r())},readlink(n,s){s(r())},rename(n,s,i){i(r())},rmdir(n,s){s(r())},stat(n,s){s(r())},symlink(n,s,i){i(r())},truncate(n,s,i){i(r())},unlink(n,s){s(r())},utimes(n,s,i,l){l(r())}}}if(globalThis.process||(globalThis.process={getuid(){return-1},getgid(){return-1},geteuid(){return-1},getegid(){return-1},getgroups(){throw r()},pid:-1,ppid:-1,umask(){throw r()},cwd(){throw r()},chdir(){throw r()}}),!globalThis.crypto)throw new Error("globalThis.crypto is not available, polyfill required (crypto.getRandomValues only)");if(!globalThis.performance)throw new Error("globalThis.performance is not available, polyfill required (performance.now only)");if(!globalThis.TextEncoder)throw new Error("globalThis.TextEncoder is not available, polyfill required");if(!globalThis.TextDecoder)throw new Error("globalThis.TextDecoder is not available, polyfill required");const f=new TextEncoder("utf-8"),m=new TextDecoder("utf-8");globalThis.Go=class{constructor(){this.argv=["js"],this.env={},this.exit=t=>{t!==0&&console.warn("exit code:",t)},this._exitPromise=new Promise(t=>{this._resolveExitPromise=t}),this._pendingEvent=null,this._scheduledTimeouts=new Map,this._nextCallbackTimeoutID=1;const c=(t,e)=>{this.mem.setUint32(t+0,e,!0),this.mem.setUint32(t+4,Math.floor(e/4294967296),!0)},n=t=>{const e=this.mem.getUint32(t+0,!0),o=this.mem.getInt32(t+4,!0);return e+o*4294967296},s=t=>{const e=this.mem.getFloat64(t,!0);if(e===0)return;if(!isNaN(e))return e;const o=this.mem.getUint32(t,!0);return this._values[o]},i=(t,e)=>{if(typeof e=="number"&&e!==0){if(isNaN(e)){this.mem.setUint32(t+4,2146959360,!0),this.mem.setUint32(t,0,!0);return}this.mem.setFloat64(t,e,!0);return}if(e===void 0){this.mem.setFloat64(t,0,!0);return}let a=this._ids.get(e);a===void 0&&(a=this._idPool.pop(),a===void 0&&(a=this._values.length),this._values[a]=e,this._goRefCounts[a]=0,this._ids.set(e,a)),this._goRefCounts[a]++;let d=0;switch(typeof e){case"object":e!==null&&(d=1);break;case"string":d=2;break;case"symbol":d=3;break;case"function":d=4;break}this.mem.setUint32(t+4,2146959360|d,!0),this.mem.setUint32(t,a,!0)},l=t=>{const e=n(t+0),o=n(t+8);return new Uint8Array(this._inst.exports.mem.buffer,e,o)},u=t=>{const e=n(t+0),o=n(t+8),a=new Array(o);for(let d=0;d<o;d++)a[d]=s(e+d*8);return a},h=t=>{const e=n(t+0),o=n(t+8);return m.decode(new DataView(this._inst.exports.mem.buffer,e,o))},g=Date.now()-performance.now();this.importObject={go:{"runtime.wasmExit":t=>{t>>>=0;const e=this.mem.getInt32(t+8,!0);this.exited=!0,delete this._inst,delete this._values,delete this._goRefCounts,delete this._ids,delete this._idPool,this.exit(e)},"runtime.wasmWrite":t=>{t>>>=0;const e=n(t+8),o=n(t+16),a=this.mem.getInt32(t+24,!0);globalThis.fs.writeSync(e,new Uint8Array(this._inst.exports.mem.buffer,o,a))},"runtime.resetMemoryDataView":t=>{t>>>=0,this.mem=new DataView(this._inst.exports.mem.buffer)},"runtime.nanotime1":t=>{t>>>=0,c(t+8,(g+performance.now())*1e6)},"runtime.walltime":t=>{t>>>=0;const e=new Date().getTime();c(t+8,e/1e3),this.mem.setInt32(t+16,e%1e3*1e6,!0)},"runtime.scheduleTimeoutEvent":t=>{t>>>=0;const e=this._nextCallbackTimeoutID;this._nextCallbackTimeoutID++,this._scheduledTimeouts.set(e,setTimeout(()=>{for(this._resume();this._scheduledTimeouts.has(e);)console.warn("scheduleTimeoutEvent: missed timeout event"),this._resume()},n(t+8)+1)),this.mem.setInt32(t+16,e,!0)},"runtime.clearTimeoutEvent":t=>{t>>>=0;const e=this.mem.getInt32(t+8,!0);clearTimeout(this._scheduledTimeouts.get(e)),this._scheduledTimeouts.delete(e)},"runtime.getRandomData":t=>{t>>>=0,crypto.getRandomValues(l(t+8))},"syscall/js.finalizeRef":t=>{t>>>=0;const e=this.mem.getUint32(t+8,!0);if(this._goRefCounts[e]--,this._goRefCounts[e]===0){const o=this._values[e];this._values[e]=null,this._ids.delete(o),this._idPool.push(e)}},"syscall/js.stringVal":t=>{t>>>=0,i(t+24,h(t+8))},"syscall/js.valueGet":t=>{t>>>=0;const e=Reflect.get(s(t+8),h(t+16));t=this._inst.exports.getsp()>>>0,i(t+32,e)},"syscall/js.valueSet":t=>{t>>>=0,Reflect.set(s(t+8),h(t+16),s(t+32))},"syscall/js.valueDelete":t=>{t>>>=0,Reflect.deleteProperty(s(t+8),h(t+16))},"syscall/js.valueIndex":t=>{t>>>=0,i(t+24,Reflect.get(s(t+8),n(t+16)))},"syscall/js.valueSetIndex":t=>{t>>>=0,Reflect.set(s(t+8),n(t+16),s(t+24))},"syscall/js.valueCall":t=>{t>>>=0;try{const e=s(t+8),o=Reflect.get(e,h(t+16)),a=u(t+32),d=Reflect.apply(o,e,a);t=this._inst.exports.getsp()>>>0,i(t+56,d),this.mem.setUint8(t+64,1)}catch(e){t=this._inst.exports.getsp()>>>0,i(t+56,e),this.mem.setUint8(t+64,0)}},"syscall/js.valueInvoke":t=>{t>>>=0;try{const e=s(t+8),o=u(t+16),a=Reflect.apply(e,void 0,o);t=this._inst.exports.getsp()>>>0,i(t+40,a),this.mem.setUint8(t+48,1)}catch(e){t=this._inst.exports.getsp()>>>0,i(t+40,e),this.mem.setUint8(t+48,0)}},"syscall/js.valueNew":t=>{t>>>=0;try{const e=s(t+8),o=u(t+16),a=Reflect.construct(e,o);t=this._inst.exports.getsp()>>>0,i(t+40,a),this.mem.setUint8(t+48,1)}catch(e){t=this._inst.exports.getsp()>>>0,i(t+40,e),this.mem.setUint8(t+48,0)}},"syscall/js.valueLength":t=>{t>>>=0,c(t+16,parseInt(s(t+8).length))},"syscall/js.valuePrepareString":t=>{t>>>=0;const e=f.encode(String(s(t+8)));i(t+16,e),c(t+24,e.length)},"syscall/js.valueLoadString":t=>{t>>>=0;const e=s(t+8);l(t+16).set(e)},"syscall/js.valueInstanceOf":t=>{t>>>=0,this.mem.setUint8(t+24,s(t+8)instanceof s(t+16)?1:0)},"syscall/js.copyBytesToGo":t=>{t>>>=0;const e=l(t+8),o=s(t+32);if(!(o instanceof Uint8Array||o instanceof Uint8ClampedArray)){this.mem.setUint8(t+48,0);return}const a=o.subarray(0,e.length);e.set(a),c(t+40,a.length),this.mem.setUint8(t+48,1)},"syscall/js.copyBytesToJS":t=>{t>>>=0;const e=s(t+8),o=l(t+16);if(!(e instanceof Uint8Array||e instanceof Uint8ClampedArray)){this.mem.setUint8(t+48,0);return}const a=o.subarray(0,e.length);e.set(a),c(t+40,a.length),this.mem.setUint8(t+48,1)},debug:t=>{console.log(t)}}}}run(c){return y(this,null,function*(){if(!(c instanceof WebAssembly.Instance))throw new Error("Go.run: WebAssembly.Instance expected");this._inst=c,this.mem=new DataView(this._inst.exports.mem.buffer),this._values=[NaN,0,null,!0,!1,globalThis,this],this._goRefCounts=new Array(this._values.length).fill(1/0),this._ids=new Map([[0,1],[null,2],[!0,3],[!1,4],[globalThis,5],[this,6]]),this._idPool=[],this.exited=!1;let n=4096;const s=t=>{const e=n,o=f.encode(t+"\0");return new Uint8Array(this.mem.buffer,n,o.length).set(o),n+=o.length,n%8!==0&&(n+=8-n%8),e},i=this.argv.length,l=[];this.argv.forEach(t=>{l.push(s(t))}),l.push(0),Object.keys(this.env).sort().forEach(t=>{l.push(s(`${t}=${this.env[t]}`))}),l.push(0);const h=n;if(l.forEach(t=>{this.mem.setUint32(n,t,!0),this.mem.setUint32(n+4,0,!0),n+=8}),n>=12288)throw new Error("total length of command line and environment variables exceeds limit");this._inst.exports.run(i,h),this.exited&&this._resolveExitPromise(),yield this._exitPromise})}_resume(){if(this.exited)throw new Error("Go program has already exited");this._inst.exports.resume(),this.exited&&this._resolveExitPromise()}_makeFuncWrapper(c){const n=this;return function(){const s={id:c,this:this,args:arguments};return n._pendingEvent=s,n._resume(),s.result}}}})(),onmessage=({data:r})=>{let f=new TextDecoder,m=globalThis.fs,c="";m.writeSync=(u,h)=>{if(u===1)postMessage(h);else if(u===2){c+=f.decode(h);let g=c.split(`
`);g.length>1&&console.log(g.slice(0,-1).join(`
`)),c=g[g.length-1]}else throw new Error("Bad write");return h.length};let n=[],s,i=0;onmessage=({data:u})=>(u.length>0&&(n.push(u),s&&s()),l),m.read=(u,h,g,t,e,o)=>{if(u!==0||g!==0||t!==h.length||e!==null)throw new Error("Bad read");if(n.length===0){s=()=>m.read(u,h,g,t,e,o);return}let a=n[0],d=Math.max(0,Math.min(t,a.length-i));h.set(a.subarray(i,i+d),g),i+=d,i===a.length&&(n.shift(),i=0),o(null,d)};let l=new globalThis.Go;return l.argv=["","--service=0.20.0"],tryToInstantiateModule(r,l).then(u=>{postMessage(null),l.run(u)},u=>{postMessage(u)}),l};function tryToInstantiateModule(r,f){return y(this,null,function*(){if(r instanceof WebAssembly.Module)return WebAssembly.instantiate(r,f.importObject);const m=yield fetch(r);if(!m.ok)throw new Error(`Failed to download ${JSON.stringify(r)}`);if("instantiateStreaming"in WebAssembly&&/^application\/wasm($|;)/i.test(m.headers.get("Content-Type")||""))return(yield WebAssembly.instantiateStreaming(m,f.importObject)).instance;const c=yield m.arrayBuffer();return(yield WebAssembly.instantiate(c,f.importObject)).instance})}return r=>onmessage(r);})(M=>i.onmessage({data:M})),w;i={onmessage:null,postMessage:M=>setTimeout(()=>w=h({data:M})),terminate(){if(w)for(let M of w._scheduledTimeouts.values())clearTimeout(M)}}}let u,l,g=new Promise((h,w)=>{u=h,l=w});i.onmessage=({data:h})=>{i.onmessage=({data:w})=>s(w),h?l(h):u()},i.postMessage(t||new URL(e,location.href).toString());let{readFromStdout:s,service:c}=qe({writeToStdin(h){i.postMessage(h)},isSync:!1,hasFS:!1,esbuild:xe});yield g,we=()=>{i.terminate(),oe=void 0,we=void 0,ve=void 0},ve={build:h=>new Promise((w,M)=>c.buildOrContext({callName:"build",refs:null,options:h,isTTY:!1,defaultWD:"/",callback:(L,B)=>L?M(L):w(B)})),context:h=>new Promise((w,M)=>c.buildOrContext({callName:"context",refs:null,options:h,isTTY:!1,defaultWD:"/",callback:(L,B)=>L?M(L):w(B)})),transform:(h,w)=>new Promise((M,L)=>c.transform({callName:"transform",refs:null,input:h,options:w||{},isTTY:!1,fs:{readFile(B,q){q(new Error("Internal error"),null)},writeFile(B,q){q(null)}},callback:(B,q)=>B?L(B):M(q)})),formatMessages:(h,w)=>new Promise((M,L)=>c.formatMessages({callName:"formatMessages",refs:null,messages:h,options:w,callback:(B,q)=>B?L(B):M(q)})),analyzeMetafile:(h,w)=>new Promise((M,L)=>c.analyzeMetafile({callName:"analyzeMetafile",refs:null,metafile:typeof h=="string"?h:JSON.stringify(h),options:w,callback:(B,q)=>B?L(B):M(q)}))}}),Rt=xe;
})(typeof module==="object"?module:{set exports(x){(typeof self!=="undefined"?self:this).esbuild=x}});
