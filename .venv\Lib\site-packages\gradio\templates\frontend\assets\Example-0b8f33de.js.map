{"version": 3, "file": "Example-0b8f33de.js", "sources": ["../../../../js/code/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let value: string | null;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<pre\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected>{value ? value : \"\"}</pre>\n\n<style>\n\tpre {\n\t\ttext-align: left;\n\t}\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n"], "names": ["ctx", "toggle_class", "pre", "insert", "target", "anchor", "set_data", "t", "t_value", "value", "$$props", "type", "selected"], "mappings": "8LASiBA,EAAK,CAAA,EAAGA,EAAK,CAAA,EAAG,IAAE,+DAFrBC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAGyCC,EAAAF,EAAAG,CAAA,gCAAxBL,EAAK,CAAA,EAAGA,EAAK,CAAA,EAAG,IAAE,KAAAM,EAAAC,EAAAC,CAAA,OAFrBP,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,0EAPtB,MAAAS,CAAoB,EAAAC,GACpB,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF"}