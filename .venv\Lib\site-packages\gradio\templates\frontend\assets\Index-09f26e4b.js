const{SvelteComponent:c,attr:d,create_slot:m,detach:r,element:h,get_all_dirty_from_scope:w,get_slot_changes:g,init:v,insert:b,safe_not_equal:q,set_style:a,toggle_class:u,transition_in:y,transition_out:I,update_slot_base:C}=window.__gradio__svelte__internal;function S(s){let t,i;const f=s[4].default,l=m(f,s,s[3],null);return{c(){t=h("div"),l&&l.c(),d(t,"class","form svelte-sfqy0y"),u(t,"hidden",!s[0]),a(t,"flex-grow",s[1]),a(t,"min-width",`calc(min(${s[2]}px, 100%))`)},m(e,n){b(e,t,n),l&&l.m(t,null),i=!0},p(e,[n]){l&&l.p&&(!i||n&8)&&C(l,f,e,e[3],i?g(f,e[3],n,null):w(e[3]),null),(!i||n&1)&&u(t,"hidden",!e[0]),n&2&&a(t,"flex-grow",e[1]),n&4&&a(t,"min-width",`calc(min(${e[2]}px, 100%))`)},i(e){i||(y(l,e),i=!0)},o(e){I(l,e),i=!1},d(e){e&&r(t),l&&l.d(e)}}}function j(s,t,i){let{$$slots:f={},$$scope:l}=t,{visible:e=!0}=t,{scale:n=null}=t,{min_width:o=0}=t;return s.$$set=_=>{"visible"in _&&i(0,e=_.visible),"scale"in _&&i(1,n=_.scale),"min_width"in _&&i(2,o=_.min_width),"$$scope"in _&&i(3,l=_.$$scope)},[e,n,o,l,f]}class k extends c{constructor(t){super(),v(this,t,j,S,q,{visible:0,scale:1,min_width:2})}}export{k as default};
//# sourceMappingURL=Index-09f26e4b.js.map
