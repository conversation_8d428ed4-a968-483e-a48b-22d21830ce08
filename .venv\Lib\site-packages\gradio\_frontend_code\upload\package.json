{"name": "@gradio/upload", "version": "0.8.3", "description": "Gradio UI packages", "type": "module", "main": "src/index.ts", "author": "", "license": "ISC", "dependencies": {"@gradio/atoms": "workspace:^", "@gradio/icons": "workspace:^", "@gradio/client": "workspace:^", "@gradio/upload": "workspace:^", "@gradio/utils": "workspace:^", "@gradio/wasm": "workspace:^"}, "main_changeset": true, "exports": {".": "./src/index.ts"}}