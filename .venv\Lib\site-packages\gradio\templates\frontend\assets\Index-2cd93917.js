import{_ as ne}from"./index-a80d931b.js";import{B as be}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{B as _e}from"./BlockLabel-f27805b1.js";import{I as we}from"./IconButton-7294c90b.js";import{a as Be}from"./DownloadLink-7ff36416.js";import{S as ke}from"./Index-26cfc80a.js";import{F as le}from"./File-d0b52941.js";import{U as Ae}from"./Undo-b088de14.js";import{U as Le}from"./ModifyUpload.svelte_svelte_type_style_lang-4ba51100.js";import{M as je}from"./ModifyUpload-66b0c302.js";import{E as Ne}from"./Empty-28f63bf0.js";import{U as Oe}from"./UploadText-39c67ae9.js";import{default as yn}from"./Example-4f6d17e3.js";import"./svelte/svelte.js";import"./file-url-bef2dc1b.js";import"./Clear-2c7bae91.js";import"./Upload-351cc897.js";var me=Object.prototype.hasOwnProperty;function de(i,e,l){for(l of i.keys())if(G(l,e))return l}function G(i,e){var l,n,t;if(i===e)return!0;if(i&&e&&(l=i.constructor)===e.constructor){if(l===Date)return i.getTime()===e.getTime();if(l===RegExp)return i.toString()===e.toString();if(l===Array){if((n=i.length)===e.length)for(;n--&&G(i[n],e[n]););return n===-1}if(l===Set){if(i.size!==e.size)return!1;for(n of i)if(t=n,t&&typeof t=="object"&&(t=de(e,t),!t)||!e.has(t))return!1;return!0}if(l===Map){if(i.size!==e.size)return!1;for(n of i)if(t=n[0],t&&typeof t=="object"&&(t=de(e,t),!t)||!G(n[1],e.get(t)))return!1;return!0}if(l===ArrayBuffer)i=new Uint8Array(i),e=new Uint8Array(e);else if(l===DataView){if((n=i.byteLength)===e.byteLength)for(;n--&&i.getInt8(n)===e.getInt8(n););return n===-1}if(ArrayBuffer.isView(i)){if((n=i.byteLength)===e.byteLength)for(;n--&&i[n]===e[n];);return n===-1}if(!l||typeof i=="object"){n=0;for(l in i)if(me.call(i,l)&&++n&&!me.call(e,l)||!(l in e)||!G(i[l],e[l]))return!1;return Object.keys(e).length===n}}return i!==i&&e!==e}const{SvelteComponent:Te,add_flush_callback:ve,append:y,attr:j,bind:$,binding_callbacks:W,check_outros:F,construct_svelte_component:x,create_component:N,destroy_component:O,detach:H,element:ae,empty:fe,group_outros:J,init:qe,insert:K,mount_component:T,noop:Pe,safe_not_equal:Ve,space:re,transition_in:w,transition_out:z}=window.__gradio__svelte__internal;function pe(i){let e,l,n,t,a,s,_,o,f,r,u=!i[8]&&ge(i);a=new we({props:{Icon:Be,label:i[4]("common.download")}});const c=[Ge,Re],d=[];function C(p,b){return p[8]?0:1}return o=C(i),f=d[o]=c[o](i),{c(){e=ae("div"),l=ae("div"),u&&u.c(),n=re(),t=ae("a"),N(a.$$.fragment),_=re(),f.c(),j(t,"href",i[12]),j(t,"target",window.__is_colab__?"_blank":null),j(t,"download",s=window.__is_colab__?null:i[0].orig_name||i[0].path),j(l,"class","buttons svelte-8r2c23"),j(e,"class","model3D svelte-8r2c23")},m(p,b){K(p,e,b),y(e,l),u&&u.m(l,null),y(l,n),y(l,t),T(a,t,null),y(e,_),d[o].m(e,null),r=!0},p(p,b){p[8]?u&&(J(),z(u,1,1,()=>{u=null}),F()):u?(u.p(p,b),b&256&&w(u,1)):(u=ge(p),u.c(),w(u,1),u.m(l,n));const k={};b&16&&(k.label=p[4]("common.download")),a.$set(k),(!r||b&4096)&&j(t,"href",p[12]),(!r||b&1&&s!==(s=window.__is_colab__?null:p[0].orig_name||p[0].path))&&j(t,"download",s);let S=o;o=C(p),o===S?d[o].p(p,b):(J(),z(d[S],1,1,()=>{d[S]=null}),F(),f=d[o],f?f.p(p,b):(f=d[o]=c[o](p),f.c()),w(f,1),f.m(e,null))},i(p){r||(w(u),w(a.$$.fragment,p),w(f),r=!0)},o(p){z(u),z(a.$$.fragment,p),z(f),r=!1},d(p){p&&H(e),u&&u.d(),O(a),d[o].d()}}}function ge(i){let e,l;return e=new we({props:{Icon:Ae,label:"Undo"}}),e.$on("click",i[15]),{c(){N(e.$$.fragment)},m(n,t){T(e,n,t),l=!0},p:Pe,i(n){l||(w(e.$$.fragment,n),l=!0)},o(n){z(e.$$.fragment,n),l=!1},d(n){O(e,n)}}}function Re(i){let e,l,n,t;function a(o){i[18](o)}var s=i[11];function _(o,f){let r={value:o[0],clear_color:o[1],camera_position:o[7],zoom_speed:o[5],pan_speed:o[6]};return o[12]!==void 0&&(r.resolved_url=o[12]),{props:r}}return s&&(e=x(s,_(i)),i[17](e),W.push(()=>$(e,"resolved_url",a))),{c(){e&&N(e.$$.fragment),n=fe()},m(o,f){e&&T(e,o,f),K(o,n,f),t=!0},p(o,f){if(f&2048&&s!==(s=o[11])){if(e){J();const r=e;z(r.$$.fragment,1,0,()=>{O(r,1)}),F()}s?(e=x(s,_(o)),o[17](e),W.push(()=>$(e,"resolved_url",a)),N(e.$$.fragment),w(e.$$.fragment,1),T(e,n.parentNode,n)):e=null}else if(s){const r={};f&1&&(r.value=o[0]),f&2&&(r.clear_color=o[1]),f&128&&(r.camera_position=o[7]),f&32&&(r.zoom_speed=o[5]),f&64&&(r.pan_speed=o[6]),!l&&f&4096&&(l=!0,r.resolved_url=o[12],ve(()=>l=!1)),e.$set(r)}},i(o){t||(e&&w(e.$$.fragment,o),t=!0)},o(o){e&&z(e.$$.fragment,o),t=!1},d(o){o&&H(n),i[17](null),e&&O(e,o)}}}function Ge(i){let e,l,n,t;function a(o){i[16](o)}var s=i[10];function _(o,f){let r={value:o[0],zoom_speed:o[5],pan_speed:o[6]};return o[12]!==void 0&&(r.resolved_url=o[12]),{props:r}}return s&&(e=x(s,_(i)),W.push(()=>$(e,"resolved_url",a))),{c(){e&&N(e.$$.fragment),n=fe()},m(o,f){e&&T(e,o,f),K(o,n,f),t=!0},p(o,f){if(f&1024&&s!==(s=o[10])){if(e){J();const r=e;z(r.$$.fragment,1,0,()=>{O(r,1)}),F()}s?(e=x(s,_(o)),W.push(()=>$(e,"resolved_url",a)),N(e.$$.fragment),w(e.$$.fragment,1),T(e,n.parentNode,n)):e=null}else if(s){const r={};f&1&&(r.value=o[0]),f&32&&(r.zoom_speed=o[5]),f&64&&(r.pan_speed=o[6]),!l&&f&4096&&(l=!0,r.resolved_url=o[12],ve(()=>l=!1)),e.$set(r)}},i(o){t||(e&&w(e.$$.fragment,o),t=!0)},o(o){e&&z(e.$$.fragment,o),t=!1},d(o){o&&H(n),e&&O(e,o)}}}function We(i){let e,l,n,t;e=new _e({props:{show_label:i[3],Icon:le,label:i[2]||i[4]("3D_model.3d_model")}});let a=i[0]&&pe(i);return{c(){N(e.$$.fragment),l=re(),a&&a.c(),n=fe()},m(s,_){T(e,s,_),K(s,l,_),a&&a.m(s,_),K(s,n,_),t=!0},p(s,[_]){const o={};_&8&&(o.show_label=s[3]),_&20&&(o.label=s[2]||s[4]("3D_model.3d_model")),e.$set(o),s[0]?a?(a.p(s,_),_&1&&w(a,1)):(a=pe(s),a.c(),w(a,1),a.m(n.parentNode,n)):a&&(J(),z(a,1,1,()=>{a=null}),F())},i(s){t||(w(e.$$.fragment,s),w(a),t=!0)},o(s){z(e.$$.fragment,s),z(a),t=!1},d(s){s&&(H(l),H(n)),O(e,s),a&&a.d(s)}}}async function Fe(){return(await ne(()=>import("./Canvas3D-9fedc756.js"),["./Canvas3D-9fedc756.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./file-url-bef2dc1b.js"],import.meta.url)).default}async function He(){return(await ne(()=>import("./Canvas3DGS-56c086ac.js"),["./Canvas3DGS-56c086ac.js","./file-url-bef2dc1b.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css"],import.meta.url)).default}function Je(i,e,l){let{value:n}=e,{clear_color:t=[0,0,0,0]}=e,{label:a=""}=e,{show_label:s}=e,{i18n:_}=e,{zoom_speed:o=1}=e,{pan_speed:f=1}=e,{camera_position:r=[null,null,null]}=e,u={camera_position:r,zoom_speed:o,pan_speed:f},c=!1,d,C,p;function b(){p?.reset_camera_position(r,o,f)}let k;const S=()=>b();function A(g){k=g,l(12,k)}function R(g){W[g?"unshift":"push"](()=>{p=g,l(9,p)})}function L(g){k=g,l(12,k)}return i.$$set=g=>{"value"in g&&l(0,n=g.value),"clear_color"in g&&l(1,t=g.clear_color),"label"in g&&l(2,a=g.label),"show_label"in g&&l(3,s=g.show_label),"i18n"in g&&l(4,_=g.i18n),"zoom_speed"in g&&l(5,o=g.zoom_speed),"pan_speed"in g&&l(6,f=g.pan_speed),"camera_position"in g&&l(7,r=g.camera_position)},i.$$.update=()=>{i.$$.dirty&257&&n&&(l(8,c=n.path.endsWith(".splat")||n.path.endsWith(".ply")),c?He().then(g=>{l(10,d=g)}):Fe().then(g=>{l(11,C=g)})),i.$$.dirty&17120&&(!G(u.camera_position,r)||u.zoom_speed!==o||u.pan_speed!==f)&&(p?.reset_camera_position(r,o,f),l(14,u={camera_position:r,zoom_speed:o,pan_speed:f}))},[n,t,a,s,_,o,f,r,c,p,d,C,k,b,u,S,A,R,L]}class Ke extends Te{constructor(e){super(),qe(this,e,Je,We,Ve,{value:0,clear_color:1,label:2,show_label:3,i18n:4,zoom_speed:5,pan_speed:6,camera_position:7})}}const Qe=Ke;const{SvelteComponent:Xe,add_flush_callback:Ye,append:Ze,attr:ye,bind:$e,binding_callbacks:De,check_outros:te,construct_svelte_component:ee,create_component:q,create_slot:xe,destroy_component:P,detach:Q,element:en,empty:ue,get_all_dirty_from_scope:nn,get_slot_changes:ln,group_outros:oe,init:tn,insert:X,mount_component:V,safe_not_equal:on,space:ze,transition_in:M,transition_out:I,update_slot_base:sn}=window.__gradio__svelte__internal,{createEventDispatcher:an,tick:he}=window.__gradio__svelte__internal;function rn(i){let e,l,n,t,a,s;l=new je({props:{undoable:!i[9],i18n:i[5],absolute:!0}}),l.$on("clear",i[15]),l.$on("undo",i[16]);const _=[un,fn],o=[];function f(r,u){return r[9]?0:1}return t=f(i),a=o[t]=_[t](i),{c(){e=en("div"),q(l.$$.fragment),n=ze(),a.c(),ye(e,"class","input-model svelte-hvduv8")},m(r,u){X(r,e,u),V(l,e,null),Ze(e,n),o[t].m(e,null),s=!0},p(r,u){const c={};u&512&&(c.undoable=!r[9]),u&32&&(c.i18n=r[5]),l.$set(c);let d=t;t=f(r),t===d?o[t].p(r,u):(oe(),I(o[d],1,1,()=>{o[d]=null}),te(),a=o[t],a?a.p(r,u):(a=o[t]=_[t](r),a.c()),M(a,1),a.m(e,null))},i(r){s||(M(l.$$.fragment,r),M(a),s=!0)},o(r){I(l.$$.fragment,r),I(a),s=!1},d(r){r&&Q(e),P(l),o[t].d()}}}function _n(i){let e,l,n;function t(s){i[18](s)}let a={root:i[4],filetype:[".stl",".obj",".gltf",".glb","model/obj",".splat",".ply"],$$slots:{default:[cn]},$$scope:{ctx:i}};return i[10]!==void 0&&(a.dragging=i[10]),e=new Le({props:a}),De.push(()=>$e(e,"dragging",t)),e.$on("load",i[14]),{c(){q(e.$$.fragment)},m(s,_){V(e,s,_),n=!0},p(s,_){const o={};_&16&&(o.root=s[4]),_&1048576&&(o.$$scope={dirty:_,ctx:s}),!l&&_&1024&&(l=!0,o.dragging=s[10],Ye(()=>l=!1)),e.$set(o)},i(s){n||(M(e.$$.fragment,s),n=!0)},o(s){I(e.$$.fragment,s),n=!1},d(s){P(e,s)}}}function fn(i){let e,l,n;var t=i[12];function a(s,_){return{props:{value:s[0],clear_color:s[1],camera_position:s[8],zoom_speed:s[6],pan_speed:s[7]}}}return t&&(e=ee(t,a(i)),i[19](e)),{c(){e&&q(e.$$.fragment),l=ue()},m(s,_){e&&V(e,s,_),X(s,l,_),n=!0},p(s,_){if(_&4096&&t!==(t=s[12])){if(e){oe();const o=e;I(o.$$.fragment,1,0,()=>{P(o,1)}),te()}t?(e=ee(t,a(s)),s[19](e),q(e.$$.fragment),M(e.$$.fragment,1),V(e,l.parentNode,l)):e=null}else if(t){const o={};_&1&&(o.value=s[0]),_&2&&(o.clear_color=s[1]),_&256&&(o.camera_position=s[8]),_&64&&(o.zoom_speed=s[6]),_&128&&(o.pan_speed=s[7]),e.$set(o)}},i(s){n||(e&&M(e.$$.fragment,s),n=!0)},o(s){e&&I(e.$$.fragment,s),n=!1},d(s){s&&Q(l),i[19](null),e&&P(e,s)}}}function un(i){let e,l,n;var t=i[11];function a(s,_){return{props:{value:s[0],zoom_speed:s[6],pan_speed:s[7]}}}return t&&(e=ee(t,a(i))),{c(){e&&q(e.$$.fragment),l=ue()},m(s,_){e&&V(e,s,_),X(s,l,_),n=!0},p(s,_){if(_&2048&&t!==(t=s[11])){if(e){oe();const o=e;I(o.$$.fragment,1,0,()=>{P(o,1)}),te()}t?(e=ee(t,a(s)),q(e.$$.fragment),M(e.$$.fragment,1),V(e,l.parentNode,l)):e=null}else if(t){const o={};_&1&&(o.value=s[0]),_&64&&(o.zoom_speed=s[6]),_&128&&(o.pan_speed=s[7]),e.$set(o)}},i(s){n||(e&&M(e.$$.fragment,s),n=!0)},o(s){e&&I(e.$$.fragment,s),n=!1},d(s){s&&Q(l),e&&P(e,s)}}}function cn(i){let e;const l=i[17].default,n=xe(l,i,i[20],null);return{c(){n&&n.c()},m(t,a){n&&n.m(t,a),e=!0},p(t,a){n&&n.p&&(!e||a&1048576)&&sn(n,l,t,t[20],e?ln(l,t[20],a,null):nn(t[20]),null)},i(t){e||(M(n,t),e=!0)},o(t){I(n,t),e=!1},d(t){n&&n.d(t)}}}function mn(i){let e,l,n,t,a,s;e=new _e({props:{show_label:i[3],Icon:le,label:i[2]||"3D Model"}});const _=[_n,rn],o=[];function f(r,u){return r[0]===null?0:1}return n=f(i),t=o[n]=_[n](i),{c(){q(e.$$.fragment),l=ze(),t.c(),a=ue()},m(r,u){V(e,r,u),X(r,l,u),o[n].m(r,u),X(r,a,u),s=!0},p(r,[u]){const c={};u&8&&(c.show_label=r[3]),u&4&&(c.label=r[2]||"3D Model"),e.$set(c);let d=n;n=f(r),n===d?o[n].p(r,u):(oe(),I(o[d],1,1,()=>{o[d]=null}),te(),t=o[n],t?t.p(r,u):(t=o[n]=_[n](r),t.c()),M(t,1),t.m(a.parentNode,a))},i(r){s||(M(e.$$.fragment,r),M(t),s=!0)},o(r){I(e.$$.fragment,r),I(t),s=!1},d(r){r&&(Q(l),Q(a)),P(e,r),o[n].d(r)}}}async function dn(){return(await ne(()=>import("./Canvas3D-9fedc756.js"),["./Canvas3D-9fedc756.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./file-url-bef2dc1b.js"],import.meta.url)).default}async function pn(){return(await ne(()=>import("./Canvas3DGS-56c086ac.js"),["./Canvas3DGS-56c086ac.js","./file-url-bef2dc1b.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css"],import.meta.url)).default}function gn(i,e,l){let{$$slots:n={},$$scope:t}=e,{value:a}=e,{clear_color:s=[0,0,0,0]}=e,{label:_=""}=e,{show_label:o}=e,{root:f}=e,{i18n:r}=e,{zoom_speed:u=1}=e,{pan_speed:c=1}=e,{camera_position:d=[null,null,null]}=e;async function C({detail:h}){l(0,a=h),await he(),L("change",a),L("load",a)}async function p(){l(0,a=null),await he(),L("clear"),L("change")}let b=!1,k,S,A;async function R(){A?.reset_camera_position(d,u,c)}const L=an();let g=!1;function ie(h){g=h,l(10,g)}function se(h){De[h?"unshift":"push"](()=>{A=h,l(13,A)})}return i.$$set=h=>{"value"in h&&l(0,a=h.value),"clear_color"in h&&l(1,s=h.clear_color),"label"in h&&l(2,_=h.label),"show_label"in h&&l(3,o=h.show_label),"root"in h&&l(4,f=h.root),"i18n"in h&&l(5,r=h.i18n),"zoom_speed"in h&&l(6,u=h.zoom_speed),"pan_speed"in h&&l(7,c=h.pan_speed),"camera_position"in h&&l(8,d=h.camera_position),"$$scope"in h&&l(20,t=h.$$scope)},i.$$.update=()=>{i.$$.dirty&513&&a&&(l(9,b=a.path.endsWith(".splat")||a.path.endsWith(".ply")),b?pn().then(h=>{l(11,k=h)}):dn().then(h=>{l(12,S=h)})),i.$$.dirty&1024&&L("drag",g)},[a,s,_,o,f,r,u,c,d,b,g,k,S,A,C,p,R,n,ie,se,t]}class hn extends Xe{constructor(e){super(),tn(this,e,gn,mn,on,{value:0,clear_color:1,label:2,show_label:3,root:4,i18n:5,zoom_speed:6,pan_speed:7,camera_position:8})}}const bn=hn,{SvelteComponent:wn,assign:Ce,check_outros:Me,create_component:U,destroy_component:E,detach:Y,empty:Ie,get_spread_object:Se,get_spread_update:Ue,group_outros:Ee,init:kn,insert:Z,mount_component:B,safe_not_equal:vn,space:ce,transition_in:v,transition_out:D}=window.__gradio__svelte__internal;function Dn(i){let e,l;return e=new be({props:{visible:i[3],variant:i[0]===null?"dashed":"solid",border_mode:i[17]?"focus":"base",padding:!1,elem_id:i[1],elem_classes:i[2],container:i[9],scale:i[10],min_width:i[11],height:i[13],$$slots:{default:[Mn]},$$scope:{ctx:i}}}),{c(){U(e.$$.fragment)},m(n,t){B(e,n,t),l=!0},p(n,t){const a={};t&8&&(a.visible=n[3]),t&1&&(a.variant=n[0]===null?"dashed":"solid"),t&131072&&(a.border_mode=n[17]?"focus":"base"),t&2&&(a.elem_id=n[1]),t&4&&(a.elem_classes=n[2]),t&512&&(a.container=n[9]),t&1024&&(a.scale=n[10]),t&2048&&(a.min_width=n[11]),t&8192&&(a.height=n[13]),t&8573425&&(a.$$scope={dirty:t,ctx:n}),e.$set(a)},i(n){l||(v(e.$$.fragment,n),l=!0)},o(n){D(e.$$.fragment,n),l=!1},d(n){E(e,n)}}}function zn(i){let e,l;return e=new be({props:{visible:i[3],variant:i[0]===null?"dashed":"solid",border_mode:i[17]?"focus":"base",padding:!1,elem_id:i[1],elem_classes:i[2],container:i[9],scale:i[10],min_width:i[11],height:i[13],$$slots:{default:[En]},$$scope:{ctx:i}}}),{c(){U(e.$$.fragment)},m(n,t){B(e,n,t),l=!0},p(n,t){const a={};t&8&&(a.visible=n[3]),t&1&&(a.variant=n[0]===null?"dashed":"solid"),t&131072&&(a.border_mode=n[17]?"focus":"base"),t&2&&(a.elem_id=n[1]),t&4&&(a.elem_classes=n[2]),t&512&&(a.container=n[9]),t&1024&&(a.scale=n[10]),t&2048&&(a.min_width=n[11]),t&8192&&(a.height=n[13]),t&8442337&&(a.$$scope={dirty:t,ctx:n}),e.$set(a)},i(n){l||(v(e.$$.fragment,n),l=!0)},o(n){D(e.$$.fragment,n),l=!1},d(n){E(e,n)}}}function Cn(i){let e,l;return e=new Oe({props:{i18n:i[12].i18n,type:"file"}}),{c(){U(e.$$.fragment)},m(n,t){B(e,n,t),l=!0},p(n,t){const a={};t&4096&&(a.i18n=n[12].i18n),e.$set(a)},i(n){l||(v(e.$$.fragment,n),l=!0)},o(n){D(e.$$.fragment,n),l=!1},d(n){E(e,n)}}}function Mn(i){let e,l,n,t;const a=[{autoscroll:i[12].autoscroll},{i18n:i[12].i18n},i[6]];let s={};for(let _=0;_<a.length;_+=1)s=Ce(s,a[_]);return e=new ke({props:s}),n=new bn({props:{label:i[7],show_label:i[8],root:i[4],clear_color:i[5],value:i[0],camera_position:i[15],zoom_speed:i[14],i18n:i[12].i18n,$$slots:{default:[Cn]},$$scope:{ctx:i}}}),n.$on("change",i[18]),n.$on("drag",i[19]),n.$on("change",i[20]),n.$on("clear",i[21]),n.$on("load",i[22]),{c(){U(e.$$.fragment),l=ce(),U(n.$$.fragment)},m(_,o){B(e,_,o),Z(_,l,o),B(n,_,o),t=!0},p(_,o){const f=o&4160?Ue(a,[o&4096&&{autoscroll:_[12].autoscroll},o&4096&&{i18n:_[12].i18n},o&64&&Se(_[6])]):{};e.$set(f);const r={};o&128&&(r.label=_[7]),o&256&&(r.show_label=_[8]),o&16&&(r.root=_[4]),o&32&&(r.clear_color=_[5]),o&1&&(r.value=_[0]),o&32768&&(r.camera_position=_[15]),o&16384&&(r.zoom_speed=_[14]),o&4096&&(r.i18n=_[12].i18n),o&8392704&&(r.$$scope={dirty:o,ctx:_}),n.$set(r)},i(_){t||(v(e.$$.fragment,_),v(n.$$.fragment,_),t=!0)},o(_){D(e.$$.fragment,_),D(n.$$.fragment,_),t=!1},d(_){_&&Y(l),E(e,_),E(n,_)}}}function In(i){let e,l,n,t;return e=new _e({props:{show_label:i[8],Icon:le,label:i[7]||"3D Model"}}),n=new Ne({props:{unpadded_box:!0,size:"large",$$slots:{default:[Un]},$$scope:{ctx:i}}}),{c(){U(e.$$.fragment),l=ce(),U(n.$$.fragment)},m(a,s){B(e,a,s),Z(a,l,s),B(n,a,s),t=!0},p(a,s){const _={};s&256&&(_.show_label=a[8]),s&128&&(_.label=a[7]||"3D Model"),e.$set(_);const o={};s&8388608&&(o.$$scope={dirty:s,ctx:a}),n.$set(o)},i(a){t||(v(e.$$.fragment,a),v(n.$$.fragment,a),t=!0)},o(a){D(e.$$.fragment,a),D(n.$$.fragment,a),t=!1},d(a){a&&Y(l),E(e,a),E(n,a)}}}function Sn(i){let e,l;return e=new Qe({props:{value:i[0],i18n:i[12].i18n,clear_color:i[5],label:i[7],show_label:i[8],camera_position:i[15],zoom_speed:i[14]}}),{c(){U(e.$$.fragment)},m(n,t){B(e,n,t),l=!0},p(n,t){const a={};t&1&&(a.value=n[0]),t&4096&&(a.i18n=n[12].i18n),t&32&&(a.clear_color=n[5]),t&128&&(a.label=n[7]),t&256&&(a.show_label=n[8]),t&32768&&(a.camera_position=n[15]),t&16384&&(a.zoom_speed=n[14]),e.$set(a)},i(n){l||(v(e.$$.fragment,n),l=!0)},o(n){D(e.$$.fragment,n),l=!1},d(n){E(e,n)}}}function Un(i){let e,l;return e=new le({}),{c(){U(e.$$.fragment)},m(n,t){B(e,n,t),l=!0},i(n){l||(v(e.$$.fragment,n),l=!0)},o(n){D(e.$$.fragment,n),l=!1},d(n){E(e,n)}}}function En(i){let e,l,n,t,a,s;const _=[{autoscroll:i[12].autoscroll},{i18n:i[12].i18n},i[6]];let o={};for(let c=0;c<_.length;c+=1)o=Ce(o,_[c]);e=new ke({props:o});const f=[Sn,In],r=[];function u(c,d){return c[0]?0:1}return n=u(i),t=r[n]=f[n](i),{c(){U(e.$$.fragment),l=ce(),t.c(),a=Ie()},m(c,d){B(e,c,d),Z(c,l,d),r[n].m(c,d),Z(c,a,d),s=!0},p(c,d){const C=d&4160?Ue(_,[d&4096&&{autoscroll:c[12].autoscroll},d&4096&&{i18n:c[12].i18n},d&64&&Se(c[6])]):{};e.$set(C);let p=n;n=u(c),n===p?r[n].p(c,d):(Ee(),D(r[p],1,1,()=>{r[p]=null}),Me(),t=r[n],t?t.p(c,d):(t=r[n]=f[n](c),t.c()),v(t,1),t.m(a.parentNode,a))},i(c){s||(v(e.$$.fragment,c),v(t),s=!0)},o(c){D(e.$$.fragment,c),D(t),s=!1},d(c){c&&(Y(l),Y(a)),E(e,c),r[n].d(c)}}}function Bn(i){let e,l,n,t;const a=[zn,Dn],s=[];function _(o,f){return o[16]?1:0}return e=_(i),l=s[e]=a[e](i),{c(){l.c(),n=Ie()},m(o,f){s[e].m(o,f),Z(o,n,f),t=!0},p(o,[f]){let r=e;e=_(o),e===r?s[e].p(o,f):(Ee(),D(s[r],1,1,()=>{s[r]=null}),Me(),l=s[e],l?l.p(o,f):(l=s[e]=a[e](o),l.c()),v(l,1),l.m(n.parentNode,n))},i(o){t||(v(l),t=!0)},o(o){D(l),t=!1},d(o){o&&Y(n),s[e].d(o)}}}function An(i,e,l){let{elem_id:n=""}=e,{elem_classes:t=[]}=e,{visible:a=!0}=e,{value:s=null}=e,{root:_}=e,{clear_color:o}=e,{loading_status:f}=e,{label:r}=e,{show_label:u}=e,{container:c=!0}=e,{scale:d=null}=e,{min_width:C=void 0}=e,{gradio:p}=e,{height:b=void 0}=e,{zoom_speed:k=1}=e,{camera_position:S=[null,null,null]}=e,{interactive:A}=e,R=!1;const L=({detail:m})=>l(0,s=m),g=({detail:m})=>l(17,R=m),ie=({detail:m})=>p.dispatch("change",m),se=()=>{l(0,s=null),p.dispatch("clear")},h=({detail:m})=>{l(0,s=m),p.dispatch("upload")};return i.$$set=m=>{"elem_id"in m&&l(1,n=m.elem_id),"elem_classes"in m&&l(2,t=m.elem_classes),"visible"in m&&l(3,a=m.visible),"value"in m&&l(0,s=m.value),"root"in m&&l(4,_=m.root),"clear_color"in m&&l(5,o=m.clear_color),"loading_status"in m&&l(6,f=m.loading_status),"label"in m&&l(7,r=m.label),"show_label"in m&&l(8,u=m.show_label),"container"in m&&l(9,c=m.container),"scale"in m&&l(10,d=m.scale),"min_width"in m&&l(11,C=m.min_width),"gradio"in m&&l(12,p=m.gradio),"height"in m&&l(13,b=m.height),"zoom_speed"in m&&l(14,k=m.zoom_speed),"camera_position"in m&&l(15,S=m.camera_position),"interactive"in m&&l(16,A=m.interactive)},[s,n,t,a,_,o,f,r,u,c,d,C,p,b,k,S,A,R,L,g,ie,se,h]}class Xn extends wn{constructor(e){super(),kn(this,e,An,Bn,vn,{elem_id:1,elem_classes:2,visible:3,value:0,root:4,clear_color:5,loading_status:6,label:7,show_label:8,container:9,scale:10,min_width:11,gradio:12,height:13,zoom_speed:14,camera_position:15,interactive:16})}}export{yn as BaseExample,Qe as BaseModel3D,bn as BaseModel3DUpload,Xn as default};
//# sourceMappingURL=Index-2cd93917.js.map
