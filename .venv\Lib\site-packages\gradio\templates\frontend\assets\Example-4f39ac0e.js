const{SvelteComponent:o,attr:g,detach:m,element:y,init:r,insert:v,noop:_,safe_not_equal:h,toggle_class:i}=window.__gradio__svelte__internal;function b(a){let e;return{c(){e=y("div"),e.textContent=`${a[2]}`,g(e,"class","svelte-1ayixqk"),i(e,"table",a[0]==="table"),i(e,"gallery",a[0]==="gallery"),i(e,"selected",a[1])},m(t,n){v(t,e,n)},p(t,[n]){n&1&&i(e,"table",t[0]==="table"),n&1&&i(e,"gallery",t[0]==="gallery"),n&2&&i(e,"selected",t[1])},i:_,o:_,d(t){t&&m(e)}}}function q(a,e,t){let{value:n}=e,{type:c}=e,{selected:f=!1}=e,{choices:s}=e,u=n.map(l=>s.find(d=>d[1]===l)?.[0]).filter(l=>l!==void 0).join(", ");return a.$$set=l=>{"value"in l&&t(3,n=l.value),"type"in l&&t(0,c=l.type),"selected"in l&&t(1,f=l.selected),"choices"in l&&t(4,s=l.choices)},[c,f,u,n,s]}class C extends o{constructor(e){super(),r(this,e,q,b,h,{value:3,type:0,selected:1,choices:4})}}export{C as default};
//# sourceMappingURL=Example-4f39ac0e.js.map
