{"version": 3, "file": "dsv-576afacd.js", "sources": ["../../../../node_modules/.pnpm/d3-dsv@3.0.1/node_modules/d3-dsv/src/dsv.js"], "sourcesContent": ["var EOL = {},\n    EOF = {},\n    QUOTE = 34,\n    NEWLINE = 10,\n    RETURN = 13;\n\nfunction objectConverter(columns) {\n  return new Function(\"d\", \"return {\" + columns.map(function(name, i) {\n    return JSON.stringify(name) + \": d[\" + i + \"] || \\\"\\\"\";\n  }).join(\",\") + \"}\");\n}\n\nfunction customConverter(columns, f) {\n  var object = objectConverter(columns);\n  return function(row, i) {\n    return f(object(row), i, columns);\n  };\n}\n\n// Compute unique columns in order of discovery.\nfunction inferColumns(rows) {\n  var columnSet = Object.create(null),\n      columns = [];\n\n  rows.forEach(function(row) {\n    for (var column in row) {\n      if (!(column in columnSet)) {\n        columns.push(columnSet[column] = column);\n      }\n    }\n  });\n\n  return columns;\n}\n\nfunction pad(value, width) {\n  var s = value + \"\", length = s.length;\n  return length < width ? new Array(width - length + 1).join(0) + s : s;\n}\n\nfunction formatYear(year) {\n  return year < 0 ? \"-\" + pad(-year, 6)\n    : year > 9999 ? \"+\" + pad(year, 6)\n    : pad(year, 4);\n}\n\nfunction formatDate(date) {\n  var hours = date.getUTCHours(),\n      minutes = date.getUTCMinutes(),\n      seconds = date.getUTCSeconds(),\n      milliseconds = date.getUTCMilliseconds();\n  return isNaN(date) ? \"Invalid Date\"\n      : formatYear(date.getUTCFullYear(), 4) + \"-\" + pad(date.getUTCMonth() + 1, 2) + \"-\" + pad(date.getUTCDate(), 2)\n      + (milliseconds ? \"T\" + pad(hours, 2) + \":\" + pad(minutes, 2) + \":\" + pad(seconds, 2) + \".\" + pad(milliseconds, 3) + \"Z\"\n      : seconds ? \"T\" + pad(hours, 2) + \":\" + pad(minutes, 2) + \":\" + pad(seconds, 2) + \"Z\"\n      : minutes || hours ? \"T\" + pad(hours, 2) + \":\" + pad(minutes, 2) + \"Z\"\n      : \"\");\n}\n\nexport default function(delimiter) {\n  var reFormat = new RegExp(\"[\\\"\" + delimiter + \"\\n\\r]\"),\n      DELIMITER = delimiter.charCodeAt(0);\n\n  function parse(text, f) {\n    var convert, columns, rows = parseRows(text, function(row, i) {\n      if (convert) return convert(row, i - 1);\n      columns = row, convert = f ? customConverter(row, f) : objectConverter(row);\n    });\n    rows.columns = columns || [];\n    return rows;\n  }\n\n  function parseRows(text, f) {\n    var rows = [], // output rows\n        N = text.length,\n        I = 0, // current character index\n        n = 0, // current line number\n        t, // current token\n        eof = N <= 0, // current token followed by EOF?\n        eol = false; // current token followed by EOL?\n\n    // Strip the trailing newline.\n    if (text.charCodeAt(N - 1) === NEWLINE) --N;\n    if (text.charCodeAt(N - 1) === RETURN) --N;\n\n    function token() {\n      if (eof) return EOF;\n      if (eol) return eol = false, EOL;\n\n      // Unescape quotes.\n      var i, j = I, c;\n      if (text.charCodeAt(j) === QUOTE) {\n        while (I++ < N && text.charCodeAt(I) !== QUOTE || text.charCodeAt(++I) === QUOTE);\n        if ((i = I) >= N) eof = true;\n        else if ((c = text.charCodeAt(I++)) === NEWLINE) eol = true;\n        else if (c === RETURN) { eol = true; if (text.charCodeAt(I) === NEWLINE) ++I; }\n        return text.slice(j + 1, i - 1).replace(/\"\"/g, \"\\\"\");\n      }\n\n      // Find next delimiter or newline.\n      while (I < N) {\n        if ((c = text.charCodeAt(i = I++)) === NEWLINE) eol = true;\n        else if (c === RETURN) { eol = true; if (text.charCodeAt(I) === NEWLINE) ++I; }\n        else if (c !== DELIMITER) continue;\n        return text.slice(j, i);\n      }\n\n      // Return last token before EOF.\n      return eof = true, text.slice(j, N);\n    }\n\n    while ((t = token()) !== EOF) {\n      var row = [];\n      while (t !== EOL && t !== EOF) row.push(t), t = token();\n      if (f && (row = f(row, n++)) == null) continue;\n      rows.push(row);\n    }\n\n    return rows;\n  }\n\n  function preformatBody(rows, columns) {\n    return rows.map(function(row) {\n      return columns.map(function(column) {\n        return formatValue(row[column]);\n      }).join(delimiter);\n    });\n  }\n\n  function format(rows, columns) {\n    if (columns == null) columns = inferColumns(rows);\n    return [columns.map(formatValue).join(delimiter)].concat(preformatBody(rows, columns)).join(\"\\n\");\n  }\n\n  function formatBody(rows, columns) {\n    if (columns == null) columns = inferColumns(rows);\n    return preformatBody(rows, columns).join(\"\\n\");\n  }\n\n  function formatRows(rows) {\n    return rows.map(formatRow).join(\"\\n\");\n  }\n\n  function formatRow(row) {\n    return row.map(formatValue).join(delimiter);\n  }\n\n  function formatValue(value) {\n    return value == null ? \"\"\n        : value instanceof Date ? formatDate(value)\n        : reFormat.test(value += \"\") ? \"\\\"\" + value.replace(/\"/g, \"\\\"\\\"\") + \"\\\"\"\n        : value;\n  }\n\n  return {\n    parse: parse,\n    parseRows: parseRows,\n    format: format,\n    formatBody: formatBody,\n    formatRows: formatRows,\n    formatRow: formatRow,\n    formatValue: formatValue\n  };\n}\n"], "names": ["EOL", "EOF", "QUOTE", "NEWLINE", "RETURN", "objectConverter", "columns", "name", "i", "customConverter", "f", "object", "row", "inferColumns", "rows", "columnSet", "column", "pad", "value", "width", "s", "length", "formatYear", "year", "formatDate", "date", "hours", "minutes", "seconds", "milliseconds", "dsvFormat", "delimiter", "reFormat", "DELIMITER", "parse", "text", "convert", "parseRows", "N", "I", "n", "t", "eof", "eol", "token", "j", "c", "preformatBody", "formatValue", "format", "formatBody", "formatRows", "formatRow"], "mappings": "AAAA,IAAIA,EAAM,CAAE,EACRC,EAAM,CAAE,EACRC,EAAQ,GACRC,EAAU,GACVC,EAAS,GAEb,SAASC,EAAgBC,EAAS,CAChC,OAAO,IAAI,SAAS,IAAK,WAAaA,EAAQ,IAAI,SAASC,EAAMC,EAAG,CAClE,OAAO,KAAK,UAAUD,CAAI,EAAI,OAASC,EAAI,SAC5C,CAAA,EAAE,KAAK,GAAG,EAAI,GAAG,CACpB,CAEA,SAASC,EAAgBH,EAASI,EAAG,CACnC,IAAIC,EAASN,EAAgBC,CAAO,EACpC,OAAO,SAASM,EAAKJ,EAAG,CACtB,OAAOE,EAAEC,EAAOC,CAAG,EAAGJ,EAAGF,CAAO,CACpC,CACA,CAGA,SAASO,EAAaC,EAAM,CAC1B,IAAIC,EAAY,OAAO,OAAO,IAAI,EAC9BT,EAAU,CAAA,EAEd,OAAAQ,EAAK,QAAQ,SAASF,EAAK,CACzB,QAASI,KAAUJ,EACXI,KAAUD,GACdT,EAAQ,KAAKS,EAAUC,CAAM,EAAIA,CAAM,CAG/C,CAAG,EAEMV,CACT,CAEA,SAASW,EAAIC,EAAOC,EAAO,CACzB,IAAIC,EAAIF,EAAQ,GAAIG,EAASD,EAAE,OAC/B,OAAOC,EAASF,EAAQ,IAAI,MAAMA,EAAQE,EAAS,CAAC,EAAE,KAAK,CAAC,EAAID,EAAIA,CACtE,CAEA,SAASE,EAAWC,EAAM,CACxB,OAAOA,EAAO,EAAI,IAAMN,EAAI,CAACM,EAAM,CAAC,EAChCA,EAAO,KAAO,IAAMN,EAAIM,EAAM,CAAC,EAC/BN,EAAIM,EAAM,CAAC,CACjB,CAEA,SAASC,EAAWC,EAAM,CACxB,IAAIC,EAAQD,EAAK,YAAa,EAC1BE,EAAUF,EAAK,cAAe,EAC9BG,EAAUH,EAAK,cAAe,EAC9BI,EAAeJ,EAAK,qBACxB,OAAO,MAAMA,CAAI,EAAI,eACfH,EAAWG,EAAK,eAAmB,CAAA,EAAI,IAAMR,EAAIQ,EAAK,YAAW,EAAK,EAAG,CAAC,EAAI,IAAMR,EAAIQ,EAAK,WAAY,EAAE,CAAC,GAC3GI,EAAe,IAAMZ,EAAIS,EAAO,CAAC,EAAI,IAAMT,EAAIU,EAAS,CAAC,EAAI,IAAMV,EAAIW,EAAS,CAAC,EAAI,IAAMX,EAAIY,EAAc,CAAC,EAAI,IACnHD,EAAU,IAAMX,EAAIS,EAAO,CAAC,EAAI,IAAMT,EAAIU,EAAS,CAAC,EAAI,IAAMV,EAAIW,EAAS,CAAC,EAAI,IAChFD,GAAWD,EAAQ,IAAMT,EAAIS,EAAO,CAAC,EAAI,IAAMT,EAAIU,EAAS,CAAC,EAAI,IACjE,GACR,CAEe,SAAQG,EAACC,EAAW,CACjC,IAAIC,EAAW,IAAI,OAAO,KAAQD,EAAY;AAAA,IAAO,EACjDE,EAAYF,EAAU,WAAW,CAAC,EAEtC,SAASG,EAAMC,EAAMzB,EAAG,CACtB,IAAI0B,EAAS9B,EAASQ,EAAOuB,EAAUF,EAAM,SAASvB,EAAKJ,EAAG,CAC5D,GAAI4B,EAAS,OAAOA,EAAQxB,EAAKJ,EAAI,CAAC,EACtCF,EAAUM,EAAKwB,EAAU1B,EAAID,EAAgBG,EAAKF,CAAC,EAAIL,EAAgBO,CAAG,CAChF,CAAK,EACD,OAAAE,EAAK,QAAUR,GAAW,GACnBQ,CACR,CAED,SAASuB,EAAUF,EAAMzB,EAAG,CAC1B,IAAII,EAAO,CAAE,EACTwB,EAAIH,EAAK,OACTI,EAAI,EACJC,EAAI,EACJC,EACAC,EAAMJ,GAAK,EACXK,EAAM,GAGNR,EAAK,WAAWG,EAAI,CAAC,IAAMnC,GAAS,EAAEmC,EACtCH,EAAK,WAAWG,EAAI,CAAC,IAAMlC,GAAQ,EAAEkC,EAEzC,SAASM,GAAQ,CACf,GAAIF,EAAK,OAAOzC,EAChB,GAAI0C,EAAK,OAAOA,EAAM,GAAO3C,EAG7B,IAAIQ,EAAGqC,EAAIN,EAAGO,EACd,GAAIX,EAAK,WAAWU,CAAC,IAAM3C,EAAO,CAChC,KAAOqC,IAAMD,GAAKH,EAAK,WAAWI,CAAC,IAAMrC,GAASiC,EAAK,WAAW,EAAEI,CAAC,IAAMrC,GAAM,CACjF,OAAKM,EAAI+B,IAAMD,EAAGI,EAAM,IACdI,EAAIX,EAAK,WAAWI,GAAG,KAAOpC,EAASwC,EAAM,GAC9CG,IAAM1C,IAAUuC,EAAM,GAAUR,EAAK,WAAWI,CAAC,IAAMpC,GAAS,EAAEoC,GACpEJ,EAAK,MAAMU,EAAI,EAAGrC,EAAI,CAAC,EAAE,QAAQ,MAAO,GAAI,EAIrD,KAAO+B,EAAID,GAAG,CACZ,IAAKQ,EAAIX,EAAK,WAAW3B,EAAI+B,GAAG,KAAOpC,EAASwC,EAAM,WAC7CG,IAAM1C,EAAUuC,EAAM,GAAUR,EAAK,WAAWI,CAAC,IAAMpC,GAAS,EAAEoC,UAClEO,IAAMb,EAAW,SAC1B,OAAOE,EAAK,MAAMU,EAAGrC,CAAC,EAIxB,OAAOkC,EAAM,GAAMP,EAAK,MAAMU,EAAGP,CAAC,CACnC,CAED,MAAQG,EAAIG,EAAO,KAAM3C,GAAK,CAE5B,QADIW,EAAM,CAAA,EACH6B,IAAMzC,GAAOyC,IAAMxC,GAAKW,EAAI,KAAK6B,CAAC,EAAGA,EAAIG,IAC5ClC,IAAME,EAAMF,EAAEE,EAAK4B,GAAG,IAAM,MAChC1B,EAAK,KAAKF,CAAG,EAGf,OAAOE,CACR,CAED,SAASiC,EAAcjC,EAAMR,EAAS,CACpC,OAAOQ,EAAK,IAAI,SAASF,EAAK,CAC5B,OAAON,EAAQ,IAAI,SAASU,EAAQ,CAClC,OAAOgC,EAAYpC,EAAII,CAAM,CAAC,CACtC,CAAO,EAAE,KAAKe,CAAS,CACvB,CAAK,CACF,CAED,SAASkB,EAAOnC,EAAMR,EAAS,CAC7B,OAAIA,GAAW,OAAMA,EAAUO,EAAaC,CAAI,GACzC,CAACR,EAAQ,IAAI0C,CAAW,EAAE,KAAKjB,CAAS,CAAC,EAAE,OAAOgB,EAAcjC,EAAMR,CAAO,CAAC,EAAE,KAAK;AAAA,CAAI,CACjG,CAED,SAAS4C,EAAWpC,EAAMR,EAAS,CACjC,OAAIA,GAAW,OAAMA,EAAUO,EAAaC,CAAI,GACzCiC,EAAcjC,EAAMR,CAAO,EAAE,KAAK;AAAA,CAAI,CAC9C,CAED,SAAS6C,EAAWrC,EAAM,CACxB,OAAOA,EAAK,IAAIsC,CAAS,EAAE,KAAK;AAAA,CAAI,CACrC,CAED,SAASA,EAAUxC,EAAK,CACtB,OAAOA,EAAI,IAAIoC,CAAW,EAAE,KAAKjB,CAAS,CAC3C,CAED,SAASiB,EAAY9B,EAAO,CAC1B,OAAOA,GAAS,KAAO,GACjBA,aAAiB,KAAOM,EAAWN,CAAK,EACxCc,EAAS,KAAKd,GAAS,EAAE,EAAI,IAAOA,EAAM,QAAQ,KAAM,IAAM,EAAI,IAClEA,CACP,CAED,MAAO,CACL,MAAOgB,EACP,UAAWG,EACX,OAAQY,EACR,WAAYC,EACZ,WAAYC,EACZ,UAAWC,EACX,YAAaJ,CACjB,CACA", "x_google_ignoreList": [0]}