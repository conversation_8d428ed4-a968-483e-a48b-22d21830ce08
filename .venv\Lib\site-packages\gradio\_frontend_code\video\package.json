{"name": "@gradio/video", "version": "0.6.10", "description": "Gradio UI packages", "type": "module", "author": "", "license": "ISC", "private": false, "dependencies": {"@gradio/atoms": "workspace:^", "@gradio/client": "workspace:^", "@gradio/icons": "workspace:^", "@gradio/image": "workspace:^", "@gradio/statustracker": "workspace:^", "@gradio/upload": "workspace:^", "@gradio/utils": "workspace:^", "@gradio/wasm": "workspace:^", "@ffmpeg/ffmpeg": "^0.12.7", "@ffmpeg/util": "^0.12.1", "mrmime": "^2.0.0"}, "exports": {".": "./index.ts", "./example": "./Example.svelte", "./shared": "./shared/index.ts", "./package.json": "./package.json"}, "main": "index.ts", "main_changeset": true}