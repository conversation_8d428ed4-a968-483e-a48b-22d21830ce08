import{g as hl}from"./color-03a28f80.js";import{c as He,B as ml}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{B as gl}from"./BlockLabel-f27805b1.js";import{E as bl}from"./Empty-28f63bf0.js";import{S as kl}from"./Index-26cfc80a.js";import"./index-a80d931b.js";import"./svelte/svelte.js";const{SvelteComponent:fn,append:Oe,attr:D,detach:cn,init:_n,insert:un,noop:je,safe_not_equal:dn,svg_element:Se}=window.__gradio__svelte__internal;function hn(l){let e,n,t;return{c(){e=Se("svg"),n=Se("path"),t=Se("path"),D(n,"fill","currentColor"),D(n,"d","M12 15H5a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h5V5a1 1 0 0 0-1-1H3V2h6a3 3 0 0 1 3 3zM5 9a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h5V9zm15 14v2a1 1 0 0 0 1 1h5v-4h-5a1 1 0 0 0-1 1z"),D(t,"fill","currentColor"),D(t,"d","M2 30h28V2Zm26-2h-7a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h5v-2a1 1 0 0 0-1-1h-6v-2h6a3 3 0 0 1 3 3Z"),D(e,"xmlns","http://www.w3.org/2000/svg"),D(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),D(e,"aria-hidden","true"),D(e,"role","img"),D(e,"class","iconify iconify--carbon"),D(e,"width","100%"),D(e,"height","100%"),D(e,"preserveAspectRatio","xMidYMid meet"),D(e,"viewBox","0 0 32 32")},m(i,s){un(i,e,s),Oe(e,n),Oe(e,t)},p:je,i:je,o:je,d(i){i&&cn(e)}}}class pe extends fn{constructor(e){super(),_n(this,e,null,hn,dn,{})}}function Ie(l,e,n){if(!n){var t=document.createElement("canvas");n=t.getContext("2d")}n.fillStyle=l,n.fillRect(0,0,1,1);const[i,s,o]=n.getImageData(0,0,1,1).data;return n.clearRect(0,0,1,1),`rgba(${i}, ${s}, ${o}, ${255/e})`}function pl(l,e,n,t){for(const i in l){const s=l[i].trim();s in He?e[i]=He[s]:e[i]={primary:n?Ie(l[i],1,t):l[i],secondary:n?Ie(l[i],.5,t):l[i]}}}function vl(l,e){let n=[],t=null,i=null;for(const s of l)e==="empty"&&s.class_or_confidence===null||e==="equal"&&i===s.class_or_confidence?t=t?t+s.token:s.token:(t!==null&&n.push({token:t,class_or_confidence:i}),t=s.token,i=s.class_or_confidence);return t!==null&&n.push({token:t,class_or_confidence:i}),n}const{SvelteComponent:mn,append:G,attr:I,destroy_each:ve,detach:V,element:F,empty:wl,ensure_array_like:W,init:gn,insert:q,listen:re,noop:Le,run_all:bn,safe_not_equal:kn,set_data:ze,set_style:he,space:oe,text:ae,toggle_class:Q}=window.__gradio__svelte__internal,{createEventDispatcher:pn}=window.__gradio__svelte__internal;function Be(l,e,n){const t=l.slice();t[17]=e[n];const i=typeof t[17].class_or_confidence=="string"?parseInt(t[17].class_or_confidence):t[17].class_or_confidence;return t[26]=i,t}function Ve(l,e,n){const t=l.slice();return t[17]=e[n],t[19]=n,t}function qe(l,e,n){const t=l.slice();return t[20]=e[n],t[22]=n,t}function Me(l,e,n){const t=l.slice();return t[23]=e[n][0],t[24]=e[n][1],t[19]=n,t}function vn(l){let e,n,t=l[1]&&Re(),i=W(l[0]),s=[];for(let o=0;o<i.length;o+=1)s[o]=De(Be(l,i,o));return{c(){t&&t.c(),e=oe(),n=F("div");for(let o=0;o<s.length;o+=1)s[o].c();I(n,"class","textfield svelte-ju12zg"),I(n,"data-testid","highlighted-text:textfield")},m(o,a){t&&t.m(o,a),q(o,e,a),q(o,n,a);for(let r=0;r<s.length;r+=1)s[r]&&s[r].m(n,null)},p(o,a){if(o[1]?t||(t=Re(),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null),a&1){i=W(o[0]);let r;for(r=0;r<i.length;r+=1){const c=Be(o,i,r);s[r]?s[r].p(c,a):(s[r]=De(c),s[r].c(),s[r].m(n,null))}for(;r<s.length;r+=1)s[r].d(1);s.length=i.length}},d(o){o&&(V(e),V(n)),t&&t.d(o),ve(s,o)}}}function wn(l){let e,n,t=l[1]&&Ae(l),i=W(l[0]),s=[];for(let o=0;o<i.length;o+=1)s[o]=Ye(Ve(l,i,o));return{c(){t&&t.c(),e=oe(),n=F("div");for(let o=0;o<s.length;o+=1)s[o].c();I(n,"class","textfield svelte-ju12zg")},m(o,a){t&&t.m(o,a),q(o,e,a),q(o,n,a);for(let r=0;r<s.length;r+=1)s[r]&&s[r].m(n,null)},p(o,a){if(o[1]?t?t.p(o,a):(t=Ae(o),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null),a&111){i=W(o[0]);let r;for(r=0;r<i.length;r+=1){const c=Ve(o,i,r);s[r]?s[r].p(c,a):(s[r]=Ye(c),s[r].c(),s[r].m(n,null))}for(;r<s.length;r+=1)s[r].d(1);s.length=i.length}},d(o){o&&(V(e),V(n)),t&&t.d(o),ve(s,o)}}}function Re(l){let e;return{c(){e=F("div"),e.innerHTML="<span>-1</span> <span>0</span> <span>+1</span>",I(e,"class","color-legend svelte-ju12zg"),I(e,"data-testid","highlighted-text:color-legend")},m(n,t){q(n,e,t)},d(n){n&&V(e)}}}function De(l){let e,n,t=l[17].token+"",i,s,o;return{c(){e=F("span"),n=F("span"),i=ae(t),s=oe(),I(n,"class","text svelte-ju12zg"),I(e,"class","textspan score-text svelte-ju12zg"),I(e,"style",o="background-color: rgba("+(l[26]&&l[26]<0?"128, 90, 213,"+-l[26]:"239, 68, 60,"+l[26])+")")},m(a,r){q(a,e,r),G(e,n),G(n,i),G(e,s)},p(a,r){r&1&&t!==(t=a[17].token+"")&&ze(i,t),r&1&&o!==(o="background-color: rgba("+(a[26]&&a[26]<0?"128, 90, 213,"+-a[26]:"239, 68, 60,"+a[26])+")")&&I(e,"style",o)},d(a){a&&V(e)}}}function Ae(l){let e,n=W(Object.entries(l[5])),t=[];for(let i=0;i<n.length;i+=1)t[i]=Fe(Me(l,n,i));return{c(){e=F("div");for(let i=0;i<t.length;i+=1)t[i].c();I(e,"class","category-legend svelte-ju12zg"),I(e,"data-testid","highlighted-text:category-legend")},m(i,s){q(i,e,s);for(let o=0;o<t.length;o+=1)t[o]&&t[o].m(e,null)},p(i,s){if(s&416){n=W(Object.entries(i[5]));let o;for(o=0;o<n.length;o+=1){const a=Me(i,n,o);t[o]?t[o].p(a,s):(t[o]=Fe(a),t[o].c(),t[o].m(e,null))}for(;o<t.length;o+=1)t[o].d(1);t.length=n.length}},d(i){i&&V(e),ve(t,i)}}}function Fe(l){let e,n=l[23]+"",t,i,s,o;function a(){return l[10](l[23])}function r(){return l[11](l[23])}return{c(){e=F("div"),t=ae(n),i=oe(),I(e,"class","category-label svelte-ju12zg"),I(e,"style","background-color:"+l[24].secondary)},m(c,f){q(c,e,f),G(e,t),G(e,i),s||(o=[re(e,"mouseover",a),re(e,"focus",r),re(e,"mouseout",l[12]),re(e,"blur",l[13])],s=!0)},p(c,f){l=c},d(c){c&&V(e),s=!1,bn(o)}}}function Ze(l){let e,n,t=l[20]+"",i,s,o,a,r=!l[1]&&l[17].class_or_confidence!==null&&Ke(l);function c(){return l[14](l[19],l[17])}return{c(){e=F("span"),n=F("span"),i=ae(t),s=oe(),r&&r.c(),I(n,"class","text svelte-ju12zg"),Q(n,"no-label",l[17].class_or_confidence===null||!l[5][l[17].class_or_confidence]),I(e,"class","textspan svelte-ju12zg"),Q(e,"no-cat",l[17].class_or_confidence===null||l[3]&&l[3]!==l[17].class_or_confidence),Q(e,"hl",l[17].class_or_confidence!==null),Q(e,"selectable",l[2]),he(e,"background-color",l[17].class_or_confidence===null||l[3]&&l[3]!==l[17].class_or_confidence?"":l[5][l[17].class_or_confidence].secondary)},m(f,g){q(f,e,g),G(e,n),G(n,i),G(e,s),r&&r.m(e,null),o||(a=re(e,"click",c),o=!0)},p(f,g){l=f,g&1&&t!==(t=l[20]+"")&&ze(i,t),g&33&&Q(n,"no-label",l[17].class_or_confidence===null||!l[5][l[17].class_or_confidence]),!l[1]&&l[17].class_or_confidence!==null?r?r.p(l,g):(r=Ke(l),r.c(),r.m(e,null)):r&&(r.d(1),r=null),g&9&&Q(e,"no-cat",l[17].class_or_confidence===null||l[3]&&l[3]!==l[17].class_or_confidence),g&1&&Q(e,"hl",l[17].class_or_confidence!==null),g&4&&Q(e,"selectable",l[2]),g&9&&he(e,"background-color",l[17].class_or_confidence===null||l[3]&&l[3]!==l[17].class_or_confidence?"":l[5][l[17].class_or_confidence].secondary)},d(f){f&&V(e),r&&r.d(),o=!1,a()}}}function Ke(l){let e,n,t=l[17].class_or_confidence+"",i;return{c(){e=ae(` 
								`),n=F("span"),i=ae(t),I(n,"class","label svelte-ju12zg"),he(n,"background-color",l[17].class_or_confidence===null||l[3]&&l[3]!==l[17].class_or_confidence?"":l[5][l[17].class_or_confidence].primary)},m(s,o){q(s,e,o),q(s,n,o),G(n,i)},p(s,o){o&1&&t!==(t=s[17].class_or_confidence+"")&&ze(i,t),o&9&&he(n,"background-color",s[17].class_or_confidence===null||s[3]&&s[3]!==s[17].class_or_confidence?"":s[5][s[17].class_or_confidence].primary)},d(s){s&&(V(e),V(n))}}}function Pe(l){let e;return{c(){e=F("br")},m(n,t){q(n,e,t)},d(n){n&&V(e)}}}function Ue(l){let e=l[20].trim()!=="",n,t=l[22]<me(l[17].token).length-1,i,s=e&&Ze(l),o=t&&Pe();return{c(){s&&s.c(),n=oe(),o&&o.c(),i=wl()},m(a,r){s&&s.m(a,r),q(a,n,r),o&&o.m(a,r),q(a,i,r)},p(a,r){r&1&&(e=a[20].trim()!==""),e?s?s.p(a,r):(s=Ze(a),s.c(),s.m(n.parentNode,n)):s&&(s.d(1),s=null),r&1&&(t=a[22]<me(a[17].token).length-1),t?o||(o=Pe(),o.c(),o.m(i.parentNode,i)):o&&(o.d(1),o=null)},d(a){a&&(V(n),V(i)),s&&s.d(a),o&&o.d(a)}}}function Ye(l){let e,n=W(me(l[17].token)),t=[];for(let i=0;i<n.length;i+=1)t[i]=Ue(qe(l,n,i));return{c(){for(let i=0;i<t.length;i+=1)t[i].c();e=wl()},m(i,s){for(let o=0;o<t.length;o+=1)t[o]&&t[o].m(i,s);q(i,e,s)},p(i,s){if(s&111){n=W(me(i[17].token));let o;for(o=0;o<n.length;o+=1){const a=qe(i,n,o);t[o]?t[o].p(a,s):(t[o]=Ue(a),t[o].c(),t[o].m(e.parentNode,e))}for(;o<t.length;o+=1)t[o].d(1);t.length=n.length}},d(i){i&&V(e),ve(t,i)}}}function yn(l){let e;function n(s,o){return s[4]==="categories"?wn:vn}let t=n(l),i=t(l);return{c(){e=F("div"),i.c(),I(e,"class","container svelte-ju12zg")},m(s,o){q(s,e,o),i.m(e,null)},p(s,[o]){t===(t=n(s))&&i?i.p(s,o):(i.d(1),i=t(s),i&&(i.c(),i.m(e,null)))},i:Le,o:Le,d(s){s&&V(e),i.d()}}}function me(l){return l.split(`
`)}function jn(l,e,n){const t=typeof document<"u";let{value:i=[]}=e,{show_legend:s=!1}=e,{color_map:o={}}=e,{selectable:a=!1}=e,r,c={},f="";const g=pn();let p;function b(k){n(3,f=k)}function _(){n(3,f="")}const d=k=>b(k),w=k=>b(k),z=()=>_(),y=()=>_(),h=(k,j)=>{g("select",{index:k,value:[j.token,j.class_or_confidence]})};return l.$$set=k=>{"value"in k&&n(0,i=k.value),"show_legend"in k&&n(1,s=k.show_legend),"color_map"in k&&n(9,o=k.color_map),"selectable"in k&&n(2,a=k.selectable)},l.$$.update=()=>{if(l.$$.dirty&513){if(o||n(9,o={}),i.length>0){for(let k of i)if(k.class_or_confidence!==null)if(typeof k.class_or_confidence=="string"){if(n(4,p="categories"),!(k.class_or_confidence in o)){let j=hl(Object.keys(o).length);n(9,o[k.class_or_confidence]=j,o)}}else n(4,p="scores")}pl(o,c,t,r)}},[i,s,a,f,p,c,g,b,_,o,d,w,z,y,h]}class Sn extends mn{constructor(e){super(),gn(this,e,jn,yn,kn,{value:0,show_legend:1,color_map:9,selectable:2})}}const zn=Sn;const{SvelteComponent:En,attr:U,detach:Ee,element:yl,empty:Cn,init:Nn,insert:Ce,listen:$,noop:Ge,run_all:jl,safe_not_equal:Tn,set_style:te}=window.__gradio__svelte__internal;function Hn(l){let e,n,t,i;return{c(){e=yl("input"),U(e,"class","label-input svelte-1cag2po"),e.autofocus=!0,U(e,"type","number"),U(e,"step","0.1"),U(e,"style",n="background-color: rgba("+(typeof l[1]=="number"&&l[1]<0?"128, 90, 213,"+-l[1]:"239, 68, 60,"+l[1])+")"),e.value=l[1],te(e,"width","7ch")},m(s,o){Ce(s,e,o),e.focus(),t||(i=[$(e,"input",l[8]),$(e,"blur",l[14]),$(e,"keydown",l[15])],t=!0)},p(s,o){o&2&&n!==(n="background-color: rgba("+(typeof s[1]=="number"&&s[1]<0?"128, 90, 213,"+-s[1]:"239, 68, 60,"+s[1])+")")&&U(e,"style",n),o&2&&e.value!==s[1]&&(e.value=s[1]);const a=o&2;(o&2||a)&&te(e,"width","7ch")},d(s){s&&Ee(e),t=!1,jl(i)}}}function On(l){let e,n,t,i;return{c(){e=yl("input"),U(e,"class","label-input svelte-1cag2po"),e.autofocus=!0,U(e,"id",n=`label-input-${l[3]}`),U(e,"type","text"),U(e,"placeholder","label"),e.value=l[1],te(e,"background-color",l[1]===null||l[2]&&l[2]!==l[1]?"":l[6][l[1]].primary),te(e,"width",l[7]?l[7].toString()?.length+4+"ch":"8ch")},m(s,o){Ce(s,e,o),e.focus(),t||(i=[$(e,"input",l[8]),$(e,"blur",l[12]),$(e,"keydown",l[13]),$(e,"focus",Ln)],t=!0)},p(s,o){o&8&&n!==(n=`label-input-${s[3]}`)&&U(e,"id",n),o&2&&e.value!==s[1]&&(e.value=s[1]),o&70&&te(e,"background-color",s[1]===null||s[2]&&s[2]!==s[1]?"":s[6][s[1]].primary),o&128&&te(e,"width",s[7]?s[7].toString()?.length+4+"ch":"8ch")},d(s){s&&Ee(e),t=!1,jl(i)}}}function In(l){let e;function n(s,o){return s[5]?Hn:On}let t=n(l),i=t(l);return{c(){i.c(),e=Cn()},m(s,o){i.m(s,o),Ce(s,e,o)},p(s,[o]){t===(t=n(s))&&i?i.p(s,o):(i.d(1),i=t(s),i&&(i.c(),i.m(e.parentNode,e)))},i:Ge,o:Ge,d(s){s&&Ee(e),i.d(s)}}}function Ln(l){let e=l.target;e&&e.placeholder&&(e.placeholder="")}function Bn(l,e,n){let{value:t}=e,{category:i}=e,{active:s}=e,{labelToEdit:o}=e,{indexOfLabel:a}=e,{text:r}=e,{handleValueChange:c}=e,{isScoresMode:f=!1}=e,{_color_map:g}=e,p=i;function b(h){let k=h.target;k&&n(7,p=k.value)}function _(h,k,j){let m=h.target;n(10,t=[...t.slice(0,k),{token:j,class_or_confidence:m.value===""?null:f?Number(m.value):m.value},...t.slice(k+1)]),c()}const d=h=>_(h,a,r),w=h=>{h.key==="Enter"&&(_(h,a,r),n(0,o=-1))},z=h=>_(h,a,r),y=h=>{h.key==="Enter"&&(_(h,a,r),n(0,o=-1))};return l.$$set=h=>{"value"in h&&n(10,t=h.value),"category"in h&&n(1,i=h.category),"active"in h&&n(2,s=h.active),"labelToEdit"in h&&n(0,o=h.labelToEdit),"indexOfLabel"in h&&n(3,a=h.indexOfLabel),"text"in h&&n(4,r=h.text),"handleValueChange"in h&&n(11,c=h.handleValueChange),"isScoresMode"in h&&n(5,f=h.isScoresMode),"_color_map"in h&&n(6,g=h._color_map)},[o,i,s,a,r,f,g,p,b,_,t,c,d,w,z,y]}class Sl extends En{constructor(e){super(),Nn(this,e,Bn,In,Tn,{value:10,category:1,active:2,labelToEdit:0,indexOfLabel:3,text:4,handleValueChange:11,isScoresMode:5,_color_map:6})}}const{SvelteComponent:Vn,add_flush_callback:zl,append:R,attr:v,bind:El,binding_callbacks:Cl,check_outros:le,create_component:Nl,destroy_component:Tl,destroy_each:we,detach:H,element:B,empty:Ne,ensure_array_like:X,group_outros:ne,init:qn,insert:O,listen:E,mount_component:Hl,run_all:ie,safe_not_equal:Mn,set_data:ye,set_style:ge,space:Y,text:_e,toggle_class:A,transition_in:N,transition_out:M}=window.__gradio__svelte__internal,{createEventDispatcher:Rn,onMount:Dn}=window.__gradio__svelte__internal;function Je(l,e,n){const t=l.slice();t[45]=e[n].token,t[46]=e[n].class_or_confidence,t[48]=n;const i=typeof t[46]=="string"?parseInt(t[46]):t[46];return t[54]=i,t}function Qe(l,e,n){const t=l.slice();return t[45]=e[n].token,t[46]=e[n].class_or_confidence,t[48]=n,t}function We(l,e,n){const t=l.slice();return t[49]=e[n],t[51]=n,t}function Xe(l,e,n){const t=l.slice();return t[46]=e[n][0],t[52]=e[n][1],t[48]=n,t}function An(l){let e,n,t,i=l[1]&&$e(),s=X(l[0]),o=[];for(let r=0;r<s.length;r+=1)o[r]=ll(Je(l,s,r));const a=r=>M(o[r],1,1,()=>{o[r]=null});return{c(){i&&i.c(),e=Y(),n=B("div");for(let r=0;r<o.length;r+=1)o[r].c();v(n,"class","textfield svelte-1ozsnjl"),v(n,"data-testid","highlighted-text:textfield")},m(r,c){i&&i.m(r,c),O(r,e,c),O(r,n,c);for(let f=0;f<o.length;f+=1)o[f]&&o[f].m(n,null);t=!0},p(r,c){if(r[1]?i||(i=$e(),i.c(),i.m(e.parentNode,e)):i&&(i.d(1),i=null),c[0]&889){s=X(r[0]);let f;for(f=0;f<s.length;f+=1){const g=Je(r,s,f);o[f]?(o[f].p(g,c),N(o[f],1)):(o[f]=ll(g),o[f].c(),N(o[f],1),o[f].m(n,null))}for(ne(),f=s.length;f<o.length;f+=1)a(f);le()}},i(r){if(!t){for(let c=0;c<s.length;c+=1)N(o[c]);t=!0}},o(r){o=o.filter(Boolean);for(let c=0;c<o.length;c+=1)M(o[c]);t=!1},d(r){r&&(H(e),H(n)),i&&i.d(r),we(o,r)}}}function Fn(l){let e,n,t,i=l[1]&&nl(l),s=X(l[0]),o=[];for(let r=0;r<s.length;r+=1)o[r]=_l(Qe(l,s,r));const a=r=>M(o[r],1,1,()=>{o[r]=null});return{c(){i&&i.c(),e=Y(),n=B("div");for(let r=0;r<o.length;r+=1)o[r].c();v(n,"class","textfield svelte-1ozsnjl")},m(r,c){i&&i.m(r,c),O(r,e,c),O(r,n,c);for(let f=0;f<o.length;f+=1)o[f]&&o[f].m(n,null);t=!0},p(r,c){if(r[1]?i?i.p(r,c):(i=nl(r),i.c(),i.m(e.parentNode,e)):i&&(i.d(1),i=null),c[0]&13183){s=X(r[0]);let f;for(f=0;f<s.length;f+=1){const g=Qe(r,s,f);o[f]?(o[f].p(g,c),N(o[f],1)):(o[f]=_l(g),o[f].c(),N(o[f],1),o[f].m(n,null))}for(ne(),f=s.length;f<o.length;f+=1)a(f);le()}},i(r){if(!t){for(let c=0;c<s.length;c+=1)N(o[c]);t=!0}},o(r){o=o.filter(Boolean);for(let c=0;c<o.length;c+=1)M(o[c]);t=!1},d(r){r&&(H(e),H(n)),i&&i.d(r),we(o,r)}}}function $e(l){let e;return{c(){e=B("div"),e.innerHTML="<span>-1</span> <span>0</span> <span>+1</span>",v(e,"class","color-legend svelte-1ozsnjl"),v(e,"data-testid","highlighted-text:color-legend")},m(n,t){O(n,e,t)},d(n){n&&H(e)}}}function xe(l){let e,n,t;function i(o){l[32](o)}let s={labelToEdit:l[6],_color_map:l[3],category:l[46],active:l[5],indexOfLabel:l[48],text:l[45],handleValueChange:l[9],isScoresMode:!0};return l[0]!==void 0&&(s.value=l[0]),e=new Sl({props:s}),Cl.push(()=>El(e,"value",i)),{c(){Nl(e.$$.fragment)},m(o,a){Hl(e,o,a),t=!0},p(o,a){const r={};a[0]&64&&(r.labelToEdit=o[6]),a[0]&8&&(r._color_map=o[3]),a[0]&1&&(r.category=o[46]),a[0]&32&&(r.active=o[5]),a[0]&1&&(r.text=o[45]),!n&&a[0]&1&&(n=!0,r.value=o[0],zl(()=>n=!1)),e.$set(r)},i(o){t||(N(e.$$.fragment,o),t=!0)},o(o){M(e.$$.fragment,o),t=!1},d(o){Tl(e,o)}}}function el(l){let e,n,t;function i(){return l[37](l[48])}function s(...o){return l[38](l[48],...o)}return{c(){e=B("span"),e.textContent="×",v(e,"class","label-clear-button svelte-1ozsnjl"),v(e,"role","button"),v(e,"aria-roledescription","Remove label from text"),v(e,"tabindex","0")},m(o,a){O(o,e,a),n||(t=[E(e,"click",i),E(e,"keydown",s)],n=!0)},p(o,a){l=o},d(o){o&&H(e),n=!1,ie(t)}}}function ll(l){let e,n,t,i=l[45]+"",s,o,a,r,c,f,g,p,b=l[46]&&l[6]===l[48]&&xe(l);function _(){return l[33](l[48])}function d(){return l[34](l[48])}function w(){return l[35](l[48])}function z(...h){return l[36](l[48],...h)}let y=l[46]&&l[4]===l[48]&&el(l);return{c(){e=B("span"),n=B("span"),t=B("span"),s=_e(i),o=Y(),b&&b.c(),r=Y(),y&&y.c(),c=Y(),v(t,"class","text svelte-1ozsnjl"),v(n,"class","textspan score-text svelte-1ozsnjl"),v(n,"role","button"),v(n,"tabindex","0"),v(n,"style",a="background-color: rgba("+(l[54]&&l[54]<0?"128, 90, 213,"+-l[54]:"239, 68, 60,"+l[54])+")"),A(n,"no-cat",l[46]===null||l[5]&&l[5]!==l[46]),A(n,"hl",l[46]!==null),v(e,"class","score-text-container svelte-1ozsnjl")},m(h,k){O(h,e,k),R(e,n),R(n,t),R(t,s),R(n,o),b&&b.m(n,null),R(e,r),y&&y.m(e,null),R(e,c),f=!0,g||(p=[E(n,"mouseover",_),E(n,"focus",d),E(n,"click",w),E(n,"keydown",z)],g=!0)},p(h,k){l=h,(!f||k[0]&1)&&i!==(i=l[45]+"")&&ye(s,i),l[46]&&l[6]===l[48]?b?(b.p(l,k),k[0]&65&&N(b,1)):(b=xe(l),b.c(),N(b,1),b.m(n,null)):b&&(ne(),M(b,1,1,()=>{b=null}),le()),(!f||k[0]&1&&a!==(a="background-color: rgba("+(l[54]&&l[54]<0?"128, 90, 213,"+-l[54]:"239, 68, 60,"+l[54])+")"))&&v(n,"style",a),(!f||k[0]&33)&&A(n,"no-cat",l[46]===null||l[5]&&l[5]!==l[46]),(!f||k[0]&1)&&A(n,"hl",l[46]!==null),l[46]&&l[4]===l[48]?y?y.p(l,k):(y=el(l),y.c(),y.m(e,c)):y&&(y.d(1),y=null)},i(h){f||(N(b),f=!0)},o(h){M(b),f=!1},d(h){h&&H(e),b&&b.d(),y&&y.d(),g=!1,ie(p)}}}function nl(l){let e,n=l[3]&&tl(l);return{c(){e=B("div"),n&&n.c(),v(e,"class","class_or_confidence-legend svelte-1ozsnjl"),v(e,"data-testid","highlighted-text:class_or_confidence-legend")},m(t,i){O(t,e,i),n&&n.m(e,null)},p(t,i){t[3]?n?n.p(t,i):(n=tl(t),n.c(),n.m(e,null)):n&&(n.d(1),n=null)},d(t){t&&H(e),n&&n.d()}}}function tl(l){let e,n=X(Object.entries(l[3])),t=[];for(let i=0;i<n.length;i+=1)t[i]=ol(Xe(l,n,i));return{c(){for(let i=0;i<t.length;i+=1)t[i].c();e=Ne()},m(i,s){for(let o=0;o<t.length;o+=1)t[o]&&t[o].m(i,s);O(i,e,s)},p(i,s){if(s[0]&3080){n=X(Object.entries(i[3]));let o;for(o=0;o<n.length;o+=1){const a=Xe(i,n,o);t[o]?t[o].p(a,s):(t[o]=ol(a),t[o].c(),t[o].m(e.parentNode,e))}for(;o<t.length;o+=1)t[o].d(1);t.length=n.length}},d(i){i&&H(e),we(t,i)}}}function ol(l){let e,n=l[46]+"",t,i,s,o,a;function r(){return l[15](l[46])}function c(){return l[16](l[46])}return{c(){e=B("div"),t=_e(n),i=Y(),v(e,"role","button"),v(e,"aria-roledescription","Categories of highlighted text. Hover to see text with this class_or_confidence highlighted."),v(e,"tabindex","0"),v(e,"class","class_or_confidence-label svelte-1ozsnjl"),v(e,"style",s="background-color:"+l[52].secondary)},m(f,g){O(f,e,g),R(e,t),R(e,i),o||(a=[E(e,"mouseover",r),E(e,"focus",c),E(e,"mouseout",l[17]),E(e,"blur",l[18])],o=!0)},p(f,g){l=f,g[0]&8&&n!==(n=l[46]+"")&&ye(t,n),g[0]&8&&s!==(s="background-color:"+l[52].secondary)&&v(e,"style",s)},d(f){f&&H(e),o=!1,ie(a)}}}function il(l){let e,n,t,i=l[49]+"",s,o,a,r,c,f,g;function p(){return l[20](l[48])}function b(){return l[21](l[48])}function _(){return l[22](l[48])}let d=!l[1]&&l[46]!==null&&l[6]!==l[48]&&sl(l),w=l[6]===l[48]&&l[46]!==null&&rl(l);function z(){return l[26](l[46],l[48],l[45])}function y(...m){return l[27](l[46],l[48],l[45],...m)}function h(){return l[28](l[48])}function k(){return l[29](l[48])}let j=l[46]!==null&&al(l);return{c(){e=B("span"),n=B("span"),t=B("span"),s=_e(i),o=Y(),d&&d.c(),a=Y(),w&&w.c(),r=Y(),j&&j.c(),v(t,"class","text svelte-1ozsnjl"),v(t,"role","button"),v(t,"tabindex","0"),A(t,"no-label",l[46]===null),v(n,"role","button"),v(n,"tabindex","0"),v(n,"class","textspan svelte-1ozsnjl"),A(n,"no-cat",l[46]===null||l[5]&&l[5]!==l[46]),A(n,"hl",l[46]!==null),A(n,"selectable",l[2]),ge(n,"background-color",l[46]===null||l[5]&&l[5]!==l[46]?"":l[46]&&l[3][l[46]]?l[3][l[46]].secondary:""),v(e,"class","text-class_or_confidence-container svelte-1ozsnjl")},m(m,L){O(m,e,L),R(e,n),R(n,t),R(t,s),R(n,o),d&&d.m(n,null),R(n,a),w&&w.m(n,null),R(e,r),j&&j.m(e,null),c=!0,f||(g=[E(t,"keydown",l[19]),E(t,"focus",p),E(t,"mouseover",b),E(t,"click",_),E(n,"click",z),E(n,"keydown",y),E(n,"focus",h),E(n,"mouseover",k)],f=!0)},p(m,L){l=m,(!c||L[0]&1)&&i!==(i=l[49]+"")&&ye(s,i),(!c||L[0]&1)&&A(t,"no-label",l[46]===null),!l[1]&&l[46]!==null&&l[6]!==l[48]?d?d.p(l,L):(d=sl(l),d.c(),d.m(n,a)):d&&(d.d(1),d=null),l[6]===l[48]&&l[46]!==null?w?(w.p(l,L),L[0]&65&&N(w,1)):(w=rl(l),w.c(),N(w,1),w.m(n,null)):w&&(ne(),M(w,1,1,()=>{w=null}),le()),(!c||L[0]&33)&&A(n,"no-cat",l[46]===null||l[5]&&l[5]!==l[46]),(!c||L[0]&1)&&A(n,"hl",l[46]!==null),(!c||L[0]&4)&&A(n,"selectable",l[2]),L[0]&41&&ge(n,"background-color",l[46]===null||l[5]&&l[5]!==l[46]?"":l[46]&&l[3][l[46]]?l[3][l[46]].secondary:""),l[46]!==null?j?j.p(l,L):(j=al(l),j.c(),j.m(e,null)):j&&(j.d(1),j=null)},i(m){c||(N(w),c=!0)},o(m){M(w),c=!1},d(m){m&&H(e),d&&d.d(),w&&w.d(),j&&j.d(),f=!1,ie(g)}}}function sl(l){let e,n=l[46]+"",t,i,s;function o(){return l[23](l[48])}function a(){return l[24](l[48])}return{c(){e=B("span"),t=_e(n),v(e,"id",`label-tag-${l[48]}`),v(e,"class","label svelte-1ozsnjl"),v(e,"role","button"),v(e,"tabindex","0"),ge(e,"background-color",l[46]===null||l[5]&&l[5]!==l[46]?"":l[3][l[46]].primary)},m(r,c){O(r,e,c),R(e,t),i||(s=[E(e,"click",o),E(e,"keydown",a)],i=!0)},p(r,c){l=r,c[0]&1&&n!==(n=l[46]+"")&&ye(t,n),c[0]&41&&ge(e,"background-color",l[46]===null||l[5]&&l[5]!==l[46]?"":l[3][l[46]].primary)},d(r){r&&H(e),i=!1,ie(s)}}}function rl(l){let e,n,t,i;function s(a){l[25](a)}let o={labelToEdit:l[6],category:l[46],active:l[5],_color_map:l[3],indexOfLabel:l[48],text:l[45],handleValueChange:l[9]};return l[0]!==void 0&&(o.value=l[0]),n=new Sl({props:o}),Cl.push(()=>El(n,"value",s)),{c(){e=_e(` 
									`),Nl(n.$$.fragment)},m(a,r){O(a,e,r),Hl(n,a,r),i=!0},p(a,r){const c={};r[0]&64&&(c.labelToEdit=a[6]),r[0]&1&&(c.category=a[46]),r[0]&32&&(c.active=a[5]),r[0]&8&&(c._color_map=a[3]),r[0]&1&&(c.text=a[45]),!t&&r[0]&1&&(t=!0,c.value=a[0],zl(()=>t=!1)),n.$set(c)},i(a){i||(N(n.$$.fragment,a),i=!0)},o(a){M(n.$$.fragment,a),i=!1},d(a){a&&H(e),Tl(n,a)}}}function al(l){let e,n,t;function i(){return l[30](l[48])}function s(...o){return l[31](l[48],...o)}return{c(){e=B("span"),e.textContent="×",v(e,"class","label-clear-button svelte-1ozsnjl"),v(e,"role","button"),v(e,"aria-roledescription","Remove label from text"),v(e,"tabindex","0")},m(o,a){O(o,e,a),n||(t=[E(e,"click",i),E(e,"keydown",s)],n=!0)},p(o,a){l=o},d(o){o&&H(e),n=!1,ie(t)}}}function fl(l){let e;return{c(){e=B("br")},m(n,t){O(n,e,t)},d(n){n&&H(e)}}}function cl(l){let e=l[49].trim()!=="",n,t=l[51]<be(l[45]).length-1,i,s,o=e&&il(l),a=t&&fl();return{c(){o&&o.c(),n=Y(),a&&a.c(),i=Ne()},m(r,c){o&&o.m(r,c),O(r,n,c),a&&a.m(r,c),O(r,i,c),s=!0},p(r,c){c[0]&1&&(e=r[49].trim()!==""),e?o?(o.p(r,c),c[0]&1&&N(o,1)):(o=il(r),o.c(),N(o,1),o.m(n.parentNode,n)):o&&(ne(),M(o,1,1,()=>{o=null}),le()),c[0]&1&&(t=r[51]<be(r[45]).length-1),t?a||(a=fl(),a.c(),a.m(i.parentNode,i)):a&&(a.d(1),a=null)},i(r){s||(N(o),s=!0)},o(r){M(o),s=!1},d(r){r&&(H(n),H(i)),o&&o.d(r),a&&a.d(r)}}}function _l(l){let e,n,t=X(be(l[45])),i=[];for(let o=0;o<t.length;o+=1)i[o]=cl(We(l,t,o));const s=o=>M(i[o],1,1,()=>{i[o]=null});return{c(){for(let o=0;o<i.length;o+=1)i[o].c();e=Ne()},m(o,a){for(let r=0;r<i.length;r+=1)i[r]&&i[r].m(o,a);O(o,e,a),n=!0},p(o,a){if(a[0]&13183){t=X(be(o[45]));let r;for(r=0;r<t.length;r+=1){const c=We(o,t,r);i[r]?(i[r].p(c,a),N(i[r],1)):(i[r]=cl(c),i[r].c(),N(i[r],1),i[r].m(e.parentNode,e))}for(ne(),r=t.length;r<i.length;r+=1)s(r);le()}},i(o){if(!n){for(let a=0;a<t.length;a+=1)N(i[a]);n=!0}},o(o){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)M(i[a]);n=!1},d(o){o&&H(e),we(i,o)}}}function Zn(l){let e,n,t,i;const s=[Fn,An],o=[];function a(r,c){return r[7]==="categories"?0:1}return n=a(l),t=o[n]=s[n](l),{c(){e=B("div"),t.c(),v(e,"class","container svelte-1ozsnjl")},m(r,c){O(r,e,c),o[n].m(e,null),i=!0},p(r,c){let f=n;n=a(r),n===f?o[n].p(r,c):(ne(),M(o[f],1,1,()=>{o[f]=null}),le(),t=o[n],t?t.p(r,c):(t=o[n]=s[n](r),t.c()),N(t,1),t.m(e,null))},i(r){i||(N(t),i=!0)},o(r){M(t),i=!1},d(r){r&&H(e),o[n].d()}}}function be(l){return l.split(`
`)}function Kn(l,e,n){const t=typeof document<"u";let{value:i=[]}=e,{show_legend:s=!1}=e,{color_map:o={}}=e,{selectable:a=!1}=e,r=-1,c,f={},g="",p,b=-1;Dn(()=>{const u=()=>{p=window.getSelection(),m(),window.removeEventListener("mouseup",u)};window.addEventListener("mousedown",()=>{window.addEventListener("mouseup",u)})});async function _(u,T){if(p?.toString()&&r!==-1&&i[r].token.toString().includes(p.toString())){const J=Symbol(),se=i[r].token,[on,sn,rn]=[se.substring(0,u),se.substring(u,T),se.substring(T)];let ue=[...i.slice(0,r),{token:on,class_or_confidence:null},{token:sn,class_or_confidence:y==="scores"?1:"label",flag:J},{token:rn,class_or_confidence:null},...i.slice(r+1)];n(6,b=ue.findIndex(({flag:de})=>de===J)),ue=ue.filter(de=>de.token.trim()!==""),n(0,i=ue.map(({flag:de,...an})=>an)),z(),document.getElementById(`label-input-${b}`)?.focus()}}const d=Rn();function w(u){!i||u<0||u>=i.length||(n(0,i[u].class_or_confidence=null,i),n(0,i=vl(i,"equal")),z(),window.getSelection()?.empty())}function z(){d("change",i),n(6,b=-1),s&&(n(14,o={}),n(3,f={}))}let y;function h(u){n(5,g=u)}function k(){n(5,g="")}async function j(u){p=window.getSelection(),u.key==="Enter"&&m()}function m(){if(p&&p?.toString().trim()!==""){const u=p.getRangeAt(0).startOffset,T=p.getRangeAt(0).endOffset;_(u,T)}}function L(u,T,J){d("select",{index:u,value:[T,J]})}const Bl=u=>h(u),Vl=u=>h(u),ql=()=>k(),Ml=()=>k(),Rl=u=>j(u),Dl=u=>n(4,r=u),Al=u=>n(4,r=u),Fl=u=>n(6,b=u),Zl=u=>n(6,b=u),Kl=u=>n(6,b=u);function Pl(u){i=u,n(0,i)}const Ul=(u,T,J)=>{u!==null&&L(T,J,u)},Yl=(u,T,J,se)=>{u!==null?(n(6,b=T),L(T,J,u)):j(se)},Gl=u=>n(4,r=u),Jl=u=>n(4,r=u),Ql=u=>w(u),Wl=(u,T)=>{T.key==="Enter"&&w(u)};function Xl(u){i=u,n(0,i)}const $l=u=>n(4,r=u),xl=u=>n(4,r=u),en=u=>n(6,b=u),ln=(u,T)=>{T.key==="Enter"&&n(6,b=u)},nn=u=>w(u),tn=(u,T)=>{T.key==="Enter"&&w(u)};return l.$$set=u=>{"value"in u&&n(0,i=u.value),"show_legend"in u&&n(1,s=u.show_legend),"color_map"in u&&n(14,o=u.color_map),"selectable"in u&&n(2,a=u.selectable)},l.$$.update=()=>{if(l.$$.dirty[0]&16393){if(o||n(14,o={}),i.length>0){for(let u of i)if(u.class_or_confidence!==null)if(typeof u.class_or_confidence=="string"){if(n(7,y="categories"),!(u.class_or_confidence in o)){let T=hl(Object.keys(o).length);n(14,o[u.class_or_confidence]=T,o)}}else n(7,y="scores")}pl(o,f,t,c)}},[i,s,a,f,r,g,b,y,w,z,h,k,j,L,o,Bl,Vl,ql,Ml,Rl,Dl,Al,Fl,Zl,Kl,Pl,Ul,Yl,Gl,Jl,Ql,Wl,Xl,$l,xl,en,ln,nn,tn]}class Pn extends Vn{constructor(e){super(),qn(this,e,Kn,Zn,Mn,{value:0,show_legend:1,color_map:14,selectable:2},null,[-1,-1])}}const Un=Pn,{SvelteComponent:Yn,add_flush_callback:Gn,assign:Ol,bind:Jn,binding_callbacks:Qn,check_outros:fe,create_component:Z,destroy_component:K,detach:x,empty:Te,get_spread_object:Il,get_spread_update:Ll,group_outros:ce,init:Wn,insert:ee,mount_component:P,safe_not_equal:Xn,space:ke,transition_in:S,transition_out:C}=window.__gradio__svelte__internal;function $n(l){let e,n;return e=new ml({props:{variant:l[12]?"dashed":"solid",test_id:"highlighted-text",visible:l[5],elem_id:l[3],elem_classes:l[4],padding:!1,container:l[8],scale:l[9],min_width:l[10],$$slots:{default:[tt]},$$scope:{ctx:l}}}),{c(){Z(e.$$.fragment)},m(t,i){P(e,t,i),n=!0},p(t,i){const s={};i&4096&&(s.variant=t[12]?"dashed":"solid"),i&32&&(s.visible=t[5]),i&8&&(s.elem_id=t[3]),i&16&&(s.elem_classes=t[4]),i&256&&(s.container=t[8]),i&512&&(s.scale=t[9]),i&1024&&(s.min_width=t[10]),i&534983&&(s.$$scope={dirty:i,ctx:t}),e.$set(s)},i(t){n||(S(e.$$.fragment,t),n=!0)},o(t){C(e.$$.fragment,t),n=!1},d(t){K(e,t)}}}function xn(l){let e,n;return e=new ml({props:{variant:"solid",test_id:"highlighted-text",visible:l[5],elem_id:l[3],elem_classes:l[4],padding:!1,container:l[8],scale:l[9],min_width:l[10],$$slots:{default:[rt]},$$scope:{ctx:l}}}),{c(){Z(e.$$.fragment)},m(t,i){P(e,t,i),n=!0},p(t,i){const s={};i&32&&(s.visible=t[5]),i&8&&(s.elem_id=t[3]),i&16&&(s.elem_classes=t[4]),i&256&&(s.container=t[8]),i&512&&(s.scale=t[9]),i&1024&&(s.min_width=t[10]),i&534983&&(s.$$scope={dirty:i,ctx:t}),e.$set(s)},i(t){n||(S(e.$$.fragment,t),n=!0)},o(t){C(e.$$.fragment,t),n=!1},d(t){K(e,t)}}}function ul(l){let e,n;return e=new gl({props:{Icon:pe,label:l[7],float:!1,disable:l[8]===!1}}),{c(){Z(e.$$.fragment)},m(t,i){P(e,t,i),n=!0},p(t,i){const s={};i&128&&(s.label=t[7]),i&256&&(s.disable=t[8]===!1),e.$set(s)},i(t){n||(S(e.$$.fragment,t),n=!0)},o(t){C(e.$$.fragment,t),n=!1},d(t){K(e,t)}}}function et(l){let e,n;return e=new bl({props:{$$slots:{default:[nt]},$$scope:{ctx:l}}}),{c(){Z(e.$$.fragment)},m(t,i){P(e,t,i),n=!0},p(t,i){const s={};i&524288&&(s.$$scope={dirty:i,ctx:t}),e.$set(s)},i(t){n||(S(e.$$.fragment,t),n=!0)},o(t){C(e.$$.fragment,t),n=!1},d(t){K(e,t)}}}function lt(l){let e,n,t;function i(o){l[17](o)}let s={selectable:l[11],show_legend:l[6],color_map:l[1]};return l[0]!==void 0&&(s.value=l[0]),e=new Un({props:s}),Qn.push(()=>Jn(e,"value",i)),e.$on("change",l[18]),{c(){Z(e.$$.fragment)},m(o,a){P(e,o,a),t=!0},p(o,a){const r={};a&2048&&(r.selectable=o[11]),a&64&&(r.show_legend=o[6]),a&2&&(r.color_map=o[1]),!n&&a&1&&(n=!0,r.value=o[0],Gn(()=>n=!1)),e.$set(r)},i(o){t||(S(e.$$.fragment,o),t=!0)},o(o){C(e.$$.fragment,o),t=!1},d(o){K(e,o)}}}function nt(l){let e,n;return e=new pe({}),{c(){Z(e.$$.fragment)},m(t,i){P(e,t,i),n=!0},i(t){n||(S(e.$$.fragment,t),n=!0)},o(t){C(e.$$.fragment,t),n=!1},d(t){K(e,t)}}}function tt(l){let e,n,t,i,s,o,a;const r=[{autoscroll:l[2].autoscroll},l[13],{i18n:l[2].i18n}];let c={};for(let _=0;_<r.length;_+=1)c=Ol(c,r[_]);e=new kl({props:c});let f=l[7]&&ul(l);const g=[lt,et],p=[];function b(_,d){return _[0]?0:1}return i=b(l),s=p[i]=g[i](l),{c(){Z(e.$$.fragment),n=ke(),f&&f.c(),t=ke(),s.c(),o=Te()},m(_,d){P(e,_,d),ee(_,n,d),f&&f.m(_,d),ee(_,t,d),p[i].m(_,d),ee(_,o,d),a=!0},p(_,d){const w=d&8196?Ll(r,[d&4&&{autoscroll:_[2].autoscroll},d&8192&&Il(_[13]),d&4&&{i18n:_[2].i18n}]):{};e.$set(w),_[7]?f?(f.p(_,d),d&128&&S(f,1)):(f=ul(_),f.c(),S(f,1),f.m(t.parentNode,t)):f&&(ce(),C(f,1,1,()=>{f=null}),fe());let z=i;i=b(_),i===z?p[i].p(_,d):(ce(),C(p[z],1,1,()=>{p[z]=null}),fe(),s=p[i],s?s.p(_,d):(s=p[i]=g[i](_),s.c()),S(s,1),s.m(o.parentNode,o))},i(_){a||(S(e.$$.fragment,_),S(f),S(s),a=!0)},o(_){C(e.$$.fragment,_),C(f),C(s),a=!1},d(_){_&&(x(n),x(t),x(o)),K(e,_),f&&f.d(_),p[i].d(_)}}}function dl(l){let e,n;return e=new gl({props:{Icon:pe,label:l[7],float:!1,disable:l[8]===!1}}),{c(){Z(e.$$.fragment)},m(t,i){P(e,t,i),n=!0},p(t,i){const s={};i&128&&(s.label=t[7]),i&256&&(s.disable=t[8]===!1),e.$set(s)},i(t){n||(S(e.$$.fragment,t),n=!0)},o(t){C(e.$$.fragment,t),n=!1},d(t){K(e,t)}}}function ot(l){let e,n;return e=new bl({props:{$$slots:{default:[st]},$$scope:{ctx:l}}}),{c(){Z(e.$$.fragment)},m(t,i){P(e,t,i),n=!0},p(t,i){const s={};i&524288&&(s.$$scope={dirty:i,ctx:t}),e.$set(s)},i(t){n||(S(e.$$.fragment,t),n=!0)},o(t){C(e.$$.fragment,t),n=!1},d(t){K(e,t)}}}function it(l){let e,n;return e=new zn({props:{selectable:l[11],value:l[0],show_legend:l[6],color_map:l[1]}}),e.$on("select",l[16]),{c(){Z(e.$$.fragment)},m(t,i){P(e,t,i),n=!0},p(t,i){const s={};i&2048&&(s.selectable=t[11]),i&1&&(s.value=t[0]),i&64&&(s.show_legend=t[6]),i&2&&(s.color_map=t[1]),e.$set(s)},i(t){n||(S(e.$$.fragment,t),n=!0)},o(t){C(e.$$.fragment,t),n=!1},d(t){K(e,t)}}}function st(l){let e,n;return e=new pe({}),{c(){Z(e.$$.fragment)},m(t,i){P(e,t,i),n=!0},i(t){n||(S(e.$$.fragment,t),n=!0)},o(t){C(e.$$.fragment,t),n=!1},d(t){K(e,t)}}}function rt(l){let e,n,t,i,s,o,a;const r=[{autoscroll:l[2].autoscroll},{i18n:l[2].i18n},l[13]];let c={};for(let _=0;_<r.length;_+=1)c=Ol(c,r[_]);e=new kl({props:c});let f=l[7]&&dl(l);const g=[it,ot],p=[];function b(_,d){return _[0]?0:1}return i=b(l),s=p[i]=g[i](l),{c(){Z(e.$$.fragment),n=ke(),f&&f.c(),t=ke(),s.c(),o=Te()},m(_,d){P(e,_,d),ee(_,n,d),f&&f.m(_,d),ee(_,t,d),p[i].m(_,d),ee(_,o,d),a=!0},p(_,d){const w=d&8196?Ll(r,[d&4&&{autoscroll:_[2].autoscroll},d&4&&{i18n:_[2].i18n},d&8192&&Il(_[13])]):{};e.$set(w),_[7]?f?(f.p(_,d),d&128&&S(f,1)):(f=dl(_),f.c(),S(f,1),f.m(t.parentNode,t)):f&&(ce(),C(f,1,1,()=>{f=null}),fe());let z=i;i=b(_),i===z?p[i].p(_,d):(ce(),C(p[z],1,1,()=>{p[z]=null}),fe(),s=p[i],s?s.p(_,d):(s=p[i]=g[i](_),s.c()),S(s,1),s.m(o.parentNode,o))},i(_){a||(S(e.$$.fragment,_),S(f),S(s),a=!0)},o(_){C(e.$$.fragment,_),C(f),C(s),a=!1},d(_){_&&(x(n),x(t),x(o)),K(e,_),f&&f.d(_),p[i].d(_)}}}function at(l){let e,n,t,i;const s=[xn,$n],o=[];function a(r,c){return r[12]?1:0}return e=a(l),n=o[e]=s[e](l),{c(){n.c(),t=Te()},m(r,c){o[e].m(r,c),ee(r,t,c),i=!0},p(r,[c]){let f=e;e=a(r),e===f?o[e].p(r,c):(ce(),C(o[f],1,1,()=>{o[f]=null}),fe(),n=o[e],n?n.p(r,c):(n=o[e]=s[e](r),n.c()),S(n,1),n.m(t.parentNode,t))},i(r){i||(S(n),i=!0)},o(r){C(n),i=!1},d(r){r&&x(t),o[e].d(r)}}}function ft(l,e,n){let{gradio:t}=e,{elem_id:i=""}=e,{elem_classes:s=[]}=e,{visible:o=!0}=e,{value:a}=e,r,{show_legend:c}=e,{color_map:f={}}=e,{label:g=t.i18n("highlighted_text.highlighted_text")}=e,{container:p=!0}=e,{scale:b=null}=e,{min_width:_=void 0}=e,{_selectable:d=!1}=e,{combine_adjacent:w=!1}=e,{interactive:z}=e,{loading_status:y}=e;const h=({detail:m})=>t.dispatch("select",m);function k(m){a=m,n(0,a),n(14,w)}const j=()=>t.dispatch("change");return l.$$set=m=>{"gradio"in m&&n(2,t=m.gradio),"elem_id"in m&&n(3,i=m.elem_id),"elem_classes"in m&&n(4,s=m.elem_classes),"visible"in m&&n(5,o=m.visible),"value"in m&&n(0,a=m.value),"show_legend"in m&&n(6,c=m.show_legend),"color_map"in m&&n(1,f=m.color_map),"label"in m&&n(7,g=m.label),"container"in m&&n(8,p=m.container),"scale"in m&&n(9,b=m.scale),"min_width"in m&&n(10,_=m.min_width),"_selectable"in m&&n(11,d=m._selectable),"combine_adjacent"in m&&n(14,w=m.combine_adjacent),"interactive"in m&&n(12,z=m.interactive),"loading_status"in m&&n(13,y=m.loading_status)},l.$$.update=()=>{l.$$.dirty&2&&!f&&Object.keys(f).length&&n(1,f),l.$$.dirty&16385&&a&&w&&n(0,a=vl(a,"equal")),l.$$.dirty&32773&&a!==r&&(n(15,r=a),t.dispatch("change"))},[a,f,t,i,s,o,c,g,p,b,_,d,z,y,w,r,h,k,j]}class bt extends Yn{constructor(e){super(),Wn(this,e,ft,at,Xn,{gradio:2,elem_id:3,elem_classes:4,visible:5,value:0,show_legend:6,color_map:1,label:7,container:8,scale:9,min_width:10,_selectable:11,combine_adjacent:14,interactive:12,loading_status:13})}}export{Un as BaseInteractiveHighlightedText,zn as BaseStaticHighlightedText,bt as default};
//# sourceMappingURL=Index-b1c13a4a.js.map
