import{B as m}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import"./Index-26cfc80a.js";import"./index-a80d931b.js";import"./svelte/svelte.js";const{SvelteComponent:u,create_component:r,create_slot:d,destroy_component:b,get_all_dirty_from_scope:g,get_slot_changes:v,init:p,mount_component:h,safe_not_equal:k,transition_in:f,transition_out:c,update_slot_base:w}=window.__gradio__svelte__internal;function B(i){let l;const s=i[3].default,e=d(s,i,i[4],null);return{c(){e&&e.c()},m(t,n){e&&e.m(t,n),l=!0},p(t,n){e&&e.p&&(!l||n&16)&&w(e,s,t,t[4],l?v(s,t[4],n,null):g(t[4]),null)},i(t){l||(f(e,t),l=!0)},o(t){c(e,t),l=!1},d(t){e&&e.d(t)}}}function q(i){let l,s;return l=new m({props:{elem_id:i[0],elem_classes:i[1],visible:i[2],explicit_call:!0,$$slots:{default:[B]},$$scope:{ctx:i}}}),{c(){r(l.$$.fragment)},m(e,t){h(l,e,t),s=!0},p(e,[t]){const n={};t&1&&(n.elem_id=e[0]),t&2&&(n.elem_classes=e[1]),t&4&&(n.visible=e[2]),t&16&&(n.$$scope={dirty:t,ctx:e}),l.$set(n)},i(e){s||(f(l.$$.fragment,e),s=!0)},o(e){c(l.$$.fragment,e),s=!1},d(e){b(l,e)}}}function C(i,l,s){let{$$slots:e={},$$scope:t}=l,{elem_id:n}=l,{elem_classes:_}=l,{visible:a=!0}=l;return i.$$set=o=>{"elem_id"in o&&s(0,n=o.elem_id),"elem_classes"in o&&s(1,_=o.elem_classes),"visible"in o&&s(2,a=o.visible),"$$scope"in o&&s(4,t=o.$$scope)},[n,_,a,e,t]}class A extends u{constructor(l){super(),p(this,l,C,q,k,{elem_id:0,elem_classes:1,visible:2})}}export{A as default};
//# sourceMappingURL=Index-0b8b008b.js.map
