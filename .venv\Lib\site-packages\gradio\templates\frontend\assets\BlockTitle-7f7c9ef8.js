import{I as h}from"./Info-84f5385d.js";import"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";const{SvelteComponent:g,attr:d,check_outros:k,create_component:w,create_slot:$,destroy_component:B,detach:c,element:I,empty:j,get_all_dirty_from_scope:q,get_slot_changes:v,group_outros:C,init:N,insert:m,mount_component:S,safe_not_equal:T,set_data:z,space:A,text:D,toggle_class:u,transition_in:r,transition_out:p,update_slot_base:E}=window.__gradio__svelte__internal;function b(f){let e,o;return e=new h({props:{$$slots:{default:[F]},$$scope:{ctx:f}}}),{c(){w(e.$$.fragment)},m(l,s){S(e,l,s),o=!0},p(l,s){const a={};s&10&&(a.$$scope={dirty:s,ctx:l}),e.$set(a)},i(l){o||(r(e.$$.fragment,l),o=!0)},o(l){p(e.$$.fragment,l),o=!1},d(l){B(e,l)}}}function F(f){let e;return{c(){e=D(f[1])},m(o,l){m(o,e,l)},p(o,l){l&2&&z(e,o[1])},d(o){o&&c(e)}}}function G(f){let e,o,l,s;const a=f[2].default,i=$(a,f,f[3],null);let t=f[1]&&b(f);return{c(){e=I("span"),i&&i.c(),o=A(),t&&t.c(),l=j(),d(e,"data-testid","block-info"),d(e,"class","svelte-1gfkn6j"),u(e,"sr-only",!f[0]),u(e,"hide",!f[0]),u(e,"has-info",f[1]!=null)},m(n,_){m(n,e,_),i&&i.m(e,null),m(n,o,_),t&&t.m(n,_),m(n,l,_),s=!0},p(n,[_]){i&&i.p&&(!s||_&8)&&E(i,a,n,n[3],s?v(a,n[3],_,null):q(n[3]),null),(!s||_&1)&&u(e,"sr-only",!n[0]),(!s||_&1)&&u(e,"hide",!n[0]),(!s||_&2)&&u(e,"has-info",n[1]!=null),n[1]?t?(t.p(n,_),_&2&&r(t,1)):(t=b(n),t.c(),r(t,1),t.m(l.parentNode,l)):t&&(C(),p(t,1,1,()=>{t=null}),k())},i(n){s||(r(i,n),r(t),s=!0)},o(n){p(i,n),p(t),s=!1},d(n){n&&(c(e),c(o),c(l)),i&&i.d(n),t&&t.d(n)}}}function H(f,e,o){let{$$slots:l={},$$scope:s}=e,{show_label:a=!0}=e,{info:i=void 0}=e;return f.$$set=t=>{"show_label"in t&&o(0,a=t.show_label),"info"in t&&o(1,i=t.info),"$$scope"in t&&o(3,s=t.$$scope)},[a,i,l,s]}class L extends g{constructor(e){super(),N(this,e,H,G,T,{show_label:0,info:1})}}export{L as B};
//# sourceMappingURL=BlockTitle-7f7c9ef8.js.map
