import{B as hi}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{f as ue,u as Gi}from"./utils-572af92b.js";import{B as _i}from"./BlockLabel-f27805b1.js";import{I as Yi}from"./IconButton-7294c90b.js";import{E as mi}from"./Empty-28f63bf0.js";import{S as Zi}from"./ShareButton-7dae44e7.js";import{D as Ji,a as Ki}from"./DownloadLink-7ff36416.js";import{S as gi}from"./Index-26cfc80a.js";import{M as Mt}from"./Music-755043aa.js";import{a as Qi,P as pi,T as $i}from"./Trim-1b343e72.js";import{U as xi}from"./Undo-b088de14.js";import{r as Cn}from"./file-url-bef2dc1b.js";import{_ as En,p as eo,u as to}from"./index-a80d931b.js";import{U as no}from"./ModifyUpload.svelte_svelte_type_style_lang-4ba51100.js";import{M as vi}from"./ModifyUpload-66b0c302.js";import{S as io}from"./SelectSource-f5281119.js";import{U as oo}from"./UploadText-39c67ae9.js";import{default as La}from"./Example-ba6a74fc.js";import"./svelte/svelte.js";import"./Clear-2c7bae91.js";import"./Upload-351cc897.js";const{SvelteComponent:ro,append:so,attr:se,detach:lo,init:ao,insert:uo,noop:Kt,safe_not_equal:co,svg_element:Rn}=window.__gradio__svelte__internal;function fo(i){let e,t;return{c(){e=Rn("svg"),t=Rn("path"),se(t,"stroke","currentColor"),se(t,"stroke-width","1.5"),se(t,"stroke-linecap","round"),se(t,"stroke-linejoin","round"),se(t,"d","M21.044 5.704a.6.6 0 0 1 .956.483v11.626a.6.6 0 0 1-.956.483l-7.889-5.813a.6.6 0 0 1 0-.966l7.89-5.813ZM10.044 5.704a.6.6 0 0 1 .956.483v11.626a.6.6 0 0 1-.956.483l-7.888-5.813a.6.6 0 0 1 0-.966l7.888-5.813Z"),se(e,"xmlns","http://www.w3.org/2000/svg"),se(e,"width","24px"),se(e,"height","24px"),se(e,"fill","currentColor"),se(e,"stroke-width","1.5"),se(e,"viewBox","0 0 24 24"),se(e,"color","currentColor")},m(n,o){uo(n,e,o),so(e,t)},p:Kt,i:Kt,o:Kt,d(n){n&&lo(e)}}}class ho extends ro{constructor(e){super(),ao(this,e,null,fo,co,{})}}const{SvelteComponent:_o,append:mo,attr:le,detach:go,init:po,insert:vo,noop:Qt,safe_not_equal:bo,svg_element:Sn}=window.__gradio__svelte__internal;function wo(i){let e,t;return{c(){e=Sn("svg"),t=Sn("path"),le(t,"stroke","currentColor"),le(t,"stroke-width","1.5"),le(t,"stroke-linecap","round"),le(t,"stroke-linejoin","round"),le(t,"d","M2.956 5.704A.6.6 0 0 0 2 6.187v11.626a.6.6 0 0 0 .956.483l7.889-5.813a.6.6 0 0 0 0-.966l-7.89-5.813ZM13.956 5.704a.6.6 0 0 0-.956.483v11.626a.6.6 0 0 0 .956.483l7.889-5.813a.6.6 0 0 0 0-.966l-7.89-5.813Z"),le(e,"xmlns","http://www.w3.org/2000/svg"),le(e,"width","24px"),le(e,"height","24px"),le(e,"fill","currentColor"),le(e,"stroke-width","1.5"),le(e,"viewBox","0 0 24 24"),le(e,"color","currentColor")},m(n,o){vo(n,e,o),mo(e,t)},p:Qt,i:Qt,o:Qt,d(n){n&&go(e)}}}class ko extends _o{constructor(e){super(),po(this,e,null,wo,bo,{})}}const{SvelteComponent:yo,append:vt,attr:ne,detach:Co,init:Eo,insert:Ro,noop:$t,safe_not_equal:So,svg_element:bt,text:Do}=window.__gradio__svelte__internal;function Lo(i){let e,t,n,o,r;return{c(){e=bt("svg"),t=bt("title"),n=Do("Low volume"),o=bt("path"),r=bt("path"),ne(o,"d","M19.5 7.5C19.5 7.5 21 9 21 11.5C21 14 19.5 15.5 19.5 15.5"),ne(o,"stroke-width","1.5"),ne(o,"stroke-linecap","round"),ne(o,"stroke-linejoin","round"),ne(r,"d","M2 13.8571V10.1429C2 9.03829 2.89543 8.14286 4 8.14286H6.9C7.09569 8.14286 7.28708 8.08544 7.45046 7.97772L13.4495 4.02228C14.1144 3.5839 15 4.06075 15 4.85714V19.1429C15 19.9392 14.1144 20.4161 13.4495 19.9777L7.45046 16.0223C7.28708 15.9146 7.09569 15.8571 6.9 15.8571H4C2.89543 15.8571 2 14.9617 2 13.8571Z"),ne(r,"stroke-width","1.5"),ne(e,"width","100%"),ne(e,"height","100%"),ne(e,"viewBox","0 0 24 24"),ne(e,"stroke-width","1.5"),ne(e,"fill","none"),ne(e,"xmlns","http://www.w3.org/2000/svg"),ne(e,"stroke","currentColor"),ne(e,"color","currentColor")},m(s,u){Ro(s,e,u),vt(e,t),vt(t,n),vt(e,o),vt(e,r)},p:$t,i:$t,o:$t,d(s){s&&Co(e)}}}class Mo extends yo{constructor(e){super(),Eo(this,e,null,Lo,So,{})}}const{SvelteComponent:Po,append:ot,attr:F,detach:Ao,init:To,insert:zo,noop:xt,safe_not_equal:Wo,svg_element:rt,text:qo}=window.__gradio__svelte__internal;function Oo(i){let e,t,n,o,r,s;return{c(){e=rt("svg"),t=rt("title"),n=qo("High volume"),o=rt("path"),r=rt("path"),s=rt("path"),F(o,"d","M1 13.8571V10.1429C1 9.03829 1.89543 8.14286 3 8.14286H5.9C6.09569 8.14286 6.28708 8.08544 6.45046 7.97772L12.4495 4.02228C13.1144 3.5839 14 4.06075 14 4.85714V19.1429C14 19.9392 13.1144 20.4161 12.4495 19.9777L6.45046 16.0223C6.28708 15.9146 6.09569 15.8571 5.9 15.8571H3C1.89543 15.8571 1 14.9617 1 13.8571Z"),F(o,"stroke-width","1.5"),F(r,"d","M17.5 7.5C17.5 7.5 19 9 19 11.5C19 14 17.5 15.5 17.5 15.5"),F(r,"stroke-width","1.5"),F(r,"stroke-linecap","round"),F(r,"stroke-linejoin","round"),F(s,"d","M20.5 4.5C20.5 4.5 23 7 23 11.5C23 16 20.5 18.5 20.5 18.5"),F(s,"stroke-width","1.5"),F(s,"stroke-linecap","round"),F(s,"stroke-linejoin","round"),F(e,"width","100%"),F(e,"height","100%"),F(e,"viewBox","0 0 24 24"),F(e,"stroke-width","1.5"),F(e,"fill","none"),F(e,"stroke","currentColor"),F(e,"xmlns","http://www.w3.org/2000/svg"),F(e,"color","currentColor")},m(u,a){zo(u,e,a),ot(e,t),ot(t,n),ot(e,o),ot(e,r),ot(e,s)},p:xt,i:xt,o:xt,d(u){u&&Ao(e)}}}class Bo extends Po{constructor(e){super(),To(this,e,null,Oo,Wo,{})}}const{SvelteComponent:Ho,append:we,attr:j,detach:No,init:Io,insert:Vo,noop:en,safe_not_equal:Uo,svg_element:ke,text:jo}=window.__gradio__svelte__internal;function Fo(i){let e,t,n,o,r,s,u,a,d;return{c(){e=ke("svg"),t=ke("title"),n=jo("Muted volume"),o=ke("g"),r=ke("path"),s=ke("path"),u=ke("defs"),a=ke("clipPath"),d=ke("rect"),j(r,"d","M18 14L20.0005 12M22 10L20.0005 12M20.0005 12L18 10M20.0005 12L22 14"),j(r,"stroke-width","1.5"),j(r,"stroke-linecap","round"),j(r,"stroke-linejoin","round"),j(s,"d","M2 13.8571V10.1429C2 9.03829 2.89543 8.14286 4 8.14286H6.9C7.09569 8.14286 7.28708 8.08544 7.45046 7.97772L13.4495 4.02228C14.1144 3.5839 15 4.06075 15 4.85714V19.1429C15 19.9392 14.1144 20.4161 13.4495 19.9777L7.45046 16.0223C7.28708 15.9146 7.09569 15.8571 6.9 15.8571H4C2.89543 15.8571 2 14.9617 2 13.8571Z"),j(s,"stroke-width","1.5"),j(o,"clip-path","url(#clip0_3173_16686)"),j(d,"width","24"),j(d,"height","24"),j(d,"fill","white"),j(a,"id","clip0_3173_16686"),j(e,"width","100%"),j(e,"height","100%"),j(e,"viewBox","0 0 24 24"),j(e,"stroke-width","1.5"),j(e,"fill","none"),j(e,"xmlns","http://www.w3.org/2000/svg"),j(e,"stroke","currentColor"),j(e,"color","currentColor")},m(l,c){Vo(l,e,c),we(e,t),we(t,n),we(e,o),we(o,r),we(o,s),we(e,u),we(u,a),we(a,d)},p:en,i:en,o:en,d(l){l&&No(e)}}}class Xo extends Ho{constructor(e){super(),Io(this,e,null,Fo,Uo,{})}}var Go=globalThis&&globalThis.__awaiter||function(i,e,t,n){function o(r){return r instanceof t?r:new t(function(s){s(r)})}return new(t||(t=Promise))(function(r,s){function u(l){try{d(n.next(l))}catch(c){s(c)}}function a(l){try{d(n.throw(l))}catch(c){s(c)}}function d(l){l.done?r(l.value):o(l.value).then(u,a)}d((n=n.apply(i,e||[])).next())})};function Yo(i,e){return Go(this,void 0,void 0,function*(){const t=new AudioContext({sampleRate:e});return t.decodeAudioData(i).finally(()=>t.close())})}function Zo(i){const e=i[0];if(e.some(t=>t>1||t<-1)){const t=e.length;let n=0;for(let o=0;o<t;o++){const r=Math.abs(e[o]);r>n&&(n=r)}for(const o of i)for(let r=0;r<t;r++)o[r]/=n}return i}function Jo(i,e){return typeof i[0]=="number"&&(i=[i]),Zo(i),{duration:e,length:i[0].length,sampleRate:i[0].length/e,numberOfChannels:i.length,getChannelData:t=>i?.[t],copyFromChannel:AudioBuffer.prototype.copyFromChannel,copyToChannel:AudioBuffer.prototype.copyToChannel}}const tn={decode:Yo,createBuffer:Jo};var Dn=globalThis&&globalThis.__awaiter||function(i,e,t,n){function o(r){return r instanceof t?r:new t(function(s){s(r)})}return new(t||(t=Promise))(function(r,s){function u(l){try{d(n.next(l))}catch(c){s(c)}}function a(l){try{d(n.throw(l))}catch(c){s(c)}}function d(l){l.done?r(l.value):o(l.value).then(u,a)}d((n=n.apply(i,e||[])).next())})};function Ko(i,e,t){var n,o;return Dn(this,void 0,void 0,function*(){const r=yield fetch(i,t);{const s=(n=r.clone().body)===null||n===void 0?void 0:n.getReader(),u=Number((o=r.headers)===null||o===void 0?void 0:o.get("Content-Length"));let a=0;const d=(l,c)=>Dn(this,void 0,void 0,function*(){if(l)return;a+=c?.length||0;const f=Math.round(a/u*100);return e(f),s?.read().then(({done:_,value:m})=>d(_,m))});s?.read().then(({done:l,value:c})=>d(l,c))}return r.blob()})}const Qo={fetchBlob:Ko};class Pt{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,n){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),n?.once){const o=()=>{this.removeEventListener(e,o),this.removeEventListener(e,t)};return this.addEventListener(e,o),o}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var n;(n=this.listeners[e])===null||n===void 0||n.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach(n=>n(...t))}}class $o extends Pt{constructor(e){super(),this.isExternalMedia=!1,e.media?(this.media=e.media,this.isExternalMedia=!0):this.media=document.createElement("audio"),e.mediaControls&&(this.media.controls=!0),e.autoplay&&(this.media.autoplay=!0),e.playbackRate!=null&&this.onceMediaEvent("canplay",()=>{e.playbackRate!=null&&(this.media.playbackRate=e.playbackRate)})}onMediaEvent(e,t,n){return this.media.addEventListener(e,t,n),()=>this.media.removeEventListener(e,t)}onceMediaEvent(e,t){return this.onMediaEvent(e,t,{once:!0})}getSrc(){return this.media.currentSrc||this.media.src||""}revokeSrc(){const e=this.getSrc();e.startsWith("blob:")&&URL.revokeObjectURL(e)}setSrc(e,t){if(this.getSrc()===e)return;this.revokeSrc();const o=t instanceof Blob?URL.createObjectURL(t):e;this.media.src=o,this.media.load()}destroy(){this.media.pause(),!this.isExternalMedia&&(this.media.remove(),this.revokeSrc(),this.media.src="",this.media.load())}setMediaElement(e){this.media=e}play(){return this.media.play()}pause(){this.media.pause()}isPlaying(){return!this.media.paused&&!this.media.ended}setTime(e){this.media.currentTime=e}getDuration(){return this.media.duration}getCurrentTime(){return this.media.currentTime}getVolume(){return this.media.volume}setVolume(e){this.media.volume=e}getMuted(){return this.media.muted}setMuted(e){this.media.muted=e}getPlaybackRate(){return this.media.playbackRate}setPlaybackRate(e,t){t!=null&&(this.media.preservesPitch=t),this.media.playbackRate=e}getMediaElement(){return this.media}setSinkId(e){return this.media.setSinkId(e)}}function xo(i,e,t,n,o=5){let r=()=>{};if(!i)return r;const s=u=>{if(u.button===2)return;u.preventDefault(),u.stopPropagation(),i.style.touchAction="none";let a=u.clientX,d=u.clientY,l=!1;const c=m=>{m.preventDefault(),m.stopPropagation();const h=m.clientX,g=m.clientY;if(l||Math.abs(h-a)>=o||Math.abs(g-d)>=o){const{left:k,top:E}=i.getBoundingClientRect();l||(l=!0,t?.(a-k,d-E)),e(h-a,g-d,h-k,g-E),a=h,d=g}},f=m=>{l&&(m.preventDefault(),m.stopPropagation())},_=()=>{i.style.touchAction="",l&&n?.(),r()};document.addEventListener("pointermove",c),document.addEventListener("pointerup",_),document.addEventListener("pointerleave",_),document.addEventListener("click",f,!0),r=()=>{document.removeEventListener("pointermove",c),document.removeEventListener("pointerup",_),document.removeEventListener("pointerleave",_),setTimeout(()=>{document.removeEventListener("click",f,!0)},10)}};return i.addEventListener("pointerdown",s),()=>{r(),i.removeEventListener("pointerdown",s)}}class At extends Pt{constructor(e,t){super(),this.timeouts=[],this.isScrolling=!1,this.audioData=null,this.resizeObserver=null,this.isDragging=!1,this.options=e;const n=this.parentFromOptionsContainer(e.container);this.parent=n;const[o,r]=this.initHtml();n.appendChild(o),this.container=o,this.scrollContainer=r.querySelector(".scroll"),this.wrapper=r.querySelector(".wrapper"),this.canvasWrapper=r.querySelector(".canvases"),this.progressWrapper=r.querySelector(".progress"),this.cursor=r.querySelector(".cursor"),t&&r.appendChild(t),this.initEvents()}parentFromOptionsContainer(e){let t;if(typeof e=="string"?t=document.querySelector(e):e instanceof HTMLElement&&(t=e),!t)throw new Error("Container not found");return t}initEvents(){const e=n=>{const o=this.wrapper.getBoundingClientRect(),r=n.clientX-o.left,s=n.clientX-o.left,u=r/o.width,a=s/o.height;return[u,a]};this.wrapper.addEventListener("click",n=>{const[o,r]=e(n);this.emit("click",o,r)}),this.wrapper.addEventListener("dblclick",n=>{const[o,r]=e(n);this.emit("dblclick",o,r)}),this.options.dragToSeek&&this.initDrag(),this.scrollContainer.addEventListener("scroll",()=>{const{scrollLeft:n,scrollWidth:o,clientWidth:r}=this.scrollContainer,s=n/o,u=(n+r)/o;this.emit("scroll",s,u)});const t=this.createDelay(100);this.resizeObserver=new ResizeObserver(()=>{t(()=>this.reRender())}),this.resizeObserver.observe(this.scrollContainer)}initDrag(){xo(this.wrapper,(e,t,n)=>{this.emit("drag",Math.max(0,Math.min(1,n/this.wrapper.getBoundingClientRect().width)))},()=>this.isDragging=!0,()=>this.isDragging=!1)}getHeight(){return this.options.height==null?128:isNaN(Number(this.options.height))?this.options.height==="auto"&&this.parent.clientHeight||128:Number(this.options.height)}initHtml(){const e=document.createElement("div"),t=e.attachShadow({mode:"open"});return t.innerHTML=`
      <style>
        :host {
          user-select: none;
          min-width: 1px;
        }
        :host audio {
          display: block;
          width: 100%;
        }
        :host .scroll {
          overflow-x: auto;
          overflow-y: hidden;
          width: 100%;
          position: relative;
        }
        :host .noScrollbar {
          scrollbar-color: transparent;
          scrollbar-width: none;
        }
        :host .noScrollbar::-webkit-scrollbar {
          display: none;
          -webkit-appearance: none;
        }
        :host .wrapper {
          position: relative;
          overflow: visible;
          z-index: 2;
        }
        :host .canvases {
          min-height: ${this.getHeight()}px;
        }
        :host .canvases > div {
          position: relative;
        }
        :host canvas {
          display: block;
          position: absolute;
          top: 0;
          image-rendering: pixelated;
        }
        :host .progress {
          pointer-events: none;
          position: absolute;
          z-index: 2;
          top: 0;
          left: 0;
          width: 0;
          height: 100%;
          overflow: hidden;
        }
        :host .progress > div {
          position: relative;
        }
        :host .cursor {
          pointer-events: none;
          position: absolute;
          z-index: 5;
          top: 0;
          left: 0;
          height: 100%;
          border-radius: 2px;
        }
      </style>

      <div class="scroll" part="scroll">
        <div class="wrapper" part="wrapper">
          <div class="canvases"></div>
          <div class="progress" part="progress"></div>
          <div class="cursor" part="cursor"></div>
        </div>
      </div>
    `,[e,t]}setOptions(e){if(this.options.container!==e.container){const t=this.parentFromOptionsContainer(e.container);t.appendChild(this.container),this.parent=t}e.dragToSeek&&!this.options.dragToSeek&&this.initDrag(),this.options=e,this.reRender()}getWrapper(){return this.wrapper}getScroll(){return this.scrollContainer.scrollLeft}destroy(){var e;this.container.remove(),(e=this.resizeObserver)===null||e===void 0||e.disconnect()}createDelay(e=10){const t={};return this.timeouts.push(t),n=>{t.timeout&&clearTimeout(t.timeout),t.timeout=setTimeout(n,e)}}convertColorValues(e){if(!Array.isArray(e))return e||"";if(e.length<2)return e[0]||"";const t=document.createElement("canvas"),o=t.getContext("2d").createLinearGradient(0,0,0,t.height),r=1/(e.length-1);return e.forEach((s,u)=>{const a=u*r;o.addColorStop(a,s)}),o}renderBarWaveform(e,t,n,o){const r=e[0],s=e[1]||e[0],u=r.length,{width:a,height:d}=n.canvas,l=d/2,c=window.devicePixelRatio||1,f=t.barWidth?t.barWidth*c:1,_=t.barGap?t.barGap*c:t.barWidth?f/2:0,m=t.barRadius||0,h=a/(f+_)/u,g=m&&"roundRect"in n?"roundRect":"rect";n.beginPath();let k=0,E=0,p=0;for(let L=0;L<=u;L++){const R=Math.round(L*h);if(R>k){const T=Math.round(E*l*o),S=Math.round(p*l*o),z=T+S||1;let H=l-T;t.barAlign==="top"?H=0:t.barAlign==="bottom"&&(H=d-z),n[g](k*(f+_),H,f,z,m),k=R,E=0,p=0}const A=Math.abs(r[L]||0),C=Math.abs(s[L]||0);A>E&&(E=A),C>p&&(p=C)}n.fill(),n.closePath()}renderLineWaveform(e,t,n,o){const r=s=>{const u=e[s]||e[0],a=u.length,{height:d}=n.canvas,l=d/2,c=n.canvas.width/a;n.moveTo(0,l);let f=0,_=0;for(let m=0;m<=a;m++){const h=Math.round(m*c);if(h>f){const k=Math.round(_*l*o)||1,E=l+k*(s===0?-1:1);n.lineTo(f,E),f=h,_=0}const g=Math.abs(u[m]||0);g>_&&(_=g)}n.lineTo(f,l)};n.beginPath(),r(0),r(1),n.fill(),n.closePath()}renderWaveform(e,t,n){if(n.fillStyle=this.convertColorValues(t.waveColor),t.renderFunction){t.renderFunction(e,n);return}let o=t.barHeight||1;if(t.normalize){const r=Array.from(e[0]).reduce((s,u)=>Math.max(s,Math.abs(u)),0);o=r?1/r:1}if(t.barWidth||t.barGap||t.barAlign){this.renderBarWaveform(e,t,n,o);return}this.renderLineWaveform(e,t,n,o)}renderSingleCanvas(e,t,n,o,r,s,u,a){const d=window.devicePixelRatio||1,l=document.createElement("canvas"),c=e[0].length;l.width=Math.round(n*(s-r)/c),l.height=o*d,l.style.width=`${Math.floor(l.width/d)}px`,l.style.height=`${o}px`,l.style.left=`${Math.floor(r*n/d/c)}px`,u.appendChild(l);const f=l.getContext("2d");if(this.renderWaveform(e.map(_=>_.slice(r,s)),t,f),l.width>0&&l.height>0){const _=l.cloneNode(),m=_.getContext("2d");m.drawImage(l,0,0),m.globalCompositeOperation="source-in",m.fillStyle=this.convertColorValues(t.progressColor),m.fillRect(0,0,l.width,l.height),a.appendChild(_)}}renderChannel(e,t,n){const o=document.createElement("div"),r=this.getHeight();o.style.height=`${r}px`,this.canvasWrapper.style.minHeight=`${r}px`,this.canvasWrapper.appendChild(o);const s=o.cloneNode();this.progressWrapper.appendChild(s);const{scrollLeft:u,scrollWidth:a,clientWidth:d}=this.scrollContainer,l=e[0].length,c=l/a;let f=Math.min(At.MAX_CANVAS_WIDTH,d);if(t.barWidth||t.barGap){const R=t.barWidth||.5,A=t.barGap||R/2,C=R+A;f%C!==0&&(f=Math.floor(f/C)*C)}const _=Math.floor(Math.abs(u)*c),m=Math.floor(_+f*c),h=m-_,g=(R,A)=>{this.renderSingleCanvas(e,t,n,r,Math.max(0,R),Math.min(A,l),o,s)},k=this.createDelay(),E=this.createDelay(),p=(R,A)=>{g(R,A),R>0&&k(()=>{p(R-h,A-h)})},L=(R,A)=>{g(R,A),A<l&&E(()=>{L(R+h,A+h)})};p(_,m),m<l&&L(m,m+h)}render(e){this.timeouts.forEach(u=>u.timeout&&clearTimeout(u.timeout)),this.timeouts=[],this.canvasWrapper.innerHTML="",this.progressWrapper.innerHTML="",this.wrapper.style.width="",this.options.width!=null&&(this.scrollContainer.style.width=typeof this.options.width=="number"?`${this.options.width}px`:this.options.width);const t=window.devicePixelRatio||1,n=this.scrollContainer.clientWidth,o=Math.ceil(e.duration*(this.options.minPxPerSec||0));this.isScrolling=o>n;const r=this.options.fillParent&&!this.isScrolling,s=(r?n:o)*t;if(this.wrapper.style.width=r?"100%":`${o}px`,this.scrollContainer.style.overflowX=this.isScrolling?"auto":"hidden",this.scrollContainer.classList.toggle("noScrollbar",!!this.options.hideScrollbar),this.cursor.style.backgroundColor=`${this.options.cursorColor||this.options.progressColor}`,this.cursor.style.width=`${this.options.cursorWidth}px`,this.options.splitChannels)for(let u=0;u<e.numberOfChannels;u++){const a=Object.assign(Object.assign({},this.options),this.options.splitChannels[u]);this.renderChannel([e.getChannelData(u)],a,s)}else{const u=[e.getChannelData(0)];e.numberOfChannels>1&&u.push(e.getChannelData(1)),this.renderChannel(u,this.options,s)}this.audioData=e,this.emit("render")}reRender(){if(!this.audioData)return;const e=this.progressWrapper.clientWidth;this.render(this.audioData);const t=this.progressWrapper.clientWidth;this.scrollContainer.scrollLeft+=t-e}zoom(e){this.options.minPxPerSec=e,this.reRender()}scrollIntoView(e,t=!1){const{clientWidth:n,scrollLeft:o,scrollWidth:r}=this.scrollContainer,s=r*e,u=n/2,a=t&&this.options.autoCenter&&!this.isDragging?u:n;if(s>o+a||s<o)if(this.options.autoCenter&&!this.isDragging){const d=u/20;s-(o+u)>=d&&s<o+n?this.scrollContainer.scrollLeft+=d:this.scrollContainer.scrollLeft=s-u}else this.isDragging?this.scrollContainer.scrollLeft=s<o?s-10:s-n+10:this.scrollContainer.scrollLeft=s;{const{scrollLeft:d}=this.scrollContainer,l=d/r,c=(d+n)/r;this.emit("scroll",l,c)}}renderProgress(e,t){if(isNaN(e))return;const n=e*100;this.canvasWrapper.style.clipPath=`polygon(${n}% 0, 100% 0, 100% 100%, ${n}% 100%)`,this.progressWrapper.style.width=`${n}%`,this.cursor.style.left=`${n}%`,this.cursor.style.marginLeft=Math.round(n)===100?`-${this.options.cursorWidth}px`:"",this.isScrolling&&this.options.autoScroll&&this.scrollIntoView(e,t)}}At.MAX_CANVAS_WIDTH=4e3;class er extends Pt{constructor(){super(...arguments),this.unsubscribe=()=>{}}start(){this.unsubscribe=this.on("tick",()=>{requestAnimationFrame(()=>{this.emit("tick")})}),this.emit("tick")}stop(){this.unsubscribe()}destroy(){this.unsubscribe()}}var nn=globalThis&&globalThis.__awaiter||function(i,e,t,n){function o(r){return r instanceof t?r:new t(function(s){s(r)})}return new(t||(t=Promise))(function(r,s){function u(l){try{d(n.next(l))}catch(c){s(c)}}function a(l){try{d(n.throw(l))}catch(c){s(c)}}function d(l){l.done?r(l.value):o(l.value).then(u,a)}d((n=n.apply(i,e||[])).next())})};class tr extends Pt{constructor(e=new AudioContext){super(),this.bufferNode=null,this.autoplay=!1,this.playStartTime=0,this.playedDuration=0,this._muted=!1,this.buffer=null,this.currentSrc="",this.paused=!0,this.crossOrigin=null,this.audioContext=e,this.gainNode=this.audioContext.createGain(),this.gainNode.connect(this.audioContext.destination)}load(){return nn(this,void 0,void 0,function*(){})}get src(){return this.currentSrc}set src(e){this.currentSrc=e,fetch(e).then(t=>t.arrayBuffer()).then(t=>this.audioContext.decodeAudioData(t)).then(t=>{this.buffer=t,this.emit("loadedmetadata"),this.emit("canplay"),this.autoplay&&this.play()})}_play(){var e;this.paused&&(this.paused=!1,(e=this.bufferNode)===null||e===void 0||e.disconnect(),this.bufferNode=this.audioContext.createBufferSource(),this.bufferNode.buffer=this.buffer,this.bufferNode.connect(this.gainNode),this.playedDuration>=this.duration&&(this.playedDuration=0),this.bufferNode.start(this.audioContext.currentTime,this.playedDuration),this.playStartTime=this.audioContext.currentTime,this.bufferNode.onended=()=>{this.currentTime>=this.duration&&(this.pause(),this.emit("ended"))})}_pause(){var e;this.paused||(this.paused=!0,(e=this.bufferNode)===null||e===void 0||e.stop(),this.playedDuration+=this.audioContext.currentTime-this.playStartTime)}play(){return nn(this,void 0,void 0,function*(){this._play(),this.emit("play")})}pause(){this._pause(),this.emit("pause")}setSinkId(e){return nn(this,void 0,void 0,function*(){return this.audioContext.setSinkId(e)})}get playbackRate(){var e,t;return(t=(e=this.bufferNode)===null||e===void 0?void 0:e.playbackRate.value)!==null&&t!==void 0?t:1}set playbackRate(e){this.bufferNode&&(this.bufferNode.playbackRate.value=e)}get currentTime(){return this.paused?this.playedDuration:this.playedDuration+this.audioContext.currentTime-this.playStartTime}set currentTime(e){this.emit("seeking"),this.paused?this.playedDuration=e:(this._pause(),this.playedDuration=e,this._play()),this.emit("timeupdate")}get duration(){var e;return((e=this.buffer)===null||e===void 0?void 0:e.duration)||0}get volume(){return this.gainNode.gain.value}set volume(e){this.gainNode.gain.value=e,this.emit("volumechange")}get muted(){return this._muted}set muted(e){this._muted!==e&&(this._muted=e,this._muted?this.gainNode.disconnect():this.gainNode.connect(this.audioContext.destination))}getGainNode(){return this.gainNode}}var Ge=globalThis&&globalThis.__awaiter||function(i,e,t,n){function o(r){return r instanceof t?r:new t(function(s){s(r)})}return new(t||(t=Promise))(function(r,s){function u(l){try{d(n.next(l))}catch(c){s(c)}}function a(l){try{d(n.throw(l))}catch(c){s(c)}}function d(l){l.done?r(l.value):o(l.value).then(u,a)}d((n=n.apply(i,e||[])).next())})};const nr={waveColor:"#999",progressColor:"#555",cursorWidth:1,minPxPerSec:0,fillParent:!0,interact:!0,dragToSeek:!1,autoScroll:!0,autoCenter:!0,sampleRate:8e3};class et extends $o{static create(e){return new et(e)}constructor(e){const t=e.media||(e.backend==="WebAudio"?new tr:void 0);super({media:t,mediaControls:e.mediaControls,autoplay:e.autoplay,playbackRate:e.audioRate}),this.plugins=[],this.decodedData=null,this.subscriptions=[],this.mediaSubscriptions=[],this.options=Object.assign({},nr,e),this.timer=new er;const n=t?void 0:this.getMediaElement();this.renderer=new At(this.options,n),this.initPlayerEvents(),this.initRendererEvents(),this.initTimerEvents(),this.initPlugins();const o=this.options.url||this.getSrc();o?this.load(o,this.options.peaks,this.options.duration):this.options.peaks&&this.options.duration&&this.loadPredecoded()}initTimerEvents(){this.subscriptions.push(this.timer.on("tick",()=>{const e=this.getCurrentTime();this.renderer.renderProgress(e/this.getDuration(),!0),this.emit("timeupdate",e),this.emit("audioprocess",e)}))}initPlayerEvents(){this.mediaSubscriptions.push(this.onMediaEvent("timeupdate",()=>{const e=this.getCurrentTime();this.renderer.renderProgress(e/this.getDuration(),this.isPlaying()),this.emit("timeupdate",e)}),this.onMediaEvent("play",()=>{this.emit("play"),this.timer.start()}),this.onMediaEvent("pause",()=>{this.emit("pause"),this.timer.stop()}),this.onMediaEvent("emptied",()=>{this.timer.stop()}),this.onMediaEvent("ended",()=>{this.emit("finish")}),this.onMediaEvent("seeking",()=>{this.emit("seeking",this.getCurrentTime())}))}initRendererEvents(){this.subscriptions.push(this.renderer.on("click",(e,t)=>{this.options.interact&&(this.seekTo(e),this.emit("interaction",e*this.getDuration()),this.emit("click",e,t))}),this.renderer.on("dblclick",(e,t)=>{this.emit("dblclick",e,t)}),this.renderer.on("scroll",(e,t)=>{const n=this.getDuration();this.emit("scroll",e*n,t*n)}),this.renderer.on("render",()=>{this.emit("redraw")}));{let e;this.subscriptions.push(this.renderer.on("drag",t=>{this.options.interact&&(this.renderer.renderProgress(t),clearTimeout(e),e=setTimeout(()=>{this.seekTo(t)},this.isPlaying()?0:200),this.emit("interaction",t*this.getDuration()),this.emit("drag",t))}))}}initPlugins(){var e;!((e=this.options.plugins)===null||e===void 0)&&e.length&&this.options.plugins.forEach(t=>{this.registerPlugin(t)})}unsubscribePlayerEvents(){this.mediaSubscriptions.forEach(e=>e()),this.mediaSubscriptions=[]}setOptions(e){this.options=Object.assign({},this.options,e),this.renderer.setOptions(this.options),e.audioRate&&this.setPlaybackRate(e.audioRate),e.mediaControls!=null&&(this.getMediaElement().controls=e.mediaControls)}registerPlugin(e){return e.init(this),this.plugins.push(e),this.subscriptions.push(e.once("destroy",()=>{this.plugins=this.plugins.filter(t=>t!==e)})),e}getWrapper(){return this.renderer.getWrapper()}getScroll(){return this.renderer.getScroll()}getActivePlugins(){return this.plugins}loadPredecoded(){return Ge(this,void 0,void 0,function*(){this.options.peaks&&this.options.duration&&(this.decodedData=tn.createBuffer(this.options.peaks,this.options.duration),yield Promise.resolve(),this.renderDecoded())})}renderDecoded(){return Ge(this,void 0,void 0,function*(){this.decodedData&&(this.emit("decode",this.getDuration()),this.renderer.render(this.decodedData))})}loadAudio(e,t,n,o){return Ge(this,void 0,void 0,function*(){if(this.emit("load",e),!this.options.media&&this.isPlaying()&&this.pause(),this.decodedData=null,!t&&!n){const r=s=>this.emit("loading",s);t=yield Qo.fetchBlob(e,r,this.options.fetchParams)}if(this.setSrc(e,t),o=(yield Promise.resolve(o||this.getDuration()))||(yield new Promise(r=>{this.onceMediaEvent("loadedmetadata",()=>r(this.getDuration()))}))||(yield Promise.resolve(0)),n)this.decodedData=tn.createBuffer(n,o);else if(t){const r=yield t.arrayBuffer();this.decodedData=yield tn.decode(r,this.options.sampleRate)}this.renderDecoded(),this.emit("ready",this.getDuration())})}load(e,t,n){return Ge(this,void 0,void 0,function*(){yield this.loadAudio(e,void 0,t,n)})}loadBlob(e,t,n){return Ge(this,void 0,void 0,function*(){yield this.loadAudio("blob",e,t,n)})}zoom(e){if(!this.decodedData)throw new Error("No audio loaded");this.renderer.zoom(e),this.emit("zoom",e)}getDecodedData(){return this.decodedData}exportPeaks({channels:e=2,maxLength:t=8e3,precision:n=1e4}={}){if(!this.decodedData)throw new Error("The audio has not been decoded yet");const o=Math.min(e,this.decodedData.numberOfChannels),r=[];for(let s=0;s<o;s++){const u=this.decodedData.getChannelData(s),a=[],d=Math.round(u.length/t);for(let l=0;l<t;l++){const c=u.slice(l*d,(l+1)*d),f=Math.max(...c);a.push(Math.round(f*n)/n)}r.push(a)}return r}getDuration(){let e=super.getDuration()||0;return(e===0||e===1/0)&&this.decodedData&&(e=this.decodedData.duration),e}toggleInteraction(e){this.options.interact=e}seekTo(e){const t=this.getDuration()*e;this.setTime(t)}playPause(){return Ge(this,void 0,void 0,function*(){return this.isPlaying()?this.pause():this.play()})}stop(){this.pause(),this.setTime(0)}skip(e){this.setTime(this.getCurrentTime()+e)}empty(){this.load("",[[0]],.001)}setMediaElement(e){this.unsubscribePlayerEvents(),super.setMediaElement(e),this.initPlayerEvents()}destroy(){this.emit("destroy"),this.plugins.forEach(e=>e.destroy()),this.subscriptions.forEach(e=>e()),this.unsubscribePlayerEvents(),this.timer.destroy(),this.renderer.destroy(),super.destroy()}}function ir(i){const e=i.numberOfChannels,t=i.length*e*2+44,n=new ArrayBuffer(t),o=new DataView(n);let r=0;const s=function(u,a,d){for(let l=0;l<d.length;l++)u.setUint8(a+l,d.charCodeAt(l))};s(o,r,"RIFF"),r+=4,o.setUint32(r,t-8,!0),r+=4,s(o,r,"WAVE"),r+=4,s(o,r,"fmt "),r+=4,o.setUint32(r,16,!0),r+=4,o.setUint16(r,1,!0),r+=2,o.setUint16(r,e,!0),r+=2,o.setUint32(r,i.sampleRate,!0),r+=4,o.setUint32(r,i.sampleRate*2*e,!0),r+=4,o.setUint16(r,e*2,!0),r+=2,o.setUint16(r,16,!0),r+=2,s(o,r,"data"),r+=4,o.setUint32(r,i.length*e*2,!0),r+=4;for(let u=0;u<i.length;u++)for(let a=0;a<e;a++){const d=Math.max(-1,Math.min(1,i.getChannelData(a)[u]));o.setInt16(r,d*32767,!0),r+=2}return new Uint8Array(n)}const ln=async(i,e,t,n)=>{const o=new AudioContext({sampleRate:n||i.sampleRate}),r=i.numberOfChannels,s=n||i.sampleRate;let u=i.length,a=0;e&&t&&(a=Math.round(e*s),u=Math.round(t*s)-a);const d=o.createBuffer(r,u,s);for(let l=0;l<r;l++){const c=i.getChannelData(l),f=d.getChannelData(l);for(let _=0;_<u;_++)f[_]=c[a+_]}return ir(d)},Et=(i,e)=>{i&&i.skip(e)},Ke=(i,e)=>(e||(e=5),i/100*e||5);let bi=class{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,n){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),n?.once){const o=()=>{this.removeEventListener(e,o),this.removeEventListener(e,t)};return this.addEventListener(e,o),o}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var n;(n=this.listeners[e])===null||n===void 0||n.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach(n=>n(...t))}},or=class extends bi{constructor(e){super(),this.subscriptions=[],this.options=e}onInit(){}init(e){this.wavesurfer=e,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach(e=>e())}};function kt(i,e,t,n,o=5){let r=()=>{};if(!i)return r;const s=u=>{if(u.button===2)return;u.preventDefault(),u.stopPropagation(),i.style.touchAction="none";let a=u.clientX,d=u.clientY,l=!1;const c=m=>{m.preventDefault(),m.stopPropagation();const h=m.clientX,g=m.clientY;if(l||Math.abs(h-a)>=o||Math.abs(g-d)>=o){const{left:k,top:E}=i.getBoundingClientRect();l||(l=!0,t?.(a-k,d-E)),e(h-a,g-d,h-k,g-E),a=h,d=g}},f=m=>{l&&(m.preventDefault(),m.stopPropagation())},_=()=>{i.style.touchAction="",l&&n?.(),r()};document.addEventListener("pointermove",c),document.addEventListener("pointerup",_),document.addEventListener("pointerleave",_),document.addEventListener("click",f,!0),r=()=>{document.removeEventListener("pointermove",c),document.removeEventListener("pointerup",_),document.removeEventListener("pointerleave",_),setTimeout(()=>{document.removeEventListener("click",f,!0)},10)}};return i.addEventListener("pointerdown",s),()=>{r(),i.removeEventListener("pointerdown",s)}}class Ln extends bi{constructor(e,t,n=0){var o,r,s,u,a,d,l;super(),this.totalDuration=t,this.numberOfChannels=n,this.minLength=0,this.maxLength=1/0,this.id=e.id||`region-${Math.random().toString(32).slice(2)}`,this.start=this.clampPosition(e.start),this.end=this.clampPosition((o=e.end)!==null&&o!==void 0?o:e.start),this.drag=(r=e.drag)===null||r===void 0||r,this.resize=(s=e.resize)===null||s===void 0||s,this.color=(u=e.color)!==null&&u!==void 0?u:"rgba(0, 0, 0, 0.1)",this.minLength=(a=e.minLength)!==null&&a!==void 0?a:this.minLength,this.maxLength=(d=e.maxLength)!==null&&d!==void 0?d:this.maxLength,this.channelIdx=(l=e.channelIdx)!==null&&l!==void 0?l:-1,this.element=this.initElement(),this.setContent(e.content),this.setPart(),this.renderPosition(),this.initMouseEvents()}clampPosition(e){return Math.max(0,Math.min(this.totalDuration,e))}setPart(){const e=this.start===this.end;this.element.setAttribute("part",`${e?"marker":"region"} ${this.id}`)}addResizeHandles(e){const t=document.createElement("div");t.setAttribute("data-resize","left"),t.setAttribute("style",`
        position: absolute;
        z-index: 2;
        width: 6px;
        height: 100%;
        top: 0;
        left: 0;
        border-left: 2px solid rgba(0, 0, 0, 0.5);
        border-radius: 2px 0 0 2px;
        cursor: ew-resize;
        word-break: keep-all;
      `),t.setAttribute("part","region-handle region-handle-left");const n=t.cloneNode();n.setAttribute("data-resize","right"),n.style.left="",n.style.right="0",n.style.borderRight=n.style.borderLeft,n.style.borderLeft="",n.style.borderRadius="0 2px 2px 0",n.setAttribute("part","region-handle region-handle-right"),e.appendChild(t),e.appendChild(n),kt(t,o=>this.onResize(o,"start"),()=>null,()=>this.onEndResizing(),1),kt(n,o=>this.onResize(o,"end"),()=>null,()=>this.onEndResizing(),1)}removeResizeHandles(e){const t=e.querySelector('[data-resize="left"]'),n=e.querySelector('[data-resize="right"]');t&&e.removeChild(t),n&&e.removeChild(n)}initElement(){const e=document.createElement("div"),t=this.start===this.end;let n=0,o=100;return this.channelIdx>=0&&this.channelIdx<this.numberOfChannels&&(o=100/this.numberOfChannels,n=o*this.channelIdx),e.setAttribute("style",`
      position: absolute;
      top: ${n}%;
      height: ${o}%;
      background-color: ${t?"none":this.color};
      border-left: ${t?"2px solid "+this.color:"none"};
      border-radius: 2px;
      box-sizing: border-box;
      transition: background-color 0.2s ease;
      cursor: ${this.drag?"grab":"default"};
      pointer-events: all;
    `),!t&&this.resize&&this.addResizeHandles(e),e}renderPosition(){const e=this.start/this.totalDuration,t=(this.totalDuration-this.end)/this.totalDuration;this.element.style.left=100*e+"%",this.element.style.right=100*t+"%"}initMouseEvents(){const{element:e}=this;e&&(e.addEventListener("click",t=>this.emit("click",t)),e.addEventListener("mouseenter",t=>this.emit("over",t)),e.addEventListener("mouseleave",t=>this.emit("leave",t)),e.addEventListener("dblclick",t=>this.emit("dblclick",t)),kt(e,t=>this.onMove(t),()=>this.onStartMoving(),()=>this.onEndMoving()))}onStartMoving(){this.drag&&(this.element.style.cursor="grabbing")}onEndMoving(){this.drag&&(this.element.style.cursor="grab",this.emit("update-end"))}_onUpdate(e,t){if(!this.element.parentElement)return;const n=e/this.element.parentElement.clientWidth*this.totalDuration,o=t&&t!=="start"?this.start:this.start+n,r=t&&t!=="end"?this.end:this.end+n,s=r-o;o>=0&&r<=this.totalDuration&&o<=r&&s>=this.minLength&&s<=this.maxLength&&(this.start=o,this.end=r,this.renderPosition(),this.emit("update"))}onMove(e){this.drag&&this._onUpdate(e)}onResize(e,t){this.resize&&this._onUpdate(e,t)}onEndResizing(){this.resize&&this.emit("update-end")}_setTotalDuration(e){this.totalDuration=e,this.renderPosition()}play(){this.emit("play")}setContent(e){var t;if((t=this.content)===null||t===void 0||t.remove(),e){if(typeof e=="string"){this.content=document.createElement("div");const n=this.start===this.end;this.content.style.padding=`0.2em ${n?.2:.4}em`,this.content.textContent=e}else this.content=e;this.content.setAttribute("part","region-content"),this.element.appendChild(this.content)}else this.content=void 0}setOptions(e){var t,n;if(e.color&&(this.color=e.color,this.element.style.backgroundColor=this.color),e.drag!==void 0&&(this.drag=e.drag,this.element.style.cursor=this.drag?"grab":"default"),e.start!==void 0||e.end!==void 0){const o=this.start===this.end;this.start=this.clampPosition((t=e.start)!==null&&t!==void 0?t:this.start),this.end=this.clampPosition((n=e.end)!==null&&n!==void 0?n:o?this.start:this.end),this.renderPosition(),this.setPart()}if(e.content&&this.setContent(e.content),e.id&&(this.id=e.id,this.setPart()),e.resize!==void 0&&e.resize!==this.resize){const o=this.start===this.end;this.resize=e.resize,this.resize&&!o?this.addResizeHandles(this.element):this.removeResizeHandles(this.element)}}remove(){this.emit("remove"),this.element.remove(),this.element=null}}let rr=class wi extends or{constructor(e){super(e),this.regions=[],this.regionsContainer=this.initRegionsContainer()}static create(e){return new wi(e)}onInit(){if(!this.wavesurfer)throw Error("WaveSurfer is not initialized");this.wavesurfer.getWrapper().appendChild(this.regionsContainer);let e=[];this.subscriptions.push(this.wavesurfer.on("timeupdate",t=>{const n=this.regions.filter(o=>o.start<=t&&o.end>=t);n.forEach(o=>{e.includes(o)||this.emit("region-in",o)}),e.forEach(o=>{n.includes(o)||this.emit("region-out",o)}),e=n}))}initRegionsContainer(){const e=document.createElement("div");return e.setAttribute("style",`
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 3;
      pointer-events: none;
    `),e}getRegions(){return this.regions}avoidOverlapping(e){if(!e.content)return;const t=e.content,n=t.getBoundingClientRect().left,o=e.element.scrollWidth,r=this.regions.filter(s=>{if(s===e||!s.content)return!1;const u=s.content.getBoundingClientRect().left,a=s.element.scrollWidth;return n<u+a&&u<n+o}).map(s=>{var u;return((u=s.content)===null||u===void 0?void 0:u.getBoundingClientRect().height)||0}).reduce((s,u)=>s+u,0);t.style.marginTop=`${r}px`}saveRegion(e){this.regionsContainer.appendChild(e.element),this.avoidOverlapping(e),this.regions.push(e);const t=[e.on("update-end",()=>{this.avoidOverlapping(e),this.emit("region-updated",e)}),e.on("play",()=>{var n,o;(n=this.wavesurfer)===null||n===void 0||n.play(),(o=this.wavesurfer)===null||o===void 0||o.setTime(e.start)}),e.on("click",n=>{this.emit("region-clicked",e,n)}),e.on("dblclick",n=>{this.emit("region-double-clicked",e,n)}),e.once("remove",()=>{t.forEach(n=>n()),this.regions=this.regions.filter(n=>n!==e)})];this.subscriptions.push(...t),this.emit("region-created",e)}addRegion(e){var t,n;if(!this.wavesurfer)throw Error("WaveSurfer is not initialized");const o=this.wavesurfer.getDuration(),r=(n=(t=this.wavesurfer)===null||t===void 0?void 0:t.getDecodedData())===null||n===void 0?void 0:n.numberOfChannels,s=new Ln(e,o,r);return o?this.saveRegion(s):this.subscriptions.push(this.wavesurfer.once("ready",u=>{s._setTotalDuration(u),this.saveRegion(s)})),s}enableDragSelection(e){var t,n;const o=(n=(t=this.wavesurfer)===null||t===void 0?void 0:t.getWrapper())===null||n===void 0?void 0:n.querySelector("div");if(!o)return()=>{};let r=null,s=0;return kt(o,(u,a,d)=>{r&&r._onUpdate(u,d>s?"end":"start")},u=>{var a,d;if(s=u,!this.wavesurfer)return;const l=this.wavesurfer.getDuration(),c=(d=(a=this.wavesurfer)===null||a===void 0?void 0:a.getDecodedData())===null||d===void 0?void 0:d.numberOfChannels,f=this.wavesurfer.getWrapper().clientWidth,_=u/f*l,m=(u+5)/f*l;r=new Ln(Object.assign(Object.assign({},e),{start:_,end:m}),l,c),this.regionsContainer.appendChild(r.element)},()=>{r&&(this.saveRegion(r),r=null)})}clearRegions(){this.regions.forEach(e=>e.remove())}destroy(){this.clearRegions(),super.destroy()}};const{SvelteComponent:sr,check_outros:lr,create_component:fn,destroy_component:hn,detach:ar,empty:ur,group_outros:cr,init:dr,insert:fr,mount_component:_n,safe_not_equal:hr,transition_in:ht,transition_out:_t}=window.__gradio__svelte__internal;function _r(i){let e,t;return e=new Bo({}),{c(){fn(e.$$.fragment)},m(n,o){_n(e,n,o),t=!0},i(n){t||(ht(e.$$.fragment,n),t=!0)},o(n){_t(e.$$.fragment,n),t=!1},d(n){hn(e,n)}}}function mr(i){let e,t;return e=new Mo({}),{c(){fn(e.$$.fragment)},m(n,o){_n(e,n,o),t=!0},i(n){t||(ht(e.$$.fragment,n),t=!0)},o(n){_t(e.$$.fragment,n),t=!1},d(n){hn(e,n)}}}function gr(i){let e,t;return e=new Xo({}),{c(){fn(e.$$.fragment)},m(n,o){_n(e,n,o),t=!0},i(n){t||(ht(e.$$.fragment,n),t=!0)},o(n){_t(e.$$.fragment,n),t=!1},d(n){hn(e,n)}}}function pr(i){let e,t,n,o;const r=[gr,mr,_r],s=[];function u(a,d){return a[0]==0?0:a[0]<.5?1:a[0]>=.5?2:-1}return~(e=u(i))&&(t=s[e]=r[e](i)),{c(){t&&t.c(),n=ur()},m(a,d){~e&&s[e].m(a,d),fr(a,n,d),o=!0},p(a,[d]){let l=e;e=u(a),e!==l&&(t&&(cr(),_t(s[l],1,1,()=>{s[l]=null}),lr()),~e?(t=s[e],t||(t=s[e]=r[e](a),t.c()),ht(t,1),t.m(n.parentNode,n)):t=null)},i(a){o||(ht(t),o=!0)},o(a){_t(t),o=!1},d(a){a&&ar(n),~e&&s[e].d(a)}}}function vr(i,e,t){let{currentVolume:n}=e;return i.$$set=o=>{"currentVolume"in o&&t(0,n=o.currentVolume)},[n]}class br extends sr{constructor(e){super(),dr(this,e,vr,pr,hr,{currentVolume:0})}}const{SvelteComponent:wr,attr:Ye,binding_callbacks:kr,detach:yr,element:Cr,init:Er,insert:Rr,listen:Mn,noop:Pn,run_all:Sr,safe_not_equal:Dr}=window.__gradio__svelte__internal,{onMount:Lr}=window.__gradio__svelte__internal;function Mr(i){let e,t,n;return{c(){e=Cr("input"),Ye(e,"id","volume"),Ye(e,"class","volume-slider svelte-wuo8j5"),Ye(e,"type","range"),Ye(e,"min","0"),Ye(e,"max","1"),Ye(e,"step","0.01"),e.value=i[0]},m(o,r){Rr(o,e,r),i[4](e),t||(n=[Mn(e,"focusout",i[5]),Mn(e,"input",i[6])],t=!0)},p(o,[r]){r&1&&(e.value=o[0])},i:Pn,o:Pn,d(o){o&&yr(e),i[4](null),t=!1,Sr(n)}}}function Pr(i,e,t){let{currentVolume:n=1}=e,{show_volume_slider:o=!1}=e,{waveform:r}=e,s;Lr(()=>{u()});const u=()=>{let c=s;c&&(c.style.background=`linear-gradient(to right, var(--color-accent) ${n*100}%, var(--neutral-400) ${n*100}%)`)};function a(c){kr[c?"unshift":"push"](()=>{s=c,t(3,s)})}const d=()=>t(1,o=!1),l=c=>{c.target instanceof HTMLInputElement&&(t(0,n=parseFloat(c.target.value)),r.setVolume(n))};return i.$$set=c=>{"currentVolume"in c&&t(0,n=c.currentVolume),"show_volume_slider"in c&&t(1,o=c.show_volume_slider),"waveform"in c&&t(2,r=c.waveform)},i.$$.update=()=>{i.$$.dirty&1&&u()},[n,o,r,s,a,d,l]}class Ar extends wr{constructor(e){super(),Er(this,e,Pr,Mr,Dr,{currentVolume:0,show_volume_slider:1,waveform:2})}}const{SvelteComponent:Tr,add_flush_callback:An,append:Z,attr:N,bind:Tn,binding_callbacks:zn,check_outros:ct,create_component:Se,destroy_component:De,detach:Le,element:ie,empty:zr,group_outros:dt,init:Wr,insert:Me,listen:_e,mount_component:Pe,noop:ft,run_all:ki,safe_not_equal:qr,set_data:Or,set_style:Wn,space:Ce,text:qn,toggle_class:On,transition_in:V,transition_out:X}=window.__gradio__svelte__internal;function Bn(i){let e,t,n,o;function r(a){i[27](a)}function s(a){i[28](a)}let u={waveform:i[2]};return i[12]!==void 0&&(u.currentVolume=i[12]),i[1]!==void 0&&(u.show_volume_slider=i[1]),e=new Ar({props:u}),zn.push(()=>Tn(e,"currentVolume",r)),zn.push(()=>Tn(e,"show_volume_slider",s)),{c(){Se(e.$$.fragment)},m(a,d){Pe(e,a,d),o=!0},p(a,d){const l={};d[0]&4&&(l.waveform=a[2]),!t&&d[0]&4096&&(t=!0,l.currentVolume=a[12],An(()=>t=!1)),!n&&d[0]&2&&(n=!0,l.show_volume_slider=a[1],An(()=>n=!1)),e.$set(l)},i(a){o||(V(e.$$.fragment,a),o=!0)},o(a){X(e.$$.fragment,a),o=!1},d(a){De(e,a)}}}function Br(i){let e,t;return e=new Qi({}),{c(){Se(e.$$.fragment)},m(n,o){Pe(e,n,o),t=!0},i(n){t||(V(e.$$.fragment,n),t=!0)},o(n){X(e.$$.fragment,n),t=!1},d(n){De(e,n)}}}function Hr(i){let e,t;return e=new pi({}),{c(){Se(e.$$.fragment)},m(n,o){Pe(e,n,o),t=!0},i(n){t||(V(e.$$.fragment,n),t=!0)},o(n){X(e.$$.fragment,n),t=!1},d(n){De(e,n)}}}function Hn(i){let e,t,n,o,r,s=i[6]&&i[0]===""&&Nn(i);const u=[Ir,Nr],a=[];function d(l,c){return l[0]===""?0:1}return t=d(i),n=a[t]=u[t](i),{c(){s&&s.c(),e=Ce(),n.c(),o=zr()},m(l,c){s&&s.m(l,c),Me(l,e,c),a[t].m(l,c),Me(l,o,c),r=!0},p(l,c){l[6]&&l[0]===""?s?(s.p(l,c),c[0]&65&&V(s,1)):(s=Nn(l),s.c(),V(s,1),s.m(e.parentNode,e)):s&&(dt(),X(s,1,1,()=>{s=null}),ct());let f=t;t=d(l),t===f?a[t].p(l,c):(dt(),X(a[f],1,1,()=>{a[f]=null}),ct(),n=a[t],n?n.p(l,c):(n=a[t]=u[t](l),n.c()),V(n,1),n.m(o.parentNode,o))},i(l){r||(V(s),V(n),r=!0)},o(l){X(s),X(n),r=!1},d(l){l&&(Le(e),Le(o)),s&&s.d(l),a[t].d(l)}}}function Nn(i){let e,t,n,o,r;return t=new xi({}),{c(){e=ie("button"),Se(t.$$.fragment),N(e,"class","action icon svelte-16ezq5z"),N(e,"aria-label","Reset audio")},m(s,u){Me(s,e,u),Pe(t,e,null),n=!0,o||(r=_e(e,"click",i[33]),o=!0)},p:ft,i(s){n||(V(t.$$.fragment,s),n=!0)},o(s){X(t.$$.fragment,s),n=!1},d(s){s&&Le(e),De(t),o=!1,r()}}}function Nr(i){let e,t,n,o,r;return{c(){e=ie("button"),e.textContent="Trim",t=Ce(),n=ie("button"),n.textContent="Cancel",N(e,"class","text-button svelte-16ezq5z"),N(n,"class","text-button svelte-16ezq5z")},m(s,u){Me(s,e,u),Me(s,t,u),Me(s,n,u),o||(r=[_e(e,"click",i[14]),_e(n,"click",i[16])],o=!0)},p:ft,i:ft,o:ft,d(s){s&&(Le(e),Le(t),Le(n)),o=!1,ki(r)}}}function Ir(i){let e,t,n,o,r;return t=new $i({}),{c(){e=ie("button"),Se(t.$$.fragment),N(e,"class","action icon svelte-16ezq5z"),N(e,"aria-label","Trim audio to selection")},m(s,u){Me(s,e,u),Pe(t,e,null),n=!0,o||(r=_e(e,"click",i[16]),o=!0)},p:ft,i(s){n||(V(t.$$.fragment,s),n=!0)},o(s){X(t.$$.fragment,s),n=!1},d(s){s&&Le(e),De(t),o=!1,r()}}}function Vr(i){let e,t,n,o,r,s,u,a,d,l,c,f,_,m,h,g,k,E,p,L,R,A,C,T,S,z,H,O,W,G;o=new br({props:{currentVolume:i[12]}});let q=i[1]&&Bn(i);h=new ho({});const M=[Hr,Br],v=[];function U(P,b){return P[5]?0:1}p=U(i),L=v[p]=M[p](i),T=new ko({});let B=i[10]&&i[7]&&Hn(i);return{c(){e=ie("div"),t=ie("div"),n=ie("button"),Se(o.$$.fragment),r=Ce(),q&&q.c(),s=Ce(),u=ie("button"),a=ie("span"),d=qn(i[11]),l=qn("x"),f=Ce(),_=ie("div"),m=ie("button"),Se(h.$$.fragment),k=Ce(),E=ie("button"),L.c(),A=Ce(),C=ie("button"),Se(T.$$.fragment),z=Ce(),H=ie("div"),B&&B.c(),N(n,"class","action icon volume svelte-16ezq5z"),N(n,"aria-label","Adjust volume"),Wn(n,"color",i[1]?"var(--color-accent)":"var(--neutral-400)"),N(u,"class","playback icon svelte-16ezq5z"),N(u,"aria-label",c=`Adjust playback speed to ${i[13][(i[13].indexOf(i[11])+1)%i[13].length]}x`),On(u,"hidden",i[1]),N(t,"class","control-wrapper svelte-16ezq5z"),N(m,"class","rewind icon svelte-16ezq5z"),N(m,"aria-label",g=`Skip backwards by ${Ke(i[3],i[9].skip_length)} seconds`),N(E,"class","play-pause-button icon svelte-16ezq5z"),N(E,"aria-label",R=i[5]?i[4]("audio.pause"):i[4]("audio.play")),N(C,"class","skip icon svelte-16ezq5z"),N(C,"aria-label",S="Skip forward by "+Ke(i[3],i[9].skip_length)+" seconds"),N(_,"class","play-pause-wrapper svelte-16ezq5z"),N(H,"class","settings-wrapper svelte-16ezq5z"),N(e,"class","controls svelte-16ezq5z"),N(e,"data-testid","waveform-controls")},m(P,b){Me(P,e,b),Z(e,t),Z(t,n),Pe(o,n,null),Z(t,r),q&&q.m(t,null),Z(t,s),Z(t,u),Z(u,a),Z(a,d),Z(a,l),Z(e,f),Z(e,_),Z(_,m),Pe(h,m,null),Z(_,k),Z(_,E),v[p].m(E,null),Z(_,A),Z(_,C),Pe(T,C,null),Z(e,z),Z(e,H),B&&B.m(H,null),O=!0,W||(G=[_e(n,"click",i[26]),_e(u,"click",i[29]),_e(m,"click",i[30]),_e(E,"click",i[31]),_e(C,"click",i[32])],W=!0)},p(P,b){const Y={};b[0]&4096&&(Y.currentVolume=P[12]),o.$set(Y),b[0]&2&&Wn(n,"color",P[1]?"var(--color-accent)":"var(--neutral-400)"),P[1]?q?(q.p(P,b),b[0]&2&&V(q,1)):(q=Bn(P),q.c(),V(q,1),q.m(t,s)):q&&(dt(),X(q,1,1,()=>{q=null}),ct()),(!O||b[0]&2048)&&Or(d,P[11]),(!O||b[0]&2048&&c!==(c=`Adjust playback speed to ${P[13][(P[13].indexOf(P[11])+1)%P[13].length]}x`))&&N(u,"aria-label",c),(!O||b[0]&2)&&On(u,"hidden",P[1]),(!O||b[0]&520&&g!==(g=`Skip backwards by ${Ke(P[3],P[9].skip_length)} seconds`))&&N(m,"aria-label",g);let y=p;p=U(P),p!==y&&(dt(),X(v[y],1,1,()=>{v[y]=null}),ct(),L=v[p],L||(L=v[p]=M[p](P),L.c()),V(L,1),L.m(E,null)),(!O||b[0]&48&&R!==(R=P[5]?P[4]("audio.pause"):P[4]("audio.play")))&&N(E,"aria-label",R),(!O||b[0]&520&&S!==(S="Skip forward by "+Ke(P[3],P[9].skip_length)+" seconds"))&&N(C,"aria-label",S),P[10]&&P[7]?B?(B.p(P,b),b[0]&1152&&V(B,1)):(B=Hn(P),B.c(),V(B,1),B.m(H,null)):B&&(dt(),X(B,1,1,()=>{B=null}),ct())},i(P){O||(V(o.$$.fragment,P),V(q),V(h.$$.fragment,P),V(L),V(T.$$.fragment,P),V(B),O=!0)},o(P){X(o.$$.fragment,P),X(q),X(h.$$.fragment,P),X(L),X(T.$$.fragment,P),X(B),O=!1},d(P){P&&Le(e),De(o),q&&q.d(),De(h),v[p].d(),De(T),B&&B.d(),W=!1,ki(G)}}}function Ur(i,e,t){let{waveform:n}=e,{audio_duration:o}=e,{i18n:r}=e,{playing:s}=e,{show_redo:u=!1}=e,{interactive:a=!1}=e,{handle_trim_audio:d}=e,{mode:l=""}=e,{container:c}=e,{handle_reset_value:f}=e,{waveform_options:_={}}=e,{trim_region_settings:m={}}=e,{show_volume_slider:h=!1}=e,{editable:g=!0}=e,{trimDuration:k=0}=e,E=[.5,1,1.5,2],p=E[1],L,R=null,A,C,T="",S=1;const z=()=>{t(22,R=L.addRegion({start:o/4,end:o/2,...m})),t(17,k=R.end-R.start)},H=()=>{if(n&&L&&R){const y=R.start,K=R.end;d(y,K),t(0,l=""),t(22,R=null)}},O=()=>{L?.getRegions().forEach(y=>{y.remove()}),L?.clearRegions()},W=()=>{O(),l==="edit"?t(0,l=""):(t(0,l="edit"),z())},G=(y,K)=>{let re,fe;R&&(y==="left"?K==="ArrowLeft"?(re=R.start-.05,fe=R.end):(re=R.start+.05,fe=R.end):K==="ArrowLeft"?(re=R.start,fe=R.end-.05):(re=R.start,fe=R.end+.05),R.setOptions({start:re,end:fe}),t(17,k=R.end-R.start))},q=()=>t(1,h=!h);function M(y){S=y,t(12,S)}function v(y){h=y,t(1,h)}const U=()=>{t(11,p=E[(E.indexOf(p)+1)%E.length]),n.setPlaybackRate(p)},B=()=>n.skip(Ke(o,_.skip_length)*-1),P=()=>n.playPause(),b=()=>n.skip(Ke(o,_.skip_length)),Y=()=>{f(),O(),t(0,l="")};return i.$$set=y=>{"waveform"in y&&t(2,n=y.waveform),"audio_duration"in y&&t(3,o=y.audio_duration),"i18n"in y&&t(4,r=y.i18n),"playing"in y&&t(5,s=y.playing),"show_redo"in y&&t(6,u=y.show_redo),"interactive"in y&&t(7,a=y.interactive),"handle_trim_audio"in y&&t(18,d=y.handle_trim_audio),"mode"in y&&t(0,l=y.mode),"container"in y&&t(19,c=y.container),"handle_reset_value"in y&&t(8,f=y.handle_reset_value),"waveform_options"in y&&t(9,_=y.waveform_options),"trim_region_settings"in y&&t(20,m=y.trim_region_settings),"show_volume_slider"in y&&t(1,h=y.show_volume_slider),"editable"in y&&t(10,g=y.editable),"trimDuration"in y&&t(17,k=y.trimDuration)},i.$$.update=()=>{if(i.$$.dirty[0]&4&&t(21,L=n.registerPlugin(rr.create())),i.$$.dirty[0]&2097152&&L?.on("region-out",y=>{y.play()}),i.$$.dirty[0]&2097152&&L?.on("region-updated",y=>{t(17,k=y.end-y.start)}),i.$$.dirty[0]&2097152&&L?.on("region-clicked",(y,K)=>{K.stopPropagation(),t(22,R=y),y.play()}),i.$$.dirty[0]&31981568&&R){const y=c.children[0].shadowRoot;t(24,C=y.querySelector('[data-resize="right"]')),t(23,A=y.querySelector('[data-resize="left"]')),A&&C&&(A.setAttribute("role","button"),C.setAttribute("role","button"),A?.setAttribute("aria-label","Drag to adjust start time"),C?.setAttribute("aria-label","Drag to adjust end time"),A?.setAttribute("tabindex","0"),C?.setAttribute("tabindex","0"),A.addEventListener("focus",()=>{L&&t(25,T="left")}),C.addEventListener("focus",()=>{L&&t(25,T="right")}))}i.$$.dirty[0]&35651584&&L&&window.addEventListener("keydown",y=>{y.key==="ArrowLeft"?G(T,"ArrowLeft"):y.key==="ArrowRight"&&G(T,"ArrowRight")})},[l,h,n,o,r,s,u,a,f,_,g,p,S,E,H,O,W,k,d,c,m,L,R,A,C,T,q,M,v,U,B,P,b,Y]}class yi extends Tr{constructor(e){super(),Wr(this,e,Ur,Vr,qr,{waveform:2,audio_duration:3,i18n:4,playing:5,show_redo:6,interactive:7,handle_trim_audio:18,mode:0,container:19,handle_reset_value:8,waveform_options:9,trim_region_settings:20,show_volume_slider:1,editable:10,trimDuration:17},null,[-1,-1])}}const{SvelteComponent:jr,add_flush_callback:on,append:ce,attr:$,bind:rn,binding_callbacks:xe,check_outros:Ci,create_component:mn,destroy_component:gn,detach:Tt,element:he,empty:Fr,group_outros:Ei,init:Xr,insert:zt,mount_component:pn,noop:In,safe_not_equal:Gr,set_data:Yr,space:wt,src_url_equal:Vn,text:Zr,transition_in:Ae,transition_out:He}=window.__gradio__svelte__internal,{onMount:Jr}=window.__gradio__svelte__internal,{createEventDispatcher:Kr}=window.__gradio__svelte__internal;function Qr(i){let e,t,n,o,r,s,u,a,d,l,c,f,_,m=i[0]==="edit"&&i[16]>0&&Un(i),h=i[11]&&jn(i);return{c(){e=he("div"),t=he("div"),n=he("div"),o=wt(),r=he("div"),s=he("time"),s.textContent="0:00",u=wt(),a=he("div"),m&&m.c(),d=wt(),l=he("time"),l.textContent="0:00",c=wt(),h&&h.c(),$(n,"id","waveform"),$(n,"class","svelte-1ark3ru"),$(t,"class","waveform-container svelte-1ark3ru"),$(s,"id","time"),$(s,"class","svelte-1ark3ru"),$(l,"id","duration"),$(l,"class","svelte-1ark3ru"),$(r,"class","timestamps svelte-1ark3ru"),$(e,"class","component-wrapper svelte-1ark3ru"),$(e,"data-testid",f=i[2]?"waveform-"+i[2]:"unlabelled-audio")},m(g,k){zt(g,e,k),ce(e,t),ce(t,n),i[21](n),ce(e,o),ce(e,r),ce(r,s),i[22](s),ce(r,u),ce(r,a),m&&m.m(a,null),ce(a,d),ce(a,l),i[23](l),ce(e,c),h&&h.m(e,null),_=!0},p(g,k){g[0]==="edit"&&g[16]>0?m?m.p(g,k):(m=Un(g),m.c(),m.m(a,d)):m&&(m.d(1),m=null),g[11]?h?(h.p(g,k),k&2048&&Ae(h,1)):(h=jn(g),h.c(),Ae(h,1),h.m(e,null)):h&&(Ei(),He(h,1,1,()=>{h=null}),Ci()),(!_||k&4&&f!==(f=g[2]?"waveform-"+g[2]:"unlabelled-audio"))&&$(e,"data-testid",f)},i(g){_||(Ae(h),_=!0)},o(g){He(h),_=!1},d(g){g&&Tt(e),i[21](null),i[22](null),m&&m.d(),i[23](null),h&&h.d()}}}function $r(i){let e,t,n;return{c(){e=he("audio"),$(e,"class","standard-player svelte-1ark3ru"),Vn(e.src,t=i[1].url)||$(e,"src",t),e.controls=!0,e.autoplay=n=i[7].autoplay},m(o,r){zt(o,e,r)},p(o,r){r&2&&!Vn(e.src,t=o[1].url)&&$(e,"src",t),r&128&&n!==(n=o[7].autoplay)&&(e.autoplay=n)},i:In,o:In,d(o){o&&Tt(e)}}}function xr(i){let e,t;return e=new mi({props:{size:"small",$$slots:{default:[es]},$$scope:{ctx:i}}}),{c(){mn(e.$$.fragment)},m(n,o){pn(e,n,o),t=!0},p(n,o){const r={};o&1073741824&&(r.$$scope={dirty:o,ctx:n}),e.$set(r)},i(n){t||(Ae(e.$$.fragment,n),t=!0)},o(n){He(e.$$.fragment,n),t=!1},d(n){gn(e,n)}}}function Un(i){let e,t=ue(i[16])+"",n;return{c(){e=he("time"),n=Zr(t),$(e,"id","trim-duration"),$(e,"class","svelte-1ark3ru")},m(o,r){zt(o,e,r),ce(e,n)},p(o,r){r&65536&&t!==(t=ue(o[16])+"")&&Yr(n,t)},d(o){o&&Tt(e)}}}function jn(i){let e,t,n,o,r;function s(l){i[24](l)}function u(l){i[25](l)}function a(l){i[26](l)}let d={container:i[10],waveform:i[11],playing:i[14],audio_duration:i[15],i18n:i[3],interactive:i[4],handle_trim_audio:i[18],show_redo:i[4],handle_reset_value:i[9],waveform_options:i[8],trim_region_settings:i[6],editable:i[5]};return i[0]!==void 0&&(d.mode=i[0]),i[16]!==void 0&&(d.trimDuration=i[16]),i[17]!==void 0&&(d.show_volume_slider=i[17]),e=new yi({props:d}),xe.push(()=>rn(e,"mode",s)),xe.push(()=>rn(e,"trimDuration",u)),xe.push(()=>rn(e,"show_volume_slider",a)),{c(){mn(e.$$.fragment)},m(l,c){pn(e,l,c),r=!0},p(l,c){const f={};c&1024&&(f.container=l[10]),c&2048&&(f.waveform=l[11]),c&16384&&(f.playing=l[14]),c&32768&&(f.audio_duration=l[15]),c&8&&(f.i18n=l[3]),c&16&&(f.interactive=l[4]),c&16&&(f.show_redo=l[4]),c&512&&(f.handle_reset_value=l[9]),c&256&&(f.waveform_options=l[8]),c&64&&(f.trim_region_settings=l[6]),c&32&&(f.editable=l[5]),!t&&c&1&&(t=!0,f.mode=l[0],on(()=>t=!1)),!n&&c&65536&&(n=!0,f.trimDuration=l[16],on(()=>n=!1)),!o&&c&131072&&(o=!0,f.show_volume_slider=l[17],on(()=>o=!1)),e.$set(f)},i(l){r||(Ae(e.$$.fragment,l),r=!0)},o(l){He(e.$$.fragment,l),r=!1},d(l){gn(e,l)}}}function es(i){let e,t;return e=new Mt({}),{c(){mn(e.$$.fragment)},m(n,o){pn(e,n,o),t=!0},i(n){t||(Ae(e.$$.fragment,n),t=!0)},o(n){He(e.$$.fragment,n),t=!1},d(n){gn(e,n)}}}function ts(i){let e,t,n,o;const r=[xr,$r,Qr],s=[];function u(a,d){return a[1]===null?0:a[1].is_stream?1:2}return e=u(i),t=s[e]=r[e](i),{c(){t.c(),n=Fr()},m(a,d){s[e].m(a,d),zt(a,n,d),o=!0},p(a,[d]){let l=e;e=u(a),e===l?s[e].p(a,d):(Ei(),He(s[l],1,1,()=>{s[l]=null}),Ci(),t=s[e],t?t.p(a,d):(t=s[e]=r[e](a),t.c()),Ae(t,1),t.m(n.parentNode,n))},i(a){o||(Ae(t),o=!0)},o(a){He(t),o=!1},d(a){a&&Tt(n),s[e].d(a)}}}function ns(i,e,t){let n,{value:o=null}=e,{label:r}=e,{i18n:s}=e,{dispatch_blob:u=()=>Promise.resolve()}=e,{interactive:a=!1}=e,{editable:d=!0}=e,{trim_region_settings:l={}}=e,{waveform_settings:c}=e,{waveform_options:f}=e,{mode:_=""}=e,{handle_reset_value:m=()=>{}}=e,h,g,k=!1,E,p,L,R=0,A=!1;const C=Kr(),T=()=>{t(11,g=et.create({container:h,...c})),Cn(o?.url).then(v=>{if(v&&g)return g.load(v)})},S=async(v,U)=>{t(0,_="");const B=g?.getDecodedData();B&&await ln(B,v,U,c.sampleRate).then(async P=>{await u([P],"change"),g?.destroy(),t(10,h.innerHTML="",h)}),C("edit")};async function z(v){await Cn(v).then(U=>{if(!(!U||o?.is_stream))return g?.load(U)})}Jr(()=>{window.addEventListener("keydown",v=>{!g||A||(v.key==="ArrowRight"&&_!=="edit"?Et(g,.1):v.key==="ArrowLeft"&&_!=="edit"&&Et(g,-.1))})});function H(v){xe[v?"unshift":"push"](()=>{h=v,t(10,h),t(11,g)})}function O(v){xe[v?"unshift":"push"](()=>{E=v,t(12,E),t(11,g)})}function W(v){xe[v?"unshift":"push"](()=>{p=v,t(13,p),t(11,g)})}function G(v){_=v,t(0,_)}function q(v){R=v,t(16,R)}function M(v){A=v,t(17,A)}return i.$$set=v=>{"value"in v&&t(1,o=v.value),"label"in v&&t(2,r=v.label),"i18n"in v&&t(3,s=v.i18n),"dispatch_blob"in v&&t(19,u=v.dispatch_blob),"interactive"in v&&t(4,a=v.interactive),"editable"in v&&t(5,d=v.editable),"trim_region_settings"in v&&t(6,l=v.trim_region_settings),"waveform_settings"in v&&t(7,c=v.waveform_settings),"waveform_options"in v&&t(8,f=v.waveform_options),"mode"in v&&t(0,_=v.mode),"handle_reset_value"in v&&t(9,m=v.handle_reset_value)},i.$$.update=()=>{i.$$.dirty&2&&t(20,n=o?.url),i.$$.dirty&3072&&h!==void 0&&(g!==void 0&&g.destroy(),t(10,h.innerHTML="",h),T(),t(14,k=!1)),i.$$.dirty&10240&&g?.on("decode",v=>{t(15,L=v),p&&t(13,p.textContent=ue(v),p)}),i.$$.dirty&6144&&g?.on("timeupdate",v=>E&&t(12,E.textContent=ue(v),E)),i.$$.dirty&2176&&g?.on("ready",()=>{c.autoplay?g?.play():g?.stop()}),i.$$.dirty&2048&&g?.on("finish",()=>{t(14,k=!1),C("stop")}),i.$$.dirty&2048&&g?.on("pause",()=>{t(14,k=!1),C("pause")}),i.$$.dirty&2048&&g?.on("play",()=>{t(14,k=!0),C("play")}),i.$$.dirty&1048576&&n&&z(n)},[_,o,r,s,a,d,l,c,f,m,h,g,E,p,k,L,R,A,S,u,n,H,O,W,G,q,M]}class is extends jr{constructor(e){super(),Xr(this,e,ns,ts,Gr,{value:1,label:2,i18n:3,dispatch_blob:19,interactive:4,editable:5,trim_region_settings:6,waveform_settings:7,waveform_options:8,mode:0,handle_reset_value:9})}}const Ri=is;const{SvelteComponent:os,append:rs,attr:ss,bubble:st,check_outros:an,create_component:je,destroy_component:Fe,detach:Rt,element:ls,empty:as,group_outros:un,init:us,insert:St,mount_component:Xe,safe_not_equal:cs,space:cn,transition_in:x,transition_out:oe}=window.__gradio__svelte__internal,{createEventDispatcher:ds}=window.__gradio__svelte__internal;function fs(i){let e,t;return e=new mi({props:{size:"small",$$slots:{default:[_s]},$$scope:{ctx:i}}}),{c(){je(e.$$.fragment)},m(n,o){Xe(e,n,o),t=!0},p(n,o){const r={};o&65536&&(r.$$scope={dirty:o,ctx:n}),e.$set(r)},i(n){t||(x(e.$$.fragment,n),t=!0)},o(n){oe(e.$$.fragment,n),t=!1},d(n){Fe(e,n)}}}function hs(i){let e,t,n,o,r,s=i[3]&&Fn(i),u=i[4]&&Xn(i);return o=new Ri({props:{value:i[0],label:i[1],i18n:i[5],waveform_settings:i[6],waveform_options:i[7],editable:i[8]}}),o.$on("pause",i[12]),o.$on("play",i[13]),o.$on("stop",i[14]),{c(){e=ls("div"),s&&s.c(),t=cn(),u&&u.c(),n=cn(),je(o.$$.fragment),ss(e,"class","icon-buttons svelte-rvdo70")},m(a,d){St(a,e,d),s&&s.m(e,null),rs(e,t),u&&u.m(e,null),St(a,n,d),Xe(o,a,d),r=!0},p(a,d){a[3]?s?(s.p(a,d),d&8&&x(s,1)):(s=Fn(a),s.c(),x(s,1),s.m(e,t)):s&&(un(),oe(s,1,1,()=>{s=null}),an()),a[4]?u?(u.p(a,d),d&16&&x(u,1)):(u=Xn(a),u.c(),x(u,1),u.m(e,null)):u&&(un(),oe(u,1,1,()=>{u=null}),an());const l={};d&1&&(l.value=a[0]),d&2&&(l.label=a[1]),d&32&&(l.i18n=a[5]),d&64&&(l.waveform_settings=a[6]),d&128&&(l.waveform_options=a[7]),d&256&&(l.editable=a[8]),o.$set(l)},i(a){r||(x(s),x(u),x(o.$$.fragment,a),r=!0)},o(a){oe(s),oe(u),oe(o.$$.fragment,a),r=!1},d(a){a&&(Rt(e),Rt(n)),s&&s.d(),u&&u.d(),Fe(o,a)}}}function _s(i){let e,t;return e=new Mt({}),{c(){je(e.$$.fragment)},m(n,o){Xe(e,n,o),t=!0},i(n){t||(x(e.$$.fragment,n),t=!0)},o(n){oe(e.$$.fragment,n),t=!1},d(n){Fe(e,n)}}}function Fn(i){let e,t;return e=new Ji({props:{href:i[0].url,download:i[0].orig_name||i[0].path,$$slots:{default:[ms]},$$scope:{ctx:i}}}),{c(){je(e.$$.fragment)},m(n,o){Xe(e,n,o),t=!0},p(n,o){const r={};o&1&&(r.href=n[0].url),o&1&&(r.download=n[0].orig_name||n[0].path),o&65568&&(r.$$scope={dirty:o,ctx:n}),e.$set(r)},i(n){t||(x(e.$$.fragment,n),t=!0)},o(n){oe(e.$$.fragment,n),t=!1},d(n){Fe(e,n)}}}function ms(i){let e,t;return e=new Yi({props:{Icon:Ki,label:i[5]("common.download")}}),{c(){je(e.$$.fragment)},m(n,o){Xe(e,n,o),t=!0},p(n,o){const r={};o&32&&(r.label=n[5]("common.download")),e.$set(r)},i(n){t||(x(e.$$.fragment,n),t=!0)},o(n){oe(e.$$.fragment,n),t=!1},d(n){Fe(e,n)}}}function Xn(i){let e,t;return e=new Zi({props:{i18n:i[5],formatter:i[9],value:i[0]}}),e.$on("error",i[10]),e.$on("share",i[11]),{c(){je(e.$$.fragment)},m(n,o){Xe(e,n,o),t=!0},p(n,o){const r={};o&32&&(r.i18n=n[5]),o&1&&(r.value=n[0]),e.$set(r)},i(n){t||(x(e.$$.fragment,n),t=!0)},o(n){oe(e.$$.fragment,n),t=!1},d(n){Fe(e,n)}}}function gs(i){let e,t,n,o,r,s;e=new _i({props:{show_label:i[2],Icon:Mt,float:!1,label:i[1]||i[5]("audio.audio")}});const u=[hs,fs],a=[];function d(l,c){return l[0]!==null?0:1}return n=d(i),o=a[n]=u[n](i),{c(){je(e.$$.fragment),t=cn(),o.c(),r=as()},m(l,c){Xe(e,l,c),St(l,t,c),a[n].m(l,c),St(l,r,c),s=!0},p(l,[c]){const f={};c&4&&(f.show_label=l[2]),c&34&&(f.label=l[1]||l[5]("audio.audio")),e.$set(f);let _=n;n=d(l),n===_?a[n].p(l,c):(un(),oe(a[_],1,1,()=>{a[_]=null}),an(),o=a[n],o?o.p(l,c):(o=a[n]=u[n](l),o.c()),x(o,1),o.m(r.parentNode,r))},i(l){s||(x(e.$$.fragment,l),x(o),s=!0)},o(l){oe(e.$$.fragment,l),oe(o),s=!1},d(l){l&&(Rt(t),Rt(r)),Fe(e,l),a[n].d(l)}}}function ps(i,e,t){let{value:n=null}=e,{label:o}=e,{show_label:r=!0}=e,{show_download_button:s=!0}=e,{show_share_button:u=!1}=e,{i18n:a}=e,{waveform_settings:d}=e,{waveform_options:l}=e,{editable:c=!0}=e;const f=ds(),_=async p=>p?`<audio controls src="${await Gi(p.url,"url")}"></audio>`:"";function m(p){st.call(this,i,p)}function h(p){st.call(this,i,p)}function g(p){st.call(this,i,p)}function k(p){st.call(this,i,p)}function E(p){st.call(this,i,p)}return i.$$set=p=>{"value"in p&&t(0,n=p.value),"label"in p&&t(1,o=p.label),"show_label"in p&&t(2,r=p.show_label),"show_download_button"in p&&t(3,s=p.show_download_button),"show_share_button"in p&&t(4,u=p.show_share_button),"i18n"in p&&t(5,a=p.i18n),"waveform_settings"in p&&t(6,d=p.waveform_settings),"waveform_options"in p&&t(7,l=p.waveform_options),"editable"in p&&t(8,c=p.editable)},i.$$.update=()=>{i.$$.dirty&1&&n&&f("change",n)},[n,o,r,s,u,a,d,l,c,_,m,h,g,k,E]}class vs extends os{constructor(e){super(),us(this,e,ps,gs,cs,{value:0,label:1,show_label:2,show_download_button:3,show_share_button:4,i18n:5,waveform_settings:6,waveform_options:7,editable:8})}}const bs=vs;function sn(i,e,t,n){return new(t||(t=Promise))(function(o,r){function s(d){try{a(n.next(d))}catch(l){r(l)}}function u(d){try{a(n.throw(d))}catch(l){r(l)}}function a(d){var l;d.done?o(d.value):(l=d.value,l instanceof t?l:new t(function(c){c(l)})).then(s,u)}a((n=n.apply(i,e||[])).next())})}class ws{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,n){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),n?.once){const o=()=>{this.removeEventListener(e,o),this.removeEventListener(e,t)};return this.addEventListener(e,o),o}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var n;(n=this.listeners[e])===null||n===void 0||n.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach(n=>n(...t))}}class ks extends ws{constructor(e){super(),this.subscriptions=[],this.options=e}onInit(){}init(e){this.wavesurfer=e,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach(e=>e())}}const ys=["audio/webm","audio/wav","audio/mpeg","audio/mp4","audio/mp3"];class mt extends ks{constructor(e){var t;super(Object.assign(Object.assign({},e),{audioBitsPerSecond:(t=e.audioBitsPerSecond)!==null&&t!==void 0?t:128e3})),this.stream=null,this.mediaRecorder=null}static create(e){return new mt(e||{})}renderMicStream(e){const t=new AudioContext,n=t.createMediaStreamSource(e),o=t.createAnalyser();n.connect(o);const r=o.frequencyBinCount,s=new Float32Array(r),u=r/t.sampleRate;let a;const d=()=>{o.getFloatTimeDomainData(s),this.wavesurfer&&(this.wavesurfer.options.cursorWidth=0,this.wavesurfer.options.interact=!1,this.wavesurfer.load("",[s],u)),a=requestAnimationFrame(d)};return d(),()=>{cancelAnimationFrame(a),n?.disconnect(),t?.close()}}startMic(e){return sn(this,void 0,void 0,function*(){let t;try{t=yield navigator.mediaDevices.getUserMedia({audio:!e?.deviceId||{deviceId:e.deviceId}})}catch(o){throw new Error("Error accessing the microphone: "+o.message)}const n=this.renderMicStream(t);return this.subscriptions.push(this.once("destroy",n)),this.stream=t,t})}stopMic(){this.stream&&(this.stream.getTracks().forEach(e=>e.stop()),this.stream=null,this.mediaRecorder=null)}startRecording(e){return sn(this,void 0,void 0,function*(){const t=this.stream||(yield this.startMic(e)),n=this.mediaRecorder||new MediaRecorder(t,{mimeType:this.options.mimeType||ys.find(r=>MediaRecorder.isTypeSupported(r)),audioBitsPerSecond:this.options.audioBitsPerSecond});this.mediaRecorder=n,this.stopRecording();const o=[];n.ondataavailable=r=>{r.data.size>0&&o.push(r.data)},n.onstop=()=>{var r;const s=new Blob(o,{type:n.mimeType});this.emit("record-end",s),this.options.renderRecordedAudio!==!1&&((r=this.wavesurfer)===null||r===void 0||r.load(URL.createObjectURL(s)))},n.start(),this.emit("record-start")})}isRecording(){var e;return((e=this.mediaRecorder)===null||e===void 0?void 0:e.state)==="recording"}isPaused(){var e;return((e=this.mediaRecorder)===null||e===void 0?void 0:e.state)==="paused"}stopRecording(){var e;this.isRecording()&&((e=this.mediaRecorder)===null||e===void 0||e.stop())}pauseRecording(){var e;this.isRecording()&&((e=this.mediaRecorder)===null||e===void 0||e.pause(),this.emit("record-pause"))}resumeRecording(){var e;this.isPaused()&&((e=this.mediaRecorder)===null||e===void 0||e.resume(),this.emit("record-resume"))}static getAvailableAudioDevices(){return sn(this,void 0,void 0,function*(){return navigator.mediaDevices.enumerateDevices().then(e=>e.filter(t=>t.kind==="audioinput"))})}destroy(){super.destroy(),this.stopRecording(),this.stopMic()}}const{SvelteComponent:Cs,append:Si,attr:Gn,destroy_each:Es,detach:Wt,element:vn,empty:Rs,ensure_array_like:Yn,init:Ss,insert:qt,noop:Zn,safe_not_equal:Ds,set_data:Di,set_input_value:dn,text:Li}=window.__gradio__svelte__internal,{createEventDispatcher:Ls}=window.__gradio__svelte__internal;function Jn(i,e,t){const n=i.slice();return n[3]=e[t],n}function Ms(i){let e,t=Yn(i[0]),n=[];for(let o=0;o<t.length;o+=1)n[o]=Kn(Jn(i,t,o));return{c(){for(let o=0;o<n.length;o+=1)n[o].c();e=Rs()},m(o,r){for(let s=0;s<n.length;s+=1)n[s]&&n[s].m(o,r);qt(o,e,r)},p(o,r){if(r&1){t=Yn(o[0]);let s;for(s=0;s<t.length;s+=1){const u=Jn(o,t,s);n[s]?n[s].p(u,r):(n[s]=Kn(u),n[s].c(),n[s].m(e.parentNode,e))}for(;s<n.length;s+=1)n[s].d(1);n.length=t.length}},d(o){o&&Wt(e),Es(n,o)}}}function Ps(i){let e,t=i[1]("audio.no_microphone")+"",n;return{c(){e=vn("option"),n=Li(t),e.__value="",dn(e,e.__value)},m(o,r){qt(o,e,r),Si(e,n)},p(o,r){r&2&&t!==(t=o[1]("audio.no_microphone")+"")&&Di(n,t)},d(o){o&&Wt(e)}}}function Kn(i){let e,t=i[3].label+"",n,o;return{c(){e=vn("option"),n=Li(t),e.__value=o=i[3].deviceId,dn(e,e.__value)},m(r,s){qt(r,e,s),Si(e,n)},p(r,s){s&1&&t!==(t=r[3].label+"")&&Di(n,t),s&1&&o!==(o=r[3].deviceId)&&(e.__value=o,dn(e,e.__value))},d(r){r&&Wt(e)}}}function As(i){let e,t;function n(s,u){return s[0].length===0?Ps:Ms}let o=n(i),r=o(i);return{c(){e=vn("select"),r.c(),Gn(e,"class","mic-select svelte-1v4948z"),Gn(e,"aria-label","Select input device"),e.disabled=t=i[0].length===0},m(s,u){qt(s,e,u),r.m(e,null)},p(s,[u]){o===(o=n(s))&&r?r.p(s,u):(r.d(1),r=o(s),r&&(r.c(),r.m(e,null))),u&1&&t!==(t=s[0].length===0)&&(e.disabled=t)},i:Zn,o:Zn,d(s){s&&Wt(e),r.d()}}}function Ts(i,e,t){let{i18n:n}=e,{micDevices:o=[]}=e;const r=Ls();return i.$$set=s=>{"i18n"in s&&t(1,n=s.i18n),"micDevices"in s&&t(0,o=s.micDevices)},i.$$.update=()=>{if(i.$$.dirty&2)try{let s=[];mt.getAvailableAudioDevices().then(u=>{t(0,o=u),u.forEach(a=>{a.deviceId&&s.push(a)}),t(0,o=s)})}catch(s){throw s instanceof DOMException&&s.name=="NotAllowedError"&&r("error",n("audio.allow_recording_access")),s}},[o,n]}class Mi extends Cs{constructor(e){super(),Ss(this,e,Ts,As,Ds,{i18n:1,micDevices:0})}}const{SvelteComponent:zs,add_flush_callback:Ws,append:J,attr:de,bind:qs,binding_callbacks:Je,create_component:Qn,destroy_component:$n,detach:Pi,element:ye,init:Os,insert:Ai,listen:lt,mount_component:xn,run_all:Bs,safe_not_equal:Hs,set_data:at,space:Ze,text:ut,transition_in:ei,transition_out:ti}=window.__gradio__svelte__internal;function ni(i){let e,t;return{c(){e=ye("time"),t=ut(i[2]),de(e,"class","duration-button duration svelte-1d9m1oy")},m(n,o){Ai(n,e,o),J(e,t)},p(n,o){o&4&&at(t,n[2])},d(n){n&&Pi(e)}}}function Ns(i){let e,t,n,o=i[1]("audio.record")+"",r,s,u,a=i[1]("audio.stop")+"",d,l,c,f,_=i[1]("audio.stop")+"",m,h,g,k,E,p,L=i[1]("audio.resume")+"",R,A,C,T,S,z,H,O;k=new pi({});let W=i[4]&&!i[3]&&ni(i);function G(M){i[21](M)}let q={i18n:i[1]};return i[5]!==void 0&&(q.micDevices=i[5]),T=new Mi({props:q}),Je.push(()=>qs(T,"micDevices",G)),{c(){e=ye("div"),t=ye("div"),n=ye("button"),r=ut(o),s=Ze(),u=ye("button"),d=ut(a),c=Ze(),f=ye("button"),m=ut(_),h=Ze(),g=ye("button"),Qn(k.$$.fragment),E=Ze(),p=ye("button"),R=ut(L),A=Ze(),W&&W.c(),C=Ze(),Qn(T.$$.fragment),de(n,"class","record record-button svelte-1d9m1oy"),de(u,"class",l="stop-button "+(i[0].isPaused()?"stop-button-paused":"")+" svelte-1d9m1oy"),de(f,"id","stop-paused"),de(f,"class","stop-button-paused svelte-1d9m1oy"),de(g,"aria-label","pause"),de(g,"class","pause-button svelte-1d9m1oy"),de(p,"class","resume-button svelte-1d9m1oy"),de(t,"class","wrapper svelte-1d9m1oy"),de(e,"class","controls svelte-1d9m1oy")},m(M,v){Ai(M,e,v),J(e,t),J(t,n),J(n,r),i[11](n),J(t,s),J(t,u),J(u,d),i[13](u),J(t,c),J(t,f),J(f,m),i[15](f),J(t,h),J(t,g),xn(k,g,null),i[17](g),J(t,E),J(t,p),J(p,R),i[19](p),J(t,A),W&&W.m(t,null),J(e,C),xn(T,e,null),z=!0,H||(O=[lt(n,"click",i[12]),lt(u,"click",i[14]),lt(f,"click",i[16]),lt(g,"click",i[18]),lt(p,"click",i[20])],H=!0)},p(M,[v]){(!z||v&2)&&o!==(o=M[1]("audio.record")+"")&&at(r,o),(!z||v&2)&&a!==(a=M[1]("audio.stop")+"")&&at(d,a),(!z||v&1&&l!==(l="stop-button "+(M[0].isPaused()?"stop-button-paused":"")+" svelte-1d9m1oy"))&&de(u,"class",l),(!z||v&2)&&_!==(_=M[1]("audio.stop")+"")&&at(m,_),(!z||v&2)&&L!==(L=M[1]("audio.resume")+"")&&at(R,L),M[4]&&!M[3]?W?W.p(M,v):(W=ni(M),W.c(),W.m(t,null)):W&&(W.d(1),W=null);const U={};v&2&&(U.i18n=M[1]),!S&&v&32&&(S=!0,U.micDevices=M[5],Ws(()=>S=!1)),T.$set(U)},i(M){z||(ei(k.$$.fragment,M),ei(T.$$.fragment,M),z=!0)},o(M){ti(k.$$.fragment,M),ti(T.$$.fragment,M),z=!1},d(M){M&&Pi(e),i[11](null),i[13](null),i[15](null),$n(k),i[17](null),i[19](null),W&&W.d(),$n(T),H=!1,Bs(O)}}}function Is(i,e,t){let{record:n}=e,{i18n:o}=e,r=[],s,u,a,d,l,{record_time:c}=e,{show_recording_waveform:f}=e,{timing:_=!1}=e;function m(S){Je[S?"unshift":"push"](()=>{s=S,t(6,s),t(0,n)})}const h=()=>n.startRecording();function g(S){Je[S?"unshift":"push"](()=>{d=S,t(9,d),t(0,n)})}const k=()=>{n.isPaused()&&(n.resumeRecording(),n.stopRecording()),n.stopRecording()};function E(S){Je[S?"unshift":"push"](()=>{l=S,t(10,l),t(0,n)})}const p=()=>{n.isPaused()&&(n.resumeRecording(),n.stopRecording()),n.stopRecording()};function L(S){Je[S?"unshift":"push"](()=>{u=S,t(7,u),t(0,n)})}const R=()=>n.pauseRecording();function A(S){Je[S?"unshift":"push"](()=>{a=S,t(8,a),t(0,n)})}const C=()=>n.resumeRecording();function T(S){r=S,t(5,r)}return i.$$set=S=>{"record"in S&&t(0,n=S.record),"i18n"in S&&t(1,o=S.i18n),"record_time"in S&&t(2,c=S.record_time),"show_recording_waveform"in S&&t(3,f=S.show_recording_waveform),"timing"in S&&t(4,_=S.timing)},i.$$.update=()=>{i.$$.dirty&1&&n.on("record-start",()=>{n.startMic(),t(6,s.style.display="none",s),t(9,d.style.display="flex",d),t(7,u.style.display="block",u)}),i.$$.dirty&1&&n.on("record-end",()=>{n.isPaused()&&(n.resumeRecording(),n.stopRecording()),n.stopMic(),t(6,s.style.display="flex",s),t(9,d.style.display="none",d),t(7,u.style.display="none",u),t(6,s.disabled=!1,s)}),i.$$.dirty&1&&n.on("record-pause",()=>{t(7,u.style.display="none",u),t(8,a.style.display="block",a),t(9,d.style.display="none",d),t(10,l.style.display="flex",l)}),i.$$.dirty&1&&n.on("record-resume",()=>{t(7,u.style.display="block",u),t(8,a.style.display="none",a),t(6,s.style.display="none",s),t(9,d.style.display="flex",d),t(10,l.style.display="none",l)})},[n,o,c,f,_,r,s,u,a,d,l,m,h,g,k,E,p,L,R,A,C,T]}class Vs extends zs{constructor(e){super(),Os(this,e,Is,Ns,Hs,{record:0,i18n:1,record_time:2,show_recording_waveform:3,timing:4})}}const{SvelteComponent:Us,add_flush_callback:yt,append:ae,attr:me,bind:Ct,binding_callbacks:Re,check_outros:ii,create_component:Ti,destroy_component:zi,detach:gt,element:ge,group_outros:oi,init:js,insert:pt,mount_component:Wi,noop:Fs,safe_not_equal:Xs,set_data:qi,space:Qe,text:Oi,transition_in:Ee,transition_out:$e}=window.__gradio__svelte__internal,{onMount:Gs}=window.__gradio__svelte__internal,{createEventDispatcher:Ys}=window.__gradio__svelte__internal;function ri(i){let e,t,n,o,r,s=i[0]==="edit"&&i[16]>0&&si(i);function u(l,c){return l[15]?Js:Zs}let a=u(i),d=a(i);return{c(){e=ge("div"),t=ge("time"),t.textContent="0:00",n=Qe(),o=ge("div"),s&&s.c(),r=Qe(),d.c(),me(t,"class","time svelte-9n45fh"),me(e,"class","timestamps svelte-9n45fh")},m(l,c){pt(l,e,c),ae(e,t),i[23](t),ae(e,n),ae(e,o),s&&s.m(o,null),ae(o,r),d.m(o,null)},p(l,c){l[0]==="edit"&&l[16]>0?s?s.p(l,c):(s=si(l),s.c(),s.m(o,r)):s&&(s.d(1),s=null),a===(a=u(l))&&d?d.p(l,c):(d.d(1),d=a(l),d&&(d.c(),d.m(o,null)))},d(l){l&&gt(e),i[23](null),s&&s.d(),d.d()}}}function si(i){let e,t=ue(i[16])+"",n;return{c(){e=ge("time"),n=Oi(t),me(e,"class","trim-duration svelte-9n45fh")},m(o,r){pt(o,e,r),ae(e,n)},p(o,r){r[0]&65536&&t!==(t=ue(o[16])+"")&&qi(n,t)},d(o){o&&gt(e)}}}function Zs(i){let e;return{c(){e=ge("time"),e.textContent="0:00",me(e,"class","duration svelte-9n45fh")},m(t,n){pt(t,e,n),i[24](e)},p:Fs,d(t){t&&gt(e),i[24](null)}}}function Js(i){let e,t=ue(i[14])+"",n;return{c(){e=ge("time"),n=Oi(t),me(e,"class","duration svelte-9n45fh")},m(o,r){pt(o,e,r),ae(e,n)},p(o,r){r[0]&16384&&t!==(t=ue(o[14])+"")&&qi(n,t)},d(o){o&&gt(e)}}}function li(i){let e,t,n;function o(s){i[25](s)}let r={i18n:i[1],timing:i[15],show_recording_waveform:i[2].show_recording_waveform,record_time:ue(i[14])};return i[8]!==void 0&&(r.record=i[8]),e=new Vs({props:r}),Re.push(()=>Ct(e,"record",o)),{c(){Ti(e.$$.fragment)},m(s,u){Wi(e,s,u),n=!0},p(s,u){const a={};u[0]&2&&(a.i18n=s[1]),u[0]&32768&&(a.timing=s[15]),u[0]&4&&(a.show_recording_waveform=s[2].show_recording_waveform),u[0]&16384&&(a.record_time=ue(s[14])),!t&&u[0]&256&&(t=!0,a.record=s[8],yt(()=>t=!1)),e.$set(a)},i(s){n||(Ee(e.$$.fragment,s),n=!0)},o(s){$e(e.$$.fragment,s),n=!1},d(s){zi(e,s)}}}function ai(i){let e,t,n,o,r;function s(l){i[26](l)}function u(l){i[27](l)}function a(l){i[28](l)}let d={container:i[6],playing:i[12],audio_duration:i[13],i18n:i[1],editable:i[4],interactive:!0,handle_trim_audio:i[17],show_redo:!0,handle_reset_value:i[3],waveform_options:i[2]};return i[5]!==void 0&&(d.waveform=i[5]),i[16]!==void 0&&(d.trimDuration=i[16]),i[0]!==void 0&&(d.mode=i[0]),e=new yi({props:d}),Re.push(()=>Ct(e,"waveform",s)),Re.push(()=>Ct(e,"trimDuration",u)),Re.push(()=>Ct(e,"mode",a)),{c(){Ti(e.$$.fragment)},m(l,c){Wi(e,l,c),r=!0},p(l,c){const f={};c[0]&64&&(f.container=l[6]),c[0]&4096&&(f.playing=l[12]),c[0]&8192&&(f.audio_duration=l[13]),c[0]&2&&(f.i18n=l[1]),c[0]&16&&(f.editable=l[4]),c[0]&8&&(f.handle_reset_value=l[3]),c[0]&4&&(f.waveform_options=l[2]),!t&&c[0]&32&&(t=!0,f.waveform=l[5],yt(()=>t=!1)),!n&&c[0]&65536&&(n=!0,f.trimDuration=l[16],yt(()=>n=!1)),!o&&c[0]&1&&(o=!0,f.mode=l[0],yt(()=>o=!1)),e.$set(f)},i(l){r||(Ee(e.$$.fragment,l),r=!0)},o(l){$e(e.$$.fragment,l),r=!1},d(l){zi(e,l)}}}function Ks(i){let e,t,n,o,r,s,u,a,d=(i[15]||i[9])&&i[2].show_recording_waveform&&ri(i),l=i[7]&&!i[9]&&li(i),c=i[5]&&i[9]&&ai(i);return{c(){e=ge("div"),t=ge("div"),n=Qe(),o=ge("div"),r=Qe(),d&&d.c(),s=Qe(),l&&l.c(),u=Qe(),c&&c.c(),me(t,"class","microphone svelte-9n45fh"),me(t,"data-testid","microphone-waveform"),me(o,"data-testid","recording-waveform"),me(e,"class","component-wrapper svelte-9n45fh")},m(f,_){pt(f,e,_),ae(e,t),i[21](t),ae(e,n),ae(e,o),i[22](o),ae(e,r),d&&d.m(e,null),ae(e,s),l&&l.m(e,null),ae(e,u),c&&c.m(e,null),a=!0},p(f,_){(f[15]||f[9])&&f[2].show_recording_waveform?d?d.p(f,_):(d=ri(f),d.c(),d.m(e,s)):d&&(d.d(1),d=null),f[7]&&!f[9]?l?(l.p(f,_),_[0]&640&&Ee(l,1)):(l=li(f),l.c(),Ee(l,1),l.m(e,u)):l&&(oi(),$e(l,1,1,()=>{l=null}),ii()),f[5]&&f[9]?c?(c.p(f,_),_[0]&544&&Ee(c,1)):(c=ai(f),c.c(),Ee(c,1),c.m(e,null)):c&&(oi(),$e(c,1,1,()=>{c=null}),ii())},i(f){a||(Ee(l),Ee(c),a=!0)},o(f){$e(l),$e(c),a=!1},d(f){f&&gt(e),i[21](null),i[22](null),d&&d.d(),l&&l.d(),c&&c.d()}}}function Qs(i,e,t){let{mode:n}=e,{i18n:o}=e,{dispatch_blob:r}=e,{waveform_settings:s}=e,{waveform_options:u={show_recording_waveform:!0}}=e,{handle_reset_value:a}=e,{editable:d=!0}=e,l,c,f=!1,_,m,h,g=null,k,E,p,L=0,R,A=!1,C=0;const T=()=>{clearInterval(R),t(20,R=setInterval(()=>{t(14,L++,L)},1e3))},S=Ys(),z=()=>{m&&t(7,m.innerHTML="",m),l!==void 0&&l.destroy(),m&&(l=et.create({...s,normalize:!1,container:m}),t(8,h=l.registerPlugin(mt.create())),h.startMic())},H=()=>{let b=_;!g||!b||t(5,c=et.create({container:b,url:g,...s}))},O=async(b,Y)=>{t(0,n="edit");const y=c.getDecodedData();y&&await ln(y,b,Y).then(async K=>{await r([K],"change"),await r([K],"stop_recording"),c.destroy(),H()}),S("edit")};Gs(()=>{z(),window.addEventListener("keydown",b=>{b.key==="ArrowRight"?Et(c,.1):b.key==="ArrowLeft"&&Et(c,-.1)})});function W(b){Re[b?"unshift":"push"](()=>{m=b,t(7,m)})}function G(b){Re[b?"unshift":"push"](()=>{_=b,t(6,_)})}function q(b){Re[b?"unshift":"push"](()=>{k=b,t(10,k),t(5,c)})}function M(b){Re[b?"unshift":"push"](()=>{E=b,t(11,E),t(5,c)})}function v(b){h=b,t(8,h)}function U(b){c=b,t(5,c)}function B(b){C=b,t(16,C)}function P(b){n=b,t(0,n)}return i.$$set=b=>{"mode"in b&&t(0,n=b.mode),"i18n"in b&&t(1,o=b.i18n),"dispatch_blob"in b&&t(18,r=b.dispatch_blob),"waveform_settings"in b&&t(19,s=b.waveform_settings),"waveform_options"in b&&t(2,u=b.waveform_options),"handle_reset_value"in b&&t(3,a=b.handle_reset_value),"editable"in b&&t(4,d=b.editable)},i.$$.update=()=>{i.$$.dirty[0]&388&&h?.on("record-start",()=>{if(T(),t(15,A=!0),S("start_recording"),u.show_recording_waveform){let b=m;b&&(b.style.display="block")}}),i.$$.dirty[0]&1835264&&h?.on("record-end",async b=>{t(14,L=0),t(15,A=!1),clearInterval(R);try{const Y=await b.arrayBuffer(),K=await new AudioContext({sampleRate:s.sampleRate}).decodeAudioData(Y);K&&await ln(K).then(async re=>{await r([re],"change"),await r([re],"stop_recording")})}catch(Y){console.error(Y)}}),i.$$.dirty[0]&1048832&&h?.on("record-pause",()=>{S("pause_recording"),clearInterval(R)}),i.$$.dirty[0]&256&&h?.on("record-resume",()=>{T()}),i.$$.dirty[0]&2080&&c?.on("decode",b=>{t(13,p=b),E&&t(11,E.textContent=ue(b),E)}),i.$$.dirty[0]&1056&&c?.on("timeupdate",b=>k&&t(10,k.textContent=ue(b),k)),i.$$.dirty[0]&32&&c?.on("pause",()=>{S("pause"),t(12,f=!1)}),i.$$.dirty[0]&32&&c?.on("play",()=>{S("play"),t(12,f=!0)}),i.$$.dirty[0]&32&&c?.on("finish",()=>{S("stop"),t(12,f=!1)}),i.$$.dirty[0]&960&&h?.on("record-end",b=>{t(9,g=URL.createObjectURL(b));const Y=m,y=_;Y&&(Y.style.display="none"),y&&g&&(y.innerHTML="",H())})},[n,o,u,a,d,c,_,m,h,g,k,E,f,p,L,A,C,O,r,s,R,W,G,q,M,v,U,B,P]}class $s extends Us{constructor(e){super(),js(this,e,Qs,Ks,Xs,{mode:0,i18n:1,dispatch_blob:18,waveform_settings:19,waveform_options:2,handle_reset_value:3,editable:4},null,[-1,-1])}}const{SvelteComponent:xs,add_flush_callback:el,append:pe,attr:Be,bind:tl,binding_callbacks:Bi,create_component:nl,destroy_component:il,detach:Ot,element:Ne,init:ol,insert:Bt,listen:Hi,mount_component:rl,null_to_empty:ui,safe_not_equal:sl,set_data:Ni,set_style:ci,space:Dt,text:Ii,transition_in:ll,transition_out:al}=window.__gradio__svelte__internal,{onMount:ul}=window.__gradio__svelte__internal;function di(i){let e;return{c(){e=Ne("div"),ci(e,"display",i[0]?"block":"none")},m(t,n){Bt(t,e,n),i[10](e)},p(t,n){n&1&&ci(e,"display",t[0]?"block":"none")},d(t){t&&Ot(e),i[10](null)}}}function cl(i){let e,t,n,o=i[4]("audio.record")+"",r,s,u;return{c(){e=Ne("button"),t=Ne("span"),t.innerHTML='<span class="dot"></span>',n=Dt(),r=Ii(o),Be(t,"class","record-icon"),Be(e,"class","record-button svelte-1m31gsz")},m(a,d){Bt(a,e,d),pe(e,t),pe(e,n),pe(e,r),s||(u=Hi(e,"click",i[12]),s=!0)},p(a,d){d&16&&o!==(o=a[4]("audio.record")+"")&&Ni(r,o)},d(a){a&&Ot(e),s=!1,u()}}}function dl(i){let e,t,n,o=(i[1]?i[4]("audio.pause"):i[4]("audio.stop"))+"",r,s,u,a;return{c(){e=Ne("button"),t=Ne("span"),t.innerHTML='<span class="pinger"></span> <span class="dot"></span>',n=Dt(),r=Ii(o),Be(t,"class","record-icon"),Be(e,"class",s=ui(i[1]?"stop-button-paused":"stop-button")+" svelte-1m31gsz")},m(d,l){Bt(d,e,l),pe(e,t),pe(e,n),pe(e,r),u||(a=Hi(e,"click",i[11]),u=!0)},p(d,l){l&18&&o!==(o=(d[1]?d[4]("audio.pause"):d[4]("audio.stop"))+"")&&Ni(r,o),l&2&&s!==(s=ui(d[1]?"stop-button-paused":"stop-button")+" svelte-1m31gsz")&&Be(e,"class",s)},d(d){d&&Ot(e),u=!1,a()}}}function fl(i){let e,t,n,o,r,s,u,a=i[5].show_recording_waveform&&di(i);function d(m,h){return m[0]?dl:cl}let l=d(i),c=l(i);function f(m){i[13](m)}let _={i18n:i[4]};return i[8]!==void 0&&(_.micDevices=i[8]),r=new Mi({props:_}),Bi.push(()=>tl(r,"micDevices",f)),{c(){e=Ne("div"),a&&a.c(),t=Dt(),n=Ne("div"),c.c(),o=Dt(),nl(r.$$.fragment),Be(n,"class","controls svelte-1m31gsz"),Be(e,"class","mic-wrap svelte-1m31gsz")},m(m,h){Bt(m,e,h),a&&a.m(e,null),pe(e,t),pe(e,n),c.m(n,null),pe(n,o),rl(r,n,null),u=!0},p(m,[h]){m[5].show_recording_waveform?a?a.p(m,h):(a=di(m),a.c(),a.m(e,t)):a&&(a.d(1),a=null),l===(l=d(m))&&c?c.p(m,h):(c.d(1),c=l(m),c&&(c.c(),c.m(n,o)));const g={};h&16&&(g.i18n=m[4]),!s&&h&256&&(s=!0,g.micDevices=m[8],el(()=>s=!1)),r.$set(g)},i(m){u||(ll(r.$$.fragment,m),u=!0)},o(m){al(r.$$.fragment,m),u=!1},d(m){m&&Ot(e),a&&a.d(),c.d(),il(r)}}}function hl(i,e,t){let{recording:n=!1}=e,{paused_recording:o=!1}=e,{stop:r}=e,{record:s}=e,{i18n:u}=e,{waveform_settings:a}=e,{waveform_options:d={show_recording_waveform:!0}}=e,l,c,f,_=[];ul(()=>{m()});const m=()=>{l!==void 0&&l.destroy(),f&&(l=et.create({...a,height:100,container:f}),t(6,c=l.registerPlugin(mt.create())))};function h(p){Bi[p?"unshift":"push"](()=>{f=p,t(7,f)})}const g=()=>{c?.stopMic(),r()},k=()=>{c?.startMic(),s()};function E(p){_=p,t(8,_)}return i.$$set=p=>{"recording"in p&&t(0,n=p.recording),"paused_recording"in p&&t(1,o=p.paused_recording),"stop"in p&&t(2,r=p.stop),"record"in p&&t(3,s=p.record),"i18n"in p&&t(4,u=p.i18n),"waveform_settings"in p&&t(9,a=p.waveform_settings),"waveform_options"in p&&t(5,d=p.waveform_options)},[n,o,r,s,u,d,c,f,_,a,h,g,k,E]}class _l extends xs{constructor(e){super(),ol(this,e,hl,fl,sl,{recording:0,paused_recording:1,stop:2,record:3,i18n:4,waveform_settings:9,waveform_options:5})}}const{SvelteComponent:ml,add_flush_callback:Ht,append:gl,attr:pl,bind:Nt,binding_callbacks:It,bubble:Oe,check_outros:bn,create_component:Te,create_slot:vl,destroy_component:ze,detach:tt,element:bl,empty:Vi,get_all_dirty_from_scope:wl,get_slot_changes:kl,group_outros:wn,init:yl,insert:nt,mount_component:We,safe_not_equal:Cl,space:Lt,transition_in:ee,transition_out:te,update_slot_base:El}=window.__gradio__svelte__internal,{getContext:Rl,onDestroy:Sl,createEventDispatcher:Dl}=window.__gradio__svelte__internal;function Ll(i){let e,t,n,o,r;e=new vi({props:{i18n:i[9],download:i[6]?i[1].url:null,absolute:!0}}),e.$on("clear",i[20]),e.$on("edit",i[34]);function s(a){i[35](a)}let u={value:i[1],label:i[3],i18n:i[9],dispatch_blob:i[18],waveform_settings:i[10],waveform_options:i[12],trim_region_settings:i[11],handle_reset_value:i[13],editable:i[14],interactive:!0};return i[16]!==void 0&&(u.mode=i[16]),n=new Ri({props:u}),It.push(()=>Nt(n,"mode",s)),n.$on("stop",i[36]),n.$on("play",i[37]),n.$on("pause",i[38]),n.$on("edit",i[39]),{c(){Te(e.$$.fragment),t=Lt(),Te(n.$$.fragment)},m(a,d){We(e,a,d),nt(a,t,d),We(n,a,d),r=!0},p(a,d){const l={};d[0]&512&&(l.i18n=a[9]),d[0]&66&&(l.download=a[6]?a[1].url:null),e.$set(l);const c={};d[0]&2&&(c.value=a[1]),d[0]&8&&(c.label=a[3]),d[0]&512&&(c.i18n=a[9]),d[0]&1024&&(c.waveform_settings=a[10]),d[0]&4096&&(c.waveform_options=a[12]),d[0]&2048&&(c.trim_region_settings=a[11]),d[0]&8192&&(c.handle_reset_value=a[13]),d[0]&16384&&(c.editable=a[14]),!o&&d[0]&65536&&(o=!0,c.mode=a[16],Ht(()=>o=!1)),n.$set(c)},i(a){r||(ee(e.$$.fragment,a),ee(n.$$.fragment,a),r=!0)},o(a){te(e.$$.fragment,a),te(n.$$.fragment,a),r=!1},d(a){a&&tt(t),ze(e,a),ze(n,a)}}}function Ml(i){let e,t,n,o;const r=[Al,Pl],s=[];function u(a,d){return a[2]==="microphone"?0:a[2]==="upload"?1:-1}return~(e=u(i))&&(t=s[e]=r[e](i)),{c(){t&&t.c(),n=Vi()},m(a,d){~e&&s[e].m(a,d),nt(a,n,d),o=!0},p(a,d){let l=e;e=u(a),e===l?~e&&s[e].p(a,d):(t&&(wn(),te(s[l],1,1,()=>{s[l]=null}),bn()),~e?(t=s[e],t?t.p(a,d):(t=s[e]=r[e](a),t.c()),ee(t,1),t.m(n.parentNode,n)):t=null)},i(a){o||(ee(t),o=!0)},o(a){te(t),o=!1},d(a){a&&tt(n),~e&&s[e].d(a)}}}function Pl(i){let e,t,n;function o(s){i[32](s)}let r={filetype:"audio/aac,audio/midi,audio/mpeg,audio/ogg,audio/wav,audio/x-wav,audio/opus,audio/webm,audio/flac,audio/vnd.rn-realaudio,audio/x-ms-wma,audio/x-aiff,audio/amr,audio/*",root:i[4],$$slots:{default:[Tl]},$$scope:{ctx:i}};return i[0]!==void 0&&(r.dragging=i[0]),e=new no({props:r}),It.push(()=>Nt(e,"dragging",o)),e.$on("load",i[21]),e.$on("error",i[33]),{c(){Te(e.$$.fragment)},m(s,u){We(e,s,u),n=!0},p(s,u){const a={};u[0]&16&&(a.root=s[4]),u[1]&1024&&(a.$$scope={dirty:u,ctx:s}),!t&&u[0]&1&&(t=!0,a.dragging=s[0],Ht(()=>t=!1)),e.$set(a)},i(s){n||(ee(e.$$.fragment,s),n=!0)},o(s){te(e.$$.fragment,s),n=!1},d(s){ze(e,s)}}}function Al(i){let e,t,n,o,r,s;e=new vi({props:{i18n:i[9],absolute:!0}}),e.$on("clear",i[20]);const u=[Wl,zl],a=[];function d(l,c){return l[8]?0:1}return n=d(i),o=a[n]=u[n](i),{c(){Te(e.$$.fragment),t=Lt(),o.c(),r=Vi()},m(l,c){We(e,l,c),nt(l,t,c),a[n].m(l,c),nt(l,r,c),s=!0},p(l,c){const f={};c[0]&512&&(f.i18n=l[9]),e.$set(f);let _=n;n=d(l),n===_?a[n].p(l,c):(wn(),te(a[_],1,1,()=>{a[_]=null}),bn(),o=a[n],o?o.p(l,c):(o=a[n]=u[n](l),o.c()),ee(o,1),o.m(r.parentNode,r))},i(l){s||(ee(e.$$.fragment,l),ee(o),s=!0)},o(l){te(e.$$.fragment,l),te(o),s=!1},d(l){l&&(tt(t),tt(r)),ze(e,l),a[n].d(l)}}}function Tl(i){let e;const t=i[27].default,n=vl(t,i,i[41],null);return{c(){n&&n.c()},m(o,r){n&&n.m(o,r),e=!0},p(o,r){n&&n.p&&(!e||r[1]&1024)&&El(n,t,o,o[41],e?kl(t,o[41],r,null):wl(o[41]),null)},i(o){e||(ee(n,o),e=!0)},o(o){te(n,o),e=!1},d(o){n&&n.d(o)}}}function zl(i){let e,t,n;function o(s){i[28](s)}let r={i18n:i[9],editable:i[14],dispatch_blob:i[18],waveform_settings:i[10],waveform_options:i[12],handle_reset_value:i[13]};return i[16]!==void 0&&(r.mode=i[16]),e=new $s({props:r}),It.push(()=>Nt(e,"mode",o)),e.$on("start_recording",i[29]),e.$on("pause_recording",i[30]),e.$on("stop_recording",i[31]),{c(){Te(e.$$.fragment)},m(s,u){We(e,s,u),n=!0},p(s,u){const a={};u[0]&512&&(a.i18n=s[9]),u[0]&16384&&(a.editable=s[14]),u[0]&1024&&(a.waveform_settings=s[10]),u[0]&4096&&(a.waveform_options=s[12]),u[0]&8192&&(a.handle_reset_value=s[13]),!t&&u[0]&65536&&(t=!0,a.mode=s[16],Ht(()=>t=!1)),e.$set(a)},i(s){n||(ee(e.$$.fragment,s),n=!0)},o(s){te(e.$$.fragment,s),n=!1},d(s){ze(e,s)}}}function Wl(i){let e,t;return e=new _l({props:{record:i[19],recording:i[15],stop:i[22],i18n:i[9],waveform_settings:i[10],waveform_options:i[12]}}),{c(){Te(e.$$.fragment)},m(n,o){We(e,n,o),t=!0},p(n,o){const r={};o[0]&32768&&(r.recording=n[15]),o[0]&512&&(r.i18n=n[9]),o[0]&1024&&(r.waveform_settings=n[10]),o[0]&4096&&(r.waveform_options=n[12]),e.$set(r)},i(n){t||(ee(e.$$.fragment,n),t=!0)},o(n){te(e.$$.fragment,n),t=!1},d(n){ze(e,n)}}}function ql(i){let e,t,n,o,r,s,u,a,d;e=new _i({props:{show_label:i[5],Icon:Mt,float:i[2]==="upload"&&i[1]===null,label:i[3]||i[9]("audio.audio")}});const l=[Ml,Ll],c=[];function f(h,g){return h[1]===null||h[8]?0:1}o=f(i),r=c[o]=l[o](i);function _(h){i[40](h)}let m={sources:i[7],handle_clear:i[20]};return i[2]!==void 0&&(m.active_source=i[2]),u=new io({props:m}),It.push(()=>Nt(u,"active_source",_)),{c(){Te(e.$$.fragment),t=Lt(),n=bl("div"),r.c(),s=Lt(),Te(u.$$.fragment),pl(n,"class","audio-container svelte-cbyffp")},m(h,g){We(e,h,g),nt(h,t,g),nt(h,n,g),c[o].m(n,null),gl(n,s),We(u,n,null),d=!0},p(h,g){const k={};g[0]&32&&(k.show_label=h[5]),g[0]&6&&(k.float=h[2]==="upload"&&h[1]===null),g[0]&520&&(k.label=h[3]||h[9]("audio.audio")),e.$set(k);let E=o;o=f(h),o===E?c[o].p(h,g):(wn(),te(c[E],1,1,()=>{c[E]=null}),bn(),r=c[o],r?r.p(h,g):(r=c[o]=l[o](h),r.c()),ee(r,1),r.m(n,s));const p={};g[0]&128&&(p.sources=h[7]),!a&&g[0]&4&&(a=!0,p.active_source=h[2],Ht(()=>a=!1)),u.$set(p)},i(h){d||(ee(e.$$.fragment,h),ee(r),ee(u.$$.fragment,h),d=!0)},o(h){te(e.$$.fragment,h),te(r),te(u.$$.fragment,h),d=!1},d(h){h&&(tt(t),tt(n)),ze(e,h),c[o].d(),ze(u)}}}const Ol=500,fi=44;function Bl(i,e,t){let{$$slots:n={},$$scope:o}=e,{value:r=null}=e,{label:s}=e,{root:u}=e,{show_label:a=!0}=e,{show_download_button:d=!1}=e,{sources:l=["microphone","upload"]}=e,{pending:c=!1}=e,{streaming:f=!1}=e,{i18n:_}=e,{waveform_settings:m}=e,{trim_region_settings:h={}}=e,{waveform_options:g={}}=e,{dragging:k}=e,{active_source:E}=e,{handle_reset_value:p=()=>{}}=e,{editable:L=!0}=e;const R=Rl("upload_files");let A=!1,C,T="",S,z=[],H=!1,O=!1,W=[],G;function q(){G=[En(()=>import("./module-0fd9a083.js"),["./module-0fd9a083.js","./module-a3cf0cc4.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css"],import.meta.url),En(()=>import("./module-a5a0afa0.js"),["./module-a5a0afa0.js","./module-a3cf0cc4.js"],import.meta.url)]}f&&q();const M=Dl(),v=async(w,Q)=>{let qe=new File(w,"audio.wav");const it=await eo([qe],Q==="stream");t(1,r=(await to(it,u,void 0,R))?.filter(Boolean)[0]),M(Q,r)};Sl(()=>{f&&C&&C.state!=="inactive"&&C.stop()});async function U(){let w;try{w=await navigator.mediaDevices.getUserMedia({audio:!0})}catch(Q){if(!navigator.mediaDevices){M("error",_("audio.no_device_support"));return}if(Q instanceof DOMException&&Q.name=="NotAllowedError"){M("error",_("audio.allow_recording_access"));return}throw Q}if(w!=null){if(f){const[{MediaRecorder:Q,register:qe},{connect:it}]=await Promise.all(G);await qe(await it()),C=new Q(w,{mimeType:"audio/wav"}),C.addEventListener("dataavailable",B)}else C=new MediaRecorder(w),C.addEventListener("dataavailable",Q=>{W.push(Q.data)}),C.addEventListener("stop",async()=>{t(15,A=!1),await v(W,"change"),await v(W,"stop_recording"),W=[]});O=!0}}async function B(w){let Q=await w.data.arrayBuffer(),qe=new Uint8Array(Q);if(S||(t(24,S=new Uint8Array(Q.slice(0,fi))),qe=new Uint8Array(Q.slice(fi))),c)z.push(qe);else{let it=[S].concat(z,[qe]);v(it,"stream"),t(25,z=[])}}async function P(){t(15,A=!0),M("start_recording"),O||await U(),t(24,S=void 0),f&&C.start(Ol)}function b(){M("change",null),M("clear"),t(16,T=""),t(1,r=null)}function Y({detail:w}){t(1,r=w),M("change",w),M("upload",w)}function y(){t(15,A=!1),f&&(M("stop_recording"),C.stop(),c&&t(26,H=!0),v(W,"stop_recording"),M("clear"),t(16,T=""))}function K(w){T=w,t(16,T)}function re(w){Oe.call(this,i,w)}function fe(w){Oe.call(this,i,w)}function Vt(w){Oe.call(this,i,w)}function Ut(w){k=w,t(0,k)}const jt=({detail:w})=>M("error",w),Ft=()=>t(16,T="edit");function Xt(w){T=w,t(16,T)}function Gt(w){Oe.call(this,i,w)}function Yt(w){Oe.call(this,i,w)}function Zt(w){Oe.call(this,i,w)}function Jt(w){Oe.call(this,i,w)}function D(w){E=w,t(2,E)}return i.$$set=w=>{"value"in w&&t(1,r=w.value),"label"in w&&t(3,s=w.label),"root"in w&&t(4,u=w.root),"show_label"in w&&t(5,a=w.show_label),"show_download_button"in w&&t(6,d=w.show_download_button),"sources"in w&&t(7,l=w.sources),"pending"in w&&t(23,c=w.pending),"streaming"in w&&t(8,f=w.streaming),"i18n"in w&&t(9,_=w.i18n),"waveform_settings"in w&&t(10,m=w.waveform_settings),"trim_region_settings"in w&&t(11,h=w.trim_region_settings),"waveform_options"in w&&t(12,g=w.waveform_options),"dragging"in w&&t(0,k=w.dragging),"active_source"in w&&t(2,E=w.active_source),"handle_reset_value"in w&&t(13,p=w.handle_reset_value),"editable"in w&&t(14,L=w.editable),"$$scope"in w&&t(41,o=w.$$scope)},i.$$.update=()=>{if(i.$$.dirty[0]&1&&M("drag",k),i.$$.dirty[0]&125829120&&H&&c===!1&&(t(26,H=!1),S&&z)){let w=[S].concat(z);t(25,z=[]),v(w,"stream")}},[k,r,E,s,u,a,d,l,f,_,m,h,g,p,L,A,T,M,v,P,b,Y,y,c,S,z,H,n,K,re,fe,Vt,Ut,jt,Ft,Xt,Gt,Yt,Zt,Jt,D,o]}class Hl extends ml{constructor(e){super(),yl(this,e,Bl,ql,Cl,{value:1,label:3,root:4,show_label:5,show_download_button:6,sources:7,pending:23,streaming:8,i18n:9,waveform_settings:10,trim_region_settings:11,waveform_options:12,dragging:0,active_source:2,handle_reset_value:13,editable:14},null,[-1,-1])}}const Nl=Hl,{SvelteComponent:Il,add_flush_callback:Vl,assign:Ui,bind:Ul,binding_callbacks:jl,check_outros:Fl,create_component:Ie,destroy_component:Ve,detach:kn,empty:Xl,flush:I,get_spread_object:ji,get_spread_update:Fi,group_outros:Gl,init:Yl,insert:yn,mount_component:Ue,safe_not_equal:Zl,space:Xi,transition_in:ve,transition_out:be}=window.__gradio__svelte__internal;function Jl(i){let e,t;return e=new hi({props:{variant:i[0]===null&&i[20]==="upload"?"dashed":"solid",border_mode:i[21]?"focus":"base",padding:!1,allow_overflow:!1,elem_id:i[2],elem_classes:i[3],visible:i[4],container:i[10],scale:i[11],min_width:i[12],$$slots:{default:[$l]},$$scope:{ctx:i}}}),{c(){Ie(e.$$.fragment)},m(n,o){Ue(e,n,o),t=!0},p(n,o){const r={};o[0]&1048577&&(r.variant=n[0]===null&&n[20]==="upload"?"dashed":"solid"),o[0]&2097152&&(r.border_mode=n[21]?"focus":"base"),o[0]&4&&(r.elem_id=n[2]),o[0]&8&&(r.elem_classes=n[3]),o[0]&16&&(r.visible=n[4]),o[0]&1024&&(r.container=n[10]),o[0]&2048&&(r.scale=n[11]),o[0]&4096&&(r.min_width=n[12]),o[0]&8364995|o[1]&262144&&(r.$$scope={dirty:o,ctx:n}),e.$set(r)},i(n){t||(ve(e.$$.fragment,n),t=!0)},o(n){be(e.$$.fragment,n),t=!1},d(n){Ve(e,n)}}}function Kl(i){let e,t;return e=new hi({props:{variant:"solid",border_mode:i[21]?"focus":"base",padding:!1,allow_overflow:!1,elem_id:i[2],elem_classes:i[3],visible:i[4],container:i[10],scale:i[11],min_width:i[12],$$slots:{default:[xl]},$$scope:{ctx:i}}}),{c(){Ie(e.$$.fragment)},m(n,o){Ue(e,n,o),t=!0},p(n,o){const r={};o[0]&2097152&&(r.border_mode=n[21]?"focus":"base"),o[0]&4&&(r.elem_id=n[2]),o[0]&8&&(r.elem_classes=n[3]),o[0]&16&&(r.visible=n[4]),o[0]&1024&&(r.container=n[10]),o[0]&2048&&(r.scale=n[11]),o[0]&4096&&(r.min_width=n[12]),o[0]&4842115|o[1]&262144&&(r.$$scope={dirty:o,ctx:n}),e.$set(r)},i(n){t||(ve(e.$$.fragment,n),t=!0)},o(n){be(e.$$.fragment,n),t=!1},d(n){Ve(e,n)}}}function Ql(i){let e,t;return e=new oo({props:{i18n:i[19].i18n,type:"audio"}}),{c(){Ie(e.$$.fragment)},m(n,o){Ue(e,n,o),t=!0},p(n,o){const r={};o[0]&524288&&(r.i18n=n[19].i18n),e.$set(r)},i(n){t||(ve(e.$$.fragment,n),t=!0)},o(n){be(e.$$.fragment,n),t=!1},d(n){Ve(e,n)}}}function $l(i){let e,t,n,o,r;const s=[{autoscroll:i[19].autoscroll},{i18n:i[19].i18n},i[1]];let u={};for(let l=0;l<s.length;l+=1)u=Ui(u,s[l]);e=new gi({props:u});function a(l){i[34](l)}let d={label:i[7],show_label:i[9],show_download_button:i[13],value:i[0],root:i[8],sources:i[6],active_source:i[20],pending:i[17],streaming:i[18],handle_reset_value:i[23],editable:i[15],i18n:i[19].i18n,waveform_settings:i[22],waveform_options:i[16],trim_region_settings:i[24],$$slots:{default:[Ql]},$$scope:{ctx:i}};return i[21]!==void 0&&(d.dragging=i[21]),n=new Nl({props:d}),jl.push(()=>Ul(n,"dragging",a)),n.$on("change",i[35]),n.$on("stream",i[36]),n.$on("drag",i[37]),n.$on("edit",i[38]),n.$on("play",i[39]),n.$on("pause",i[40]),n.$on("stop",i[41]),n.$on("start_recording",i[42]),n.$on("pause_recording",i[43]),n.$on("stop_recording",i[44]),n.$on("upload",i[45]),n.$on("clear",i[46]),n.$on("error",i[25]),{c(){Ie(e.$$.fragment),t=Xi(),Ie(n.$$.fragment)},m(l,c){Ue(e,l,c),yn(l,t,c),Ue(n,l,c),r=!0},p(l,c){const f=c[0]&524290?Fi(s,[c[0]&524288&&{autoscroll:l[19].autoscroll},c[0]&524288&&{i18n:l[19].i18n},c[0]&2&&ji(l[1])]):{};e.$set(f);const _={};c[0]&128&&(_.label=l[7]),c[0]&512&&(_.show_label=l[9]),c[0]&8192&&(_.show_download_button=l[13]),c[0]&1&&(_.value=l[0]),c[0]&256&&(_.root=l[8]),c[0]&64&&(_.sources=l[6]),c[0]&1048576&&(_.active_source=l[20]),c[0]&131072&&(_.pending=l[17]),c[0]&262144&&(_.streaming=l[18]),c[0]&32768&&(_.editable=l[15]),c[0]&524288&&(_.i18n=l[19].i18n),c[0]&4194304&&(_.waveform_settings=l[22]),c[0]&65536&&(_.waveform_options=l[16]),c[0]&524288|c[1]&262144&&(_.$$scope={dirty:c,ctx:l}),!o&&c[0]&2097152&&(o=!0,_.dragging=l[21],Vl(()=>o=!1)),n.$set(_)},i(l){r||(ve(e.$$.fragment,l),ve(n.$$.fragment,l),r=!0)},o(l){be(e.$$.fragment,l),be(n.$$.fragment,l),r=!1},d(l){l&&kn(t),Ve(e,l),Ve(n,l)}}}function xl(i){let e,t,n,o;const r=[{autoscroll:i[19].autoscroll},{i18n:i[19].i18n},i[1]];let s={};for(let u=0;u<r.length;u+=1)s=Ui(s,r[u]);return e=new gi({props:s}),n=new bs({props:{i18n:i[19].i18n,show_label:i[9],show_download_button:i[13],show_share_button:i[14],value:i[0],label:i[7],waveform_settings:i[22],waveform_options:i[16],editable:i[15]}}),n.$on("share",i[29]),n.$on("error",i[30]),n.$on("play",i[31]),n.$on("pause",i[32]),n.$on("stop",i[33]),{c(){Ie(e.$$.fragment),t=Xi(),Ie(n.$$.fragment)},m(u,a){Ue(e,u,a),yn(u,t,a),Ue(n,u,a),o=!0},p(u,a){const d=a[0]&524290?Fi(r,[a[0]&524288&&{autoscroll:u[19].autoscroll},a[0]&524288&&{i18n:u[19].i18n},a[0]&2&&ji(u[1])]):{};e.$set(d);const l={};a[0]&524288&&(l.i18n=u[19].i18n),a[0]&512&&(l.show_label=u[9]),a[0]&8192&&(l.show_download_button=u[13]),a[0]&16384&&(l.show_share_button=u[14]),a[0]&1&&(l.value=u[0]),a[0]&128&&(l.label=u[7]),a[0]&4194304&&(l.waveform_settings=u[22]),a[0]&65536&&(l.waveform_options=u[16]),a[0]&32768&&(l.editable=u[15]),n.$set(l)},i(u){o||(ve(e.$$.fragment,u),ve(n.$$.fragment,u),o=!0)},o(u){be(e.$$.fragment,u),be(n.$$.fragment,u),o=!1},d(u){u&&kn(t),Ve(e,u),Ve(n,u)}}}function ea(i){let e,t,n,o;const r=[Kl,Jl],s=[];function u(a,d){return a[5]?1:0}return e=u(i),t=s[e]=r[e](i),{c(){t.c(),n=Xl()},m(a,d){s[e].m(a,d),yn(a,n,d),o=!0},p(a,d){let l=e;e=u(a),e===l?s[e].p(a,d):(Gl(),be(s[l],1,1,()=>{s[l]=null}),Fl(),t=s[e],t?t.p(a,d):(t=s[e]=r[e](a),t.c()),ve(t,1),t.m(n.parentNode,n))},i(a){o||(ve(t),o=!0)},o(a){be(t),o=!1},d(a){a&&kn(n),s[e].d(a)}}}function ta(i,e,t){let{elem_id:n=""}=e,{elem_classes:o=[]}=e,{visible:r=!0}=e,{interactive:s}=e,{value:u=null}=e,{sources:a}=e,{label:d}=e,{root:l}=e,{show_label:c}=e,{container:f=!0}=e,{scale:_=null}=e,{min_width:m=void 0}=e,{loading_status:h}=e,{autoplay:g=!1}=e,{show_download_button:k}=e,{show_share_button:E=!1}=e,{editable:p=!0}=e,{waveform_options:L={}}=e,{pending:R}=e,{streaming:A}=e,{gradio:C}=e,T=null,S,z=u;const H=()=>{z===null||u===z||t(0,u=z)};let O,W,G=getComputedStyle(document.documentElement).getPropertyValue("--color-accent");const q={color:L.trim_region_color,drag:!0,resize:!0};function M(){document.documentElement.style.setProperty("--trim-region-color",q.color||G)}M();function v({detail:D}){const[w,Q]=D.includes("Invalid file type")?["warning","complete"]:["error","error"];t(1,h=h||{}),t(1,h.status=Q,h),t(1,h.message=D,h),C.dispatch(w,D)}const U=D=>C.dispatch("share",D.detail),B=D=>C.dispatch("error",D.detail),P=()=>C.dispatch("play"),b=()=>C.dispatch("pause"),Y=()=>C.dispatch("stop");function y(D){O=D,t(21,O)}const K=({detail:D})=>t(0,u=D),re=({detail:D})=>{t(0,u=D),C.dispatch("stream",u)},fe=({detail:D})=>t(21,O=D),Vt=()=>C.dispatch("edit"),Ut=()=>C.dispatch("play"),jt=()=>C.dispatch("pause"),Ft=()=>C.dispatch("stop"),Xt=()=>C.dispatch("start_recording"),Gt=()=>C.dispatch("pause_recording"),Yt=D=>C.dispatch("stop_recording"),Zt=()=>C.dispatch("upload"),Jt=()=>C.dispatch("clear");return i.$$set=D=>{"elem_id"in D&&t(2,n=D.elem_id),"elem_classes"in D&&t(3,o=D.elem_classes),"visible"in D&&t(4,r=D.visible),"interactive"in D&&t(5,s=D.interactive),"value"in D&&t(0,u=D.value),"sources"in D&&t(6,a=D.sources),"label"in D&&t(7,d=D.label),"root"in D&&t(8,l=D.root),"show_label"in D&&t(9,c=D.show_label),"container"in D&&t(10,f=D.container),"scale"in D&&t(11,_=D.scale),"min_width"in D&&t(12,m=D.min_width),"loading_status"in D&&t(1,h=D.loading_status),"autoplay"in D&&t(26,g=D.autoplay),"show_download_button"in D&&t(13,k=D.show_download_button),"show_share_button"in D&&t(14,E=D.show_share_button),"editable"in D&&t(15,p=D.editable),"waveform_options"in D&&t(16,L=D.waveform_options),"pending"in D&&t(17,R=D.pending),"streaming"in D&&t(18,A=D.streaming),"gradio"in D&&t(19,C=D.gradio)},i.$$.update=()=>{i.$$.dirty[0]&268435457&&u&&z===null&&t(28,z=u),i.$$.dirty[0]&134742017&&JSON.stringify(u)!==JSON.stringify(T)&&(t(27,T=u),C.dispatch("change")),i.$$.dirty[0]&1048640&&!S&&a&&t(20,S=a[0]),i.$$.dirty[0]&67174400&&t(22,W={height:50,waveColor:L.waveform_color||"#9ca3af",progressColor:L.waveform_progress_color||G,barWidth:2,barGap:3,cursorWidth:2,cursorColor:"#ddd5e9",autoplay:g,barRadius:10,dragToSeek:!0,normalize:!0,minPxPerSec:20,mediaControls:L.show_controls,sampleRate:L.sample_rate||44100})},[u,h,n,o,r,s,a,d,l,c,f,_,m,k,E,p,L,R,A,C,S,O,W,H,q,v,g,T,z,U,B,P,b,Y,y,K,re,fe,Vt,Ut,jt,Ft,Xt,Gt,Yt,Zt,Jt]}class na extends Il{constructor(e){super(),Yl(this,e,ta,ea,Zl,{elem_id:2,elem_classes:3,visible:4,interactive:5,value:0,sources:6,label:7,root:8,show_label:9,container:10,scale:11,min_width:12,loading_status:1,autoplay:26,show_download_button:13,show_share_button:14,editable:15,waveform_options:16,pending:17,streaming:18,gradio:19},null,[-1,-1])}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),I()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),I()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),I()}get interactive(){return this.$$.ctx[5]}set interactive(e){this.$$set({interactive:e}),I()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),I()}get sources(){return this.$$.ctx[6]}set sources(e){this.$$set({sources:e}),I()}get label(){return this.$$.ctx[7]}set label(e){this.$$set({label:e}),I()}get root(){return this.$$.ctx[8]}set root(e){this.$$set({root:e}),I()}get show_label(){return this.$$.ctx[9]}set show_label(e){this.$$set({show_label:e}),I()}get container(){return this.$$.ctx[10]}set container(e){this.$$set({container:e}),I()}get scale(){return this.$$.ctx[11]}set scale(e){this.$$set({scale:e}),I()}get min_width(){return this.$$.ctx[12]}set min_width(e){this.$$set({min_width:e}),I()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),I()}get autoplay(){return this.$$.ctx[26]}set autoplay(e){this.$$set({autoplay:e}),I()}get show_download_button(){return this.$$.ctx[13]}set show_download_button(e){this.$$set({show_download_button:e}),I()}get show_share_button(){return this.$$.ctx[14]}set show_share_button(e){this.$$set({show_share_button:e}),I()}get editable(){return this.$$.ctx[15]}set editable(e){this.$$set({editable:e}),I()}get waveform_options(){return this.$$.ctx[16]}set waveform_options(e){this.$$set({waveform_options:e}),I()}get pending(){return this.$$.ctx[17]}set pending(e){this.$$set({pending:e}),I()}get streaming(){return this.$$.ctx[18]}set streaming(e){this.$$set({streaming:e}),I()}get gradio(){return this.$$.ctx[19]}set gradio(e){this.$$set({gradio:e}),I()}}const Ra=na;export{La as BaseExample,Nl as BaseInteractiveAudio,Ri as BasePlayer,bs as BaseStaticAudio,Ra as default};
//# sourceMappingURL=index-769c92f6.js.map
