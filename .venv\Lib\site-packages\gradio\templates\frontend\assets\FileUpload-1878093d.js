import"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{B as re}from"./BlockLabel-f27805b1.js";import{E as be}from"./Empty-28f63bf0.js";import"./Index-26cfc80a.js";import{F as X}from"./File-d0b52941.js";import{U as ge}from"./ModifyUpload.svelte_svelte_type_style_lang-4ba51100.js";import{M as he}from"./ModifyUpload-66b0c302.js";import{D as de}from"./DownloadLink-7ff36416.js";const Z=t=>{let e=["B","KB","MB","GB","PB"],n=0;for(;t>1024;)t/=1024,n++;let l=e[n];return t.toFixed(1)+"&nbsp;"+l};const{HtmlTag:we,SvelteComponent:pe,append:w,attr:p,check_outros:fe,create_component:ke,destroy_component:ve,destroy_each:ye,detach:U,element:v,ensure_array_like:$,group_outros:ue,init:qe,insert:P,listen:J,mount_component:Fe,noop:x,run_all:Be,safe_not_equal:ze,set_data:Q,set_style:ee,space:L,text:N,toggle_class:le,transition_in:A,transition_out:S}=window.__gradio__svelte__internal,{createEventDispatcher:Ae}=window.__gradio__svelte__internal;function te(t,e,n){const l=t.slice();return l[10]=e[n],l[12]=n,l}function Ce(t){let e=t[2]("file.uploading")+"",n;return{c(){n=N(e)},m(l,i){P(l,n,i)},p(l,i){i&4&&e!==(e=l[2]("file.uploading")+"")&&Q(n,e)},i:x,o:x,d(l){l&&U(n)}}}function Ee(t){let e,n;return e=new de({props:{href:t[10].url,download:window.__is_colab__?null:t[10].orig_name,$$slots:{default:[De]},$$scope:{ctx:t}}}),{c(){ke(e.$$.fragment)},m(l,i){Fe(e,l,i),n=!0},p(l,i){const o={};i&8&&(o.href=l[10].url),i&8&&(o.download=window.__is_colab__?null:l[10].orig_name),i&8200&&(o.$$scope={dirty:i,ctx:l}),e.$set(o)},i(l){n||(A(e.$$.fragment,l),n=!0)},o(l){S(e.$$.fragment,l),n=!1},d(l){ve(e,l)}}}function De(t){let e,n=(t[10].size!=null?Z(t[10].size):"(size unknown)")+"",l;return{c(){e=new we(!1),l=N(" ⇣"),e.a=l},m(i,o){e.m(n,i,o),P(i,l,o)},p(i,o){o&8&&n!==(n=(i[10].size!=null?Z(i[10].size):"(size unknown)")+"")&&e.p(n)},d(i){i&&(e.d(),U(l))}}}function ne(t){let e,n,l,i;function o(){return t[7](t[12])}function a(...u){return t[8](t[12],...u)}return{c(){e=v("td"),n=v("button"),n.textContent="×",p(n,"class","label-clear-button svelte-18wv37q"),p(n,"aria-label","Remove this file"),p(e,"class","svelte-18wv37q")},m(u,r){P(u,e,r),w(e,n),l||(i=[J(n,"click",o),J(n,"keydown",a)],l=!0)},p(u,r){t=u},d(u){u&&U(e),l=!1,Be(i)}}}function ie(t){let e,n,l,i=t[10].filename_stem+"",o,a,u,r=t[10].filename_ext+"",c,f,s,m,_,g,z,y,d,D,I;const M=[Ee,Ce],b=[];function Y(q,k){return q[10].url?0:1}_=Y(t),g=b[_]=M[_](t);let h=t[3].length>1&&ne(t);function me(){return t[9](t[10],t[12])}return{c(){e=v("tr"),n=v("td"),l=v("span"),o=N(i),a=L(),u=v("span"),c=N(r),s=L(),m=v("td"),g.c(),z=L(),h&&h.c(),y=L(),p(l,"class","stem svelte-18wv37q"),p(u,"class","ext svelte-18wv37q"),p(n,"class","filename svelte-18wv37q"),p(n,"aria-label",f=t[10].orig_name),p(m,"class","download svelte-18wv37q"),p(e,"class","file svelte-18wv37q"),le(e,"selectable",t[0])},m(q,k){P(q,e,k),w(e,n),w(n,l),w(l,o),w(n,a),w(n,u),w(u,c),w(e,s),w(e,m),b[_].m(m,null),w(e,z),h&&h.m(e,null),w(e,y),d=!0,D||(I=J(e,"click",me),D=!0)},p(q,k){t=q,(!d||k&8)&&i!==(i=t[10].filename_stem+"")&&Q(o,i),(!d||k&8)&&r!==(r=t[10].filename_ext+"")&&Q(c,r),(!d||k&8&&f!==(f=t[10].orig_name))&&p(n,"aria-label",f);let j=_;_=Y(t),_===j?b[_].p(t,k):(ue(),S(b[j],1,1,()=>{b[j]=null}),fe(),g=b[_],g?g.p(t,k):(g=b[_]=M[_](t),g.c()),A(g,1),g.m(m,null)),t[3].length>1?h?h.p(t,k):(h=ne(t),h.c(),h.m(e,y)):h&&(h.d(1),h=null),(!d||k&1)&&le(e,"selectable",t[0])},i(q){d||(A(g),d=!0)},o(q){S(g),d=!1},d(q){q&&U(e),b[_].d(),h&&h.d(),D=!1,I()}}}function Se(t){let e,n,l,i,o=$(t[3]),a=[];for(let r=0;r<o.length;r+=1)a[r]=ie(te(t,o,r));const u=r=>S(a[r],1,1,()=>{a[r]=null});return{c(){e=v("div"),n=v("table"),l=v("tbody");for(let r=0;r<a.length;r+=1)a[r].c();p(l,"class","svelte-18wv37q"),p(n,"class","file-preview svelte-18wv37q"),p(e,"class","file-preview-holder svelte-18wv37q"),ee(e,"max-height",typeof t[1]===void 0?"auto":t[1]+"px")},m(r,c){P(r,e,c),w(e,n),w(n,l);for(let f=0;f<a.length;f+=1)a[f]&&a[f].m(l,null);i=!0},p(r,[c]){if(c&61){o=$(r[3]);let f;for(f=0;f<o.length;f+=1){const s=te(r,o,f);a[f]?(a[f].p(s,c),A(a[f],1)):(a[f]=ie(s),a[f].c(),A(a[f],1),a[f].m(l,null))}for(ue(),f=o.length;f<a.length;f+=1)u(f);fe()}(!i||c&2)&&ee(e,"max-height",typeof r[1]===void 0?"auto":r[1]+"px")},i(r){if(!i){for(let c=0;c<o.length;c+=1)A(a[c]);i=!0}},o(r){a=a.filter(Boolean);for(let c=0;c<a.length;c+=1)S(a[c]);i=!1},d(r){r&&U(e),ye(a,r)}}}function Ue(t){const e=t.lastIndexOf(".");return e===-1?[t,""]:[t.slice(0,e),t.slice(e)]}function Pe(t,e,n){let l;const i=Ae();let{value:o}=e,{selectable:a=!1}=e,{height:u=void 0}=e,{i18n:r}=e;function c(_){l.splice(_,1),n(3,l=[...l]),n(6,o=l),i("change",l)}const f=_=>c(_),s=(_,g)=>{g.key==="Enter"&&c(_)},m=(_,g)=>i("select",{value:_.orig_name,index:g});return t.$$set=_=>{"value"in _&&n(6,o=_.value),"selectable"in _&&n(0,a=_.selectable),"height"in _&&n(1,u=_.height),"i18n"in _&&n(2,r=_.i18n)},t.$$.update=()=>{t.$$.dirty&64&&n(3,l=(Array.isArray(o)?o:[o]).map(_=>{const[g,z]=Ue(_.orig_name??"");return{..._,filename_stem:g,filename_ext:z}}))},[a,u,r,l,i,c,o,f,s,m]}class Ie extends pe{constructor(e){super(),qe(this,e,Pe,Se,ze,{value:6,selectable:0,height:1,i18n:2})}}const _e=Ie,{SvelteComponent:Me,bubble:Le,check_outros:Ne,create_component:O,destroy_component:R,detach:oe,empty:Ge,group_outros:He,init:Ke,insert:ae,mount_component:T,safe_not_equal:Oe,space:Re,transition_in:C,transition_out:E}=window.__gradio__svelte__internal;function Te(t){let e,n;return e=new be({props:{unpadded_box:!0,size:"large",$$slots:{default:[Je]},$$scope:{ctx:t}}}),{c(){O(e.$$.fragment)},m(l,i){T(e,l,i),n=!0},p(l,i){const o={};i&128&&(o.$$scope={dirty:i,ctx:l}),e.$set(o)},i(l){n||(C(e.$$.fragment,l),n=!0)},o(l){E(e.$$.fragment,l),n=!1},d(l){R(e,l)}}}function je(t){let e,n;return e=new _e({props:{i18n:t[5],selectable:t[3],value:t[0],height:t[4]}}),e.$on("select",t[6]),{c(){O(e.$$.fragment)},m(l,i){T(e,l,i),n=!0},p(l,i){const o={};i&32&&(o.i18n=l[5]),i&8&&(o.selectable=l[3]),i&1&&(o.value=l[0]),i&16&&(o.height=l[4]),e.$set(o)},i(l){n||(C(e.$$.fragment,l),n=!0)},o(l){E(e.$$.fragment,l),n=!1},d(l){R(e,l)}}}function Je(t){let e,n;return e=new X({}),{c(){O(e.$$.fragment)},m(l,i){T(e,l,i),n=!0},i(l){n||(C(e.$$.fragment,l),n=!0)},o(l){E(e.$$.fragment,l),n=!1},d(l){R(e,l)}}}function Qe(t){let e,n,l,i,o,a,u;e=new re({props:{show_label:t[2],float:t[0]===null,Icon:X,label:t[1]||"File"}});const r=[je,Te],c=[];function f(s,m){return m&1&&(l=null),l==null&&(l=!!(s[0]&&(!Array.isArray(s[0])||s[0].length>0))),l?0:1}return i=f(t,-1),o=c[i]=r[i](t),{c(){O(e.$$.fragment),n=Re(),o.c(),a=Ge()},m(s,m){T(e,s,m),ae(s,n,m),c[i].m(s,m),ae(s,a,m),u=!0},p(s,[m]){const _={};m&4&&(_.show_label=s[2]),m&1&&(_.float=s[0]===null),m&2&&(_.label=s[1]||"File"),e.$set(_);let g=i;i=f(s,m),i===g?c[i].p(s,m):(He(),E(c[g],1,1,()=>{c[g]=null}),Ne(),o=c[i],o?o.p(s,m):(o=c[i]=r[i](s),o.c()),C(o,1),o.m(a.parentNode,a))},i(s){u||(C(e.$$.fragment,s),C(o),u=!0)},o(s){E(e.$$.fragment,s),E(o),u=!1},d(s){s&&(oe(n),oe(a)),R(e,s),c[i].d(s)}}}function Ve(t,e,n){let{value:l=null}=e,{label:i}=e,{show_label:o=!0}=e,{selectable:a=!1}=e,{height:u=void 0}=e,{i18n:r}=e;function c(f){Le.call(this,t,f)}return t.$$set=f=>{"value"in f&&n(0,l=f.value),"label"in f&&n(1,i=f.label),"show_label"in f&&n(2,o=f.show_label),"selectable"in f&&n(3,a=f.selectable),"height"in f&&n(4,u=f.height),"i18n"in f&&n(5,r=f.i18n)},[l,i,o,a,u,r,c]}class We extends Me{constructor(e){super(),Ke(this,e,Ve,Qe,Oe,{value:0,label:1,show_label:2,selectable:3,height:4,i18n:5})}}const Fl=We,{SvelteComponent:Xe,add_flush_callback:Ye,bind:Ze,binding_callbacks:$e,bubble:se,check_outros:xe,create_component:G,create_slot:el,destroy_component:H,detach:V,empty:ll,get_all_dirty_from_scope:tl,get_slot_changes:nl,group_outros:il,init:ol,insert:W,mount_component:K,safe_not_equal:al,space:ce,transition_in:F,transition_out:B,update_slot_base:sl}=window.__gradio__svelte__internal,{createEventDispatcher:rl,tick:fl}=window.__gradio__svelte__internal;function ul(t){let e,n,l;function i(a){t[15](a)}let o={filetype:t[4],file_count:t[3],root:t[6],$$slots:{default:[cl]},$$scope:{ctx:t}};return t[9]!==void 0&&(o.dragging=t[9]),e=new ge({props:o}),$e.push(()=>Ze(e,"dragging",i)),e.$on("load",t[10]),{c(){G(e.$$.fragment)},m(a,u){K(e,a,u),l=!0},p(a,u){const r={};u&16&&(r.filetype=a[4]),u&8&&(r.file_count=a[3]),u&64&&(r.root=a[6]),u&65536&&(r.$$scope={dirty:u,ctx:a}),!n&&u&512&&(n=!0,r.dragging=a[9],Ye(()=>n=!1)),e.$set(r)},i(a){l||(F(e.$$.fragment,a),l=!0)},o(a){B(e.$$.fragment,a),l=!1},d(a){H(e,a)}}}function _l(t){let e,n,l,i;return e=new he({props:{i18n:t[8],absolute:!0}}),e.$on("clear",t[11]),l=new _e({props:{i18n:t[8],selectable:t[5],value:t[0],height:t[7]}}),l.$on("select",t[13]),l.$on("change",t[14]),{c(){G(e.$$.fragment),n=ce(),G(l.$$.fragment)},m(o,a){K(e,o,a),W(o,n,a),K(l,o,a),i=!0},p(o,a){const u={};a&256&&(u.i18n=o[8]),e.$set(u);const r={};a&256&&(r.i18n=o[8]),a&32&&(r.selectable=o[5]),a&1&&(r.value=o[0]),a&128&&(r.height=o[7]),l.$set(r)},i(o){i||(F(e.$$.fragment,o),F(l.$$.fragment,o),i=!0)},o(o){B(e.$$.fragment,o),B(l.$$.fragment,o),i=!1},d(o){o&&V(n),H(e,o),H(l,o)}}}function cl(t){let e;const n=t[12].default,l=el(n,t,t[16],null);return{c(){l&&l.c()},m(i,o){l&&l.m(i,o),e=!0},p(i,o){l&&l.p&&(!e||o&65536)&&sl(l,n,i,i[16],e?nl(n,i[16],o,null):tl(i[16]),null)},i(i){e||(F(l,i),e=!0)},o(i){B(l,i),e=!1},d(i){l&&l.d(i)}}}function ml(t){let e,n,l,i,o,a,u;e=new re({props:{show_label:t[2],Icon:X,float:t[0]===null,label:t[1]||"File"}});const r=[_l,ul],c=[];function f(s,m){return m&1&&(l=null),l==null&&(l=!!(s[0]&&(!Array.isArray(s[0])||s[0].length>0))),l?0:1}return i=f(t,-1),o=c[i]=r[i](t),{c(){G(e.$$.fragment),n=ce(),o.c(),a=ll()},m(s,m){K(e,s,m),W(s,n,m),c[i].m(s,m),W(s,a,m),u=!0},p(s,[m]){const _={};m&4&&(_.show_label=s[2]),m&1&&(_.float=s[0]===null),m&2&&(_.label=s[1]||"File"),e.$set(_);let g=i;i=f(s,m),i===g?c[i].p(s,m):(il(),B(c[g],1,1,()=>{c[g]=null}),xe(),o=c[i],o?o.p(s,m):(o=c[i]=r[i](s),o.c()),F(o,1),o.m(a.parentNode,a))},i(s){u||(F(e.$$.fragment,s),F(o),u=!0)},o(s){B(e.$$.fragment,s),B(o),u=!1},d(s){s&&(V(n),V(a)),H(e,s),c[i].d(s)}}}function bl(t,e,n){let{$$slots:l={},$$scope:i}=e,{value:o}=e,{label:a}=e,{show_label:u=!0}=e,{file_count:r="single"}=e,{file_types:c=null}=e,{selectable:f=!1}=e,{root:s}=e,{height:m=void 0}=e,{i18n:_}=e;async function g({detail:b}){n(0,o=b),await fl(),y("change",o),y("upload",b)}function z(){n(0,o=null),y("change",null),y("clear")}const y=rl();let d=!1;function D(b){se.call(this,t,b)}function I(b){se.call(this,t,b)}function M(b){d=b,n(9,d)}return t.$$set=b=>{"value"in b&&n(0,o=b.value),"label"in b&&n(1,a=b.label),"show_label"in b&&n(2,u=b.show_label),"file_count"in b&&n(3,r=b.file_count),"file_types"in b&&n(4,c=b.file_types),"selectable"in b&&n(5,f=b.selectable),"root"in b&&n(6,s=b.root),"height"in b&&n(7,m=b.height),"i18n"in b&&n(8,_=b.i18n),"$$scope"in b&&n(16,i=b.$$scope)},t.$$.update=()=>{t.$$.dirty&512&&y("drag",d)},[o,a,u,r,c,f,s,m,_,d,g,z,l,D,I,M,i]}class gl extends Xe{constructor(e){super(),ol(this,e,bl,ml,al,{value:0,label:1,show_label:2,file_count:3,file_types:4,selectable:5,root:6,height:7,i18n:8})}}const Bl=gl;export{Bl as B,Fl as F,_e as a};
//# sourceMappingURL=FileUpload-1878093d.js.map
