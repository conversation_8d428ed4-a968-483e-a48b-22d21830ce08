{"version": 3, "file": "Example-f2263091.js", "sources": ["../../../../js/multimodaltextbox/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport { Image } from \"@gradio/image/shared\";\n\timport type { FileData } from \"@gradio/client\";\n\n\texport let value: { text: string; files: FileData[] } = {\n\t\ttext: \"\",\n\t\tfiles: []\n\t};\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n\n\tlet size: number;\n\tlet el: HTMLDivElement;\n\n\tfunction set_styles(element: HTMLElement, el_width: number): void {\n\t\tif (!element || !el_width) return;\n\t\tel.style.setProperty(\n\t\t\t\"--local-text-width\",\n\t\t\t`${el_width < 150 ? el_width : 200}px`\n\t\t);\n\t\tel.style.whiteSpace = \"unset\";\n\t}\n\n\tonMount(() => {\n\t\tset_styles(el, size);\n\t});\n</script>\n\n<div\n\tbind:clientWidth={size}\n\tbind:this={el}\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t<p>{value.text ? value.text : \"\"}</p>\n\t{#each value.files as file}\n\t\t{#if file.mime_type && file.mime_type.includes(\"image\")}\n\t\t\t<Image src={file.url} alt=\"\" />\n\t\t{:else}\n\t\t\t{file.path}\n\t\t{/if}\n\t{/each}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 20px;\n\t\toverflow-x: auto;\n\t}\n\n\tdiv {\n\t\toverflow: hidden;\n\t\tmin-width: var(--local-text-width);\n\n\t\twhite-space: nowrap;\n\t}\n\n\t:global(img) {\n\t\twidth: 100px;\n\t\theight: 100px;\n\t}\n\n\tdiv > :global(p) {\n\t\tfont-size: var(--text-lg);\n\t\twhite-space: normal;\n\t}\n</style>\n"], "names": ["onMount", "t_value", "ctx", "dirty", "set_data", "t", "image_changes", "t0_value", "each_value", "ensure_array_like", "i", "toggle_class", "div", "insert", "target", "anchor", "append", "p", "current", "t0", "each_blocks", "value", "$$props", "type", "selected", "size", "el", "set_styles", "element", "el_width", "$$invalidate", "$$value"], "mappings": "mjBACU,CAAA,QAAAA,CAAA,SAAuB,gGAwC7B,IAAAC,EAAAC,KAAK,KAAI,gDAATC,EAAA,GAAAF,KAAAA,EAAAC,KAAK,KAAI,KAAAE,EAAAC,EAAAJ,CAAA,gEAFE,MAAA,CAAA,IAAAC,KAAK,IAAG,IAAA,EAAA,mEAARC,EAAA,IAAAG,EAAA,IAAAJ,KAAK,kMADbA,EAAI,CAAA,EAAC,WAAaA,KAAK,UAAU,SAAS,OAAO,uUAFnDK,GAAAL,KAAM,KAAOA,EAAM,CAAA,EAAA,KAAO,IAAE,WACzBM,EAAAC,EAAAP,KAAM,KAAK,uBAAhB,OAAIQ,GAAA,4LALOC,EAAAC,EAAA,QAAAV,OAAS,OAAO,EACdS,EAAAC,EAAA,UAAAV,OAAS,SAAS,+BAJlCW,EAeKC,EAAAF,EAAAG,CAAA,EARJC,EAAoCJ,EAAAK,CAAA,gHAAhC,CAAAC,GAAAf,EAAA,IAAAI,KAAAA,GAAAL,KAAM,KAAOA,EAAM,CAAA,EAAA,KAAO,IAAE,KAAAE,EAAAe,EAAAZ,CAAA,OACzBC,EAAAC,EAAAP,KAAM,KAAK,oBAAhB,OAAIQ,GAAA,EAAA,yGAAJ,OAAIA,EAAAU,EAAA,OAAAV,GAAA,sBALOC,EAAAC,EAAA,QAAAV,OAAS,OAAO,aACdS,EAAAC,EAAA,UAAAV,OAAS,SAAS,+DAI/B,OAAIQ,GAAA,2IAhCK,GAAA,CAAA,MAAAW,EACV,CAAA,KAAM,GACN,MAAK,CAAA,CAAA,CAAA,EAAAC,GAEK,KAAAC,CAAyB,EAAAD,EACzB,CAAA,SAAAE,EAAW,EAAK,EAAAF,EAEvBG,EACAC,WAEKC,EAAWC,EAAsBC,EAAgB,CACpD,CAAAD,IAAYC,IACjBH,EAAG,MAAM,YACR,qBACG,GAAAG,EAAW,IAAMA,EAAW,OAAG,EAEnCC,EAAA,EAAAJ,EAAG,MAAM,WAAa,QAAOA,CAAA,GAG9B1B,EAAO,IAAA,CACN2B,EAAWD,EAAID,CAAI,iBAKFA,EAAI,KAAA,4DACXC,EAAEK"}