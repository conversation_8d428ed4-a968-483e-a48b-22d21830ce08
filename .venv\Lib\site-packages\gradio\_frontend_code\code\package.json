{"name": "@gradio/code", "version": "0.5.10", "description": "Gradio UI packages", "type": "module", "author": "", "license": "ISC", "private": false, "dependencies": {"@codemirror/autocomplete": "^6.3.0", "@codemirror/commands": "^6.1.2", "@codemirror/lang-css": "^6.1.0", "@codemirror/lang-html": "^6.4.2", "@codemirror/lang-javascript": "^6.1.4", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.1.0", "@codemirror/lang-python": "^6.0.4", "@codemirror/language": "^6.6.0", "@codemirror/legacy-modes": "^6.3.1", "@codemirror/lint": "^6.0.0", "@codemirror/search": "^6.2.2", "@codemirror/state": "^6.1.2", "@codemirror/view": "^6.4.1", "@gradio/atoms": "workspace:^", "@gradio/icons": "workspace:^", "@gradio/statustracker": "workspace:^", "@gradio/upload": "workspace:^", "@gradio/utils": "workspace:^", "@gradio/wasm": "workspace:^", "@lezer/common": "^1.0.2", "@lezer/highlight": "^1.1.3", "@lezer/markdown": "^1.0.2", "cm6-theme-basic-dark": "^0.2.0", "cm6-theme-basic-light": "^0.2.0", "codemirror": "^6.0.1"}, "main_changeset": true, "main": "./Index.svelte", "exports": {".": "./Index.svelte", "./example": "./Example.svelte", "./package.json": "./package.json"}}