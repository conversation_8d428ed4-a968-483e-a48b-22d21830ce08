{"version": 3, "mappings": "29CAAA,IAAIA,GAAK,IAAI,KAAK,SAAS,EAAG,CAAE,QAAS,EAAG,EAAE,QAC9C,SAASC,GAAQC,EAAGC,EAAGC,EAAM,CAC3B,OAAAF,EAAIA,EAAE,MAAM,GAAG,EACfC,EAAIA,EAAE,MAAM,GAAG,EACRH,GAAGE,EAAE,CAAC,EAAGC,EAAE,CAAC,CAAC,GAAKH,GAAGE,EAAE,CAAC,EAAGC,EAAE,CAAC,CAAC,IAAMA,EAAE,CAAC,EAAIA,EAAE,MAAM,CAAC,EAAE,KAAK,GAAG,EAAGC,EAAO,OAAO,KAAKF,EAAE,CAAC,EAAIA,EAAE,MAAM,CAAC,EAAE,KAAK,GAAG,CAAC,EAAGE,GAAQ,OAAO,KAAKD,EAAE,CAAC,CAAC,EAAIH,GAAGE,EAAE,CAAC,EAAGC,EAAE,CAAC,CAAC,EAAIC,EAAO,GAAK,EACrL,CACA,SAASC,GAAaC,EAAUC,EAAWC,EAAiB,CAC1D,OAAID,EAAU,WAAW,SAAS,GAAKA,EAAU,WAAW,UAAU,EAC7DC,EAAkBF,EAAWC,EAE/BD,EAAWC,CACpB,CACA,SAASE,GAAmBC,EAAU,CACpC,GAAIA,EAAS,WAAW,MAAM,EAAG,CAC/B,KAAM,CAAE,SAAAC,EAAU,KAAAC,CAAI,EAAK,IAAI,IAAIF,CAAQ,EAC3C,OAAIE,EAAK,SAAS,UAAU,EACnB,CACL,YAAa,MACb,KAAAA,EACA,cAAeD,CACvB,EAEW,CACL,YAAaA,IAAa,SAAW,MAAQ,KAC7C,cAAeA,EACf,KAAAC,CACN,UACaF,EAAS,WAAW,OAAO,EACpC,MAAO,CACL,YAAa,KACb,cAAe,QACf,KAAM,YAEZ,EAEE,MAAO,CACL,YAAa,MACb,cAAe,SACf,KAAMA,CACV,CACA,CACA,MAAMG,GAAgB,mBAChBC,GAAkB,sBACxB,eAAeC,GAAiBC,EAAeC,EAAO,CACpD,MAAMC,EAAU,GACZD,IACFC,EAAQ,cAAgB,UAAUD,KAEpC,MAAME,EAAiBH,EAAc,OACrC,GAAIH,GAAc,KAAKM,CAAc,EACnC,GAAI,CACF,MAAMC,EAAM,MAAM,MAChB,qCAAqCD,SACrC,CAAE,QAAAD,CAAS,CACnB,EACM,GAAIE,EAAI,SAAW,IACjB,MAAM,IAAI,MAAM,qCAAqC,EACvD,MAAMC,GAAS,MAAMD,EAAI,KAAI,GAAI,KACjC,MAAO,CACL,SAAUJ,EACV,GAAGP,GAAmBY,CAAK,CACnC,CACK,OAAQC,EAAP,CACA,MAAM,IAAI,MAAM,sCAAwCA,EAAE,OAAO,CAClE,CAEH,GAAIR,GAAgB,KAAKK,CAAc,EAAG,CACxC,KAAM,CAAE,YAAAI,EAAa,cAAAC,EAAe,KAAAZ,CAAI,EAAKH,GAAmBU,CAAc,EAC9E,MAAO,CACL,SAAUP,EAAK,QAAQ,YAAa,EAAE,EACtC,YAAAW,EACA,cAAAC,EACA,KAAAZ,CACN,EAEE,MAAO,CACL,SAAU,GACV,GAAGH,GAAmBU,CAAc,CACxC,CACA,CACA,SAASM,GAAiBC,EAAK,CAC7B,IAAIC,EAAO,GACX,OAAAD,EAAI,QAAQ,CAAC,CAAE,SAAAE,CAAQ,EAAIC,IAAM,CAC3BD,IACFD,EAAKC,CAAQ,EAAIC,EACvB,CAAG,EACMF,CACT,CACA,MAAMG,GAAyB,+DAC/B,eAAeC,GAAoBC,EAAU,CAC3C,GAAI,CAOF,MAAMC,GANI,MAAM,MACd,qCAAqCD,gBACrC,CACE,OAAQ,MACT,CACP,GACoB,QAAQ,IAAI,iBAAiB,EAC7C,MAAI,EAAAC,GAASH,GAAuB,KAAKG,CAAK,EAG/C,MAAC,CACA,MAAO,EACR,CACH,CAoEA,SAASC,GAAWC,EAAQC,EAAMC,EAAQC,EAAO,CAC/C,GAAIF,EAAK,SAAW,EAAG,CACrB,GAAIC,IAAW,UACb,OAAOC,EACF,GAAID,IAAW,SACpB,OAAOF,EAASG,EAElB,MAAM,IAAI,MAAM,uBAAuBD,GAAQ,EAEjD,IAAIE,EAAUJ,EACd,QAAS,EAAI,EAAG,EAAIC,EAAK,OAAS,EAAG,IACnCG,EAAUA,EAAQH,EAAK,CAAC,CAAC,EAE3B,MAAMI,EAAYJ,EAAKA,EAAK,OAAS,CAAC,EACtC,OAAQC,EAAM,CACZ,IAAK,UACHE,EAAQC,CAAS,EAAIF,EACrB,MACF,IAAK,SACHC,EAAQC,CAAS,GAAKF,EACtB,MACF,IAAK,MACC,MAAM,QAAQC,CAAO,EACvBA,EAAQ,OAAO,OAAOC,CAAS,EAAG,EAAGF,CAAK,EAE1CC,EAAQC,CAAS,EAAIF,EAEvB,MACF,IAAK,SACC,MAAM,QAAQC,CAAO,EACvBA,EAAQ,OAAO,OAAOC,CAAS,EAAG,CAAC,EAEnC,OAAOD,EAAQC,CAAS,EAE1B,MACF,QACE,MAAM,IAAI,MAAM,mBAAmBH,GAAQ,CAC9C,CACD,OAAOF,CACT,CACA,SAASM,GAAWC,EAAKC,EAAM,CAC7B,OAAAA,EAAK,QAAQ,CAAC,CAACN,EAAQD,EAAME,CAAK,IAAM,CACtCI,EAAMR,GAAWQ,EAAKN,EAAMC,EAAQC,CAAK,CAC7C,CAAG,EACMI,CACT,CACA,eAAeE,GAAOC,EAAWC,EAAMC,EAAWC,EAAYC,GAAc,CAC1E,IAAIC,GAAS,MAAM,QAAQL,CAAS,EAAIA,EAAY,CAACA,CAAS,GAAG,IAC9DM,GAAeA,EAAW,IAC/B,EACE,OAAO,MAAM,QAAQ,IACnB,MAAMH,EAAUF,EAAMI,EAAO,OAAQH,CAAS,EAAE,KAC9C,MAAOK,GAAa,CAClB,GAAIA,EAAS,MACX,MAAM,IAAI,MAAMA,EAAS,KAAK,EAE9B,OAAIA,EAAS,MACJA,EAAS,MAAM,IAAI,CAACC,EAAGxB,IACf,IAAIyB,GAAS,CACxB,GAAGT,EAAUhB,CAAC,EACd,KAAMwB,EACN,IAAKP,EAAO,SAAWO,CACvC,CAAe,CAEF,EAEI,EAEV,CACF,CACL,CACA,CACA,eAAeE,GAAcL,EAAOM,EAAW,CAC7C,OAAON,EAAM,IACX,CAACG,EAAGxB,IAAM,IAAIyB,GAAS,CACrB,KAAMD,EAAE,KACR,UAAWA,EAAE,KACb,KAAMA,EACN,KAAMA,EAAE,KACR,UAAWA,EAAE,KACb,UAAAG,CACN,CAAK,CACL,CACA,CACA,MAAMF,EAAS,CACb,YAAY,CACV,KAAAlB,EACA,IAAAqB,EACA,UAAAC,EACA,KAAAC,EACA,KAAAC,EACA,UAAAJ,EACA,UAAAK,EACA,SAAAC,CACJ,EAAK,CACD,KAAK,KAAO,CAAE,MAAO,iBAAiB,EACtC,KAAK,KAAO1B,EACZ,KAAK,IAAMqB,EACX,KAAK,UAAYC,EACjB,KAAK,KAAOC,EACZ,KAAK,KAAOF,EAAM,OAASG,EAC3B,KAAK,UAAYJ,EACjB,KAAK,UAAYK,EACjB,KAAK,SAAWC,CACjB,CACH,CACA,MAAMC,GAAiB,6CACjBC,EAAwB,0BAC9B,IAAIC,GAkDJ,SAASC,GAAYC,EAAsBC,EAAqB,CAC9D,MAAO,CAAE,UAAWC,EAAY,aAAcC,EAAe,OAAQC,EAAS,YAAaC,GAC3F,eAAeH,EAAWZ,EAAKgB,EAAMxD,EAAO,CAC1C,MAAMC,EAAU,CAAE,eAAgB,oBAC9BD,IACFC,EAAQ,cAAgB,UAAUD,KAEpC,GAAI,CACF,IAAImC,EAAW,MAAMe,EAAqBV,EAAK,CAC7C,OAAQ,OACR,KAAM,KAAK,UAAUgB,CAAI,EACzB,QAAAvD,CACR,CAAO,CACF,MAAC,CACA,MAAO,CAAC,CAAE,MAAO8C,CAAuB,EAAE,GAAG,CAC9C,CACD,IAAIU,EACAC,EACJ,GAAI,CACFD,EAAS,MAAMtB,EAAS,OACxBuB,EAASvB,EAAS,MACnB,OAAQ9B,EAAP,CACAoD,EAAS,CAAE,MAAO,oCAAoCpD,GAAG,EACzDqD,EAAS,GACV,CACD,MAAO,CAACD,EAAQC,CAAM,CACvB,CACD,eAAeL,EAAcxB,EAAMI,EAAOjC,EAAO8B,EAAW,CAC1D,MAAM7B,EAAU,GACZD,IACFC,EAAQ,cAAgB,UAAUD,KAEpC,MAAM2D,EAAY,IACZC,EAAkB,GACxB,QAAShD,EAAI,EAAGA,EAAIqB,EAAM,OAAQrB,GAAK+C,EAAW,CAChD,MAAME,EAAQ5B,EAAM,MAAMrB,EAAGA,EAAI+C,CAAS,EACpCG,EAAW,IAAI,SACrBD,EAAM,QAASE,GAAS,CACtBD,EAAS,OAAO,QAASC,CAAI,CACrC,CAAO,EACD,GAAI,CACF,MAAMC,EAAalC,EAAY,GAAGD,sBAAyBC,IAAc,GAAGD,WAC5E,IAAIM,EAAW,MAAMe,EAAqBc,EAAY,CACpD,OAAQ,OACR,KAAMF,EACN,QAAA7D,CACV,CAAS,CACF,MAAC,CACA,MAAO,CAAE,MAAO8C,EACjB,CACD,MAAMU,EAAS,MAAMtB,EAAS,OAC9ByB,EAAgB,KAAK,GAAGH,CAAM,EAEhC,MAAO,CAAE,MAAOG,EACjB,CACD,eAAeN,EAAQvD,EAAekE,EAAU,GAAI,CAClD,OAAO,IAAI,QAAQ,MAAO9D,GAAQ,CAChC,KAAM,CAAE,gBAAA+D,EAAiB,SAAAC,CAAU,EAAGF,EAChCG,EAAa,CACjB,QAAAC,GACA,OAAAC,GACA,SAAAC,GACA,iBAAAC,EACR,EACM,IAAK,OAAO,OAAW,KAAe,EAAE,cAAe,UAAY,CAAC,OAAO,UAAW,CACpF,MAAMC,EAAK,MAAMC,GAAA,WAAO,gCAAuB,8FAC/C1B,IAAY,MAAM0B,GAAA,WAAO,uCAAa,EAAC,KAAAC,KAAA,wBAAE,KACzC,OAAO,UAAYF,EAAG,UAExB,KAAM,CAAE,YAAAnE,EAAa,cAAAC,EAAe,KAAAZ,EAAM,SAAAoB,CAAU,EAAG,MAAMjB,GAAiBC,EAAeoE,CAAQ,EAC/FS,EAAe,KAAK,OAAQ,EAAC,SAAS,EAAE,EAAE,UAAU,CAAC,EACrDC,EAAc,GACpB,IAAIC,EAAc,GACdC,EAA0B,GAC1BC,EAAuB,GACvBC,EAAe,KACnB,MAAMC,EAAkB,GAClBC,GAAkC,IAAI,IAC5C,IAAIC,EACAC,EAAU,GACVC,GAAM,GACNnB,GAAYpD,IACduE,GAAM,MAAMC,GAAQxE,EAAUoD,CAAQ,GAExC,eAAeqB,GAAeC,EAAS,CAMrC,GALAL,EAASK,EACL,OAAO,SAAS,WAAa,WAC/BL,EAAO,KAAOA,EAAO,KAAK,QAAQ,UAAW,UAAU,GAEzDC,EAAU7E,GAA6CiF,GAAQ,cAAiB,EAAE,EAC9EL,EAAO,cACT,MAAO,CACL,OAAAA,EACA,GAAGhB,CACf,EAEQ,GAAI,CACFsB,EAAM,MAAMnB,GAASa,CAAM,CAC5B,OAAQ/E,EAAP,CACA,QAAQ,MAAM,8BAA8BA,EAAE,SAAS,CACxD,CACD,MAAO,CACL,OAAA+E,EACA,GAAGhB,CACb,CACO,CACD,IAAIsB,EACJ,eAAeC,GAAoBjC,EAAQ,CAGzC,GAFIQ,GACFA,EAAgBR,CAAM,EACpBA,EAAO,SAAW,UACpB,GAAI,CACF0B,EAAS,MAAMQ,GACb1C,EACA,GAAG3C,MAAkBZ,IACrBwE,CACd,EACY,MAAMsB,EAAU,MAAMD,GAAeJ,CAAM,EAC3CjF,EAAIsF,CAAO,CACZ,OAAQpF,EAAP,CACA,QAAQ,MAAMA,CAAC,EACX6D,GACFA,EAAgB,CACd,OAAQ,QACR,QAAS,6BACT,YAAa,QACb,OAAQ,WACxB,CAAe,CAEJ,CACJ,CACD,GAAI,CACFkB,EAAS,MAAMQ,GACb1C,EACA,GAAG3C,MAAkBZ,IACrBwE,CACV,EACQ,MAAMsB,EAAU,MAAMD,GAAeJ,CAAM,EACrCS,EAAY,IAAI,YACpB,GAAGT,EAAO,kBAAkBR,GACtC,EACQzE,EAAIsF,CAAO,CACZ,OAAQpF,EAAP,CACA,QAAQ,MAAMA,CAAC,EACXU,EACF+E,GACE/E,EACAnB,GAAc,KAAKmB,CAAQ,EAAI,aAAe,YAC9C4E,EACZ,EAEczB,GACFA,EAAgB,CACd,OAAQ,QACR,QAAS,6BACT,YAAa,QACb,OAAQ,WACtB,CAAa,CAEN,CACD,SAASG,GAAQ5E,EAAUsG,EAAMC,EAAY,CAC3C,IAAIC,EAAgB,GAChBC,EAAkB,GAClBC,EACJ,GAAI,OAAO1G,GAAa,SACtB0G,EAAaf,EAAO,aAAa3F,CAAQ,MACpC,CACL,MAAM2G,EAAmB3G,EAAS,QAAQ,MAAO,EAAE,EACnD0G,EAAaf,EAAO,aAAaC,EAAQe,CAAgB,CAAC,EAE5D,GAAID,EAAW,MAAM,WACnB,MAAM,IAAI,MACR,gFACZ,EAEQ,OAAO,IAAI,QAAQ,CAACE,EAAMC,IAAQ,CAChC,MAAMC,EAAMjC,GAAO7E,EAAUsG,EAAMC,CAAU,EAC7C,IAAIQ,EACJD,EAAI,GAAG,OAASE,GAAM,CAChBP,IACFK,EAAI,QAAO,EACXF,EAAKI,CAAC,GAERR,EAAgB,GAChBO,EAASC,CACV,GAAE,GAAG,SAAW/C,GAAW,CACtBA,EAAO,QAAU,SACnB4C,EAAI5C,CAAM,EACRA,EAAO,QAAU,aACnBwC,EAAkB,GACdD,IACFM,EAAI,QAAO,EACXF,EAAKG,CAAM,GAG3B,CAAW,CACX,CAAS,CACF,CACD,SAASlC,GAAO7E,EAAUsG,EAAMC,EAAYU,EAAa,KAAM,CAC7D,IAAIC,EACAC,EACJ,GAAI,OAAOnH,GAAa,SACtBkH,EAAWlH,EACXmH,EAAWlB,EAAI,kBAAkBiB,CAAQ,MACpC,CACL,MAAMP,EAAmB3G,EAAS,QAAQ,MAAO,EAAE,EACnDkH,EAAWtB,EAAQe,CAAgB,EACnCQ,EAAWlB,EAAI,gBAAgBjG,EAAS,KAAM,GAEhD,GAAI,OAAOkH,GAAa,SACtB,MAAM,IAAI,MACR,2EACZ,EAEQ,IAAIE,EACAC,EACApH,EAAW0F,EAAO,UAAY,KAClC,MAAM2B,EAAY,OAAOtH,GAAa,SAAW,WAAaA,EAC9D,IAAIuH,EACAC,EAAW,KACXC,EAAW,GACf,MAAMC,EAAe,GACrB,IAAIC,EAAa,GACb,OAAO,OAAW,MACpBA,EAAa,IAAI,gBAAgB,OAAO,SAAS,MAAM,EAAE,YAE3D7D,EAAa,GAAG6B,EAAO,OAAQW,EAAMa,EAAUzC,CAAQ,EAAE,KACtDkD,GAAa,CAOZ,GANAL,EAAU,CACR,KAAMK,GAAY,CAAE,EACpB,WAAArB,EACA,SAAAW,EACA,WAAAD,CACd,EACgBY,GAAWX,EAAUvB,CAAM,EAC7BmC,EAAW,CACT,KAAM,SACN,SAAUR,EACV,MAAO,UACP,MAAO,GACP,SAAAJ,EACA,KAAsB,IAAI,IAC1C,CAAe,EACDvD,EACE,GAAGgC,EAAO,WAAW2B,EAAU,WAAW,GAAG,EAAIA,EAAY,IAAIA,MAAcK,EAAa,IAAMA,EAAa,KAC/G,CACE,GAAGJ,EACH,aAAApC,CACD,EACDT,CACD,EAAC,KAAK,CAAC,CAACV,EAAQ+D,CAAW,IAAM,CAChC,MAAMC,EAAQhE,EAAO,KACjB+D,GAAe,KACjBD,EAAW,CACT,KAAM,OACN,SAAUR,EACV,SAAAJ,EACA,KAAMc,EACN,KAAsB,IAAI,IAC9C,CAAmB,EACDF,EAAW,CACT,KAAM,SACN,SAAUR,EACV,SAAAJ,EACA,MAAO,WACP,IAAKlD,EAAO,iBACZ,MAAO,GACP,KAAsB,IAAI,IAC9C,CAAmB,GAED8D,EAAW,CACT,KAAM,SACN,MAAO,QACP,SAAUR,EACV,SAAAJ,EACA,QAASlD,EAAO,MAChB,MAAO,GACP,KAAsB,IAAI,IAC9C,CAAmB,CAEnB,CAAe,EAAE,MAAOpD,GAAM,CACdkH,EAAW,CACT,KAAM,SACN,MAAO,QACP,QAASlH,EAAE,QACX,SAAU0G,EACV,SAAAJ,EACA,MAAO,GACP,KAAsB,IAAI,IAC5C,CAAiB,CACjB,CAAe,UACQjH,GAAY,KAAM,CAC3B6H,EAAW,CACT,KAAM,SACN,MAAO,UACP,MAAO,GACP,SAAUR,EACV,SAAAJ,EACA,KAAsB,IAAI,IAC1C,CAAe,EACD,IAAInE,EAAM,IAAI,IAAI,GAAGlC,OAAiBlB,GACpCO,EACAyF,EAAO,KACP,EAChB;AAAA,oBACoBgC,EAAa,IAAMA,EAAa,IAAI,EACtC9B,IACF9C,EAAI,aAAa,IAAI,SAAU8C,EAAG,EAEpCuB,EAAY,IAAI,UAAUrE,CAAG,EAC7BqE,EAAU,QAAWa,GAAQ,CACtBA,EAAI,UACPH,EAAW,CACT,KAAM,SACN,MAAO,QACP,OAAQ,GACR,QAASxE,EACT,MAAO,GACP,SAAUgE,EACV,SAAAJ,EACA,KAAsB,IAAI,IAC9C,CAAmB,CAEnB,EACcE,EAAU,UAAY,SAASc,EAAO,CACpC,MAAMC,EAAQ,KAAK,MAAMD,EAAM,IAAI,EAC7B,CAAE,KAAAE,EAAM,OAAAnE,EAAQ,KAAM+D,CAAO,EAAGK,GACpCF,EACA/C,EAAY8B,CAAQ,CACtC,EACgB,GAAIkB,IAAS,UAAYnE,GAAU,CAACwD,EAClCK,EAAW,CACT,KAAM,SACN,SAAUR,EACV,SAAAJ,EACA,KAAsB,IAAI,KAC1B,GAAGjD,CACvB,CAAmB,EACGA,EAAO,QAAU,SACnBmD,EAAU,MAAK,UAERgB,IAAS,OAAQ,CAC1BhB,EAAU,KAAK,KAAK,UAAU,CAAE,SAAAF,EAAU,aAAA/B,CAAc,EAAC,EACzD,YACSiD,IAAS,OAClBhB,EAAU,KAAK,KAAK,UAAU,CAAE,GAAGG,EAAS,aAAApC,CAAc,EAAC,EAClDiD,IAAS,WAClBX,EAAWxD,EACFmE,IAAS,MAClBN,EAAW,CACT,KAAM,MACN,IAAKE,EAAM,IACX,MAAOA,EAAM,MACb,SAAUV,EACV,SAAAJ,CACpB,CAAmB,EACQkB,IAAS,cAClBN,EAAW,CACT,KAAM,SACN,KAAsB,IAAI,KAC1B,GAAG7D,EACH,MAAiCA,GAAO,MACxC,MAAO,GACP,SAAUqD,EACV,SAAAJ,CACpB,CAAmB,EAECc,IACFF,EAAW,CACT,KAAM,OACN,KAAsB,IAAI,KAC1B,KAAME,EAAM,KACZ,SAAUV,EACV,SAAAJ,CACpB,CAAmB,EACGO,IACFK,EAAW,CACT,KAAM,SACN,KAAsB,IAAI,KAC1B,GAAGL,EACH,MAAiCxD,GAAO,MACxC,MAAO,GACP,SAAUqD,EACV,SAAAJ,CACtB,CAAqB,EACDE,EAAU,MAAK,GAGnC,EACkB7H,GAAQoG,EAAO,SAAW,QAAS,KAAK,EAAI,GAC9C,iBACE,OACA,IAAMyB,EAAU,KAAK,KAAK,UAAU,CAAE,KAAMjC,CAAY,CAAE,CAAC,CAC7E,UAEuBlF,GAAY,MAAO,CAC5B6H,EAAW,CACT,KAAM,SACN,MAAO,UACP,MAAO,GACP,SAAUR,EACV,SAAAJ,EACA,KAAsB,IAAI,IAC1C,CAAe,EACD,IAAIoB,EAAS,IAAI,gBAAgB,CAC/B,SAAUpB,EAAS,SAAU,EAC7B,aAAA/B,CAChB,CAAe,EAAE,SAAQ,EACX,IAAIpC,EAAM,IAAI,IACZ,GAAG4C,EAAO,mBAAmBgC,EAAaA,EAAa,IAAM,KAAKW,GAClF,EACcjB,EAAc3D,EAAoBX,CAAG,EACrCsE,EAAY,UAAY,eAAea,EAAO,CAC5C,MAAMC,EAAQ,KAAK,MAAMD,EAAM,IAAI,EAC7B,CAAE,KAAAE,EAAM,OAAAnE,EAAQ,KAAM+D,CAAO,EAAGK,GACpCF,EACA/C,EAAY8B,CAAQ,CACtC,EACgB,GAAIkB,IAAS,UAAYnE,GAAU,CAACwD,EAClCK,EAAW,CACT,KAAM,SACN,SAAUR,EACV,SAAAJ,EACA,KAAsB,IAAI,KAC1B,GAAGjD,CACvB,CAAmB,EACGA,EAAO,QAAU,SACnBoD,EAAY,MAAK,UAEVe,IAAS,OAAQ,CAC1BZ,EAAWW,EAAM,SACjB,GAAI,CAACI,EAAGC,EAAO,EAAI,MAAM7E,EACvB,GAAGgC,EAAO,kBACV,CACE,GAAG4B,EACH,aAAApC,EACA,SAAAqC,CACD,EACD9C,CACpB,EACsB8D,KAAY,MACdV,EAAW,CACT,KAAM,SACN,MAAO,QACP,QAASxE,EACT,MAAO,GACP,SAAUgE,EACV,SAAAJ,EACA,KAAsB,IAAI,IAChD,CAAqB,EACDG,EAAY,MAAK,QAEVe,IAAS,WAClBX,EAAWxD,EACFmE,IAAS,MAClBN,EAAW,CACT,KAAM,MACN,IAAKE,EAAM,IACX,MAAOA,EAAM,MACb,SAAUV,EACV,SAAAJ,CACpB,CAAmB,EACQkB,IAAS,cAClBN,EAAW,CACT,KAAM,SACN,KAAsB,IAAI,KAC1B,GAAG7D,EACH,MAAiCA,GAAO,MACxC,MAAO,GACP,SAAUqD,EACV,SAAAJ,CACpB,CAAmB,EAECc,IACFF,EAAW,CACT,KAAM,OACN,KAAsB,IAAI,KAC1B,KAAME,EAAM,KACZ,SAAUV,EACV,SAAAJ,CACpB,CAAmB,EACGO,IACFK,EAAW,CACT,KAAM,SACN,KAAsB,IAAI,KAC1B,GAAGL,EACH,MAAiCxD,GAAO,MACxC,MAAO,GACP,SAAUqD,EACV,SAAAJ,CACtB,CAAqB,EACDG,EAAY,MAAK,GAGrC,OACuBpH,GAAY,UAAYA,GAAY,UAAYA,GAAY,YAAcA,GAAY,YAC/F6H,EAAW,CACT,KAAM,SACN,MAAO,UACP,MAAO,GACP,SAAUR,EACV,SAAAJ,EACA,KAAsB,IAAI,IAC1C,CAAe,EACDvD,EACE,GAAGgC,EAAO,mBAAmBgC,IAC7B,CACE,GAAGJ,EACH,aAAApC,CACD,EACDT,CACD,EAAC,KAAK,CAAC,CAAChC,EAAUuB,CAAM,IAAM,CAC7B,GAAIA,IAAW,IACb6D,EAAW,CACT,KAAM,SACN,MAAO,QACP,QAASzE,GACT,MAAO,GACP,SAAUiE,EACV,SAAAJ,EACA,KAAsB,IAAI,IAC9C,CAAmB,UACQjD,IAAW,IACpB6D,EAAW,CACT,KAAM,SACN,MAAO,QACP,QAASxE,EACT,MAAO,GACP,SAAUgE,EACV,SAAAJ,EACA,KAAsB,IAAI,IAC9C,CAAmB,MACI,CACLM,EAAW9E,EAAS,SACpB,IAAI+F,EAAW,eAAeN,EAAO,CACnC,GAAI,CACF,KAAM,CAAE,KAAAC,EAAM,OAAQI,EAAS,KAAMR,CAAK,EAAKK,GAC7CF,EACA/C,EAAY8B,CAAQ,CAC5C,EACsB,GAAIkB,GAAQ,YACV,OAEF,GAAIA,IAAS,UAAYI,GAAW,CAACf,EACnCK,EAAW,CACT,KAAM,SACN,SAAUR,EACV,SAAAJ,EACA,KAAsB,IAAI,KAC1B,GAAGsB,CAC7B,CAAyB,UACQJ,IAAS,WAClBX,EAAWe,UACFJ,GAAQ,mBACjB,QAAQ,MAAM,mBAA+CI,GAAQ,OAAO,EAC5EV,EAAW,CACT,KAAM,SACN,MAAO,QACP,QAAqCU,GAAQ,SAAY,gCACzD,MAAO,GACP,SAAUlB,EACV,SAAAJ,EACA,KAAsB,IAAI,IACpD,CAAyB,UACQkB,IAAS,MAAO,CACzBN,EAAW,CACT,KAAM,MACN,IAAKE,EAAM,IACX,MAAOA,EAAM,MACb,SAAUV,EACV,SAAAJ,CAC1B,CAAyB,EACD,YACSkB,IAAS,eAClBN,EAAW,CACT,KAAM,SACN,KAAsB,IAAI,KAC1B,GAAGU,EACH,MAAkCA,GAAQ,MAC1C,MAAO,GACP,SAAUlB,EACV,SAAAJ,CAC1B,CAAyB,EACGc,GAAS,CAAC,SAAU,WAAY,QAAQ,EAAE,SAAS/H,CAAQ,GAC7DyI,GAAkBlB,EAAUQ,CAAK,GAGjCA,IACFF,EAAW,CACT,KAAM,OACN,KAAsB,IAAI,KAC1B,KAAME,EAAM,KACZ,SAAUV,EACV,SAAAJ,CAC1B,CAAyB,EACGO,GACFK,EAAW,CACT,KAAM,SACN,KAAsB,IAAI,KAC1B,GAAGL,EACH,MAAkCe,GAAQ,MAC1C,MAAO,GACP,SAAUlB,EACV,SAAAJ,CAC5B,CAA2B,IAG2BsB,GAAQ,QAAW,YAA0CA,GAAQ,QAAW,WAC1G/C,EAAgB+B,CAAQ,GAC1B,OAAO/B,EAAgB+B,CAAQ,EAE7BA,KAAYjC,GACd,OAAOA,EAAqBiC,CAAQ,EAGzC,OAAQ5G,EAAP,CACA,QAAQ,MAAM,8BAA+BA,CAAC,EAC9CkH,EAAW,CACT,KAAM,SACN,MAAO,QACP,QAAS,gCACT,MAAO,GACP,SAAUR,EACV,SAAAJ,EACA,KAAsB,IAAI,IAClD,CAAuB,EACG,CAAC,SAAU,UAAU,EAAE,SAASjH,CAAQ,GAC1C0I,GAEH,CACrB,EACsBnB,KAAYlC,IACdA,EAAwBkC,CAAQ,EAAE,QAC/BoB,GAAQH,EAASG,CAAG,CAC3C,EACoB,OAAOtD,EAAwBkC,CAAQ,GAEzC/B,EAAgB+B,CAAQ,EAAIiB,EAC5B/C,GAAgB,IAAI8B,CAAQ,EACvBnC,GACHwD,KAGpB,CAAe,EAEJ,CACX,EACQ,SAASH,GAAkBI,EAAWd,EAAO,CACjB,CAACzC,EAAqBuD,CAAS,GAEvDvD,EAAqBuD,CAAS,EAAI,GAClCd,EAAM,KAAK,QAAQ,CAACpG,EAAOT,IAAM,CAC/BoE,EAAqBuD,CAAS,EAAE3H,CAAC,EAAIS,CACnD,CAAa,GAEDoG,EAAM,KAAK,QAAQ,CAACpG,EAAOT,IAAM,CAC/B,IAAI4H,EAAWhH,GACbwD,EAAqBuD,CAAS,EAAE3H,CAAC,EACjCS,CAChB,EACc2D,EAAqBuD,CAAS,EAAE3H,CAAC,EAAI4H,EACrCf,EAAM,KAAK7G,CAAC,EAAI4H,CAC9B,CAAa,CAEJ,CACD,SAASjB,EAAWI,EAAO,CAEzB,MAAMc,EADwBtB,EACUQ,EAAM,IAAI,GAAK,GAC1Bc,GAAU,QAASC,GAAMA,EAAEf,CAAK,CAAC,CAC/D,CACD,SAASgB,GAAGC,EAAWC,EAAU,CAC/B,MAAMC,EAAwB3B,EACxBsB,EAAYK,EAAsBF,CAAS,GAAK,GACtD,OAAAE,EAAsBF,CAAS,EAAIH,EACNA,GAAU,KAAKI,CAAQ,EAC7C,CAAE,GAAAF,GAAI,IAAAI,EAAK,OAAAC,GAAQ,QAAAC,EAAO,CAClC,CACD,SAASF,EAAIH,EAAWC,EAAU,CAChC,MAAMC,EAAwB3B,EAC9B,IAAIsB,EAAYK,EAAsBF,CAAS,GAAK,GACpD,OAAAH,EAAyCA,GAAU,OAAQC,GAAMA,IAAMG,CAAQ,EAC/EC,EAAsBF,CAAS,EAAIH,EAC5B,CAAE,GAAAE,GAAI,IAAAI,EAAK,OAAAC,GAAQ,QAAAC,EAAO,CAClC,CACD,eAAeD,IAAS,CACtB,MAAME,EAAU,CACd,MAAO,WACP,MAAO,GACP,KAAsB,IAAI,IACtC,EACUhC,EAAWgC,EACX3B,EAAW,CACT,GAAG2B,EACH,KAAM,SACN,SAAUnC,EACV,SAAAJ,CACZ,CAAW,EACD,IAAIwC,EAAiB,GACjBzJ,IAAa,MACXmH,GAAaA,EAAU,aAAe,EACxCA,EAAU,iBAAiB,OAAQ,IAAM,CACvCA,EAAU,MAAK,CAC/B,CAAe,EAEDA,EAAU,MAAK,EAEjBsC,EAAiB,CAAE,SAAAxC,EAAU,aAAA/B,KAE7BkC,EAAY,MAAK,EACjBqC,EAAiB,CAAE,SAAAlC,IAErB,GAAI,CACF,MAAM/D,EAAqB,GAAGkC,EAAO,aAAc,CACjD,QAAS,CAAE,eAAgB,kBAAoB,EAC/C,OAAQ,OACR,KAAM,KAAK,UAAU+D,CAAc,CACjD,CAAa,CACF,MAAC,CACA,QAAQ,KACN,2FACd,CACW,CACF,CACD,SAASF,IAAU,CACjB,UAAWG,KAAcjC,EACvBA,EAAaiC,CAAU,EAAE,QAASC,GAAQ,CACxCN,EAAIK,EAAYC,CAAG,CACjC,CAAa,CAEJ,CACD,MAAO,CACL,GAAAV,GACA,IAAAI,EACA,OAAAC,GACA,QAAAC,EACV,CACO,CACD,SAASX,IAAc,CACrBxD,EAAc,GACd,IAAIiD,EAAS,IAAI,gBAAgB,CAC/B,aAAAnD,CACV,CAAS,EAAE,SAAQ,EACPpC,EAAM,IAAI,IAAI,GAAG4C,EAAO,mBAAmB2C,GAAQ,EACvD9C,EAAe9B,EAAoBX,CAAG,EACtCyC,EAAa,UAAY,eAAe0C,EAAO,CAC7C,IAAIC,EAAQ,KAAK,MAAMD,EAAM,IAAI,EACjC,MAAMV,EAAWW,EAAM,SACvB,GAAI,CAACX,EACH,MAAM,QAAQ,IACZ,OAAO,KAAK/B,CAAe,EAAE,IAC1BqD,GAAcrD,EAAgBqD,CAAS,EAAEX,CAAK,CAChD,CACf,UACqB1C,EAAgB+B,CAAQ,EAAG,CAChCW,EAAM,MAAQ,qBAAuB,CAAC,MAAO,SAAU,SAAU,UAAU,EAAE,SAASxC,EAAO,QAAQ,IACvGD,GAAgB,OAAO8B,CAAQ,EAC3B9B,GAAgB,OAAS,GAC3BiD,KAGJ,IAAIiB,EAAMnE,EAAgB+B,CAAQ,EAClC,OAAO,WAAWoC,EAAK,EAAGzB,CAAK,OAE1B7C,EAAwBkC,CAAQ,IACnClC,EAAwBkC,CAAQ,EAAI,IAEtClC,EAAwBkC,CAAQ,EAAE,KAAKW,CAAK,EAE1CA,EAAM,MAAQ,gBAChBQ,GAEZ,EACQnD,EAAa,QAAU,eAAe0C,EAAO,CAC3C,MAAM,QAAQ,IACZ,OAAO,KAAKzC,CAAe,EAAE,IAC1B+B,GAAa/B,EAAgB+B,CAAQ,EAAE,CACtC,IAAK,mBACL,QAASlE,CACzB,CAAe,CACF,CACb,EACUqF,GACV,CACO,CACD,SAASA,GAAe,CACtBtD,EAAc,GACkBG,GAAa,MAAK,CACnD,CACD,eAAeT,GAAiB8E,EAAcC,EAASxD,EAAM,CAC3D,IAAIyD,EACJ,MAAMvJ,EAAU,CAAE,eAAgB,oBAC9BkE,IACFlE,EAAQ,cAAgB,UAAUkE,KAEpC,IAAIsF,EACAC,EAAYtE,EAAO,WAAW,KAC/BuE,GAASA,EAAK,KAAOL,CAChC,GACaE,EAAkCE,GAAU,QAAU,MAAgBF,EAAG,SAC5EC,EAAWC,EAAU,MAAM,SAE3BD,EAAWrE,EAAO,KAEpB,MAAMjD,EAAW,MAAMe,EACrB,GAAGuG,sBACH,CACE,OAAQ,OACR,KAAM,KAAK,UAAU,CACnB,KAAA1D,EACA,aAAAuD,EACA,QAAAC,EACA,aAAA3E,CACd,CAAa,EACD,QAAA3E,CACD,CACX,EACQ,GAAI,CAACkC,EAAS,GACZ,MAAM,IAAI,MACR,0CAA4CA,EAAS,UACjE,EAGQ,OADe,MAAMA,EAAS,MAE/B,CACD,eAAeoC,GAASqF,EAAS,CAC/B,GAAIlE,EACF,OAAOA,EACT,MAAMzF,EAAU,CAAE,eAAgB,oBAC9BkE,IACFlE,EAAQ,cAAgB,UAAUkE,KAEpC,IAAIhC,EAkBJ,GAjBInD,GAAQ4K,EAAQ,SAAW,QAAS,MAAM,EAAI,EAChDzH,EAAW,MAAMe,EACf,mDACA,CACE,OAAQ,OACR,KAAM,KAAK,UAAU,CACnB,UAAW,GACX,OAAQ,KAAK,UAAU0G,CAAO,CAC9C,CAAe,EACD,QAAA3J,CACD,CACb,EAEUkC,EAAW,MAAMe,EAAqB,GAAG0G,EAAQ,YAAa,CAC5D,QAAA3J,CACZ,CAAW,EAEC,CAACkC,EAAS,GACZ,MAAM,IAAI,MAAMY,CAAqB,EAEvC,IAAI6D,EAAW,MAAMzE,EAAS,OAC9B,MAAI,QAASyE,IACXA,EAAWA,EAAS,KAElBA,EAAS,gBAAgB,UAAU,GAAK,CAACA,EAAS,kBAAkB,CAAG,IACzEA,EAAS,kBAAkB,CAAC,EAAIA,EAAS,gBAAgB,UAAU,GAE3DiD,GAAmBjD,EAAUgD,EAASvE,CAAO,CAExD,CACP,CAAK,CACF,CACD,eAAe9B,EAAa9D,EAAUsG,EAAMa,EAAU5G,EAAO,CAC3D,MAAM8J,EAAY,MAAMC,GACtBhE,EACA,OACA,CAAE,EACF,GACAa,CACN,EACI,OAAO,QAAQ,IACbkD,EAAU,IAAI,MAAO,CAAE,KAAA3I,EAAM,KAAAwB,EAAM,KAAAkF,CAAI,IAAO,CAC5C,GAAIlF,EAAM,CACR,MAAMqH,GAAY,MAAM3G,EAAc5D,EAAU,CAACkD,CAAI,EAAG3C,CAAK,GAAG,MAAM,CAAC,EACvE,MAAO,CAAE,KAAAmB,EAAM,SAAA6I,EAAU,KAAAnC,EAAM,KAA8BlF,GAAK,IAAI,EAExE,MAAO,CAAE,KAAAxB,EAAM,KAAA0G,EACvB,CAAO,CACP,EAAM,KAAMoC,IACNA,EAAE,QAAQ,CAAC,CAAE,KAAA9I,EAAM,SAAA6I,EAAU,KAAAnC,EAAM,KAAAqC,KAAW,CAC5C,GAAIrC,IAAS,UACXsC,GAAcpE,EAAMiE,EAAU7I,CAAI,UACzB6I,EAAU,CACnB,MAAMjG,EAAO,IAAI1B,GAAS,CAAE,KAAM2H,EAAU,UAAWE,CAAI,CAAE,EAC7DC,GAAcpE,EAAMhC,EAAM5C,CAAI,EAExC,CAAO,EACM4E,EACR,CACF,CACH,CACA,KAAM,CAAE,UAAAqE,GAAW,aAAApI,GAAc,OAAAqI,GAAQ,YAAAC,EAAa,EAAGrH,GACvD,MACA,IAAIsH,IAAS,IAAI,YAAY,GAAGA,CAAI,CACtC,EACA,SAASC,GAAS3C,EAAM6B,EAAWe,EAAYC,EAAgB,CAC7D,OAAQ7C,EAAK,KAAI,CACf,IAAK,SACH,MAAO,SACT,IAAK,UACH,MAAO,UACT,IAAK,SACH,MAAO,QACV,CACD,GAAI4C,IAAe,oBAAsBA,IAAe,qBACtD,MAAO,MACF,GAAIA,IAAe,yBACxB,MAAO,WACF,GAAIf,IAAc,QACvB,OAAOgB,IAAmB,YAAc,uBAAyB,SAC5D,GAAID,IAAe,mBACxB,OAA6B5C,GAAK,OAAU,QACnC6C,IAAmB,YAAc,2BAA6B,wFAEhEA,IAAmB,YAAc,uBAAyB,sFAC5D,GAAID,IAAe,sBACxB,OAAOC,IAAmB,YAAc,8CAAgD,2GAE5F,CACA,SAASC,GAAgB9C,EAAM4C,EAAY,CACzC,OAAIA,IAAe,sBACV,gCACEA,IAAe,yBACjB,mBACEA,IAAe,mBACjB,gCAEF5C,EAAK,WACd,CACA,SAASgC,GAAmBjD,EAAUxB,EAAQC,EAAS,CACrD,MAAMmD,EAAW,CACf,gBAAiB,CAAE,EACnB,kBAAmB,CAAE,CACzB,EACE,UAAWoC,KAAOhE,EAAU,CAC1B,MAAMiE,EAAMjE,EAASgE,CAAG,EACxB,UAAWnL,KAAYoL,EAAK,CAC1B,MAAMC,EAAY1F,EAAO,aAAa3F,CAAQ,EAAIA,EAAW4F,EAAQ5F,EAAS,QAAQ,IAAK,EAAE,CAAC,EACxFsL,EAAOF,EAAIpL,CAAQ,EACzB+I,EAASoC,CAAG,EAAEnL,CAAQ,EAAI,GAC1B+I,EAASoC,CAAG,EAAEnL,CAAQ,EAAE,WAAa,GACrC+I,EAASoC,CAAG,EAAEnL,CAAQ,EAAE,QAAU,GAClC+I,EAASoC,CAAG,EAAEnL,CAAQ,EAAE,KAAO2F,EAAO,aAAa0F,CAAS,EAAE,MAC9DtC,EAASoC,CAAG,EAAEnL,CAAQ,EAAE,WAAasL,EAAK,WAAW,IACnD,CAAC,CAAE,MAAAC,EAAO,UAAAtB,EAAW,KAAA7B,EAAM,WAAA4C,CAAU,KAAQ,CAC3C,MAAAO,EACA,UAAAtB,EACA,KAAMc,GAAS3C,EAAM6B,EAAWe,EAAY,WAAW,EACvD,YAAaE,GAAgB9C,EAAM4C,CAAU,CACvD,EACA,EACMjC,EAASoC,CAAG,EAAEnL,CAAQ,EAAE,QAAUsL,EAAK,QAAQ,IAC7C,CAAC,CAAE,MAAAC,EAAO,UAAAtB,EAAW,KAAA7B,EAAM,WAAA4C,CAAU,KAAQ,CAC3C,MAAAO,EACA,UAAAtB,EACA,KAAMc,GAAS3C,EAAM6B,EAAWe,EAAY,QAAQ,EACpD,YAAaE,GAAgB9C,EAAM4C,CAAU,CACvD,EACA,GAGE,OAAOjC,CACT,CACA,eAAejD,GAAQ0F,EAAOjL,EAAO,CACnC,GAAI,CAOF,OADa,MALH,MAAM,MAAM,qCAAqCiL,QAAa,CACtE,QAAS,CACP,cAAe,UAAUjL,GAC1B,CACP,CAAK,GACoB,KAAI,GAAI,OACf,EACf,OAAQK,EAAP,CACA,eAAQ,MAAMA,CAAC,EACR,EACR,CACH,CACA,SAAS8J,GAAce,EAAQC,EAAUC,EAAO,CAC9C,KAAOA,EAAM,OAAS,GACpBF,EAASA,EAAOE,EAAM,MAAO,GAE/BF,EAAOE,EAAM,MAAO,GAAID,CAC1B,CACA,eAAepB,GAAqBsB,EAAOxD,EAAO,OAAQ1G,EAAO,CAAE,EAAEU,EAAO,GAAO+E,EAAW,OAAQ,CACpG,GAAI,MAAM,QAAQyE,CAAK,EAAG,CACxB,IAAIvB,EAAY,GAChB,aAAM,QAAQ,IACZuB,EAAM,IAAI,MAAOC,EAAG1K,IAAM,CACxB,IAAI4I,EACJ,IAAI+B,EAAWpK,EAAK,QACpBoK,EAAS,KAAK3K,CAAC,EACf,MAAM4K,EAAa,MAAMzB,GACvBsB,EAAMzK,CAAC,EACPiB,IAAS2H,EAAiC5C,GAAS,WAAWhG,CAAC,IAAM,KAAO,OAAS4I,EAAG,YAAc,OAAS3B,EAC/G0D,EACA,GACA3E,CACV,EACQkD,EAAYA,EAAU,OAAO0B,CAAU,CAC/C,CAAO,CACP,EACW1B,MACF,IAAI,WAAW,QAAUuB,aAAiB,WAAW,OAE1D,MAAO,CACL,CACE,KAAAlK,EACA,KAJa0G,IAAS,QAIL,GAAQ,IAAI7E,GAAS,CAACqI,CAAK,CAAC,EAC7C,KAAAxD,CACD,CACP,EACS,GAAI,OAAOwD,GAAU,SAAU,CACpC,IAAIvB,EAAY,GAChB,QAASc,KAAOS,EACd,GAAIA,EAAM,eAAeT,CAAG,EAAG,CAC7B,IAAIW,EAAWpK,EAAK,QACpBoK,EAAS,KAAKX,CAAG,EACjBd,EAAYA,EAAU,OACpB,MAAMC,GACJsB,EAAMT,CAAG,EACT,OACAW,EACA,GACA3E,CACD,CACX,EAGI,OAAOkD,GAET,MAAO,EACT,CACA,SAASxC,GAAWmE,EAAIrG,EAAQ,CAC9B,IAAIoE,EAAIkC,EAAIC,EAAIC,EAChB,MAAO,IAAIF,GAAMlC,EAA+BpE,GAAO,eAAiB,KAAO,OAASoE,EAAGiC,CAAE,IAAM,KAAO,OAASC,EAAG,SAAW,KAAOtG,EAAO,cAAgBwG,GAAMD,EAA+BvG,GAAO,eAAiB,KAAO,OAASuG,EAAGF,CAAE,IAAM,MAAgBG,EAAG,QAAU,EACtR,CACA,eAAehG,GAAe1C,EAAsBzD,EAAUO,EAAO,CACnE,MAAMC,EAAU,GAIhB,GAHID,IACFC,EAAQ,cAAgB,UAAUD,KAEhC,OAAO,OAAW,KAAe,OAAO,eAAiB,SAAS,SAAW,yBAA2B,CAAC,OAAO,cAAc,SAAU,CAC1I,MAAMmB,EAAO,OAAO,cAAc,KAC5BiE,EAAS,OAAO,cACtB,OAAAA,EAAO,KAAOhG,GAAaK,EAAU2F,EAAO,KAAM,EAAK,EAChD,CAAE,GAAGA,EAAQ,KAAAjE,WACX1B,EAAU,CACnB,IAAI0C,EAAW,MAAMe,EAAqB,GAAGzD,WAAmB,CAC9D,QAAAQ,CACN,CAAK,EACD,GAAIkC,EAAS,SAAW,IAAK,CAC3B,MAAMiD,EAAS,MAAMjD,EAAS,OAC9B,OAAAiD,EAAO,KAAOA,EAAO,MAAQ,GAC7BA,EAAO,KAAO3F,EACP2F,EAET,MAAM,IAAI,MAAM,uBAAuB,EAEzC,MAAM,IAAI,MAAM,iCAAiC,CACnD,CACA,eAAeU,GAAmB2F,EAAI5D,EAAM3D,EAAiB,CAC3D,IAAIzE,EAAWoI,IAAS,YAAc,kDAAkD4D,IAAO,qCAAqCA,IAChItJ,EACA+G,EACJ,GAAI,CAGF,GAFA/G,EAAW,MAAM,MAAM1C,CAAQ,EAC/ByJ,EAAU/G,EAAS,OACf+G,IAAY,IACd,MAAM,IAAI,MAEZ/G,EAAW,MAAMA,EAAS,MAC3B,MAAC,CACA+B,EAAgB,CACd,OAAQ,QACR,YAAa,QACb,QAAS,6BACT,OAAQ,WACd,CAAK,EACD,MACD,CACD,GAAI,CAAC/B,GAAY+G,IAAY,IAC3B,OACF,KAAM,CACJ,QAAS,CAAE,MAAA2C,CAAO,EAClB,GAAIC,CACL,EAAG3J,EACJ,OAAQ0J,EAAK,CACX,IAAK,UACL,IAAK,WACH3H,EAAgB,CACd,OAAQ,WACR,YAAa,UACb,QAAS,mCACT,OAAQ2H,CAChB,CAAO,EACD,WAAW,IAAM,CACf/F,GAAmB2F,EAAI5D,EAAM3D,CAAe,CAC7C,EAAE,GAAG,EACN,MACF,IAAK,SACHA,EAAgB,CACd,OAAQ,SACR,YAAa,QACb,QAAS,gHACT,OAAQ2H,EACR,oBAAqB,MAAM/K,GAAoBgL,CAAU,CACjE,CAAO,EACD,MACF,IAAK,UACL,IAAK,mBACH5H,EAAgB,CACd,OAAQ,UACR,YAAa,WACb,QAAS,GACT,OAAQ2H,CAChB,CAAO,EACD,MACF,IAAK,WACH3H,EAAgB,CACd,OAAQ,WACR,YAAa,UACb,QAAS,uBACT,OAAQ2H,CAChB,CAAO,EACD,WAAW,IAAM,CACf/F,GAAmB2F,EAAI5D,EAAM3D,CAAe,CAC7C,EAAE,GAAG,EACN,MACF,QACEA,EAAgB,CACd,OAAQ,cACR,YAAa,QACb,QAAS,uCACT,OAAQ2H,EACR,oBAAqB,MAAM/K,GAAoBgL,CAAU,CACjE,CAAO,EACD,KACH,CACH,CACA,SAAShE,GAAe/B,EAAMlB,EAAa,CAEzC,OAAQkB,EAAK,IAAG,CACd,IAAK,YACH,MAAO,CAAE,KAAM,QACjB,IAAK,YACH,MAAO,CAAE,KAAM,QACjB,IAAK,aACH,MAAO,CACL,KAAM,SACN,OAAQ,CACN,SACA,QAASjD,GACT,MAAO,QACP,KAAMiD,EAAK,KACX,QAASA,EAAK,OACf,CACT,EACI,IAAK,YACH,MAAO,CACL,KAAM,WACd,EACI,IAAK,mBACH,MAAO,CACL,KAAM,mBACN,OAAQ,CACN,SACA,QAASA,EAAK,QACd,MAAO,QACP,QAAS,EACV,CACT,EACI,IAAK,aACH,MAAO,CACL,KAAM,SACN,OAAQ,CACN,SACA,MAAOlB,GAAe,UACtB,KAAMkB,EAAK,KACX,KAAMA,EAAK,WACX,SAAUA,EAAK,KACf,IAAKA,EAAK,SACV,QAASA,EAAK,OACf,CACT,EACI,IAAK,WACH,MAAO,CACL,KAAM,SACN,OAAQ,CACN,SACA,MAAO,UACP,KAAMA,EAAK,KACX,cAAeA,EAAK,cACpB,QAASA,EAAK,OACf,CACT,EACI,IAAK,MACH,MAAO,CAAE,KAAM,MAAO,KAAAA,GACxB,IAAK,qBACH,MAAO,CACL,KAAM,aACN,OAAQ,CACN,SACA,QAAUA,EAAK,QAA8B,KAApBA,EAAK,OAAO,MACrC,MAAOA,EAAK,QAAU,aAAe,QACrC,KAAMA,EAAK,KACX,cAAeA,EAAK,cACpB,IAAKA,EAAK,gBACX,EACD,KAAMA,EAAK,QAAUA,EAAK,OAAS,IAC3C,EACI,IAAK,oBACH,MAAI,UAAWA,EAAK,OACX,CACL,KAAM,SACN,OAAQ,CACN,SACA,QAASA,EAAK,OAAO,MACrB,MAAO,QACP,KAAMA,EAAK,KACX,QAASA,EAAK,OACf,CACX,EAEa,CACL,KAAM,WACN,OAAQ,CACN,SACA,QAAUA,EAAK,QAA8B,OAApBA,EAAK,OAAO,MACrC,MAAOA,EAAK,QAAU,WAAa,QACnC,KAAMA,EAAK,KACX,cAAeA,EAAK,aACrB,EACD,KAAMA,EAAK,QAAUA,EAAK,OAAS,IAC3C,EACI,IAAK,iBACH,MAAO,CACL,KAAM,SACN,OAAQ,CACN,SACA,MAAO,UACP,KAAMA,EAAK,KACX,KAAMA,EAAK,KACX,SAAU,EACV,QAASA,EAAK,QACd,IAAKA,EAAK,GACX,CACT,CACG,CACD,MAAO,CAAE,KAAM,OAAQ,OAAQ,CAAE,MAAO,QAAS,QAAK,EACxD,CC7iDA,IAAIgG,GAA+B,GAGlC,iBAAkB,QAAQ,WAC1B,uBAAwB,SAAS,YAMjCA,GAA+B,uBAHN,SACvB,cAAc,KAAK,EACnB,aAAa,CAAE,KAAM,OAAQ,GAIhB,SAAAC,GAAUxJ,EAAatB,EAAoC,CAC1E,MAAM+K,EAAO,IAAI,IAAI,YAAY,GAAG,EAAE,OAChCC,EAAO,IAAI,IAAI1J,EAAKyJ,CAAI,EAAE,KAG5B,GAFkB,SAAS,cAAc,cAAcC,KAAQ,EAEhD,OAAO,QAAQ,UAE5B,MAAAC,EAAO,SAAS,cAAc,MAAM,EAC1C,OAAAA,EAAK,IAAM,aACXA,EAAK,KAAOD,EAEL,IAAI,QAAQ,CAAC/L,EAAKmG,IAAQ,CAChC6F,EAAK,iBAAiB,OAAQ,IAAMhM,EAAK,GACpCgM,EAAA,iBAAiB,QAAS,IAAM,CAC5B,cAAM,6BAA6BD,GAAM,EAC7C/L,GAAA,CACJ,EACDe,EAAO,YAAYiL,CAAI,EACvB,CACF,CAEO,SAASC,GACfC,EACAC,EACAC,EAAgB,SAAS,cAAc,OAAO,EACpB,CAC1B,GAAI,CAACR,GAAqC,YAC1CQ,EAAc,OAAO,EAEf,MAAAC,EAAa,IAAI,cACvBA,EAAW,YAAYH,CAAM,EAE7B,IAAII,EAAe,GACnBJ,EAASA,EAAO,QAAQ,8BAA+B,CAACK,EAAOlK,KAC9DiK,GAAgB,eAAejK;AAAA,EACxB,GACP,EAED,MAAMmK,EAAQH,EAAW,SAEzB,IAAII,EAAa,GACbC,EAAmB,iDAAiDP,cAExE,QAAS1L,EAAI,EAAGA,EAAI+L,EAAM,OAAQ/L,IAAK,CAChC,MAAAkM,EAAOH,EAAM/L,CAAC,EAEpB,IAAImM,EAAeD,EAAK,QAAQ,SAAS,OAAO,EAChD,GAAIA,aAAgB,aAAc,CACjC,MAAME,EAAWF,EAAK,aACtB,GAAIE,EAAU,CACP,MAAAC,EAAeD,EACnB,QAAQ,QAAS,EAAE,EACnB,MAAM,GAAG,EACT,IACCE,GACA,GAAGH,EAAe,QAAU,MAAMF,KAAoBK,EAAE,KAAK,MAE9D,KAAK,GAAG,EAEVN,GAAcE,EAAK,QACnBF,GAAcE,EAAK,QAAQ,QAAQE,EAAUC,CAAY,WAEhDH,aAAgB,aAAc,CACpC,IAAAK,EAAiB,UAAUL,EAAK,MAAM,cAC1C,QAASM,EAAI,EAAGA,EAAIN,EAAK,SAAS,OAAQM,IAAK,CACxC,MAAAC,EAAYP,EAAK,SAASM,CAAC,EACjC,GAAIC,aAAqB,aAAc,CACtC,IAAIN,EAAeM,EAAU,QAAQ,SAAS,QAAQ,EACtD,MAAML,EAAWK,EAAU,aACrBJ,EAAeD,EACnB,QAAQ,QAAS,EAAE,EACnB,MAAM,GAAG,EACT,IACCE,GACA,GACCH,EAAe,QAAU,MACtBF,KAAoBK,EAAE,KAAK,MAEhC,KAAK,GAAG,EACVC,GAAkBE,EAAU,QAAQ,QAAQL,EAAUC,CAAY,GAGlDE,GAAA,IACJP,GAAAO,UACJL,aAAgB,iBAAkB,CAC5CF,GAAc,cAAcE,EAAK,SACjC,QAASM,EAAI,EAAGA,EAAIN,EAAK,SAAS,OAAQM,IAAK,CACxC,MAAAC,EAAYP,EAAK,SAASM,CAAC,EAC7BC,aAAqB,kBACxBT,GAAc,GAAGS,EAAU,aAAaA,EAAU,MAAM,aAG5CT,GAAA,SACJE,aAAgB,kBACZF,GAAA,gBAAgBE,EAAK,MAAM,aAG3C,OAAAF,EAAaH,EAAeG,EAC5BL,EAAc,YAAcK,EAEnB,cAAK,YAAYL,CAAa,EAChCA,CACR,CCnGA,MAAMe,GAAY,gBAElB,IAAIC,GAEJA,GAAQ,gBAER,IAAIC,GACAC,GACAC,GAAU,IAAI,QAASvN,GAAQ,CAC3BsN,GAAAtN,CACR,CAAC,EACD,eAAewN,IAA2B,CACvBH,IAAA,MAAA9I,GAAA,IAAM,OAAO,qBAAgB,EAAG,KAAAC,KAAA,4EAC7C8I,IACN,CAEA,SAASG,IAA8B,CACtC,MAAMC,EAAI,CACT,gBAAiBC,GAAO,iBAEzB,UAAWlD,KAAOkD,GACblD,IAAQ,oBACRA,IAAQ,qBAETiD,EAAAjD,CAAG,EAAIiD,EAAE,gBAGTA,EAAAjD,CAAG,EAAIkD,GAAOlD,CAAG,GAIrB,OAAO,2BAA6BiD,EACpC,MAAME,UAAkB,WAAY,CAgBnC,aAAc,CACP,QACD,UAAO,KAAK,aAAa,MAAM,EAC/B,WAAQ,KAAK,aAAa,OAAO,EACjC,SAAM,KAAK,aAAa,KAAK,EAE7B,wBAAqB,KAAK,aAAa,oBAAoB,EAChE,KAAK,eAAiB,KAAK,aAAa,gBAAgB,GAAK,QAC7D,KAAK,SAAW,KAAK,aAAa,OAAO,GAAK,OAC9C,KAAK,UAAY,KAAK,aAAa,WAAW,GAAK,OACnD,KAAK,KAAO,KAAK,aAAa,MAAM,GAAK,GACpC,gBAAa,KAAK,aAAa,YAAY,EAC3C,WAAQ,KAAK,aAAa,OAAO,EACjC,gBAAa,KAAK,aAAa,YAAY,EAChD,KAAK,SAAW,GAChB,KAAK,QAAU,EAChB,CAEA,MAAM,mBAAmC,CACxC,MAAMJ,GAAU,EAChB,KAAK,QAAU,GAEX,KAAK,KACR,KAAK,IAAI,WAGN,OAAOJ,IAAU,UACpBA,GAAM,QAASnL,GAAM4J,GAAU5J,EAAG,SAAS,IAAI,CAAC,EAG3C,MAAA4J,GAAUsB,GAAW,SAAS,IAAI,EAElC,MAAA3F,EAAQ,IAAI,YAAY,YAAa,CAC1C,QAAS,GACT,WAAY,GACZ,SAAU,GACV,EAEgB,IAAI,iBAAkBqG,GAAc,CACpD,KAAK,cAAcrG,CAAK,EACxB,EAEQ,QAAQ,KAAM,CAAE,UAAW,EAAM,GAErC,SAAM,IAAI6F,GAAe,CAC7B,OAAQ,KACR,MAAO,CAEN,MAAO,KAAK,MAAQ,KAAK,MAAM,OAAS,KAAK,MAC7C,IAAK,KAAK,IAAM,KAAK,IAAI,OAAS,KAAK,IACvC,KAAM,KAAK,KAAO,KAAK,KAAK,OAAS,KAAK,KAE1C,KAAM,KAAK,OAAS,QACpB,UAAW,KAAK,YAAc,QAC9B,SAAU,KAAK,WAAa,QAC5B,eAAgB,KAAK,eACrB,MAAO,KAAK,QAAU,OAEtB,QAAS,SACT,WAAY,KAAK,WAEjB,WAAY,KAAK,aAAe,OAChC,mBAAoB,KAAK,qBAAuB,OAEhD,OAAAnD,GACA,aAAArI,GAGA,SAAU,OAAO,kBAAoB,KACtC,EACA,EAEG,KAAK,UACR,KAAK,aAAa,KAAK,SAAS,KAAM,KAAK,SAAS,KAAK,EAG1D,KAAK,QAAU,EAChB,CAEA,WAAW,oBAA+C,CAClD,OAAC,MAAO,QAAS,MAAM,CAC/B,CAEA,MAAM,yBACLkI,EACA+D,EACAC,EACgB,CAEhB,GADM,MAAAR,IAEJxD,IAAS,QAAUA,IAAS,SAAWA,IAAS,QACjDgE,IAAYD,EACX,CAED,GADA,KAAK,SAAW,CAAE,KAAA/D,EAAM,MAAOgE,CAAQ,EACnC,KAAK,QAAS,OAEd,KAAK,KACR,KAAK,IAAI,WAGV,KAAK,MAAQ,KACb,KAAK,KAAO,KACZ,KAAK,IAAM,KAEPhE,IAAS,OACZ,KAAK,KAAOgE,EACFhE,IAAS,QACnB,KAAK,MAAQgE,EACHhE,IAAS,QACnB,KAAK,IAAMgE,GAGP,SAAM,IAAIV,GAAe,CAC7B,OAAQ,KACR,MAAO,CAEN,MAAO,KAAK,MAAQ,KAAK,MAAM,OAAS,KAAK,MAC7C,IAAK,KAAK,IAAM,KAAK,IAAI,OAAS,KAAK,IACvC,KAAM,KAAK,KAAO,KAAK,KAAK,OAAS,KAAK,KAE1C,KAAM,KAAK,OAAS,QACpB,UAAW,KAAK,YAAc,QAC9B,SAAU,KAAK,WAAa,QAC5B,eAAgB,KAAK,eACrB,MAAO,KAAK,QAAU,OAEtB,QAAS,SACT,WAAY,KAAK,WAEjB,WAAY,KAAK,aAAe,OAChC,mBACC,KAAK,qBAAuB,OAE7B,OAAAnD,GACA,aAAArI,GAGA,SAAU,OAAO,kBAAoB,KACtC,EACA,EAED,KAAK,SAAW,GAElB,CACD,CACK,eAAe,IAAI,YAAY,GACpB,sBAAO,aAAc+L,CAAS,CAC/C,CAEAH,GAAsB", "names": ["fn", "semiver", "a", "b", "bool", "resolve_root", "base_url", "root_path", "prioritize_base", "determine_protocol", "endpoint", "protocol", "host", "RE_SPACE_NAME", "RE_SPACE_DOMAIN", "process_endpoint", "app_reference", "token", "headers", "_app_reference", "res", "_host", "e", "ws_protocol", "http_protocol", "map_names_to_ids", "fns", "apis", "api_name", "i", "RE_DISABLED_DISCUSSION", "discussions_enabled", "space_id", "error", "apply_edit", "target", "path", "action", "value", "current", "last_path", "apply_diff", "obj", "diff", "upload", "file_data", "root", "upload_id", "upload_fn", "upload_files", "files", "file_data2", "response", "f", "FileData", "prepare_files", "is_stream", "url", "orig_name", "size", "blob", "mime_type", "alt_text", "QUEUE_FULL_MSG", "BROKEN_CONNECTION_MSG", "NodeBlob", "api_factory", "fetch_implementation", "EventSource_factory", "post_data2", "upload_files2", "client2", "handle_blob2", "body", "output", "status", "chunkSize", "uploadResponses", "chunk", "formData", "file", "upload_url", "options", "status_callback", "hf_token", "return_obj", "predict", "submit", "view_api", "component_server", "ws", "__vitePreload", "n", "session_hash", "last_status", "stream_open", "pending_stream_messages", "pending_diff_streams", "event_stream", "event_callbacks", "unclosed_events", "config", "api_map", "jwt", "get_jwt", "config_success", "_config", "api", "handle_space_sucess", "resolve_config", "heartbeat", "check_space_status", "data", "event_data", "data_returned", "status_complete", "dependency", "trimmed_endpoint", "res2", "rej", "app", "result", "d", "trigger_id", "fn_index", "api_info", "websocket", "eventSource", "_endpoint", "payload", "event_id", "complete", "listener_map", "url_params", "_payload", "skip_queue", "fire_event", "status_code", "data2", "evt", "event", "_data", "type", "handle_message", "params", "_", "status2", "callback", "apply_diff_stream", "close_stream", "msg", "open_stream", "event_id2", "new_data", "listeners", "l", "on", "eventType", "listener", "narrowed_listener_map", "off", "cancel", "destroy", "_status", "cancel_request", "event_type", "fn2", "component_id", "fn_name", "_a", "root_url", "component", "comp", "config2", "transform_api_info", "blob_refs", "walk_and_store_blobs", "file_url", "r", "name", "update_object", "post_data", "client", "handle_blob", "args", "get_type", "serializer", "signature_type", "get_description", "key", "cat", "dep_index", "info", "label", "space", "object", "newValue", "stack", "param", "v", "new_path", "array_refs", "id", "_b", "_c", "_d", "stage", "space_name", "supports_adopted_stylesheets", "mount_css", "base", "_url", "link", "prefix_css", "string", "version", "style_element", "stylesheet", "importString", "match", "rules", "css_string", "gradio_css_infix", "rule", "is_dark_rule", "selector", "new_selector", "s", "mediaCssString", "j", "innerRule", "ENTRY_CSS", "FONTS", "IndexComponent", "_res", "pending", "get_index", "create_custom_element", "o", "svelte", "GradioApp", "mutations", "old_val", "new_val"], "sources": ["../../../../client/js/dist/index.js", "../../../../js/app/src/css.ts", "../../../../js/app/src/main.ts"], "sourcesContent": ["var fn = new Intl.Collator(0, { numeric: 1 }).compare;\nfunction semiver(a, b, bool) {\n  a = a.split(\".\");\n  b = b.split(\".\");\n  return fn(a[0], b[0]) || fn(a[1], b[1]) || (b[2] = b.slice(2).join(\".\"), bool = /[.-]/.test(a[2] = a.slice(2).join(\".\")), bool == /[.-]/.test(b[2]) ? fn(a[2], b[2]) : bool ? -1 : 1);\n}\nfunction resolve_root(base_url, root_path, prioritize_base) {\n  if (root_path.startsWith(\"http://\") || root_path.startsWith(\"https://\")) {\n    return prioritize_base ? base_url : root_path;\n  }\n  return base_url + root_path;\n}\nfunction determine_protocol(endpoint) {\n  if (endpoint.startsWith(\"http\")) {\n    const { protocol, host } = new URL(endpoint);\n    if (host.endsWith(\"hf.space\")) {\n      return {\n        ws_protocol: \"wss\",\n        host,\n        http_protocol: protocol\n      };\n    }\n    return {\n      ws_protocol: protocol === \"https:\" ? \"wss\" : \"ws\",\n      http_protocol: protocol,\n      host\n    };\n  } else if (endpoint.startsWith(\"file:\")) {\n    return {\n      ws_protocol: \"ws\",\n      http_protocol: \"http:\",\n      host: \"lite.local\"\n      // Special fake hostname only used for this case. This matches the hostname allowed in `is_self_host()` in `js/wasm/network/host.ts`.\n    };\n  }\n  return {\n    ws_protocol: \"wss\",\n    http_protocol: \"https:\",\n    host: endpoint\n  };\n}\nconst RE_SPACE_NAME = /^[^\\/]*\\/[^\\/]*$/;\nconst RE_SPACE_DOMAIN = /.*hf\\.space\\/{0,1}$/;\nasync function process_endpoint(app_reference, token) {\n  const headers = {};\n  if (token) {\n    headers.Authorization = `Bearer ${token}`;\n  }\n  const _app_reference = app_reference.trim();\n  if (RE_SPACE_NAME.test(_app_reference)) {\n    try {\n      const res = await fetch(\n        `https://huggingface.co/api/spaces/${_app_reference}/host`,\n        { headers }\n      );\n      if (res.status !== 200)\n        throw new Error(\"Space metadata could not be loaded.\");\n      const _host = (await res.json()).host;\n      return {\n        space_id: app_reference,\n        ...determine_protocol(_host)\n      };\n    } catch (e) {\n      throw new Error(\"Space metadata could not be loaded.\" + e.message);\n    }\n  }\n  if (RE_SPACE_DOMAIN.test(_app_reference)) {\n    const { ws_protocol, http_protocol, host } = determine_protocol(_app_reference);\n    return {\n      space_id: host.replace(\".hf.space\", \"\"),\n      ws_protocol,\n      http_protocol,\n      host\n    };\n  }\n  return {\n    space_id: false,\n    ...determine_protocol(_app_reference)\n  };\n}\nfunction map_names_to_ids(fns) {\n  let apis = {};\n  fns.forEach(({ api_name }, i) => {\n    if (api_name)\n      apis[api_name] = i;\n  });\n  return apis;\n}\nconst RE_DISABLED_DISCUSSION = /^(?=[^]*\\b[dD]iscussions{0,1}\\b)(?=[^]*\\b[dD]isabled\\b)[^]*$/;\nasync function discussions_enabled(space_id) {\n  try {\n    const r = await fetch(\n      `https://huggingface.co/api/spaces/${space_id}/discussions`,\n      {\n        method: \"HEAD\"\n      }\n    );\n    const error = r.headers.get(\"x-error-message\");\n    if (error && RE_DISABLED_DISCUSSION.test(error))\n      return false;\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nasync function get_space_hardware(space_id, token) {\n  const headers = {};\n  if (token) {\n    headers.Authorization = `Bearer ${token}`;\n  }\n  try {\n    const res = await fetch(\n      `https://huggingface.co/api/spaces/${space_id}/runtime`,\n      { headers }\n    );\n    if (res.status !== 200)\n      throw new Error(\"Space hardware could not be obtained.\");\n    const { hardware } = await res.json();\n    return hardware;\n  } catch (e) {\n    throw new Error(e.message);\n  }\n}\nasync function set_space_hardware(space_id, new_hardware, token) {\n  const headers = {};\n  if (token) {\n    headers.Authorization = `Bearer ${token}`;\n  }\n  try {\n    const res = await fetch(\n      `https://huggingface.co/api/spaces/${space_id}/hardware`,\n      { headers, body: JSON.stringify(new_hardware) }\n    );\n    if (res.status !== 200)\n      throw new Error(\n        \"Space hardware could not be set. Please ensure the space hardware provided is valid and that a Hugging Face token is passed in.\"\n      );\n    const { hardware } = await res.json();\n    return hardware;\n  } catch (e) {\n    throw new Error(e.message);\n  }\n}\nasync function set_space_timeout(space_id, timeout, token) {\n  const headers = {};\n  if (token) {\n    headers.Authorization = `Bearer ${token}`;\n  }\n  try {\n    const res = await fetch(\n      `https://huggingface.co/api/spaces/${space_id}/hardware`,\n      { headers, body: JSON.stringify({ seconds: timeout }) }\n    );\n    if (res.status !== 200)\n      throw new Error(\n        \"Space hardware could not be set. Please ensure the space hardware provided is valid and that a Hugging Face token is passed in.\"\n      );\n    const { hardware } = await res.json();\n    return hardware;\n  } catch (e) {\n    throw new Error(e.message);\n  }\n}\nconst hardware_types = [\n  \"cpu-basic\",\n  \"cpu-upgrade\",\n  \"t4-small\",\n  \"t4-medium\",\n  \"a10g-small\",\n  \"a10g-large\",\n  \"a100-large\"\n];\nfunction apply_edit(target, path, action, value) {\n  if (path.length === 0) {\n    if (action === \"replace\") {\n      return value;\n    } else if (action === \"append\") {\n      return target + value;\n    }\n    throw new Error(`Unsupported action: ${action}`);\n  }\n  let current = target;\n  for (let i = 0; i < path.length - 1; i++) {\n    current = current[path[i]];\n  }\n  const last_path = path[path.length - 1];\n  switch (action) {\n    case \"replace\":\n      current[last_path] = value;\n      break;\n    case \"append\":\n      current[last_path] += value;\n      break;\n    case \"add\":\n      if (Array.isArray(current)) {\n        current.splice(Number(last_path), 0, value);\n      } else {\n        current[last_path] = value;\n      }\n      break;\n    case \"delete\":\n      if (Array.isArray(current)) {\n        current.splice(Number(last_path), 1);\n      } else {\n        delete current[last_path];\n      }\n      break;\n    default:\n      throw new Error(`Unknown action: ${action}`);\n  }\n  return target;\n}\nfunction apply_diff(obj, diff) {\n  diff.forEach(([action, path, value]) => {\n    obj = apply_edit(obj, path, action, value);\n  });\n  return obj;\n}\nasync function upload(file_data, root, upload_id, upload_fn = upload_files) {\n  let files = (Array.isArray(file_data) ? file_data : [file_data]).map(\n    (file_data2) => file_data2.blob\n  );\n  return await Promise.all(\n    await upload_fn(root, files, void 0, upload_id).then(\n      async (response) => {\n        if (response.error) {\n          throw new Error(response.error);\n        } else {\n          if (response.files) {\n            return response.files.map((f, i) => {\n              const file = new FileData({\n                ...file_data[i],\n                path: f,\n                url: root + \"/file=\" + f\n              });\n              return file;\n            });\n          }\n          return [];\n        }\n      }\n    )\n  );\n}\nasync function prepare_files(files, is_stream) {\n  return files.map(\n    (f, i) => new FileData({\n      path: f.name,\n      orig_name: f.name,\n      blob: f,\n      size: f.size,\n      mime_type: f.type,\n      is_stream\n    })\n  );\n}\nclass FileData {\n  constructor({\n    path,\n    url,\n    orig_name,\n    size,\n    blob,\n    is_stream,\n    mime_type,\n    alt_text\n  }) {\n    this.meta = { _type: \"gradio.FileData\" };\n    this.path = path;\n    this.url = url;\n    this.orig_name = orig_name;\n    this.size = size;\n    this.blob = url ? void 0 : blob;\n    this.is_stream = is_stream;\n    this.mime_type = mime_type;\n    this.alt_text = alt_text;\n  }\n}\nconst QUEUE_FULL_MSG = \"This application is too busy. Keep trying!\";\nconst BROKEN_CONNECTION_MSG = \"Connection errored out.\";\nlet NodeBlob;\nasync function duplicate(app_reference, options) {\n  const { hf_token, private: _private, hardware, timeout } = options;\n  if (hardware && !hardware_types.includes(hardware)) {\n    throw new Error(\n      `Invalid hardware type provided. Valid types are: ${hardware_types.map((v) => `\"${v}\"`).join(\",\")}.`\n    );\n  }\n  const headers = {\n    Authorization: `Bearer ${hf_token}`\n  };\n  const user = (await (await fetch(`https://huggingface.co/api/whoami-v2`, {\n    headers\n  })).json()).name;\n  const space_name = app_reference.split(\"/\")[1];\n  const body = {\n    repository: `${user}/${space_name}`\n  };\n  if (_private) {\n    body.private = true;\n  }\n  try {\n    const response = await fetch(\n      `https://huggingface.co/api/spaces/${app_reference}/duplicate`,\n      {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\", ...headers },\n        body: JSON.stringify(body)\n      }\n    );\n    if (response.status === 409) {\n      return client(`${user}/${space_name}`, options);\n    }\n    const duplicated_space = await response.json();\n    let original_hardware;\n    if (!hardware) {\n      original_hardware = await get_space_hardware(app_reference, hf_token);\n    }\n    const requested_hardware = hardware || original_hardware || \"cpu-basic\";\n    await set_space_hardware(\n      `${user}/${space_name}`,\n      requested_hardware,\n      hf_token\n    );\n    await set_space_timeout(`${user}/${space_name}`, timeout || 300, hf_token);\n    return client(duplicated_space.url, options);\n  } catch (e) {\n    throw new Error(e);\n  }\n}\nfunction api_factory(fetch_implementation, EventSource_factory) {\n  return { post_data: post_data2, upload_files: upload_files2, client: client2, handle_blob: handle_blob2 };\n  async function post_data2(url, body, token) {\n    const headers = { \"Content-Type\": \"application/json\" };\n    if (token) {\n      headers.Authorization = `Bearer ${token}`;\n    }\n    try {\n      var response = await fetch_implementation(url, {\n        method: \"POST\",\n        body: JSON.stringify(body),\n        headers\n      });\n    } catch (e) {\n      return [{ error: BROKEN_CONNECTION_MSG }, 500];\n    }\n    let output;\n    let status;\n    try {\n      output = await response.json();\n      status = response.status;\n    } catch (e) {\n      output = { error: `Could not parse server response: ${e}` };\n      status = 500;\n    }\n    return [output, status];\n  }\n  async function upload_files2(root, files, token, upload_id) {\n    const headers = {};\n    if (token) {\n      headers.Authorization = `Bearer ${token}`;\n    }\n    const chunkSize = 1e3;\n    const uploadResponses = [];\n    for (let i = 0; i < files.length; i += chunkSize) {\n      const chunk = files.slice(i, i + chunkSize);\n      const formData = new FormData();\n      chunk.forEach((file) => {\n        formData.append(\"files\", file);\n      });\n      try {\n        const upload_url = upload_id ? `${root}/upload?upload_id=${upload_id}` : `${root}/upload`;\n        var response = await fetch_implementation(upload_url, {\n          method: \"POST\",\n          body: formData,\n          headers\n        });\n      } catch (e) {\n        return { error: BROKEN_CONNECTION_MSG };\n      }\n      const output = await response.json();\n      uploadResponses.push(...output);\n    }\n    return { files: uploadResponses };\n  }\n  async function client2(app_reference, options = {}) {\n    return new Promise(async (res) => {\n      const { status_callback, hf_token } = options;\n      const return_obj = {\n        predict,\n        submit,\n        view_api,\n        component_server\n      };\n      if ((typeof window === \"undefined\" || !(\"WebSocket\" in window)) && !global.Websocket) {\n        const ws = await import(\"./wrapper-6f348d45.js\");\n        NodeBlob = (await import(\"node:buffer\")).Blob;\n        global.WebSocket = ws.WebSocket;\n      }\n      const { ws_protocol, http_protocol, host, space_id } = await process_endpoint(app_reference, hf_token);\n      const session_hash = Math.random().toString(36).substring(2);\n      const last_status = {};\n      let stream_open = false;\n      let pending_stream_messages = {};\n      let pending_diff_streams = {};\n      let event_stream = null;\n      const event_callbacks = {};\n      const unclosed_events = /* @__PURE__ */ new Set();\n      let config;\n      let api_map = {};\n      let jwt = false;\n      if (hf_token && space_id) {\n        jwt = await get_jwt(space_id, hf_token);\n      }\n      async function config_success(_config) {\n        config = _config;\n        if (window.location.protocol === \"https:\") {\n          config.root = config.root.replace(\"http://\", \"https://\");\n        }\n        api_map = map_names_to_ids((_config == null ? void 0 : _config.dependencies) || []);\n        if (config.auth_required) {\n          return {\n            config,\n            ...return_obj\n          };\n        }\n        try {\n          api = await view_api(config);\n        } catch (e) {\n          console.error(`Could not get api details: ${e.message}`);\n        }\n        return {\n          config,\n          ...return_obj\n        };\n      }\n      let api;\n      async function handle_space_sucess(status) {\n        if (status_callback)\n          status_callback(status);\n        if (status.status === \"running\")\n          try {\n            config = await resolve_config(\n              fetch_implementation,\n              `${http_protocol}//${host}`,\n              hf_token\n            );\n            const _config = await config_success(config);\n            res(_config);\n          } catch (e) {\n            console.error(e);\n            if (status_callback) {\n              status_callback({\n                status: \"error\",\n                message: \"Could not load this space.\",\n                load_status: \"error\",\n                detail: \"NOT_FOUND\"\n              });\n            }\n          }\n      }\n      try {\n        config = await resolve_config(\n          fetch_implementation,\n          `${http_protocol}//${host}`,\n          hf_token\n        );\n        const _config = await config_success(config);\n        const heartbeat = new EventSource(\n          `${config.root}/heartbeat/${session_hash}`\n        );\n        res(_config);\n      } catch (e) {\n        console.error(e);\n        if (space_id) {\n          check_space_status(\n            space_id,\n            RE_SPACE_NAME.test(space_id) ? \"space_name\" : \"subdomain\",\n            handle_space_sucess\n          );\n        } else {\n          if (status_callback)\n            status_callback({\n              status: \"error\",\n              message: \"Could not load this space.\",\n              load_status: \"error\",\n              detail: \"NOT_FOUND\"\n            });\n        }\n      }\n      function predict(endpoint, data, event_data) {\n        let data_returned = false;\n        let status_complete = false;\n        let dependency;\n        if (typeof endpoint === \"number\") {\n          dependency = config.dependencies[endpoint];\n        } else {\n          const trimmed_endpoint = endpoint.replace(/^\\//, \"\");\n          dependency = config.dependencies[api_map[trimmed_endpoint]];\n        }\n        if (dependency.types.continuous) {\n          throw new Error(\n            \"Cannot call predict on this function as it may run forever. Use submit instead\"\n          );\n        }\n        return new Promise((res2, rej) => {\n          const app = submit(endpoint, data, event_data);\n          let result;\n          app.on(\"data\", (d) => {\n            if (status_complete) {\n              app.destroy();\n              res2(d);\n            }\n            data_returned = true;\n            result = d;\n          }).on(\"status\", (status) => {\n            if (status.stage === \"error\")\n              rej(status);\n            if (status.stage === \"complete\") {\n              status_complete = true;\n              if (data_returned) {\n                app.destroy();\n                res2(result);\n              }\n            }\n          });\n        });\n      }\n      function submit(endpoint, data, event_data, trigger_id = null) {\n        let fn_index;\n        let api_info;\n        if (typeof endpoint === \"number\") {\n          fn_index = endpoint;\n          api_info = api.unnamed_endpoints[fn_index];\n        } else {\n          const trimmed_endpoint = endpoint.replace(/^\\//, \"\");\n          fn_index = api_map[trimmed_endpoint];\n          api_info = api.named_endpoints[endpoint.trim()];\n        }\n        if (typeof fn_index !== \"number\") {\n          throw new Error(\n            \"There is no endpoint matching that name of fn_index matching that number.\"\n          );\n        }\n        let websocket;\n        let eventSource;\n        let protocol = config.protocol ?? \"ws\";\n        const _endpoint = typeof endpoint === \"number\" ? \"/predict\" : endpoint;\n        let payload;\n        let event_id = null;\n        let complete = false;\n        const listener_map = {};\n        let url_params = \"\";\n        if (typeof window !== \"undefined\") {\n          url_params = new URLSearchParams(window.location.search).toString();\n        }\n        handle_blob2(`${config.root}`, data, api_info, hf_token).then(\n          (_payload) => {\n            payload = {\n              data: _payload || [],\n              event_data,\n              fn_index,\n              trigger_id\n            };\n            if (skip_queue(fn_index, config)) {\n              fire_event({\n                type: \"status\",\n                endpoint: _endpoint,\n                stage: \"pending\",\n                queue: false,\n                fn_index,\n                time: /* @__PURE__ */ new Date()\n              });\n              post_data2(\n                `${config.root}/run${_endpoint.startsWith(\"/\") ? _endpoint : `/${_endpoint}`}${url_params ? \"?\" + url_params : \"\"}`,\n                {\n                  ...payload,\n                  session_hash\n                },\n                hf_token\n              ).then(([output, status_code]) => {\n                const data2 = output.data;\n                if (status_code == 200) {\n                  fire_event({\n                    type: \"data\",\n                    endpoint: _endpoint,\n                    fn_index,\n                    data: data2,\n                    time: /* @__PURE__ */ new Date()\n                  });\n                  fire_event({\n                    type: \"status\",\n                    endpoint: _endpoint,\n                    fn_index,\n                    stage: \"complete\",\n                    eta: output.average_duration,\n                    queue: false,\n                    time: /* @__PURE__ */ new Date()\n                  });\n                } else {\n                  fire_event({\n                    type: \"status\",\n                    stage: \"error\",\n                    endpoint: _endpoint,\n                    fn_index,\n                    message: output.error,\n                    queue: false,\n                    time: /* @__PURE__ */ new Date()\n                  });\n                }\n              }).catch((e) => {\n                fire_event({\n                  type: \"status\",\n                  stage: \"error\",\n                  message: e.message,\n                  endpoint: _endpoint,\n                  fn_index,\n                  queue: false,\n                  time: /* @__PURE__ */ new Date()\n                });\n              });\n            } else if (protocol == \"ws\") {\n              fire_event({\n                type: \"status\",\n                stage: \"pending\",\n                queue: true,\n                endpoint: _endpoint,\n                fn_index,\n                time: /* @__PURE__ */ new Date()\n              });\n              let url = new URL(`${ws_protocol}://${resolve_root(\n                host,\n                config.path,\n                true\n              )}\n\t\t\t\t\t\t\t/queue/join${url_params ? \"?\" + url_params : \"\"}`);\n              if (jwt) {\n                url.searchParams.set(\"__sign\", jwt);\n              }\n              websocket = new WebSocket(url);\n              websocket.onclose = (evt) => {\n                if (!evt.wasClean) {\n                  fire_event({\n                    type: \"status\",\n                    stage: \"error\",\n                    broken: true,\n                    message: BROKEN_CONNECTION_MSG,\n                    queue: true,\n                    endpoint: _endpoint,\n                    fn_index,\n                    time: /* @__PURE__ */ new Date()\n                  });\n                }\n              };\n              websocket.onmessage = function(event) {\n                const _data = JSON.parse(event.data);\n                const { type, status, data: data2 } = handle_message(\n                  _data,\n                  last_status[fn_index]\n                );\n                if (type === \"update\" && status && !complete) {\n                  fire_event({\n                    type: \"status\",\n                    endpoint: _endpoint,\n                    fn_index,\n                    time: /* @__PURE__ */ new Date(),\n                    ...status\n                  });\n                  if (status.stage === \"error\") {\n                    websocket.close();\n                  }\n                } else if (type === \"hash\") {\n                  websocket.send(JSON.stringify({ fn_index, session_hash }));\n                  return;\n                } else if (type === \"data\") {\n                  websocket.send(JSON.stringify({ ...payload, session_hash }));\n                } else if (type === \"complete\") {\n                  complete = status;\n                } else if (type === \"log\") {\n                  fire_event({\n                    type: \"log\",\n                    log: data2.log,\n                    level: data2.level,\n                    endpoint: _endpoint,\n                    fn_index\n                  });\n                } else if (type === \"generating\") {\n                  fire_event({\n                    type: \"status\",\n                    time: /* @__PURE__ */ new Date(),\n                    ...status,\n                    stage: status == null ? void 0 : status.stage,\n                    queue: true,\n                    endpoint: _endpoint,\n                    fn_index\n                  });\n                }\n                if (data2) {\n                  fire_event({\n                    type: \"data\",\n                    time: /* @__PURE__ */ new Date(),\n                    data: data2.data,\n                    endpoint: _endpoint,\n                    fn_index\n                  });\n                  if (complete) {\n                    fire_event({\n                      type: \"status\",\n                      time: /* @__PURE__ */ new Date(),\n                      ...complete,\n                      stage: status == null ? void 0 : status.stage,\n                      queue: true,\n                      endpoint: _endpoint,\n                      fn_index\n                    });\n                    websocket.close();\n                  }\n                }\n              };\n              if (semiver(config.version || \"2.0.0\", \"3.6\") < 0) {\n                addEventListener(\n                  \"open\",\n                  () => websocket.send(JSON.stringify({ hash: session_hash }))\n                );\n              }\n            } else if (protocol == \"sse\") {\n              fire_event({\n                type: \"status\",\n                stage: \"pending\",\n                queue: true,\n                endpoint: _endpoint,\n                fn_index,\n                time: /* @__PURE__ */ new Date()\n              });\n              var params = new URLSearchParams({\n                fn_index: fn_index.toString(),\n                session_hash\n              }).toString();\n              let url = new URL(\n                `${config.root}/queue/join?${url_params ? url_params + \"&\" : \"\"}${params}`\n              );\n              eventSource = EventSource_factory(url);\n              eventSource.onmessage = async function(event) {\n                const _data = JSON.parse(event.data);\n                const { type, status, data: data2 } = handle_message(\n                  _data,\n                  last_status[fn_index]\n                );\n                if (type === \"update\" && status && !complete) {\n                  fire_event({\n                    type: \"status\",\n                    endpoint: _endpoint,\n                    fn_index,\n                    time: /* @__PURE__ */ new Date(),\n                    ...status\n                  });\n                  if (status.stage === \"error\") {\n                    eventSource.close();\n                  }\n                } else if (type === \"data\") {\n                  event_id = _data.event_id;\n                  let [_, status2] = await post_data2(\n                    `${config.root}/queue/data`,\n                    {\n                      ...payload,\n                      session_hash,\n                      event_id\n                    },\n                    hf_token\n                  );\n                  if (status2 !== 200) {\n                    fire_event({\n                      type: \"status\",\n                      stage: \"error\",\n                      message: BROKEN_CONNECTION_MSG,\n                      queue: true,\n                      endpoint: _endpoint,\n                      fn_index,\n                      time: /* @__PURE__ */ new Date()\n                    });\n                    eventSource.close();\n                  }\n                } else if (type === \"complete\") {\n                  complete = status;\n                } else if (type === \"log\") {\n                  fire_event({\n                    type: \"log\",\n                    log: data2.log,\n                    level: data2.level,\n                    endpoint: _endpoint,\n                    fn_index\n                  });\n                } else if (type === \"generating\") {\n                  fire_event({\n                    type: \"status\",\n                    time: /* @__PURE__ */ new Date(),\n                    ...status,\n                    stage: status == null ? void 0 : status.stage,\n                    queue: true,\n                    endpoint: _endpoint,\n                    fn_index\n                  });\n                }\n                if (data2) {\n                  fire_event({\n                    type: \"data\",\n                    time: /* @__PURE__ */ new Date(),\n                    data: data2.data,\n                    endpoint: _endpoint,\n                    fn_index\n                  });\n                  if (complete) {\n                    fire_event({\n                      type: \"status\",\n                      time: /* @__PURE__ */ new Date(),\n                      ...complete,\n                      stage: status == null ? void 0 : status.stage,\n                      queue: true,\n                      endpoint: _endpoint,\n                      fn_index\n                    });\n                    eventSource.close();\n                  }\n                }\n              };\n            } else if (protocol == \"sse_v1\" || protocol == \"sse_v2\" || protocol == \"sse_v2.1\" || protocol == \"sse_v3\") {\n              fire_event({\n                type: \"status\",\n                stage: \"pending\",\n                queue: true,\n                endpoint: _endpoint,\n                fn_index,\n                time: /* @__PURE__ */ new Date()\n              });\n              post_data2(\n                `${config.root}/queue/join?${url_params}`,\n                {\n                  ...payload,\n                  session_hash\n                },\n                hf_token\n              ).then(([response, status]) => {\n                if (status === 503) {\n                  fire_event({\n                    type: \"status\",\n                    stage: \"error\",\n                    message: QUEUE_FULL_MSG,\n                    queue: true,\n                    endpoint: _endpoint,\n                    fn_index,\n                    time: /* @__PURE__ */ new Date()\n                  });\n                } else if (status !== 200) {\n                  fire_event({\n                    type: \"status\",\n                    stage: \"error\",\n                    message: BROKEN_CONNECTION_MSG,\n                    queue: true,\n                    endpoint: _endpoint,\n                    fn_index,\n                    time: /* @__PURE__ */ new Date()\n                  });\n                } else {\n                  event_id = response.event_id;\n                  let callback = async function(_data) {\n                    try {\n                      const { type, status: status2, data: data2 } = handle_message(\n                        _data,\n                        last_status[fn_index]\n                      );\n                      if (type == \"heartbeat\") {\n                        return;\n                      }\n                      if (type === \"update\" && status2 && !complete) {\n                        fire_event({\n                          type: \"status\",\n                          endpoint: _endpoint,\n                          fn_index,\n                          time: /* @__PURE__ */ new Date(),\n                          ...status2\n                        });\n                      } else if (type === \"complete\") {\n                        complete = status2;\n                      } else if (type == \"unexpected_error\") {\n                        console.error(\"Unexpected error\", status2 == null ? void 0 : status2.message);\n                        fire_event({\n                          type: \"status\",\n                          stage: \"error\",\n                          message: (status2 == null ? void 0 : status2.message) || \"An Unexpected Error Occurred!\",\n                          queue: true,\n                          endpoint: _endpoint,\n                          fn_index,\n                          time: /* @__PURE__ */ new Date()\n                        });\n                      } else if (type === \"log\") {\n                        fire_event({\n                          type: \"log\",\n                          log: data2.log,\n                          level: data2.level,\n                          endpoint: _endpoint,\n                          fn_index\n                        });\n                        return;\n                      } else if (type === \"generating\") {\n                        fire_event({\n                          type: \"status\",\n                          time: /* @__PURE__ */ new Date(),\n                          ...status2,\n                          stage: status2 == null ? void 0 : status2.stage,\n                          queue: true,\n                          endpoint: _endpoint,\n                          fn_index\n                        });\n                        if (data2 && [\"sse_v2\", \"sse_v2.1\", \"sse_v3\"].includes(protocol)) {\n                          apply_diff_stream(event_id, data2);\n                        }\n                      }\n                      if (data2) {\n                        fire_event({\n                          type: \"data\",\n                          time: /* @__PURE__ */ new Date(),\n                          data: data2.data,\n                          endpoint: _endpoint,\n                          fn_index\n                        });\n                        if (complete) {\n                          fire_event({\n                            type: \"status\",\n                            time: /* @__PURE__ */ new Date(),\n                            ...complete,\n                            stage: status2 == null ? void 0 : status2.stage,\n                            queue: true,\n                            endpoint: _endpoint,\n                            fn_index\n                          });\n                        }\n                      }\n                      if ((status2 == null ? void 0 : status2.stage) === \"complete\" || (status2 == null ? void 0 : status2.stage) === \"error\") {\n                        if (event_callbacks[event_id]) {\n                          delete event_callbacks[event_id];\n                        }\n                        if (event_id in pending_diff_streams) {\n                          delete pending_diff_streams[event_id];\n                        }\n                      }\n                    } catch (e) {\n                      console.error(\"Unexpected client exception\", e);\n                      fire_event({\n                        type: \"status\",\n                        stage: \"error\",\n                        message: \"An Unexpected Error Occurred!\",\n                        queue: true,\n                        endpoint: _endpoint,\n                        fn_index,\n                        time: /* @__PURE__ */ new Date()\n                      });\n                      if ([\"sse_v2\", \"sse_v2.1\"].includes(protocol)) {\n                        close_stream();\n                      }\n                    }\n                  };\n                  if (event_id in pending_stream_messages) {\n                    pending_stream_messages[event_id].forEach(\n                      (msg) => callback(msg)\n                    );\n                    delete pending_stream_messages[event_id];\n                  }\n                  event_callbacks[event_id] = callback;\n                  unclosed_events.add(event_id);\n                  if (!stream_open) {\n                    open_stream();\n                  }\n                }\n              });\n            }\n          }\n        );\n        function apply_diff_stream(event_id2, data2) {\n          let is_first_generation = !pending_diff_streams[event_id2];\n          if (is_first_generation) {\n            pending_diff_streams[event_id2] = [];\n            data2.data.forEach((value, i) => {\n              pending_diff_streams[event_id2][i] = value;\n            });\n          } else {\n            data2.data.forEach((value, i) => {\n              let new_data = apply_diff(\n                pending_diff_streams[event_id2][i],\n                value\n              );\n              pending_diff_streams[event_id2][i] = new_data;\n              data2.data[i] = new_data;\n            });\n          }\n        }\n        function fire_event(event) {\n          const narrowed_listener_map = listener_map;\n          const listeners = narrowed_listener_map[event.type] || [];\n          listeners == null ? void 0 : listeners.forEach((l) => l(event));\n        }\n        function on(eventType, listener) {\n          const narrowed_listener_map = listener_map;\n          const listeners = narrowed_listener_map[eventType] || [];\n          narrowed_listener_map[eventType] = listeners;\n          listeners == null ? void 0 : listeners.push(listener);\n          return { on, off, cancel, destroy };\n        }\n        function off(eventType, listener) {\n          const narrowed_listener_map = listener_map;\n          let listeners = narrowed_listener_map[eventType] || [];\n          listeners = listeners == null ? void 0 : listeners.filter((l) => l !== listener);\n          narrowed_listener_map[eventType] = listeners;\n          return { on, off, cancel, destroy };\n        }\n        async function cancel() {\n          const _status = {\n            stage: \"complete\",\n            queue: false,\n            time: /* @__PURE__ */ new Date()\n          };\n          complete = _status;\n          fire_event({\n            ..._status,\n            type: \"status\",\n            endpoint: _endpoint,\n            fn_index\n          });\n          let cancel_request = {};\n          if (protocol === \"ws\") {\n            if (websocket && websocket.readyState === 0) {\n              websocket.addEventListener(\"open\", () => {\n                websocket.close();\n              });\n            } else {\n              websocket.close();\n            }\n            cancel_request = { fn_index, session_hash };\n          } else {\n            eventSource.close();\n            cancel_request = { event_id };\n          }\n          try {\n            await fetch_implementation(`${config.root}/reset`, {\n              headers: { \"Content-Type\": \"application/json\" },\n              method: \"POST\",\n              body: JSON.stringify(cancel_request)\n            });\n          } catch (e) {\n            console.warn(\n              \"The `/reset` endpoint could not be called. Subsequent endpoint results may be unreliable.\"\n            );\n          }\n        }\n        function destroy() {\n          for (const event_type in listener_map) {\n            listener_map[event_type].forEach((fn2) => {\n              off(event_type, fn2);\n            });\n          }\n        }\n        return {\n          on,\n          off,\n          cancel,\n          destroy\n        };\n      }\n      function open_stream() {\n        stream_open = true;\n        let params = new URLSearchParams({\n          session_hash\n        }).toString();\n        let url = new URL(`${config.root}/queue/data?${params}`);\n        event_stream = EventSource_factory(url);\n        event_stream.onmessage = async function(event) {\n          let _data = JSON.parse(event.data);\n          const event_id = _data.event_id;\n          if (!event_id) {\n            await Promise.all(\n              Object.keys(event_callbacks).map(\n                (event_id2) => event_callbacks[event_id2](_data)\n              )\n            );\n          } else if (event_callbacks[event_id]) {\n            if (_data.msg === \"process_completed\" && [\"sse\", \"sse_v1\", \"sse_v2\", \"sse_v2.1\"].includes(config.protocol)) {\n              unclosed_events.delete(event_id);\n              if (unclosed_events.size === 0) {\n                close_stream();\n              }\n            }\n            let fn2 = event_callbacks[event_id];\n            window.setTimeout(fn2, 0, _data);\n          } else {\n            if (!pending_stream_messages[event_id]) {\n              pending_stream_messages[event_id] = [];\n            }\n            pending_stream_messages[event_id].push(_data);\n          }\n          if (_data.msg === \"close_stream\") {\n            close_stream();\n          }\n        };\n        event_stream.onerror = async function(event) {\n          await Promise.all(\n            Object.keys(event_callbacks).map(\n              (event_id) => event_callbacks[event_id]({\n                msg: \"unexpected_error\",\n                message: BROKEN_CONNECTION_MSG\n              })\n            )\n          );\n          close_stream();\n        };\n      }\n      function close_stream() {\n        stream_open = false;\n        event_stream == null ? void 0 : event_stream.close();\n      }\n      async function component_server(component_id, fn_name, data) {\n        var _a;\n        const headers = { \"Content-Type\": \"application/json\" };\n        if (hf_token) {\n          headers.Authorization = `Bearer ${hf_token}`;\n        }\n        let root_url;\n        let component = config.components.find(\n          (comp) => comp.id === component_id\n        );\n        if ((_a = component == null ? void 0 : component.props) == null ? void 0 : _a.root_url) {\n          root_url = component.props.root_url;\n        } else {\n          root_url = config.root;\n        }\n        const response = await fetch_implementation(\n          `${root_url}/component_server/`,\n          {\n            method: \"POST\",\n            body: JSON.stringify({\n              data,\n              component_id,\n              fn_name,\n              session_hash\n            }),\n            headers\n          }\n        );\n        if (!response.ok) {\n          throw new Error(\n            \"Could not connect to component server: \" + response.statusText\n          );\n        }\n        const output = await response.json();\n        return output;\n      }\n      async function view_api(config2) {\n        if (api)\n          return api;\n        const headers = { \"Content-Type\": \"application/json\" };\n        if (hf_token) {\n          headers.Authorization = `Bearer ${hf_token}`;\n        }\n        let response;\n        if (semiver(config2.version || \"2.0.0\", \"3.30\") < 0) {\n          response = await fetch_implementation(\n            \"https://gradio-space-api-fetcher-v2.hf.space/api\",\n            {\n              method: \"POST\",\n              body: JSON.stringify({\n                serialize: false,\n                config: JSON.stringify(config2)\n              }),\n              headers\n            }\n          );\n        } else {\n          response = await fetch_implementation(`${config2.root}/info`, {\n            headers\n          });\n        }\n        if (!response.ok) {\n          throw new Error(BROKEN_CONNECTION_MSG);\n        }\n        let api_info = await response.json();\n        if (\"api\" in api_info) {\n          api_info = api_info.api;\n        }\n        if (api_info.named_endpoints[\"/predict\"] && !api_info.unnamed_endpoints[\"0\"]) {\n          api_info.unnamed_endpoints[0] = api_info.named_endpoints[\"/predict\"];\n        }\n        const x = transform_api_info(api_info, config2, api_map);\n        return x;\n      }\n    });\n  }\n  async function handle_blob2(endpoint, data, api_info, token) {\n    const blob_refs = await walk_and_store_blobs(\n      data,\n      void 0,\n      [],\n      true,\n      api_info\n    );\n    return Promise.all(\n      blob_refs.map(async ({ path, blob, type }) => {\n        if (blob) {\n          const file_url = (await upload_files2(endpoint, [blob], token)).files[0];\n          return { path, file_url, type, name: blob == null ? void 0 : blob.name };\n        }\n        return { path, type };\n      })\n    ).then((r) => {\n      r.forEach(({ path, file_url, type, name }) => {\n        if (type === \"Gallery\") {\n          update_object(data, file_url, path);\n        } else if (file_url) {\n          const file = new FileData({ path: file_url, orig_name: name });\n          update_object(data, file, path);\n        }\n      });\n      return data;\n    });\n  }\n}\nconst { post_data, upload_files, client, handle_blob } = api_factory(\n  fetch,\n  (...args) => new EventSource(...args)\n);\nfunction get_type(type, component, serializer, signature_type) {\n  switch (type.type) {\n    case \"string\":\n      return \"string\";\n    case \"boolean\":\n      return \"boolean\";\n    case \"number\":\n      return \"number\";\n  }\n  if (serializer === \"JSONSerializable\" || serializer === \"StringSerializable\") {\n    return \"any\";\n  } else if (serializer === \"ListStringSerializable\") {\n    return \"string[]\";\n  } else if (component === \"Image\") {\n    return signature_type === \"parameter\" ? \"Blob | File | Buffer\" : \"string\";\n  } else if (serializer === \"FileSerializable\") {\n    if ((type == null ? void 0 : type.type) === \"array\") {\n      return signature_type === \"parameter\" ? \"(Blob | File | Buffer)[]\" : `{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}[]`;\n    }\n    return signature_type === \"parameter\" ? \"Blob | File | Buffer\" : `{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}`;\n  } else if (serializer === \"GallerySerializable\") {\n    return signature_type === \"parameter\" ? \"[(Blob | File | Buffer), (string | null)][]\" : `[{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}, (string | null))][]`;\n  }\n}\nfunction get_description(type, serializer) {\n  if (serializer === \"GallerySerializable\") {\n    return \"array of [file, label] tuples\";\n  } else if (serializer === \"ListStringSerializable\") {\n    return \"array of strings\";\n  } else if (serializer === \"FileSerializable\") {\n    return \"array of files or single file\";\n  }\n  return type.description;\n}\nfunction transform_api_info(api_info, config, api_map) {\n  const new_data = {\n    named_endpoints: {},\n    unnamed_endpoints: {}\n  };\n  for (const key in api_info) {\n    const cat = api_info[key];\n    for (const endpoint in cat) {\n      const dep_index = config.dependencies[endpoint] ? endpoint : api_map[endpoint.replace(\"/\", \"\")];\n      const info = cat[endpoint];\n      new_data[key][endpoint] = {};\n      new_data[key][endpoint].parameters = {};\n      new_data[key][endpoint].returns = {};\n      new_data[key][endpoint].type = config.dependencies[dep_index].types;\n      new_data[key][endpoint].parameters = info.parameters.map(\n        ({ label, component, type, serializer }) => ({\n          label,\n          component,\n          type: get_type(type, component, serializer, \"parameter\"),\n          description: get_description(type, serializer)\n        })\n      );\n      new_data[key][endpoint].returns = info.returns.map(\n        ({ label, component, type, serializer }) => ({\n          label,\n          component,\n          type: get_type(type, component, serializer, \"return\"),\n          description: get_description(type, serializer)\n        })\n      );\n    }\n  }\n  return new_data;\n}\nasync function get_jwt(space, token) {\n  try {\n    const r = await fetch(`https://huggingface.co/api/spaces/${space}/jwt`, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n    const jwt = (await r.json()).token;\n    return jwt || false;\n  } catch (e) {\n    console.error(e);\n    return false;\n  }\n}\nfunction update_object(object, newValue, stack) {\n  while (stack.length > 1) {\n    object = object[stack.shift()];\n  }\n  object[stack.shift()] = newValue;\n}\nasync function walk_and_store_blobs(param, type = void 0, path = [], root = false, api_info = void 0) {\n  if (Array.isArray(param)) {\n    let blob_refs = [];\n    await Promise.all(\n      param.map(async (v, i) => {\n        var _a;\n        let new_path = path.slice();\n        new_path.push(i);\n        const array_refs = await walk_and_store_blobs(\n          param[i],\n          root ? ((_a = api_info == null ? void 0 : api_info.parameters[i]) == null ? void 0 : _a.component) || void 0 : type,\n          new_path,\n          false,\n          api_info\n        );\n        blob_refs = blob_refs.concat(array_refs);\n      })\n    );\n    return blob_refs;\n  } else if (globalThis.Buffer && param instanceof globalThis.Buffer) {\n    const is_image = type === \"Image\";\n    return [\n      {\n        path,\n        blob: is_image ? false : new NodeBlob([param]),\n        type\n      }\n    ];\n  } else if (typeof param === \"object\") {\n    let blob_refs = [];\n    for (let key in param) {\n      if (param.hasOwnProperty(key)) {\n        let new_path = path.slice();\n        new_path.push(key);\n        blob_refs = blob_refs.concat(\n          await walk_and_store_blobs(\n            param[key],\n            void 0,\n            new_path,\n            false,\n            api_info\n          )\n        );\n      }\n    }\n    return blob_refs;\n  }\n  return [];\n}\nfunction skip_queue(id, config) {\n  var _a, _b, _c, _d;\n  return !(((_b = (_a = config == null ? void 0 : config.dependencies) == null ? void 0 : _a[id]) == null ? void 0 : _b.queue) === null ? config.enable_queue : (_d = (_c = config == null ? void 0 : config.dependencies) == null ? void 0 : _c[id]) == null ? void 0 : _d.queue) || false;\n}\nasync function resolve_config(fetch_implementation, endpoint, token) {\n  const headers = {};\n  if (token) {\n    headers.Authorization = `Bearer ${token}`;\n  }\n  if (typeof window !== \"undefined\" && window.gradio_config && location.origin !== \"http://localhost:9876\" && !window.gradio_config.dev_mode) {\n    const path = window.gradio_config.root;\n    const config = window.gradio_config;\n    config.root = resolve_root(endpoint, config.root, false);\n    return { ...config, path };\n  } else if (endpoint) {\n    let response = await fetch_implementation(`${endpoint}/config`, {\n      headers\n    });\n    if (response.status === 200) {\n      const config = await response.json();\n      config.path = config.path ?? \"\";\n      config.root = endpoint;\n      return config;\n    }\n    throw new Error(\"Could not get config.\");\n  }\n  throw new Error(\"No config or app endpoint found\");\n}\nasync function check_space_status(id, type, status_callback) {\n  let endpoint = type === \"subdomain\" ? `https://huggingface.co/api/spaces/by-subdomain/${id}` : `https://huggingface.co/api/spaces/${id}`;\n  let response;\n  let _status;\n  try {\n    response = await fetch(endpoint);\n    _status = response.status;\n    if (_status !== 200) {\n      throw new Error();\n    }\n    response = await response.json();\n  } catch (e) {\n    status_callback({\n      status: \"error\",\n      load_status: \"error\",\n      message: \"Could not get space status\",\n      detail: \"NOT_FOUND\"\n    });\n    return;\n  }\n  if (!response || _status !== 200)\n    return;\n  const {\n    runtime: { stage },\n    id: space_name\n  } = response;\n  switch (stage) {\n    case \"STOPPED\":\n    case \"SLEEPING\":\n      status_callback({\n        status: \"sleeping\",\n        load_status: \"pending\",\n        message: \"Space is asleep. Waking it up...\",\n        detail: stage\n      });\n      setTimeout(() => {\n        check_space_status(id, type, status_callback);\n      }, 1e3);\n      break;\n    case \"PAUSED\":\n      status_callback({\n        status: \"paused\",\n        load_status: \"error\",\n        message: \"This space has been paused by the author. If you would like to try this demo, consider duplicating the space.\",\n        detail: stage,\n        discussions_enabled: await discussions_enabled(space_name)\n      });\n      break;\n    case \"RUNNING\":\n    case \"RUNNING_BUILDING\":\n      status_callback({\n        status: \"running\",\n        load_status: \"complete\",\n        message: \"\",\n        detail: stage\n      });\n      break;\n    case \"BUILDING\":\n      status_callback({\n        status: \"building\",\n        load_status: \"pending\",\n        message: \"Space is building...\",\n        detail: stage\n      });\n      setTimeout(() => {\n        check_space_status(id, type, status_callback);\n      }, 1e3);\n      break;\n    default:\n      status_callback({\n        status: \"space_error\",\n        load_status: \"error\",\n        message: \"This space is experiencing an issue.\",\n        detail: stage,\n        discussions_enabled: await discussions_enabled(space_name)\n      });\n      break;\n  }\n}\nfunction handle_message(data, last_status) {\n  const queue = true;\n  switch (data.msg) {\n    case \"send_data\":\n      return { type: \"data\" };\n    case \"send_hash\":\n      return { type: \"hash\" };\n    case \"queue_full\":\n      return {\n        type: \"update\",\n        status: {\n          queue,\n          message: QUEUE_FULL_MSG,\n          stage: \"error\",\n          code: data.code,\n          success: data.success\n        }\n      };\n    case \"heartbeat\":\n      return {\n        type: \"heartbeat\"\n      };\n    case \"unexpected_error\":\n      return {\n        type: \"unexpected_error\",\n        status: {\n          queue,\n          message: data.message,\n          stage: \"error\",\n          success: false\n        }\n      };\n    case \"estimation\":\n      return {\n        type: \"update\",\n        status: {\n          queue,\n          stage: last_status || \"pending\",\n          code: data.code,\n          size: data.queue_size,\n          position: data.rank,\n          eta: data.rank_eta,\n          success: data.success\n        }\n      };\n    case \"progress\":\n      return {\n        type: \"update\",\n        status: {\n          queue,\n          stage: \"pending\",\n          code: data.code,\n          progress_data: data.progress_data,\n          success: data.success\n        }\n      };\n    case \"log\":\n      return { type: \"log\", data };\n    case \"process_generating\":\n      return {\n        type: \"generating\",\n        status: {\n          queue,\n          message: !data.success ? data.output.error : null,\n          stage: data.success ? \"generating\" : \"error\",\n          code: data.code,\n          progress_data: data.progress_data,\n          eta: data.average_duration\n        },\n        data: data.success ? data.output : null\n      };\n    case \"process_completed\":\n      if (\"error\" in data.output) {\n        return {\n          type: \"update\",\n          status: {\n            queue,\n            message: data.output.error,\n            stage: \"error\",\n            code: data.code,\n            success: data.success\n          }\n        };\n      }\n      return {\n        type: \"complete\",\n        status: {\n          queue,\n          message: !data.success ? data.output.error : void 0,\n          stage: data.success ? \"complete\" : \"error\",\n          code: data.code,\n          progress_data: data.progress_data\n        },\n        data: data.success ? data.output : null\n      };\n    case \"process_starts\":\n      return {\n        type: \"update\",\n        status: {\n          queue,\n          stage: \"pending\",\n          code: data.code,\n          size: data.rank,\n          position: 0,\n          success: data.success,\n          eta: data.eta\n        }\n      };\n  }\n  return { type: \"none\", status: { stage: \"error\", queue } };\n}\nexport {\n  FileData,\n  api_factory,\n  client,\n  duplicate,\n  post_data,\n  prepare_files,\n  upload,\n  upload_files\n};\n", "let supports_adopted_stylesheets = false;\n\nif (\n\t\"attachShadow\" in Element.prototype &&\n\t\"adoptedStyleSheets\" in Document.prototype\n) {\n\t// Both Shadow DOM and adoptedStyleSheets are supported\n\tconst shadow_root_test = document\n\t\t.createElement(\"div\")\n\t\t.attachShadow({ mode: \"open\" });\n\tsupports_adopted_stylesheets = \"adoptedStyleSheets\" in shadow_root_test;\n}\n\nexport function mount_css(url: string, target: HTMLElement): Promise<void> {\n\tconst base = new URL(import.meta.url).origin;\n\tconst _url = new URL(url, base).href;\n\tconst existing_link = document.querySelector(`link[href='${_url}']`);\n\n\tif (existing_link) return Promise.resolve();\n\n\tconst link = document.createElement(\"link\");\n\tlink.rel = \"stylesheet\";\n\tlink.href = _url;\n\n\treturn new Promise((res, rej) => {\n\t\tlink.addEventListener(\"load\", () => res());\n\t\tlink.addEventListener(\"error\", () => {\n\t\t\tconsole.error(`Unable to preload CSS for ${_url}`);\n\t\t\tres();\n\t\t});\n\t\ttarget.appendChild(link);\n\t});\n}\n\nexport function prefix_css(\n\tstring: string,\n\tversion: string,\n\tstyle_element = document.createElement(\"style\")\n): HTMLStyleElement | null {\n\tif (!supports_adopted_stylesheets) return null;\n\tstyle_element.remove();\n\n\tconst stylesheet = new CSSStyleSheet();\n\tstylesheet.replaceSync(string);\n\n\tlet importString = \"\";\n\tstring = string.replace(/@import\\s+url\\((.*?)\\);\\s*/g, (match, url) => {\n\t\timportString += `@import url(${url});\\n`;\n\t\treturn \"\"; // remove and store any @import statements from the CSS\n\t});\n\n\tconst rules = stylesheet.cssRules;\n\n\tlet css_string = \"\";\n\tlet gradio_css_infix = `gradio-app .gradio-container.gradio-container-${version} .contain `;\n\n\tfor (let i = 0; i < rules.length; i++) {\n\t\tconst rule = rules[i];\n\n\t\tlet is_dark_rule = rule.cssText.includes(\".dark\");\n\t\tif (rule instanceof CSSStyleRule) {\n\t\t\tconst selector = rule.selectorText;\n\t\t\tif (selector) {\n\t\t\t\tconst new_selector = selector\n\t\t\t\t\t.replace(\".dark\", \"\")\n\t\t\t\t\t.split(\",\")\n\t\t\t\t\t.map(\n\t\t\t\t\t\t(s) =>\n\t\t\t\t\t\t\t`${is_dark_rule ? \".dark\" : \"\"} ${gradio_css_infix} ${s.trim()} `\n\t\t\t\t\t)\n\t\t\t\t\t.join(\",\");\n\n\t\t\t\tcss_string += rule.cssText;\n\t\t\t\tcss_string += rule.cssText.replace(selector, new_selector);\n\t\t\t}\n\t\t} else if (rule instanceof CSSMediaRule) {\n\t\t\tlet mediaCssString = `@media ${rule.media.mediaText} {`;\n\t\t\tfor (let j = 0; j < rule.cssRules.length; j++) {\n\t\t\t\tconst innerRule = rule.cssRules[j];\n\t\t\t\tif (innerRule instanceof CSSStyleRule) {\n\t\t\t\t\tlet is_dark_rule = innerRule.cssText.includes(\".dark \");\n\t\t\t\t\tconst selector = innerRule.selectorText;\n\t\t\t\t\tconst new_selector = selector\n\t\t\t\t\t\t.replace(\".dark\", \"\")\n\t\t\t\t\t\t.split(\",\")\n\t\t\t\t\t\t.map(\n\t\t\t\t\t\t\t(s) =>\n\t\t\t\t\t\t\t\t`${\n\t\t\t\t\t\t\t\t\tis_dark_rule ? \".dark\" : \"\"\n\t\t\t\t\t\t\t\t} ${gradio_css_infix} ${s.trim()} `\n\t\t\t\t\t\t)\n\t\t\t\t\t\t.join(\",\");\n\t\t\t\t\tmediaCssString += innerRule.cssText.replace(selector, new_selector);\n\t\t\t\t}\n\t\t\t}\n\t\t\tmediaCssString += \"}\";\n\t\t\tcss_string += mediaCssString;\n\t\t} else if (rule instanceof CSSKeyframesRule) {\n\t\t\tcss_string += `@keyframes ${rule.name} {`;\n\t\t\tfor (let j = 0; j < rule.cssRules.length; j++) {\n\t\t\t\tconst innerRule = rule.cssRules[j];\n\t\t\t\tif (innerRule instanceof CSSKeyframeRule) {\n\t\t\t\t\tcss_string += `${innerRule.keyText} { ${innerRule.style.cssText} }`;\n\t\t\t\t}\n\t\t\t}\n\t\t\tcss_string += \"}\";\n\t\t} else if (rule instanceof CSSFontFaceRule) {\n\t\t\tcss_string += `@font-face { ${rule.style.cssText} }`;\n\t\t}\n\t}\n\tcss_string = importString + css_string;\n\tstyle_element.textContent = css_string;\n\n\tdocument.head.appendChild(style_element);\n\treturn style_element;\n}\n", "import \"@gradio/theme/src/reset.css\";\nimport \"@gradio/theme/src/global.css\";\nimport \"@gradio/theme/src/pollen.css\";\nimport \"@gradio/theme/src/typography.css\";\nimport { client, upload_files } from \"@gradio/client\";\nimport { mount_css } from \"./css\";\nimport type Index from \"./Index.svelte\";\n\nimport type { ThemeMode } from \"./types\";\n\n//@ts-ignore\nimport * as svelte from \"./svelte/svelte.js\";\n\ndeclare let BUILD_MODE: string;\ndeclare let GRADIO_VERSION: string;\n\nconst ENTRY_CSS = \"__ENTRY_CSS__\";\n\nlet FONTS: string | [];\n\nFONTS = \"__FONTS_CSS__\";\n\nlet IndexComponent: typeof Index;\nlet _res: (value?: unknown) => void;\nlet pending = new Promise((res) => {\n\t_res = res;\n});\nasync function get_index(): Promise<void> {\n\tIndexComponent = (await import(\"./Index.svelte\")).default;\n\t_res();\n}\n\nfunction create_custom_element(): void {\n\tconst o = {\n\t\tSvelteComponent: svelte.SvelteComponent\n\t};\n\tfor (const key in svelte) {\n\t\tif (key === \"SvelteComponent\") continue;\n\t\tif (key === \"SvelteComponentDev\") {\n\t\t\t//@ts-ignore\n\t\t\to[key] = o[\"SvelteComponent\"];\n\t\t} else {\n\t\t\t//@ts-ignore\n\t\t\to[key] = svelte[key];\n\t\t}\n\t}\n\t//@ts-ignore\n\twindow.__gradio__svelte__internal = o;\n\tclass GradioApp extends HTMLElement {\n\t\tcontrol_page_title: string | null;\n\t\tinitial_height: string;\n\t\tis_embed: string;\n\t\tcontainer: string;\n\t\tinfo: string | true;\n\t\tautoscroll: string | null;\n\t\teager: string | null;\n\t\ttheme_mode: ThemeMode | null;\n\t\thost: string | null;\n\t\tspace: string | null;\n\t\tsrc: string | null;\n\t\tapp?: Index;\n\t\tloading: boolean;\n\t\tupdating: { name: string; value: string } | false;\n\n\t\tconstructor() {\n\t\t\tsuper();\n\t\t\tthis.host = this.getAttribute(\"host\");\n\t\t\tthis.space = this.getAttribute(\"space\");\n\t\t\tthis.src = this.getAttribute(\"src\");\n\n\t\t\tthis.control_page_title = this.getAttribute(\"control_page_title\");\n\t\t\tthis.initial_height = this.getAttribute(\"initial_height\") ?? \"300px\"; // default: 300px\n\t\t\tthis.is_embed = this.getAttribute(\"embed\") ?? \"true\"; // default: true\n\t\t\tthis.container = this.getAttribute(\"container\") ?? \"true\"; // default: true\n\t\t\tthis.info = this.getAttribute(\"info\") ?? true; // default: true\n\t\t\tthis.autoscroll = this.getAttribute(\"autoscroll\");\n\t\t\tthis.eager = this.getAttribute(\"eager\");\n\t\t\tthis.theme_mode = this.getAttribute(\"theme_mode\") as ThemeMode | null;\n\t\t\tthis.updating = false;\n\t\t\tthis.loading = false;\n\t\t}\n\n\t\tasync connectedCallback(): Promise<void> {\n\t\t\tawait get_index();\n\t\t\tthis.loading = true;\n\n\t\t\tif (this.app) {\n\t\t\t\tthis.app.$destroy();\n\t\t\t}\n\n\t\t\tif (typeof FONTS !== \"string\") {\n\t\t\t\tFONTS.forEach((f) => mount_css(f, document.head));\n\t\t\t}\n\n\t\t\tawait mount_css(ENTRY_CSS, document.head);\n\n\t\t\tconst event = new CustomEvent(\"domchange\", {\n\t\t\t\tbubbles: true,\n\t\t\t\tcancelable: false,\n\t\t\t\tcomposed: true\n\t\t\t});\n\n\t\t\tconst observer = new MutationObserver((mutations) => {\n\t\t\t\tthis.dispatchEvent(event);\n\t\t\t});\n\n\t\t\tobserver.observe(this, { childList: true });\n\n\t\t\tthis.app = new IndexComponent({\n\t\t\t\ttarget: this,\n\t\t\t\tprops: {\n\t\t\t\t\t// embed source\n\t\t\t\t\tspace: this.space ? this.space.trim() : this.space,\n\t\t\t\t\tsrc: this.src ? this.src.trim() : this.src,\n\t\t\t\t\thost: this.host ? this.host.trim() : this.host,\n\t\t\t\t\t// embed info\n\t\t\t\t\tinfo: this.info === \"false\" ? false : true,\n\t\t\t\t\tcontainer: this.container === \"false\" ? false : true,\n\t\t\t\t\tis_embed: this.is_embed === \"false\" ? false : true,\n\t\t\t\t\tinitial_height: this.initial_height,\n\t\t\t\t\teager: this.eager === \"true\" ? true : false,\n\t\t\t\t\t// gradio meta info\n\t\t\t\t\tversion: GRADIO_VERSION,\n\t\t\t\t\ttheme_mode: this.theme_mode,\n\t\t\t\t\t// misc global behaviour\n\t\t\t\t\tautoscroll: this.autoscroll === \"true\" ? true : false,\n\t\t\t\t\tcontrol_page_title: this.control_page_title === \"true\" ? true : false,\n\t\t\t\t\t// injectables\n\t\t\t\t\tclient,\n\t\t\t\t\tupload_files,\n\t\t\t\t\t// for gradio docs\n\t\t\t\t\t// TODO: Remove -- i think this is just for autoscroll behavhiour, app vs embeds\n\t\t\t\t\tapp_mode: window.__gradio_mode__ === \"app\"\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tif (this.updating) {\n\t\t\t\tthis.setAttribute(this.updating.name, this.updating.value);\n\t\t\t}\n\n\t\t\tthis.loading = false;\n\t\t}\n\n\t\tstatic get observedAttributes(): [\"src\", \"space\", \"host\"] {\n\t\t\treturn [\"src\", \"space\", \"host\"];\n\t\t}\n\n\t\tasync attributeChangedCallback(\n\t\t\tname: string,\n\t\t\told_val: string,\n\t\t\tnew_val: string\n\t\t): Promise<void> {\n\t\t\tawait pending;\n\t\t\tif (\n\t\t\t\t(name === \"host\" || name === \"space\" || name === \"src\") &&\n\t\t\t\tnew_val !== old_val\n\t\t\t) {\n\t\t\t\tthis.updating = { name, value: new_val };\n\t\t\t\tif (this.loading) return;\n\n\t\t\t\tif (this.app) {\n\t\t\t\t\tthis.app.$destroy();\n\t\t\t\t}\n\n\t\t\t\tthis.space = null;\n\t\t\t\tthis.host = null;\n\t\t\t\tthis.src = null;\n\n\t\t\t\tif (name === \"host\") {\n\t\t\t\t\tthis.host = new_val;\n\t\t\t\t} else if (name === \"space\") {\n\t\t\t\t\tthis.space = new_val;\n\t\t\t\t} else if (name === \"src\") {\n\t\t\t\t\tthis.src = new_val;\n\t\t\t\t}\n\n\t\t\t\tthis.app = new IndexComponent({\n\t\t\t\t\ttarget: this,\n\t\t\t\t\tprops: {\n\t\t\t\t\t\t// embed source\n\t\t\t\t\t\tspace: this.space ? this.space.trim() : this.space,\n\t\t\t\t\t\tsrc: this.src ? this.src.trim() : this.src,\n\t\t\t\t\t\thost: this.host ? this.host.trim() : this.host,\n\t\t\t\t\t\t// embed info\n\t\t\t\t\t\tinfo: this.info === \"false\" ? false : true,\n\t\t\t\t\t\tcontainer: this.container === \"false\" ? false : true,\n\t\t\t\t\t\tis_embed: this.is_embed === \"false\" ? false : true,\n\t\t\t\t\t\tinitial_height: this.initial_height,\n\t\t\t\t\t\teager: this.eager === \"true\" ? true : false,\n\t\t\t\t\t\t// gradio meta info\n\t\t\t\t\t\tversion: GRADIO_VERSION,\n\t\t\t\t\t\ttheme_mode: this.theme_mode,\n\t\t\t\t\t\t// misc global behaviour\n\t\t\t\t\t\tautoscroll: this.autoscroll === \"true\" ? true : false,\n\t\t\t\t\t\tcontrol_page_title:\n\t\t\t\t\t\t\tthis.control_page_title === \"true\" ? true : false,\n\t\t\t\t\t\t// injectables\n\t\t\t\t\t\tclient,\n\t\t\t\t\t\tupload_files,\n\t\t\t\t\t\t// for gradio docs\n\t\t\t\t\t\t// TODO: Remove -- i think this is just for autoscroll behavhiour, app vs embeds\n\t\t\t\t\t\tapp_mode: window.__gradio_mode__ === \"app\"\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\tthis.updating = false;\n\t\t\t}\n\t\t}\n\t}\n\tif (!customElements.get(\"gradio-app\"))\n\t\tcustomElements.define(\"gradio-app\", GradioApp);\n}\n\ncreate_custom_element();\n"], "file": "assets/index-a80d931b.js"}