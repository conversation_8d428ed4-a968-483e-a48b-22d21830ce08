import{I as c}from"./Image-21c02477.js";/* empty css                                              */import"./file-url-bef2dc1b.js";import"./Index-26cfc80a.js";import"./index-a80d931b.js";import"./svelte/svelte.js";const{SvelteComponent:m,attr:_,check_outros:g,create_component:d,destroy_component:p,detach:b,element:v,group_outros:h,init:k,insert:y,mount_component:w,safe_not_equal:I,toggle_class:i,transition_in:s,transition_out:f}=window.__gradio__svelte__internal;function u(a){let t,n;return t=new c({props:{src:a[0].url,alt:""}}),{c(){d(t.$$.fragment)},m(e,l){w(t,e,l),n=!0},p(e,l){const r={};l&1&&(r.src=e[0].url),t.$set(r)},i(e){n||(s(t.$$.fragment,e),n=!0)},o(e){f(t.$$.fragment,e),n=!1},d(e){p(t,e)}}}function q(a){let t,n,e=a[0]&&u(a);return{c(){t=v("div"),e&&e.c(),_(t,"class","container svelte-a9zvka"),i(t,"table",a[1]==="table"),i(t,"gallery",a[1]==="gallery"),i(t,"selected",a[2]),i(t,"border",a[0])},m(l,r){y(l,t,r),e&&e.m(t,null),n=!0},p(l,[r]){l[0]?e?(e.p(l,r),r&1&&s(e,1)):(e=u(l),e.c(),s(e,1),e.m(t,null)):e&&(h(),f(e,1,1,()=>{e=null}),g()),(!n||r&2)&&i(t,"table",l[1]==="table"),(!n||r&2)&&i(t,"gallery",l[1]==="gallery"),(!n||r&4)&&i(t,"selected",l[2]),(!n||r&1)&&i(t,"border",l[0])},i(l){n||(s(e),n=!0)},o(l){f(e),n=!1},d(l){l&&b(t),e&&e.d()}}}function z(a,t,n){let{value:e}=t,{type:l}=t,{selected:r=!1}=t;return a.$$set=o=>{"value"in o&&n(0,e=o.value),"type"in o&&n(1,l=o.type),"selected"in o&&n(2,r=o.selected)},[e,l,r]}class D extends m{constructor(t){super(),k(this,t,z,q,I,{value:0,type:1,selected:2})}}export{D as default};
//# sourceMappingURL=Example-092ffb31.js.map
