import{c as Vs,a as ka}from"./Index-26cfc80a.js";import{P as Un}from"./prism-python-b0b31d02.js";/*! @license DOMPurify 3.0.3 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.0.3/LICENSE */const{entries:Ws,setPrototypeOf:ks,isFrozen:Aa,getPrototypeOf:Sa,getOwnPropertyDescriptor:va}=Object;let{freeze:Ue,seal:c0,create:Fa}=Object,{apply:Zn,construct:Kn}=typeof Reflect<"u"&&Reflect;Zn||(Zn=function(u,c,h){return u.apply(c,h)});Ue||(Ue=function(u){return u});c0||(c0=function(u){return u});Kn||(Kn=function(u,c){return new u(...c)});const Ea=o0(Array.prototype.forEach),As=o0(Array.prototype.pop),gt=o0(Array.prototype.push),Jt=o0(String.prototype.toLowerCase),Gn=o0(String.prototype.toString),Ta=o0(String.prototype.match),u0=o0(String.prototype.replace),Ca=o0(String.prototype.indexOf),Ma=o0(String.prototype.trim),Ze=o0(RegExp.prototype.test),bt=za(TypeError);function o0(F){return function(u){for(var c=arguments.length,h=new Array(c>1?c-1:0),x=1;x<c;x++)h[x-1]=arguments[x];return Zn(F,u,h)}}function za(F){return function(){for(var u=arguments.length,c=new Array(u),h=0;h<u;h++)c[h]=arguments[h];return Kn(F,c)}}function te(F,u,c){var h;c=(h=c)!==null&&h!==void 0?h:Jt,ks&&ks(F,null);let x=u.length;for(;x--;){let f=u[x];if(typeof f=="string"){const D=c(f);D!==f&&(Aa(u)||(u[x]=D),f=D)}F[f]=!0}return F}function tt(F){const u=Fa(null);for(const[c,h]of Ws(F))u[c]=h;return u}function jt(F,u){for(;F!==null;){const h=va(F,u);if(h){if(h.get)return o0(h.get);if(typeof h.value=="function")return o0(h.value)}F=Sa(F)}function c(h){return console.warn("fallback value for",h),null}return c}const Ss=Ue(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),$n=Ue(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Vn=Ue(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Ba=Ue(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Wn=Ue(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),_a=Ue(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),vs=Ue(["#text"]),Fs=Ue(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),Yn=Ue(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Es=Ue(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Zt=Ue(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Na=c0(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Ra=c0(/<%[\w\W]*|[\w\W]*%>/gm),Ia=c0(/\${[\w\W]*}/gm),Oa=c0(/^data-[\-\w.\u00B7-\uFFFF]/),La=c0(/^aria-[\-\w]+$/),Ys=c0(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Ha=c0(/^(?:\w+script|data):/i),qa=c0(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Xs=c0(/^html$/i);var Ts=Object.freeze({__proto__:null,MUSTACHE_EXPR:Na,ERB_EXPR:Ra,TMPLIT_EXPR:Ia,DATA_ATTR:Oa,ARIA_ATTR:La,IS_ALLOWED_URI:Ys,IS_SCRIPT_OR_DATA:Ha,ATTR_WHITESPACE:qa,DOCTYPE_NAME:Xs});const Pa=()=>typeof window>"u"?null:window,Ua=function(u,c){if(typeof u!="object"||typeof u.createPolicy!="function")return null;let h=null;const x="data-tt-policy-suffix";c&&c.hasAttribute(x)&&(h=c.getAttribute(x));const f="dompurify"+(h?"#"+h:"");try{return u.createPolicy(f,{createHTML(D){return D},createScriptURL(D){return D}})}catch{return console.warn("TrustedTypes policy "+f+" could not be created."),null}};function js(){let F=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Pa();const u=G=>js(G);if(u.version="3.0.3",u.removed=[],!F||!F.document||F.document.nodeType!==9)return u.isSupported=!1,u;const c=F.document,h=c.currentScript;let{document:x}=F;const{DocumentFragment:f,HTMLTemplateElement:D,Node:C,Element:B,NodeFilter:$,NamedNodeMap:U=F.NamedNodeMap||F.MozNamedAttrMap,HTMLFormElement:K,DOMParser:Y,trustedTypes:Q}=F,_e=B.prototype,ze=jt(_e,"cloneNode"),We=jt(_e,"nextSibling"),N=jt(_e,"childNodes"),ee=jt(_e,"parentNode");if(typeof D=="function"){const G=x.createElement("template");G.content&&G.content.ownerDocument&&(x=G.content.ownerDocument)}let X,W="";const{implementation:j,createNodeIterator:xe,createDocumentFragment:me,getElementsByTagName:ge}=x,{importNode:Ee}=c;let Te={};u.isSupported=typeof Ws=="function"&&typeof ee=="function"&&j&&j.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:Le,ERB_EXPR:h0,TMPLIT_EXPR:Ne,DATA_ATTR:Qe,ARIA_ATTR:an,IS_SCRIPT_OR_DATA:on,ATTR_WHITESPACE:kt}=Ts;let{IS_ALLOWED_URI:At}=Ts,Be=null;const St=te({},[...Ss,...$n,...Vn,...Wn,...vs]);let q=null;const $0=te({},[...Fs,...Yn,...Es,...Zt]);let ke=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),m0=null,V0=null,w0=!0,nt=!0,vt=!1,Ft=!0,E0=!1,D0=!1,rt=!1,st=!1,T0=!1,W0=!1,L0=!1,Et=!0,C0=!1;const Je="user-content-";let M0=!0,z0=!1,B0={},d0=null;const Y0=te({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Tt=null;const Ct=te({},["audio","video","img","source","image","track"]);let X0=null;const it=te({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Ge="http://www.w3.org/1998/Math/MathML",j0="http://www.w3.org/2000/svg",Ye="http://www.w3.org/1999/xhtml";let _0=Ye,Z0=!1,de=null;const I=te({},[Ge,j0,Ye],Gn);let He;const Mt=["application/xhtml+xml","text/html"],zt="text/html";let Ce,e0=null;const at=x.createElement("form"),Bt=function(w){return w instanceof RegExp||w instanceof Function},ot=function(w){if(!(e0&&e0===w)){if((!w||typeof w!="object")&&(w={}),w=tt(w),He=Mt.indexOf(w.PARSER_MEDIA_TYPE)===-1?He=zt:He=w.PARSER_MEDIA_TYPE,Ce=He==="application/xhtml+xml"?Gn:Jt,Be="ALLOWED_TAGS"in w?te({},w.ALLOWED_TAGS,Ce):St,q="ALLOWED_ATTR"in w?te({},w.ALLOWED_ATTR,Ce):$0,de="ALLOWED_NAMESPACES"in w?te({},w.ALLOWED_NAMESPACES,Gn):I,X0="ADD_URI_SAFE_ATTR"in w?te(tt(it),w.ADD_URI_SAFE_ATTR,Ce):it,Tt="ADD_DATA_URI_TAGS"in w?te(tt(Ct),w.ADD_DATA_URI_TAGS,Ce):Ct,d0="FORBID_CONTENTS"in w?te({},w.FORBID_CONTENTS,Ce):Y0,m0="FORBID_TAGS"in w?te({},w.FORBID_TAGS,Ce):{},V0="FORBID_ATTR"in w?te({},w.FORBID_ATTR,Ce):{},B0="USE_PROFILES"in w?w.USE_PROFILES:!1,w0=w.ALLOW_ARIA_ATTR!==!1,nt=w.ALLOW_DATA_ATTR!==!1,vt=w.ALLOW_UNKNOWN_PROTOCOLS||!1,Ft=w.ALLOW_SELF_CLOSE_IN_ATTR!==!1,E0=w.SAFE_FOR_TEMPLATES||!1,D0=w.WHOLE_DOCUMENT||!1,T0=w.RETURN_DOM||!1,W0=w.RETURN_DOM_FRAGMENT||!1,L0=w.RETURN_TRUSTED_TYPE||!1,st=w.FORCE_BODY||!1,Et=w.SANITIZE_DOM!==!1,C0=w.SANITIZE_NAMED_PROPS||!1,M0=w.KEEP_CONTENT!==!1,z0=w.IN_PLACE||!1,At=w.ALLOWED_URI_REGEXP||Ys,_0=w.NAMESPACE||Ye,ke=w.CUSTOM_ELEMENT_HANDLING||{},w.CUSTOM_ELEMENT_HANDLING&&Bt(w.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(ke.tagNameCheck=w.CUSTOM_ELEMENT_HANDLING.tagNameCheck),w.CUSTOM_ELEMENT_HANDLING&&Bt(w.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(ke.attributeNameCheck=w.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),w.CUSTOM_ELEMENT_HANDLING&&typeof w.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(ke.allowCustomizedBuiltInElements=w.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),E0&&(nt=!1),W0&&(T0=!0),B0&&(Be=te({},[...vs]),q=[],B0.html===!0&&(te(Be,Ss),te(q,Fs)),B0.svg===!0&&(te(Be,$n),te(q,Yn),te(q,Zt)),B0.svgFilters===!0&&(te(Be,Vn),te(q,Yn),te(q,Zt)),B0.mathMl===!0&&(te(Be,Wn),te(q,Es),te(q,Zt))),w.ADD_TAGS&&(Be===St&&(Be=tt(Be)),te(Be,w.ADD_TAGS,Ce)),w.ADD_ATTR&&(q===$0&&(q=tt(q)),te(q,w.ADD_ATTR,Ce)),w.ADD_URI_SAFE_ATTR&&te(X0,w.ADD_URI_SAFE_ATTR,Ce),w.FORBID_CONTENTS&&(d0===Y0&&(d0=tt(d0)),te(d0,w.FORBID_CONTENTS,Ce)),M0&&(Be["#text"]=!0),D0&&te(Be,["html","head","body"]),Be.table&&(te(Be,["tbody"]),delete m0.tbody),w.TRUSTED_TYPES_POLICY){if(typeof w.TRUSTED_TYPES_POLICY.createHTML!="function")throw bt('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof w.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw bt('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');X=w.TRUSTED_TYPES_POLICY,W=X.createHTML("")}else X===void 0&&(X=Ua(Q,h)),X!==null&&typeof W=="string"&&(W=X.createHTML(""));Ue&&Ue(w),e0=w}},qe=te({},["mi","mo","mn","ms","mtext"]),t0=te({},["foreignobject","desc","title","annotation-xml"]),p0=te({},["title","style","font","a","script"]),N0=te({},$n);te(N0,Vn),te(N0,Ba);const K0=te({},Wn);te(K0,_a);const ln=function(w){let z=ee(w);(!z||!z.tagName)&&(z={namespaceURI:_0,tagName:"template"});const O=Jt(w.tagName),ie=Jt(z.tagName);return de[w.namespaceURI]?w.namespaceURI===j0?z.namespaceURI===Ye?O==="svg":z.namespaceURI===Ge?O==="svg"&&(ie==="annotation-xml"||qe[ie]):!!N0[O]:w.namespaceURI===Ge?z.namespaceURI===Ye?O==="math":z.namespaceURI===j0?O==="math"&&t0[ie]:!!K0[O]:w.namespaceURI===Ye?z.namespaceURI===j0&&!t0[ie]||z.namespaceURI===Ge&&!qe[ie]?!1:!K0[O]&&(p0[O]||!N0[O]):!!(He==="application/xhtml+xml"&&de[w.namespaceURI]):!1},k0=function(w){gt(u.removed,{element:w});try{w.parentNode.removeChild(w)}catch{w.remove()}},lt=function(w,z){try{gt(u.removed,{attribute:z.getAttributeNode(w),from:z})}catch{gt(u.removed,{attribute:null,from:z})}if(z.removeAttribute(w),w==="is"&&!q[w])if(T0||W0)try{k0(z)}catch{}else try{z.setAttribute(w,"")}catch{}},H0=function(w){let z,O;if(st)w="<remove></remove>"+w;else{const ve=Ta(w,/^[\r\n\t ]+/);O=ve&&ve[0]}He==="application/xhtml+xml"&&_0===Ye&&(w='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+w+"</body></html>");const ie=X?X.createHTML(w):w;if(_0===Ye)try{z=new Y().parseFromString(ie,He)}catch{}if(!z||!z.documentElement){z=j.createDocument(_0,"template",null);try{z.documentElement.innerHTML=Z0?W:ie}catch{}}const b=z.body||z.documentElement;return w&&O&&b.insertBefore(x.createTextNode(O),b.childNodes[0]||null),_0===Ye?ge.call(z,D0?"html":"body")[0]:D0?z.documentElement:b},be=function(w){return xe.call(w.ownerDocument||w,w,$.SHOW_ELEMENT|$.SHOW_COMMENT|$.SHOW_TEXT,null,!1)},i=function(w){return w instanceof K&&(typeof w.nodeName!="string"||typeof w.textContent!="string"||typeof w.removeChild!="function"||!(w.attributes instanceof U)||typeof w.removeAttribute!="function"||typeof w.setAttribute!="function"||typeof w.namespaceURI!="string"||typeof w.insertBefore!="function"||typeof w.hasChildNodes!="function")},a=function(w){return typeof C=="object"?w instanceof C:w&&typeof w=="object"&&typeof w.nodeType=="number"&&typeof w.nodeName=="string"},M=function(w,z,O){Te[w]&&Ea(Te[w],ie=>{ie.call(u,z,O,e0)})},m=function(w){let z;if(M("beforeSanitizeElements",w,null),i(w))return k0(w),!0;const O=Ce(w.nodeName);if(M("uponSanitizeElement",w,{tagName:O,allowedTags:Be}),w.hasChildNodes()&&!a(w.firstElementChild)&&(!a(w.content)||!a(w.content.firstElementChild))&&Ze(/<[/\w]/g,w.innerHTML)&&Ze(/<[/\w]/g,w.textContent))return k0(w),!0;if(!Be[O]||m0[O]){if(!m0[O]&&pe(O)&&(ke.tagNameCheck instanceof RegExp&&Ze(ke.tagNameCheck,O)||ke.tagNameCheck instanceof Function&&ke.tagNameCheck(O)))return!1;if(M0&&!d0[O]){const ie=ee(w)||w.parentNode,b=N(w)||w.childNodes;if(b&&ie){const ve=b.length;for(let A=ve-1;A>=0;--A)ie.insertBefore(ze(b[A],!0),We(w))}}return k0(w),!0}return w instanceof B&&!ln(w)||(O==="noscript"||O==="noembed")&&Ze(/<\/no(script|embed)/i,w.innerHTML)?(k0(w),!0):(E0&&w.nodeType===3&&(z=w.textContent,z=u0(z,Le," "),z=u0(z,h0," "),z=u0(z,Ne," "),w.textContent!==z&&(gt(u.removed,{element:w.cloneNode()}),w.textContent=z)),M("afterSanitizeElements",w,null),!1)},y=function(w,z,O){if(Et&&(z==="id"||z==="name")&&(O in x||O in at))return!1;if(!(nt&&!V0[z]&&Ze(Qe,z))){if(!(w0&&Ze(an,z))){if(!q[z]||V0[z]){if(!(pe(w)&&(ke.tagNameCheck instanceof RegExp&&Ze(ke.tagNameCheck,w)||ke.tagNameCheck instanceof Function&&ke.tagNameCheck(w))&&(ke.attributeNameCheck instanceof RegExp&&Ze(ke.attributeNameCheck,z)||ke.attributeNameCheck instanceof Function&&ke.attributeNameCheck(z))||z==="is"&&ke.allowCustomizedBuiltInElements&&(ke.tagNameCheck instanceof RegExp&&Ze(ke.tagNameCheck,O)||ke.tagNameCheck instanceof Function&&ke.tagNameCheck(O))))return!1}else if(!X0[z]){if(!Ze(At,u0(O,kt,""))){if(!((z==="src"||z==="xlink:href"||z==="href")&&w!=="script"&&Ca(O,"data:")===0&&Tt[w])){if(!(vt&&!Ze(on,u0(O,kt,"")))){if(O)return!1}}}}}}return!0},pe=function(w){return w.indexOf("-")>0},H=function(w){let z,O,ie,b;M("beforeSanitizeAttributes",w,null);const{attributes:ve}=w;if(!ve)return;const A={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:q};for(b=ve.length;b--;){z=ve[b];const{name:Xe,namespaceURI:Q0}=z;if(O=Xe==="value"?z.value:Ma(z.value),ie=Ce(Xe),A.attrName=ie,A.attrValue=O,A.keepAttr=!0,A.forceKeepAttr=void 0,M("uponSanitizeAttribute",w,A),O=A.attrValue,A.forceKeepAttr||(lt(Xe,w),!A.keepAttr))continue;if(!Ft&&Ze(/\/>/i,O)){lt(Xe,w);continue}E0&&(O=u0(O,Le," "),O=u0(O,h0," "),O=u0(O,Ne," "));const ut=Ce(w.nodeName);if(y(ut,ie,O)){if(C0&&(ie==="id"||ie==="name")&&(lt(Xe,w),O=Je+O),X&&typeof Q=="object"&&typeof Q.getAttributeType=="function"&&!Q0)switch(Q.getAttributeType(ut,ie)){case"TrustedHTML":{O=X.createHTML(O);break}case"TrustedScriptURL":{O=X.createScriptURL(O);break}}try{Q0?w.setAttributeNS(Q0,Xe,O):w.setAttribute(Xe,O),As(u.removed)}catch{}}}M("afterSanitizeAttributes",w,null)},Oe=function G(w){let z;const O=be(w);for(M("beforeSanitizeShadowDOM",w,null);z=O.nextNode();)M("uponSanitizeShadowNode",z,null),!m(z)&&(z.content instanceof f&&G(z.content),H(z));M("afterSanitizeShadowDOM",w,null)};return u.sanitize=function(G){let w=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},z,O,ie,b;if(Z0=!G,Z0&&(G="<!-->"),typeof G!="string"&&!a(G))if(typeof G.toString=="function"){if(G=G.toString(),typeof G!="string")throw bt("dirty is not a string, aborting")}else throw bt("toString is not a function");if(!u.isSupported)return G;if(rt||ot(w),u.removed=[],typeof G=="string"&&(z0=!1),z0){if(G.nodeName){const Xe=Ce(G.nodeName);if(!Be[Xe]||m0[Xe])throw bt("root node is forbidden and cannot be sanitized in-place")}}else if(G instanceof C)z=H0("<!---->"),O=z.ownerDocument.importNode(G,!0),O.nodeType===1&&O.nodeName==="BODY"||O.nodeName==="HTML"?z=O:z.appendChild(O);else{if(!T0&&!E0&&!D0&&G.indexOf("<")===-1)return X&&L0?X.createHTML(G):G;if(z=H0(G),!z)return T0?null:L0?W:""}z&&st&&k0(z.firstChild);const ve=be(z0?G:z);for(;ie=ve.nextNode();)m(ie)||(ie.content instanceof f&&Oe(ie.content),H(ie));if(z0)return G;if(T0){if(W0)for(b=me.call(z.ownerDocument);z.firstChild;)b.appendChild(z.firstChild);else b=z;return(q.shadowroot||q.shadowrootmod)&&(b=Ee.call(c,b,!0)),b}let A=D0?z.outerHTML:z.innerHTML;return D0&&Be["!doctype"]&&z.ownerDocument&&z.ownerDocument.doctype&&z.ownerDocument.doctype.name&&Ze(Xs,z.ownerDocument.doctype.name)&&(A="<!DOCTYPE "+z.ownerDocument.doctype.name+`>
`+A),E0&&(A=u0(A,Le," "),A=u0(A,h0," "),A=u0(A,Ne," ")),X&&L0?X.createHTML(A):A},u.setConfig=function(G){ot(G),rt=!0},u.clearConfig=function(){e0=null,rt=!1},u.isValidAttribute=function(G,w,z){e0||ot({});const O=Ce(G),ie=Ce(w);return y(O,ie,z)},u.addHook=function(G,w){typeof w=="function"&&(Te[G]=Te[G]||[],gt(Te[G],w))},u.removeHook=function(G){if(Te[G])return As(Te[G])},u.removeHooks=function(G){Te[G]&&(Te[G]=[])},u.removeAllHooks=function(){Te={}},u}var Cs=js(),Zs={exports:{}},Xn={exports:{}},Ms;function Ga(){return Ms||(Ms=1,function(F,u){(function(h,x){F.exports=x()})(typeof self<"u"?self:Vs,function(){return function(){var c={};(function(){c.d=function(t,e){for(var n in e)c.o(e,n)&&!c.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}})(),function(){c.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}}();var h={};c.d(h,{default:function(){return xa}});class x{constructor(e,n){this.name=void 0,this.position=void 0,this.length=void 0,this.rawMessage=void 0;let r="KaTeX parse error: "+e,s,o;const l=n&&n.loc;if(l&&l.start<=l.end){const g=l.lexer.input;s=l.start,o=l.end,s===g.length?r+=" at end of input: ":r+=" at position "+(s+1)+": ";const k=g.slice(s,o).replace(/[^]/g,"$&̲");let S;s>15?S="…"+g.slice(s-15,s):S=g.slice(0,s);let v;o+15<g.length?v=g.slice(o,o+15)+"…":v=g.slice(o),r+=S+k+v}const d=new Error(r);return d.name="ParseError",d.__proto__=x.prototype,d.position=s,s!=null&&o!=null&&(d.length=o-s),d.rawMessage=e,d}}x.prototype.__proto__=Error.prototype;var f=x;const D=function(t,e){return t.indexOf(e)!==-1},C=function(t,e){return t===void 0?e:t},B=/([A-Z])/g,$=function(t){return t.replace(B,"-$1").toLowerCase()},U={"&":"&amp;",">":"&gt;","<":"&lt;",'"':"&quot;","'":"&#x27;"},K=/[&><"']/g;function Y(t){return String(t).replace(K,e=>U[e])}const Q=function(t){return t.type==="ordgroup"||t.type==="color"?t.body.length===1?Q(t.body[0]):t:t.type==="font"?Q(t.body):t},_e=function(t){const e=Q(t);return e.type==="mathord"||e.type==="textord"||e.type==="atom"},ze=function(t){if(!t)throw new Error("Expected non-null, but got "+String(t));return t};var N={contains:D,deflt:C,escape:Y,hyphenate:$,getBaseElem:Q,isCharacterBox:_e,protocolFromUrl:function(t){const e=/^[\x00-\x20]*([^\\/#?]*?)(:|&#0*58|&#x0*3a|&colon)/i.exec(t);return e?e[2]!==":"||!/^[a-zA-Z][a-zA-Z0-9+\-.]*$/.test(e[1])?null:e[1].toLowerCase():"_relative"}};const ee={displayMode:{type:"boolean",description:"Render math in display mode, which puts the math in display style (so \\int and \\sum are large, for example), and centers the math on the page on its own line.",cli:"-d, --display-mode"},output:{type:{enum:["htmlAndMathml","html","mathml"]},description:"Determines the markup language of the output.",cli:"-F, --format <type>"},leqno:{type:"boolean",description:"Render display math in leqno style (left-justified tags)."},fleqn:{type:"boolean",description:"Render display math flush left."},throwOnError:{type:"boolean",default:!0,cli:"-t, --no-throw-on-error",cliDescription:"Render errors (in the color given by --error-color) instead of throwing a ParseError exception when encountering an error."},errorColor:{type:"string",default:"#cc0000",cli:"-c, --error-color <color>",cliDescription:"A color string given in the format 'rgb' or 'rrggbb' (no #). This option determines the color of errors rendered by the -t option.",cliProcessor:t=>"#"+t},macros:{type:"object",cli:"-m, --macro <def>",cliDescription:"Define custom macro of the form '\\foo:expansion' (use multiple -m arguments for multiple macros).",cliDefault:[],cliProcessor:(t,e)=>(e.push(t),e)},minRuleThickness:{type:"number",description:"Specifies a minimum thickness, in ems, for fraction lines, `\\sqrt` top lines, `{array}` vertical lines, `\\hline`, `\\hdashline`, `\\underline`, `\\overline`, and the borders of `\\fbox`, `\\boxed`, and `\\fcolorbox`.",processor:t=>Math.max(0,t),cli:"--min-rule-thickness <size>",cliProcessor:parseFloat},colorIsTextColor:{type:"boolean",description:"Makes \\color behave like LaTeX's 2-argument \\textcolor, instead of LaTeX's one-argument \\color mode change.",cli:"-b, --color-is-text-color"},strict:{type:[{enum:["warn","ignore","error"]},"boolean","function"],description:"Turn on strict / LaTeX faithfulness mode, which throws an error if the input uses features that are not supported by LaTeX.",cli:"-S, --strict",cliDefault:!1},trust:{type:["boolean","function"],description:"Trust the input, enabling all HTML features such as \\url.",cli:"-T, --trust"},maxSize:{type:"number",default:1/0,description:"If non-zero, all user-specified sizes, e.g. in \\rule{500em}{500em}, will be capped to maxSize ems. Otherwise, elements and spaces can be arbitrarily large",processor:t=>Math.max(0,t),cli:"-s, --max-size <n>",cliProcessor:parseInt},maxExpand:{type:"number",default:1e3,description:"Limit the number of macro expansions to the specified number, to prevent e.g. infinite macro loops. If set to Infinity, the macro expander will try to fully expand as in LaTeX.",processor:t=>Math.max(0,t),cli:"-e, --max-expand <n>",cliProcessor:t=>t==="Infinity"?1/0:parseInt(t)},globalGroup:{type:"boolean",cli:!1}};function X(t){if(t.default)return t.default;const e=t.type,n=Array.isArray(e)?e[0]:e;if(typeof n!="string")return n.enum[0];switch(n){case"boolean":return!1;case"string":return"";case"number":return 0;case"object":return{}}}class W{constructor(e){this.displayMode=void 0,this.output=void 0,this.leqno=void 0,this.fleqn=void 0,this.throwOnError=void 0,this.errorColor=void 0,this.macros=void 0,this.minRuleThickness=void 0,this.colorIsTextColor=void 0,this.strict=void 0,this.trust=void 0,this.maxSize=void 0,this.maxExpand=void 0,this.globalGroup=void 0,e=e||{};for(const n in ee)if(ee.hasOwnProperty(n)){const r=ee[n];this[n]=e[n]!==void 0?r.processor?r.processor(e[n]):e[n]:X(r)}}reportNonstrict(e,n,r){let s=this.strict;if(typeof s=="function"&&(s=s(e,n,r)),!(!s||s==="ignore")){if(s===!0||s==="error")throw new f("LaTeX-incompatible input and strict mode is set to 'error': "+(n+" ["+e+"]"),r);s==="warn"?typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(n+" ["+e+"]")):typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+s+"': "+n+" ["+e+"]"))}}useStrictBehavior(e,n,r){let s=this.strict;if(typeof s=="function")try{s=s(e,n,r)}catch{s="error"}return!s||s==="ignore"?!1:s===!0||s==="error"?!0:s==="warn"?(typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(n+" ["+e+"]")),!1):(typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+s+"': "+n+" ["+e+"]")),!1)}isTrusted(e){if(e.url&&!e.protocol){const r=N.protocolFromUrl(e.url);if(r==null)return!1;e.protocol=r}return!!(typeof this.trust=="function"?this.trust(e):this.trust)}}class j{constructor(e,n,r){this.id=void 0,this.size=void 0,this.cramped=void 0,this.id=e,this.size=n,this.cramped=r}sup(){return Qe[an[this.id]]}sub(){return Qe[on[this.id]]}fracNum(){return Qe[kt[this.id]]}fracDen(){return Qe[At[this.id]]}cramp(){return Qe[Be[this.id]]}text(){return Qe[St[this.id]]}isTight(){return this.size>=2}}const xe=0,me=1,ge=2,Ee=3,Te=4,Le=5,h0=6,Ne=7,Qe=[new j(xe,0,!1),new j(me,0,!0),new j(ge,1,!1),new j(Ee,1,!0),new j(Te,2,!1),new j(Le,2,!0),new j(h0,3,!1),new j(Ne,3,!0)],an=[Te,Le,Te,Le,h0,Ne,h0,Ne],on=[Le,Le,Le,Le,Ne,Ne,Ne,Ne],kt=[ge,Ee,Te,Le,h0,Ne,h0,Ne],At=[Ee,Ee,Le,Le,Ne,Ne,Ne,Ne],Be=[me,me,Ee,Ee,Le,Le,Ne,Ne],St=[xe,me,ge,Ee,ge,Ee,ge,Ee];var q={DISPLAY:Qe[xe],TEXT:Qe[ge],SCRIPT:Qe[Te],SCRIPTSCRIPT:Qe[h0]};const $0=[{name:"latin",blocks:[[256,591],[768,879]]},{name:"cyrillic",blocks:[[1024,1279]]},{name:"armenian",blocks:[[1328,1423]]},{name:"brahmic",blocks:[[2304,4255]]},{name:"georgian",blocks:[[4256,4351]]},{name:"cjk",blocks:[[12288,12543],[19968,40879],[65280,65376]]},{name:"hangul",blocks:[[44032,55215]]}];function ke(t){for(let e=0;e<$0.length;e++){const n=$0[e];for(let r=0;r<n.blocks.length;r++){const s=n.blocks[r];if(t>=s[0]&&t<=s[1])return n.name}}return null}const m0=[];$0.forEach(t=>t.blocks.forEach(e=>m0.push(...e)));function V0(t){for(let e=0;e<m0.length;e+=2)if(t>=m0[e]&&t<=m0[e+1])return!0;return!1}const w0=80,nt=function(t,e){return"M95,"+(622+t+e)+`
c-2.7,0,-7.17,-2.7,-13.5,-8c-5.8,-5.3,-9.5,-10,-9.5,-14
c0,-2,0.3,-3.3,1,-4c1.3,-2.7,23.83,-20.7,67.5,-54
c44.2,-33.3,65.8,-50.3,66.5,-51c1.3,-1.3,3,-2,5,-2c4.7,0,8.7,3.3,12,10
s173,378,173,378c0.7,0,35.3,-71,104,-213c68.7,-142,137.5,-285,206.5,-429
c69,-144,104.5,-217.7,106.5,-221
l`+t/2.075+" -"+t+`
c5.3,-9.3,12,-14,20,-14
H400000v`+(40+t)+`H845.2724
s-225.272,467,-225.272,467s-235,486,-235,486c-2.7,4.7,-9,7,-19,7
c-6,0,-10,-1,-12,-3s-194,-422,-194,-422s-65,47,-65,47z
M`+(834+t)+" "+e+"h400000v"+(40+t)+"h-400000z"},vt=function(t,e){return"M263,"+(601+t+e)+`c0.7,0,18,39.7,52,119
c34,79.3,68.167,158.7,102.5,238c34.3,79.3,51.8,119.3,52.5,120
c340,-704.7,510.7,-1060.3,512,-1067
l`+t/2.084+" -"+t+`
c4.7,-7.3,11,-11,19,-11
H40000v`+(40+t)+`H1012.3
s-271.3,567,-271.3,567c-38.7,80.7,-84,175,-136,283c-52,108,-89.167,185.3,-111.5,232
c-22.3,46.7,-33.8,70.3,-34.5,71c-4.7,4.7,-12.3,7,-23,7s-12,-1,-12,-1
s-109,-253,-109,-253c-72.7,-168,-109.3,-252,-110,-252c-10.7,8,-22,16.7,-34,26
c-22,17.3,-33.3,26,-34,26s-26,-26,-26,-26s76,-59,76,-59s76,-60,76,-60z
M`+(1001+t)+" "+e+"h400000v"+(40+t)+"h-400000z"},Ft=function(t,e){return"M983 "+(10+t+e)+`
l`+t/3.13+" -"+t+`
c4,-6.7,10,-10,18,-10 H400000v`+(40+t)+`
H1013.1s-83.4,268,-264.1,840c-180.7,572,-277,876.3,-289,913c-4.7,4.7,-12.7,7,-24,7
s-12,0,-12,0c-1.3,-3.3,-3.7,-11.7,-7,-25c-35.3,-125.3,-106.7,-373.3,-214,-744
c-10,12,-21,25,-33,39s-32,39,-32,39c-6,-5.3,-15,-14,-27,-26s25,-30,25,-30
c26.7,-32.7,52,-63,76,-91s52,-60,52,-60s208,722,208,722
c56,-175.3,126.3,-397.3,211,-666c84.7,-268.7,153.8,-488.2,207.5,-658.5
c53.7,-170.3,84.5,-266.8,92.5,-289.5z
M`+(1001+t)+" "+e+"h400000v"+(40+t)+"h-400000z"},E0=function(t,e){return"M424,"+(2398+t+e)+`
c-1.3,-0.7,-38.5,-172,-111.5,-514c-73,-342,-109.8,-513.3,-110.5,-514
c0,-2,-10.7,14.3,-32,49c-4.7,7.3,-9.8,15.7,-15.5,25c-5.7,9.3,-9.8,16,-12.5,20
s-5,7,-5,7c-4,-3.3,-8.3,-7.7,-13,-13s-13,-13,-13,-13s76,-122,76,-122s77,-121,77,-121
s209,968,209,968c0,-2,84.7,-361.7,254,-1079c169.3,-717.3,254.7,-1077.7,256,-1081
l`+t/4.223+" -"+t+`c4,-6.7,10,-10,18,-10 H400000
v`+(40+t)+`H1014.6
s-87.3,378.7,-272.6,1166c-185.3,787.3,-279.3,1182.3,-282,1185
c-2,6,-10,9,-24,9
c-8,0,-12,-0.7,-12,-2z M`+(1001+t)+" "+e+`
h400000v`+(40+t)+"h-400000z"},D0=function(t,e){return"M473,"+(2713+t+e)+`
c339.3,-1799.3,509.3,-2700,510,-2702 l`+t/5.298+" -"+t+`
c3.3,-7.3,9.3,-11,18,-11 H400000v`+(40+t)+`H1017.7
s-90.5,478,-276.2,1466c-185.7,988,-279.5,1483,-281.5,1485c-2,6,-10,9,-24,9
c-8,0,-12,-0.7,-12,-2c0,-1.3,-5.3,-32,-16,-92c-50.7,-293.3,-119.7,-693.3,-207,-1200
c0,-1.3,-5.3,8.7,-16,30c-10.7,21.3,-21.3,42.7,-32,64s-16,33,-16,33s-26,-26,-26,-26
s76,-153,76,-153s77,-151,77,-151c0.7,0.7,35.7,202,105,604c67.3,400.7,102,602.7,104,
606zM`+(1001+t)+" "+e+"h400000v"+(40+t)+"H1017.7z"},rt=function(t){const e=t/2;return"M400000 "+t+" H0 L"+e+" 0 l65 45 L145 "+(t-80)+" H400000z"},st=function(t,e,n){const r=n-54-e-t;return"M702 "+(t+e)+"H400000"+(40+t)+`
H742v`+r+`l-4 4-4 4c-.667.7 -2 1.5-4 2.5s-4.167 1.833-6.5 2.5-5.5 1-9.5 1
h-12l-28-84c-16.667-52-96.667 -294.333-240-727l-212 -643 -85 170
c-4-3.333-8.333-7.667-13 -13l-13-13l77-155 77-156c66 199.333 139 419.667
219 661 l218 661zM702 `+e+"H400000v"+(40+t)+"H742z"},T0=function(t,e,n){e=1e3*e;let r="";switch(t){case"sqrtMain":r=nt(e,w0);break;case"sqrtSize1":r=vt(e,w0);break;case"sqrtSize2":r=Ft(e,w0);break;case"sqrtSize3":r=E0(e,w0);break;case"sqrtSize4":r=D0(e,w0);break;case"sqrtTall":r=st(e,w0,n)}return r},W0=function(t,e){switch(t){case"⎜":return"M291 0 H417 V"+e+" H291z M291 0 H417 V"+e+" H291z";case"∣":return"M145 0 H188 V"+e+" H145z M145 0 H188 V"+e+" H145z";case"∥":return"M145 0 H188 V"+e+" H145z M145 0 H188 V"+e+" H145z"+("M367 0 H410 V"+e+" H367z M367 0 H410 V"+e+" H367z");case"⎟":return"M457 0 H583 V"+e+" H457z M457 0 H583 V"+e+" H457z";case"⎢":return"M319 0 H403 V"+e+" H319z M319 0 H403 V"+e+" H319z";case"⎥":return"M263 0 H347 V"+e+" H263z M263 0 H347 V"+e+" H263z";case"⎪":return"M384 0 H504 V"+e+" H384z M384 0 H504 V"+e+" H384z";case"⏐":return"M312 0 H355 V"+e+" H312z M312 0 H355 V"+e+" H312z";case"‖":return"M257 0 H300 V"+e+" H257z M257 0 H300 V"+e+" H257z"+("M478 0 H521 V"+e+" H478z M478 0 H521 V"+e+" H478z");default:return""}},L0={doubleleftarrow:`M262 157
l10-10c34-36 62.7-77 86-123 3.3-8 5-13.3 5-16 0-5.3-6.7-8-20-8-7.3
 0-12.2.5-14.5 1.5-2.3 1-4.8 4.5-7.5 10.5-49.3 97.3-121.7 169.3-217 216-28
 14-57.3 25-88 33-6.7 2-11 3.8-13 5.5-2 1.7-3 4.2-3 7.5s1 5.8 3 7.5
c2 1.7 6.3 3.5 13 5.5 68 17.3 128.2 47.8 180.5 91.5 52.3 43.7 93.8 96.2 124.5
 157.5 9.3 8 15.3 12.3 18 13h6c12-.7 18-4 18-10 0-2-1.7-7-5-15-23.3-46-52-87
-86-123l-10-10h399738v-40H218c328 0 0 0 0 0l-10-8c-26.7-20-65.7-43-117-69 2.7
-2 6-3.7 10-5 36.7-16 72.3-37.3 107-64l10-8h399782v-40z
m8 0v40h399730v-40zm0 194v40h399730v-40z`,doublerightarrow:`M399738 392l
-10 10c-34 36-62.7 77-86 123-3.3 8-5 13.3-5 16 0 5.3 6.7 8 20 8 7.3 0 12.2-.5
 14.5-1.5 2.3-1 4.8-4.5 7.5-10.5 49.3-97.3 121.7-169.3 217-216 28-14 57.3-25 88
-33 6.7-2 11-3.8 13-5.5 2-1.7 3-4.2 3-7.5s-1-5.8-3-7.5c-2-1.7-6.3-3.5-13-5.5-68
-17.3-128.2-47.8-180.5-91.5-52.3-43.7-93.8-96.2-124.5-157.5-9.3-8-15.3-12.3-18
-13h-6c-12 .7-18 4-18 10 0 2 1.7 7 5 15 23.3 46 52 87 86 123l10 10H0v40h399782
c-328 0 0 0 0 0l10 8c26.7 20 65.7 43 117 69-2.7 2-6 3.7-10 5-36.7 16-72.3 37.3
-107 64l-10 8H0v40zM0 157v40h399730v-40zm0 194v40h399730v-40z`,leftarrow:`M400000 241H110l3-3c68.7-52.7 113.7-120
 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8
-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247
c-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208
 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3
 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202
 l-3-3h399890zM100 241v40h399900v-40z`,leftbrace:`M6 548l-6-6v-35l6-11c56-104 135.3-181.3 238-232 57.3-28.7 117
-45 179-50h399577v120H403c-43.3 7-81 15-113 26-100.7 33-179.7 91-237 174-2.7
 5-6 9-10 13-.7 1-7.3 1-20 1H6z`,leftbraceunder:`M0 6l6-6h17c12.688 0 19.313.3 20 1 4 4 7.313 8.3 10 13
 35.313 51.3 80.813 93.8 136.5 127.5 55.688 33.7 117.188 55.8 184.5 66.5.688
 0 2 .3 4 1 18.688 2.7 76 4.3 172 5h399450v120H429l-6-1c-124.688-8-235-61.7
-331-161C60.687 138.7 32.312 99.3 7 54L0 41V6z`,leftgroup:`M400000 80
H435C64 80 168.3 229.4 21 260c-5.9 1.2-18 0-18 0-2 0-3-1-3-3v-38C76 61 257 0
 435 0h399565z`,leftgroupunder:`M400000 262
H435C64 262 168.3 112.6 21 82c-5.9-1.2-18 0-18 0-2 0-3 1-3 3v38c76 158 257 219
 435 219h399565z`,leftharpoon:`M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3
-3.3 10.2-9.5 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5
-18.3 3-21-1.3-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7
-196 228-6.7 4.7-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40z`,leftharpoonplus:`M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3-3.3 10.2-9.5
 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5-18.3 3-21-1.3
-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7-196 228-6.7 4.7
-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40zM0 435v40h400000v-40z
m0 0v40h400000v-40z`,leftharpoondown:`M7 241c-4 4-6.333 8.667-7 14 0 5.333.667 9 2 11s5.333
 5.333 12 10c90.667 54 156 130 196 228 3.333 10.667 6.333 16.333 9 17 2 .667 5
 1 9 1h5c10.667 0 16.667-2 18-6 2-2.667 1-9.667-3-21-32-87.333-82.667-157.667
-152-211l-3-3h399907v-40zM93 281 H400000 v-40L7 241z`,leftharpoondownplus:`M7 435c-4 4-6.3 8.7-7 14 0 5.3.7 9 2 11s5.3 5.3 12
 10c90.7 54 156 130 196 228 3.3 10.7 6.3 16.3 9 17 2 .7 5 1 9 1h5c10.7 0 16.7
-2 18-6 2-2.7 1-9.7-3-21-32-87.3-82.7-157.7-152-211l-3-3h399907v-40H7zm93 0
v40h399900v-40zM0 241v40h399900v-40zm0 0v40h399900v-40z`,lefthook:`M400000 281 H103s-33-11.2-61-33.5S0 197.3 0 164s14.2-61.2 42.5
-83.5C70.8 58.2 104 47 142 47 c16.7 0 25 6.7 25 20 0 12-8.7 18.7-26 20-40 3.3
-68.7 15.7-86 37-10 12-15 25.3-15 40 0 22.7 9.8 40.7 29.5 54 19.7 13.3 43.5 21
 71.5 23h399859zM103 281v-40h399897v40z`,leftlinesegment:`M40 281 V428 H0 V94 H40 V241 H400000 v40z
M40 281 V428 H0 V94 H40 V241 H400000 v40z`,leftmapsto:`M40 281 V448H0V74H40V241H400000v40z
M40 281 V448H0V74H40V241H400000v40z`,leftToFrom:`M0 147h400000v40H0zm0 214c68 40 115.7 95.7 143 167h22c15.3 0 23
-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69-70-101l-7-8h399905v-40H95l7-8
c28.7-32 52-65.7 70-101 10.7-23.3 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 265.3
 68 321 0 361zm0-174v-40h399900v40zm100 154v40h399900v-40z`,longequal:`M0 50 h400000 v40H0z m0 194h40000v40H0z
M0 50 h400000 v40H0z m0 194h40000v40H0z`,midbrace:`M200428 334
c-100.7-8.3-195.3-44-280-108-55.3-42-101.7-93-139-153l-9-14c-2.7 4-5.7 8.7-9 14
-53.3 86.7-123.7 153-211 199-66.7 36-137.3 56.3-212 62H0V214h199568c178.3-11.7
 311.7-78.3 403-201 6-8 9.7-12 11-12 .7-.7 6.7-1 18-1s17.3.3 18 1c1.3 0 5 4 11
 12 44.7 59.3 101.3 106.3 170 141s145.3 54.3 229 60h199572v120z`,midbraceunder:`M199572 214
c100.7 8.3 195.3 44 280 108 55.3 42 101.7 93 139 153l9 14c2.7-4 5.7-8.7 9-14
 53.3-86.7 123.7-153 211-199 66.7-36 137.3-56.3 212-62h199568v120H200432c-178.3
 11.7-311.7 78.3-403 201-6 8-9.7 12-11 12-.7.7-6.7 1-18 1s-17.3-.3-18-1c-1.3 0
-5-4-11-12-44.7-59.3-101.3-106.3-170-141s-145.3-54.3-229-60H0V214z`,oiintSize1:`M512.6 71.6c272.6 0 320.3 106.8 320.3 178.2 0 70.8-47.7 177.6
-320.3 177.6S193.1 320.6 193.1 249.8c0-71.4 46.9-178.2 319.5-178.2z
m368.1 178.2c0-86.4-60.9-215.4-368.1-215.4-306.4 0-367.3 129-367.3 215.4 0 85.8
60.9 214.8 367.3 214.8 307.2 0 368.1-129 368.1-214.8z`,oiintSize2:`M757.8 100.1c384.7 0 451.1 137.6 451.1 230 0 91.3-66.4 228.8
-451.1 228.8-386.3 0-452.7-137.5-452.7-228.8 0-92.4 66.4-230 452.7-230z
m502.4 230c0-111.2-82.4-277.2-502.4-277.2s-504 166-504 277.2
c0 110 84 276 504 276s502.4-166 502.4-276z`,oiiintSize1:`M681.4 71.6c408.9 0 480.5 106.8 480.5 178.2 0 70.8-71.6 177.6
-480.5 177.6S202.1 320.6 202.1 249.8c0-71.4 70.5-178.2 479.3-178.2z
m525.8 178.2c0-86.4-86.8-215.4-525.7-215.4-437.9 0-524.7 129-524.7 215.4 0
85.8 86.8 214.8 524.7 214.8 438.9 0 525.7-129 525.7-214.8z`,oiiintSize2:`M1021.2 53c603.6 0 707.8 165.8 707.8 277.2 0 110-104.2 275.8
-707.8 275.8-606 0-710.2-165.8-710.2-275.8C311 218.8 415.2 53 1021.2 53z
m770.4 277.1c0-131.2-126.4-327.6-770.5-327.6S248.4 198.9 248.4 330.1
c0 130 128.8 326.4 772.7 326.4s770.5-196.4 770.5-326.4z`,rightarrow:`M0 241v40h399891c-47.3 35.3-84 78-110 128
-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20
 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7
 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85
-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5
-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67
 151.7 139 205zm0 0v40h399900v-40z`,rightbrace:`M400000 542l
-6 6h-17c-12.7 0-19.3-.3-20-1-4-4-7.3-8.3-10-13-35.3-51.3-80.8-93.8-136.5-127.5
s-117.2-55.8-184.5-66.5c-.7 0-2-.3-4-1-18.7-2.7-76-4.3-172-5H0V214h399571l6 1
c124.7 8 235 61.7 331 161 31.3 33.3 59.7 72.7 85 118l7 13v35z`,rightbraceunder:`M399994 0l6 6v35l-6 11c-56 104-135.3 181.3-238 232-57.3
 28.7-117 45-179 50H-300V214h399897c43.3-7 81-15 113-26 100.7-33 179.7-91 237
-174 2.7-5 6-9 10-13 .7-1 7.3-1 20-1h17z`,rightgroup:`M0 80h399565c371 0 266.7 149.4 414 180 5.9 1.2 18 0 18 0 2 0
 3-1 3-3v-38c-76-158-257-219-435-219H0z`,rightgroupunder:`M0 262h399565c371 0 266.7-149.4 414-180 5.9-1.2 18 0 18
 0 2 0 3 1 3 3v38c-76 158-257 219-435 219H0z`,rightharpoon:`M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3
-3.7-15.3-11-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2
-10.7 0-16.7 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58
 69.2 92 94.5zm0 0v40h399900v-40z`,rightharpoonplus:`M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3-3.7-15.3-11
-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2-10.7 0-16.7
 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58 69.2 92 94.5z
m0 0v40h399900v-40z m100 194v40h399900v-40zm0 0v40h399900v-40z`,rightharpoondown:`M399747 511c0 7.3 6.7 11 20 11 8 0 13-.8 15-2.5s4.7-6.8
 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3 8.5-5.8 9.5
-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3-64.7 57-92 95
-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 241v40h399900v-40z`,rightharpoondownplus:`M399747 705c0 7.3 6.7 11 20 11 8 0 13-.8
 15-2.5s4.7-6.8 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3
 8.5-5.8 9.5-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3
-64.7 57-92 95-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 435v40h399900v-40z
m0-194v40h400000v-40zm0 0v40h400000v-40z`,righthook:`M399859 241c-764 0 0 0 0 0 40-3.3 68.7-15.7 86-37 10-12 15-25.3
 15-40 0-22.7-9.8-40.7-29.5-54-19.7-13.3-43.5-21-71.5-23-17.3-1.3-26-8-26-20 0
-13.3 8.7-20 26-20 38 0 71 11.2 99 33.5 0 0 7 5.6 21 16.7 14 11.2 21 33.5 21
 66.8s-14 61.2-42 83.5c-28 22.3-61 33.5-99 33.5L0 241z M0 281v-40h399859v40z`,rightlinesegment:`M399960 241 V94 h40 V428 h-40 V281 H0 v-40z
M399960 241 V94 h40 V428 h-40 V281 H0 v-40z`,rightToFrom:`M400000 167c-70.7-42-118-97.7-142-167h-23c-15.3 0-23 .3-23
 1 0 1.3 5.3 13.7 16 37 18 35.3 41.3 69 70 101l7 8H0v40h399905l-7 8c-28.7 32
-52 65.7-70 101-10.7 23.3-16 35.7-16 37 0 .7 7.7 1 23 1h23c24-69.3 71.3-125 142
-167z M100 147v40h399900v-40zM0 341v40h399900v-40z`,twoheadleftarrow:`M0 167c68 40
 115.7 95.7 143 167h22c15.3 0 23-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69
-70-101l-7-8h125l9 7c50.7 39.3 85 86 103 140h46c0-4.7-6.3-18.7-19-42-18-35.3
-40-67.3-66-96l-9-9h399716v-40H284l9-9c26-28.7 48-60.7 66-96 12.7-23.333 19
-37.333 19-42h-46c-18 54-52.3 100.7-103 140l-9 7H95l7-8c28.7-32 52-65.7 70-101
 10.7-23.333 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 71.3 68 127 0 167z`,twoheadrightarrow:`M400000 167
c-68-40-115.7-95.7-143-167h-22c-15.3 0-23 .3-23 1 0 1.3 5.3 13.7 16 37 18 35.3
 41.3 69 70 101l7 8h-125l-9-7c-50.7-39.3-85-86-103-140h-46c0 4.7 6.3 18.7 19 42
 18 35.3 40 67.3 66 96l9 9H0v40h399716l-9 9c-26 28.7-48 60.7-66 96-12.7 23.333
-19 37.333-19 42h46c18-54 52.3-100.7 103-140l9-7h125l-7 8c-28.7 32-52 65.7-70
 101-10.7 23.333-16 35.7-16 37 0 .7 7.7 1 23 1h22c27.3-71.3 75-127 143-167z`,tilde1:`M200 55.538c-77 0-168 73.953-177 73.953-3 0-7
-2.175-9-5.437L2 97c-1-2-2-4-2-6 0-4 2-7 5-9l20-12C116 12 171 0 207 0c86 0
 114 68 191 68 78 0 168-68 177-68 4 0 7 2 9 5l12 19c1 2.175 2 4.35 2 6.525 0
 4.35-2 7.613-5 9.788l-19 13.05c-92 63.077-116.937 75.308-183 76.128
-68.267.847-113-73.952-191-73.952z`,tilde2:`M344 55.266c-142 0-300.638 81.316-311.5 86.418
-8.01 3.762-22.5 10.91-23.5 5.562L1 120c-1-2-1-3-1-4 0-5 3-9 8-10l18.4-9C160.9
 31.9 283 0 358 0c148 0 188 122 331 122s314-97 326-97c4 0 8 2 10 7l7 21.114
c1 2.14 1 3.21 1 4.28 0 5.347-3 9.626-7 10.696l-22.3 12.622C852.6 158.372 751
 181.476 676 181.476c-149 0-189-126.21-332-126.21z`,tilde3:`M786 59C457 59 32 175.242 13 175.242c-6 0-10-3.457
-11-10.37L.15 138c-1-7 3-12 10-13l19.2-6.4C378.4 40.7 634.3 0 804.3 0c337 0
 411.8 157 746.8 157 328 0 754-112 773-112 5 0 10 3 11 9l1 14.075c1 8.066-.697
 16.595-6.697 17.492l-21.052 7.31c-367.9 98.146-609.15 122.696-778.15 122.696
 -338 0-409-156.573-744-156.573z`,tilde4:`M786 58C457 58 32 177.487 13 177.487c-6 0-10-3.345
-11-10.035L.15 143c-1-7 3-12 10-13l22-6.7C381.2 35 637.15 0 807.15 0c337 0 409
 177 744 177 328 0 754-127 773-127 5 0 10 3 11 9l1 14.794c1 7.805-3 13.38-9
 14.495l-20.7 5.574c-366.85 99.79-607.3 139.372-776.3 139.372-338 0-409
 -175.236-744-175.236z`,vec:`M377 20c0-5.333 1.833-10 5.5-14S391 0 397 0c4.667 0 8.667 1.667 12 5
3.333 2.667 6.667 9 10 19 6.667 24.667 20.333 43.667 41 57 7.333 4.667 11
10.667 11 18 0 6-1 10-3 12s-6.667 5-14 9c-28.667 14.667-53.667 35.667-75 63
-1.333 1.333-3.167 3.5-5.5 6.5s-4 4.833-5 5.5c-1 .667-2.5 1.333-4.5 2s-4.333 1
-7 1c-4.667 0-9.167-1.833-13.5-5.5S337 184 337 178c0-12.667 15.667-32.333 47-59
H213l-171-1c-8.667-6-13-12.333-13-19 0-4.667 4.333-11.333 13-20h359
c-16-25.333-24-45-24-59z`,widehat1:`M529 0h5l519 115c5 1 9 5 9 10 0 1-1 2-1 3l-4 22
c-1 5-5 9-11 9h-2L532 67 19 159h-2c-5 0-9-4-11-9l-5-22c-1-6 2-12 8-13z`,widehat2:`M1181 0h2l1171 176c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 220h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widehat3:`M1181 0h2l1171 236c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 280h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widehat4:`M1181 0h2l1171 296c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 340h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widecheck1:`M529,159h5l519,-115c5,-1,9,-5,9,-10c0,-1,-1,-2,-1,-3l-4,-22c-1,
-5,-5,-9,-11,-9h-2l-512,92l-513,-92h-2c-5,0,-9,4,-11,9l-5,22c-1,6,2,12,8,13z`,widecheck2:`M1181,220h2l1171,-176c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,153l-1167,-153h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,widecheck3:`M1181,280h2l1171,-236c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,213l-1167,-213h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,widecheck4:`M1181,340h2l1171,-296c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,273l-1167,-273h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,baraboveleftarrow:`M400000 620h-399890l3 -3c68.7 -52.7 113.7 -120 135 -202
c4 -14.7 6 -23 6 -25c0 -7.3 -7 -11 -21 -11c-8 0 -13.2 0.8 -15.5 2.5
c-2.3 1.7 -4.2 5.8 -5.5 12.5c-1.3 4.7 -2.7 10.3 -4 17c-12 48.7 -34.8 92 -68.5 130
s-74.2 66.3 -121.5 85c-10 4 -16 7.7 -18 11c0 8.7 6 14.3 18 17c47.3 18.7 87.8 47
121.5 85s56.5 81.3 68.5 130c0.7 2 1.3 5 2 9s1.2 6.7 1.5 8c0.3 1.3 1 3.3 2 6
s2.2 4.5 3.5 5.5c1.3 1 3.3 1.8 6 2.5s6 1 10 1c14 0 21 -3.7 21 -11
c0 -2 -2 -10.3 -6 -25c-20 -79.3 -65 -146.7 -135 -202l-3 -3h399890z
M100 620v40h399900v-40z M0 241v40h399900v-40zM0 241v40h399900v-40z`,rightarrowabovebar:`M0 241v40h399891c-47.3 35.3-84 78-110 128-16.7 32
-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20 11 8 0
13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7 39
-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85-40.5
-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5
-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67
151.7 139 205zm96 379h399894v40H0zm0 0h399904v40H0z`,baraboveshortleftharpoon:`M507,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11
c1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17
c2,0.7,5,1,9,1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21
c-32,-87.3,-82.7,-157.7,-152,-211c0,0,-3,-3,-3,-3l399351,0l0,-40
c-398570,0,-399437,0,-399437,0z M593 435 v40 H399500 v-40z
M0 281 v-40 H399908 v40z M0 281 v-40 H399908 v40z`,rightharpoonaboveshortbar:`M0,241 l0,40c399126,0,399993,0,399993,0
c4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,
-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6
c-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z
M0 241 v40 H399908 v-40z M0 475 v-40 H399500 v40z M0 475 v-40 H399500 v40z`,shortbaraboveleftharpoon:`M7,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11
c1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17c2,0.7,5,1,9,
1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21c-32,-87.3,-82.7,-157.7,
-152,-211c0,0,-3,-3,-3,-3l399907,0l0,-40c-399126,0,-399993,0,-399993,0z
M93 435 v40 H400000 v-40z M500 241 v40 H400000 v-40z M500 241 v40 H400000 v-40z`,shortrightharpoonabovebar:`M53,241l0,40c398570,0,399437,0,399437,0
c4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,
-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6
c-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z
M500 241 v40 H399408 v-40z M500 435 v40 H400000 v-40z`},Et=function(t,e){switch(t){case"lbrack":return"M403 1759 V84 H666 V0 H319 V1759 v"+e+` v1759 h347 v-84
H403z M403 1759 V0 H319 V1759 v`+e+" v1759 h84z";case"rbrack":return"M347 1759 V0 H0 V84 H263 V1759 v"+e+` v1759 H0 v84 H347z
M347 1759 V0 H263 V1759 v`+e+" v1759 h84z";case"vert":return"M145 15 v585 v"+e+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-e+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M188 15 H145 v585 v`+e+" v585 h43z";case"doublevert":return"M145 15 v585 v"+e+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-e+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M188 15 H145 v585 v`+e+` v585 h43z
M367 15 v585 v`+e+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-e+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M410 15 H367 v585 v`+e+" v585 h43z";case"lfloor":return"M319 602 V0 H403 V602 v"+e+` v1715 h263 v84 H319z
MM319 602 V0 H403 V602 v`+e+" v1715 H319z";case"rfloor":return"M319 602 V0 H403 V602 v"+e+` v1799 H0 v-84 H319z
MM319 602 V0 H403 V602 v`+e+" v1715 H319z";case"lceil":return"M403 1759 V84 H666 V0 H319 V1759 v"+e+` v602 h84z
M403 1759 V0 H319 V1759 v`+e+" v602 h84z";case"rceil":return"M347 1759 V0 H0 V84 H263 V1759 v"+e+` v602 h84z
M347 1759 V0 h-84 V1759 v`+e+" v602 h84z";case"lparen":return`M863,9c0,-2,-2,-5,-6,-9c0,0,-17,0,-17,0c-12.7,0,-19.3,0.3,-20,1
c-5.3,5.3,-10.3,11,-15,17c-242.7,294.7,-395.3,682,-458,1162c-21.3,163.3,-33.3,349,
-36,557 l0,`+(e+84)+`c0.2,6,0,26,0,60c2,159.3,10,310.7,24,454c53.3,528,210,
949.7,470,1265c4.7,6,9.7,11.7,15,17c0.7,0.7,7,1,19,1c0,0,18,0,18,0c4,-4,6,-7,6,-9
c0,-2.7,-3.3,-8.7,-10,-18c-135.3,-192.7,-235.5,-414.3,-300.5,-665c-65,-250.7,-102.5,
-544.7,-112.5,-882c-2,-104,-3,-167,-3,-189
l0,-`+(e+92)+`c0,-162.7,5.7,-314,17,-454c20.7,-272,63.7,-513,129,-723c65.3,
-210,155.3,-396.3,270,-559c6.7,-9.3,10,-15.3,10,-18z`;case"rparen":return`M76,0c-16.7,0,-25,3,-25,9c0,2,2,6.3,6,13c21.3,28.7,42.3,60.3,
63,95c96.7,156.7,172.8,332.5,228.5,527.5c55.7,195,92.8,416.5,111.5,664.5
c11.3,139.3,17,290.7,17,454c0,28,1.7,43,3.3,45l0,`+(e+9)+`
c-3,4,-3.3,16.7,-3.3,38c0,162,-5.7,313.7,-17,455c-18.7,248,-55.8,469.3,-111.5,664
c-55.7,194.7,-131.8,370.3,-228.5,527c-20.7,34.7,-41.7,66.3,-63,95c-2,3.3,-4,7,-6,11
c0,7.3,5.7,11,17,11c0,0,11,0,11,0c9.3,0,14.3,-0.3,15,-1c5.3,-5.3,10.3,-11,15,-17
c242.7,-294.7,395.3,-681.7,458,-1161c21.3,-164.7,33.3,-350.7,36,-558
l0,-`+(e+144)+`c-2,-159.3,-10,-310.7,-24,-454c-53.3,-528,-210,-949.7,
-470,-1265c-4.7,-6,-9.7,-11.7,-15,-17c-0.7,-0.7,-6.7,-1,-18,-1z`;default:throw new Error("Unknown stretchy delimiter.")}};class C0{constructor(e){this.children=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.children=e,this.classes=[],this.height=0,this.depth=0,this.maxFontSize=0,this.style={}}hasClass(e){return N.contains(this.classes,e)}toNode(){const e=document.createDocumentFragment();for(let n=0;n<this.children.length;n++)e.appendChild(this.children[n].toNode());return e}toMarkup(){let e="";for(let n=0;n<this.children.length;n++)e+=this.children[n].toMarkup();return e}toText(){const e=n=>n.toText();return this.children.map(e).join("")}}var Je={"AMS-Regular":{32:[0,0,0,0,.25],65:[0,.68889,0,0,.72222],66:[0,.68889,0,0,.66667],67:[0,.68889,0,0,.72222],68:[0,.68889,0,0,.72222],69:[0,.68889,0,0,.66667],70:[0,.68889,0,0,.61111],71:[0,.68889,0,0,.77778],72:[0,.68889,0,0,.77778],73:[0,.68889,0,0,.38889],74:[.16667,.68889,0,0,.5],75:[0,.68889,0,0,.77778],76:[0,.68889,0,0,.66667],77:[0,.68889,0,0,.94445],78:[0,.68889,0,0,.72222],79:[.16667,.68889,0,0,.77778],80:[0,.68889,0,0,.61111],81:[.16667,.68889,0,0,.77778],82:[0,.68889,0,0,.72222],83:[0,.68889,0,0,.55556],84:[0,.68889,0,0,.66667],85:[0,.68889,0,0,.72222],86:[0,.68889,0,0,.72222],87:[0,.68889,0,0,1],88:[0,.68889,0,0,.72222],89:[0,.68889,0,0,.72222],90:[0,.68889,0,0,.66667],107:[0,.68889,0,0,.55556],160:[0,0,0,0,.25],165:[0,.675,.025,0,.75],174:[.15559,.69224,0,0,.94666],240:[0,.68889,0,0,.55556],295:[0,.68889,0,0,.54028],710:[0,.825,0,0,2.33334],732:[0,.9,0,0,2.33334],770:[0,.825,0,0,2.33334],771:[0,.9,0,0,2.33334],989:[.08167,.58167,0,0,.77778],1008:[0,.43056,.04028,0,.66667],8245:[0,.54986,0,0,.275],8463:[0,.68889,0,0,.54028],8487:[0,.68889,0,0,.72222],8498:[0,.68889,0,0,.55556],8502:[0,.68889,0,0,.66667],8503:[0,.68889,0,0,.44445],8504:[0,.68889,0,0,.66667],8513:[0,.68889,0,0,.63889],8592:[-.03598,.46402,0,0,.5],8594:[-.03598,.46402,0,0,.5],8602:[-.13313,.36687,0,0,1],8603:[-.13313,.36687,0,0,1],8606:[.01354,.52239,0,0,1],8608:[.01354,.52239,0,0,1],8610:[.01354,.52239,0,0,1.11111],8611:[.01354,.52239,0,0,1.11111],8619:[0,.54986,0,0,1],8620:[0,.54986,0,0,1],8621:[-.13313,.37788,0,0,1.38889],8622:[-.13313,.36687,0,0,1],8624:[0,.69224,0,0,.5],8625:[0,.69224,0,0,.5],8630:[0,.43056,0,0,1],8631:[0,.43056,0,0,1],8634:[.08198,.58198,0,0,.77778],8635:[.08198,.58198,0,0,.77778],8638:[.19444,.69224,0,0,.41667],8639:[.19444,.69224,0,0,.41667],8642:[.19444,.69224,0,0,.41667],8643:[.19444,.69224,0,0,.41667],8644:[.1808,.675,0,0,1],8646:[.1808,.675,0,0,1],8647:[.1808,.675,0,0,1],8648:[.19444,.69224,0,0,.83334],8649:[.1808,.675,0,0,1],8650:[.19444,.69224,0,0,.83334],8651:[.01354,.52239,0,0,1],8652:[.01354,.52239,0,0,1],8653:[-.13313,.36687,0,0,1],8654:[-.13313,.36687,0,0,1],8655:[-.13313,.36687,0,0,1],8666:[.13667,.63667,0,0,1],8667:[.13667,.63667,0,0,1],8669:[-.13313,.37788,0,0,1],8672:[-.064,.437,0,0,1.334],8674:[-.064,.437,0,0,1.334],8705:[0,.825,0,0,.5],8708:[0,.68889,0,0,.55556],8709:[.08167,.58167,0,0,.77778],8717:[0,.43056,0,0,.42917],8722:[-.03598,.46402,0,0,.5],8724:[.08198,.69224,0,0,.77778],8726:[.08167,.58167,0,0,.77778],8733:[0,.69224,0,0,.77778],8736:[0,.69224,0,0,.72222],8737:[0,.69224,0,0,.72222],8738:[.03517,.52239,0,0,.72222],8739:[.08167,.58167,0,0,.22222],8740:[.25142,.74111,0,0,.27778],8741:[.08167,.58167,0,0,.38889],8742:[.25142,.74111,0,0,.5],8756:[0,.69224,0,0,.66667],8757:[0,.69224,0,0,.66667],8764:[-.13313,.36687,0,0,.77778],8765:[-.13313,.37788,0,0,.77778],8769:[-.13313,.36687,0,0,.77778],8770:[-.03625,.46375,0,0,.77778],8774:[.30274,.79383,0,0,.77778],8776:[-.01688,.48312,0,0,.77778],8778:[.08167,.58167,0,0,.77778],8782:[.06062,.54986,0,0,.77778],8783:[.06062,.54986,0,0,.77778],8785:[.08198,.58198,0,0,.77778],8786:[.08198,.58198,0,0,.77778],8787:[.08198,.58198,0,0,.77778],8790:[0,.69224,0,0,.77778],8791:[.22958,.72958,0,0,.77778],8796:[.08198,.91667,0,0,.77778],8806:[.25583,.75583,0,0,.77778],8807:[.25583,.75583,0,0,.77778],8808:[.25142,.75726,0,0,.77778],8809:[.25142,.75726,0,0,.77778],8812:[.25583,.75583,0,0,.5],8814:[.20576,.70576,0,0,.77778],8815:[.20576,.70576,0,0,.77778],8816:[.30274,.79383,0,0,.77778],8817:[.30274,.79383,0,0,.77778],8818:[.22958,.72958,0,0,.77778],8819:[.22958,.72958,0,0,.77778],8822:[.1808,.675,0,0,.77778],8823:[.1808,.675,0,0,.77778],8828:[.13667,.63667,0,0,.77778],8829:[.13667,.63667,0,0,.77778],8830:[.22958,.72958,0,0,.77778],8831:[.22958,.72958,0,0,.77778],8832:[.20576,.70576,0,0,.77778],8833:[.20576,.70576,0,0,.77778],8840:[.30274,.79383,0,0,.77778],8841:[.30274,.79383,0,0,.77778],8842:[.13597,.63597,0,0,.77778],8843:[.13597,.63597,0,0,.77778],8847:[.03517,.54986,0,0,.77778],8848:[.03517,.54986,0,0,.77778],8858:[.08198,.58198,0,0,.77778],8859:[.08198,.58198,0,0,.77778],8861:[.08198,.58198,0,0,.77778],8862:[0,.675,0,0,.77778],8863:[0,.675,0,0,.77778],8864:[0,.675,0,0,.77778],8865:[0,.675,0,0,.77778],8872:[0,.69224,0,0,.61111],8873:[0,.69224,0,0,.72222],8874:[0,.69224,0,0,.88889],8876:[0,.68889,0,0,.61111],8877:[0,.68889,0,0,.61111],8878:[0,.68889,0,0,.72222],8879:[0,.68889,0,0,.72222],8882:[.03517,.54986,0,0,.77778],8883:[.03517,.54986,0,0,.77778],8884:[.13667,.63667,0,0,.77778],8885:[.13667,.63667,0,0,.77778],8888:[0,.54986,0,0,1.11111],8890:[.19444,.43056,0,0,.55556],8891:[.19444,.69224,0,0,.61111],8892:[.19444,.69224,0,0,.61111],8901:[0,.54986,0,0,.27778],8903:[.08167,.58167,0,0,.77778],8905:[.08167,.58167,0,0,.77778],8906:[.08167,.58167,0,0,.77778],8907:[0,.69224,0,0,.77778],8908:[0,.69224,0,0,.77778],8909:[-.03598,.46402,0,0,.77778],8910:[0,.54986,0,0,.76042],8911:[0,.54986,0,0,.76042],8912:[.03517,.54986,0,0,.77778],8913:[.03517,.54986,0,0,.77778],8914:[0,.54986,0,0,.66667],8915:[0,.54986,0,0,.66667],8916:[0,.69224,0,0,.66667],8918:[.0391,.5391,0,0,.77778],8919:[.0391,.5391,0,0,.77778],8920:[.03517,.54986,0,0,1.33334],8921:[.03517,.54986,0,0,1.33334],8922:[.38569,.88569,0,0,.77778],8923:[.38569,.88569,0,0,.77778],8926:[.13667,.63667,0,0,.77778],8927:[.13667,.63667,0,0,.77778],8928:[.30274,.79383,0,0,.77778],8929:[.30274,.79383,0,0,.77778],8934:[.23222,.74111,0,0,.77778],8935:[.23222,.74111,0,0,.77778],8936:[.23222,.74111,0,0,.77778],8937:[.23222,.74111,0,0,.77778],8938:[.20576,.70576,0,0,.77778],8939:[.20576,.70576,0,0,.77778],8940:[.30274,.79383,0,0,.77778],8941:[.30274,.79383,0,0,.77778],8994:[.19444,.69224,0,0,.77778],8995:[.19444,.69224,0,0,.77778],9416:[.15559,.69224,0,0,.90222],9484:[0,.69224,0,0,.5],9488:[0,.69224,0,0,.5],9492:[0,.37788,0,0,.5],9496:[0,.37788,0,0,.5],9585:[.19444,.68889,0,0,.88889],9586:[.19444,.74111,0,0,.88889],9632:[0,.675,0,0,.77778],9633:[0,.675,0,0,.77778],9650:[0,.54986,0,0,.72222],9651:[0,.54986,0,0,.72222],9654:[.03517,.54986,0,0,.77778],9660:[0,.54986,0,0,.72222],9661:[0,.54986,0,0,.72222],9664:[.03517,.54986,0,0,.77778],9674:[.11111,.69224,0,0,.66667],9733:[.19444,.69224,0,0,.94445],10003:[0,.69224,0,0,.83334],10016:[0,.69224,0,0,.83334],10731:[.11111,.69224,0,0,.66667],10846:[.19444,.75583,0,0,.61111],10877:[.13667,.63667,0,0,.77778],10878:[.13667,.63667,0,0,.77778],10885:[.25583,.75583,0,0,.77778],10886:[.25583,.75583,0,0,.77778],10887:[.13597,.63597,0,0,.77778],10888:[.13597,.63597,0,0,.77778],10889:[.26167,.75726,0,0,.77778],10890:[.26167,.75726,0,0,.77778],10891:[.48256,.98256,0,0,.77778],10892:[.48256,.98256,0,0,.77778],10901:[.13667,.63667,0,0,.77778],10902:[.13667,.63667,0,0,.77778],10933:[.25142,.75726,0,0,.77778],10934:[.25142,.75726,0,0,.77778],10935:[.26167,.75726,0,0,.77778],10936:[.26167,.75726,0,0,.77778],10937:[.26167,.75726,0,0,.77778],10938:[.26167,.75726,0,0,.77778],10949:[.25583,.75583,0,0,.77778],10950:[.25583,.75583,0,0,.77778],10955:[.28481,.79383,0,0,.77778],10956:[.28481,.79383,0,0,.77778],57350:[.08167,.58167,0,0,.22222],57351:[.08167,.58167,0,0,.38889],57352:[.08167,.58167,0,0,.77778],57353:[0,.43056,.04028,0,.66667],57356:[.25142,.75726,0,0,.77778],57357:[.25142,.75726,0,0,.77778],57358:[.41951,.91951,0,0,.77778],57359:[.30274,.79383,0,0,.77778],57360:[.30274,.79383,0,0,.77778],57361:[.41951,.91951,0,0,.77778],57366:[.25142,.75726,0,0,.77778],57367:[.25142,.75726,0,0,.77778],57368:[.25142,.75726,0,0,.77778],57369:[.25142,.75726,0,0,.77778],57370:[.13597,.63597,0,0,.77778],57371:[.13597,.63597,0,0,.77778]},"Caligraphic-Regular":{32:[0,0,0,0,.25],65:[0,.68333,0,.19445,.79847],66:[0,.68333,.03041,.13889,.65681],67:[0,.68333,.05834,.13889,.52653],68:[0,.68333,.02778,.08334,.77139],69:[0,.68333,.08944,.11111,.52778],70:[0,.68333,.09931,.11111,.71875],71:[.09722,.68333,.0593,.11111,.59487],72:[0,.68333,.00965,.11111,.84452],73:[0,.68333,.07382,0,.54452],74:[.09722,.68333,.18472,.16667,.67778],75:[0,.68333,.01445,.05556,.76195],76:[0,.68333,0,.13889,.68972],77:[0,.68333,0,.13889,1.2009],78:[0,.68333,.14736,.08334,.82049],79:[0,.68333,.02778,.11111,.79611],80:[0,.68333,.08222,.08334,.69556],81:[.09722,.68333,0,.11111,.81667],82:[0,.68333,0,.08334,.8475],83:[0,.68333,.075,.13889,.60556],84:[0,.68333,.25417,0,.54464],85:[0,.68333,.09931,.08334,.62583],86:[0,.68333,.08222,0,.61278],87:[0,.68333,.08222,.08334,.98778],88:[0,.68333,.14643,.13889,.7133],89:[.09722,.68333,.08222,.08334,.66834],90:[0,.68333,.07944,.13889,.72473],160:[0,0,0,0,.25]},"Fraktur-Regular":{32:[0,0,0,0,.25],33:[0,.69141,0,0,.29574],34:[0,.69141,0,0,.21471],38:[0,.69141,0,0,.73786],39:[0,.69141,0,0,.21201],40:[.24982,.74947,0,0,.38865],41:[.24982,.74947,0,0,.38865],42:[0,.62119,0,0,.27764],43:[.08319,.58283,0,0,.75623],44:[0,.10803,0,0,.27764],45:[.08319,.58283,0,0,.75623],46:[0,.10803,0,0,.27764],47:[.24982,.74947,0,0,.50181],48:[0,.47534,0,0,.50181],49:[0,.47534,0,0,.50181],50:[0,.47534,0,0,.50181],51:[.18906,.47534,0,0,.50181],52:[.18906,.47534,0,0,.50181],53:[.18906,.47534,0,0,.50181],54:[0,.69141,0,0,.50181],55:[.18906,.47534,0,0,.50181],56:[0,.69141,0,0,.50181],57:[.18906,.47534,0,0,.50181],58:[0,.47534,0,0,.21606],59:[.12604,.47534,0,0,.21606],61:[-.13099,.36866,0,0,.75623],63:[0,.69141,0,0,.36245],65:[0,.69141,0,0,.7176],66:[0,.69141,0,0,.88397],67:[0,.69141,0,0,.61254],68:[0,.69141,0,0,.83158],69:[0,.69141,0,0,.66278],70:[.12604,.69141,0,0,.61119],71:[0,.69141,0,0,.78539],72:[.06302,.69141,0,0,.7203],73:[0,.69141,0,0,.55448],74:[.12604,.69141,0,0,.55231],75:[0,.69141,0,0,.66845],76:[0,.69141,0,0,.66602],77:[0,.69141,0,0,1.04953],78:[0,.69141,0,0,.83212],79:[0,.69141,0,0,.82699],80:[.18906,.69141,0,0,.82753],81:[.03781,.69141,0,0,.82699],82:[0,.69141,0,0,.82807],83:[0,.69141,0,0,.82861],84:[0,.69141,0,0,.66899],85:[0,.69141,0,0,.64576],86:[0,.69141,0,0,.83131],87:[0,.69141,0,0,1.04602],88:[0,.69141,0,0,.71922],89:[.18906,.69141,0,0,.83293],90:[.12604,.69141,0,0,.60201],91:[.24982,.74947,0,0,.27764],93:[.24982,.74947,0,0,.27764],94:[0,.69141,0,0,.49965],97:[0,.47534,0,0,.50046],98:[0,.69141,0,0,.51315],99:[0,.47534,0,0,.38946],100:[0,.62119,0,0,.49857],101:[0,.47534,0,0,.40053],102:[.18906,.69141,0,0,.32626],103:[.18906,.47534,0,0,.5037],104:[.18906,.69141,0,0,.52126],105:[0,.69141,0,0,.27899],106:[0,.69141,0,0,.28088],107:[0,.69141,0,0,.38946],108:[0,.69141,0,0,.27953],109:[0,.47534,0,0,.76676],110:[0,.47534,0,0,.52666],111:[0,.47534,0,0,.48885],112:[.18906,.52396,0,0,.50046],113:[.18906,.47534,0,0,.48912],114:[0,.47534,0,0,.38919],115:[0,.47534,0,0,.44266],116:[0,.62119,0,0,.33301],117:[0,.47534,0,0,.5172],118:[0,.52396,0,0,.5118],119:[0,.52396,0,0,.77351],120:[.18906,.47534,0,0,.38865],121:[.18906,.47534,0,0,.49884],122:[.18906,.47534,0,0,.39054],160:[0,0,0,0,.25],8216:[0,.69141,0,0,.21471],8217:[0,.69141,0,0,.21471],58112:[0,.62119,0,0,.49749],58113:[0,.62119,0,0,.4983],58114:[.18906,.69141,0,0,.33328],58115:[.18906,.69141,0,0,.32923],58116:[.18906,.47534,0,0,.50343],58117:[0,.69141,0,0,.33301],58118:[0,.62119,0,0,.33409],58119:[0,.47534,0,0,.50073]},"Main-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.35],34:[0,.69444,0,0,.60278],35:[.19444,.69444,0,0,.95833],36:[.05556,.75,0,0,.575],37:[.05556,.75,0,0,.95833],38:[0,.69444,0,0,.89444],39:[0,.69444,0,0,.31944],40:[.25,.75,0,0,.44722],41:[.25,.75,0,0,.44722],42:[0,.75,0,0,.575],43:[.13333,.63333,0,0,.89444],44:[.19444,.15556,0,0,.31944],45:[0,.44444,0,0,.38333],46:[0,.15556,0,0,.31944],47:[.25,.75,0,0,.575],48:[0,.64444,0,0,.575],49:[0,.64444,0,0,.575],50:[0,.64444,0,0,.575],51:[0,.64444,0,0,.575],52:[0,.64444,0,0,.575],53:[0,.64444,0,0,.575],54:[0,.64444,0,0,.575],55:[0,.64444,0,0,.575],56:[0,.64444,0,0,.575],57:[0,.64444,0,0,.575],58:[0,.44444,0,0,.31944],59:[.19444,.44444,0,0,.31944],60:[.08556,.58556,0,0,.89444],61:[-.10889,.39111,0,0,.89444],62:[.08556,.58556,0,0,.89444],63:[0,.69444,0,0,.54305],64:[0,.69444,0,0,.89444],65:[0,.68611,0,0,.86944],66:[0,.68611,0,0,.81805],67:[0,.68611,0,0,.83055],68:[0,.68611,0,0,.88194],69:[0,.68611,0,0,.75555],70:[0,.68611,0,0,.72361],71:[0,.68611,0,0,.90416],72:[0,.68611,0,0,.9],73:[0,.68611,0,0,.43611],74:[0,.68611,0,0,.59444],75:[0,.68611,0,0,.90138],76:[0,.68611,0,0,.69166],77:[0,.68611,0,0,1.09166],78:[0,.68611,0,0,.9],79:[0,.68611,0,0,.86388],80:[0,.68611,0,0,.78611],81:[.19444,.68611,0,0,.86388],82:[0,.68611,0,0,.8625],83:[0,.68611,0,0,.63889],84:[0,.68611,0,0,.8],85:[0,.68611,0,0,.88472],86:[0,.68611,.01597,0,.86944],87:[0,.68611,.01597,0,1.18888],88:[0,.68611,0,0,.86944],89:[0,.68611,.02875,0,.86944],90:[0,.68611,0,0,.70277],91:[.25,.75,0,0,.31944],92:[.25,.75,0,0,.575],93:[.25,.75,0,0,.31944],94:[0,.69444,0,0,.575],95:[.31,.13444,.03194,0,.575],97:[0,.44444,0,0,.55902],98:[0,.69444,0,0,.63889],99:[0,.44444,0,0,.51111],100:[0,.69444,0,0,.63889],101:[0,.44444,0,0,.52708],102:[0,.69444,.10903,0,.35139],103:[.19444,.44444,.01597,0,.575],104:[0,.69444,0,0,.63889],105:[0,.69444,0,0,.31944],106:[.19444,.69444,0,0,.35139],107:[0,.69444,0,0,.60694],108:[0,.69444,0,0,.31944],109:[0,.44444,0,0,.95833],110:[0,.44444,0,0,.63889],111:[0,.44444,0,0,.575],112:[.19444,.44444,0,0,.63889],113:[.19444,.44444,0,0,.60694],114:[0,.44444,0,0,.47361],115:[0,.44444,0,0,.45361],116:[0,.63492,0,0,.44722],117:[0,.44444,0,0,.63889],118:[0,.44444,.01597,0,.60694],119:[0,.44444,.01597,0,.83055],120:[0,.44444,0,0,.60694],121:[.19444,.44444,.01597,0,.60694],122:[0,.44444,0,0,.51111],123:[.25,.75,0,0,.575],124:[.25,.75,0,0,.31944],125:[.25,.75,0,0,.575],126:[.35,.34444,0,0,.575],160:[0,0,0,0,.25],163:[0,.69444,0,0,.86853],168:[0,.69444,0,0,.575],172:[0,.44444,0,0,.76666],176:[0,.69444,0,0,.86944],177:[.13333,.63333,0,0,.89444],184:[.17014,0,0,0,.51111],198:[0,.68611,0,0,1.04166],215:[.13333,.63333,0,0,.89444],216:[.04861,.73472,0,0,.89444],223:[0,.69444,0,0,.59722],230:[0,.44444,0,0,.83055],247:[.13333,.63333,0,0,.89444],248:[.09722,.54167,0,0,.575],305:[0,.44444,0,0,.31944],338:[0,.68611,0,0,1.16944],339:[0,.44444,0,0,.89444],567:[.19444,.44444,0,0,.35139],710:[0,.69444,0,0,.575],711:[0,.63194,0,0,.575],713:[0,.59611,0,0,.575],714:[0,.69444,0,0,.575],715:[0,.69444,0,0,.575],728:[0,.69444,0,0,.575],729:[0,.69444,0,0,.31944],730:[0,.69444,0,0,.86944],732:[0,.69444,0,0,.575],733:[0,.69444,0,0,.575],915:[0,.68611,0,0,.69166],916:[0,.68611,0,0,.95833],920:[0,.68611,0,0,.89444],923:[0,.68611,0,0,.80555],926:[0,.68611,0,0,.76666],928:[0,.68611,0,0,.9],931:[0,.68611,0,0,.83055],933:[0,.68611,0,0,.89444],934:[0,.68611,0,0,.83055],936:[0,.68611,0,0,.89444],937:[0,.68611,0,0,.83055],8211:[0,.44444,.03194,0,.575],8212:[0,.44444,.03194,0,1.14999],8216:[0,.69444,0,0,.31944],8217:[0,.69444,0,0,.31944],8220:[0,.69444,0,0,.60278],8221:[0,.69444,0,0,.60278],8224:[.19444,.69444,0,0,.51111],8225:[.19444,.69444,0,0,.51111],8242:[0,.55556,0,0,.34444],8407:[0,.72444,.15486,0,.575],8463:[0,.69444,0,0,.66759],8465:[0,.69444,0,0,.83055],8467:[0,.69444,0,0,.47361],8472:[.19444,.44444,0,0,.74027],8476:[0,.69444,0,0,.83055],8501:[0,.69444,0,0,.70277],8592:[-.10889,.39111,0,0,1.14999],8593:[.19444,.69444,0,0,.575],8594:[-.10889,.39111,0,0,1.14999],8595:[.19444,.69444,0,0,.575],8596:[-.10889,.39111,0,0,1.14999],8597:[.25,.75,0,0,.575],8598:[.19444,.69444,0,0,1.14999],8599:[.19444,.69444,0,0,1.14999],8600:[.19444,.69444,0,0,1.14999],8601:[.19444,.69444,0,0,1.14999],8636:[-.10889,.39111,0,0,1.14999],8637:[-.10889,.39111,0,0,1.14999],8640:[-.10889,.39111,0,0,1.14999],8641:[-.10889,.39111,0,0,1.14999],8656:[-.10889,.39111,0,0,1.14999],8657:[.19444,.69444,0,0,.70277],8658:[-.10889,.39111,0,0,1.14999],8659:[.19444,.69444,0,0,.70277],8660:[-.10889,.39111,0,0,1.14999],8661:[.25,.75,0,0,.70277],8704:[0,.69444,0,0,.63889],8706:[0,.69444,.06389,0,.62847],8707:[0,.69444,0,0,.63889],8709:[.05556,.75,0,0,.575],8711:[0,.68611,0,0,.95833],8712:[.08556,.58556,0,0,.76666],8715:[.08556,.58556,0,0,.76666],8722:[.13333,.63333,0,0,.89444],8723:[.13333,.63333,0,0,.89444],8725:[.25,.75,0,0,.575],8726:[.25,.75,0,0,.575],8727:[-.02778,.47222,0,0,.575],8728:[-.02639,.47361,0,0,.575],8729:[-.02639,.47361,0,0,.575],8730:[.18,.82,0,0,.95833],8733:[0,.44444,0,0,.89444],8734:[0,.44444,0,0,1.14999],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.31944],8741:[.25,.75,0,0,.575],8743:[0,.55556,0,0,.76666],8744:[0,.55556,0,0,.76666],8745:[0,.55556,0,0,.76666],8746:[0,.55556,0,0,.76666],8747:[.19444,.69444,.12778,0,.56875],8764:[-.10889,.39111,0,0,.89444],8768:[.19444,.69444,0,0,.31944],8771:[.00222,.50222,0,0,.89444],8773:[.027,.638,0,0,.894],8776:[.02444,.52444,0,0,.89444],8781:[.00222,.50222,0,0,.89444],8801:[.00222,.50222,0,0,.89444],8804:[.19667,.69667,0,0,.89444],8805:[.19667,.69667,0,0,.89444],8810:[.08556,.58556,0,0,1.14999],8811:[.08556,.58556,0,0,1.14999],8826:[.08556,.58556,0,0,.89444],8827:[.08556,.58556,0,0,.89444],8834:[.08556,.58556,0,0,.89444],8835:[.08556,.58556,0,0,.89444],8838:[.19667,.69667,0,0,.89444],8839:[.19667,.69667,0,0,.89444],8846:[0,.55556,0,0,.76666],8849:[.19667,.69667,0,0,.89444],8850:[.19667,.69667,0,0,.89444],8851:[0,.55556,0,0,.76666],8852:[0,.55556,0,0,.76666],8853:[.13333,.63333,0,0,.89444],8854:[.13333,.63333,0,0,.89444],8855:[.13333,.63333,0,0,.89444],8856:[.13333,.63333,0,0,.89444],8857:[.13333,.63333,0,0,.89444],8866:[0,.69444,0,0,.70277],8867:[0,.69444,0,0,.70277],8868:[0,.69444,0,0,.89444],8869:[0,.69444,0,0,.89444],8900:[-.02639,.47361,0,0,.575],8901:[-.02639,.47361,0,0,.31944],8902:[-.02778,.47222,0,0,.575],8968:[.25,.75,0,0,.51111],8969:[.25,.75,0,0,.51111],8970:[.25,.75,0,0,.51111],8971:[.25,.75,0,0,.51111],8994:[-.13889,.36111,0,0,1.14999],8995:[-.13889,.36111,0,0,1.14999],9651:[.19444,.69444,0,0,1.02222],9657:[-.02778,.47222,0,0,.575],9661:[.19444,.69444,0,0,1.02222],9667:[-.02778,.47222,0,0,.575],9711:[.19444,.69444,0,0,1.14999],9824:[.12963,.69444,0,0,.89444],9825:[.12963,.69444,0,0,.89444],9826:[.12963,.69444,0,0,.89444],9827:[.12963,.69444,0,0,.89444],9837:[0,.75,0,0,.44722],9838:[.19444,.69444,0,0,.44722],9839:[.19444,.69444,0,0,.44722],10216:[.25,.75,0,0,.44722],10217:[.25,.75,0,0,.44722],10815:[0,.68611,0,0,.9],10927:[.19667,.69667,0,0,.89444],10928:[.19667,.69667,0,0,.89444],57376:[.19444,.69444,0,0,0]},"Main-BoldItalic":{32:[0,0,0,0,.25],33:[0,.69444,.11417,0,.38611],34:[0,.69444,.07939,0,.62055],35:[.19444,.69444,.06833,0,.94444],37:[.05556,.75,.12861,0,.94444],38:[0,.69444,.08528,0,.88555],39:[0,.69444,.12945,0,.35555],40:[.25,.75,.15806,0,.47333],41:[.25,.75,.03306,0,.47333],42:[0,.75,.14333,0,.59111],43:[.10333,.60333,.03306,0,.88555],44:[.19444,.14722,0,0,.35555],45:[0,.44444,.02611,0,.41444],46:[0,.14722,0,0,.35555],47:[.25,.75,.15806,0,.59111],48:[0,.64444,.13167,0,.59111],49:[0,.64444,.13167,0,.59111],50:[0,.64444,.13167,0,.59111],51:[0,.64444,.13167,0,.59111],52:[.19444,.64444,.13167,0,.59111],53:[0,.64444,.13167,0,.59111],54:[0,.64444,.13167,0,.59111],55:[.19444,.64444,.13167,0,.59111],56:[0,.64444,.13167,0,.59111],57:[0,.64444,.13167,0,.59111],58:[0,.44444,.06695,0,.35555],59:[.19444,.44444,.06695,0,.35555],61:[-.10889,.39111,.06833,0,.88555],63:[0,.69444,.11472,0,.59111],64:[0,.69444,.09208,0,.88555],65:[0,.68611,0,0,.86555],66:[0,.68611,.0992,0,.81666],67:[0,.68611,.14208,0,.82666],68:[0,.68611,.09062,0,.87555],69:[0,.68611,.11431,0,.75666],70:[0,.68611,.12903,0,.72722],71:[0,.68611,.07347,0,.89527],72:[0,.68611,.17208,0,.8961],73:[0,.68611,.15681,0,.47166],74:[0,.68611,.145,0,.61055],75:[0,.68611,.14208,0,.89499],76:[0,.68611,0,0,.69777],77:[0,.68611,.17208,0,1.07277],78:[0,.68611,.17208,0,.8961],79:[0,.68611,.09062,0,.85499],80:[0,.68611,.0992,0,.78721],81:[.19444,.68611,.09062,0,.85499],82:[0,.68611,.02559,0,.85944],83:[0,.68611,.11264,0,.64999],84:[0,.68611,.12903,0,.7961],85:[0,.68611,.17208,0,.88083],86:[0,.68611,.18625,0,.86555],87:[0,.68611,.18625,0,1.15999],88:[0,.68611,.15681,0,.86555],89:[0,.68611,.19803,0,.86555],90:[0,.68611,.14208,0,.70888],91:[.25,.75,.1875,0,.35611],93:[.25,.75,.09972,0,.35611],94:[0,.69444,.06709,0,.59111],95:[.31,.13444,.09811,0,.59111],97:[0,.44444,.09426,0,.59111],98:[0,.69444,.07861,0,.53222],99:[0,.44444,.05222,0,.53222],100:[0,.69444,.10861,0,.59111],101:[0,.44444,.085,0,.53222],102:[.19444,.69444,.21778,0,.4],103:[.19444,.44444,.105,0,.53222],104:[0,.69444,.09426,0,.59111],105:[0,.69326,.11387,0,.35555],106:[.19444,.69326,.1672,0,.35555],107:[0,.69444,.11111,0,.53222],108:[0,.69444,.10861,0,.29666],109:[0,.44444,.09426,0,.94444],110:[0,.44444,.09426,0,.64999],111:[0,.44444,.07861,0,.59111],112:[.19444,.44444,.07861,0,.59111],113:[.19444,.44444,.105,0,.53222],114:[0,.44444,.11111,0,.50167],115:[0,.44444,.08167,0,.48694],116:[0,.63492,.09639,0,.385],117:[0,.44444,.09426,0,.62055],118:[0,.44444,.11111,0,.53222],119:[0,.44444,.11111,0,.76777],120:[0,.44444,.12583,0,.56055],121:[.19444,.44444,.105,0,.56166],122:[0,.44444,.13889,0,.49055],126:[.35,.34444,.11472,0,.59111],160:[0,0,0,0,.25],168:[0,.69444,.11473,0,.59111],176:[0,.69444,0,0,.94888],184:[.17014,0,0,0,.53222],198:[0,.68611,.11431,0,1.02277],216:[.04861,.73472,.09062,0,.88555],223:[.19444,.69444,.09736,0,.665],230:[0,.44444,.085,0,.82666],248:[.09722,.54167,.09458,0,.59111],305:[0,.44444,.09426,0,.35555],338:[0,.68611,.11431,0,1.14054],339:[0,.44444,.085,0,.82666],567:[.19444,.44444,.04611,0,.385],710:[0,.69444,.06709,0,.59111],711:[0,.63194,.08271,0,.59111],713:[0,.59444,.10444,0,.59111],714:[0,.69444,.08528,0,.59111],715:[0,.69444,0,0,.59111],728:[0,.69444,.10333,0,.59111],729:[0,.69444,.12945,0,.35555],730:[0,.69444,0,0,.94888],732:[0,.69444,.11472,0,.59111],733:[0,.69444,.11472,0,.59111],915:[0,.68611,.12903,0,.69777],916:[0,.68611,0,0,.94444],920:[0,.68611,.09062,0,.88555],923:[0,.68611,0,0,.80666],926:[0,.68611,.15092,0,.76777],928:[0,.68611,.17208,0,.8961],931:[0,.68611,.11431,0,.82666],933:[0,.68611,.10778,0,.88555],934:[0,.68611,.05632,0,.82666],936:[0,.68611,.10778,0,.88555],937:[0,.68611,.0992,0,.82666],8211:[0,.44444,.09811,0,.59111],8212:[0,.44444,.09811,0,1.18221],8216:[0,.69444,.12945,0,.35555],8217:[0,.69444,.12945,0,.35555],8220:[0,.69444,.16772,0,.62055],8221:[0,.69444,.07939,0,.62055]},"Main-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.12417,0,.30667],34:[0,.69444,.06961,0,.51444],35:[.19444,.69444,.06616,0,.81777],37:[.05556,.75,.13639,0,.81777],38:[0,.69444,.09694,0,.76666],39:[0,.69444,.12417,0,.30667],40:[.25,.75,.16194,0,.40889],41:[.25,.75,.03694,0,.40889],42:[0,.75,.14917,0,.51111],43:[.05667,.56167,.03694,0,.76666],44:[.19444,.10556,0,0,.30667],45:[0,.43056,.02826,0,.35778],46:[0,.10556,0,0,.30667],47:[.25,.75,.16194,0,.51111],48:[0,.64444,.13556,0,.51111],49:[0,.64444,.13556,0,.51111],50:[0,.64444,.13556,0,.51111],51:[0,.64444,.13556,0,.51111],52:[.19444,.64444,.13556,0,.51111],53:[0,.64444,.13556,0,.51111],54:[0,.64444,.13556,0,.51111],55:[.19444,.64444,.13556,0,.51111],56:[0,.64444,.13556,0,.51111],57:[0,.64444,.13556,0,.51111],58:[0,.43056,.0582,0,.30667],59:[.19444,.43056,.0582,0,.30667],61:[-.13313,.36687,.06616,0,.76666],63:[0,.69444,.1225,0,.51111],64:[0,.69444,.09597,0,.76666],65:[0,.68333,0,0,.74333],66:[0,.68333,.10257,0,.70389],67:[0,.68333,.14528,0,.71555],68:[0,.68333,.09403,0,.755],69:[0,.68333,.12028,0,.67833],70:[0,.68333,.13305,0,.65277],71:[0,.68333,.08722,0,.77361],72:[0,.68333,.16389,0,.74333],73:[0,.68333,.15806,0,.38555],74:[0,.68333,.14028,0,.525],75:[0,.68333,.14528,0,.76888],76:[0,.68333,0,0,.62722],77:[0,.68333,.16389,0,.89666],78:[0,.68333,.16389,0,.74333],79:[0,.68333,.09403,0,.76666],80:[0,.68333,.10257,0,.67833],81:[.19444,.68333,.09403,0,.76666],82:[0,.68333,.03868,0,.72944],83:[0,.68333,.11972,0,.56222],84:[0,.68333,.13305,0,.71555],85:[0,.68333,.16389,0,.74333],86:[0,.68333,.18361,0,.74333],87:[0,.68333,.18361,0,.99888],88:[0,.68333,.15806,0,.74333],89:[0,.68333,.19383,0,.74333],90:[0,.68333,.14528,0,.61333],91:[.25,.75,.1875,0,.30667],93:[.25,.75,.10528,0,.30667],94:[0,.69444,.06646,0,.51111],95:[.31,.12056,.09208,0,.51111],97:[0,.43056,.07671,0,.51111],98:[0,.69444,.06312,0,.46],99:[0,.43056,.05653,0,.46],100:[0,.69444,.10333,0,.51111],101:[0,.43056,.07514,0,.46],102:[.19444,.69444,.21194,0,.30667],103:[.19444,.43056,.08847,0,.46],104:[0,.69444,.07671,0,.51111],105:[0,.65536,.1019,0,.30667],106:[.19444,.65536,.14467,0,.30667],107:[0,.69444,.10764,0,.46],108:[0,.69444,.10333,0,.25555],109:[0,.43056,.07671,0,.81777],110:[0,.43056,.07671,0,.56222],111:[0,.43056,.06312,0,.51111],112:[.19444,.43056,.06312,0,.51111],113:[.19444,.43056,.08847,0,.46],114:[0,.43056,.10764,0,.42166],115:[0,.43056,.08208,0,.40889],116:[0,.61508,.09486,0,.33222],117:[0,.43056,.07671,0,.53666],118:[0,.43056,.10764,0,.46],119:[0,.43056,.10764,0,.66444],120:[0,.43056,.12042,0,.46389],121:[.19444,.43056,.08847,0,.48555],122:[0,.43056,.12292,0,.40889],126:[.35,.31786,.11585,0,.51111],160:[0,0,0,0,.25],168:[0,.66786,.10474,0,.51111],176:[0,.69444,0,0,.83129],184:[.17014,0,0,0,.46],198:[0,.68333,.12028,0,.88277],216:[.04861,.73194,.09403,0,.76666],223:[.19444,.69444,.10514,0,.53666],230:[0,.43056,.07514,0,.71555],248:[.09722,.52778,.09194,0,.51111],338:[0,.68333,.12028,0,.98499],339:[0,.43056,.07514,0,.71555],710:[0,.69444,.06646,0,.51111],711:[0,.62847,.08295,0,.51111],713:[0,.56167,.10333,0,.51111],714:[0,.69444,.09694,0,.51111],715:[0,.69444,0,0,.51111],728:[0,.69444,.10806,0,.51111],729:[0,.66786,.11752,0,.30667],730:[0,.69444,0,0,.83129],732:[0,.66786,.11585,0,.51111],733:[0,.69444,.1225,0,.51111],915:[0,.68333,.13305,0,.62722],916:[0,.68333,0,0,.81777],920:[0,.68333,.09403,0,.76666],923:[0,.68333,0,0,.69222],926:[0,.68333,.15294,0,.66444],928:[0,.68333,.16389,0,.74333],931:[0,.68333,.12028,0,.71555],933:[0,.68333,.11111,0,.76666],934:[0,.68333,.05986,0,.71555],936:[0,.68333,.11111,0,.76666],937:[0,.68333,.10257,0,.71555],8211:[0,.43056,.09208,0,.51111],8212:[0,.43056,.09208,0,1.02222],8216:[0,.69444,.12417,0,.30667],8217:[0,.69444,.12417,0,.30667],8220:[0,.69444,.1685,0,.51444],8221:[0,.69444,.06961,0,.51444],8463:[0,.68889,0,0,.54028]},"Main-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.27778],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.77778],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.19444,.10556,0,0,.27778],45:[0,.43056,0,0,.33333],46:[0,.10556,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.64444,0,0,.5],49:[0,.64444,0,0,.5],50:[0,.64444,0,0,.5],51:[0,.64444,0,0,.5],52:[0,.64444,0,0,.5],53:[0,.64444,0,0,.5],54:[0,.64444,0,0,.5],55:[0,.64444,0,0,.5],56:[0,.64444,0,0,.5],57:[0,.64444,0,0,.5],58:[0,.43056,0,0,.27778],59:[.19444,.43056,0,0,.27778],60:[.0391,.5391,0,0,.77778],61:[-.13313,.36687,0,0,.77778],62:[.0391,.5391,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.77778],65:[0,.68333,0,0,.75],66:[0,.68333,0,0,.70834],67:[0,.68333,0,0,.72222],68:[0,.68333,0,0,.76389],69:[0,.68333,0,0,.68056],70:[0,.68333,0,0,.65278],71:[0,.68333,0,0,.78472],72:[0,.68333,0,0,.75],73:[0,.68333,0,0,.36111],74:[0,.68333,0,0,.51389],75:[0,.68333,0,0,.77778],76:[0,.68333,0,0,.625],77:[0,.68333,0,0,.91667],78:[0,.68333,0,0,.75],79:[0,.68333,0,0,.77778],80:[0,.68333,0,0,.68056],81:[.19444,.68333,0,0,.77778],82:[0,.68333,0,0,.73611],83:[0,.68333,0,0,.55556],84:[0,.68333,0,0,.72222],85:[0,.68333,0,0,.75],86:[0,.68333,.01389,0,.75],87:[0,.68333,.01389,0,1.02778],88:[0,.68333,0,0,.75],89:[0,.68333,.025,0,.75],90:[0,.68333,0,0,.61111],91:[.25,.75,0,0,.27778],92:[.25,.75,0,0,.5],93:[.25,.75,0,0,.27778],94:[0,.69444,0,0,.5],95:[.31,.12056,.02778,0,.5],97:[0,.43056,0,0,.5],98:[0,.69444,0,0,.55556],99:[0,.43056,0,0,.44445],100:[0,.69444,0,0,.55556],101:[0,.43056,0,0,.44445],102:[0,.69444,.07778,0,.30556],103:[.19444,.43056,.01389,0,.5],104:[0,.69444,0,0,.55556],105:[0,.66786,0,0,.27778],106:[.19444,.66786,0,0,.30556],107:[0,.69444,0,0,.52778],108:[0,.69444,0,0,.27778],109:[0,.43056,0,0,.83334],110:[0,.43056,0,0,.55556],111:[0,.43056,0,0,.5],112:[.19444,.43056,0,0,.55556],113:[.19444,.43056,0,0,.52778],114:[0,.43056,0,0,.39167],115:[0,.43056,0,0,.39445],116:[0,.61508,0,0,.38889],117:[0,.43056,0,0,.55556],118:[0,.43056,.01389,0,.52778],119:[0,.43056,.01389,0,.72222],120:[0,.43056,0,0,.52778],121:[.19444,.43056,.01389,0,.52778],122:[0,.43056,0,0,.44445],123:[.25,.75,0,0,.5],124:[.25,.75,0,0,.27778],125:[.25,.75,0,0,.5],126:[.35,.31786,0,0,.5],160:[0,0,0,0,.25],163:[0,.69444,0,0,.76909],167:[.19444,.69444,0,0,.44445],168:[0,.66786,0,0,.5],172:[0,.43056,0,0,.66667],176:[0,.69444,0,0,.75],177:[.08333,.58333,0,0,.77778],182:[.19444,.69444,0,0,.61111],184:[.17014,0,0,0,.44445],198:[0,.68333,0,0,.90278],215:[.08333,.58333,0,0,.77778],216:[.04861,.73194,0,0,.77778],223:[0,.69444,0,0,.5],230:[0,.43056,0,0,.72222],247:[.08333,.58333,0,0,.77778],248:[.09722,.52778,0,0,.5],305:[0,.43056,0,0,.27778],338:[0,.68333,0,0,1.01389],339:[0,.43056,0,0,.77778],567:[.19444,.43056,0,0,.30556],710:[0,.69444,0,0,.5],711:[0,.62847,0,0,.5],713:[0,.56778,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.66786,0,0,.27778],730:[0,.69444,0,0,.75],732:[0,.66786,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.68333,0,0,.625],916:[0,.68333,0,0,.83334],920:[0,.68333,0,0,.77778],923:[0,.68333,0,0,.69445],926:[0,.68333,0,0,.66667],928:[0,.68333,0,0,.75],931:[0,.68333,0,0,.72222],933:[0,.68333,0,0,.77778],934:[0,.68333,0,0,.72222],936:[0,.68333,0,0,.77778],937:[0,.68333,0,0,.72222],8211:[0,.43056,.02778,0,.5],8212:[0,.43056,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5],8224:[.19444,.69444,0,0,.44445],8225:[.19444,.69444,0,0,.44445],8230:[0,.123,0,0,1.172],8242:[0,.55556,0,0,.275],8407:[0,.71444,.15382,0,.5],8463:[0,.68889,0,0,.54028],8465:[0,.69444,0,0,.72222],8467:[0,.69444,0,.11111,.41667],8472:[.19444,.43056,0,.11111,.63646],8476:[0,.69444,0,0,.72222],8501:[0,.69444,0,0,.61111],8592:[-.13313,.36687,0,0,1],8593:[.19444,.69444,0,0,.5],8594:[-.13313,.36687,0,0,1],8595:[.19444,.69444,0,0,.5],8596:[-.13313,.36687,0,0,1],8597:[.25,.75,0,0,.5],8598:[.19444,.69444,0,0,1],8599:[.19444,.69444,0,0,1],8600:[.19444,.69444,0,0,1],8601:[.19444,.69444,0,0,1],8614:[.011,.511,0,0,1],8617:[.011,.511,0,0,1.126],8618:[.011,.511,0,0,1.126],8636:[-.13313,.36687,0,0,1],8637:[-.13313,.36687,0,0,1],8640:[-.13313,.36687,0,0,1],8641:[-.13313,.36687,0,0,1],8652:[.011,.671,0,0,1],8656:[-.13313,.36687,0,0,1],8657:[.19444,.69444,0,0,.61111],8658:[-.13313,.36687,0,0,1],8659:[.19444,.69444,0,0,.61111],8660:[-.13313,.36687,0,0,1],8661:[.25,.75,0,0,.61111],8704:[0,.69444,0,0,.55556],8706:[0,.69444,.05556,.08334,.5309],8707:[0,.69444,0,0,.55556],8709:[.05556,.75,0,0,.5],8711:[0,.68333,0,0,.83334],8712:[.0391,.5391,0,0,.66667],8715:[.0391,.5391,0,0,.66667],8722:[.08333,.58333,0,0,.77778],8723:[.08333,.58333,0,0,.77778],8725:[.25,.75,0,0,.5],8726:[.25,.75,0,0,.5],8727:[-.03472,.46528,0,0,.5],8728:[-.05555,.44445,0,0,.5],8729:[-.05555,.44445,0,0,.5],8730:[.2,.8,0,0,.83334],8733:[0,.43056,0,0,.77778],8734:[0,.43056,0,0,1],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.27778],8741:[.25,.75,0,0,.5],8743:[0,.55556,0,0,.66667],8744:[0,.55556,0,0,.66667],8745:[0,.55556,0,0,.66667],8746:[0,.55556,0,0,.66667],8747:[.19444,.69444,.11111,0,.41667],8764:[-.13313,.36687,0,0,.77778],8768:[.19444,.69444,0,0,.27778],8771:[-.03625,.46375,0,0,.77778],8773:[-.022,.589,0,0,.778],8776:[-.01688,.48312,0,0,.77778],8781:[-.03625,.46375,0,0,.77778],8784:[-.133,.673,0,0,.778],8801:[-.03625,.46375,0,0,.77778],8804:[.13597,.63597,0,0,.77778],8805:[.13597,.63597,0,0,.77778],8810:[.0391,.5391,0,0,1],8811:[.0391,.5391,0,0,1],8826:[.0391,.5391,0,0,.77778],8827:[.0391,.5391,0,0,.77778],8834:[.0391,.5391,0,0,.77778],8835:[.0391,.5391,0,0,.77778],8838:[.13597,.63597,0,0,.77778],8839:[.13597,.63597,0,0,.77778],8846:[0,.55556,0,0,.66667],8849:[.13597,.63597,0,0,.77778],8850:[.13597,.63597,0,0,.77778],8851:[0,.55556,0,0,.66667],8852:[0,.55556,0,0,.66667],8853:[.08333,.58333,0,0,.77778],8854:[.08333,.58333,0,0,.77778],8855:[.08333,.58333,0,0,.77778],8856:[.08333,.58333,0,0,.77778],8857:[.08333,.58333,0,0,.77778],8866:[0,.69444,0,0,.61111],8867:[0,.69444,0,0,.61111],8868:[0,.69444,0,0,.77778],8869:[0,.69444,0,0,.77778],8872:[.249,.75,0,0,.867],8900:[-.05555,.44445,0,0,.5],8901:[-.05555,.44445,0,0,.27778],8902:[-.03472,.46528,0,0,.5],8904:[.005,.505,0,0,.9],8942:[.03,.903,0,0,.278],8943:[-.19,.313,0,0,1.172],8945:[-.1,.823,0,0,1.282],8968:[.25,.75,0,0,.44445],8969:[.25,.75,0,0,.44445],8970:[.25,.75,0,0,.44445],8971:[.25,.75,0,0,.44445],8994:[-.14236,.35764,0,0,1],8995:[-.14236,.35764,0,0,1],9136:[.244,.744,0,0,.412],9137:[.244,.745,0,0,.412],9651:[.19444,.69444,0,0,.88889],9657:[-.03472,.46528,0,0,.5],9661:[.19444,.69444,0,0,.88889],9667:[-.03472,.46528,0,0,.5],9711:[.19444,.69444,0,0,1],9824:[.12963,.69444,0,0,.77778],9825:[.12963,.69444,0,0,.77778],9826:[.12963,.69444,0,0,.77778],9827:[.12963,.69444,0,0,.77778],9837:[0,.75,0,0,.38889],9838:[.19444,.69444,0,0,.38889],9839:[.19444,.69444,0,0,.38889],10216:[.25,.75,0,0,.38889],10217:[.25,.75,0,0,.38889],10222:[.244,.744,0,0,.412],10223:[.244,.745,0,0,.412],10229:[.011,.511,0,0,1.609],10230:[.011,.511,0,0,1.638],10231:[.011,.511,0,0,1.859],10232:[.024,.525,0,0,1.609],10233:[.024,.525,0,0,1.638],10234:[.024,.525,0,0,1.858],10236:[.011,.511,0,0,1.638],10815:[0,.68333,0,0,.75],10927:[.13597,.63597,0,0,.77778],10928:[.13597,.63597,0,0,.77778],57376:[.19444,.69444,0,0,0]},"Math-BoldItalic":{32:[0,0,0,0,.25],48:[0,.44444,0,0,.575],49:[0,.44444,0,0,.575],50:[0,.44444,0,0,.575],51:[.19444,.44444,0,0,.575],52:[.19444,.44444,0,0,.575],53:[.19444,.44444,0,0,.575],54:[0,.64444,0,0,.575],55:[.19444,.44444,0,0,.575],56:[0,.64444,0,0,.575],57:[.19444,.44444,0,0,.575],65:[0,.68611,0,0,.86944],66:[0,.68611,.04835,0,.8664],67:[0,.68611,.06979,0,.81694],68:[0,.68611,.03194,0,.93812],69:[0,.68611,.05451,0,.81007],70:[0,.68611,.15972,0,.68889],71:[0,.68611,0,0,.88673],72:[0,.68611,.08229,0,.98229],73:[0,.68611,.07778,0,.51111],74:[0,.68611,.10069,0,.63125],75:[0,.68611,.06979,0,.97118],76:[0,.68611,0,0,.75555],77:[0,.68611,.11424,0,1.14201],78:[0,.68611,.11424,0,.95034],79:[0,.68611,.03194,0,.83666],80:[0,.68611,.15972,0,.72309],81:[.19444,.68611,0,0,.86861],82:[0,.68611,.00421,0,.87235],83:[0,.68611,.05382,0,.69271],84:[0,.68611,.15972,0,.63663],85:[0,.68611,.11424,0,.80027],86:[0,.68611,.25555,0,.67778],87:[0,.68611,.15972,0,1.09305],88:[0,.68611,.07778,0,.94722],89:[0,.68611,.25555,0,.67458],90:[0,.68611,.06979,0,.77257],97:[0,.44444,0,0,.63287],98:[0,.69444,0,0,.52083],99:[0,.44444,0,0,.51342],100:[0,.69444,0,0,.60972],101:[0,.44444,0,0,.55361],102:[.19444,.69444,.11042,0,.56806],103:[.19444,.44444,.03704,0,.5449],104:[0,.69444,0,0,.66759],105:[0,.69326,0,0,.4048],106:[.19444,.69326,.0622,0,.47083],107:[0,.69444,.01852,0,.6037],108:[0,.69444,.0088,0,.34815],109:[0,.44444,0,0,1.0324],110:[0,.44444,0,0,.71296],111:[0,.44444,0,0,.58472],112:[.19444,.44444,0,0,.60092],113:[.19444,.44444,.03704,0,.54213],114:[0,.44444,.03194,0,.5287],115:[0,.44444,0,0,.53125],116:[0,.63492,0,0,.41528],117:[0,.44444,0,0,.68102],118:[0,.44444,.03704,0,.56666],119:[0,.44444,.02778,0,.83148],120:[0,.44444,0,0,.65903],121:[.19444,.44444,.03704,0,.59028],122:[0,.44444,.04213,0,.55509],160:[0,0,0,0,.25],915:[0,.68611,.15972,0,.65694],916:[0,.68611,0,0,.95833],920:[0,.68611,.03194,0,.86722],923:[0,.68611,0,0,.80555],926:[0,.68611,.07458,0,.84125],928:[0,.68611,.08229,0,.98229],931:[0,.68611,.05451,0,.88507],933:[0,.68611,.15972,0,.67083],934:[0,.68611,0,0,.76666],936:[0,.68611,.11653,0,.71402],937:[0,.68611,.04835,0,.8789],945:[0,.44444,0,0,.76064],946:[.19444,.69444,.03403,0,.65972],947:[.19444,.44444,.06389,0,.59003],948:[0,.69444,.03819,0,.52222],949:[0,.44444,0,0,.52882],950:[.19444,.69444,.06215,0,.50833],951:[.19444,.44444,.03704,0,.6],952:[0,.69444,.03194,0,.5618],953:[0,.44444,0,0,.41204],954:[0,.44444,0,0,.66759],955:[0,.69444,0,0,.67083],956:[.19444,.44444,0,0,.70787],957:[0,.44444,.06898,0,.57685],958:[.19444,.69444,.03021,0,.50833],959:[0,.44444,0,0,.58472],960:[0,.44444,.03704,0,.68241],961:[.19444,.44444,0,0,.6118],962:[.09722,.44444,.07917,0,.42361],963:[0,.44444,.03704,0,.68588],964:[0,.44444,.13472,0,.52083],965:[0,.44444,.03704,0,.63055],966:[.19444,.44444,0,0,.74722],967:[.19444,.44444,0,0,.71805],968:[.19444,.69444,.03704,0,.75833],969:[0,.44444,.03704,0,.71782],977:[0,.69444,0,0,.69155],981:[.19444,.69444,0,0,.7125],982:[0,.44444,.03194,0,.975],1009:[.19444,.44444,0,0,.6118],1013:[0,.44444,0,0,.48333],57649:[0,.44444,0,0,.39352],57911:[.19444,.44444,0,0,.43889]},"Math-Italic":{32:[0,0,0,0,.25],48:[0,.43056,0,0,.5],49:[0,.43056,0,0,.5],50:[0,.43056,0,0,.5],51:[.19444,.43056,0,0,.5],52:[.19444,.43056,0,0,.5],53:[.19444,.43056,0,0,.5],54:[0,.64444,0,0,.5],55:[.19444,.43056,0,0,.5],56:[0,.64444,0,0,.5],57:[.19444,.43056,0,0,.5],65:[0,.68333,0,.13889,.75],66:[0,.68333,.05017,.08334,.75851],67:[0,.68333,.07153,.08334,.71472],68:[0,.68333,.02778,.05556,.82792],69:[0,.68333,.05764,.08334,.7382],70:[0,.68333,.13889,.08334,.64306],71:[0,.68333,0,.08334,.78625],72:[0,.68333,.08125,.05556,.83125],73:[0,.68333,.07847,.11111,.43958],74:[0,.68333,.09618,.16667,.55451],75:[0,.68333,.07153,.05556,.84931],76:[0,.68333,0,.02778,.68056],77:[0,.68333,.10903,.08334,.97014],78:[0,.68333,.10903,.08334,.80347],79:[0,.68333,.02778,.08334,.76278],80:[0,.68333,.13889,.08334,.64201],81:[.19444,.68333,0,.08334,.79056],82:[0,.68333,.00773,.08334,.75929],83:[0,.68333,.05764,.08334,.6132],84:[0,.68333,.13889,.08334,.58438],85:[0,.68333,.10903,.02778,.68278],86:[0,.68333,.22222,0,.58333],87:[0,.68333,.13889,0,.94445],88:[0,.68333,.07847,.08334,.82847],89:[0,.68333,.22222,0,.58056],90:[0,.68333,.07153,.08334,.68264],97:[0,.43056,0,0,.52859],98:[0,.69444,0,0,.42917],99:[0,.43056,0,.05556,.43276],100:[0,.69444,0,.16667,.52049],101:[0,.43056,0,.05556,.46563],102:[.19444,.69444,.10764,.16667,.48959],103:[.19444,.43056,.03588,.02778,.47697],104:[0,.69444,0,0,.57616],105:[0,.65952,0,0,.34451],106:[.19444,.65952,.05724,0,.41181],107:[0,.69444,.03148,0,.5206],108:[0,.69444,.01968,.08334,.29838],109:[0,.43056,0,0,.87801],110:[0,.43056,0,0,.60023],111:[0,.43056,0,.05556,.48472],112:[.19444,.43056,0,.08334,.50313],113:[.19444,.43056,.03588,.08334,.44641],114:[0,.43056,.02778,.05556,.45116],115:[0,.43056,0,.05556,.46875],116:[0,.61508,0,.08334,.36111],117:[0,.43056,0,.02778,.57246],118:[0,.43056,.03588,.02778,.48472],119:[0,.43056,.02691,.08334,.71592],120:[0,.43056,0,.02778,.57153],121:[.19444,.43056,.03588,.05556,.49028],122:[0,.43056,.04398,.05556,.46505],160:[0,0,0,0,.25],915:[0,.68333,.13889,.08334,.61528],916:[0,.68333,0,.16667,.83334],920:[0,.68333,.02778,.08334,.76278],923:[0,.68333,0,.16667,.69445],926:[0,.68333,.07569,.08334,.74236],928:[0,.68333,.08125,.05556,.83125],931:[0,.68333,.05764,.08334,.77986],933:[0,.68333,.13889,.05556,.58333],934:[0,.68333,0,.08334,.66667],936:[0,.68333,.11,.05556,.61222],937:[0,.68333,.05017,.08334,.7724],945:[0,.43056,.0037,.02778,.6397],946:[.19444,.69444,.05278,.08334,.56563],947:[.19444,.43056,.05556,0,.51773],948:[0,.69444,.03785,.05556,.44444],949:[0,.43056,0,.08334,.46632],950:[.19444,.69444,.07378,.08334,.4375],951:[.19444,.43056,.03588,.05556,.49653],952:[0,.69444,.02778,.08334,.46944],953:[0,.43056,0,.05556,.35394],954:[0,.43056,0,0,.57616],955:[0,.69444,0,0,.58334],956:[.19444,.43056,0,.02778,.60255],957:[0,.43056,.06366,.02778,.49398],958:[.19444,.69444,.04601,.11111,.4375],959:[0,.43056,0,.05556,.48472],960:[0,.43056,.03588,0,.57003],961:[.19444,.43056,0,.08334,.51702],962:[.09722,.43056,.07986,.08334,.36285],963:[0,.43056,.03588,0,.57141],964:[0,.43056,.1132,.02778,.43715],965:[0,.43056,.03588,.02778,.54028],966:[.19444,.43056,0,.08334,.65417],967:[.19444,.43056,0,.05556,.62569],968:[.19444,.69444,.03588,.11111,.65139],969:[0,.43056,.03588,0,.62245],977:[0,.69444,0,.08334,.59144],981:[.19444,.69444,0,.08334,.59583],982:[0,.43056,.02778,0,.82813],1009:[.19444,.43056,0,.08334,.51702],1013:[0,.43056,0,.05556,.4059],57649:[0,.43056,0,.02778,.32246],57911:[.19444,.43056,0,.08334,.38403]},"SansSerif-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.36667],34:[0,.69444,0,0,.55834],35:[.19444,.69444,0,0,.91667],36:[.05556,.75,0,0,.55],37:[.05556,.75,0,0,1.02912],38:[0,.69444,0,0,.83056],39:[0,.69444,0,0,.30556],40:[.25,.75,0,0,.42778],41:[.25,.75,0,0,.42778],42:[0,.75,0,0,.55],43:[.11667,.61667,0,0,.85556],44:[.10556,.13056,0,0,.30556],45:[0,.45833,0,0,.36667],46:[0,.13056,0,0,.30556],47:[.25,.75,0,0,.55],48:[0,.69444,0,0,.55],49:[0,.69444,0,0,.55],50:[0,.69444,0,0,.55],51:[0,.69444,0,0,.55],52:[0,.69444,0,0,.55],53:[0,.69444,0,0,.55],54:[0,.69444,0,0,.55],55:[0,.69444,0,0,.55],56:[0,.69444,0,0,.55],57:[0,.69444,0,0,.55],58:[0,.45833,0,0,.30556],59:[.10556,.45833,0,0,.30556],61:[-.09375,.40625,0,0,.85556],63:[0,.69444,0,0,.51945],64:[0,.69444,0,0,.73334],65:[0,.69444,0,0,.73334],66:[0,.69444,0,0,.73334],67:[0,.69444,0,0,.70278],68:[0,.69444,0,0,.79445],69:[0,.69444,0,0,.64167],70:[0,.69444,0,0,.61111],71:[0,.69444,0,0,.73334],72:[0,.69444,0,0,.79445],73:[0,.69444,0,0,.33056],74:[0,.69444,0,0,.51945],75:[0,.69444,0,0,.76389],76:[0,.69444,0,0,.58056],77:[0,.69444,0,0,.97778],78:[0,.69444,0,0,.79445],79:[0,.69444,0,0,.79445],80:[0,.69444,0,0,.70278],81:[.10556,.69444,0,0,.79445],82:[0,.69444,0,0,.70278],83:[0,.69444,0,0,.61111],84:[0,.69444,0,0,.73334],85:[0,.69444,0,0,.76389],86:[0,.69444,.01528,0,.73334],87:[0,.69444,.01528,0,1.03889],88:[0,.69444,0,0,.73334],89:[0,.69444,.0275,0,.73334],90:[0,.69444,0,0,.67223],91:[.25,.75,0,0,.34306],93:[.25,.75,0,0,.34306],94:[0,.69444,0,0,.55],95:[.35,.10833,.03056,0,.55],97:[0,.45833,0,0,.525],98:[0,.69444,0,0,.56111],99:[0,.45833,0,0,.48889],100:[0,.69444,0,0,.56111],101:[0,.45833,0,0,.51111],102:[0,.69444,.07639,0,.33611],103:[.19444,.45833,.01528,0,.55],104:[0,.69444,0,0,.56111],105:[0,.69444,0,0,.25556],106:[.19444,.69444,0,0,.28611],107:[0,.69444,0,0,.53056],108:[0,.69444,0,0,.25556],109:[0,.45833,0,0,.86667],110:[0,.45833,0,0,.56111],111:[0,.45833,0,0,.55],112:[.19444,.45833,0,0,.56111],113:[.19444,.45833,0,0,.56111],114:[0,.45833,.01528,0,.37222],115:[0,.45833,0,0,.42167],116:[0,.58929,0,0,.40417],117:[0,.45833,0,0,.56111],118:[0,.45833,.01528,0,.5],119:[0,.45833,.01528,0,.74445],120:[0,.45833,0,0,.5],121:[.19444,.45833,.01528,0,.5],122:[0,.45833,0,0,.47639],126:[.35,.34444,0,0,.55],160:[0,0,0,0,.25],168:[0,.69444,0,0,.55],176:[0,.69444,0,0,.73334],180:[0,.69444,0,0,.55],184:[.17014,0,0,0,.48889],305:[0,.45833,0,0,.25556],567:[.19444,.45833,0,0,.28611],710:[0,.69444,0,0,.55],711:[0,.63542,0,0,.55],713:[0,.63778,0,0,.55],728:[0,.69444,0,0,.55],729:[0,.69444,0,0,.30556],730:[0,.69444,0,0,.73334],732:[0,.69444,0,0,.55],733:[0,.69444,0,0,.55],915:[0,.69444,0,0,.58056],916:[0,.69444,0,0,.91667],920:[0,.69444,0,0,.85556],923:[0,.69444,0,0,.67223],926:[0,.69444,0,0,.73334],928:[0,.69444,0,0,.79445],931:[0,.69444,0,0,.79445],933:[0,.69444,0,0,.85556],934:[0,.69444,0,0,.79445],936:[0,.69444,0,0,.85556],937:[0,.69444,0,0,.79445],8211:[0,.45833,.03056,0,.55],8212:[0,.45833,.03056,0,1.10001],8216:[0,.69444,0,0,.30556],8217:[0,.69444,0,0,.30556],8220:[0,.69444,0,0,.55834],8221:[0,.69444,0,0,.55834]},"SansSerif-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.05733,0,.31945],34:[0,.69444,.00316,0,.5],35:[.19444,.69444,.05087,0,.83334],36:[.05556,.75,.11156,0,.5],37:[.05556,.75,.03126,0,.83334],38:[0,.69444,.03058,0,.75834],39:[0,.69444,.07816,0,.27778],40:[.25,.75,.13164,0,.38889],41:[.25,.75,.02536,0,.38889],42:[0,.75,.11775,0,.5],43:[.08333,.58333,.02536,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,.01946,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,.13164,0,.5],48:[0,.65556,.11156,0,.5],49:[0,.65556,.11156,0,.5],50:[0,.65556,.11156,0,.5],51:[0,.65556,.11156,0,.5],52:[0,.65556,.11156,0,.5],53:[0,.65556,.11156,0,.5],54:[0,.65556,.11156,0,.5],55:[0,.65556,.11156,0,.5],56:[0,.65556,.11156,0,.5],57:[0,.65556,.11156,0,.5],58:[0,.44444,.02502,0,.27778],59:[.125,.44444,.02502,0,.27778],61:[-.13,.37,.05087,0,.77778],63:[0,.69444,.11809,0,.47222],64:[0,.69444,.07555,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,.08293,0,.66667],67:[0,.69444,.11983,0,.63889],68:[0,.69444,.07555,0,.72223],69:[0,.69444,.11983,0,.59722],70:[0,.69444,.13372,0,.56945],71:[0,.69444,.11983,0,.66667],72:[0,.69444,.08094,0,.70834],73:[0,.69444,.13372,0,.27778],74:[0,.69444,.08094,0,.47222],75:[0,.69444,.11983,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,.08094,0,.875],78:[0,.69444,.08094,0,.70834],79:[0,.69444,.07555,0,.73611],80:[0,.69444,.08293,0,.63889],81:[.125,.69444,.07555,0,.73611],82:[0,.69444,.08293,0,.64584],83:[0,.69444,.09205,0,.55556],84:[0,.69444,.13372,0,.68056],85:[0,.69444,.08094,0,.6875],86:[0,.69444,.1615,0,.66667],87:[0,.69444,.1615,0,.94445],88:[0,.69444,.13372,0,.66667],89:[0,.69444,.17261,0,.66667],90:[0,.69444,.11983,0,.61111],91:[.25,.75,.15942,0,.28889],93:[.25,.75,.08719,0,.28889],94:[0,.69444,.0799,0,.5],95:[.35,.09444,.08616,0,.5],97:[0,.44444,.00981,0,.48056],98:[0,.69444,.03057,0,.51667],99:[0,.44444,.08336,0,.44445],100:[0,.69444,.09483,0,.51667],101:[0,.44444,.06778,0,.44445],102:[0,.69444,.21705,0,.30556],103:[.19444,.44444,.10836,0,.5],104:[0,.69444,.01778,0,.51667],105:[0,.67937,.09718,0,.23889],106:[.19444,.67937,.09162,0,.26667],107:[0,.69444,.08336,0,.48889],108:[0,.69444,.09483,0,.23889],109:[0,.44444,.01778,0,.79445],110:[0,.44444,.01778,0,.51667],111:[0,.44444,.06613,0,.5],112:[.19444,.44444,.0389,0,.51667],113:[.19444,.44444,.04169,0,.51667],114:[0,.44444,.10836,0,.34167],115:[0,.44444,.0778,0,.38333],116:[0,.57143,.07225,0,.36111],117:[0,.44444,.04169,0,.51667],118:[0,.44444,.10836,0,.46111],119:[0,.44444,.10836,0,.68334],120:[0,.44444,.09169,0,.46111],121:[.19444,.44444,.10836,0,.46111],122:[0,.44444,.08752,0,.43472],126:[.35,.32659,.08826,0,.5],160:[0,0,0,0,.25],168:[0,.67937,.06385,0,.5],176:[0,.69444,0,0,.73752],184:[.17014,0,0,0,.44445],305:[0,.44444,.04169,0,.23889],567:[.19444,.44444,.04169,0,.26667],710:[0,.69444,.0799,0,.5],711:[0,.63194,.08432,0,.5],713:[0,.60889,.08776,0,.5],714:[0,.69444,.09205,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,.09483,0,.5],729:[0,.67937,.07774,0,.27778],730:[0,.69444,0,0,.73752],732:[0,.67659,.08826,0,.5],733:[0,.69444,.09205,0,.5],915:[0,.69444,.13372,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,.07555,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,.12816,0,.66667],928:[0,.69444,.08094,0,.70834],931:[0,.69444,.11983,0,.72222],933:[0,.69444,.09031,0,.77778],934:[0,.69444,.04603,0,.72222],936:[0,.69444,.09031,0,.77778],937:[0,.69444,.08293,0,.72222],8211:[0,.44444,.08616,0,.5],8212:[0,.44444,.08616,0,1],8216:[0,.69444,.07816,0,.27778],8217:[0,.69444,.07816,0,.27778],8220:[0,.69444,.14205,0,.5],8221:[0,.69444,.00316,0,.5]},"SansSerif-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.31945],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.75834],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,0,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.65556,0,0,.5],49:[0,.65556,0,0,.5],50:[0,.65556,0,0,.5],51:[0,.65556,0,0,.5],52:[0,.65556,0,0,.5],53:[0,.65556,0,0,.5],54:[0,.65556,0,0,.5],55:[0,.65556,0,0,.5],56:[0,.65556,0,0,.5],57:[0,.65556,0,0,.5],58:[0,.44444,0,0,.27778],59:[.125,.44444,0,0,.27778],61:[-.13,.37,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,0,0,.66667],67:[0,.69444,0,0,.63889],68:[0,.69444,0,0,.72223],69:[0,.69444,0,0,.59722],70:[0,.69444,0,0,.56945],71:[0,.69444,0,0,.66667],72:[0,.69444,0,0,.70834],73:[0,.69444,0,0,.27778],74:[0,.69444,0,0,.47222],75:[0,.69444,0,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,0,0,.875],78:[0,.69444,0,0,.70834],79:[0,.69444,0,0,.73611],80:[0,.69444,0,0,.63889],81:[.125,.69444,0,0,.73611],82:[0,.69444,0,0,.64584],83:[0,.69444,0,0,.55556],84:[0,.69444,0,0,.68056],85:[0,.69444,0,0,.6875],86:[0,.69444,.01389,0,.66667],87:[0,.69444,.01389,0,.94445],88:[0,.69444,0,0,.66667],89:[0,.69444,.025,0,.66667],90:[0,.69444,0,0,.61111],91:[.25,.75,0,0,.28889],93:[.25,.75,0,0,.28889],94:[0,.69444,0,0,.5],95:[.35,.09444,.02778,0,.5],97:[0,.44444,0,0,.48056],98:[0,.69444,0,0,.51667],99:[0,.44444,0,0,.44445],100:[0,.69444,0,0,.51667],101:[0,.44444,0,0,.44445],102:[0,.69444,.06944,0,.30556],103:[.19444,.44444,.01389,0,.5],104:[0,.69444,0,0,.51667],105:[0,.67937,0,0,.23889],106:[.19444,.67937,0,0,.26667],107:[0,.69444,0,0,.48889],108:[0,.69444,0,0,.23889],109:[0,.44444,0,0,.79445],110:[0,.44444,0,0,.51667],111:[0,.44444,0,0,.5],112:[.19444,.44444,0,0,.51667],113:[.19444,.44444,0,0,.51667],114:[0,.44444,.01389,0,.34167],115:[0,.44444,0,0,.38333],116:[0,.57143,0,0,.36111],117:[0,.44444,0,0,.51667],118:[0,.44444,.01389,0,.46111],119:[0,.44444,.01389,0,.68334],120:[0,.44444,0,0,.46111],121:[.19444,.44444,.01389,0,.46111],122:[0,.44444,0,0,.43472],126:[.35,.32659,0,0,.5],160:[0,0,0,0,.25],168:[0,.67937,0,0,.5],176:[0,.69444,0,0,.66667],184:[.17014,0,0,0,.44445],305:[0,.44444,0,0,.23889],567:[.19444,.44444,0,0,.26667],710:[0,.69444,0,0,.5],711:[0,.63194,0,0,.5],713:[0,.60889,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.67937,0,0,.27778],730:[0,.69444,0,0,.66667],732:[0,.67659,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.69444,0,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,0,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,0,0,.66667],928:[0,.69444,0,0,.70834],931:[0,.69444,0,0,.72222],933:[0,.69444,0,0,.77778],934:[0,.69444,0,0,.72222],936:[0,.69444,0,0,.77778],937:[0,.69444,0,0,.72222],8211:[0,.44444,.02778,0,.5],8212:[0,.44444,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5]},"Script-Regular":{32:[0,0,0,0,.25],65:[0,.7,.22925,0,.80253],66:[0,.7,.04087,0,.90757],67:[0,.7,.1689,0,.66619],68:[0,.7,.09371,0,.77443],69:[0,.7,.18583,0,.56162],70:[0,.7,.13634,0,.89544],71:[0,.7,.17322,0,.60961],72:[0,.7,.29694,0,.96919],73:[0,.7,.19189,0,.80907],74:[.27778,.7,.19189,0,1.05159],75:[0,.7,.31259,0,.91364],76:[0,.7,.19189,0,.87373],77:[0,.7,.15981,0,1.08031],78:[0,.7,.3525,0,.9015],79:[0,.7,.08078,0,.73787],80:[0,.7,.08078,0,1.01262],81:[0,.7,.03305,0,.88282],82:[0,.7,.06259,0,.85],83:[0,.7,.19189,0,.86767],84:[0,.7,.29087,0,.74697],85:[0,.7,.25815,0,.79996],86:[0,.7,.27523,0,.62204],87:[0,.7,.27523,0,.80532],88:[0,.7,.26006,0,.94445],89:[0,.7,.2939,0,.70961],90:[0,.7,.24037,0,.8212],160:[0,0,0,0,.25]},"Size1-Regular":{32:[0,0,0,0,.25],40:[.35001,.85,0,0,.45834],41:[.35001,.85,0,0,.45834],47:[.35001,.85,0,0,.57778],91:[.35001,.85,0,0,.41667],92:[.35001,.85,0,0,.57778],93:[.35001,.85,0,0,.41667],123:[.35001,.85,0,0,.58334],125:[.35001,.85,0,0,.58334],160:[0,0,0,0,.25],710:[0,.72222,0,0,.55556],732:[0,.72222,0,0,.55556],770:[0,.72222,0,0,.55556],771:[0,.72222,0,0,.55556],8214:[-99e-5,.601,0,0,.77778],8593:[1e-5,.6,0,0,.66667],8595:[1e-5,.6,0,0,.66667],8657:[1e-5,.6,0,0,.77778],8659:[1e-5,.6,0,0,.77778],8719:[.25001,.75,0,0,.94445],8720:[.25001,.75,0,0,.94445],8721:[.25001,.75,0,0,1.05556],8730:[.35001,.85,0,0,1],8739:[-.00599,.606,0,0,.33333],8741:[-.00599,.606,0,0,.55556],8747:[.30612,.805,.19445,0,.47222],8748:[.306,.805,.19445,0,.47222],8749:[.306,.805,.19445,0,.47222],8750:[.30612,.805,.19445,0,.47222],8896:[.25001,.75,0,0,.83334],8897:[.25001,.75,0,0,.83334],8898:[.25001,.75,0,0,.83334],8899:[.25001,.75,0,0,.83334],8968:[.35001,.85,0,0,.47222],8969:[.35001,.85,0,0,.47222],8970:[.35001,.85,0,0,.47222],8971:[.35001,.85,0,0,.47222],9168:[-99e-5,.601,0,0,.66667],10216:[.35001,.85,0,0,.47222],10217:[.35001,.85,0,0,.47222],10752:[.25001,.75,0,0,1.11111],10753:[.25001,.75,0,0,1.11111],10754:[.25001,.75,0,0,1.11111],10756:[.25001,.75,0,0,.83334],10758:[.25001,.75,0,0,.83334]},"Size2-Regular":{32:[0,0,0,0,.25],40:[.65002,1.15,0,0,.59722],41:[.65002,1.15,0,0,.59722],47:[.65002,1.15,0,0,.81111],91:[.65002,1.15,0,0,.47222],92:[.65002,1.15,0,0,.81111],93:[.65002,1.15,0,0,.47222],123:[.65002,1.15,0,0,.66667],125:[.65002,1.15,0,0,.66667],160:[0,0,0,0,.25],710:[0,.75,0,0,1],732:[0,.75,0,0,1],770:[0,.75,0,0,1],771:[0,.75,0,0,1],8719:[.55001,1.05,0,0,1.27778],8720:[.55001,1.05,0,0,1.27778],8721:[.55001,1.05,0,0,1.44445],8730:[.65002,1.15,0,0,1],8747:[.86225,1.36,.44445,0,.55556],8748:[.862,1.36,.44445,0,.55556],8749:[.862,1.36,.44445,0,.55556],8750:[.86225,1.36,.44445,0,.55556],8896:[.55001,1.05,0,0,1.11111],8897:[.55001,1.05,0,0,1.11111],8898:[.55001,1.05,0,0,1.11111],8899:[.55001,1.05,0,0,1.11111],8968:[.65002,1.15,0,0,.52778],8969:[.65002,1.15,0,0,.52778],8970:[.65002,1.15,0,0,.52778],8971:[.65002,1.15,0,0,.52778],10216:[.65002,1.15,0,0,.61111],10217:[.65002,1.15,0,0,.61111],10752:[.55001,1.05,0,0,1.51112],10753:[.55001,1.05,0,0,1.51112],10754:[.55001,1.05,0,0,1.51112],10756:[.55001,1.05,0,0,1.11111],10758:[.55001,1.05,0,0,1.11111]},"Size3-Regular":{32:[0,0,0,0,.25],40:[.95003,1.45,0,0,.73611],41:[.95003,1.45,0,0,.73611],47:[.95003,1.45,0,0,1.04445],91:[.95003,1.45,0,0,.52778],92:[.95003,1.45,0,0,1.04445],93:[.95003,1.45,0,0,.52778],123:[.95003,1.45,0,0,.75],125:[.95003,1.45,0,0,.75],160:[0,0,0,0,.25],710:[0,.75,0,0,1.44445],732:[0,.75,0,0,1.44445],770:[0,.75,0,0,1.44445],771:[0,.75,0,0,1.44445],8730:[.95003,1.45,0,0,1],8968:[.95003,1.45,0,0,.58334],8969:[.95003,1.45,0,0,.58334],8970:[.95003,1.45,0,0,.58334],8971:[.95003,1.45,0,0,.58334],10216:[.95003,1.45,0,0,.75],10217:[.95003,1.45,0,0,.75]},"Size4-Regular":{32:[0,0,0,0,.25],40:[1.25003,1.75,0,0,.79167],41:[1.25003,1.75,0,0,.79167],47:[1.25003,1.75,0,0,1.27778],91:[1.25003,1.75,0,0,.58334],92:[1.25003,1.75,0,0,1.27778],93:[1.25003,1.75,0,0,.58334],123:[1.25003,1.75,0,0,.80556],125:[1.25003,1.75,0,0,.80556],160:[0,0,0,0,.25],710:[0,.825,0,0,1.8889],732:[0,.825,0,0,1.8889],770:[0,.825,0,0,1.8889],771:[0,.825,0,0,1.8889],8730:[1.25003,1.75,0,0,1],8968:[1.25003,1.75,0,0,.63889],8969:[1.25003,1.75,0,0,.63889],8970:[1.25003,1.75,0,0,.63889],8971:[1.25003,1.75,0,0,.63889],9115:[.64502,1.155,0,0,.875],9116:[1e-5,.6,0,0,.875],9117:[.64502,1.155,0,0,.875],9118:[.64502,1.155,0,0,.875],9119:[1e-5,.6,0,0,.875],9120:[.64502,1.155,0,0,.875],9121:[.64502,1.155,0,0,.66667],9122:[-99e-5,.601,0,0,.66667],9123:[.64502,1.155,0,0,.66667],9124:[.64502,1.155,0,0,.66667],9125:[-99e-5,.601,0,0,.66667],9126:[.64502,1.155,0,0,.66667],9127:[1e-5,.9,0,0,.88889],9128:[.65002,1.15,0,0,.88889],9129:[.90001,0,0,0,.88889],9130:[0,.3,0,0,.88889],9131:[1e-5,.9,0,0,.88889],9132:[.65002,1.15,0,0,.88889],9133:[.90001,0,0,0,.88889],9143:[.88502,.915,0,0,1.05556],10216:[1.25003,1.75,0,0,.80556],10217:[1.25003,1.75,0,0,.80556],57344:[-.00499,.605,0,0,1.05556],57345:[-.00499,.605,0,0,1.05556],57680:[0,.12,0,0,.45],57681:[0,.12,0,0,.45],57682:[0,.12,0,0,.45],57683:[0,.12,0,0,.45]},"Typewriter-Regular":{32:[0,0,0,0,.525],33:[0,.61111,0,0,.525],34:[0,.61111,0,0,.525],35:[0,.61111,0,0,.525],36:[.08333,.69444,0,0,.525],37:[.08333,.69444,0,0,.525],38:[0,.61111,0,0,.525],39:[0,.61111,0,0,.525],40:[.08333,.69444,0,0,.525],41:[.08333,.69444,0,0,.525],42:[0,.52083,0,0,.525],43:[-.08056,.53055,0,0,.525],44:[.13889,.125,0,0,.525],45:[-.08056,.53055,0,0,.525],46:[0,.125,0,0,.525],47:[.08333,.69444,0,0,.525],48:[0,.61111,0,0,.525],49:[0,.61111,0,0,.525],50:[0,.61111,0,0,.525],51:[0,.61111,0,0,.525],52:[0,.61111,0,0,.525],53:[0,.61111,0,0,.525],54:[0,.61111,0,0,.525],55:[0,.61111,0,0,.525],56:[0,.61111,0,0,.525],57:[0,.61111,0,0,.525],58:[0,.43056,0,0,.525],59:[.13889,.43056,0,0,.525],60:[-.05556,.55556,0,0,.525],61:[-.19549,.41562,0,0,.525],62:[-.05556,.55556,0,0,.525],63:[0,.61111,0,0,.525],64:[0,.61111,0,0,.525],65:[0,.61111,0,0,.525],66:[0,.61111,0,0,.525],67:[0,.61111,0,0,.525],68:[0,.61111,0,0,.525],69:[0,.61111,0,0,.525],70:[0,.61111,0,0,.525],71:[0,.61111,0,0,.525],72:[0,.61111,0,0,.525],73:[0,.61111,0,0,.525],74:[0,.61111,0,0,.525],75:[0,.61111,0,0,.525],76:[0,.61111,0,0,.525],77:[0,.61111,0,0,.525],78:[0,.61111,0,0,.525],79:[0,.61111,0,0,.525],80:[0,.61111,0,0,.525],81:[.13889,.61111,0,0,.525],82:[0,.61111,0,0,.525],83:[0,.61111,0,0,.525],84:[0,.61111,0,0,.525],85:[0,.61111,0,0,.525],86:[0,.61111,0,0,.525],87:[0,.61111,0,0,.525],88:[0,.61111,0,0,.525],89:[0,.61111,0,0,.525],90:[0,.61111,0,0,.525],91:[.08333,.69444,0,0,.525],92:[.08333,.69444,0,0,.525],93:[.08333,.69444,0,0,.525],94:[0,.61111,0,0,.525],95:[.09514,0,0,0,.525],96:[0,.61111,0,0,.525],97:[0,.43056,0,0,.525],98:[0,.61111,0,0,.525],99:[0,.43056,0,0,.525],100:[0,.61111,0,0,.525],101:[0,.43056,0,0,.525],102:[0,.61111,0,0,.525],103:[.22222,.43056,0,0,.525],104:[0,.61111,0,0,.525],105:[0,.61111,0,0,.525],106:[.22222,.61111,0,0,.525],107:[0,.61111,0,0,.525],108:[0,.61111,0,0,.525],109:[0,.43056,0,0,.525],110:[0,.43056,0,0,.525],111:[0,.43056,0,0,.525],112:[.22222,.43056,0,0,.525],113:[.22222,.43056,0,0,.525],114:[0,.43056,0,0,.525],115:[0,.43056,0,0,.525],116:[0,.55358,0,0,.525],117:[0,.43056,0,0,.525],118:[0,.43056,0,0,.525],119:[0,.43056,0,0,.525],120:[0,.43056,0,0,.525],121:[.22222,.43056,0,0,.525],122:[0,.43056,0,0,.525],123:[.08333,.69444,0,0,.525],124:[.08333,.69444,0,0,.525],125:[.08333,.69444,0,0,.525],126:[0,.61111,0,0,.525],127:[0,.61111,0,0,.525],160:[0,0,0,0,.525],176:[0,.61111,0,0,.525],184:[.19445,0,0,0,.525],305:[0,.43056,0,0,.525],567:[.22222,.43056,0,0,.525],711:[0,.56597,0,0,.525],713:[0,.56555,0,0,.525],714:[0,.61111,0,0,.525],715:[0,.61111,0,0,.525],728:[0,.61111,0,0,.525],730:[0,.61111,0,0,.525],770:[0,.61111,0,0,.525],771:[0,.61111,0,0,.525],776:[0,.61111,0,0,.525],915:[0,.61111,0,0,.525],916:[0,.61111,0,0,.525],920:[0,.61111,0,0,.525],923:[0,.61111,0,0,.525],926:[0,.61111,0,0,.525],928:[0,.61111,0,0,.525],931:[0,.61111,0,0,.525],933:[0,.61111,0,0,.525],934:[0,.61111,0,0,.525],936:[0,.61111,0,0,.525],937:[0,.61111,0,0,.525],8216:[0,.61111,0,0,.525],8217:[0,.61111,0,0,.525],8242:[0,.61111,0,0,.525],9251:[.11111,.21944,0,0,.525]}};const M0={slant:[.25,.25,.25],space:[0,0,0],stretch:[0,0,0],shrink:[0,0,0],xHeight:[.431,.431,.431],quad:[1,1.171,1.472],extraSpace:[0,0,0],num1:[.677,.732,.925],num2:[.394,.384,.387],num3:[.444,.471,.504],denom1:[.686,.752,1.025],denom2:[.345,.344,.532],sup1:[.413,.503,.504],sup2:[.363,.431,.404],sup3:[.289,.286,.294],sub1:[.15,.143,.2],sub2:[.247,.286,.4],supDrop:[.386,.353,.494],subDrop:[.05,.071,.1],delim1:[2.39,1.7,1.98],delim2:[1.01,1.157,1.42],axisHeight:[.25,.25,.25],defaultRuleThickness:[.04,.049,.049],bigOpSpacing1:[.111,.111,.111],bigOpSpacing2:[.166,.166,.166],bigOpSpacing3:[.2,.2,.2],bigOpSpacing4:[.6,.611,.611],bigOpSpacing5:[.1,.143,.143],sqrtRuleThickness:[.04,.04,.04],ptPerEm:[10,10,10],doubleRuleSep:[.2,.2,.2],arrayRuleWidth:[.04,.04,.04],fboxsep:[.3,.3,.3],fboxrule:[.04,.04,.04]},z0={Å:"A",Ð:"D",Þ:"o",å:"a",ð:"d",þ:"o",А:"A",Б:"B",В:"B",Г:"F",Д:"A",Е:"E",Ж:"K",З:"3",И:"N",Й:"N",К:"K",Л:"N",М:"M",Н:"H",О:"O",П:"N",Р:"P",С:"C",Т:"T",У:"y",Ф:"O",Х:"X",Ц:"U",Ч:"h",Ш:"W",Щ:"W",Ъ:"B",Ы:"X",Ь:"B",Э:"3",Ю:"X",Я:"R",а:"a",б:"b",в:"a",г:"r",д:"y",е:"e",ж:"m",з:"e",и:"n",й:"n",к:"n",л:"n",м:"m",н:"n",о:"o",п:"n",р:"p",с:"c",т:"o",у:"y",ф:"b",х:"x",ц:"n",ч:"n",ш:"w",щ:"w",ъ:"a",ы:"m",ь:"a",э:"e",ю:"m",я:"r"};function B0(t,e){Je[t]=e}function d0(t,e,n){if(!Je[e])throw new Error("Font metrics not found for font: "+e+".");let r=t.charCodeAt(0),s=Je[e][r];if(!s&&t[0]in z0&&(r=z0[t[0]].charCodeAt(0),s=Je[e][r]),!s&&n==="text"&&V0(r)&&(s=Je[e][77]),s)return{depth:s[0],height:s[1],italic:s[2],skew:s[3],width:s[4]}}const Y0={};function Tt(t){let e;if(t>=5?e=0:t>=3?e=1:e=2,!Y0[e]){const n=Y0[e]={cssEmPerMu:M0.quad[e]/18};for(const r in M0)M0.hasOwnProperty(r)&&(n[r]=M0[r][e])}return Y0[e]}const Ct=[[1,1,1],[2,1,1],[3,1,1],[4,2,1],[5,2,1],[6,3,1],[7,4,2],[8,6,3],[9,7,6],[10,8,7],[11,10,9]],X0=[.5,.6,.7,.8,.9,1,1.2,1.44,1.728,2.074,2.488],it=function(t,e){return e.size<2?t:Ct[t-1][e.size-1]};class Ge{constructor(e){this.style=void 0,this.color=void 0,this.size=void 0,this.textSize=void 0,this.phantom=void 0,this.font=void 0,this.fontFamily=void 0,this.fontWeight=void 0,this.fontShape=void 0,this.sizeMultiplier=void 0,this.maxSize=void 0,this.minRuleThickness=void 0,this._fontMetrics=void 0,this.style=e.style,this.color=e.color,this.size=e.size||Ge.BASESIZE,this.textSize=e.textSize||this.size,this.phantom=!!e.phantom,this.font=e.font||"",this.fontFamily=e.fontFamily||"",this.fontWeight=e.fontWeight||"",this.fontShape=e.fontShape||"",this.sizeMultiplier=X0[this.size-1],this.maxSize=e.maxSize,this.minRuleThickness=e.minRuleThickness,this._fontMetrics=void 0}extend(e){const n={style:this.style,size:this.size,textSize:this.textSize,color:this.color,phantom:this.phantom,font:this.font,fontFamily:this.fontFamily,fontWeight:this.fontWeight,fontShape:this.fontShape,maxSize:this.maxSize,minRuleThickness:this.minRuleThickness};for(const r in e)e.hasOwnProperty(r)&&(n[r]=e[r]);return new Ge(n)}havingStyle(e){return this.style===e?this:this.extend({style:e,size:it(this.textSize,e)})}havingCrampedStyle(){return this.havingStyle(this.style.cramp())}havingSize(e){return this.size===e&&this.textSize===e?this:this.extend({style:this.style.text(),size:e,textSize:e,sizeMultiplier:X0[e-1]})}havingBaseStyle(e){e=e||this.style.text();const n=it(Ge.BASESIZE,e);return this.size===n&&this.textSize===Ge.BASESIZE&&this.style===e?this:this.extend({style:e,size:n})}havingBaseSizing(){let e;switch(this.style.id){case 4:case 5:e=3;break;case 6:case 7:e=1;break;default:e=6}return this.extend({style:this.style.text(),size:e})}withColor(e){return this.extend({color:e})}withPhantom(){return this.extend({phantom:!0})}withFont(e){return this.extend({font:e})}withTextFontFamily(e){return this.extend({fontFamily:e,font:""})}withTextFontWeight(e){return this.extend({fontWeight:e,font:""})}withTextFontShape(e){return this.extend({fontShape:e,font:""})}sizingClasses(e){return e.size!==this.size?["sizing","reset-size"+e.size,"size"+this.size]:[]}baseSizingClasses(){return this.size!==Ge.BASESIZE?["sizing","reset-size"+this.size,"size"+Ge.BASESIZE]:[]}fontMetrics(){return this._fontMetrics||(this._fontMetrics=Tt(this.size)),this._fontMetrics}getColor(){return this.phantom?"transparent":this.color}}Ge.BASESIZE=6;var j0=Ge;const Ye={pt:1,mm:7227/2540,cm:7227/254,in:72.27,bp:803/800,pc:12,dd:1238/1157,cc:14856/1157,nd:685/642,nc:1370/107,sp:1/65536,px:803/800},_0={ex:!0,em:!0,mu:!0},Z0=function(t){return typeof t!="string"&&(t=t.unit),t in Ye||t in _0||t==="ex"},de=function(t,e){let n;if(t.unit in Ye)n=Ye[t.unit]/e.fontMetrics().ptPerEm/e.sizeMultiplier;else if(t.unit==="mu")n=e.fontMetrics().cssEmPerMu;else{let r;if(e.style.isTight()?r=e.havingStyle(e.style.text()):r=e,t.unit==="ex")n=r.fontMetrics().xHeight;else if(t.unit==="em")n=r.fontMetrics().quad;else throw new f("Invalid unit: '"+t.unit+"'");r!==e&&(n*=r.sizeMultiplier/e.sizeMultiplier)}return Math.min(t.number*n,e.maxSize)},I=function(t){return+t.toFixed(4)+"em"},He=function(t){return t.filter(e=>e).join(" ")},Mt=function(t,e,n){if(this.classes=t||[],this.attributes={},this.height=0,this.depth=0,this.maxFontSize=0,this.style=n||{},e){e.style.isTight()&&this.classes.push("mtight");const r=e.getColor();r&&(this.style.color=r)}},zt=function(t){const e=document.createElement(t);e.className=He(this.classes);for(const n in this.style)this.style.hasOwnProperty(n)&&(e.style[n]=this.style[n]);for(const n in this.attributes)this.attributes.hasOwnProperty(n)&&e.setAttribute(n,this.attributes[n]);for(let n=0;n<this.children.length;n++)e.appendChild(this.children[n].toNode());return e},Ce=function(t){let e="<"+t;this.classes.length&&(e+=' class="'+N.escape(He(this.classes))+'"');let n="";for(const r in this.style)this.style.hasOwnProperty(r)&&(n+=N.hyphenate(r)+":"+this.style[r]+";");n&&(e+=' style="'+N.escape(n)+'"');for(const r in this.attributes)this.attributes.hasOwnProperty(r)&&(e+=" "+r+'="'+N.escape(this.attributes[r])+'"');e+=">";for(let r=0;r<this.children.length;r++)e+=this.children[r].toMarkup();return e+="</"+t+">",e};class e0{constructor(e,n,r,s){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.width=void 0,this.maxFontSize=void 0,this.style=void 0,Mt.call(this,e,r,s),this.children=n||[]}setAttribute(e,n){this.attributes[e]=n}hasClass(e){return N.contains(this.classes,e)}toNode(){return zt.call(this,"span")}toMarkup(){return Ce.call(this,"span")}}class at{constructor(e,n,r,s){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,Mt.call(this,n,s),this.children=r||[],this.setAttribute("href",e)}setAttribute(e,n){this.attributes[e]=n}hasClass(e){return N.contains(this.classes,e)}toNode(){return zt.call(this,"a")}toMarkup(){return Ce.call(this,"a")}}class Bt{constructor(e,n,r){this.src=void 0,this.alt=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.alt=n,this.src=e,this.classes=["mord"],this.style=r}hasClass(e){return N.contains(this.classes,e)}toNode(){const e=document.createElement("img");e.src=this.src,e.alt=this.alt,e.className="mord";for(const n in this.style)this.style.hasOwnProperty(n)&&(e.style[n]=this.style[n]);return e}toMarkup(){let e='<img src="'+N.escape(this.src)+'"'+(' alt="'+N.escape(this.alt)+'"'),n="";for(const r in this.style)this.style.hasOwnProperty(r)&&(n+=N.hyphenate(r)+":"+this.style[r]+";");return n&&(e+=' style="'+N.escape(n)+'"'),e+="'/>",e}}const ot={î:"ı̂",ï:"ı̈",í:"ı́",ì:"ı̀"};class qe{constructor(e,n,r,s,o,l,d,g){this.text=void 0,this.height=void 0,this.depth=void 0,this.italic=void 0,this.skew=void 0,this.width=void 0,this.maxFontSize=void 0,this.classes=void 0,this.style=void 0,this.text=e,this.height=n||0,this.depth=r||0,this.italic=s||0,this.skew=o||0,this.width=l||0,this.classes=d||[],this.style=g||{},this.maxFontSize=0;const k=ke(this.text.charCodeAt(0));k&&this.classes.push(k+"_fallback"),/[îïíì]/.test(this.text)&&(this.text=ot[this.text])}hasClass(e){return N.contains(this.classes,e)}toNode(){const e=document.createTextNode(this.text);let n=null;this.italic>0&&(n=document.createElement("span"),n.style.marginRight=I(this.italic)),this.classes.length>0&&(n=n||document.createElement("span"),n.className=He(this.classes));for(const r in this.style)this.style.hasOwnProperty(r)&&(n=n||document.createElement("span"),n.style[r]=this.style[r]);return n?(n.appendChild(e),n):e}toMarkup(){let e=!1,n="<span";this.classes.length&&(e=!0,n+=' class="',n+=N.escape(He(this.classes)),n+='"');let r="";this.italic>0&&(r+="margin-right:"+this.italic+"em;");for(const o in this.style)this.style.hasOwnProperty(o)&&(r+=N.hyphenate(o)+":"+this.style[o]+";");r&&(e=!0,n+=' style="'+N.escape(r)+'"');const s=N.escape(this.text);return e?(n+=">",n+=s,n+="</span>",n):s}}class t0{constructor(e,n){this.children=void 0,this.attributes=void 0,this.children=e||[],this.attributes=n||{}}toNode(){const e="http://www.w3.org/2000/svg",n=document.createElementNS(e,"svg");for(const r in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,r)&&n.setAttribute(r,this.attributes[r]);for(let r=0;r<this.children.length;r++)n.appendChild(this.children[r].toNode());return n}toMarkup(){let e='<svg xmlns="http://www.w3.org/2000/svg"';for(const n in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,n)&&(e+=" "+n+'="'+N.escape(this.attributes[n])+'"');e+=">";for(let n=0;n<this.children.length;n++)e+=this.children[n].toMarkup();return e+="</svg>",e}}class p0{constructor(e,n){this.pathName=void 0,this.alternate=void 0,this.pathName=e,this.alternate=n}toNode(){const e="http://www.w3.org/2000/svg",n=document.createElementNS(e,"path");return this.alternate?n.setAttribute("d",this.alternate):n.setAttribute("d",L0[this.pathName]),n}toMarkup(){return this.alternate?'<path d="'+N.escape(this.alternate)+'"/>':'<path d="'+N.escape(L0[this.pathName])+'"/>'}}class N0{constructor(e){this.attributes=void 0,this.attributes=e||{}}toNode(){const e="http://www.w3.org/2000/svg",n=document.createElementNS(e,"line");for(const r in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,r)&&n.setAttribute(r,this.attributes[r]);return n}toMarkup(){let e="<line";for(const n in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,n)&&(e+=" "+n+'="'+N.escape(this.attributes[n])+'"');return e+="/>",e}}function K0(t){if(t instanceof qe)return t;throw new Error("Expected symbolNode but got "+String(t)+".")}function ln(t){if(t instanceof e0)return t;throw new Error("Expected span<HtmlDomNode> but got "+String(t)+".")}const k0={bin:1,close:1,inner:1,open:1,punct:1,rel:1},lt={"accent-token":1,mathord:1,"op-token":1,spacing:1,textord:1},H0={math:{},text:{}};var be=H0;function i(t,e,n,r,s,o){H0[t][s]={font:e,group:n,replace:r},o&&r&&(H0[t][r]=H0[t][s])}const a="math",M="text",m="main",y="ams",pe="accent-token",H="bin",Oe="close",G="inner",w="mathord",z="op-token",O="open",ie="punct",b="rel",ve="spacing",A="textord";i(a,m,b,"≡","\\equiv",!0),i(a,m,b,"≺","\\prec",!0),i(a,m,b,"≻","\\succ",!0),i(a,m,b,"∼","\\sim",!0),i(a,m,b,"⊥","\\perp"),i(a,m,b,"⪯","\\preceq",!0),i(a,m,b,"⪰","\\succeq",!0),i(a,m,b,"≃","\\simeq",!0),i(a,m,b,"∣","\\mid",!0),i(a,m,b,"≪","\\ll",!0),i(a,m,b,"≫","\\gg",!0),i(a,m,b,"≍","\\asymp",!0),i(a,m,b,"∥","\\parallel"),i(a,m,b,"⋈","\\bowtie",!0),i(a,m,b,"⌣","\\smile",!0),i(a,m,b,"⊑","\\sqsubseteq",!0),i(a,m,b,"⊒","\\sqsupseteq",!0),i(a,m,b,"≐","\\doteq",!0),i(a,m,b,"⌢","\\frown",!0),i(a,m,b,"∋","\\ni",!0),i(a,m,b,"∝","\\propto",!0),i(a,m,b,"⊢","\\vdash",!0),i(a,m,b,"⊣","\\dashv",!0),i(a,m,b,"∋","\\owns"),i(a,m,ie,".","\\ldotp"),i(a,m,ie,"⋅","\\cdotp"),i(a,m,A,"#","\\#"),i(M,m,A,"#","\\#"),i(a,m,A,"&","\\&"),i(M,m,A,"&","\\&"),i(a,m,A,"ℵ","\\aleph",!0),i(a,m,A,"∀","\\forall",!0),i(a,m,A,"ℏ","\\hbar",!0),i(a,m,A,"∃","\\exists",!0),i(a,m,A,"∇","\\nabla",!0),i(a,m,A,"♭","\\flat",!0),i(a,m,A,"ℓ","\\ell",!0),i(a,m,A,"♮","\\natural",!0),i(a,m,A,"♣","\\clubsuit",!0),i(a,m,A,"℘","\\wp",!0),i(a,m,A,"♯","\\sharp",!0),i(a,m,A,"♢","\\diamondsuit",!0),i(a,m,A,"ℜ","\\Re",!0),i(a,m,A,"♡","\\heartsuit",!0),i(a,m,A,"ℑ","\\Im",!0),i(a,m,A,"♠","\\spadesuit",!0),i(a,m,A,"§","\\S",!0),i(M,m,A,"§","\\S"),i(a,m,A,"¶","\\P",!0),i(M,m,A,"¶","\\P"),i(a,m,A,"†","\\dag"),i(M,m,A,"†","\\dag"),i(M,m,A,"†","\\textdagger"),i(a,m,A,"‡","\\ddag"),i(M,m,A,"‡","\\ddag"),i(M,m,A,"‡","\\textdaggerdbl"),i(a,m,Oe,"⎱","\\rmoustache",!0),i(a,m,O,"⎰","\\lmoustache",!0),i(a,m,Oe,"⟯","\\rgroup",!0),i(a,m,O,"⟮","\\lgroup",!0),i(a,m,H,"∓","\\mp",!0),i(a,m,H,"⊖","\\ominus",!0),i(a,m,H,"⊎","\\uplus",!0),i(a,m,H,"⊓","\\sqcap",!0),i(a,m,H,"∗","\\ast"),i(a,m,H,"⊔","\\sqcup",!0),i(a,m,H,"◯","\\bigcirc",!0),i(a,m,H,"∙","\\bullet",!0),i(a,m,H,"‡","\\ddagger"),i(a,m,H,"≀","\\wr",!0),i(a,m,H,"⨿","\\amalg"),i(a,m,H,"&","\\And"),i(a,m,b,"⟵","\\longleftarrow",!0),i(a,m,b,"⇐","\\Leftarrow",!0),i(a,m,b,"⟸","\\Longleftarrow",!0),i(a,m,b,"⟶","\\longrightarrow",!0),i(a,m,b,"⇒","\\Rightarrow",!0),i(a,m,b,"⟹","\\Longrightarrow",!0),i(a,m,b,"↔","\\leftrightarrow",!0),i(a,m,b,"⟷","\\longleftrightarrow",!0),i(a,m,b,"⇔","\\Leftrightarrow",!0),i(a,m,b,"⟺","\\Longleftrightarrow",!0),i(a,m,b,"↦","\\mapsto",!0),i(a,m,b,"⟼","\\longmapsto",!0),i(a,m,b,"↗","\\nearrow",!0),i(a,m,b,"↩","\\hookleftarrow",!0),i(a,m,b,"↪","\\hookrightarrow",!0),i(a,m,b,"↘","\\searrow",!0),i(a,m,b,"↼","\\leftharpoonup",!0),i(a,m,b,"⇀","\\rightharpoonup",!0),i(a,m,b,"↙","\\swarrow",!0),i(a,m,b,"↽","\\leftharpoondown",!0),i(a,m,b,"⇁","\\rightharpoondown",!0),i(a,m,b,"↖","\\nwarrow",!0),i(a,m,b,"⇌","\\rightleftharpoons",!0),i(a,y,b,"≮","\\nless",!0),i(a,y,b,"","\\@nleqslant"),i(a,y,b,"","\\@nleqq"),i(a,y,b,"⪇","\\lneq",!0),i(a,y,b,"≨","\\lneqq",!0),i(a,y,b,"","\\@lvertneqq"),i(a,y,b,"⋦","\\lnsim",!0),i(a,y,b,"⪉","\\lnapprox",!0),i(a,y,b,"⊀","\\nprec",!0),i(a,y,b,"⋠","\\npreceq",!0),i(a,y,b,"⋨","\\precnsim",!0),i(a,y,b,"⪹","\\precnapprox",!0),i(a,y,b,"≁","\\nsim",!0),i(a,y,b,"","\\@nshortmid"),i(a,y,b,"∤","\\nmid",!0),i(a,y,b,"⊬","\\nvdash",!0),i(a,y,b,"⊭","\\nvDash",!0),i(a,y,b,"⋪","\\ntriangleleft"),i(a,y,b,"⋬","\\ntrianglelefteq",!0),i(a,y,b,"⊊","\\subsetneq",!0),i(a,y,b,"","\\@varsubsetneq"),i(a,y,b,"⫋","\\subsetneqq",!0),i(a,y,b,"","\\@varsubsetneqq"),i(a,y,b,"≯","\\ngtr",!0),i(a,y,b,"","\\@ngeqslant"),i(a,y,b,"","\\@ngeqq"),i(a,y,b,"⪈","\\gneq",!0),i(a,y,b,"≩","\\gneqq",!0),i(a,y,b,"","\\@gvertneqq"),i(a,y,b,"⋧","\\gnsim",!0),i(a,y,b,"⪊","\\gnapprox",!0),i(a,y,b,"⊁","\\nsucc",!0),i(a,y,b,"⋡","\\nsucceq",!0),i(a,y,b,"⋩","\\succnsim",!0),i(a,y,b,"⪺","\\succnapprox",!0),i(a,y,b,"≆","\\ncong",!0),i(a,y,b,"","\\@nshortparallel"),i(a,y,b,"∦","\\nparallel",!0),i(a,y,b,"⊯","\\nVDash",!0),i(a,y,b,"⋫","\\ntriangleright"),i(a,y,b,"⋭","\\ntrianglerighteq",!0),i(a,y,b,"","\\@nsupseteqq"),i(a,y,b,"⊋","\\supsetneq",!0),i(a,y,b,"","\\@varsupsetneq"),i(a,y,b,"⫌","\\supsetneqq",!0),i(a,y,b,"","\\@varsupsetneqq"),i(a,y,b,"⊮","\\nVdash",!0),i(a,y,b,"⪵","\\precneqq",!0),i(a,y,b,"⪶","\\succneqq",!0),i(a,y,b,"","\\@nsubseteqq"),i(a,y,H,"⊴","\\unlhd"),i(a,y,H,"⊵","\\unrhd"),i(a,y,b,"↚","\\nleftarrow",!0),i(a,y,b,"↛","\\nrightarrow",!0),i(a,y,b,"⇍","\\nLeftarrow",!0),i(a,y,b,"⇏","\\nRightarrow",!0),i(a,y,b,"↮","\\nleftrightarrow",!0),i(a,y,b,"⇎","\\nLeftrightarrow",!0),i(a,y,b,"△","\\vartriangle"),i(a,y,A,"ℏ","\\hslash"),i(a,y,A,"▽","\\triangledown"),i(a,y,A,"◊","\\lozenge"),i(a,y,A,"Ⓢ","\\circledS"),i(a,y,A,"®","\\circledR"),i(M,y,A,"®","\\circledR"),i(a,y,A,"∡","\\measuredangle",!0),i(a,y,A,"∄","\\nexists"),i(a,y,A,"℧","\\mho"),i(a,y,A,"Ⅎ","\\Finv",!0),i(a,y,A,"⅁","\\Game",!0),i(a,y,A,"‵","\\backprime"),i(a,y,A,"▲","\\blacktriangle"),i(a,y,A,"▼","\\blacktriangledown"),i(a,y,A,"■","\\blacksquare"),i(a,y,A,"⧫","\\blacklozenge"),i(a,y,A,"★","\\bigstar"),i(a,y,A,"∢","\\sphericalangle",!0),i(a,y,A,"∁","\\complement",!0),i(a,y,A,"ð","\\eth",!0),i(M,m,A,"ð","ð"),i(a,y,A,"╱","\\diagup"),i(a,y,A,"╲","\\diagdown"),i(a,y,A,"□","\\square"),i(a,y,A,"□","\\Box"),i(a,y,A,"◊","\\Diamond"),i(a,y,A,"¥","\\yen",!0),i(M,y,A,"¥","\\yen",!0),i(a,y,A,"✓","\\checkmark",!0),i(M,y,A,"✓","\\checkmark"),i(a,y,A,"ℶ","\\beth",!0),i(a,y,A,"ℸ","\\daleth",!0),i(a,y,A,"ℷ","\\gimel",!0),i(a,y,A,"ϝ","\\digamma",!0),i(a,y,A,"ϰ","\\varkappa"),i(a,y,O,"┌","\\@ulcorner",!0),i(a,y,Oe,"┐","\\@urcorner",!0),i(a,y,O,"└","\\@llcorner",!0),i(a,y,Oe,"┘","\\@lrcorner",!0),i(a,y,b,"≦","\\leqq",!0),i(a,y,b,"⩽","\\leqslant",!0),i(a,y,b,"⪕","\\eqslantless",!0),i(a,y,b,"≲","\\lesssim",!0),i(a,y,b,"⪅","\\lessapprox",!0),i(a,y,b,"≊","\\approxeq",!0),i(a,y,H,"⋖","\\lessdot"),i(a,y,b,"⋘","\\lll",!0),i(a,y,b,"≶","\\lessgtr",!0),i(a,y,b,"⋚","\\lesseqgtr",!0),i(a,y,b,"⪋","\\lesseqqgtr",!0),i(a,y,b,"≑","\\doteqdot"),i(a,y,b,"≓","\\risingdotseq",!0),i(a,y,b,"≒","\\fallingdotseq",!0),i(a,y,b,"∽","\\backsim",!0),i(a,y,b,"⋍","\\backsimeq",!0),i(a,y,b,"⫅","\\subseteqq",!0),i(a,y,b,"⋐","\\Subset",!0),i(a,y,b,"⊏","\\sqsubset",!0),i(a,y,b,"≼","\\preccurlyeq",!0),i(a,y,b,"⋞","\\curlyeqprec",!0),i(a,y,b,"≾","\\precsim",!0),i(a,y,b,"⪷","\\precapprox",!0),i(a,y,b,"⊲","\\vartriangleleft"),i(a,y,b,"⊴","\\trianglelefteq"),i(a,y,b,"⊨","\\vDash",!0),i(a,y,b,"⊪","\\Vvdash",!0),i(a,y,b,"⌣","\\smallsmile"),i(a,y,b,"⌢","\\smallfrown"),i(a,y,b,"≏","\\bumpeq",!0),i(a,y,b,"≎","\\Bumpeq",!0),i(a,y,b,"≧","\\geqq",!0),i(a,y,b,"⩾","\\geqslant",!0),i(a,y,b,"⪖","\\eqslantgtr",!0),i(a,y,b,"≳","\\gtrsim",!0),i(a,y,b,"⪆","\\gtrapprox",!0),i(a,y,H,"⋗","\\gtrdot"),i(a,y,b,"⋙","\\ggg",!0),i(a,y,b,"≷","\\gtrless",!0),i(a,y,b,"⋛","\\gtreqless",!0),i(a,y,b,"⪌","\\gtreqqless",!0),i(a,y,b,"≖","\\eqcirc",!0),i(a,y,b,"≗","\\circeq",!0),i(a,y,b,"≜","\\triangleq",!0),i(a,y,b,"∼","\\thicksim"),i(a,y,b,"≈","\\thickapprox"),i(a,y,b,"⫆","\\supseteqq",!0),i(a,y,b,"⋑","\\Supset",!0),i(a,y,b,"⊐","\\sqsupset",!0),i(a,y,b,"≽","\\succcurlyeq",!0),i(a,y,b,"⋟","\\curlyeqsucc",!0),i(a,y,b,"≿","\\succsim",!0),i(a,y,b,"⪸","\\succapprox",!0),i(a,y,b,"⊳","\\vartriangleright"),i(a,y,b,"⊵","\\trianglerighteq"),i(a,y,b,"⊩","\\Vdash",!0),i(a,y,b,"∣","\\shortmid"),i(a,y,b,"∥","\\shortparallel"),i(a,y,b,"≬","\\between",!0),i(a,y,b,"⋔","\\pitchfork",!0),i(a,y,b,"∝","\\varpropto"),i(a,y,b,"◀","\\blacktriangleleft"),i(a,y,b,"∴","\\therefore",!0),i(a,y,b,"∍","\\backepsilon"),i(a,y,b,"▶","\\blacktriangleright"),i(a,y,b,"∵","\\because",!0),i(a,y,b,"⋘","\\llless"),i(a,y,b,"⋙","\\gggtr"),i(a,y,H,"⊲","\\lhd"),i(a,y,H,"⊳","\\rhd"),i(a,y,b,"≂","\\eqsim",!0),i(a,m,b,"⋈","\\Join"),i(a,y,b,"≑","\\Doteq",!0),i(a,y,H,"∔","\\dotplus",!0),i(a,y,H,"∖","\\smallsetminus"),i(a,y,H,"⋒","\\Cap",!0),i(a,y,H,"⋓","\\Cup",!0),i(a,y,H,"⩞","\\doublebarwedge",!0),i(a,y,H,"⊟","\\boxminus",!0),i(a,y,H,"⊞","\\boxplus",!0),i(a,y,H,"⋇","\\divideontimes",!0),i(a,y,H,"⋉","\\ltimes",!0),i(a,y,H,"⋊","\\rtimes",!0),i(a,y,H,"⋋","\\leftthreetimes",!0),i(a,y,H,"⋌","\\rightthreetimes",!0),i(a,y,H,"⋏","\\curlywedge",!0),i(a,y,H,"⋎","\\curlyvee",!0),i(a,y,H,"⊝","\\circleddash",!0),i(a,y,H,"⊛","\\circledast",!0),i(a,y,H,"⋅","\\centerdot"),i(a,y,H,"⊺","\\intercal",!0),i(a,y,H,"⋒","\\doublecap"),i(a,y,H,"⋓","\\doublecup"),i(a,y,H,"⊠","\\boxtimes",!0),i(a,y,b,"⇢","\\dashrightarrow",!0),i(a,y,b,"⇠","\\dashleftarrow",!0),i(a,y,b,"⇇","\\leftleftarrows",!0),i(a,y,b,"⇆","\\leftrightarrows",!0),i(a,y,b,"⇚","\\Lleftarrow",!0),i(a,y,b,"↞","\\twoheadleftarrow",!0),i(a,y,b,"↢","\\leftarrowtail",!0),i(a,y,b,"↫","\\looparrowleft",!0),i(a,y,b,"⇋","\\leftrightharpoons",!0),i(a,y,b,"↶","\\curvearrowleft",!0),i(a,y,b,"↺","\\circlearrowleft",!0),i(a,y,b,"↰","\\Lsh",!0),i(a,y,b,"⇈","\\upuparrows",!0),i(a,y,b,"↿","\\upharpoonleft",!0),i(a,y,b,"⇃","\\downharpoonleft",!0),i(a,m,b,"⊶","\\origof",!0),i(a,m,b,"⊷","\\imageof",!0),i(a,y,b,"⊸","\\multimap",!0),i(a,y,b,"↭","\\leftrightsquigarrow",!0),i(a,y,b,"⇉","\\rightrightarrows",!0),i(a,y,b,"⇄","\\rightleftarrows",!0),i(a,y,b,"↠","\\twoheadrightarrow",!0),i(a,y,b,"↣","\\rightarrowtail",!0),i(a,y,b,"↬","\\looparrowright",!0),i(a,y,b,"↷","\\curvearrowright",!0),i(a,y,b,"↻","\\circlearrowright",!0),i(a,y,b,"↱","\\Rsh",!0),i(a,y,b,"⇊","\\downdownarrows",!0),i(a,y,b,"↾","\\upharpoonright",!0),i(a,y,b,"⇂","\\downharpoonright",!0),i(a,y,b,"⇝","\\rightsquigarrow",!0),i(a,y,b,"⇝","\\leadsto"),i(a,y,b,"⇛","\\Rrightarrow",!0),i(a,y,b,"↾","\\restriction"),i(a,m,A,"‘","`"),i(a,m,A,"$","\\$"),i(M,m,A,"$","\\$"),i(M,m,A,"$","\\textdollar"),i(a,m,A,"%","\\%"),i(M,m,A,"%","\\%"),i(a,m,A,"_","\\_"),i(M,m,A,"_","\\_"),i(M,m,A,"_","\\textunderscore"),i(a,m,A,"∠","\\angle",!0),i(a,m,A,"∞","\\infty",!0),i(a,m,A,"′","\\prime"),i(a,m,A,"△","\\triangle"),i(a,m,A,"Γ","\\Gamma",!0),i(a,m,A,"Δ","\\Delta",!0),i(a,m,A,"Θ","\\Theta",!0),i(a,m,A,"Λ","\\Lambda",!0),i(a,m,A,"Ξ","\\Xi",!0),i(a,m,A,"Π","\\Pi",!0),i(a,m,A,"Σ","\\Sigma",!0),i(a,m,A,"Υ","\\Upsilon",!0),i(a,m,A,"Φ","\\Phi",!0),i(a,m,A,"Ψ","\\Psi",!0),i(a,m,A,"Ω","\\Omega",!0),i(a,m,A,"A","Α"),i(a,m,A,"B","Β"),i(a,m,A,"E","Ε"),i(a,m,A,"Z","Ζ"),i(a,m,A,"H","Η"),i(a,m,A,"I","Ι"),i(a,m,A,"K","Κ"),i(a,m,A,"M","Μ"),i(a,m,A,"N","Ν"),i(a,m,A,"O","Ο"),i(a,m,A,"P","Ρ"),i(a,m,A,"T","Τ"),i(a,m,A,"X","Χ"),i(a,m,A,"¬","\\neg",!0),i(a,m,A,"¬","\\lnot"),i(a,m,A,"⊤","\\top"),i(a,m,A,"⊥","\\bot"),i(a,m,A,"∅","\\emptyset"),i(a,y,A,"∅","\\varnothing"),i(a,m,w,"α","\\alpha",!0),i(a,m,w,"β","\\beta",!0),i(a,m,w,"γ","\\gamma",!0),i(a,m,w,"δ","\\delta",!0),i(a,m,w,"ϵ","\\epsilon",!0),i(a,m,w,"ζ","\\zeta",!0),i(a,m,w,"η","\\eta",!0),i(a,m,w,"θ","\\theta",!0),i(a,m,w,"ι","\\iota",!0),i(a,m,w,"κ","\\kappa",!0),i(a,m,w,"λ","\\lambda",!0),i(a,m,w,"μ","\\mu",!0),i(a,m,w,"ν","\\nu",!0),i(a,m,w,"ξ","\\xi",!0),i(a,m,w,"ο","\\omicron",!0),i(a,m,w,"π","\\pi",!0),i(a,m,w,"ρ","\\rho",!0),i(a,m,w,"σ","\\sigma",!0),i(a,m,w,"τ","\\tau",!0),i(a,m,w,"υ","\\upsilon",!0),i(a,m,w,"ϕ","\\phi",!0),i(a,m,w,"χ","\\chi",!0),i(a,m,w,"ψ","\\psi",!0),i(a,m,w,"ω","\\omega",!0),i(a,m,w,"ε","\\varepsilon",!0),i(a,m,w,"ϑ","\\vartheta",!0),i(a,m,w,"ϖ","\\varpi",!0),i(a,m,w,"ϱ","\\varrho",!0),i(a,m,w,"ς","\\varsigma",!0),i(a,m,w,"φ","\\varphi",!0),i(a,m,H,"∗","*",!0),i(a,m,H,"+","+"),i(a,m,H,"−","-",!0),i(a,m,H,"⋅","\\cdot",!0),i(a,m,H,"∘","\\circ",!0),i(a,m,H,"÷","\\div",!0),i(a,m,H,"±","\\pm",!0),i(a,m,H,"×","\\times",!0),i(a,m,H,"∩","\\cap",!0),i(a,m,H,"∪","\\cup",!0),i(a,m,H,"∖","\\setminus",!0),i(a,m,H,"∧","\\land"),i(a,m,H,"∨","\\lor"),i(a,m,H,"∧","\\wedge",!0),i(a,m,H,"∨","\\vee",!0),i(a,m,A,"√","\\surd"),i(a,m,O,"⟨","\\langle",!0),i(a,m,O,"∣","\\lvert"),i(a,m,O,"∥","\\lVert"),i(a,m,Oe,"?","?"),i(a,m,Oe,"!","!"),i(a,m,Oe,"⟩","\\rangle",!0),i(a,m,Oe,"∣","\\rvert"),i(a,m,Oe,"∥","\\rVert"),i(a,m,b,"=","="),i(a,m,b,":",":"),i(a,m,b,"≈","\\approx",!0),i(a,m,b,"≅","\\cong",!0),i(a,m,b,"≥","\\ge"),i(a,m,b,"≥","\\geq",!0),i(a,m,b,"←","\\gets"),i(a,m,b,">","\\gt",!0),i(a,m,b,"∈","\\in",!0),i(a,m,b,"","\\@not"),i(a,m,b,"⊂","\\subset",!0),i(a,m,b,"⊃","\\supset",!0),i(a,m,b,"⊆","\\subseteq",!0),i(a,m,b,"⊇","\\supseteq",!0),i(a,y,b,"⊈","\\nsubseteq",!0),i(a,y,b,"⊉","\\nsupseteq",!0),i(a,m,b,"⊨","\\models"),i(a,m,b,"←","\\leftarrow",!0),i(a,m,b,"≤","\\le"),i(a,m,b,"≤","\\leq",!0),i(a,m,b,"<","\\lt",!0),i(a,m,b,"→","\\rightarrow",!0),i(a,m,b,"→","\\to"),i(a,y,b,"≱","\\ngeq",!0),i(a,y,b,"≰","\\nleq",!0),i(a,m,ve," ","\\ "),i(a,m,ve," ","\\space"),i(a,m,ve," ","\\nobreakspace"),i(M,m,ve," ","\\ "),i(M,m,ve," "," "),i(M,m,ve," ","\\space"),i(M,m,ve," ","\\nobreakspace"),i(a,m,ve,null,"\\nobreak"),i(a,m,ve,null,"\\allowbreak"),i(a,m,ie,",",","),i(a,m,ie,";",";"),i(a,y,H,"⊼","\\barwedge",!0),i(a,y,H,"⊻","\\veebar",!0),i(a,m,H,"⊙","\\odot",!0),i(a,m,H,"⊕","\\oplus",!0),i(a,m,H,"⊗","\\otimes",!0),i(a,m,A,"∂","\\partial",!0),i(a,m,H,"⊘","\\oslash",!0),i(a,y,H,"⊚","\\circledcirc",!0),i(a,y,H,"⊡","\\boxdot",!0),i(a,m,H,"△","\\bigtriangleup"),i(a,m,H,"▽","\\bigtriangledown"),i(a,m,H,"†","\\dagger"),i(a,m,H,"⋄","\\diamond"),i(a,m,H,"⋆","\\star"),i(a,m,H,"◃","\\triangleleft"),i(a,m,H,"▹","\\triangleright"),i(a,m,O,"{","\\{"),i(M,m,A,"{","\\{"),i(M,m,A,"{","\\textbraceleft"),i(a,m,Oe,"}","\\}"),i(M,m,A,"}","\\}"),i(M,m,A,"}","\\textbraceright"),i(a,m,O,"{","\\lbrace"),i(a,m,Oe,"}","\\rbrace"),i(a,m,O,"[","\\lbrack",!0),i(M,m,A,"[","\\lbrack",!0),i(a,m,Oe,"]","\\rbrack",!0),i(M,m,A,"]","\\rbrack",!0),i(a,m,O,"(","\\lparen",!0),i(a,m,Oe,")","\\rparen",!0),i(M,m,A,"<","\\textless",!0),i(M,m,A,">","\\textgreater",!0),i(a,m,O,"⌊","\\lfloor",!0),i(a,m,Oe,"⌋","\\rfloor",!0),i(a,m,O,"⌈","\\lceil",!0),i(a,m,Oe,"⌉","\\rceil",!0),i(a,m,A,"\\","\\backslash"),i(a,m,A,"∣","|"),i(a,m,A,"∣","\\vert"),i(M,m,A,"|","\\textbar",!0),i(a,m,A,"∥","\\|"),i(a,m,A,"∥","\\Vert"),i(M,m,A,"∥","\\textbardbl"),i(M,m,A,"~","\\textasciitilde"),i(M,m,A,"\\","\\textbackslash"),i(M,m,A,"^","\\textasciicircum"),i(a,m,b,"↑","\\uparrow",!0),i(a,m,b,"⇑","\\Uparrow",!0),i(a,m,b,"↓","\\downarrow",!0),i(a,m,b,"⇓","\\Downarrow",!0),i(a,m,b,"↕","\\updownarrow",!0),i(a,m,b,"⇕","\\Updownarrow",!0),i(a,m,z,"∐","\\coprod"),i(a,m,z,"⋁","\\bigvee"),i(a,m,z,"⋀","\\bigwedge"),i(a,m,z,"⨄","\\biguplus"),i(a,m,z,"⋂","\\bigcap"),i(a,m,z,"⋃","\\bigcup"),i(a,m,z,"∫","\\int"),i(a,m,z,"∫","\\intop"),i(a,m,z,"∬","\\iint"),i(a,m,z,"∭","\\iiint"),i(a,m,z,"∏","\\prod"),i(a,m,z,"∑","\\sum"),i(a,m,z,"⨂","\\bigotimes"),i(a,m,z,"⨁","\\bigoplus"),i(a,m,z,"⨀","\\bigodot"),i(a,m,z,"∮","\\oint"),i(a,m,z,"∯","\\oiint"),i(a,m,z,"∰","\\oiiint"),i(a,m,z,"⨆","\\bigsqcup"),i(a,m,z,"∫","\\smallint"),i(M,m,G,"…","\\textellipsis"),i(a,m,G,"…","\\mathellipsis"),i(M,m,G,"…","\\ldots",!0),i(a,m,G,"…","\\ldots",!0),i(a,m,G,"⋯","\\@cdots",!0),i(a,m,G,"⋱","\\ddots",!0),i(a,m,A,"⋮","\\varvdots"),i(a,m,pe,"ˊ","\\acute"),i(a,m,pe,"ˋ","\\grave"),i(a,m,pe,"¨","\\ddot"),i(a,m,pe,"~","\\tilde"),i(a,m,pe,"ˉ","\\bar"),i(a,m,pe,"˘","\\breve"),i(a,m,pe,"ˇ","\\check"),i(a,m,pe,"^","\\hat"),i(a,m,pe,"⃗","\\vec"),i(a,m,pe,"˙","\\dot"),i(a,m,pe,"˚","\\mathring"),i(a,m,w,"","\\@imath"),i(a,m,w,"","\\@jmath"),i(a,m,A,"ı","ı"),i(a,m,A,"ȷ","ȷ"),i(M,m,A,"ı","\\i",!0),i(M,m,A,"ȷ","\\j",!0),i(M,m,A,"ß","\\ss",!0),i(M,m,A,"æ","\\ae",!0),i(M,m,A,"œ","\\oe",!0),i(M,m,A,"ø","\\o",!0),i(M,m,A,"Æ","\\AE",!0),i(M,m,A,"Œ","\\OE",!0),i(M,m,A,"Ø","\\O",!0),i(M,m,pe,"ˊ","\\'"),i(M,m,pe,"ˋ","\\`"),i(M,m,pe,"ˆ","\\^"),i(M,m,pe,"˜","\\~"),i(M,m,pe,"ˉ","\\="),i(M,m,pe,"˘","\\u"),i(M,m,pe,"˙","\\."),i(M,m,pe,"¸","\\c"),i(M,m,pe,"˚","\\r"),i(M,m,pe,"ˇ","\\v"),i(M,m,pe,"¨",'\\"'),i(M,m,pe,"˝","\\H"),i(M,m,pe,"◯","\\textcircled");const Xe={"--":!0,"---":!0,"``":!0,"''":!0};i(M,m,A,"–","--",!0),i(M,m,A,"–","\\textendash"),i(M,m,A,"—","---",!0),i(M,m,A,"—","\\textemdash"),i(M,m,A,"‘","`",!0),i(M,m,A,"‘","\\textquoteleft"),i(M,m,A,"’","'",!0),i(M,m,A,"’","\\textquoteright"),i(M,m,A,"“","``",!0),i(M,m,A,"“","\\textquotedblleft"),i(M,m,A,"”","''",!0),i(M,m,A,"”","\\textquotedblright"),i(a,m,A,"°","\\degree",!0),i(M,m,A,"°","\\degree"),i(M,m,A,"°","\\textdegree",!0),i(a,m,A,"£","\\pounds"),i(a,m,A,"£","\\mathsterling",!0),i(M,m,A,"£","\\pounds"),i(M,m,A,"£","\\textsterling",!0),i(a,y,A,"✠","\\maltese"),i(M,y,A,"✠","\\maltese");const Q0='0123456789/@."';for(let t=0;t<Q0.length;t++){const e=Q0.charAt(t);i(a,m,A,e,e)}const ut='0123456789!@*()-=+";:?/.,';for(let t=0;t<ut.length;t++){const e=ut.charAt(t);i(M,m,A,e,e)}const ct="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";for(let t=0;t<ct.length;t++){const e=ct.charAt(t);i(a,m,w,e,e),i(M,m,A,e,e)}i(a,y,A,"C","ℂ"),i(M,y,A,"C","ℂ"),i(a,y,A,"H","ℍ"),i(M,y,A,"H","ℍ"),i(a,y,A,"N","ℕ"),i(M,y,A,"N","ℕ"),i(a,y,A,"P","ℙ"),i(M,y,A,"P","ℙ"),i(a,y,A,"Q","ℚ"),i(M,y,A,"Q","ℚ"),i(a,y,A,"R","ℝ"),i(M,y,A,"R","ℝ"),i(a,y,A,"Z","ℤ"),i(M,y,A,"Z","ℤ"),i(a,m,w,"h","ℎ"),i(M,m,w,"h","ℎ");let Z="";for(let t=0;t<ct.length;t++){const e=ct.charAt(t);Z=String.fromCharCode(55349,56320+t),i(a,m,w,e,Z),i(M,m,A,e,Z),Z=String.fromCharCode(55349,56372+t),i(a,m,w,e,Z),i(M,m,A,e,Z),Z=String.fromCharCode(55349,56424+t),i(a,m,w,e,Z),i(M,m,A,e,Z),Z=String.fromCharCode(55349,56580+t),i(a,m,w,e,Z),i(M,m,A,e,Z),Z=String.fromCharCode(55349,56684+t),i(a,m,w,e,Z),i(M,m,A,e,Z),Z=String.fromCharCode(55349,56736+t),i(a,m,w,e,Z),i(M,m,A,e,Z),Z=String.fromCharCode(55349,56788+t),i(a,m,w,e,Z),i(M,m,A,e,Z),Z=String.fromCharCode(55349,56840+t),i(a,m,w,e,Z),i(M,m,A,e,Z),Z=String.fromCharCode(55349,56944+t),i(a,m,w,e,Z),i(M,m,A,e,Z),t<26&&(Z=String.fromCharCode(55349,56632+t),i(a,m,w,e,Z),i(M,m,A,e,Z),Z=String.fromCharCode(55349,56476+t),i(a,m,w,e,Z),i(M,m,A,e,Z))}Z=String.fromCharCode(55349,56668),i(a,m,w,"k",Z),i(M,m,A,"k",Z);for(let t=0;t<10;t++){const e=t.toString();Z=String.fromCharCode(55349,57294+t),i(a,m,w,e,Z),i(M,m,A,e,Z),Z=String.fromCharCode(55349,57314+t),i(a,m,w,e,Z),i(M,m,A,e,Z),Z=String.fromCharCode(55349,57324+t),i(a,m,w,e,Z),i(M,m,A,e,Z),Z=String.fromCharCode(55349,57334+t),i(a,m,w,e,Z),i(M,m,A,e,Z)}const un="ÐÞþ";for(let t=0;t<un.length;t++){const e=un.charAt(t);i(a,m,w,e,e),i(M,m,A,e,e)}const _t=[["mathbf","textbf","Main-Bold"],["mathbf","textbf","Main-Bold"],["mathnormal","textit","Math-Italic"],["mathnormal","textit","Math-Italic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["mathscr","textscr","Script-Regular"],["","",""],["","",""],["","",""],["mathfrak","textfrak","Fraktur-Regular"],["mathfrak","textfrak","Fraktur-Regular"],["mathbb","textbb","AMS-Regular"],["mathbb","textbb","AMS-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathitsf","textitsf","SansSerif-Italic"],["mathitsf","textitsf","SansSerif-Italic"],["","",""],["","",""],["mathtt","texttt","Typewriter-Regular"],["mathtt","texttt","Typewriter-Regular"]],lr=[["mathbf","textbf","Main-Bold"],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathtt","texttt","Typewriter-Regular"]],di=function(t,e){const n=t.charCodeAt(0),r=t.charCodeAt(1),s=(n-55296)*1024+(r-56320)+65536,o=e==="math"?0:1;if(119808<=s&&s<120484){const l=Math.floor((s-119808)/26);return[_t[l][2],_t[l][o]]}else if(120782<=s&&s<=120831){const l=Math.floor((s-120782)/10);return[lr[l][2],lr[l][o]]}else{if(s===120485||s===120486)return[_t[0][2],_t[0][o]];if(120486<s&&s<120782)return["",""];throw new f("Unsupported character: "+t)}},Nt=function(t,e,n){return be[n][t]&&be[n][t].replace&&(t=be[n][t].replace),{value:t,metrics:d0(t,e,n)}},l0=function(t,e,n,r,s){const o=Nt(t,e,n),l=o.metrics;t=o.value;let d;if(l){let g=l.italic;(n==="text"||r&&r.font==="mathit")&&(g=0),d=new qe(t,l.height,l.depth,g,l.skew,l.width,s)}else typeof console<"u"&&console.warn("No character metrics "+("for '"+t+"' in style '"+e+"' and mode '"+n+"'")),d=new qe(t,0,0,0,0,0,s);if(r){d.maxFontSize=r.sizeMultiplier,r.style.isTight()&&d.classes.push("mtight");const g=r.getColor();g&&(d.style.color=g)}return d},pi=function(t,e,n,r){return r===void 0&&(r=[]),n.font==="boldsymbol"&&Nt(t,"Main-Bold",e).metrics?l0(t,"Main-Bold",e,n,r.concat(["mathbf"])):t==="\\"||be[e][t].font==="main"?l0(t,"Main-Regular",e,n,r):l0(t,"AMS-Regular",e,n,r.concat(["amsrm"]))},fi=function(t,e,n,r,s){return s!=="textord"&&Nt(t,"Math-BoldItalic",e).metrics?{fontName:"Math-BoldItalic",fontClass:"boldsymbol"}:{fontName:"Main-Bold",fontClass:"mathbf"}},gi=function(t,e,n){const r=t.mode,s=t.text,o=["mord"],l=r==="math"||r==="text"&&e.font,d=l?e.font:e.fontFamily;let g="",k="";if(s.charCodeAt(0)===55349&&([g,k]=di(s,r)),g.length>0)return l0(s,g,r,e,o.concat(k));if(d){let S,v;if(d==="boldsymbol"){const T=fi(s,r,e,o,n);S=T.fontName,v=[T.fontClass]}else l?(S=hr[d].fontName,v=[d]):(S=Rt(d,e.fontWeight,e.fontShape),v=[d,e.fontWeight,e.fontShape]);if(Nt(s,S,r).metrics)return l0(s,S,r,e,o.concat(v));if(Xe.hasOwnProperty(s)&&S.slice(0,10)==="Typewriter"){const T=[];for(let _=0;_<s.length;_++)T.push(l0(s[_],S,r,e,o.concat(v)));return cr(T)}}if(n==="mathord")return l0(s,"Math-Italic",r,e,o.concat(["mathnormal"]));if(n==="textord"){const S=be[r][s]&&be[r][s].font;if(S==="ams"){const v=Rt("amsrm",e.fontWeight,e.fontShape);return l0(s,v,r,e,o.concat("amsrm",e.fontWeight,e.fontShape))}else if(S==="main"||!S){const v=Rt("textrm",e.fontWeight,e.fontShape);return l0(s,v,r,e,o.concat(e.fontWeight,e.fontShape))}else{const v=Rt(S,e.fontWeight,e.fontShape);return l0(s,v,r,e,o.concat(v,e.fontWeight,e.fontShape))}}else throw new Error("unexpected type: "+n+" in makeOrd")},bi=(t,e)=>{if(He(t.classes)!==He(e.classes)||t.skew!==e.skew||t.maxFontSize!==e.maxFontSize)return!1;if(t.classes.length===1){const n=t.classes[0];if(n==="mbin"||n==="mord")return!1}for(const n in t.style)if(t.style.hasOwnProperty(n)&&t.style[n]!==e.style[n])return!1;for(const n in e.style)if(e.style.hasOwnProperty(n)&&t.style[n]!==e.style[n])return!1;return!0},yi=t=>{for(let e=0;e<t.length-1;e++){const n=t[e],r=t[e+1];n instanceof qe&&r instanceof qe&&bi(n,r)&&(n.text+=r.text,n.height=Math.max(n.height,r.height),n.depth=Math.max(n.depth,r.depth),n.italic=r.italic,t.splice(e+1,1),e--)}return t},cn=function(t){let e=0,n=0,r=0;for(let s=0;s<t.children.length;s++){const o=t.children[s];o.height>e&&(e=o.height),o.depth>n&&(n=o.depth),o.maxFontSize>r&&(r=o.maxFontSize)}t.height=e,t.depth=n,t.maxFontSize=r},$e=function(t,e,n,r){const s=new e0(t,e,n,r);return cn(s),s},ur=(t,e,n,r)=>new e0(t,e,n,r),xi=function(t,e,n){const r=$e([t],[],e);return r.height=Math.max(n||e.fontMetrics().defaultRuleThickness,e.minRuleThickness),r.style.borderBottomWidth=I(r.height),r.maxFontSize=1,r},wi=function(t,e,n,r){const s=new at(t,e,n,r);return cn(s),s},cr=function(t){const e=new C0(t);return cn(e),e},Di=function(t,e){return t instanceof C0?$e([],[t],e):t},ki=function(t){if(t.positionType==="individualShift"){const n=t.children,r=[n[0]],s=-n[0].shift-n[0].elem.depth;let o=s;for(let l=1;l<n.length;l++){const d=-n[l].shift-o-n[l].elem.depth,g=d-(n[l-1].elem.height+n[l-1].elem.depth);o=o+d,r.push({type:"kern",size:g}),r.push(n[l])}return{children:r,depth:s}}let e;if(t.positionType==="top"){let n=t.positionData;for(let r=0;r<t.children.length;r++){const s=t.children[r];n-=s.type==="kern"?s.size:s.elem.height+s.elem.depth}e=n}else if(t.positionType==="bottom")e=-t.positionData;else{const n=t.children[0];if(n.type!=="elem")throw new Error('First child must have type "elem".');if(t.positionType==="shift")e=-n.elem.depth-t.positionData;else if(t.positionType==="firstBaseline")e=-n.elem.depth;else throw new Error("Invalid positionType "+t.positionType+".")}return{children:t.children,depth:e}},Ai=function(t,e){const{children:n,depth:r}=ki(t);let s=0;for(let _=0;_<n.length;_++){const P=n[_];if(P.type==="elem"){const V=P.elem;s=Math.max(s,V.maxFontSize,V.height)}}s+=2;const o=$e(["pstrut"],[]);o.style.height=I(s);const l=[];let d=r,g=r,k=r;for(let _=0;_<n.length;_++){const P=n[_];if(P.type==="kern")k+=P.size;else{const V=P.elem,re=P.wrapperClasses||[],ne=P.wrapperStyle||{},se=$e(re,[o,V],void 0,ne);se.style.top=I(-s-k-V.depth),P.marginLeft&&(se.style.marginLeft=P.marginLeft),P.marginRight&&(se.style.marginRight=P.marginRight),l.push(se),k+=V.height+V.depth}d=Math.min(d,k),g=Math.max(g,k)}const S=$e(["vlist"],l);S.style.height=I(g);let v;if(d<0){const _=$e([],[]),P=$e(["vlist"],[_]);P.style.height=I(-d);const V=$e(["vlist-s"],[new qe("​")]);v=[$e(["vlist-r"],[S,V]),$e(["vlist-r"],[P])]}else v=[$e(["vlist-r"],[S])];const T=$e(["vlist-t"],v);return v.length===2&&T.classes.push("vlist-t2"),T.height=g,T.depth=-d,T},Si=(t,e)=>{const n=$e(["mspace"],[],e),r=de(t,e);return n.style.marginRight=I(r),n},Rt=function(t,e,n){let r="";switch(t){case"amsrm":r="AMS";break;case"textrm":r="Main";break;case"textsf":r="SansSerif";break;case"texttt":r="Typewriter";break;default:r=t}let s;return e==="textbf"&&n==="textit"?s="BoldItalic":e==="textbf"?s="Bold":e==="textit"?s="Italic":s="Regular",r+"-"+s},hr={mathbf:{variant:"bold",fontName:"Main-Bold"},mathrm:{variant:"normal",fontName:"Main-Regular"},textit:{variant:"italic",fontName:"Main-Italic"},mathit:{variant:"italic",fontName:"Main-Italic"},mathnormal:{variant:"italic",fontName:"Math-Italic"},mathbb:{variant:"double-struck",fontName:"AMS-Regular"},mathcal:{variant:"script",fontName:"Caligraphic-Regular"},mathfrak:{variant:"fraktur",fontName:"Fraktur-Regular"},mathscr:{variant:"script",fontName:"Script-Regular"},mathsf:{variant:"sans-serif",fontName:"SansSerif-Regular"},mathtt:{variant:"monospace",fontName:"Typewriter-Regular"}},mr={vec:["vec",.471,.714],oiintSize1:["oiintSize1",.957,.499],oiintSize2:["oiintSize2",1.472,.659],oiiintSize1:["oiiintSize1",1.304,.499],oiiintSize2:["oiiintSize2",1.98,.659]};var E={fontMap:hr,makeSymbol:l0,mathsym:pi,makeSpan:$e,makeSvgSpan:ur,makeLineSpan:xi,makeAnchor:wi,makeFragment:cr,wrapFragment:Di,makeVList:Ai,makeOrd:gi,makeGlue:Si,staticSvg:function(t,e){const[n,r,s]=mr[t],o=new p0(n),l=new t0([o],{width:I(r),height:I(s),style:"width:"+I(r),viewBox:"0 0 "+1e3*r+" "+1e3*s,preserveAspectRatio:"xMinYMin"}),d=ur(["overlay"],[l],e);return d.height=s,d.style.height=I(s),d.style.width=I(r),d},svgData:mr,tryCombineChars:yi};const Ae={number:3,unit:"mu"},q0={number:4,unit:"mu"},A0={number:5,unit:"mu"},vi={mord:{mop:Ae,mbin:q0,mrel:A0,minner:Ae},mop:{mord:Ae,mop:Ae,mrel:A0,minner:Ae},mbin:{mord:q0,mop:q0,mopen:q0,minner:q0},mrel:{mord:A0,mop:A0,mopen:A0,minner:A0},mopen:{},mclose:{mop:Ae,mbin:q0,mrel:A0,minner:Ae},mpunct:{mord:Ae,mop:Ae,mrel:A0,mopen:Ae,mclose:Ae,mpunct:Ae,minner:Ae},minner:{mord:Ae,mop:Ae,mbin:q0,mrel:A0,mopen:Ae,mpunct:Ae,minner:Ae}},Fi={mord:{mop:Ae},mop:{mord:Ae,mop:Ae},mbin:{},mrel:{},mopen:{},mclose:{mop:Ae},mpunct:{},minner:{mop:Ae}},dr={},It={},Ot={};function L(t){let{type:e,names:n,props:r,handler:s,htmlBuilder:o,mathmlBuilder:l}=t;const d={type:e,numArgs:r.numArgs,argTypes:r.argTypes,allowedInArgument:!!r.allowedInArgument,allowedInText:!!r.allowedInText,allowedInMath:r.allowedInMath===void 0?!0:r.allowedInMath,numOptionalArgs:r.numOptionalArgs||0,infix:!!r.infix,primitive:!!r.primitive,handler:s};for(let g=0;g<n.length;++g)dr[n[g]]=d;e&&(o&&(It[e]=o),l&&(Ot[e]=l))}function P0(t){let{type:e,htmlBuilder:n,mathmlBuilder:r}=t;L({type:e,names:[],props:{numArgs:0},handler(){throw new Error("Should never be called.")},htmlBuilder:n,mathmlBuilder:r})}const Lt=function(t){return t.type==="ordgroup"&&t.body.length===1?t.body[0]:t},Me=function(t){return t.type==="ordgroup"?t.body:[t]},S0=E.makeSpan,Ei=["leftmost","mbin","mopen","mrel","mop","mpunct"],Ti=["rightmost","mrel","mclose","mpunct"],Ci={display:q.DISPLAY,text:q.TEXT,script:q.SCRIPT,scriptscript:q.SCRIPTSCRIPT},Mi={mord:"mord",mop:"mop",mbin:"mbin",mrel:"mrel",mopen:"mopen",mclose:"mclose",mpunct:"mpunct",minner:"minner"},Re=function(t,e,n,r){r===void 0&&(r=[null,null]);const s=[];for(let k=0;k<t.length;k++){const S=ae(t[k],e);if(S instanceof C0){const v=S.children;s.push(...v)}else s.push(S)}if(E.tryCombineChars(s),!n)return s;let o=e;if(t.length===1){const k=t[0];k.type==="sizing"?o=e.havingSize(k.size):k.type==="styling"&&(o=e.havingStyle(Ci[k.style]))}const l=S0([r[0]||"leftmost"],[],e),d=S0([r[1]||"rightmost"],[],e),g=n==="root";return hn(s,(k,S)=>{const v=S.classes[0],T=k.classes[0];v==="mbin"&&N.contains(Ti,T)?S.classes[0]="mord":T==="mbin"&&N.contains(Ei,v)&&(k.classes[0]="mord")},{node:l},d,g),hn(s,(k,S)=>{const v=dn(S),T=dn(k),_=v&&T?k.hasClass("mtight")?Fi[v][T]:vi[v][T]:null;if(_)return E.makeGlue(_,o)},{node:l},d,g),s},hn=function(t,e,n,r,s){r&&t.push(r);let o=0;for(;o<t.length;o++){const l=t[o],d=pr(l);if(d){hn(d.children,e,n,null,s);continue}const g=!l.hasClass("mspace");if(g){const k=e(l,n.node);k&&(n.insertAfter?n.insertAfter(k):(t.unshift(k),o++))}g?n.node=l:s&&l.hasClass("newline")&&(n.node=S0(["leftmost"])),n.insertAfter=(k=>S=>{t.splice(k+1,0,S),o++})(o)}r&&t.pop()},pr=function(t){return t instanceof C0||t instanceof at||t instanceof e0&&t.hasClass("enclosing")?t:null},mn=function(t,e){const n=pr(t);if(n){const r=n.children;if(r.length){if(e==="right")return mn(r[r.length-1],"right");if(e==="left")return mn(r[0],"left")}}return t},dn=function(t,e){return t?(e&&(t=mn(t,e)),Mi[t.classes[0]]||null):null},ht=function(t,e){const n=["nulldelimiter"].concat(t.baseSizingClasses());return S0(e.concat(n))},ae=function(t,e,n){if(!t)return S0();if(It[t.type]){let r=It[t.type](t,e);if(n&&e.size!==n.size){r=S0(e.sizingClasses(n),[r],e);const s=e.sizeMultiplier/n.sizeMultiplier;r.height*=s,r.depth*=s}return r}else throw new f("Got group of unknown type: '"+t.type+"'")};function Ht(t,e){const n=S0(["base"],t,e),r=S0(["strut"]);return r.style.height=I(n.height+n.depth),n.depth&&(r.style.verticalAlign=I(-n.depth)),n.children.unshift(r),n}function pn(t,e){let n=null;t.length===1&&t[0].type==="tag"&&(n=t[0].tag,t=t[0].body);const r=Re(t,e,"root");let s;r.length===2&&r[1].hasClass("tag")&&(s=r.pop());const o=[];let l=[];for(let k=0;k<r.length;k++)if(l.push(r[k]),r[k].hasClass("mbin")||r[k].hasClass("mrel")||r[k].hasClass("allowbreak")){let S=!1;for(;k<r.length-1&&r[k+1].hasClass("mspace")&&!r[k+1].hasClass("newline");)k++,l.push(r[k]),r[k].hasClass("nobreak")&&(S=!0);S||(o.push(Ht(l,e)),l=[])}else r[k].hasClass("newline")&&(l.pop(),l.length>0&&(o.push(Ht(l,e)),l=[]),o.push(r[k]));l.length>0&&o.push(Ht(l,e));let d;n?(d=Ht(Re(n,e,!0)),d.classes=["tag"],o.push(d)):s&&o.push(s);const g=S0(["katex-html"],o);if(g.setAttribute("aria-hidden","true"),d){const k=d.children[0];k.style.height=I(g.height+g.depth),g.depth&&(k.style.verticalAlign=I(-g.depth))}return g}function fr(t){return new C0(t)}class n0{constructor(e,n,r){this.type=void 0,this.attributes=void 0,this.children=void 0,this.classes=void 0,this.type=e,this.attributes={},this.children=n||[],this.classes=r||[]}setAttribute(e,n){this.attributes[e]=n}getAttribute(e){return this.attributes[e]}toNode(){const e=document.createElementNS("http://www.w3.org/1998/Math/MathML",this.type);for(const n in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,n)&&e.setAttribute(n,this.attributes[n]);this.classes.length>0&&(e.className=He(this.classes));for(let n=0;n<this.children.length;n++)e.appendChild(this.children[n].toNode());return e}toMarkup(){let e="<"+this.type;for(const n in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,n)&&(e+=" "+n+'="',e+=N.escape(this.attributes[n]),e+='"');this.classes.length>0&&(e+=' class ="'+N.escape(He(this.classes))+'"'),e+=">";for(let n=0;n<this.children.length;n++)e+=this.children[n].toMarkup();return e+="</"+this.type+">",e}toText(){return this.children.map(e=>e.toText()).join("")}}class mt{constructor(e){this.text=void 0,this.text=e}toNode(){return document.createTextNode(this.text)}toMarkup(){return N.escape(this.toText())}toText(){return this.text}}class zi{constructor(e){this.width=void 0,this.character=void 0,this.width=e,e>=.05555&&e<=.05556?this.character=" ":e>=.1666&&e<=.1667?this.character=" ":e>=.2222&&e<=.2223?this.character=" ":e>=.2777&&e<=.2778?this.character="  ":e>=-.05556&&e<=-.05555?this.character=" ⁣":e>=-.1667&&e<=-.1666?this.character=" ⁣":e>=-.2223&&e<=-.2222?this.character=" ⁣":e>=-.2778&&e<=-.2777?this.character=" ⁣":this.character=null}toNode(){if(this.character)return document.createTextNode(this.character);{const e=document.createElementNS("http://www.w3.org/1998/Math/MathML","mspace");return e.setAttribute("width",I(this.width)),e}}toMarkup(){return this.character?"<mtext>"+this.character+"</mtext>":'<mspace width="'+I(this.width)+'"/>'}toText(){return this.character?this.character:" "}}var R={MathNode:n0,TextNode:mt,SpaceNode:zi,newDocumentFragment:fr};const r0=function(t,e,n){return be[e][t]&&be[e][t].replace&&t.charCodeAt(0)!==55349&&!(Xe.hasOwnProperty(t)&&n&&(n.fontFamily&&n.fontFamily.slice(4,6)==="tt"||n.font&&n.font.slice(4,6)==="tt"))&&(t=be[e][t].replace),new R.TextNode(t)},fn=function(t){return t.length===1?t[0]:new R.MathNode("mrow",t)},gn=function(t,e){if(e.fontFamily==="texttt")return"monospace";if(e.fontFamily==="textsf")return e.fontShape==="textit"&&e.fontWeight==="textbf"?"sans-serif-bold-italic":e.fontShape==="textit"?"sans-serif-italic":e.fontWeight==="textbf"?"bold-sans-serif":"sans-serif";if(e.fontShape==="textit"&&e.fontWeight==="textbf")return"bold-italic";if(e.fontShape==="textit")return"italic";if(e.fontWeight==="textbf")return"bold";const n=e.font;if(!n||n==="mathnormal")return null;const r=t.mode;if(n==="mathit")return"italic";if(n==="boldsymbol")return t.type==="textord"?"bold":"bold-italic";if(n==="mathbf")return"bold";if(n==="mathbb")return"double-struck";if(n==="mathfrak")return"fraktur";if(n==="mathscr"||n==="mathcal")return"script";if(n==="mathsf")return"sans-serif";if(n==="mathtt")return"monospace";let s=t.text;if(N.contains(["\\imath","\\jmath"],s))return null;be[r][s]&&be[r][s].replace&&(s=be[r][s].replace);const o=E.fontMap[n].fontName;return d0(s,o,r)?E.fontMap[n].variant:null},Ve=function(t,e,n){if(t.length===1){const o=fe(t[0],e);return n&&o instanceof n0&&o.type==="mo"&&(o.setAttribute("lspace","0em"),o.setAttribute("rspace","0em")),[o]}const r=[];let s;for(let o=0;o<t.length;o++){const l=fe(t[o],e);if(l instanceof n0&&s instanceof n0){if(l.type==="mtext"&&s.type==="mtext"&&l.getAttribute("mathvariant")===s.getAttribute("mathvariant")){s.children.push(...l.children);continue}else if(l.type==="mn"&&s.type==="mn"){s.children.push(...l.children);continue}else if(l.type==="mi"&&l.children.length===1&&s.type==="mn"){const d=l.children[0];if(d instanceof mt&&d.text==="."){s.children.push(...l.children);continue}}else if(s.type==="mi"&&s.children.length===1){const d=s.children[0];if(d instanceof mt&&d.text==="̸"&&(l.type==="mo"||l.type==="mi"||l.type==="mn")){const g=l.children[0];g instanceof mt&&g.text.length>0&&(g.text=g.text.slice(0,1)+"̸"+g.text.slice(1),r.pop())}}}r.push(l),s=l}return r},R0=function(t,e,n){return fn(Ve(t,e,n))},fe=function(t,e){if(!t)return new R.MathNode("mrow");if(Ot[t.type])return Ot[t.type](t,e);throw new f("Got group of unknown type: '"+t.type+"'")};function gr(t,e,n,r,s){const o=Ve(t,n);let l;o.length===1&&o[0]instanceof n0&&N.contains(["mrow","mtable"],o[0].type)?l=o[0]:l=new R.MathNode("mrow",o);const d=new R.MathNode("annotation",[new R.TextNode(e)]);d.setAttribute("encoding","application/x-tex");const g=new R.MathNode("semantics",[l,d]),k=new R.MathNode("math",[g]);k.setAttribute("xmlns","http://www.w3.org/1998/Math/MathML"),r&&k.setAttribute("display","block");const S=s?"katex":"katex-mathml";return E.makeSpan([S],[k])}const br=function(t){return new j0({style:t.displayMode?q.DISPLAY:q.TEXT,maxSize:t.maxSize,minRuleThickness:t.minRuleThickness})},yr=function(t,e){if(e.displayMode){const n=["katex-display"];e.leqno&&n.push("leqno"),e.fleqn&&n.push("fleqn"),t=E.makeSpan(n,[t])}return t},Bi=function(t,e,n){const r=br(n);let s;if(n.output==="mathml")return gr(t,e,r,n.displayMode,!0);if(n.output==="html"){const o=pn(t,r);s=E.makeSpan(["katex"],[o])}else{const o=gr(t,e,r,n.displayMode,!1),l=pn(t,r);s=E.makeSpan(["katex"],[o,l])}return yr(s,n)},_i=function(t,e,n){const r=br(n),s=pn(t,r),o=E.makeSpan(["katex"],[s]);return yr(o,n)},Ni={widehat:"^",widecheck:"ˇ",widetilde:"~",utilde:"~",overleftarrow:"←",underleftarrow:"←",xleftarrow:"←",overrightarrow:"→",underrightarrow:"→",xrightarrow:"→",underbrace:"⏟",overbrace:"⏞",overgroup:"⏠",undergroup:"⏡",overleftrightarrow:"↔",underleftrightarrow:"↔",xleftrightarrow:"↔",Overrightarrow:"⇒",xRightarrow:"⇒",overleftharpoon:"↼",xleftharpoonup:"↼",overrightharpoon:"⇀",xrightharpoonup:"⇀",xLeftarrow:"⇐",xLeftrightarrow:"⇔",xhookleftarrow:"↩",xhookrightarrow:"↪",xmapsto:"↦",xrightharpoondown:"⇁",xleftharpoondown:"↽",xrightleftharpoons:"⇌",xleftrightharpoons:"⇋",xtwoheadleftarrow:"↞",xtwoheadrightarrow:"↠",xlongequal:"=",xtofrom:"⇄",xrightleftarrows:"⇄",xrightequilibrium:"⇌",xleftequilibrium:"⇋","\\cdrightarrow":"→","\\cdleftarrow":"←","\\cdlongequal":"="},Ri=function(t){const e=new R.MathNode("mo",[new R.TextNode(Ni[t.replace(/^\\/,"")])]);return e.setAttribute("stretchy","true"),e},Ii={overrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],overleftarrow:[["leftarrow"],.888,522,"xMinYMin"],underrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],underleftarrow:[["leftarrow"],.888,522,"xMinYMin"],xrightarrow:[["rightarrow"],1.469,522,"xMaxYMin"],"\\cdrightarrow":[["rightarrow"],3,522,"xMaxYMin"],xleftarrow:[["leftarrow"],1.469,522,"xMinYMin"],"\\cdleftarrow":[["leftarrow"],3,522,"xMinYMin"],Overrightarrow:[["doublerightarrow"],.888,560,"xMaxYMin"],xRightarrow:[["doublerightarrow"],1.526,560,"xMaxYMin"],xLeftarrow:[["doubleleftarrow"],1.526,560,"xMinYMin"],overleftharpoon:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoonup:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoondown:[["leftharpoondown"],.888,522,"xMinYMin"],overrightharpoon:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoonup:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoondown:[["rightharpoondown"],.888,522,"xMaxYMin"],xlongequal:[["longequal"],.888,334,"xMinYMin"],"\\cdlongequal":[["longequal"],3,334,"xMinYMin"],xtwoheadleftarrow:[["twoheadleftarrow"],.888,334,"xMinYMin"],xtwoheadrightarrow:[["twoheadrightarrow"],.888,334,"xMaxYMin"],overleftrightarrow:[["leftarrow","rightarrow"],.888,522],overbrace:[["leftbrace","midbrace","rightbrace"],1.6,548],underbrace:[["leftbraceunder","midbraceunder","rightbraceunder"],1.6,548],underleftrightarrow:[["leftarrow","rightarrow"],.888,522],xleftrightarrow:[["leftarrow","rightarrow"],1.75,522],xLeftrightarrow:[["doubleleftarrow","doublerightarrow"],1.75,560],xrightleftharpoons:[["leftharpoondownplus","rightharpoonplus"],1.75,716],xleftrightharpoons:[["leftharpoonplus","rightharpoondownplus"],1.75,716],xhookleftarrow:[["leftarrow","righthook"],1.08,522],xhookrightarrow:[["lefthook","rightarrow"],1.08,522],overlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],underlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],overgroup:[["leftgroup","rightgroup"],.888,342],undergroup:[["leftgroupunder","rightgroupunder"],.888,342],xmapsto:[["leftmapsto","rightarrow"],1.5,522],xtofrom:[["leftToFrom","rightToFrom"],1.75,528],xrightleftarrows:[["baraboveleftarrow","rightarrowabovebar"],1.75,901],xrightequilibrium:[["baraboveshortleftharpoon","rightharpoonaboveshortbar"],1.75,716],xleftequilibrium:[["shortbaraboveleftharpoon","shortrightharpoonabovebar"],1.75,716]},Oi=function(t){return t.type==="ordgroup"?t.body.length:1};var v0={encloseSpan:function(t,e,n,r,s){let o;const l=t.height+t.depth+n+r;if(/fbox|color|angl/.test(e)){if(o=E.makeSpan(["stretchy",e],[],s),e==="fbox"){const d=s.color&&s.getColor();d&&(o.style.borderColor=d)}}else{const d=[];/^[bx]cancel$/.test(e)&&d.push(new N0({x1:"0",y1:"0",x2:"100%",y2:"100%","stroke-width":"0.046em"})),/^x?cancel$/.test(e)&&d.push(new N0({x1:"0",y1:"100%",x2:"100%",y2:"0","stroke-width":"0.046em"}));const g=new t0(d,{width:"100%",height:I(l)});o=E.makeSvgSpan([],[g],s)}return o.height=l,o.style.height=I(l),o},mathMLnode:Ri,svgSpan:function(t,e){function n(){let l=4e5;const d=t.label.slice(1);if(N.contains(["widehat","widecheck","widetilde","utilde"],d)){const k=Oi(t.base);let S,v,T;if(k>5)d==="widehat"||d==="widecheck"?(S=420,l=2364,T=.42,v=d+"4"):(S=312,l=2340,T=.34,v="tilde4");else{const V=[1,1,2,2,3,3][k];d==="widehat"||d==="widecheck"?(l=[0,1062,2364,2364,2364][V],S=[0,239,300,360,420][V],T=[0,.24,.3,.3,.36,.42][V],v=d+V):(l=[0,600,1033,2339,2340][V],S=[0,260,286,306,312][V],T=[0,.26,.286,.3,.306,.34][V],v="tilde"+V)}const _=new p0(v),P=new t0([_],{width:"100%",height:I(T),viewBox:"0 0 "+l+" "+S,preserveAspectRatio:"none"});return{span:E.makeSvgSpan([],[P],e),minWidth:0,height:T}}else{const g=[],k=Ii[d],[S,v,T]=k,_=T/1e3,P=S.length;let V,re;if(P===1){const ne=k[3];V=["hide-tail"],re=[ne]}else if(P===2)V=["halfarrow-left","halfarrow-right"],re=["xMinYMin","xMaxYMin"];else if(P===3)V=["brace-left","brace-center","brace-right"],re=["xMinYMin","xMidYMin","xMaxYMin"];else throw new Error(`Correct katexImagesData or update code here to support
                    `+P+" children.");for(let ne=0;ne<P;ne++){const se=new p0(S[ne]),le=new t0([se],{width:"400em",height:I(_),viewBox:"0 0 "+l+" "+T,preserveAspectRatio:re[ne]+" slice"}),ye=E.makeSvgSpan([V[ne]],[le],e);if(P===1)return{span:ye,minWidth:v,height:_};ye.style.height=I(_),g.push(ye)}return{span:E.makeSpan(["stretchy"],g,e),minWidth:v,height:_}}}const{span:r,minWidth:s,height:o}=n();return r.height=o,r.style.height=I(o),s>0&&(r.style.minWidth=I(s)),r}};function J(t,e){if(!t||t.type!==e)throw new Error("Expected node of type "+e+", but got "+(t?"node of type "+t.type:String(t)));return t}function bn(t){const e=qt(t);if(!e)throw new Error("Expected node of symbol group type, but got "+(t?"node of type "+t.type:String(t)));return e}function qt(t){return t&&(t.type==="atom"||lt.hasOwnProperty(t.type))?t:null}const yn=(t,e)=>{let n,r,s;t&&t.type==="supsub"?(r=J(t.base,"accent"),n=r.base,t.base=n,s=ln(ae(t,e)),t.base=r):(r=J(t,"accent"),n=r.base);const o=ae(n,e.havingCrampedStyle()),l=r.isShifty&&N.isCharacterBox(n);let d=0;if(l){const T=N.getBaseElem(n),_=ae(T,e.havingCrampedStyle());d=K0(_).skew}const g=r.label==="\\c";let k=g?o.height+o.depth:Math.min(o.height,e.fontMetrics().xHeight),S;if(r.isStretchy)S=v0.svgSpan(r,e),S=E.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:o},{type:"elem",elem:S,wrapperClasses:["svg-align"],wrapperStyle:d>0?{width:"calc(100% - "+I(2*d)+")",marginLeft:I(2*d)}:void 0}]},e);else{let T,_;r.label==="\\vec"?(T=E.staticSvg("vec",e),_=E.svgData.vec[1]):(T=E.makeOrd({mode:r.mode,text:r.label},e,"textord"),T=K0(T),T.italic=0,_=T.width,g&&(k+=T.depth)),S=E.makeSpan(["accent-body"],[T]);const P=r.label==="\\textcircled";P&&(S.classes.push("accent-full"),k=o.height);let V=d;P||(V-=_/2),S.style.left=I(V),r.label==="\\textcircled"&&(S.style.top=".2em"),S=E.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:o},{type:"kern",size:-k},{type:"elem",elem:S}]},e)}const v=E.makeSpan(["mord","accent"],[S],e);return s?(s.children[0]=v,s.height=Math.max(v.height,s.height),s.classes[0]="mord",s):v},xr=(t,e)=>{const n=t.isStretchy?v0.mathMLnode(t.label):new R.MathNode("mo",[r0(t.label,t.mode)]),r=new R.MathNode("mover",[fe(t.base,e),n]);return r.setAttribute("accent","true"),r},Li=new RegExp(["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring"].map(t=>"\\"+t).join("|"));L({type:"accent",names:["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring","\\widecheck","\\widehat","\\widetilde","\\overrightarrow","\\overleftarrow","\\Overrightarrow","\\overleftrightarrow","\\overgroup","\\overlinesegment","\\overleftharpoon","\\overrightharpoon"],props:{numArgs:1},handler:(t,e)=>{const n=Lt(e[0]),r=!Li.test(t.funcName),s=!r||t.funcName==="\\widehat"||t.funcName==="\\widetilde"||t.funcName==="\\widecheck";return{type:"accent",mode:t.parser.mode,label:t.funcName,isStretchy:r,isShifty:s,base:n}},htmlBuilder:yn,mathmlBuilder:xr}),L({type:"accent",names:["\\'","\\`","\\^","\\~","\\=","\\u","\\.",'\\"',"\\c","\\r","\\H","\\v","\\textcircled"],props:{numArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["primitive"]},handler:(t,e)=>{const n=e[0];let r=t.parser.mode;return r==="math"&&(t.parser.settings.reportNonstrict("mathVsTextAccents","LaTeX's accent "+t.funcName+" works only in text mode"),r="text"),{type:"accent",mode:r,label:t.funcName,isStretchy:!1,isShifty:!0,base:n}},htmlBuilder:yn,mathmlBuilder:xr}),L({type:"accentUnder",names:["\\underleftarrow","\\underrightarrow","\\underleftrightarrow","\\undergroup","\\underlinesegment","\\utilde"],props:{numArgs:1},handler:(t,e)=>{let{parser:n,funcName:r}=t;const s=e[0];return{type:"accentUnder",mode:n.mode,label:r,base:s}},htmlBuilder:(t,e)=>{const n=ae(t.base,e),r=v0.svgSpan(t,e),s=t.label==="\\utilde"?.12:0,o=E.makeVList({positionType:"top",positionData:n.height,children:[{type:"elem",elem:r,wrapperClasses:["svg-align"]},{type:"kern",size:s},{type:"elem",elem:n}]},e);return E.makeSpan(["mord","accentunder"],[o],e)},mathmlBuilder:(t,e)=>{const n=v0.mathMLnode(t.label),r=new R.MathNode("munder",[fe(t.base,e),n]);return r.setAttribute("accentunder","true"),r}});const Pt=t=>{const e=new R.MathNode("mpadded",t?[t]:[]);return e.setAttribute("width","+0.6em"),e.setAttribute("lspace","0.3em"),e};L({type:"xArrow",names:["\\xleftarrow","\\xrightarrow","\\xLeftarrow","\\xRightarrow","\\xleftrightarrow","\\xLeftrightarrow","\\xhookleftarrow","\\xhookrightarrow","\\xmapsto","\\xrightharpoondown","\\xrightharpoonup","\\xleftharpoondown","\\xleftharpoonup","\\xrightleftharpoons","\\xleftrightharpoons","\\xlongequal","\\xtwoheadrightarrow","\\xtwoheadleftarrow","\\xtofrom","\\xrightleftarrows","\\xrightequilibrium","\\xleftequilibrium","\\\\cdrightarrow","\\\\cdleftarrow","\\\\cdlongequal"],props:{numArgs:1,numOptionalArgs:1},handler(t,e,n){let{parser:r,funcName:s}=t;return{type:"xArrow",mode:r.mode,label:s,body:e[0],below:n[0]}},htmlBuilder(t,e){const n=e.style;let r=e.havingStyle(n.sup());const s=E.wrapFragment(ae(t.body,r,e),e),o=t.label.slice(0,2)==="\\x"?"x":"cd";s.classes.push(o+"-arrow-pad");let l;t.below&&(r=e.havingStyle(n.sub()),l=E.wrapFragment(ae(t.below,r,e),e),l.classes.push(o+"-arrow-pad"));const d=v0.svgSpan(t,e),g=-e.fontMetrics().axisHeight+.5*d.height;let k=-e.fontMetrics().axisHeight-.5*d.height-.111;(s.depth>.25||t.label==="\\xleftequilibrium")&&(k-=s.depth);let S;if(l){const v=-e.fontMetrics().axisHeight+l.height+.5*d.height+.111;S=E.makeVList({positionType:"individualShift",children:[{type:"elem",elem:s,shift:k},{type:"elem",elem:d,shift:g},{type:"elem",elem:l,shift:v}]},e)}else S=E.makeVList({positionType:"individualShift",children:[{type:"elem",elem:s,shift:k},{type:"elem",elem:d,shift:g}]},e);return S.children[0].children[0].children[1].classes.push("svg-align"),E.makeSpan(["mrel","x-arrow"],[S],e)},mathmlBuilder(t,e){const n=v0.mathMLnode(t.label);n.setAttribute("minsize",t.label.charAt(0)==="x"?"1.75em":"3.0em");let r;if(t.body){const s=Pt(fe(t.body,e));if(t.below){const o=Pt(fe(t.below,e));r=new R.MathNode("munderover",[n,o,s])}else r=new R.MathNode("mover",[n,s])}else if(t.below){const s=Pt(fe(t.below,e));r=new R.MathNode("munder",[n,s])}else r=Pt(),r=new R.MathNode("mover",[n,r]);return r}});const Hi=E.makeSpan;function wr(t,e){const n=Re(t.body,e,!0);return Hi([t.mclass],n,e)}function Dr(t,e){let n;const r=Ve(t.body,e);return t.mclass==="minner"?n=new R.MathNode("mpadded",r):t.mclass==="mord"?t.isCharacterBox?(n=r[0],n.type="mi"):n=new R.MathNode("mi",r):(t.isCharacterBox?(n=r[0],n.type="mo"):n=new R.MathNode("mo",r),t.mclass==="mbin"?(n.attributes.lspace="0.22em",n.attributes.rspace="0.22em"):t.mclass==="mpunct"?(n.attributes.lspace="0em",n.attributes.rspace="0.17em"):t.mclass==="mopen"||t.mclass==="mclose"?(n.attributes.lspace="0em",n.attributes.rspace="0em"):t.mclass==="minner"&&(n.attributes.lspace="0.0556em",n.attributes.width="+0.1111em")),n}L({type:"mclass",names:["\\mathord","\\mathbin","\\mathrel","\\mathopen","\\mathclose","\\mathpunct","\\mathinner"],props:{numArgs:1,primitive:!0},handler(t,e){let{parser:n,funcName:r}=t;const s=e[0];return{type:"mclass",mode:n.mode,mclass:"m"+r.slice(5),body:Me(s),isCharacterBox:N.isCharacterBox(s)}},htmlBuilder:wr,mathmlBuilder:Dr});const Ut=t=>{const e=t.type==="ordgroup"&&t.body.length?t.body[0]:t;return e.type==="atom"&&(e.family==="bin"||e.family==="rel")?"m"+e.family:"mord"};L({type:"mclass",names:["\\@binrel"],props:{numArgs:2},handler(t,e){let{parser:n}=t;return{type:"mclass",mode:n.mode,mclass:Ut(e[0]),body:Me(e[1]),isCharacterBox:N.isCharacterBox(e[1])}}}),L({type:"mclass",names:["\\stackrel","\\overset","\\underset"],props:{numArgs:2},handler(t,e){let{parser:n,funcName:r}=t;const s=e[1],o=e[0];let l;r!=="\\stackrel"?l=Ut(s):l="mrel";const d={type:"op",mode:s.mode,limits:!0,alwaysHandleSupSub:!0,parentIsSupSub:!1,symbol:!1,suppressBaseShift:r!=="\\stackrel",body:Me(s)},g={type:"supsub",mode:o.mode,base:d,sup:r==="\\underset"?null:o,sub:r==="\\underset"?o:null};return{type:"mclass",mode:n.mode,mclass:l,body:[g],isCharacterBox:N.isCharacterBox(g)}},htmlBuilder:wr,mathmlBuilder:Dr}),L({type:"pmb",names:["\\pmb"],props:{numArgs:1,allowedInText:!0},handler(t,e){let{parser:n}=t;return{type:"pmb",mode:n.mode,mclass:Ut(e[0]),body:Me(e[0])}},htmlBuilder(t,e){const n=Re(t.body,e,!0),r=E.makeSpan([t.mclass],n,e);return r.style.textShadow="0.02em 0.01em 0.04px",r},mathmlBuilder(t,e){const n=Ve(t.body,e),r=new R.MathNode("mstyle",n);return r.setAttribute("style","text-shadow: 0.02em 0.01em 0.04px"),r}});const qi={">":"\\\\cdrightarrow","<":"\\\\cdleftarrow","=":"\\\\cdlongequal",A:"\\uparrow",V:"\\downarrow","|":"\\Vert",".":"no arrow"},kr=()=>({type:"styling",body:[],mode:"math",style:"display"}),Ar=t=>t.type==="textord"&&t.text==="@",Pi=(t,e)=>(t.type==="mathord"||t.type==="atom")&&t.text===e;function Ui(t,e,n){const r=qi[t];switch(r){case"\\\\cdrightarrow":case"\\\\cdleftarrow":return n.callFunction(r,[e[0]],[e[1]]);case"\\uparrow":case"\\downarrow":{const s=n.callFunction("\\\\cdleft",[e[0]],[]),o={type:"atom",text:r,mode:"math",family:"rel"},l=n.callFunction("\\Big",[o],[]),d=n.callFunction("\\\\cdright",[e[1]],[]),g={type:"ordgroup",mode:"math",body:[s,l,d]};return n.callFunction("\\\\cdparent",[g],[])}case"\\\\cdlongequal":return n.callFunction("\\\\cdlongequal",[],[]);case"\\Vert":{const s={type:"textord",text:"\\Vert",mode:"math"};return n.callFunction("\\Big",[s],[])}default:return{type:"textord",text:" ",mode:"math"}}}function Gi(t){const e=[];for(t.gullet.beginGroup(),t.gullet.macros.set("\\cr","\\\\\\relax"),t.gullet.beginGroup();;){e.push(t.parseExpression(!1,"\\\\")),t.gullet.endGroup(),t.gullet.beginGroup();const o=t.fetch().text;if(o==="&"||o==="\\\\")t.consume();else if(o==="\\end"){e[e.length-1].length===0&&e.pop();break}else throw new f("Expected \\\\ or \\cr or \\end",t.nextToken)}let n=[];const r=[n];for(let o=0;o<e.length;o++){const l=e[o];let d=kr();for(let g=0;g<l.length;g++)if(!Ar(l[g]))d.body.push(l[g]);else{n.push(d),g+=1;const k=bn(l[g]).text,S=new Array(2);if(S[0]={type:"ordgroup",mode:"math",body:[]},S[1]={type:"ordgroup",mode:"math",body:[]},!("=|.".indexOf(k)>-1))if("<>AV".indexOf(k)>-1)for(let _=0;_<2;_++){let P=!0;for(let V=g+1;V<l.length;V++){if(Pi(l[V],k)){P=!1,g=V;break}if(Ar(l[V]))throw new f("Missing a "+k+" character to complete a CD arrow.",l[V]);S[_].body.push(l[V])}if(P)throw new f("Missing a "+k+" character to complete a CD arrow.",l[g])}else throw new f('Expected one of "<>AV=|." after @',l[g]);const T={type:"styling",body:[Ui(k,S,t)],mode:"math",style:"display"};n.push(T),d=kr()}o%2===0?n.push(d):n.shift(),n=[],r.push(n)}t.gullet.endGroup(),t.gullet.endGroup();const s=new Array(r[0].length).fill({type:"align",align:"c",pregap:.25,postgap:.25});return{type:"array",mode:"math",body:r,arraystretch:1,addJot:!0,rowGaps:[null],cols:s,colSeparationType:"CD",hLinesBeforeRow:new Array(r.length+1).fill([])}}L({type:"cdlabel",names:["\\\\cdleft","\\\\cdright"],props:{numArgs:1},handler(t,e){let{parser:n,funcName:r}=t;return{type:"cdlabel",mode:n.mode,side:r.slice(4),label:e[0]}},htmlBuilder(t,e){const n=e.havingStyle(e.style.sup()),r=E.wrapFragment(ae(t.label,n,e),e);return r.classes.push("cd-label-"+t.side),r.style.bottom=I(.8-r.depth),r.height=0,r.depth=0,r},mathmlBuilder(t,e){let n=new R.MathNode("mrow",[fe(t.label,e)]);return n=new R.MathNode("mpadded",[n]),n.setAttribute("width","0"),t.side==="left"&&n.setAttribute("lspace","-1width"),n.setAttribute("voffset","0.7em"),n=new R.MathNode("mstyle",[n]),n.setAttribute("displaystyle","false"),n.setAttribute("scriptlevel","1"),n}}),L({type:"cdlabelparent",names:["\\\\cdparent"],props:{numArgs:1},handler(t,e){let{parser:n}=t;return{type:"cdlabelparent",mode:n.mode,fragment:e[0]}},htmlBuilder(t,e){const n=E.wrapFragment(ae(t.fragment,e),e);return n.classes.push("cd-vert-arrow"),n},mathmlBuilder(t,e){return new R.MathNode("mrow",[fe(t.fragment,e)])}}),L({type:"textord",names:["\\@char"],props:{numArgs:1,allowedInText:!0},handler(t,e){let{parser:n}=t;const s=J(e[0],"ordgroup").body;let o="";for(let g=0;g<s.length;g++){const k=J(s[g],"textord");o+=k.text}let l=parseInt(o),d;if(isNaN(l))throw new f("\\@char has non-numeric argument "+o);if(l<0||l>=1114111)throw new f("\\@char with invalid code point "+o);return l<=65535?d=String.fromCharCode(l):(l-=65536,d=String.fromCharCode((l>>10)+55296,(l&1023)+56320)),{type:"textord",mode:n.mode,text:d}}});const Sr=(t,e)=>{const n=Re(t.body,e.withColor(t.color),!1);return E.makeFragment(n)},vr=(t,e)=>{const n=Ve(t.body,e.withColor(t.color)),r=new R.MathNode("mstyle",n);return r.setAttribute("mathcolor",t.color),r};L({type:"color",names:["\\textcolor"],props:{numArgs:2,allowedInText:!0,argTypes:["color","original"]},handler(t,e){let{parser:n}=t;const r=J(e[0],"color-token").color,s=e[1];return{type:"color",mode:n.mode,color:r,body:Me(s)}},htmlBuilder:Sr,mathmlBuilder:vr}),L({type:"color",names:["\\color"],props:{numArgs:1,allowedInText:!0,argTypes:["color"]},handler(t,e){let{parser:n,breakOnTokenText:r}=t;const s=J(e[0],"color-token").color;n.gullet.macros.set("\\current@color",s);const o=n.parseExpression(!0,r);return{type:"color",mode:n.mode,color:s,body:o}},htmlBuilder:Sr,mathmlBuilder:vr}),L({type:"cr",names:["\\\\"],props:{numArgs:0,numOptionalArgs:0,allowedInText:!0},handler(t,e,n){let{parser:r}=t;const s=r.gullet.future().text==="["?r.parseSizeGroup(!0):null,o=!r.settings.displayMode||!r.settings.useStrictBehavior("newLineInDisplayMode","In LaTeX, \\\\ or \\newline does nothing in display mode");return{type:"cr",mode:r.mode,newLine:o,size:s&&J(s,"size").value}},htmlBuilder(t,e){const n=E.makeSpan(["mspace"],[],e);return t.newLine&&(n.classes.push("newline"),t.size&&(n.style.marginTop=I(de(t.size,e)))),n},mathmlBuilder(t,e){const n=new R.MathNode("mspace");return t.newLine&&(n.setAttribute("linebreak","newline"),t.size&&n.setAttribute("height",I(de(t.size,e)))),n}});const xn={"\\global":"\\global","\\long":"\\\\globallong","\\\\globallong":"\\\\globallong","\\def":"\\gdef","\\gdef":"\\gdef","\\edef":"\\xdef","\\xdef":"\\xdef","\\let":"\\\\globallet","\\futurelet":"\\\\globalfuture"},Fr=t=>{const e=t.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(e))throw new f("Expected a control sequence",t);return e},$i=t=>{let e=t.gullet.popToken();return e.text==="="&&(e=t.gullet.popToken(),e.text===" "&&(e=t.gullet.popToken())),e},Er=(t,e,n,r)=>{let s=t.gullet.macros.get(n.text);s==null&&(n.noexpand=!0,s={tokens:[n],numArgs:0,unexpandable:!t.gullet.isExpandable(n.text)}),t.gullet.macros.set(e,s,r)};L({type:"internal",names:["\\global","\\long","\\\\globallong"],props:{numArgs:0,allowedInText:!0},handler(t){let{parser:e,funcName:n}=t;e.consumeSpaces();const r=e.fetch();if(xn[r.text])return(n==="\\global"||n==="\\\\globallong")&&(r.text=xn[r.text]),J(e.parseFunction(),"internal");throw new f("Invalid token after macro prefix",r)}}),L({type:"internal",names:["\\def","\\gdef","\\edef","\\xdef"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(t){let{parser:e,funcName:n}=t,r=e.gullet.popToken();const s=r.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(s))throw new f("Expected a control sequence",r);let o=0,l;const d=[[]];for(;e.gullet.future().text!=="{";)if(r=e.gullet.popToken(),r.text==="#"){if(e.gullet.future().text==="{"){l=e.gullet.future(),d[o].push("{");break}if(r=e.gullet.popToken(),!/^[1-9]$/.test(r.text))throw new f('Invalid argument number "'+r.text+'"');if(parseInt(r.text)!==o+1)throw new f('Argument number "'+r.text+'" out of order');o++,d.push([])}else{if(r.text==="EOF")throw new f("Expected a macro definition");d[o].push(r.text)}let{tokens:g}=e.gullet.consumeArg();return l&&g.unshift(l),(n==="\\edef"||n==="\\xdef")&&(g=e.gullet.expandTokens(g),g.reverse()),e.gullet.macros.set(s,{tokens:g,numArgs:o,delimiters:d},n===xn[n]),{type:"internal",mode:e.mode}}}),L({type:"internal",names:["\\let","\\\\globallet"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(t){let{parser:e,funcName:n}=t;const r=Fr(e.gullet.popToken());e.gullet.consumeSpaces();const s=$i(e);return Er(e,r,s,n==="\\\\globallet"),{type:"internal",mode:e.mode}}}),L({type:"internal",names:["\\futurelet","\\\\globalfuture"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(t){let{parser:e,funcName:n}=t;const r=Fr(e.gullet.popToken()),s=e.gullet.popToken(),o=e.gullet.popToken();return Er(e,r,o,n==="\\\\globalfuture"),e.gullet.pushToken(o),e.gullet.pushToken(s),{type:"internal",mode:e.mode}}});const dt=function(t,e,n){const r=be.math[t]&&be.math[t].replace,s=d0(r||t,e,n);if(!s)throw new Error("Unsupported symbol "+t+" and font size "+e+".");return s},wn=function(t,e,n,r){const s=n.havingBaseStyle(e),o=E.makeSpan(r.concat(s.sizingClasses(n)),[t],n),l=s.sizeMultiplier/n.sizeMultiplier;return o.height*=l,o.depth*=l,o.maxFontSize=s.sizeMultiplier,o},Tr=function(t,e,n){const r=e.havingBaseStyle(n),s=(1-e.sizeMultiplier/r.sizeMultiplier)*e.fontMetrics().axisHeight;t.classes.push("delimcenter"),t.style.top=I(s),t.height-=s,t.depth+=s},Vi=function(t,e,n,r,s,o){const l=E.makeSymbol(t,"Main-Regular",s,r),d=wn(l,e,r,o);return n&&Tr(d,r,e),d},Wi=function(t,e,n,r){return E.makeSymbol(t,"Size"+e+"-Regular",n,r)},Cr=function(t,e,n,r,s,o){const l=Wi(t,e,s,r),d=wn(E.makeSpan(["delimsizing","size"+e],[l],r),q.TEXT,r,o);return n&&Tr(d,r,q.TEXT),d},Dn=function(t,e,n){let r;return e==="Size1-Regular"?r="delim-size1":r="delim-size4",{type:"elem",elem:E.makeSpan(["delimsizinginner",r],[E.makeSpan([],[E.makeSymbol(t,e,n)])])}},kn=function(t,e,n){const r=Je["Size4-Regular"][t.charCodeAt(0)]?Je["Size4-Regular"][t.charCodeAt(0)][4]:Je["Size1-Regular"][t.charCodeAt(0)][4],s=new p0("inner",W0(t,Math.round(1e3*e))),o=new t0([s],{width:I(r),height:I(e),style:"width:"+I(r),viewBox:"0 0 "+1e3*r+" "+Math.round(1e3*e),preserveAspectRatio:"xMinYMin"}),l=E.makeSvgSpan([],[o],n);return l.height=e,l.style.height=I(e),l.style.width=I(r),{type:"elem",elem:l}},An=.008,Gt={type:"kern",size:-1*An},Yi=["|","\\lvert","\\rvert","\\vert"],Xi=["\\|","\\lVert","\\rVert","\\Vert"],Mr=function(t,e,n,r,s,o){let l,d,g,k,S="",v=0;l=g=k=t,d=null;let T="Size1-Regular";t==="\\uparrow"?g=k="⏐":t==="\\Uparrow"?g=k="‖":t==="\\downarrow"?l=g="⏐":t==="\\Downarrow"?l=g="‖":t==="\\updownarrow"?(l="\\uparrow",g="⏐",k="\\downarrow"):t==="\\Updownarrow"?(l="\\Uparrow",g="‖",k="\\Downarrow"):N.contains(Yi,t)?(g="∣",S="vert",v=333):N.contains(Xi,t)?(g="∥",S="doublevert",v=556):t==="["||t==="\\lbrack"?(l="⎡",g="⎢",k="⎣",T="Size4-Regular",S="lbrack",v=667):t==="]"||t==="\\rbrack"?(l="⎤",g="⎥",k="⎦",T="Size4-Regular",S="rbrack",v=667):t==="\\lfloor"||t==="⌊"?(g=l="⎢",k="⎣",T="Size4-Regular",S="lfloor",v=667):t==="\\lceil"||t==="⌈"?(l="⎡",g=k="⎢",T="Size4-Regular",S="lceil",v=667):t==="\\rfloor"||t==="⌋"?(g=l="⎥",k="⎦",T="Size4-Regular",S="rfloor",v=667):t==="\\rceil"||t==="⌉"?(l="⎤",g=k="⎥",T="Size4-Regular",S="rceil",v=667):t==="("||t==="\\lparen"?(l="⎛",g="⎜",k="⎝",T="Size4-Regular",S="lparen",v=875):t===")"||t==="\\rparen"?(l="⎞",g="⎟",k="⎠",T="Size4-Regular",S="rparen",v=875):t==="\\{"||t==="\\lbrace"?(l="⎧",d="⎨",k="⎩",g="⎪",T="Size4-Regular"):t==="\\}"||t==="\\rbrace"?(l="⎫",d="⎬",k="⎭",g="⎪",T="Size4-Regular"):t==="\\lgroup"||t==="⟮"?(l="⎧",k="⎩",g="⎪",T="Size4-Regular"):t==="\\rgroup"||t==="⟯"?(l="⎫",k="⎭",g="⎪",T="Size4-Regular"):t==="\\lmoustache"||t==="⎰"?(l="⎧",k="⎭",g="⎪",T="Size4-Regular"):(t==="\\rmoustache"||t==="⎱")&&(l="⎫",k="⎩",g="⎪",T="Size4-Regular");const _=dt(l,T,s),P=_.height+_.depth,V=dt(g,T,s),re=V.height+V.depth,ne=dt(k,T,s),se=ne.height+ne.depth;let le=0,ye=1;if(d!==null){const Fe=dt(d,T,s);le=Fe.height+Fe.depth,ye=2}const Pe=P+se+le,Ie=Math.max(0,Math.ceil((e-Pe)/(ye*re))),i0=Pe+Ie*ye*re;let et=r.fontMetrics().axisHeight;n&&(et*=r.sizeMultiplier);const oe=i0/2-et,ue=[];if(S.length>0){const Fe=i0-P-se,Se=Math.round(i0*1e3),a0=Et(S,Math.round(Fe*1e3)),wa=new p0(S,a0),ws=(v/1e3).toFixed(3)+"em",Ds=(Se/1e3).toFixed(3)+"em",Da=new t0([wa],{width:ws,height:Ds,viewBox:"0 0 "+v+" "+Se}),Xt=E.makeSvgSpan([],[Da],r);Xt.height=Se/1e3,Xt.style.width=ws,Xt.style.height=Ds,ue.push({type:"elem",elem:Xt})}else{if(ue.push(Dn(k,T,s)),ue.push(Gt),d===null){const Fe=i0-P-se+2*An;ue.push(kn(g,Fe,r))}else{const Fe=(i0-P-se-le)/2+2*An;ue.push(kn(g,Fe,r)),ue.push(Gt),ue.push(Dn(d,T,s)),ue.push(Gt),ue.push(kn(g,Fe,r))}ue.push(Gt),ue.push(Dn(l,T,s))}const we=r.havingBaseStyle(q.TEXT),De=E.makeVList({positionType:"bottom",positionData:oe,children:ue},we);return wn(E.makeSpan(["delimsizing","mult"],[De],we),q.TEXT,r,o)},Sn=80,vn=.08,Fn=function(t,e,n,r,s){const o=T0(t,r,n),l=new p0(t,o),d=new t0([l],{width:"400em",height:I(e),viewBox:"0 0 400000 "+n,preserveAspectRatio:"xMinYMin slice"});return E.makeSvgSpan(["hide-tail"],[d],s)},ji=function(t,e){const n=e.havingBaseSizing(),r=Nr("\\surd",t*n.sizeMultiplier,_r,n);let s=n.sizeMultiplier;const o=Math.max(0,e.minRuleThickness-e.fontMetrics().sqrtRuleThickness);let l,d=0,g=0,k=0,S;return r.type==="small"?(k=1e3+1e3*o+Sn,t<1?s=1:t<1.4&&(s=.7),d=(1+o+vn)/s,g=(1+o)/s,l=Fn("sqrtMain",d,k,o,e),l.style.minWidth="0.853em",S=.833/s):r.type==="large"?(k=(1e3+Sn)*pt[r.size],g=(pt[r.size]+o)/s,d=(pt[r.size]+o+vn)/s,l=Fn("sqrtSize"+r.size,d,k,o,e),l.style.minWidth="1.02em",S=1/s):(d=t+o+vn,g=t+o,k=Math.floor(1e3*t+o)+Sn,l=Fn("sqrtTall",d,k,o,e),l.style.minWidth="0.742em",S=1.056),l.height=g,l.style.height=I(d),{span:l,advanceWidth:S,ruleWidth:(e.fontMetrics().sqrtRuleThickness+o)*s}},zr=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","\\surd"],Zi=["\\uparrow","\\downarrow","\\updownarrow","\\Uparrow","\\Downarrow","\\Updownarrow","|","\\|","\\vert","\\Vert","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱"],Br=["<",">","\\langle","\\rangle","/","\\backslash","\\lt","\\gt"],pt=[0,1.2,1.8,2.4,3],Ki=function(t,e,n,r,s){if(t==="<"||t==="\\lt"||t==="⟨"?t="\\langle":(t===">"||t==="\\gt"||t==="⟩")&&(t="\\rangle"),N.contains(zr,t)||N.contains(Br,t))return Cr(t,e,!1,n,r,s);if(N.contains(Zi,t))return Mr(t,pt[e],!1,n,r,s);throw new f("Illegal delimiter: '"+t+"'")},Qi=[{type:"small",style:q.SCRIPTSCRIPT},{type:"small",style:q.SCRIPT},{type:"small",style:q.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4}],Ji=[{type:"small",style:q.SCRIPTSCRIPT},{type:"small",style:q.SCRIPT},{type:"small",style:q.TEXT},{type:"stack"}],_r=[{type:"small",style:q.SCRIPTSCRIPT},{type:"small",style:q.SCRIPT},{type:"small",style:q.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4},{type:"stack"}],ea=function(t){if(t.type==="small")return"Main-Regular";if(t.type==="large")return"Size"+t.size+"-Regular";if(t.type==="stack")return"Size4-Regular";throw new Error("Add support for delim type '"+t.type+"' here.")},Nr=function(t,e,n,r){const s=Math.min(2,3-r.style.size);for(let o=s;o<n.length&&n[o].type!=="stack";o++){const l=dt(t,ea(n[o]),"math");let d=l.height+l.depth;if(n[o].type==="small"){const g=r.havingBaseStyle(n[o].style);d*=g.sizeMultiplier}if(d>e)return n[o]}return n[n.length-1]},Rr=function(t,e,n,r,s,o){t==="<"||t==="\\lt"||t==="⟨"?t="\\langle":(t===">"||t==="\\gt"||t==="⟩")&&(t="\\rangle");let l;N.contains(Br,t)?l=Qi:N.contains(zr,t)?l=_r:l=Ji;const d=Nr(t,e,l,r);return d.type==="small"?Vi(t,d.style,n,r,s,o):d.type==="large"?Cr(t,d.size,n,r,s,o):Mr(t,e,n,r,s,o)};var F0={sqrtImage:ji,sizedDelim:Ki,sizeToMaxHeight:pt,customSizedDelim:Rr,leftRightDelim:function(t,e,n,r,s,o){const l=r.fontMetrics().axisHeight*r.sizeMultiplier,d=901,g=5/r.fontMetrics().ptPerEm,k=Math.max(e-l,n+l),S=Math.max(k/500*d,2*k-g);return Rr(t,S,!0,r,s,o)}};const Ir={"\\bigl":{mclass:"mopen",size:1},"\\Bigl":{mclass:"mopen",size:2},"\\biggl":{mclass:"mopen",size:3},"\\Biggl":{mclass:"mopen",size:4},"\\bigr":{mclass:"mclose",size:1},"\\Bigr":{mclass:"mclose",size:2},"\\biggr":{mclass:"mclose",size:3},"\\Biggr":{mclass:"mclose",size:4},"\\bigm":{mclass:"mrel",size:1},"\\Bigm":{mclass:"mrel",size:2},"\\biggm":{mclass:"mrel",size:3},"\\Biggm":{mclass:"mrel",size:4},"\\big":{mclass:"mord",size:1},"\\Big":{mclass:"mord",size:2},"\\bigg":{mclass:"mord",size:3},"\\Bigg":{mclass:"mord",size:4}},ta=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","<",">","\\langle","⟨","\\rangle","⟩","\\lt","\\gt","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱","/","\\backslash","|","\\vert","\\|","\\Vert","\\uparrow","\\Uparrow","\\downarrow","\\Downarrow","\\updownarrow","\\Updownarrow","."];function $t(t,e){const n=qt(t);if(n&&N.contains(ta,n.text))return n;throw n?new f("Invalid delimiter '"+n.text+"' after '"+e.funcName+"'",t):new f("Invalid delimiter type '"+t.type+"'",t)}L({type:"delimsizing",names:["\\bigl","\\Bigl","\\biggl","\\Biggl","\\bigr","\\Bigr","\\biggr","\\Biggr","\\bigm","\\Bigm","\\biggm","\\Biggm","\\big","\\Big","\\bigg","\\Bigg"],props:{numArgs:1,argTypes:["primitive"]},handler:(t,e)=>{const n=$t(e[0],t);return{type:"delimsizing",mode:t.parser.mode,size:Ir[t.funcName].size,mclass:Ir[t.funcName].mclass,delim:n.text}},htmlBuilder:(t,e)=>t.delim==="."?E.makeSpan([t.mclass]):F0.sizedDelim(t.delim,t.size,e,t.mode,[t.mclass]),mathmlBuilder:t=>{const e=[];t.delim!=="."&&e.push(r0(t.delim,t.mode));const n=new R.MathNode("mo",e);t.mclass==="mopen"||t.mclass==="mclose"?n.setAttribute("fence","true"):n.setAttribute("fence","false"),n.setAttribute("stretchy","true");const r=I(F0.sizeToMaxHeight[t.size]);return n.setAttribute("minsize",r),n.setAttribute("maxsize",r),n}});function Or(t){if(!t.body)throw new Error("Bug: The leftright ParseNode wasn't fully parsed.")}L({type:"leftright-right",names:["\\right"],props:{numArgs:1,primitive:!0},handler:(t,e)=>{const n=t.parser.gullet.macros.get("\\current@color");if(n&&typeof n!="string")throw new f("\\current@color set to non-string in \\right");return{type:"leftright-right",mode:t.parser.mode,delim:$t(e[0],t).text,color:n}}}),L({type:"leftright",names:["\\left"],props:{numArgs:1,primitive:!0},handler:(t,e)=>{const n=$t(e[0],t),r=t.parser;++r.leftrightDepth;const s=r.parseExpression(!1);--r.leftrightDepth,r.expect("\\right",!1);const o=J(r.parseFunction(),"leftright-right");return{type:"leftright",mode:r.mode,body:s,left:n.text,right:o.delim,rightColor:o.color}},htmlBuilder:(t,e)=>{Or(t);const n=Re(t.body,e,!0,["mopen","mclose"]);let r=0,s=0,o=!1;for(let g=0;g<n.length;g++)n[g].isMiddle?o=!0:(r=Math.max(n[g].height,r),s=Math.max(n[g].depth,s));r*=e.sizeMultiplier,s*=e.sizeMultiplier;let l;if(t.left==="."?l=ht(e,["mopen"]):l=F0.leftRightDelim(t.left,r,s,e,t.mode,["mopen"]),n.unshift(l),o)for(let g=1;g<n.length;g++){const S=n[g].isMiddle;S&&(n[g]=F0.leftRightDelim(S.delim,r,s,S.options,t.mode,[]))}let d;if(t.right===".")d=ht(e,["mclose"]);else{const g=t.rightColor?e.withColor(t.rightColor):e;d=F0.leftRightDelim(t.right,r,s,g,t.mode,["mclose"])}return n.push(d),E.makeSpan(["minner"],n,e)},mathmlBuilder:(t,e)=>{Or(t);const n=Ve(t.body,e);if(t.left!=="."){const r=new R.MathNode("mo",[r0(t.left,t.mode)]);r.setAttribute("fence","true"),n.unshift(r)}if(t.right!=="."){const r=new R.MathNode("mo",[r0(t.right,t.mode)]);r.setAttribute("fence","true"),t.rightColor&&r.setAttribute("mathcolor",t.rightColor),n.push(r)}return fn(n)}}),L({type:"middle",names:["\\middle"],props:{numArgs:1,primitive:!0},handler:(t,e)=>{const n=$t(e[0],t);if(!t.parser.leftrightDepth)throw new f("\\middle without preceding \\left",n);return{type:"middle",mode:t.parser.mode,delim:n.text}},htmlBuilder:(t,e)=>{let n;if(t.delim===".")n=ht(e,[]);else{n=F0.sizedDelim(t.delim,1,e,t.mode,[]);const r={delim:t.delim,options:e};n.isMiddle=r}return n},mathmlBuilder:(t,e)=>{const n=t.delim==="\\vert"||t.delim==="|"?r0("|","text"):r0(t.delim,t.mode),r=new R.MathNode("mo",[n]);return r.setAttribute("fence","true"),r.setAttribute("lspace","0.05em"),r.setAttribute("rspace","0.05em"),r}});const En=(t,e)=>{const n=E.wrapFragment(ae(t.body,e),e),r=t.label.slice(1);let s=e.sizeMultiplier,o,l=0;const d=N.isCharacterBox(t.body);if(r==="sout")o=E.makeSpan(["stretchy","sout"]),o.height=e.fontMetrics().defaultRuleThickness/s,l=-.5*e.fontMetrics().xHeight;else if(r==="phase"){const k=de({number:.6,unit:"pt"},e),S=de({number:.35,unit:"ex"},e),v=e.havingBaseSizing();s=s/v.sizeMultiplier;const T=n.height+n.depth+k+S;n.style.paddingLeft=I(T/2+k);const _=Math.floor(1e3*T*s),P=rt(_),V=new t0([new p0("phase",P)],{width:"400em",height:I(_/1e3),viewBox:"0 0 400000 "+_,preserveAspectRatio:"xMinYMin slice"});o=E.makeSvgSpan(["hide-tail"],[V],e),o.style.height=I(T),l=n.depth+k+S}else{/cancel/.test(r)?d||n.classes.push("cancel-pad"):r==="angl"?n.classes.push("anglpad"):n.classes.push("boxpad");let k=0,S=0,v=0;/box/.test(r)?(v=Math.max(e.fontMetrics().fboxrule,e.minRuleThickness),k=e.fontMetrics().fboxsep+(r==="colorbox"?0:v),S=k):r==="angl"?(v=Math.max(e.fontMetrics().defaultRuleThickness,e.minRuleThickness),k=4*v,S=Math.max(0,.25-n.depth)):(k=d?.2:0,S=k),o=v0.encloseSpan(n,r,k,S,e),/fbox|boxed|fcolorbox/.test(r)?(o.style.borderStyle="solid",o.style.borderWidth=I(v)):r==="angl"&&v!==.049&&(o.style.borderTopWidth=I(v),o.style.borderRightWidth=I(v)),l=n.depth+S,t.backgroundColor&&(o.style.backgroundColor=t.backgroundColor,t.borderColor&&(o.style.borderColor=t.borderColor))}let g;if(t.backgroundColor)g=E.makeVList({positionType:"individualShift",children:[{type:"elem",elem:o,shift:l},{type:"elem",elem:n,shift:0}]},e);else{const k=/cancel|phase/.test(r)?["svg-align"]:[];g=E.makeVList({positionType:"individualShift",children:[{type:"elem",elem:n,shift:0},{type:"elem",elem:o,shift:l,wrapperClasses:k}]},e)}return/cancel/.test(r)&&(g.height=n.height,g.depth=n.depth),/cancel/.test(r)&&!d?E.makeSpan(["mord","cancel-lap"],[g],e):E.makeSpan(["mord"],[g],e)},Tn=(t,e)=>{let n=0;const r=new R.MathNode(t.label.indexOf("colorbox")>-1?"mpadded":"menclose",[fe(t.body,e)]);switch(t.label){case"\\cancel":r.setAttribute("notation","updiagonalstrike");break;case"\\bcancel":r.setAttribute("notation","downdiagonalstrike");break;case"\\phase":r.setAttribute("notation","phasorangle");break;case"\\sout":r.setAttribute("notation","horizontalstrike");break;case"\\fbox":r.setAttribute("notation","box");break;case"\\angl":r.setAttribute("notation","actuarial");break;case"\\fcolorbox":case"\\colorbox":if(n=e.fontMetrics().fboxsep*e.fontMetrics().ptPerEm,r.setAttribute("width","+"+2*n+"pt"),r.setAttribute("height","+"+2*n+"pt"),r.setAttribute("lspace",n+"pt"),r.setAttribute("voffset",n+"pt"),t.label==="\\fcolorbox"){const s=Math.max(e.fontMetrics().fboxrule,e.minRuleThickness);r.setAttribute("style","border: "+s+"em solid "+String(t.borderColor))}break;case"\\xcancel":r.setAttribute("notation","updiagonalstrike downdiagonalstrike");break}return t.backgroundColor&&r.setAttribute("mathbackground",t.backgroundColor),r};L({type:"enclose",names:["\\colorbox"],props:{numArgs:2,allowedInText:!0,argTypes:["color","text"]},handler(t,e,n){let{parser:r,funcName:s}=t;const o=J(e[0],"color-token").color,l=e[1];return{type:"enclose",mode:r.mode,label:s,backgroundColor:o,body:l}},htmlBuilder:En,mathmlBuilder:Tn}),L({type:"enclose",names:["\\fcolorbox"],props:{numArgs:3,allowedInText:!0,argTypes:["color","color","text"]},handler(t,e,n){let{parser:r,funcName:s}=t;const o=J(e[0],"color-token").color,l=J(e[1],"color-token").color,d=e[2];return{type:"enclose",mode:r.mode,label:s,backgroundColor:l,borderColor:o,body:d}},htmlBuilder:En,mathmlBuilder:Tn}),L({type:"enclose",names:["\\fbox"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!0},handler(t,e){let{parser:n}=t;return{type:"enclose",mode:n.mode,label:"\\fbox",body:e[0]}}}),L({type:"enclose",names:["\\cancel","\\bcancel","\\xcancel","\\sout","\\phase"],props:{numArgs:1},handler(t,e){let{parser:n,funcName:r}=t;const s=e[0];return{type:"enclose",mode:n.mode,label:r,body:s}},htmlBuilder:En,mathmlBuilder:Tn}),L({type:"enclose",names:["\\angl"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!1},handler(t,e){let{parser:n}=t;return{type:"enclose",mode:n.mode,label:"\\angl",body:e[0]}}});const Lr={};function f0(t){let{type:e,names:n,props:r,handler:s,htmlBuilder:o,mathmlBuilder:l}=t;const d={type:e,numArgs:r.numArgs||0,allowedInText:!1,numOptionalArgs:0,handler:s};for(let g=0;g<n.length;++g)Lr[n[g]]=d;o&&(It[e]=o),l&&(Ot[e]=l)}const Hr={};function p(t,e){Hr[t]=e}class je{constructor(e,n,r){this.lexer=void 0,this.start=void 0,this.end=void 0,this.lexer=e,this.start=n,this.end=r}static range(e,n){return n?!e||!e.loc||!n.loc||e.loc.lexer!==n.loc.lexer?null:new je(e.loc.lexer,e.loc.start,n.loc.end):e&&e.loc}}class s0{constructor(e,n){this.text=void 0,this.loc=void 0,this.noexpand=void 0,this.treatAsRelax=void 0,this.text=e,this.loc=n}range(e,n){return new s0(n,je.range(this,e))}}function qr(t){const e=[];t.consumeSpaces();let n=t.fetch().text;for(n==="\\relax"&&(t.consume(),t.consumeSpaces(),n=t.fetch().text);n==="\\hline"||n==="\\hdashline";)t.consume(),e.push(n==="\\hdashline"),t.consumeSpaces(),n=t.fetch().text;return e}const Vt=t=>{if(!t.parser.settings.displayMode)throw new f("{"+t.envName+"} can be used only in display mode.")};function Cn(t){if(t.indexOf("ed")===-1)return t.indexOf("*")===-1}function I0(t,e,n){let{hskipBeforeAndAfter:r,addJot:s,cols:o,arraystretch:l,colSeparationType:d,autoTag:g,singleRow:k,emptySingleRow:S,maxNumCols:v,leqno:T}=e;if(t.gullet.beginGroup(),k||t.gullet.macros.set("\\cr","\\\\\\relax"),!l){const ye=t.gullet.expandMacroAsText("\\arraystretch");if(ye==null)l=1;else if(l=parseFloat(ye),!l||l<0)throw new f("Invalid \\arraystretch: "+ye)}t.gullet.beginGroup();let _=[];const P=[_],V=[],re=[],ne=g!=null?[]:void 0;function se(){g&&t.gullet.macros.set("\\@eqnsw","1",!0)}function le(){ne&&(t.gullet.macros.get("\\df@tag")?(ne.push(t.subparse([new s0("\\df@tag")])),t.gullet.macros.set("\\df@tag",void 0,!0)):ne.push(!!g&&t.gullet.macros.get("\\@eqnsw")==="1"))}for(se(),re.push(qr(t));;){let ye=t.parseExpression(!1,k?"\\end":"\\\\");t.gullet.endGroup(),t.gullet.beginGroup(),ye={type:"ordgroup",mode:t.mode,body:ye},n&&(ye={type:"styling",mode:t.mode,style:n,body:[ye]}),_.push(ye);const Pe=t.fetch().text;if(Pe==="&"){if(v&&_.length===v){if(k||d)throw new f("Too many tab characters: &",t.nextToken);t.settings.reportNonstrict("textEnv","Too few columns specified in the {array} column argument.")}t.consume()}else if(Pe==="\\end"){le(),_.length===1&&ye.type==="styling"&&ye.body[0].body.length===0&&(P.length>1||!S)&&P.pop(),re.length<P.length+1&&re.push([]);break}else if(Pe==="\\\\"){t.consume();let Ie;t.gullet.future().text!==" "&&(Ie=t.parseSizeGroup(!0)),V.push(Ie?Ie.value:null),le(),re.push(qr(t)),_=[],P.push(_),se()}else throw new f("Expected & or \\\\ or \\cr or \\end",t.nextToken)}return t.gullet.endGroup(),t.gullet.endGroup(),{type:"array",mode:t.mode,addJot:s,arraystretch:l,body:P,cols:o,rowGaps:V,hskipBeforeAndAfter:r,hLinesBeforeRow:re,colSeparationType:d,tags:ne,leqno:T}}function Mn(t){return t.slice(0,1)==="d"?"display":"text"}const g0=function(t,e){let n,r;const s=t.body.length,o=t.hLinesBeforeRow;let l=0,d=new Array(s);const g=[],k=Math.max(e.fontMetrics().arrayRuleWidth,e.minRuleThickness),S=1/e.fontMetrics().ptPerEm;let v=5*S;t.colSeparationType&&t.colSeparationType==="small"&&(v=.2778*(e.havingStyle(q.SCRIPT).sizeMultiplier/e.sizeMultiplier));const T=t.colSeparationType==="CD"?de({number:3,unit:"ex"},e):12*S,_=3*S,P=t.arraystretch*T,V=.7*P,re=.3*P;let ne=0;function se(oe){for(let ue=0;ue<oe.length;++ue)ue>0&&(ne+=.25),g.push({pos:ne,isDashed:oe[ue]})}for(se(o[0]),n=0;n<t.body.length;++n){const oe=t.body[n];let ue=V,we=re;l<oe.length&&(l=oe.length);const De=new Array(oe.length);for(r=0;r<oe.length;++r){const a0=ae(oe[r],e);we<a0.depth&&(we=a0.depth),ue<a0.height&&(ue=a0.height),De[r]=a0}const Fe=t.rowGaps[n];let Se=0;Fe&&(Se=de(Fe,e),Se>0&&(Se+=re,we<Se&&(we=Se),Se=0)),t.addJot&&(we+=_),De.height=ue,De.depth=we,ne+=ue,De.pos=ne,ne+=we+Se,d[n]=De,se(o[n+1])}const le=ne/2+e.fontMetrics().axisHeight,ye=t.cols||[],Pe=[];let Ie,i0;const et=[];if(t.tags&&t.tags.some(oe=>oe))for(n=0;n<s;++n){const oe=d[n],ue=oe.pos-le,we=t.tags[n];let De;we===!0?De=E.makeSpan(["eqn-num"],[],e):we===!1?De=E.makeSpan([],[],e):De=E.makeSpan([],Re(we,e,!0),e),De.depth=oe.depth,De.height=oe.height,et.push({type:"elem",elem:De,shift:ue})}for(r=0,i0=0;r<l||i0<ye.length;++r,++i0){let oe=ye[i0]||{},ue=!0;for(;oe.type==="separator";){if(ue||(Ie=E.makeSpan(["arraycolsep"],[]),Ie.style.width=I(e.fontMetrics().doubleRuleSep),Pe.push(Ie)),oe.separator==="|"||oe.separator===":"){const Fe=oe.separator==="|"?"solid":"dashed",Se=E.makeSpan(["vertical-separator"],[],e);Se.style.height=I(ne),Se.style.borderRightWidth=I(k),Se.style.borderRightStyle=Fe,Se.style.margin="0 "+I(-k/2);const a0=ne-le;a0&&(Se.style.verticalAlign=I(-a0)),Pe.push(Se)}else throw new f("Invalid separator type: "+oe.separator);i0++,oe=ye[i0]||{},ue=!1}if(r>=l)continue;let we;(r>0||t.hskipBeforeAndAfter)&&(we=N.deflt(oe.pregap,v),we!==0&&(Ie=E.makeSpan(["arraycolsep"],[]),Ie.style.width=I(we),Pe.push(Ie)));let De=[];for(n=0;n<s;++n){const Fe=d[n],Se=Fe[r];if(!Se)continue;const a0=Fe.pos-le;Se.depth=Fe.depth,Se.height=Fe.height,De.push({type:"elem",elem:Se,shift:a0})}De=E.makeVList({positionType:"individualShift",children:De},e),De=E.makeSpan(["col-align-"+(oe.align||"c")],[De]),Pe.push(De),(r<l-1||t.hskipBeforeAndAfter)&&(we=N.deflt(oe.postgap,v),we!==0&&(Ie=E.makeSpan(["arraycolsep"],[]),Ie.style.width=I(we),Pe.push(Ie)))}if(d=E.makeSpan(["mtable"],Pe),g.length>0){const oe=E.makeLineSpan("hline",e,k),ue=E.makeLineSpan("hdashline",e,k),we=[{type:"elem",elem:d,shift:0}];for(;g.length>0;){const De=g.pop(),Fe=De.pos-le;De.isDashed?we.push({type:"elem",elem:ue,shift:Fe}):we.push({type:"elem",elem:oe,shift:Fe})}d=E.makeVList({positionType:"individualShift",children:we},e)}if(et.length===0)return E.makeSpan(["mord"],[d],e);{let oe=E.makeVList({positionType:"individualShift",children:et},e);return oe=E.makeSpan(["tag"],[oe],e),E.makeFragment([d,oe])}},na={c:"center ",l:"left ",r:"right "},b0=function(t,e){const n=[],r=new R.MathNode("mtd",[],["mtr-glue"]),s=new R.MathNode("mtd",[],["mml-eqn-num"]);for(let v=0;v<t.body.length;v++){const T=t.body[v],_=[];for(let P=0;P<T.length;P++)_.push(new R.MathNode("mtd",[fe(T[P],e)]));t.tags&&t.tags[v]&&(_.unshift(r),_.push(r),t.leqno?_.unshift(s):_.push(s)),n.push(new R.MathNode("mtr",_))}let o=new R.MathNode("mtable",n);const l=t.arraystretch===.5?.1:.16+t.arraystretch-1+(t.addJot?.09:0);o.setAttribute("rowspacing",I(l));let d="",g="";if(t.cols&&t.cols.length>0){const v=t.cols;let T="",_=!1,P=0,V=v.length;v[0].type==="separator"&&(d+="top ",P=1),v[v.length-1].type==="separator"&&(d+="bottom ",V-=1);for(let re=P;re<V;re++)v[re].type==="align"?(g+=na[v[re].align],_&&(T+="none "),_=!0):v[re].type==="separator"&&_&&(T+=v[re].separator==="|"?"solid ":"dashed ",_=!1);o.setAttribute("columnalign",g.trim()),/[sd]/.test(T)&&o.setAttribute("columnlines",T.trim())}if(t.colSeparationType==="align"){const v=t.cols||[];let T="";for(let _=1;_<v.length;_++)T+=_%2?"0em ":"1em ";o.setAttribute("columnspacing",T.trim())}else t.colSeparationType==="alignat"||t.colSeparationType==="gather"?o.setAttribute("columnspacing","0em"):t.colSeparationType==="small"?o.setAttribute("columnspacing","0.2778em"):t.colSeparationType==="CD"?o.setAttribute("columnspacing","0.5em"):o.setAttribute("columnspacing","1em");let k="";const S=t.hLinesBeforeRow;d+=S[0].length>0?"left ":"",d+=S[S.length-1].length>0?"right ":"";for(let v=1;v<S.length-1;v++)k+=S[v].length===0?"none ":S[v][0]?"dashed ":"solid ";return/[sd]/.test(k)&&o.setAttribute("rowlines",k.trim()),d!==""&&(o=new R.MathNode("menclose",[o]),o.setAttribute("notation",d.trim())),t.arraystretch&&t.arraystretch<1&&(o=new R.MathNode("mstyle",[o]),o.setAttribute("scriptlevel","1")),o},Pr=function(t,e){t.envName.indexOf("ed")===-1&&Vt(t);const n=[],r=t.envName.indexOf("at")>-1?"alignat":"align",s=t.envName==="split",o=I0(t.parser,{cols:n,addJot:!0,autoTag:s?void 0:Cn(t.envName),emptySingleRow:!0,colSeparationType:r,maxNumCols:s?2:void 0,leqno:t.parser.settings.leqno},"display");let l,d=0;const g={type:"ordgroup",mode:t.mode,body:[]};if(e[0]&&e[0].type==="ordgroup"){let S="";for(let v=0;v<e[0].body.length;v++){const T=J(e[0].body[v],"textord");S+=T.text}l=Number(S),d=l*2}const k=!d;o.body.forEach(function(S){for(let v=1;v<S.length;v+=2){const T=J(S[v],"styling");J(T.body[0],"ordgroup").body.unshift(g)}if(k)d<S.length&&(d=S.length);else{const v=S.length/2;if(l<v)throw new f("Too many math in a row: "+("expected "+l+", but got "+v),S[0])}});for(let S=0;S<d;++S){let v="r",T=0;S%2===1?v="l":S>0&&k&&(T=1),n[S]={type:"align",align:v,pregap:T,postgap:0}}return o.colSeparationType=k?"align":"alignat",o};f0({type:"array",names:["array","darray"],props:{numArgs:1},handler(t,e){const s=(qt(e[0])?[e[0]]:J(e[0],"ordgroup").body).map(function(l){const g=bn(l).text;if("lcr".indexOf(g)!==-1)return{type:"align",align:g};if(g==="|")return{type:"separator",separator:"|"};if(g===":")return{type:"separator",separator:":"};throw new f("Unknown column alignment: "+g,l)}),o={cols:s,hskipBeforeAndAfter:!0,maxNumCols:s.length};return I0(t.parser,o,Mn(t.envName))},htmlBuilder:g0,mathmlBuilder:b0}),f0({type:"array",names:["matrix","pmatrix","bmatrix","Bmatrix","vmatrix","Vmatrix","matrix*","pmatrix*","bmatrix*","Bmatrix*","vmatrix*","Vmatrix*"],props:{numArgs:0},handler(t){const e={matrix:null,pmatrix:["(",")"],bmatrix:["[","]"],Bmatrix:["\\{","\\}"],vmatrix:["|","|"],Vmatrix:["\\Vert","\\Vert"]}[t.envName.replace("*","")];let n="c";const r={hskipBeforeAndAfter:!1,cols:[{type:"align",align:n}]};if(t.envName.charAt(t.envName.length-1)==="*"){const l=t.parser;if(l.consumeSpaces(),l.fetch().text==="["){if(l.consume(),l.consumeSpaces(),n=l.fetch().text,"lcr".indexOf(n)===-1)throw new f("Expected l or c or r",l.nextToken);l.consume(),l.consumeSpaces(),l.expect("]"),l.consume(),r.cols=[{type:"align",align:n}]}}const s=I0(t.parser,r,Mn(t.envName)),o=Math.max(0,...s.body.map(l=>l.length));return s.cols=new Array(o).fill({type:"align",align:n}),e?{type:"leftright",mode:t.mode,body:[s],left:e[0],right:e[1],rightColor:void 0}:s},htmlBuilder:g0,mathmlBuilder:b0}),f0({type:"array",names:["smallmatrix"],props:{numArgs:0},handler(t){const e={arraystretch:.5},n=I0(t.parser,e,"script");return n.colSeparationType="small",n},htmlBuilder:g0,mathmlBuilder:b0}),f0({type:"array",names:["subarray"],props:{numArgs:1},handler(t,e){const s=(qt(e[0])?[e[0]]:J(e[0],"ordgroup").body).map(function(l){const g=bn(l).text;if("lc".indexOf(g)!==-1)return{type:"align",align:g};throw new f("Unknown column alignment: "+g,l)});if(s.length>1)throw new f("{subarray} can contain only one column");let o={cols:s,hskipBeforeAndAfter:!1,arraystretch:.5};if(o=I0(t.parser,o,"script"),o.body.length>0&&o.body[0].length>1)throw new f("{subarray} can contain only one column");return o},htmlBuilder:g0,mathmlBuilder:b0}),f0({type:"array",names:["cases","dcases","rcases","drcases"],props:{numArgs:0},handler(t){const e={arraystretch:1.2,cols:[{type:"align",align:"l",pregap:0,postgap:1},{type:"align",align:"l",pregap:0,postgap:0}]},n=I0(t.parser,e,Mn(t.envName));return{type:"leftright",mode:t.mode,body:[n],left:t.envName.indexOf("r")>-1?".":"\\{",right:t.envName.indexOf("r")>-1?"\\}":".",rightColor:void 0}},htmlBuilder:g0,mathmlBuilder:b0}),f0({type:"array",names:["align","align*","aligned","split"],props:{numArgs:0},handler:Pr,htmlBuilder:g0,mathmlBuilder:b0}),f0({type:"array",names:["gathered","gather","gather*"],props:{numArgs:0},handler(t){N.contains(["gather","gather*"],t.envName)&&Vt(t);const e={cols:[{type:"align",align:"c"}],addJot:!0,colSeparationType:"gather",autoTag:Cn(t.envName),emptySingleRow:!0,leqno:t.parser.settings.leqno};return I0(t.parser,e,"display")},htmlBuilder:g0,mathmlBuilder:b0}),f0({type:"array",names:["alignat","alignat*","alignedat"],props:{numArgs:1},handler:Pr,htmlBuilder:g0,mathmlBuilder:b0}),f0({type:"array",names:["equation","equation*"],props:{numArgs:0},handler(t){Vt(t);const e={autoTag:Cn(t.envName),emptySingleRow:!0,singleRow:!0,maxNumCols:1,leqno:t.parser.settings.leqno};return I0(t.parser,e,"display")},htmlBuilder:g0,mathmlBuilder:b0}),f0({type:"array",names:["CD"],props:{numArgs:0},handler(t){return Vt(t),Gi(t.parser)},htmlBuilder:g0,mathmlBuilder:b0}),p("\\nonumber","\\gdef\\@eqnsw{0}"),p("\\notag","\\nonumber"),L({type:"text",names:["\\hline","\\hdashline"],props:{numArgs:0,allowedInText:!0,allowedInMath:!0},handler(t,e){throw new f(t.funcName+" valid only within array environment")}});var Ur=Lr;L({type:"environment",names:["\\begin","\\end"],props:{numArgs:1,argTypes:["text"]},handler(t,e){let{parser:n,funcName:r}=t;const s=e[0];if(s.type!=="ordgroup")throw new f("Invalid environment name",s);let o="";for(let l=0;l<s.body.length;++l)o+=J(s.body[l],"textord").text;if(r==="\\begin"){if(!Ur.hasOwnProperty(o))throw new f("No such environment: "+o,s);const l=Ur[o],{args:d,optArgs:g}=n.parseArguments("\\begin{"+o+"}",l),k={mode:n.mode,envName:o,parser:n},S=l.handler(k,d,g);n.expect("\\end",!1);const v=n.nextToken,T=J(n.parseFunction(),"environment");if(T.name!==o)throw new f("Mismatch: \\begin{"+o+"} matched by \\end{"+T.name+"}",v);return S}return{type:"environment",mode:n.mode,name:o,nameGroup:s}}});const Gr=(t,e)=>{const n=t.font,r=e.withFont(n);return ae(t.body,r)},$r=(t,e)=>{const n=t.font,r=e.withFont(n);return fe(t.body,r)},Vr={"\\Bbb":"\\mathbb","\\bold":"\\mathbf","\\frak":"\\mathfrak","\\bm":"\\boldsymbol"};L({type:"font",names:["\\mathrm","\\mathit","\\mathbf","\\mathnormal","\\mathbb","\\mathcal","\\mathfrak","\\mathscr","\\mathsf","\\mathtt","\\Bbb","\\bold","\\frak"],props:{numArgs:1,allowedInArgument:!0},handler:(t,e)=>{let{parser:n,funcName:r}=t;const s=Lt(e[0]);let o=r;return o in Vr&&(o=Vr[o]),{type:"font",mode:n.mode,font:o.slice(1),body:s}},htmlBuilder:Gr,mathmlBuilder:$r}),L({type:"mclass",names:["\\boldsymbol","\\bm"],props:{numArgs:1},handler:(t,e)=>{let{parser:n}=t;const r=e[0],s=N.isCharacterBox(r);return{type:"mclass",mode:n.mode,mclass:Ut(r),body:[{type:"font",mode:n.mode,font:"boldsymbol",body:r}],isCharacterBox:s}}}),L({type:"font",names:["\\rm","\\sf","\\tt","\\bf","\\it","\\cal"],props:{numArgs:0,allowedInText:!0},handler:(t,e)=>{let{parser:n,funcName:r,breakOnTokenText:s}=t;const{mode:o}=n,l=n.parseExpression(!0,s),d="math"+r.slice(1);return{type:"font",mode:o,font:d,body:{type:"ordgroup",mode:n.mode,body:l}}},htmlBuilder:Gr,mathmlBuilder:$r});const Wr=(t,e)=>{let n=e;return t==="display"?n=n.id>=q.SCRIPT.id?n.text():q.DISPLAY:t==="text"&&n.size===q.DISPLAY.size?n=q.TEXT:t==="script"?n=q.SCRIPT:t==="scriptscript"&&(n=q.SCRIPTSCRIPT),n},zn=(t,e)=>{const n=Wr(t.size,e.style),r=n.fracNum(),s=n.fracDen();let o;o=e.havingStyle(r);const l=ae(t.numer,o,e);if(t.continued){const se=8.5/e.fontMetrics().ptPerEm,le=3.5/e.fontMetrics().ptPerEm;l.height=l.height<se?se:l.height,l.depth=l.depth<le?le:l.depth}o=e.havingStyle(s);const d=ae(t.denom,o,e);let g,k,S;t.hasBarLine?(t.barSize?(k=de(t.barSize,e),g=E.makeLineSpan("frac-line",e,k)):g=E.makeLineSpan("frac-line",e),k=g.height,S=g.height):(g=null,k=0,S=e.fontMetrics().defaultRuleThickness);let v,T,_;n.size===q.DISPLAY.size||t.size==="display"?(v=e.fontMetrics().num1,k>0?T=3*S:T=7*S,_=e.fontMetrics().denom1):(k>0?(v=e.fontMetrics().num2,T=S):(v=e.fontMetrics().num3,T=3*S),_=e.fontMetrics().denom2);let P;if(g){const se=e.fontMetrics().axisHeight;v-l.depth-(se+.5*k)<T&&(v+=T-(v-l.depth-(se+.5*k))),se-.5*k-(d.height-_)<T&&(_+=T-(se-.5*k-(d.height-_)));const le=-(se-.5*k);P=E.makeVList({positionType:"individualShift",children:[{type:"elem",elem:d,shift:_},{type:"elem",elem:g,shift:le},{type:"elem",elem:l,shift:-v}]},e)}else{const se=v-l.depth-(d.height-_);se<T&&(v+=.5*(T-se),_+=.5*(T-se)),P=E.makeVList({positionType:"individualShift",children:[{type:"elem",elem:d,shift:_},{type:"elem",elem:l,shift:-v}]},e)}o=e.havingStyle(n),P.height*=o.sizeMultiplier/e.sizeMultiplier,P.depth*=o.sizeMultiplier/e.sizeMultiplier;let V;n.size===q.DISPLAY.size?V=e.fontMetrics().delim1:n.size===q.SCRIPTSCRIPT.size?V=e.havingStyle(q.SCRIPT).fontMetrics().delim2:V=e.fontMetrics().delim2;let re,ne;return t.leftDelim==null?re=ht(e,["mopen"]):re=F0.customSizedDelim(t.leftDelim,V,!0,e.havingStyle(n),t.mode,["mopen"]),t.continued?ne=E.makeSpan([]):t.rightDelim==null?ne=ht(e,["mclose"]):ne=F0.customSizedDelim(t.rightDelim,V,!0,e.havingStyle(n),t.mode,["mclose"]),E.makeSpan(["mord"].concat(o.sizingClasses(e)),[re,E.makeSpan(["mfrac"],[P]),ne],e)},Bn=(t,e)=>{let n=new R.MathNode("mfrac",[fe(t.numer,e),fe(t.denom,e)]);if(!t.hasBarLine)n.setAttribute("linethickness","0px");else if(t.barSize){const s=de(t.barSize,e);n.setAttribute("linethickness",I(s))}const r=Wr(t.size,e.style);if(r.size!==e.style.size){n=new R.MathNode("mstyle",[n]);const s=r.size===q.DISPLAY.size?"true":"false";n.setAttribute("displaystyle",s),n.setAttribute("scriptlevel","0")}if(t.leftDelim!=null||t.rightDelim!=null){const s=[];if(t.leftDelim!=null){const o=new R.MathNode("mo",[new R.TextNode(t.leftDelim.replace("\\",""))]);o.setAttribute("fence","true"),s.push(o)}if(s.push(n),t.rightDelim!=null){const o=new R.MathNode("mo",[new R.TextNode(t.rightDelim.replace("\\",""))]);o.setAttribute("fence","true"),s.push(o)}return fn(s)}return n};L({type:"genfrac",names:["\\dfrac","\\frac","\\tfrac","\\dbinom","\\binom","\\tbinom","\\\\atopfrac","\\\\bracefrac","\\\\brackfrac"],props:{numArgs:2,allowedInArgument:!0},handler:(t,e)=>{let{parser:n,funcName:r}=t;const s=e[0],o=e[1];let l,d=null,g=null,k="auto";switch(r){case"\\dfrac":case"\\frac":case"\\tfrac":l=!0;break;case"\\\\atopfrac":l=!1;break;case"\\dbinom":case"\\binom":case"\\tbinom":l=!1,d="(",g=")";break;case"\\\\bracefrac":l=!1,d="\\{",g="\\}";break;case"\\\\brackfrac":l=!1,d="[",g="]";break;default:throw new Error("Unrecognized genfrac command")}switch(r){case"\\dfrac":case"\\dbinom":k="display";break;case"\\tfrac":case"\\tbinom":k="text";break}return{type:"genfrac",mode:n.mode,continued:!1,numer:s,denom:o,hasBarLine:l,leftDelim:d,rightDelim:g,size:k,barSize:null}},htmlBuilder:zn,mathmlBuilder:Bn}),L({type:"genfrac",names:["\\cfrac"],props:{numArgs:2},handler:(t,e)=>{let{parser:n,funcName:r}=t;const s=e[0],o=e[1];return{type:"genfrac",mode:n.mode,continued:!0,numer:s,denom:o,hasBarLine:!0,leftDelim:null,rightDelim:null,size:"display",barSize:null}}}),L({type:"infix",names:["\\over","\\choose","\\atop","\\brace","\\brack"],props:{numArgs:0,infix:!0},handler(t){let{parser:e,funcName:n,token:r}=t,s;switch(n){case"\\over":s="\\frac";break;case"\\choose":s="\\binom";break;case"\\atop":s="\\\\atopfrac";break;case"\\brace":s="\\\\bracefrac";break;case"\\brack":s="\\\\brackfrac";break;default:throw new Error("Unrecognized infix genfrac command")}return{type:"infix",mode:e.mode,replaceWith:s,token:r}}});const Yr=["display","text","script","scriptscript"],Xr=function(t){let e=null;return t.length>0&&(e=t,e=e==="."?null:e),e};L({type:"genfrac",names:["\\genfrac"],props:{numArgs:6,allowedInArgument:!0,argTypes:["math","math","size","text","math","math"]},handler(t,e){let{parser:n}=t;const r=e[4],s=e[5],o=Lt(e[0]),l=o.type==="atom"&&o.family==="open"?Xr(o.text):null,d=Lt(e[1]),g=d.type==="atom"&&d.family==="close"?Xr(d.text):null,k=J(e[2],"size");let S,v=null;k.isBlank?S=!0:(v=k.value,S=v.number>0);let T="auto",_=e[3];if(_.type==="ordgroup"){if(_.body.length>0){const P=J(_.body[0],"textord");T=Yr[Number(P.text)]}}else _=J(_,"textord"),T=Yr[Number(_.text)];return{type:"genfrac",mode:n.mode,numer:r,denom:s,continued:!1,hasBarLine:S,barSize:v,leftDelim:l,rightDelim:g,size:T}},htmlBuilder:zn,mathmlBuilder:Bn}),L({type:"infix",names:["\\above"],props:{numArgs:1,argTypes:["size"],infix:!0},handler(t,e){let{parser:n,funcName:r,token:s}=t;return{type:"infix",mode:n.mode,replaceWith:"\\\\abovefrac",size:J(e[0],"size").value,token:s}}}),L({type:"genfrac",names:["\\\\abovefrac"],props:{numArgs:3,argTypes:["math","size","math"]},handler:(t,e)=>{let{parser:n,funcName:r}=t;const s=e[0],o=ze(J(e[1],"infix").size),l=e[2],d=o.number>0;return{type:"genfrac",mode:n.mode,numer:s,denom:l,continued:!1,hasBarLine:d,barSize:o,leftDelim:null,rightDelim:null,size:"auto"}},htmlBuilder:zn,mathmlBuilder:Bn});const jr=(t,e)=>{const n=e.style;let r,s;t.type==="supsub"?(r=t.sup?ae(t.sup,e.havingStyle(n.sup()),e):ae(t.sub,e.havingStyle(n.sub()),e),s=J(t.base,"horizBrace")):s=J(t,"horizBrace");const o=ae(s.base,e.havingBaseStyle(q.DISPLAY)),l=v0.svgSpan(s,e);let d;if(s.isOver?(d=E.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:o},{type:"kern",size:.1},{type:"elem",elem:l}]},e),d.children[0].children[0].children[1].classes.push("svg-align")):(d=E.makeVList({positionType:"bottom",positionData:o.depth+.1+l.height,children:[{type:"elem",elem:l},{type:"kern",size:.1},{type:"elem",elem:o}]},e),d.children[0].children[0].children[0].classes.push("svg-align")),r){const g=E.makeSpan(["mord",s.isOver?"mover":"munder"],[d],e);s.isOver?d=E.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:g},{type:"kern",size:.2},{type:"elem",elem:r}]},e):d=E.makeVList({positionType:"bottom",positionData:g.depth+.2+r.height+r.depth,children:[{type:"elem",elem:r},{type:"kern",size:.2},{type:"elem",elem:g}]},e)}return E.makeSpan(["mord",s.isOver?"mover":"munder"],[d],e)};L({type:"horizBrace",names:["\\overbrace","\\underbrace"],props:{numArgs:1},handler(t,e){let{parser:n,funcName:r}=t;return{type:"horizBrace",mode:n.mode,label:r,isOver:/^\\over/.test(r),base:e[0]}},htmlBuilder:jr,mathmlBuilder:(t,e)=>{const n=v0.mathMLnode(t.label);return new R.MathNode(t.isOver?"mover":"munder",[fe(t.base,e),n])}}),L({type:"href",names:["\\href"],props:{numArgs:2,argTypes:["url","original"],allowedInText:!0},handler:(t,e)=>{let{parser:n}=t;const r=e[1],s=J(e[0],"url").url;return n.settings.isTrusted({command:"\\href",url:s})?{type:"href",mode:n.mode,href:s,body:Me(r)}:n.formatUnsupportedCmd("\\href")},htmlBuilder:(t,e)=>{const n=Re(t.body,e,!1);return E.makeAnchor(t.href,[],n,e)},mathmlBuilder:(t,e)=>{let n=R0(t.body,e);return n instanceof n0||(n=new n0("mrow",[n])),n.setAttribute("href",t.href),n}}),L({type:"href",names:["\\url"],props:{numArgs:1,argTypes:["url"],allowedInText:!0},handler:(t,e)=>{let{parser:n}=t;const r=J(e[0],"url").url;if(!n.settings.isTrusted({command:"\\url",url:r}))return n.formatUnsupportedCmd("\\url");const s=[];for(let l=0;l<r.length;l++){let d=r[l];d==="~"&&(d="\\textasciitilde"),s.push({type:"textord",mode:"text",text:d})}const o={type:"text",mode:n.mode,font:"\\texttt",body:s};return{type:"href",mode:n.mode,href:r,body:Me(o)}}}),L({type:"hbox",names:["\\hbox"],props:{numArgs:1,argTypes:["text"],allowedInText:!0,primitive:!0},handler(t,e){let{parser:n}=t;return{type:"hbox",mode:n.mode,body:Me(e[0])}},htmlBuilder(t,e){const n=Re(t.body,e,!1);return E.makeFragment(n)},mathmlBuilder(t,e){return new R.MathNode("mrow",Ve(t.body,e))}}),L({type:"html",names:["\\htmlClass","\\htmlId","\\htmlStyle","\\htmlData"],props:{numArgs:2,argTypes:["raw","original"],allowedInText:!0},handler:(t,e)=>{let{parser:n,funcName:r,token:s}=t;const o=J(e[0],"raw").string,l=e[1];n.settings.strict&&n.settings.reportNonstrict("htmlExtension","HTML extension is disabled on strict mode");let d;const g={};switch(r){case"\\htmlClass":g.class=o,d={command:"\\htmlClass",class:o};break;case"\\htmlId":g.id=o,d={command:"\\htmlId",id:o};break;case"\\htmlStyle":g.style=o,d={command:"\\htmlStyle",style:o};break;case"\\htmlData":{const k=o.split(",");for(let S=0;S<k.length;S++){const v=k[S].split("=");if(v.length!==2)throw new f("Error parsing key-value for \\htmlData");g["data-"+v[0].trim()]=v[1].trim()}d={command:"\\htmlData",attributes:g};break}default:throw new Error("Unrecognized html command")}return n.settings.isTrusted(d)?{type:"html",mode:n.mode,attributes:g,body:Me(l)}:n.formatUnsupportedCmd(r)},htmlBuilder:(t,e)=>{const n=Re(t.body,e,!1),r=["enclosing"];t.attributes.class&&r.push(...t.attributes.class.trim().split(/\s+/));const s=E.makeSpan(r,n,e);for(const o in t.attributes)o!=="class"&&t.attributes.hasOwnProperty(o)&&s.setAttribute(o,t.attributes[o]);return s},mathmlBuilder:(t,e)=>R0(t.body,e)}),L({type:"htmlmathml",names:["\\html@mathml"],props:{numArgs:2,allowedInText:!0},handler:(t,e)=>{let{parser:n}=t;return{type:"htmlmathml",mode:n.mode,html:Me(e[0]),mathml:Me(e[1])}},htmlBuilder:(t,e)=>{const n=Re(t.html,e,!1);return E.makeFragment(n)},mathmlBuilder:(t,e)=>R0(t.mathml,e)});const _n=function(t){if(/^[-+]? *(\d+(\.\d*)?|\.\d+)$/.test(t))return{number:+t,unit:"bp"};{const e=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(t);if(!e)throw new f("Invalid size: '"+t+"' in \\includegraphics");const n={number:+(e[1]+e[2]),unit:e[3]};if(!Z0(n))throw new f("Invalid unit: '"+n.unit+"' in \\includegraphics.");return n}};L({type:"includegraphics",names:["\\includegraphics"],props:{numArgs:1,numOptionalArgs:1,argTypes:["raw","url"],allowedInText:!1},handler:(t,e,n)=>{let{parser:r}=t,s={number:0,unit:"em"},o={number:.9,unit:"em"},l={number:0,unit:"em"},d="";if(n[0]){const S=J(n[0],"raw").string.split(",");for(let v=0;v<S.length;v++){const T=S[v].split("=");if(T.length===2){const _=T[1].trim();switch(T[0].trim()){case"alt":d=_;break;case"width":s=_n(_);break;case"height":o=_n(_);break;case"totalheight":l=_n(_);break;default:throw new f("Invalid key: '"+T[0]+"' in \\includegraphics.")}}}}const g=J(e[0],"url").url;return d===""&&(d=g,d=d.replace(/^.*[\\/]/,""),d=d.substring(0,d.lastIndexOf("."))),r.settings.isTrusted({command:"\\includegraphics",url:g})?{type:"includegraphics",mode:r.mode,alt:d,width:s,height:o,totalheight:l,src:g}:r.formatUnsupportedCmd("\\includegraphics")},htmlBuilder:(t,e)=>{const n=de(t.height,e);let r=0;t.totalheight.number>0&&(r=de(t.totalheight,e)-n);let s=0;t.width.number>0&&(s=de(t.width,e));const o={height:I(n+r)};s>0&&(o.width=I(s)),r>0&&(o.verticalAlign=I(-r));const l=new Bt(t.src,t.alt,o);return l.height=n,l.depth=r,l},mathmlBuilder:(t,e)=>{const n=new R.MathNode("mglyph",[]);n.setAttribute("alt",t.alt);const r=de(t.height,e);let s=0;if(t.totalheight.number>0&&(s=de(t.totalheight,e)-r,n.setAttribute("valign",I(-s))),n.setAttribute("height",I(r+s)),t.width.number>0){const o=de(t.width,e);n.setAttribute("width",I(o))}return n.setAttribute("src",t.src),n}}),L({type:"kern",names:["\\kern","\\mkern","\\hskip","\\mskip"],props:{numArgs:1,argTypes:["size"],primitive:!0,allowedInText:!0},handler(t,e){let{parser:n,funcName:r}=t;const s=J(e[0],"size");if(n.settings.strict){const o=r[1]==="m",l=s.value.unit==="mu";o?(l||n.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+r+" supports only mu units, "+("not "+s.value.unit+" units")),n.mode!=="math"&&n.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+r+" works only in math mode")):l&&n.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+r+" doesn't support mu units")}return{type:"kern",mode:n.mode,dimension:s.value}},htmlBuilder(t,e){return E.makeGlue(t.dimension,e)},mathmlBuilder(t,e){const n=de(t.dimension,e);return new R.SpaceNode(n)}}),L({type:"lap",names:["\\mathllap","\\mathrlap","\\mathclap"],props:{numArgs:1,allowedInText:!0},handler:(t,e)=>{let{parser:n,funcName:r}=t;const s=e[0];return{type:"lap",mode:n.mode,alignment:r.slice(5),body:s}},htmlBuilder:(t,e)=>{let n;t.alignment==="clap"?(n=E.makeSpan([],[ae(t.body,e)]),n=E.makeSpan(["inner"],[n],e)):n=E.makeSpan(["inner"],[ae(t.body,e)]);const r=E.makeSpan(["fix"],[]);let s=E.makeSpan([t.alignment],[n,r],e);const o=E.makeSpan(["strut"]);return o.style.height=I(s.height+s.depth),s.depth&&(o.style.verticalAlign=I(-s.depth)),s.children.unshift(o),s=E.makeSpan(["thinbox"],[s],e),E.makeSpan(["mord","vbox"],[s],e)},mathmlBuilder:(t,e)=>{const n=new R.MathNode("mpadded",[fe(t.body,e)]);if(t.alignment!=="rlap"){const r=t.alignment==="llap"?"-1":"-0.5";n.setAttribute("lspace",r+"width")}return n.setAttribute("width","0px"),n}}),L({type:"styling",names:["\\(","$"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(t,e){let{funcName:n,parser:r}=t;const s=r.mode;r.switchMode("math");const o=n==="\\("?"\\)":"$",l=r.parseExpression(!1,o);return r.expect(o),r.switchMode(s),{type:"styling",mode:r.mode,style:"text",body:l}}}),L({type:"text",names:["\\)","\\]"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(t,e){throw new f("Mismatched "+t.funcName)}});const Zr=(t,e)=>{switch(e.style.size){case q.DISPLAY.size:return t.display;case q.TEXT.size:return t.text;case q.SCRIPT.size:return t.script;case q.SCRIPTSCRIPT.size:return t.scriptscript;default:return t.text}};L({type:"mathchoice",names:["\\mathchoice"],props:{numArgs:4,primitive:!0},handler:(t,e)=>{let{parser:n}=t;return{type:"mathchoice",mode:n.mode,display:Me(e[0]),text:Me(e[1]),script:Me(e[2]),scriptscript:Me(e[3])}},htmlBuilder:(t,e)=>{const n=Zr(t,e),r=Re(n,e,!1);return E.makeFragment(r)},mathmlBuilder:(t,e)=>{const n=Zr(t,e);return R0(n,e)}});const Kr=(t,e,n,r,s,o,l)=>{t=E.makeSpan([],[t]);const d=n&&N.isCharacterBox(n);let g,k;if(e){const T=ae(e,r.havingStyle(s.sup()),r);k={elem:T,kern:Math.max(r.fontMetrics().bigOpSpacing1,r.fontMetrics().bigOpSpacing3-T.depth)}}if(n){const T=ae(n,r.havingStyle(s.sub()),r);g={elem:T,kern:Math.max(r.fontMetrics().bigOpSpacing2,r.fontMetrics().bigOpSpacing4-T.height)}}let S;if(k&&g){const T=r.fontMetrics().bigOpSpacing5+g.elem.height+g.elem.depth+g.kern+t.depth+l;S=E.makeVList({positionType:"bottom",positionData:T,children:[{type:"kern",size:r.fontMetrics().bigOpSpacing5},{type:"elem",elem:g.elem,marginLeft:I(-o)},{type:"kern",size:g.kern},{type:"elem",elem:t},{type:"kern",size:k.kern},{type:"elem",elem:k.elem,marginLeft:I(o)},{type:"kern",size:r.fontMetrics().bigOpSpacing5}]},r)}else if(g){const T=t.height-l;S=E.makeVList({positionType:"top",positionData:T,children:[{type:"kern",size:r.fontMetrics().bigOpSpacing5},{type:"elem",elem:g.elem,marginLeft:I(-o)},{type:"kern",size:g.kern},{type:"elem",elem:t}]},r)}else if(k){const T=t.depth+l;S=E.makeVList({positionType:"bottom",positionData:T,children:[{type:"elem",elem:t},{type:"kern",size:k.kern},{type:"elem",elem:k.elem,marginLeft:I(o)},{type:"kern",size:r.fontMetrics().bigOpSpacing5}]},r)}else return t;const v=[S];if(g&&o!==0&&!d){const T=E.makeSpan(["mspace"],[],r);T.style.marginRight=I(o),v.unshift(T)}return E.makeSpan(["mop","op-limits"],v,r)},Qr=["\\smallint"],J0=(t,e)=>{let n,r,s=!1,o;t.type==="supsub"?(n=t.sup,r=t.sub,o=J(t.base,"op"),s=!0):o=J(t,"op");const l=e.style;let d=!1;l.size===q.DISPLAY.size&&o.symbol&&!N.contains(Qr,o.name)&&(d=!0);let g;if(o.symbol){const v=d?"Size2-Regular":"Size1-Regular";let T="";if((o.name==="\\oiint"||o.name==="\\oiiint")&&(T=o.name.slice(1),o.name=T==="oiint"?"\\iint":"\\iiint"),g=E.makeSymbol(o.name,v,"math",e,["mop","op-symbol",d?"large-op":"small-op"]),T.length>0){const _=g.italic,P=E.staticSvg(T+"Size"+(d?"2":"1"),e);g=E.makeVList({positionType:"individualShift",children:[{type:"elem",elem:g,shift:0},{type:"elem",elem:P,shift:d?.08:0}]},e),o.name="\\"+T,g.classes.unshift("mop"),g.italic=_}}else if(o.body){const v=Re(o.body,e,!0);v.length===1&&v[0]instanceof qe?(g=v[0],g.classes[0]="mop"):g=E.makeSpan(["mop"],v,e)}else{const v=[];for(let T=1;T<o.name.length;T++)v.push(E.mathsym(o.name[T],o.mode,e));g=E.makeSpan(["mop"],v,e)}let k=0,S=0;return(g instanceof qe||o.name==="\\oiint"||o.name==="\\oiiint")&&!o.suppressBaseShift&&(k=(g.height-g.depth)/2-e.fontMetrics().axisHeight,S=g.italic),s?Kr(g,n,r,e,l,S,k):(k&&(g.style.position="relative",g.style.top=I(k)),g)},ft=(t,e)=>{let n;if(t.symbol)n=new n0("mo",[r0(t.name,t.mode)]),N.contains(Qr,t.name)&&n.setAttribute("largeop","false");else if(t.body)n=new n0("mo",Ve(t.body,e));else{n=new n0("mi",[new mt(t.name.slice(1))]);const r=new n0("mo",[r0("⁡","text")]);t.parentIsSupSub?n=new n0("mrow",[n,r]):n=fr([n,r])}return n},ra={"∏":"\\prod","∐":"\\coprod","∑":"\\sum","⋀":"\\bigwedge","⋁":"\\bigvee","⋂":"\\bigcap","⋃":"\\bigcup","⨀":"\\bigodot","⨁":"\\bigoplus","⨂":"\\bigotimes","⨄":"\\biguplus","⨆":"\\bigsqcup"};L({type:"op",names:["\\coprod","\\bigvee","\\bigwedge","\\biguplus","\\bigcap","\\bigcup","\\intop","\\prod","\\sum","\\bigotimes","\\bigoplus","\\bigodot","\\bigsqcup","\\smallint","∏","∐","∑","⋀","⋁","⋂","⋃","⨀","⨁","⨂","⨄","⨆"],props:{numArgs:0},handler:(t,e)=>{let{parser:n,funcName:r}=t,s=r;return s.length===1&&(s=ra[s]),{type:"op",mode:n.mode,limits:!0,parentIsSupSub:!1,symbol:!0,name:s}},htmlBuilder:J0,mathmlBuilder:ft}),L({type:"op",names:["\\mathop"],props:{numArgs:1,primitive:!0},handler:(t,e)=>{let{parser:n}=t;const r=e[0];return{type:"op",mode:n.mode,limits:!1,parentIsSupSub:!1,symbol:!1,body:Me(r)}},htmlBuilder:J0,mathmlBuilder:ft});const sa={"∫":"\\int","∬":"\\iint","∭":"\\iiint","∮":"\\oint","∯":"\\oiint","∰":"\\oiiint"};L({type:"op",names:["\\arcsin","\\arccos","\\arctan","\\arctg","\\arcctg","\\arg","\\ch","\\cos","\\cosec","\\cosh","\\cot","\\cotg","\\coth","\\csc","\\ctg","\\cth","\\deg","\\dim","\\exp","\\hom","\\ker","\\lg","\\ln","\\log","\\sec","\\sin","\\sinh","\\sh","\\tan","\\tanh","\\tg","\\th"],props:{numArgs:0},handler(t){let{parser:e,funcName:n}=t;return{type:"op",mode:e.mode,limits:!1,parentIsSupSub:!1,symbol:!1,name:n}},htmlBuilder:J0,mathmlBuilder:ft}),L({type:"op",names:["\\det","\\gcd","\\inf","\\lim","\\max","\\min","\\Pr","\\sup"],props:{numArgs:0},handler(t){let{parser:e,funcName:n}=t;return{type:"op",mode:e.mode,limits:!0,parentIsSupSub:!1,symbol:!1,name:n}},htmlBuilder:J0,mathmlBuilder:ft}),L({type:"op",names:["\\int","\\iint","\\iiint","\\oint","\\oiint","\\oiiint","∫","∬","∭","∮","∯","∰"],props:{numArgs:0},handler(t){let{parser:e,funcName:n}=t,r=n;return r.length===1&&(r=sa[r]),{type:"op",mode:e.mode,limits:!1,parentIsSupSub:!1,symbol:!0,name:r}},htmlBuilder:J0,mathmlBuilder:ft});const Jr=(t,e)=>{let n,r,s=!1,o;t.type==="supsub"?(n=t.sup,r=t.sub,o=J(t.base,"operatorname"),s=!0):o=J(t,"operatorname");let l;if(o.body.length>0){const d=o.body.map(k=>{const S=k.text;return typeof S=="string"?{type:"textord",mode:k.mode,text:S}:k}),g=Re(d,e.withFont("mathrm"),!0);for(let k=0;k<g.length;k++){const S=g[k];S instanceof qe&&(S.text=S.text.replace(/\u2212/,"-").replace(/\u2217/,"*"))}l=E.makeSpan(["mop"],g,e)}else l=E.makeSpan(["mop"],[],e);return s?Kr(l,n,r,e,e.style,0,0):l};L({type:"operatorname",names:["\\operatorname@","\\operatornamewithlimits"],props:{numArgs:1},handler:(t,e)=>{let{parser:n,funcName:r}=t;const s=e[0];return{type:"operatorname",mode:n.mode,body:Me(s),alwaysHandleSupSub:r==="\\operatornamewithlimits",limits:!1,parentIsSupSub:!1}},htmlBuilder:Jr,mathmlBuilder:(t,e)=>{let n=Ve(t.body,e.withFont("mathrm")),r=!0;for(let l=0;l<n.length;l++){const d=n[l];if(!(d instanceof R.SpaceNode))if(d instanceof R.MathNode)switch(d.type){case"mi":case"mn":case"ms":case"mspace":case"mtext":break;case"mo":{const g=d.children[0];d.children.length===1&&g instanceof R.TextNode?g.text=g.text.replace(/\u2212/,"-").replace(/\u2217/,"*"):r=!1;break}default:r=!1}else r=!1}if(r){const l=n.map(d=>d.toText()).join("");n=[new R.TextNode(l)]}const s=new R.MathNode("mi",n);s.setAttribute("mathvariant","normal");const o=new R.MathNode("mo",[r0("⁡","text")]);return t.parentIsSupSub?new R.MathNode("mrow",[s,o]):R.newDocumentFragment([s,o])}}),p("\\operatorname","\\@ifstar\\operatornamewithlimits\\operatorname@"),P0({type:"ordgroup",htmlBuilder(t,e){return t.semisimple?E.makeFragment(Re(t.body,e,!1)):E.makeSpan(["mord"],Re(t.body,e,!0),e)},mathmlBuilder(t,e){return R0(t.body,e,!0)}}),L({type:"overline",names:["\\overline"],props:{numArgs:1},handler(t,e){let{parser:n}=t;const r=e[0];return{type:"overline",mode:n.mode,body:r}},htmlBuilder(t,e){const n=ae(t.body,e.havingCrampedStyle()),r=E.makeLineSpan("overline-line",e),s=e.fontMetrics().defaultRuleThickness,o=E.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:n},{type:"kern",size:3*s},{type:"elem",elem:r},{type:"kern",size:s}]},e);return E.makeSpan(["mord","overline"],[o],e)},mathmlBuilder(t,e){const n=new R.MathNode("mo",[new R.TextNode("‾")]);n.setAttribute("stretchy","true");const r=new R.MathNode("mover",[fe(t.body,e),n]);return r.setAttribute("accent","true"),r}}),L({type:"phantom",names:["\\phantom"],props:{numArgs:1,allowedInText:!0},handler:(t,e)=>{let{parser:n}=t;const r=e[0];return{type:"phantom",mode:n.mode,body:Me(r)}},htmlBuilder:(t,e)=>{const n=Re(t.body,e.withPhantom(),!1);return E.makeFragment(n)},mathmlBuilder:(t,e)=>{const n=Ve(t.body,e);return new R.MathNode("mphantom",n)}}),L({type:"hphantom",names:["\\hphantom"],props:{numArgs:1,allowedInText:!0},handler:(t,e)=>{let{parser:n}=t;const r=e[0];return{type:"hphantom",mode:n.mode,body:r}},htmlBuilder:(t,e)=>{let n=E.makeSpan([],[ae(t.body,e.withPhantom())]);if(n.height=0,n.depth=0,n.children)for(let r=0;r<n.children.length;r++)n.children[r].height=0,n.children[r].depth=0;return n=E.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:n}]},e),E.makeSpan(["mord"],[n],e)},mathmlBuilder:(t,e)=>{const n=Ve(Me(t.body),e),r=new R.MathNode("mphantom",n),s=new R.MathNode("mpadded",[r]);return s.setAttribute("height","0px"),s.setAttribute("depth","0px"),s}}),L({type:"vphantom",names:["\\vphantom"],props:{numArgs:1,allowedInText:!0},handler:(t,e)=>{let{parser:n}=t;const r=e[0];return{type:"vphantom",mode:n.mode,body:r}},htmlBuilder:(t,e)=>{const n=E.makeSpan(["inner"],[ae(t.body,e.withPhantom())]),r=E.makeSpan(["fix"],[]);return E.makeSpan(["mord","rlap"],[n,r],e)},mathmlBuilder:(t,e)=>{const n=Ve(Me(t.body),e),r=new R.MathNode("mphantom",n),s=new R.MathNode("mpadded",[r]);return s.setAttribute("width","0px"),s}}),L({type:"raisebox",names:["\\raisebox"],props:{numArgs:2,argTypes:["size","hbox"],allowedInText:!0},handler(t,e){let{parser:n}=t;const r=J(e[0],"size").value,s=e[1];return{type:"raisebox",mode:n.mode,dy:r,body:s}},htmlBuilder(t,e){const n=ae(t.body,e),r=de(t.dy,e);return E.makeVList({positionType:"shift",positionData:-r,children:[{type:"elem",elem:n}]},e)},mathmlBuilder(t,e){const n=new R.MathNode("mpadded",[fe(t.body,e)]),r=t.dy.number+t.dy.unit;return n.setAttribute("voffset",r),n}}),L({type:"internal",names:["\\relax"],props:{numArgs:0,allowedInText:!0},handler(t){let{parser:e}=t;return{type:"internal",mode:e.mode}}}),L({type:"rule",names:["\\rule"],props:{numArgs:2,numOptionalArgs:1,argTypes:["size","size","size"]},handler(t,e,n){let{parser:r}=t;const s=n[0],o=J(e[0],"size"),l=J(e[1],"size");return{type:"rule",mode:r.mode,shift:s&&J(s,"size").value,width:o.value,height:l.value}},htmlBuilder(t,e){const n=E.makeSpan(["mord","rule"],[],e),r=de(t.width,e),s=de(t.height,e),o=t.shift?de(t.shift,e):0;return n.style.borderRightWidth=I(r),n.style.borderTopWidth=I(s),n.style.bottom=I(o),n.width=r,n.height=s+o,n.depth=-o,n.maxFontSize=s*1.125*e.sizeMultiplier,n},mathmlBuilder(t,e){const n=de(t.width,e),r=de(t.height,e),s=t.shift?de(t.shift,e):0,o=e.color&&e.getColor()||"black",l=new R.MathNode("mspace");l.setAttribute("mathbackground",o),l.setAttribute("width",I(n)),l.setAttribute("height",I(r));const d=new R.MathNode("mpadded",[l]);return s>=0?d.setAttribute("height",I(s)):(d.setAttribute("height",I(s)),d.setAttribute("depth",I(-s))),d.setAttribute("voffset",I(s)),d}});function es(t,e,n){const r=Re(t,e,!1),s=e.sizeMultiplier/n.sizeMultiplier;for(let o=0;o<r.length;o++){const l=r[o].classes.indexOf("sizing");l<0?Array.prototype.push.apply(r[o].classes,e.sizingClasses(n)):r[o].classes[l+1]==="reset-size"+e.size&&(r[o].classes[l+1]="reset-size"+n.size),r[o].height*=s,r[o].depth*=s}return E.makeFragment(r)}const ts=["\\tiny","\\sixptsize","\\scriptsize","\\footnotesize","\\small","\\normalsize","\\large","\\Large","\\LARGE","\\huge","\\Huge"];L({type:"sizing",names:ts,props:{numArgs:0,allowedInText:!0},handler:(t,e)=>{let{breakOnTokenText:n,funcName:r,parser:s}=t;const o=s.parseExpression(!1,n);return{type:"sizing",mode:s.mode,size:ts.indexOf(r)+1,body:o}},htmlBuilder:(t,e)=>{const n=e.havingSize(t.size);return es(t.body,n,e)},mathmlBuilder:(t,e)=>{const n=e.havingSize(t.size),r=Ve(t.body,n),s=new R.MathNode("mstyle",r);return s.setAttribute("mathsize",I(n.sizeMultiplier)),s}}),L({type:"smash",names:["\\smash"],props:{numArgs:1,numOptionalArgs:1,allowedInText:!0},handler:(t,e,n)=>{let{parser:r}=t,s=!1,o=!1;const l=n[0]&&J(n[0],"ordgroup");if(l){let g="";for(let k=0;k<l.body.length;++k)if(g=l.body[k].text,g==="t")s=!0;else if(g==="b")o=!0;else{s=!1,o=!1;break}}else s=!0,o=!0;const d=e[0];return{type:"smash",mode:r.mode,body:d,smashHeight:s,smashDepth:o}},htmlBuilder:(t,e)=>{const n=E.makeSpan([],[ae(t.body,e)]);if(!t.smashHeight&&!t.smashDepth)return n;if(t.smashHeight&&(n.height=0,n.children))for(let s=0;s<n.children.length;s++)n.children[s].height=0;if(t.smashDepth&&(n.depth=0,n.children))for(let s=0;s<n.children.length;s++)n.children[s].depth=0;const r=E.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:n}]},e);return E.makeSpan(["mord"],[r],e)},mathmlBuilder:(t,e)=>{const n=new R.MathNode("mpadded",[fe(t.body,e)]);return t.smashHeight&&n.setAttribute("height","0px"),t.smashDepth&&n.setAttribute("depth","0px"),n}}),L({type:"sqrt",names:["\\sqrt"],props:{numArgs:1,numOptionalArgs:1},handler(t,e,n){let{parser:r}=t;const s=n[0],o=e[0];return{type:"sqrt",mode:r.mode,body:o,index:s}},htmlBuilder(t,e){let n=ae(t.body,e.havingCrampedStyle());n.height===0&&(n.height=e.fontMetrics().xHeight),n=E.wrapFragment(n,e);const s=e.fontMetrics().defaultRuleThickness;let o=s;e.style.id<q.TEXT.id&&(o=e.fontMetrics().xHeight);let l=s+o/4;const d=n.height+n.depth+l+s,{span:g,ruleWidth:k,advanceWidth:S}=F0.sqrtImage(d,e),v=g.height-k;v>n.height+n.depth+l&&(l=(l+v-n.height-n.depth)/2);const T=g.height-n.height-l-k;n.style.paddingLeft=I(S);const _=E.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:n,wrapperClasses:["svg-align"]},{type:"kern",size:-(n.height+T)},{type:"elem",elem:g},{type:"kern",size:k}]},e);if(t.index){const P=e.havingStyle(q.SCRIPTSCRIPT),V=ae(t.index,P,e),re=.6*(_.height-_.depth),ne=E.makeVList({positionType:"shift",positionData:-re,children:[{type:"elem",elem:V}]},e),se=E.makeSpan(["root"],[ne]);return E.makeSpan(["mord","sqrt"],[se,_],e)}else return E.makeSpan(["mord","sqrt"],[_],e)},mathmlBuilder(t,e){const{body:n,index:r}=t;return r?new R.MathNode("mroot",[fe(n,e),fe(r,e)]):new R.MathNode("msqrt",[fe(n,e)])}});const ns={display:q.DISPLAY,text:q.TEXT,script:q.SCRIPT,scriptscript:q.SCRIPTSCRIPT};L({type:"styling",names:["\\displaystyle","\\textstyle","\\scriptstyle","\\scriptscriptstyle"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(t,e){let{breakOnTokenText:n,funcName:r,parser:s}=t;const o=s.parseExpression(!0,n),l=r.slice(1,r.length-5);return{type:"styling",mode:s.mode,style:l,body:o}},htmlBuilder(t,e){const n=ns[t.style],r=e.havingStyle(n).withFont("");return es(t.body,r,e)},mathmlBuilder(t,e){const n=ns[t.style],r=e.havingStyle(n),s=Ve(t.body,r),o=new R.MathNode("mstyle",s),d={display:["0","true"],text:["0","false"],script:["1","false"],scriptscript:["2","false"]}[t.style];return o.setAttribute("scriptlevel",d[0]),o.setAttribute("displaystyle",d[1]),o}});const ia=function(t,e){const n=t.base;return n?n.type==="op"?n.limits&&(e.style.size===q.DISPLAY.size||n.alwaysHandleSupSub)?J0:null:n.type==="operatorname"?n.alwaysHandleSupSub&&(e.style.size===q.DISPLAY.size||n.limits)?Jr:null:n.type==="accent"?N.isCharacterBox(n.base)?yn:null:n.type==="horizBrace"&&!t.sub===n.isOver?jr:null:null};P0({type:"supsub",htmlBuilder(t,e){const n=ia(t,e);if(n)return n(t,e);const{base:r,sup:s,sub:o}=t,l=ae(r,e);let d,g;const k=e.fontMetrics();let S=0,v=0;const T=r&&N.isCharacterBox(r);if(s){const le=e.havingStyle(e.style.sup());d=ae(s,le,e),T||(S=l.height-le.fontMetrics().supDrop*le.sizeMultiplier/e.sizeMultiplier)}if(o){const le=e.havingStyle(e.style.sub());g=ae(o,le,e),T||(v=l.depth+le.fontMetrics().subDrop*le.sizeMultiplier/e.sizeMultiplier)}let _;e.style===q.DISPLAY?_=k.sup1:e.style.cramped?_=k.sup3:_=k.sup2;const P=e.sizeMultiplier,V=I(.5/k.ptPerEm/P);let re=null;if(g){const le=t.base&&t.base.type==="op"&&t.base.name&&(t.base.name==="\\oiint"||t.base.name==="\\oiiint");(l instanceof qe||le)&&(re=I(-l.italic))}let ne;if(d&&g){S=Math.max(S,_,d.depth+.25*k.xHeight),v=Math.max(v,k.sub2);const ye=4*k.defaultRuleThickness;if(S-d.depth-(g.height-v)<ye){v=ye-(S-d.depth)+g.height;const Ie=.8*k.xHeight-(S-d.depth);Ie>0&&(S+=Ie,v-=Ie)}const Pe=[{type:"elem",elem:g,shift:v,marginRight:V,marginLeft:re},{type:"elem",elem:d,shift:-S,marginRight:V}];ne=E.makeVList({positionType:"individualShift",children:Pe},e)}else if(g){v=Math.max(v,k.sub1,g.height-.8*k.xHeight);const le=[{type:"elem",elem:g,marginLeft:re,marginRight:V}];ne=E.makeVList({positionType:"shift",positionData:v,children:le},e)}else if(d)S=Math.max(S,_,d.depth+.25*k.xHeight),ne=E.makeVList({positionType:"shift",positionData:-S,children:[{type:"elem",elem:d,marginRight:V}]},e);else throw new Error("supsub must have either sup or sub.");const se=dn(l,"right")||"mord";return E.makeSpan([se],[l,E.makeSpan(["msupsub"],[ne])],e)},mathmlBuilder(t,e){let n=!1,r,s;t.base&&t.base.type==="horizBrace"&&(s=!!t.sup,s===t.base.isOver&&(n=!0,r=t.base.isOver)),t.base&&(t.base.type==="op"||t.base.type==="operatorname")&&(t.base.parentIsSupSub=!0);const o=[fe(t.base,e)];t.sub&&o.push(fe(t.sub,e)),t.sup&&o.push(fe(t.sup,e));let l;if(n)l=r?"mover":"munder";else if(t.sub)if(t.sup){const d=t.base;d&&d.type==="op"&&d.limits&&e.style===q.DISPLAY||d&&d.type==="operatorname"&&d.alwaysHandleSupSub&&(e.style===q.DISPLAY||d.limits)?l="munderover":l="msubsup"}else{const d=t.base;d&&d.type==="op"&&d.limits&&(e.style===q.DISPLAY||d.alwaysHandleSupSub)||d&&d.type==="operatorname"&&d.alwaysHandleSupSub&&(d.limits||e.style===q.DISPLAY)?l="munder":l="msub"}else{const d=t.base;d&&d.type==="op"&&d.limits&&(e.style===q.DISPLAY||d.alwaysHandleSupSub)||d&&d.type==="operatorname"&&d.alwaysHandleSupSub&&(d.limits||e.style===q.DISPLAY)?l="mover":l="msup"}return new R.MathNode(l,o)}}),P0({type:"atom",htmlBuilder(t,e){return E.mathsym(t.text,t.mode,e,["m"+t.family])},mathmlBuilder(t,e){const n=new R.MathNode("mo",[r0(t.text,t.mode)]);if(t.family==="bin"){const r=gn(t,e);r==="bold-italic"&&n.setAttribute("mathvariant",r)}else t.family==="punct"?n.setAttribute("separator","true"):(t.family==="open"||t.family==="close")&&n.setAttribute("stretchy","false");return n}});const rs={mi:"italic",mn:"normal",mtext:"normal"};P0({type:"mathord",htmlBuilder(t,e){return E.makeOrd(t,e,"mathord")},mathmlBuilder(t,e){const n=new R.MathNode("mi",[r0(t.text,t.mode,e)]),r=gn(t,e)||"italic";return r!==rs[n.type]&&n.setAttribute("mathvariant",r),n}}),P0({type:"textord",htmlBuilder(t,e){return E.makeOrd(t,e,"textord")},mathmlBuilder(t,e){const n=r0(t.text,t.mode,e),r=gn(t,e)||"normal";let s;return t.mode==="text"?s=new R.MathNode("mtext",[n]):/[0-9]/.test(t.text)?s=new R.MathNode("mn",[n]):t.text==="\\prime"?s=new R.MathNode("mo",[n]):s=new R.MathNode("mi",[n]),r!==rs[s.type]&&s.setAttribute("mathvariant",r),s}});const Nn={"\\nobreak":"nobreak","\\allowbreak":"allowbreak"},Rn={" ":{},"\\ ":{},"~":{className:"nobreak"},"\\space":{},"\\nobreakspace":{className:"nobreak"}};P0({type:"spacing",htmlBuilder(t,e){if(Rn.hasOwnProperty(t.text)){const n=Rn[t.text].className||"";if(t.mode==="text"){const r=E.makeOrd(t,e,"textord");return r.classes.push(n),r}else return E.makeSpan(["mspace",n],[E.mathsym(t.text,t.mode,e)],e)}else{if(Nn.hasOwnProperty(t.text))return E.makeSpan(["mspace",Nn[t.text]],[],e);throw new f('Unknown type of space "'+t.text+'"')}},mathmlBuilder(t,e){let n;if(Rn.hasOwnProperty(t.text))n=new R.MathNode("mtext",[new R.TextNode(" ")]);else{if(Nn.hasOwnProperty(t.text))return new R.MathNode("mspace");throw new f('Unknown type of space "'+t.text+'"')}return n}});const ss=()=>{const t=new R.MathNode("mtd",[]);return t.setAttribute("width","50%"),t};P0({type:"tag",mathmlBuilder(t,e){const n=new R.MathNode("mtable",[new R.MathNode("mtr",[ss(),new R.MathNode("mtd",[R0(t.body,e)]),ss(),new R.MathNode("mtd",[R0(t.tag,e)])])]);return n.setAttribute("width","100%"),n}});const is={"\\text":void 0,"\\textrm":"textrm","\\textsf":"textsf","\\texttt":"texttt","\\textnormal":"textrm"},as={"\\textbf":"textbf","\\textmd":"textmd"},aa={"\\textit":"textit","\\textup":"textup"},os=(t,e)=>{const n=t.font;return n?is[n]?e.withTextFontFamily(is[n]):as[n]?e.withTextFontWeight(as[n]):e.withTextFontShape(aa[n]):e};L({type:"text",names:["\\text","\\textrm","\\textsf","\\texttt","\\textnormal","\\textbf","\\textmd","\\textit","\\textup"],props:{numArgs:1,argTypes:["text"],allowedInArgument:!0,allowedInText:!0},handler(t,e){let{parser:n,funcName:r}=t;const s=e[0];return{type:"text",mode:n.mode,body:Me(s),font:r}},htmlBuilder(t,e){const n=os(t,e),r=Re(t.body,n,!0);return E.makeSpan(["mord","text"],r,n)},mathmlBuilder(t,e){const n=os(t,e);return R0(t.body,n)}}),L({type:"underline",names:["\\underline"],props:{numArgs:1,allowedInText:!0},handler(t,e){let{parser:n}=t;return{type:"underline",mode:n.mode,body:e[0]}},htmlBuilder(t,e){const n=ae(t.body,e),r=E.makeLineSpan("underline-line",e),s=e.fontMetrics().defaultRuleThickness,o=E.makeVList({positionType:"top",positionData:n.height,children:[{type:"kern",size:s},{type:"elem",elem:r},{type:"kern",size:3*s},{type:"elem",elem:n}]},e);return E.makeSpan(["mord","underline"],[o],e)},mathmlBuilder(t,e){const n=new R.MathNode("mo",[new R.TextNode("‾")]);n.setAttribute("stretchy","true");const r=new R.MathNode("munder",[fe(t.body,e),n]);return r.setAttribute("accentunder","true"),r}}),L({type:"vcenter",names:["\\vcenter"],props:{numArgs:1,argTypes:["original"],allowedInText:!1},handler(t,e){let{parser:n}=t;return{type:"vcenter",mode:n.mode,body:e[0]}},htmlBuilder(t,e){const n=ae(t.body,e),r=e.fontMetrics().axisHeight,s=.5*(n.height-r-(n.depth+r));return E.makeVList({positionType:"shift",positionData:s,children:[{type:"elem",elem:n}]},e)},mathmlBuilder(t,e){return new R.MathNode("mpadded",[fe(t.body,e)],["vcenter"])}}),L({type:"verb",names:["\\verb"],props:{numArgs:0,allowedInText:!0},handler(t,e,n){throw new f("\\verb ended by end of line instead of matching delimiter")},htmlBuilder(t,e){const n=ls(t),r=[],s=e.havingStyle(e.style.text());for(let o=0;o<n.length;o++){let l=n[o];l==="~"&&(l="\\textasciitilde"),r.push(E.makeSymbol(l,"Typewriter-Regular",t.mode,s,["mord","texttt"]))}return E.makeSpan(["mord","text"].concat(s.sizingClasses(e)),E.tryCombineChars(r),s)},mathmlBuilder(t,e){const n=new R.TextNode(ls(t)),r=new R.MathNode("mtext",[n]);return r.setAttribute("mathvariant","monospace"),r}});const ls=t=>t.body.replace(/ /g,t.star?"␣":" ");var O0=dr;const us=`[ \r
	]`,oa="\\\\[a-zA-Z@]+",la="\\\\[^\uD800-\uDFFF]",ua="("+oa+")"+us+"*",ca=`\\\\(
|[ \r	]+
?)[ \r	]*`,In="[̀-ͯ]",ha=new RegExp(In+"+$"),ma="("+us+"+)|"+(ca+"|")+"([!-\\[\\]-‧‪-퟿豈-￿]"+(In+"*")+"|[\uD800-\uDBFF][\uDC00-\uDFFF]"+(In+"*")+"|\\\\verb\\*([^]).*?\\4|\\\\verb([^*a-zA-Z]).*?\\5"+("|"+ua)+("|"+la+")");class cs{constructor(e,n){this.input=void 0,this.settings=void 0,this.tokenRegex=void 0,this.catcodes=void 0,this.input=e,this.settings=n,this.tokenRegex=new RegExp(ma,"g"),this.catcodes={"%":14,"~":13}}setCatcode(e,n){this.catcodes[e]=n}lex(){const e=this.input,n=this.tokenRegex.lastIndex;if(n===e.length)return new s0("EOF",new je(this,n,n));const r=this.tokenRegex.exec(e);if(r===null||r.index!==n)throw new f("Unexpected character: '"+e[n]+"'",new s0(e[n],new je(this,n,n+1)));const s=r[6]||r[3]||(r[2]?"\\ ":" ");if(this.catcodes[s]===14){const o=e.indexOf(`
`,this.tokenRegex.lastIndex);return o===-1?(this.tokenRegex.lastIndex=e.length,this.settings.reportNonstrict("commentAtEnd","% comment has no terminating newline; LaTeX would fail because of commenting the end of math mode (e.g. $)")):this.tokenRegex.lastIndex=o+1,this.lex()}return new s0(s,new je(this,n,this.tokenRegex.lastIndex))}}class da{constructor(e,n){e===void 0&&(e={}),n===void 0&&(n={}),this.current=void 0,this.builtins=void 0,this.undefStack=void 0,this.current=n,this.builtins=e,this.undefStack=[]}beginGroup(){this.undefStack.push({})}endGroup(){if(this.undefStack.length===0)throw new f("Unbalanced namespace destruction: attempt to pop global namespace; please report this as a bug");const e=this.undefStack.pop();for(const n in e)e.hasOwnProperty(n)&&(e[n]==null?delete this.current[n]:this.current[n]=e[n])}endGroups(){for(;this.undefStack.length>0;)this.endGroup()}has(e){return this.current.hasOwnProperty(e)||this.builtins.hasOwnProperty(e)}get(e){return this.current.hasOwnProperty(e)?this.current[e]:this.builtins[e]}set(e,n,r){if(r===void 0&&(r=!1),r){for(let s=0;s<this.undefStack.length;s++)delete this.undefStack[s][e];this.undefStack.length>0&&(this.undefStack[this.undefStack.length-1][e]=n)}else{const s=this.undefStack[this.undefStack.length-1];s&&!s.hasOwnProperty(e)&&(s[e]=this.current[e])}n==null?delete this.current[e]:this.current[e]=n}}var pa=Hr;p("\\noexpand",function(t){const e=t.popToken();return t.isExpandable(e.text)&&(e.noexpand=!0,e.treatAsRelax=!0),{tokens:[e],numArgs:0}}),p("\\expandafter",function(t){const e=t.popToken();return t.expandOnce(!0),{tokens:[e],numArgs:0}}),p("\\@firstoftwo",function(t){return{tokens:t.consumeArgs(2)[0],numArgs:0}}),p("\\@secondoftwo",function(t){return{tokens:t.consumeArgs(2)[1],numArgs:0}}),p("\\@ifnextchar",function(t){const e=t.consumeArgs(3);t.consumeSpaces();const n=t.future();return e[0].length===1&&e[0][0].text===n.text?{tokens:e[1],numArgs:0}:{tokens:e[2],numArgs:0}}),p("\\@ifstar","\\@ifnextchar *{\\@firstoftwo{#1}}"),p("\\TextOrMath",function(t){const e=t.consumeArgs(2);return t.mode==="text"?{tokens:e[0],numArgs:0}:{tokens:e[1],numArgs:0}});const hs={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};p("\\char",function(t){let e=t.popToken(),n,r="";if(e.text==="'")n=8,e=t.popToken();else if(e.text==='"')n=16,e=t.popToken();else if(e.text==="`")if(e=t.popToken(),e.text[0]==="\\")r=e.text.charCodeAt(1);else{if(e.text==="EOF")throw new f("\\char` missing argument");r=e.text.charCodeAt(0)}else n=10;if(n){if(r=hs[e.text],r==null||r>=n)throw new f("Invalid base-"+n+" digit "+e.text);let s;for(;(s=hs[t.future().text])!=null&&s<n;)r*=n,r+=s,t.popToken()}return"\\@char{"+r+"}"});const On=(t,e,n)=>{let r=t.consumeArg().tokens;if(r.length!==1)throw new f("\\newcommand's first argument must be a macro name");const s=r[0].text,o=t.isDefined(s);if(o&&!e)throw new f("\\newcommand{"+s+"} attempting to redefine "+(s+"; use \\renewcommand"));if(!o&&!n)throw new f("\\renewcommand{"+s+"} when command "+s+" does not yet exist; use \\newcommand");let l=0;if(r=t.consumeArg().tokens,r.length===1&&r[0].text==="["){let d="",g=t.expandNextToken();for(;g.text!=="]"&&g.text!=="EOF";)d+=g.text,g=t.expandNextToken();if(!d.match(/^\s*[0-9]+\s*$/))throw new f("Invalid number of arguments: "+d);l=parseInt(d),r=t.consumeArg().tokens}return t.macros.set(s,{tokens:r,numArgs:l}),""};p("\\newcommand",t=>On(t,!1,!0)),p("\\renewcommand",t=>On(t,!0,!1)),p("\\providecommand",t=>On(t,!0,!0)),p("\\message",t=>{const e=t.consumeArgs(1)[0];return console.log(e.reverse().map(n=>n.text).join("")),""}),p("\\errmessage",t=>{const e=t.consumeArgs(1)[0];return console.error(e.reverse().map(n=>n.text).join("")),""}),p("\\show",t=>{const e=t.popToken(),n=e.text;return console.log(e,t.macros.get(n),O0[n],be.math[n],be.text[n]),""}),p("\\bgroup","{"),p("\\egroup","}"),p("~","\\nobreakspace"),p("\\lq","`"),p("\\rq","'"),p("\\aa","\\r a"),p("\\AA","\\r A"),p("\\textcopyright","\\html@mathml{\\textcircled{c}}{\\char`©}"),p("\\copyright","\\TextOrMath{\\textcopyright}{\\text{\\textcopyright}}"),p("\\textregistered","\\html@mathml{\\textcircled{\\scriptsize R}}{\\char`®}"),p("ℬ","\\mathscr{B}"),p("ℰ","\\mathscr{E}"),p("ℱ","\\mathscr{F}"),p("ℋ","\\mathscr{H}"),p("ℐ","\\mathscr{I}"),p("ℒ","\\mathscr{L}"),p("ℳ","\\mathscr{M}"),p("ℛ","\\mathscr{R}"),p("ℭ","\\mathfrak{C}"),p("ℌ","\\mathfrak{H}"),p("ℨ","\\mathfrak{Z}"),p("\\Bbbk","\\Bbb{k}"),p("·","\\cdotp"),p("\\llap","\\mathllap{\\textrm{#1}}"),p("\\rlap","\\mathrlap{\\textrm{#1}}"),p("\\clap","\\mathclap{\\textrm{#1}}"),p("\\mathstrut","\\vphantom{(}"),p("\\underbar","\\underline{\\text{#1}}"),p("\\not",'\\html@mathml{\\mathrel{\\mathrlap\\@not}}{\\char"338}'),p("\\neq","\\html@mathml{\\mathrel{\\not=}}{\\mathrel{\\char`≠}}"),p("\\ne","\\neq"),p("≠","\\neq"),p("\\notin","\\html@mathml{\\mathrel{{\\in}\\mathllap{/\\mskip1mu}}}{\\mathrel{\\char`∉}}"),p("∉","\\notin"),p("≘","\\html@mathml{\\mathrel{=\\kern{-1em}\\raisebox{0.4em}{$\\scriptsize\\frown$}}}{\\mathrel{\\char`≘}}"),p("≙","\\html@mathml{\\stackrel{\\tiny\\wedge}{=}}{\\mathrel{\\char`≘}}"),p("≚","\\html@mathml{\\stackrel{\\tiny\\vee}{=}}{\\mathrel{\\char`≚}}"),p("≛","\\html@mathml{\\stackrel{\\scriptsize\\star}{=}}{\\mathrel{\\char`≛}}"),p("≝","\\html@mathml{\\stackrel{\\tiny\\mathrm{def}}{=}}{\\mathrel{\\char`≝}}"),p("≞","\\html@mathml{\\stackrel{\\tiny\\mathrm{m}}{=}}{\\mathrel{\\char`≞}}"),p("≟","\\html@mathml{\\stackrel{\\tiny?}{=}}{\\mathrel{\\char`≟}}"),p("⟂","\\perp"),p("‼","\\mathclose{!\\mkern-0.8mu!}"),p("∌","\\notni"),p("⌜","\\ulcorner"),p("⌝","\\urcorner"),p("⌞","\\llcorner"),p("⌟","\\lrcorner"),p("©","\\copyright"),p("®","\\textregistered"),p("️","\\textregistered"),p("\\ulcorner",'\\html@mathml{\\@ulcorner}{\\mathop{\\char"231c}}'),p("\\urcorner",'\\html@mathml{\\@urcorner}{\\mathop{\\char"231d}}'),p("\\llcorner",'\\html@mathml{\\@llcorner}{\\mathop{\\char"231e}}'),p("\\lrcorner",'\\html@mathml{\\@lrcorner}{\\mathop{\\char"231f}}'),p("\\vdots","\\mathord{\\varvdots\\rule{0pt}{15pt}}"),p("⋮","\\vdots"),p("\\varGamma","\\mathit{\\Gamma}"),p("\\varDelta","\\mathit{\\Delta}"),p("\\varTheta","\\mathit{\\Theta}"),p("\\varLambda","\\mathit{\\Lambda}"),p("\\varXi","\\mathit{\\Xi}"),p("\\varPi","\\mathit{\\Pi}"),p("\\varSigma","\\mathit{\\Sigma}"),p("\\varUpsilon","\\mathit{\\Upsilon}"),p("\\varPhi","\\mathit{\\Phi}"),p("\\varPsi","\\mathit{\\Psi}"),p("\\varOmega","\\mathit{\\Omega}"),p("\\substack","\\begin{subarray}{c}#1\\end{subarray}"),p("\\colon","\\nobreak\\mskip2mu\\mathpunct{}\\mathchoice{\\mkern-3mu}{\\mkern-3mu}{}{}{:}\\mskip6mu\\relax"),p("\\boxed","\\fbox{$\\displaystyle{#1}$}"),p("\\iff","\\DOTSB\\;\\Longleftrightarrow\\;"),p("\\implies","\\DOTSB\\;\\Longrightarrow\\;"),p("\\impliedby","\\DOTSB\\;\\Longleftarrow\\;");const ms={",":"\\dotsc","\\not":"\\dotsb","+":"\\dotsb","=":"\\dotsb","<":"\\dotsb",">":"\\dotsb","-":"\\dotsb","*":"\\dotsb",":":"\\dotsb","\\DOTSB":"\\dotsb","\\coprod":"\\dotsb","\\bigvee":"\\dotsb","\\bigwedge":"\\dotsb","\\biguplus":"\\dotsb","\\bigcap":"\\dotsb","\\bigcup":"\\dotsb","\\prod":"\\dotsb","\\sum":"\\dotsb","\\bigotimes":"\\dotsb","\\bigoplus":"\\dotsb","\\bigodot":"\\dotsb","\\bigsqcup":"\\dotsb","\\And":"\\dotsb","\\longrightarrow":"\\dotsb","\\Longrightarrow":"\\dotsb","\\longleftarrow":"\\dotsb","\\Longleftarrow":"\\dotsb","\\longleftrightarrow":"\\dotsb","\\Longleftrightarrow":"\\dotsb","\\mapsto":"\\dotsb","\\longmapsto":"\\dotsb","\\hookrightarrow":"\\dotsb","\\doteq":"\\dotsb","\\mathbin":"\\dotsb","\\mathrel":"\\dotsb","\\relbar":"\\dotsb","\\Relbar":"\\dotsb","\\xrightarrow":"\\dotsb","\\xleftarrow":"\\dotsb","\\DOTSI":"\\dotsi","\\int":"\\dotsi","\\oint":"\\dotsi","\\iint":"\\dotsi","\\iiint":"\\dotsi","\\iiiint":"\\dotsi","\\idotsint":"\\dotsi","\\DOTSX":"\\dotsx"};p("\\dots",function(t){let e="\\dotso";const n=t.expandAfterFuture().text;return n in ms?e=ms[n]:(n.slice(0,4)==="\\not"||n in be.math&&N.contains(["bin","rel"],be.math[n].group))&&(e="\\dotsb"),e});const Ln={")":!0,"]":!0,"\\rbrack":!0,"\\}":!0,"\\rbrace":!0,"\\rangle":!0,"\\rceil":!0,"\\rfloor":!0,"\\rgroup":!0,"\\rmoustache":!0,"\\right":!0,"\\bigr":!0,"\\biggr":!0,"\\Bigr":!0,"\\Biggr":!0,$:!0,";":!0,".":!0,",":!0};p("\\dotso",function(t){return t.future().text in Ln?"\\ldots\\,":"\\ldots"}),p("\\dotsc",function(t){const e=t.future().text;return e in Ln&&e!==","?"\\ldots\\,":"\\ldots"}),p("\\cdots",function(t){return t.future().text in Ln?"\\@cdots\\,":"\\@cdots"}),p("\\dotsb","\\cdots"),p("\\dotsm","\\cdots"),p("\\dotsi","\\!\\cdots"),p("\\dotsx","\\ldots\\,"),p("\\DOTSI","\\relax"),p("\\DOTSB","\\relax"),p("\\DOTSX","\\relax"),p("\\tmspace","\\TextOrMath{\\kern#1#3}{\\mskip#1#2}\\relax"),p("\\,","\\tmspace+{3mu}{.1667em}"),p("\\thinspace","\\,"),p("\\>","\\mskip{4mu}"),p("\\:","\\tmspace+{4mu}{.2222em}"),p("\\medspace","\\:"),p("\\;","\\tmspace+{5mu}{.2777em}"),p("\\thickspace","\\;"),p("\\!","\\tmspace-{3mu}{.1667em}"),p("\\negthinspace","\\!"),p("\\negmedspace","\\tmspace-{4mu}{.2222em}"),p("\\negthickspace","\\tmspace-{5mu}{.277em}"),p("\\enspace","\\kern.5em "),p("\\enskip","\\hskip.5em\\relax"),p("\\quad","\\hskip1em\\relax"),p("\\qquad","\\hskip2em\\relax"),p("\\tag","\\@ifstar\\tag@literal\\tag@paren"),p("\\tag@paren","\\tag@literal{({#1})}"),p("\\tag@literal",t=>{if(t.macros.get("\\df@tag"))throw new f("Multiple \\tag");return"\\gdef\\df@tag{\\text{#1}}"}),p("\\bmod","\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}\\mathbin{\\rm mod}\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}"),p("\\pod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern8mu}{\\mkern8mu}{\\mkern8mu}(#1)"),p("\\pmod","\\pod{{\\rm mod}\\mkern6mu#1}"),p("\\mod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern12mu}{\\mkern12mu}{\\mkern12mu}{\\rm mod}\\,\\,#1"),p("\\newline","\\\\\\relax"),p("\\TeX","\\textrm{\\html@mathml{T\\kern-.1667em\\raisebox{-.5ex}{E}\\kern-.125emX}{TeX}}");const ds=I(Je["Main-Regular"]["T".charCodeAt(0)][1]-.7*Je["Main-Regular"]["A".charCodeAt(0)][1]);p("\\LaTeX","\\textrm{\\html@mathml{"+("L\\kern-.36em\\raisebox{"+ds+"}{\\scriptstyle A}")+"\\kern-.15em\\TeX}{LaTeX}}"),p("\\KaTeX","\\textrm{\\html@mathml{"+("K\\kern-.17em\\raisebox{"+ds+"}{\\scriptstyle A}")+"\\kern-.15em\\TeX}{KaTeX}}"),p("\\hspace","\\@ifstar\\@hspacer\\@hspace"),p("\\@hspace","\\hskip #1\\relax"),p("\\@hspacer","\\rule{0pt}{0pt}\\hskip #1\\relax"),p("\\ordinarycolon",":"),p("\\vcentcolon","\\mathrel{\\mathop\\ordinarycolon}"),p("\\dblcolon",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-.9mu}\\vcentcolon}}{\\mathop{\\char"2237}}'),p("\\coloneqq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2254}}'),p("\\Coloneqq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2237\\char"3d}}'),p("\\coloneq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"3a\\char"2212}}'),p("\\Coloneq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"2237\\char"2212}}'),p("\\eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2255}}'),p("\\Eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"3d\\char"2237}}'),p("\\eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2239}}'),p("\\Eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"2212\\char"2237}}'),p("\\colonapprox",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"3a\\char"2248}}'),p("\\Colonapprox",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"2237\\char"2248}}'),p("\\colonsim",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"3a\\char"223c}}'),p("\\Colonsim",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"2237\\char"223c}}'),p("∷","\\dblcolon"),p("∹","\\eqcolon"),p("≔","\\coloneqq"),p("≕","\\eqqcolon"),p("⩴","\\Coloneqq"),p("\\ratio","\\vcentcolon"),p("\\coloncolon","\\dblcolon"),p("\\colonequals","\\coloneqq"),p("\\coloncolonequals","\\Coloneqq"),p("\\equalscolon","\\eqqcolon"),p("\\equalscoloncolon","\\Eqqcolon"),p("\\colonminus","\\coloneq"),p("\\coloncolonminus","\\Coloneq"),p("\\minuscolon","\\eqcolon"),p("\\minuscoloncolon","\\Eqcolon"),p("\\coloncolonapprox","\\Colonapprox"),p("\\coloncolonsim","\\Colonsim"),p("\\simcolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),p("\\simcoloncolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\dblcolon}"),p("\\approxcolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),p("\\approxcoloncolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\dblcolon}"),p("\\notni","\\html@mathml{\\not\\ni}{\\mathrel{\\char`∌}}"),p("\\limsup","\\DOTSB\\operatorname*{lim\\,sup}"),p("\\liminf","\\DOTSB\\operatorname*{lim\\,inf}"),p("\\injlim","\\DOTSB\\operatorname*{inj\\,lim}"),p("\\projlim","\\DOTSB\\operatorname*{proj\\,lim}"),p("\\varlimsup","\\DOTSB\\operatorname*{\\overline{lim}}"),p("\\varliminf","\\DOTSB\\operatorname*{\\underline{lim}}"),p("\\varinjlim","\\DOTSB\\operatorname*{\\underrightarrow{lim}}"),p("\\varprojlim","\\DOTSB\\operatorname*{\\underleftarrow{lim}}"),p("\\gvertneqq","\\html@mathml{\\@gvertneqq}{≩}"),p("\\lvertneqq","\\html@mathml{\\@lvertneqq}{≨}"),p("\\ngeqq","\\html@mathml{\\@ngeqq}{≱}"),p("\\ngeqslant","\\html@mathml{\\@ngeqslant}{≱}"),p("\\nleqq","\\html@mathml{\\@nleqq}{≰}"),p("\\nleqslant","\\html@mathml{\\@nleqslant}{≰}"),p("\\nshortmid","\\html@mathml{\\@nshortmid}{∤}"),p("\\nshortparallel","\\html@mathml{\\@nshortparallel}{∦}"),p("\\nsubseteqq","\\html@mathml{\\@nsubseteqq}{⊈}"),p("\\nsupseteqq","\\html@mathml{\\@nsupseteqq}{⊉}"),p("\\varsubsetneq","\\html@mathml{\\@varsubsetneq}{⊊}"),p("\\varsubsetneqq","\\html@mathml{\\@varsubsetneqq}{⫋}"),p("\\varsupsetneq","\\html@mathml{\\@varsupsetneq}{⊋}"),p("\\varsupsetneqq","\\html@mathml{\\@varsupsetneqq}{⫌}"),p("\\imath","\\html@mathml{\\@imath}{ı}"),p("\\jmath","\\html@mathml{\\@jmath}{ȷ}"),p("\\llbracket","\\html@mathml{\\mathopen{[\\mkern-3.2mu[}}{\\mathopen{\\char`⟦}}"),p("\\rrbracket","\\html@mathml{\\mathclose{]\\mkern-3.2mu]}}{\\mathclose{\\char`⟧}}"),p("⟦","\\llbracket"),p("⟧","\\rrbracket"),p("\\lBrace","\\html@mathml{\\mathopen{\\{\\mkern-3.2mu[}}{\\mathopen{\\char`⦃}}"),p("\\rBrace","\\html@mathml{\\mathclose{]\\mkern-3.2mu\\}}}{\\mathclose{\\char`⦄}}"),p("⦃","\\lBrace"),p("⦄","\\rBrace"),p("\\minuso","\\mathbin{\\html@mathml{{\\mathrlap{\\mathchoice{\\kern{0.145em}}{\\kern{0.145em}}{\\kern{0.1015em}}{\\kern{0.0725em}}\\circ}{-}}}{\\char`⦵}}"),p("⦵","\\minuso"),p("\\darr","\\downarrow"),p("\\dArr","\\Downarrow"),p("\\Darr","\\Downarrow"),p("\\lang","\\langle"),p("\\rang","\\rangle"),p("\\uarr","\\uparrow"),p("\\uArr","\\Uparrow"),p("\\Uarr","\\Uparrow"),p("\\N","\\mathbb{N}"),p("\\R","\\mathbb{R}"),p("\\Z","\\mathbb{Z}"),p("\\alef","\\aleph"),p("\\alefsym","\\aleph"),p("\\Alpha","\\mathrm{A}"),p("\\Beta","\\mathrm{B}"),p("\\bull","\\bullet"),p("\\Chi","\\mathrm{X}"),p("\\clubs","\\clubsuit"),p("\\cnums","\\mathbb{C}"),p("\\Complex","\\mathbb{C}"),p("\\Dagger","\\ddagger"),p("\\diamonds","\\diamondsuit"),p("\\empty","\\emptyset"),p("\\Epsilon","\\mathrm{E}"),p("\\Eta","\\mathrm{H}"),p("\\exist","\\exists"),p("\\harr","\\leftrightarrow"),p("\\hArr","\\Leftrightarrow"),p("\\Harr","\\Leftrightarrow"),p("\\hearts","\\heartsuit"),p("\\image","\\Im"),p("\\infin","\\infty"),p("\\Iota","\\mathrm{I}"),p("\\isin","\\in"),p("\\Kappa","\\mathrm{K}"),p("\\larr","\\leftarrow"),p("\\lArr","\\Leftarrow"),p("\\Larr","\\Leftarrow"),p("\\lrarr","\\leftrightarrow"),p("\\lrArr","\\Leftrightarrow"),p("\\Lrarr","\\Leftrightarrow"),p("\\Mu","\\mathrm{M}"),p("\\natnums","\\mathbb{N}"),p("\\Nu","\\mathrm{N}"),p("\\Omicron","\\mathrm{O}"),p("\\plusmn","\\pm"),p("\\rarr","\\rightarrow"),p("\\rArr","\\Rightarrow"),p("\\Rarr","\\Rightarrow"),p("\\real","\\Re"),p("\\reals","\\mathbb{R}"),p("\\Reals","\\mathbb{R}"),p("\\Rho","\\mathrm{P}"),p("\\sdot","\\cdot"),p("\\sect","\\S"),p("\\spades","\\spadesuit"),p("\\sub","\\subset"),p("\\sube","\\subseteq"),p("\\supe","\\supseteq"),p("\\Tau","\\mathrm{T}"),p("\\thetasym","\\vartheta"),p("\\weierp","\\wp"),p("\\Zeta","\\mathrm{Z}"),p("\\argmin","\\DOTSB\\operatorname*{arg\\,min}"),p("\\argmax","\\DOTSB\\operatorname*{arg\\,max}"),p("\\plim","\\DOTSB\\mathop{\\operatorname{plim}}\\limits"),p("\\bra","\\mathinner{\\langle{#1}|}"),p("\\ket","\\mathinner{|{#1}\\rangle}"),p("\\braket","\\mathinner{\\langle{#1}\\rangle}"),p("\\Bra","\\left\\langle#1\\right|"),p("\\Ket","\\left|#1\\right\\rangle");const ps=t=>e=>{const n=e.consumeArg().tokens,r=e.consumeArg().tokens,s=e.consumeArg().tokens,o=e.consumeArg().tokens,l=e.macros.get("|"),d=e.macros.get("\\|");e.macros.beginGroup();const g=v=>T=>{t&&(T.macros.set("|",l),s.length&&T.macros.set("\\|",d));let _=v;return!v&&s.length&&T.future().text==="|"&&(T.popToken(),_=!0),{tokens:_?s:r,numArgs:0}};e.macros.set("|",g(!1)),s.length&&e.macros.set("\\|",g(!0));const k=e.consumeArg().tokens,S=e.expandTokens([...o,...k,...n]);return e.macros.endGroup(),{tokens:S.reverse(),numArgs:0}};p("\\bra@ket",ps(!1)),p("\\bra@set",ps(!0)),p("\\Braket","\\bra@ket{\\left\\langle}{\\,\\middle\\vert\\,}{\\,\\middle\\vert\\,}{\\right\\rangle}"),p("\\Set","\\bra@set{\\left\\{\\:}{\\;\\middle\\vert\\;}{\\;\\middle\\Vert\\;}{\\:\\right\\}}"),p("\\set","\\bra@set{\\{\\,}{\\mid}{}{\\,\\}}"),p("\\angln","{\\angl n}"),p("\\blue","\\textcolor{##6495ed}{#1}"),p("\\orange","\\textcolor{##ffa500}{#1}"),p("\\pink","\\textcolor{##ff00af}{#1}"),p("\\red","\\textcolor{##df0030}{#1}"),p("\\green","\\textcolor{##28ae7b}{#1}"),p("\\gray","\\textcolor{gray}{#1}"),p("\\purple","\\textcolor{##9d38bd}{#1}"),p("\\blueA","\\textcolor{##ccfaff}{#1}"),p("\\blueB","\\textcolor{##80f6ff}{#1}"),p("\\blueC","\\textcolor{##63d9ea}{#1}"),p("\\blueD","\\textcolor{##11accd}{#1}"),p("\\blueE","\\textcolor{##0c7f99}{#1}"),p("\\tealA","\\textcolor{##94fff5}{#1}"),p("\\tealB","\\textcolor{##26edd5}{#1}"),p("\\tealC","\\textcolor{##01d1c1}{#1}"),p("\\tealD","\\textcolor{##01a995}{#1}"),p("\\tealE","\\textcolor{##208170}{#1}"),p("\\greenA","\\textcolor{##b6ffb0}{#1}"),p("\\greenB","\\textcolor{##8af281}{#1}"),p("\\greenC","\\textcolor{##74cf70}{#1}"),p("\\greenD","\\textcolor{##1fab54}{#1}"),p("\\greenE","\\textcolor{##0d923f}{#1}"),p("\\goldA","\\textcolor{##ffd0a9}{#1}"),p("\\goldB","\\textcolor{##ffbb71}{#1}"),p("\\goldC","\\textcolor{##ff9c39}{#1}"),p("\\goldD","\\textcolor{##e07d10}{#1}"),p("\\goldE","\\textcolor{##a75a05}{#1}"),p("\\redA","\\textcolor{##fca9a9}{#1}"),p("\\redB","\\textcolor{##ff8482}{#1}"),p("\\redC","\\textcolor{##f9685d}{#1}"),p("\\redD","\\textcolor{##e84d39}{#1}"),p("\\redE","\\textcolor{##bc2612}{#1}"),p("\\maroonA","\\textcolor{##ffbde0}{#1}"),p("\\maroonB","\\textcolor{##ff92c6}{#1}"),p("\\maroonC","\\textcolor{##ed5fa6}{#1}"),p("\\maroonD","\\textcolor{##ca337c}{#1}"),p("\\maroonE","\\textcolor{##9e034e}{#1}"),p("\\purpleA","\\textcolor{##ddd7ff}{#1}"),p("\\purpleB","\\textcolor{##c6b9fc}{#1}"),p("\\purpleC","\\textcolor{##aa87ff}{#1}"),p("\\purpleD","\\textcolor{##7854ab}{#1}"),p("\\purpleE","\\textcolor{##543b78}{#1}"),p("\\mintA","\\textcolor{##f5f9e8}{#1}"),p("\\mintB","\\textcolor{##edf2df}{#1}"),p("\\mintC","\\textcolor{##e0e5cc}{#1}"),p("\\grayA","\\textcolor{##f6f7f7}{#1}"),p("\\grayB","\\textcolor{##f0f1f2}{#1}"),p("\\grayC","\\textcolor{##e3e5e6}{#1}"),p("\\grayD","\\textcolor{##d6d8da}{#1}"),p("\\grayE","\\textcolor{##babec2}{#1}"),p("\\grayF","\\textcolor{##888d93}{#1}"),p("\\grayG","\\textcolor{##626569}{#1}"),p("\\grayH","\\textcolor{##3b3e40}{#1}"),p("\\grayI","\\textcolor{##21242c}{#1}"),p("\\kaBlue","\\textcolor{##314453}{#1}"),p("\\kaGreen","\\textcolor{##71B307}{#1}");const fs={"^":!0,_:!0,"\\limits":!0,"\\nolimits":!0};class fa{constructor(e,n,r){this.settings=void 0,this.expansionCount=void 0,this.lexer=void 0,this.macros=void 0,this.stack=void 0,this.mode=void 0,this.settings=n,this.expansionCount=0,this.feed(e),this.macros=new da(pa,n.macros),this.mode=r,this.stack=[]}feed(e){this.lexer=new cs(e,this.settings)}switchMode(e){this.mode=e}beginGroup(){this.macros.beginGroup()}endGroup(){this.macros.endGroup()}endGroups(){this.macros.endGroups()}future(){return this.stack.length===0&&this.pushToken(this.lexer.lex()),this.stack[this.stack.length-1]}popToken(){return this.future(),this.stack.pop()}pushToken(e){this.stack.push(e)}pushTokens(e){this.stack.push(...e)}scanArgument(e){let n,r,s;if(e){if(this.consumeSpaces(),this.future().text!=="[")return null;n=this.popToken(),{tokens:s,end:r}=this.consumeArg(["]"])}else({tokens:s,start:n,end:r}=this.consumeArg());return this.pushToken(new s0("EOF",r.loc)),this.pushTokens(s),n.range(r,"")}consumeSpaces(){for(;this.future().text===" ";)this.stack.pop()}consumeArg(e){const n=[],r=e&&e.length>0;r||this.consumeSpaces();const s=this.future();let o,l=0,d=0;do{if(o=this.popToken(),n.push(o),o.text==="{")++l;else if(o.text==="}"){if(--l,l===-1)throw new f("Extra }",o)}else if(o.text==="EOF")throw new f("Unexpected end of input in a macro argument, expected '"+(e&&r?e[d]:"}")+"'",o);if(e&&r)if((l===0||l===1&&e[d]==="{")&&o.text===e[d]){if(++d,d===e.length){n.splice(-d,d);break}}else d=0}while(l!==0||r);return s.text==="{"&&n[n.length-1].text==="}"&&(n.pop(),n.shift()),n.reverse(),{tokens:n,start:s,end:o}}consumeArgs(e,n){if(n){if(n.length!==e+1)throw new f("The length of delimiters doesn't match the number of args!");const s=n[0];for(let o=0;o<s.length;o++){const l=this.popToken();if(s[o]!==l.text)throw new f("Use of the macro doesn't match its definition",l)}}const r=[];for(let s=0;s<e;s++)r.push(this.consumeArg(n&&n[s+1]).tokens);return r}countExpansion(e){if(this.expansionCount+=e,this.expansionCount>this.settings.maxExpand)throw new f("Too many expansions: infinite loop or need to increase maxExpand setting")}expandOnce(e){const n=this.popToken(),r=n.text,s=n.noexpand?null:this._getExpansion(r);if(s==null||e&&s.unexpandable){if(e&&s==null&&r[0]==="\\"&&!this.isDefined(r))throw new f("Undefined control sequence: "+r);return this.pushToken(n),!1}this.countExpansion(1);let o=s.tokens;const l=this.consumeArgs(s.numArgs,s.delimiters);if(s.numArgs){o=o.slice();for(let d=o.length-1;d>=0;--d){let g=o[d];if(g.text==="#"){if(d===0)throw new f("Incomplete placeholder at end of macro body",g);if(g=o[--d],g.text==="#")o.splice(d+1,1);else if(/^[1-9]$/.test(g.text))o.splice(d,2,...l[+g.text-1]);else throw new f("Not a valid argument number",g)}}}return this.pushTokens(o),o.length}expandAfterFuture(){return this.expandOnce(),this.future()}expandNextToken(){for(;;)if(this.expandOnce()===!1){const e=this.stack.pop();return e.treatAsRelax&&(e.text="\\relax"),e}throw new Error}expandMacro(e){return this.macros.has(e)?this.expandTokens([new s0(e)]):void 0}expandTokens(e){const n=[],r=this.stack.length;for(this.pushTokens(e);this.stack.length>r;)if(this.expandOnce(!0)===!1){const s=this.stack.pop();s.treatAsRelax&&(s.noexpand=!1,s.treatAsRelax=!1),n.push(s)}return this.countExpansion(n.length),n}expandMacroAsText(e){const n=this.expandMacro(e);return n&&n.map(r=>r.text).join("")}_getExpansion(e){const n=this.macros.get(e);if(n==null)return n;if(e.length===1){const s=this.lexer.catcodes[e];if(s!=null&&s!==13)return}const r=typeof n=="function"?n(this):n;if(typeof r=="string"){let s=0;if(r.indexOf("#")!==-1){const k=r.replace(/##/g,"");for(;k.indexOf("#"+(s+1))!==-1;)++s}const o=new cs(r,this.settings),l=[];let d=o.lex();for(;d.text!=="EOF";)l.push(d),d=o.lex();return l.reverse(),{tokens:l,numArgs:s}}return r}isDefined(e){return this.macros.has(e)||O0.hasOwnProperty(e)||be.math.hasOwnProperty(e)||be.text.hasOwnProperty(e)||fs.hasOwnProperty(e)}isExpandable(e){const n=this.macros.get(e);return n!=null?typeof n=="string"||typeof n=="function"||!n.unexpandable:O0.hasOwnProperty(e)&&!O0[e].primitive}}const gs=/^[₊₋₌₍₎₀₁₂₃₄₅₆₇₈₉ₐₑₕᵢⱼₖₗₘₙₒₚᵣₛₜᵤᵥₓᵦᵧᵨᵩᵪ]/,Wt=Object.freeze({"₊":"+","₋":"-","₌":"=","₍":"(","₎":")","₀":"0","₁":"1","₂":"2","₃":"3","₄":"4","₅":"5","₆":"6","₇":"7","₈":"8","₉":"9","ₐ":"a","ₑ":"e","ₕ":"h","ᵢ":"i","ⱼ":"j","ₖ":"k","ₗ":"l","ₘ":"m","ₙ":"n","ₒ":"o","ₚ":"p","ᵣ":"r","ₛ":"s","ₜ":"t","ᵤ":"u","ᵥ":"v","ₓ":"x","ᵦ":"β","ᵧ":"γ","ᵨ":"ρ","ᵩ":"ϕ","ᵪ":"χ","⁺":"+","⁻":"-","⁼":"=","⁽":"(","⁾":")","⁰":"0","¹":"1","²":"2","³":"3","⁴":"4","⁵":"5","⁶":"6","⁷":"7","⁸":"8","⁹":"9","ᴬ":"A","ᴮ":"B","ᴰ":"D","ᴱ":"E","ᴳ":"G","ᴴ":"H","ᴵ":"I","ᴶ":"J","ᴷ":"K","ᴸ":"L","ᴹ":"M","ᴺ":"N","ᴼ":"O","ᴾ":"P","ᴿ":"R","ᵀ":"T","ᵁ":"U","ⱽ":"V","ᵂ":"W","ᵃ":"a","ᵇ":"b","ᶜ":"c","ᵈ":"d","ᵉ":"e","ᶠ":"f","ᵍ":"g",ʰ:"h","ⁱ":"i",ʲ:"j","ᵏ":"k",ˡ:"l","ᵐ":"m",ⁿ:"n","ᵒ":"o","ᵖ":"p",ʳ:"r",ˢ:"s","ᵗ":"t","ᵘ":"u","ᵛ":"v",ʷ:"w",ˣ:"x",ʸ:"y","ᶻ":"z","ᵝ":"β","ᵞ":"γ","ᵟ":"δ","ᵠ":"ϕ","ᵡ":"χ","ᶿ":"θ"}),Hn={"́":{text:"\\'",math:"\\acute"},"̀":{text:"\\`",math:"\\grave"},"̈":{text:'\\"',math:"\\ddot"},"̃":{text:"\\~",math:"\\tilde"},"̄":{text:"\\=",math:"\\bar"},"̆":{text:"\\u",math:"\\breve"},"̌":{text:"\\v",math:"\\check"},"̂":{text:"\\^",math:"\\hat"},"̇":{text:"\\.",math:"\\dot"},"̊":{text:"\\r",math:"\\mathring"},"̋":{text:"\\H"},"̧":{text:"\\c"}},bs={á:"á",à:"à",ä:"ä",ǟ:"ǟ",ã:"ã",ā:"ā",ă:"ă",ắ:"ắ",ằ:"ằ",ẵ:"ẵ",ǎ:"ǎ",â:"â",ấ:"ấ",ầ:"ầ",ẫ:"ẫ",ȧ:"ȧ",ǡ:"ǡ",å:"å",ǻ:"ǻ",ḃ:"ḃ",ć:"ć",ḉ:"ḉ",č:"č",ĉ:"ĉ",ċ:"ċ",ç:"ç",ď:"ď",ḋ:"ḋ",ḑ:"ḑ",é:"é",è:"è",ë:"ë",ẽ:"ẽ",ē:"ē",ḗ:"ḗ",ḕ:"ḕ",ĕ:"ĕ",ḝ:"ḝ",ě:"ě",ê:"ê",ế:"ế",ề:"ề",ễ:"ễ",ė:"ė",ȩ:"ȩ",ḟ:"ḟ",ǵ:"ǵ",ḡ:"ḡ",ğ:"ğ",ǧ:"ǧ",ĝ:"ĝ",ġ:"ġ",ģ:"ģ",ḧ:"ḧ",ȟ:"ȟ",ĥ:"ĥ",ḣ:"ḣ",ḩ:"ḩ",í:"í",ì:"ì",ï:"ï",ḯ:"ḯ",ĩ:"ĩ",ī:"ī",ĭ:"ĭ",ǐ:"ǐ",î:"î",ǰ:"ǰ",ĵ:"ĵ",ḱ:"ḱ",ǩ:"ǩ",ķ:"ķ",ĺ:"ĺ",ľ:"ľ",ļ:"ļ",ḿ:"ḿ",ṁ:"ṁ",ń:"ń",ǹ:"ǹ",ñ:"ñ",ň:"ň",ṅ:"ṅ",ņ:"ņ",ó:"ó",ò:"ò",ö:"ö",ȫ:"ȫ",õ:"õ",ṍ:"ṍ",ṏ:"ṏ",ȭ:"ȭ",ō:"ō",ṓ:"ṓ",ṑ:"ṑ",ŏ:"ŏ",ǒ:"ǒ",ô:"ô",ố:"ố",ồ:"ồ",ỗ:"ỗ",ȯ:"ȯ",ȱ:"ȱ",ő:"ő",ṕ:"ṕ",ṗ:"ṗ",ŕ:"ŕ",ř:"ř",ṙ:"ṙ",ŗ:"ŗ",ś:"ś",ṥ:"ṥ",š:"š",ṧ:"ṧ",ŝ:"ŝ",ṡ:"ṡ",ş:"ş",ẗ:"ẗ",ť:"ť",ṫ:"ṫ",ţ:"ţ",ú:"ú",ù:"ù",ü:"ü",ǘ:"ǘ",ǜ:"ǜ",ǖ:"ǖ",ǚ:"ǚ",ũ:"ũ",ṹ:"ṹ",ū:"ū",ṻ:"ṻ",ŭ:"ŭ",ǔ:"ǔ",û:"û",ů:"ů",ű:"ű",ṽ:"ṽ",ẃ:"ẃ",ẁ:"ẁ",ẅ:"ẅ",ŵ:"ŵ",ẇ:"ẇ",ẘ:"ẘ",ẍ:"ẍ",ẋ:"ẋ",ý:"ý",ỳ:"ỳ",ÿ:"ÿ",ỹ:"ỹ",ȳ:"ȳ",ŷ:"ŷ",ẏ:"ẏ",ẙ:"ẙ",ź:"ź",ž:"ž",ẑ:"ẑ",ż:"ż",Á:"Á",À:"À",Ä:"Ä",Ǟ:"Ǟ",Ã:"Ã",Ā:"Ā",Ă:"Ă",Ắ:"Ắ",Ằ:"Ằ",Ẵ:"Ẵ",Ǎ:"Ǎ",Â:"Â",Ấ:"Ấ",Ầ:"Ầ",Ẫ:"Ẫ",Ȧ:"Ȧ",Ǡ:"Ǡ",Å:"Å",Ǻ:"Ǻ",Ḃ:"Ḃ",Ć:"Ć",Ḉ:"Ḉ",Č:"Č",Ĉ:"Ĉ",Ċ:"Ċ",Ç:"Ç",Ď:"Ď",Ḋ:"Ḋ",Ḑ:"Ḑ",É:"É",È:"È",Ë:"Ë",Ẽ:"Ẽ",Ē:"Ē",Ḗ:"Ḗ",Ḕ:"Ḕ",Ĕ:"Ĕ",Ḝ:"Ḝ",Ě:"Ě",Ê:"Ê",Ế:"Ế",Ề:"Ề",Ễ:"Ễ",Ė:"Ė",Ȩ:"Ȩ",Ḟ:"Ḟ",Ǵ:"Ǵ",Ḡ:"Ḡ",Ğ:"Ğ",Ǧ:"Ǧ",Ĝ:"Ĝ",Ġ:"Ġ",Ģ:"Ģ",Ḧ:"Ḧ",Ȟ:"Ȟ",Ĥ:"Ĥ",Ḣ:"Ḣ",Ḩ:"Ḩ",Í:"Í",Ì:"Ì",Ï:"Ï",Ḯ:"Ḯ",Ĩ:"Ĩ",Ī:"Ī",Ĭ:"Ĭ",Ǐ:"Ǐ",Î:"Î",İ:"İ",Ĵ:"Ĵ",Ḱ:"Ḱ",Ǩ:"Ǩ",Ķ:"Ķ",Ĺ:"Ĺ",Ľ:"Ľ",Ļ:"Ļ",Ḿ:"Ḿ",Ṁ:"Ṁ",Ń:"Ń",Ǹ:"Ǹ",Ñ:"Ñ",Ň:"Ň",Ṅ:"Ṅ",Ņ:"Ņ",Ó:"Ó",Ò:"Ò",Ö:"Ö",Ȫ:"Ȫ",Õ:"Õ",Ṍ:"Ṍ",Ṏ:"Ṏ",Ȭ:"Ȭ",Ō:"Ō",Ṓ:"Ṓ",Ṑ:"Ṑ",Ŏ:"Ŏ",Ǒ:"Ǒ",Ô:"Ô",Ố:"Ố",Ồ:"Ồ",Ỗ:"Ỗ",Ȯ:"Ȯ",Ȱ:"Ȱ",Ő:"Ő",Ṕ:"Ṕ",Ṗ:"Ṗ",Ŕ:"Ŕ",Ř:"Ř",Ṙ:"Ṙ",Ŗ:"Ŗ",Ś:"Ś",Ṥ:"Ṥ",Š:"Š",Ṧ:"Ṧ",Ŝ:"Ŝ",Ṡ:"Ṡ",Ş:"Ş",Ť:"Ť",Ṫ:"Ṫ",Ţ:"Ţ",Ú:"Ú",Ù:"Ù",Ü:"Ü",Ǘ:"Ǘ",Ǜ:"Ǜ",Ǖ:"Ǖ",Ǚ:"Ǚ",Ũ:"Ũ",Ṹ:"Ṹ",Ū:"Ū",Ṻ:"Ṻ",Ŭ:"Ŭ",Ǔ:"Ǔ",Û:"Û",Ů:"Ů",Ű:"Ű",Ṽ:"Ṽ",Ẃ:"Ẃ",Ẁ:"Ẁ",Ẅ:"Ẅ",Ŵ:"Ŵ",Ẇ:"Ẇ",Ẍ:"Ẍ",Ẋ:"Ẋ",Ý:"Ý",Ỳ:"Ỳ",Ÿ:"Ÿ",Ỹ:"Ỹ",Ȳ:"Ȳ",Ŷ:"Ŷ",Ẏ:"Ẏ",Ź:"Ź",Ž:"Ž",Ẑ:"Ẑ",Ż:"Ż",ά:"ά",ὰ:"ὰ",ᾱ:"ᾱ",ᾰ:"ᾰ",έ:"έ",ὲ:"ὲ",ή:"ή",ὴ:"ὴ",ί:"ί",ὶ:"ὶ",ϊ:"ϊ",ΐ:"ΐ",ῒ:"ῒ",ῑ:"ῑ",ῐ:"ῐ",ό:"ό",ὸ:"ὸ",ύ:"ύ",ὺ:"ὺ",ϋ:"ϋ",ΰ:"ΰ",ῢ:"ῢ",ῡ:"ῡ",ῠ:"ῠ",ώ:"ώ",ὼ:"ὼ",Ύ:"Ύ",Ὺ:"Ὺ",Ϋ:"Ϋ",Ῡ:"Ῡ",Ῠ:"Ῠ",Ώ:"Ώ",Ὼ:"Ὼ"};class Yt{constructor(e,n){this.mode=void 0,this.gullet=void 0,this.settings=void 0,this.leftrightDepth=void 0,this.nextToken=void 0,this.mode="math",this.gullet=new fa(e,n,this.mode),this.settings=n,this.leftrightDepth=0}expect(e,n){if(n===void 0&&(n=!0),this.fetch().text!==e)throw new f("Expected '"+e+"', got '"+this.fetch().text+"'",this.fetch());n&&this.consume()}consume(){this.nextToken=null}fetch(){return this.nextToken==null&&(this.nextToken=this.gullet.expandNextToken()),this.nextToken}switchMode(e){this.mode=e,this.gullet.switchMode(e)}parse(){this.settings.globalGroup||this.gullet.beginGroup(),this.settings.colorIsTextColor&&this.gullet.macros.set("\\color","\\textcolor");try{const e=this.parseExpression(!1);return this.expect("EOF"),this.settings.globalGroup||this.gullet.endGroup(),e}finally{this.gullet.endGroups()}}subparse(e){const n=this.nextToken;this.consume(),this.gullet.pushToken(new s0("}")),this.gullet.pushTokens(e);const r=this.parseExpression(!1);return this.expect("}"),this.nextToken=n,r}parseExpression(e,n){const r=[];for(;;){this.mode==="math"&&this.consumeSpaces();const s=this.fetch();if(Yt.endOfExpression.indexOf(s.text)!==-1||n&&s.text===n||e&&O0[s.text]&&O0[s.text].infix)break;const o=this.parseAtom(n);if(o){if(o.type==="internal")continue}else break;r.push(o)}return this.mode==="text"&&this.formLigatures(r),this.handleInfixNodes(r)}handleInfixNodes(e){let n=-1,r;for(let s=0;s<e.length;s++)if(e[s].type==="infix"){if(n!==-1)throw new f("only one infix operator per group",e[s].token);n=s,r=e[s].replaceWith}if(n!==-1&&r){let s,o;const l=e.slice(0,n),d=e.slice(n+1);l.length===1&&l[0].type==="ordgroup"?s=l[0]:s={type:"ordgroup",mode:this.mode,body:l},d.length===1&&d[0].type==="ordgroup"?o=d[0]:o={type:"ordgroup",mode:this.mode,body:d};let g;return r==="\\\\abovefrac"?g=this.callFunction(r,[s,e[n],o],[]):g=this.callFunction(r,[s,o],[]),[g]}else return e}handleSupSubscript(e){const n=this.fetch(),r=n.text;this.consume(),this.consumeSpaces();const s=this.parseGroup(e);if(!s)throw new f("Expected group after '"+r+"'",n);return s}formatUnsupportedCmd(e){const n=[];for(let o=0;o<e.length;o++)n.push({type:"textord",mode:"text",text:e[o]});const r={type:"text",mode:this.mode,body:n};return{type:"color",mode:this.mode,color:this.settings.errorColor,body:[r]}}parseAtom(e){const n=this.parseGroup("atom",e);if(this.mode==="text")return n;let r,s;for(;;){this.consumeSpaces();const o=this.fetch();if(o.text==="\\limits"||o.text==="\\nolimits"){if(n&&n.type==="op"){const l=o.text==="\\limits";n.limits=l,n.alwaysHandleSupSub=!0}else if(n&&n.type==="operatorname")n.alwaysHandleSupSub&&(n.limits=o.text==="\\limits");else throw new f("Limit controls must follow a math operator",o);this.consume()}else if(o.text==="^"){if(r)throw new f("Double superscript",o);r=this.handleSupSubscript("superscript")}else if(o.text==="_"){if(s)throw new f("Double subscript",o);s=this.handleSupSubscript("subscript")}else if(o.text==="'"){if(r)throw new f("Double superscript",o);const l={type:"textord",mode:this.mode,text:"\\prime"},d=[l];for(this.consume();this.fetch().text==="'";)d.push(l),this.consume();this.fetch().text==="^"&&d.push(this.handleSupSubscript("superscript")),r={type:"ordgroup",mode:this.mode,body:d}}else if(Wt[o.text]){const l=gs.test(o.text),d=[];for(d.push(new s0(Wt[o.text])),this.consume();;){const k=this.fetch().text;if(!Wt[k]||gs.test(k)!==l)break;d.unshift(new s0(Wt[k])),this.consume()}const g=this.subparse(d);l?s={type:"ordgroup",mode:"math",body:g}:r={type:"ordgroup",mode:"math",body:g}}else break}return r||s?{type:"supsub",mode:this.mode,base:n,sup:r,sub:s}:n}parseFunction(e,n){const r=this.fetch(),s=r.text,o=O0[s];if(!o)return null;if(this.consume(),n&&n!=="atom"&&!o.allowedInArgument)throw new f("Got function '"+s+"' with no arguments"+(n?" as "+n:""),r);if(this.mode==="text"&&!o.allowedInText)throw new f("Can't use function '"+s+"' in text mode",r);if(this.mode==="math"&&o.allowedInMath===!1)throw new f("Can't use function '"+s+"' in math mode",r);const{args:l,optArgs:d}=this.parseArguments(s,o);return this.callFunction(s,l,d,r,e)}callFunction(e,n,r,s,o){const l={funcName:e,parser:this,token:s,breakOnTokenText:o},d=O0[e];if(d&&d.handler)return d.handler(l,n,r);throw new f("No function handler for "+e)}parseArguments(e,n){const r=n.numArgs+n.numOptionalArgs;if(r===0)return{args:[],optArgs:[]};const s=[],o=[];for(let l=0;l<r;l++){let d=n.argTypes&&n.argTypes[l];const g=l<n.numOptionalArgs;(n.primitive&&d==null||n.type==="sqrt"&&l===1&&o[0]==null)&&(d="primitive");const k=this.parseGroupOfType("argument to '"+e+"'",d,g);if(g)o.push(k);else if(k!=null)s.push(k);else throw new f("Null argument, please report this as a bug")}return{args:s,optArgs:o}}parseGroupOfType(e,n,r){switch(n){case"color":return this.parseColorGroup(r);case"size":return this.parseSizeGroup(r);case"url":return this.parseUrlGroup(r);case"math":case"text":return this.parseArgumentGroup(r,n);case"hbox":{const s=this.parseArgumentGroup(r,"text");return s!=null?{type:"styling",mode:s.mode,body:[s],style:"text"}:null}case"raw":{const s=this.parseStringGroup("raw",r);return s!=null?{type:"raw",mode:"text",string:s.text}:null}case"primitive":{if(r)throw new f("A primitive argument cannot be optional");const s=this.parseGroup(e);if(s==null)throw new f("Expected group as "+e,this.fetch());return s}case"original":case null:case void 0:return this.parseArgumentGroup(r);default:throw new f("Unknown group type as "+e,this.fetch())}}consumeSpaces(){for(;this.fetch().text===" ";)this.consume()}parseStringGroup(e,n){const r=this.gullet.scanArgument(n);if(r==null)return null;let s="",o;for(;(o=this.fetch()).text!=="EOF";)s+=o.text,this.consume();return this.consume(),r.text=s,r}parseRegexGroup(e,n){const r=this.fetch();let s=r,o="",l;for(;(l=this.fetch()).text!=="EOF"&&e.test(o+l.text);)s=l,o+=s.text,this.consume();if(o==="")throw new f("Invalid "+n+": '"+r.text+"'",r);return r.range(s,o)}parseColorGroup(e){const n=this.parseStringGroup("color",e);if(n==null)return null;const r=/^(#[a-f0-9]{3}|#?[a-f0-9]{6}|[a-z]+)$/i.exec(n.text);if(!r)throw new f("Invalid color: '"+n.text+"'",n);let s=r[0];return/^[0-9a-f]{6}$/i.test(s)&&(s="#"+s),{type:"color-token",mode:this.mode,color:s}}parseSizeGroup(e){let n,r=!1;if(this.gullet.consumeSpaces(),!e&&this.gullet.future().text!=="{"?n=this.parseRegexGroup(/^[-+]? *(?:$|\d+|\d+\.\d*|\.\d*) *[a-z]{0,2} *$/,"size"):n=this.parseStringGroup("size",e),!n)return null;!e&&n.text.length===0&&(n.text="0pt",r=!0);const s=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(n.text);if(!s)throw new f("Invalid size: '"+n.text+"'",n);const o={number:+(s[1]+s[2]),unit:s[3]};if(!Z0(o))throw new f("Invalid unit: '"+o.unit+"'",n);return{type:"size",mode:this.mode,value:o,isBlank:r}}parseUrlGroup(e){this.gullet.lexer.setCatcode("%",13),this.gullet.lexer.setCatcode("~",12);const n=this.parseStringGroup("url",e);if(this.gullet.lexer.setCatcode("%",14),this.gullet.lexer.setCatcode("~",13),n==null)return null;const r=n.text.replace(/\\([#$%&~_^{}])/g,"$1");return{type:"url",mode:this.mode,url:r}}parseArgumentGroup(e,n){const r=this.gullet.scanArgument(e);if(r==null)return null;const s=this.mode;n&&this.switchMode(n),this.gullet.beginGroup();const o=this.parseExpression(!1,"EOF");this.expect("EOF"),this.gullet.endGroup();const l={type:"ordgroup",mode:this.mode,loc:r.loc,body:o};return n&&this.switchMode(s),l}parseGroup(e,n){const r=this.fetch(),s=r.text;let o;if(s==="{"||s==="\\begingroup"){this.consume();const l=s==="{"?"}":"\\endgroup";this.gullet.beginGroup();const d=this.parseExpression(!1,l),g=this.fetch();this.expect(l),this.gullet.endGroup(),o={type:"ordgroup",mode:this.mode,loc:je.range(r,g),body:d,semisimple:s==="\\begingroup"||void 0}}else if(o=this.parseFunction(n,e)||this.parseSymbol(),o==null&&s[0]==="\\"&&!fs.hasOwnProperty(s)){if(this.settings.throwOnError)throw new f("Undefined control sequence: "+s,r);o=this.formatUnsupportedCmd(s),this.consume()}return o}formLigatures(e){let n=e.length-1;for(let r=0;r<n;++r){const s=e[r],o=s.text;o==="-"&&e[r+1].text==="-"&&(r+1<n&&e[r+2].text==="-"?(e.splice(r,3,{type:"textord",mode:"text",loc:je.range(s,e[r+2]),text:"---"}),n-=2):(e.splice(r,2,{type:"textord",mode:"text",loc:je.range(s,e[r+1]),text:"--"}),n-=1)),(o==="'"||o==="`")&&e[r+1].text===o&&(e.splice(r,2,{type:"textord",mode:"text",loc:je.range(s,e[r+1]),text:o+o}),n-=1)}}parseSymbol(){const e=this.fetch();let n=e.text;if(/^\\verb[^a-zA-Z]/.test(n)){this.consume();let o=n.slice(5);const l=o.charAt(0)==="*";if(l&&(o=o.slice(1)),o.length<2||o.charAt(0)!==o.slice(-1))throw new f(`\\verb assertion failed --
                    please report what input caused this bug`);return o=o.slice(1,-1),{type:"verb",mode:"text",body:o,star:l}}bs.hasOwnProperty(n[0])&&!be[this.mode][n[0]]&&(this.settings.strict&&this.mode==="math"&&this.settings.reportNonstrict("unicodeTextInMathMode",'Accented Unicode text character "'+n[0]+'" used in math mode',e),n=bs[n[0]]+n.slice(1));const r=ha.exec(n);r&&(n=n.substring(0,r.index),n==="i"?n="ı":n==="j"&&(n="ȷ"));let s;if(be[this.mode][n]){this.settings.strict&&this.mode==="math"&&un.indexOf(n)>=0&&this.settings.reportNonstrict("unicodeTextInMathMode",'Latin-1/Unicode text character "'+n[0]+'" used in math mode',e);const o=be[this.mode][n].group,l=je.range(e);let d;if(k0.hasOwnProperty(o)){const g=o;d={type:"atom",mode:this.mode,family:g,loc:l,text:n}}else d={type:o,mode:this.mode,loc:l,text:n};s=d}else if(n.charCodeAt(0)>=128)this.settings.strict&&(V0(n.charCodeAt(0))?this.mode==="math"&&this.settings.reportNonstrict("unicodeTextInMathMode",'Unicode text character "'+n[0]+'" used in math mode',e):this.settings.reportNonstrict("unknownSymbol",'Unrecognized Unicode character "'+n[0]+'"'+(" ("+n.charCodeAt(0)+")"),e)),s={type:"textord",mode:"text",loc:je.range(e),text:n};else return null;if(this.consume(),r)for(let o=0;o<r[0].length;o++){const l=r[0][o];if(!Hn[l])throw new f("Unknown accent ' "+l+"'",e);const d=Hn[l][this.mode]||Hn[l].text;if(!d)throw new f("Accent "+l+" unsupported in "+this.mode+" mode",e);s={type:"accent",mode:this.mode,loc:je.range(e),label:d,isStretchy:!1,isShifty:!0,base:s}}return s}}Yt.endOfExpression=["}","\\endgroup","\\end","\\right","&"];var qn=function(t,e){if(!(typeof t=="string"||t instanceof String))throw new TypeError("KaTeX can only parse string typed expression");const n=new Yt(t,e);delete n.gullet.macros.current["\\df@tag"];let r=n.parse();if(delete n.gullet.macros.current["\\current@color"],delete n.gullet.macros.current["\\color"],n.gullet.macros.get("\\df@tag")){if(!e.displayMode)throw new f("\\tag works only in display equations");r=[{type:"tag",mode:"text",body:r,tag:n.subparse([new s0("\\df@tag")])}]}return r};let ys=function(t,e,n){e.textContent="";const r=Pn(t,n).toNode();e.appendChild(r)};typeof document<"u"&&document.compatMode!=="CSS1Compat"&&(typeof console<"u"&&console.warn("Warning: KaTeX doesn't work in quirks mode. Make sure your website has a suitable doctype."),ys=function(){throw new f("KaTeX doesn't work in quirks mode.")});const ga=function(t,e){return Pn(t,e).toMarkup()},ba=function(t,e){const n=new W(e);return qn(t,n)},xs=function(t,e,n){if(n.throwOnError||!(t instanceof f))throw t;const r=E.makeSpan(["katex-error"],[new qe(e)]);return r.setAttribute("title",t.toString()),r.setAttribute("style","color:"+n.errorColor),r},Pn=function(t,e){const n=new W(e);try{const r=qn(t,n);return Bi(r,t,n)}catch(r){return xs(r,t,n)}};var ya={version:"0.16.10",render:ys,renderToString:ga,ParseError:f,SETTINGS_SCHEMA:ee,__parse:ba,__renderToDomTree:Pn,__renderToHTMLTree:function(t,e){const n=new W(e);try{const r=qn(t,n);return _i(r,t,n)}catch(r){return xs(r,t,n)}},__setFontMetrics:B0,__defineSymbol:i,__defineFunction:L,__defineMacro:p,__domTree:{Span:e0,Anchor:at,SymbolNode:qe,SvgNode:t0,PathNode:p0,LineNode:N0}},xa=ya;return h=h.default,h}()})}(Xn)),Xn.exports}(function(F,u){(function(h,x){F.exports=x(Ga())})(typeof self<"u"?self:Vs,function(c){return function(){var h={771:function(C){C.exports=c}},x={};function f(C){var B=x[C];if(B!==void 0)return B.exports;var $=x[C]={exports:{}};return h[C]($,$.exports,f),$.exports}(function(){f.n=function(C){var B=C&&C.__esModule?function(){return C.default}:function(){return C};return f.d(B,{a:B}),B}})(),function(){f.d=function(C,B){for(var $ in B)f.o(B,$)&&!f.o(C,$)&&Object.defineProperty(C,$,{enumerable:!0,get:B[$]})}}(),function(){f.o=function(C,B){return Object.prototype.hasOwnProperty.call(C,B)}}();var D={};return function(){f.d(D,{default:function(){return N}});var C=f(771),B=f.n(C);const $=function(ee,X,W){let j=W,xe=0;const me=ee.length;for(;j<X.length;){const ge=X[j];if(xe<=0&&X.slice(j,j+me)===ee)return j;ge==="\\"?j++:ge==="{"?xe++:ge==="}"&&xe--,j++}return-1},U=function(ee){return ee.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&")},K=/^\\begin{/;var Q=function(ee,X){let W;const j=[],xe=new RegExp("("+X.map(me=>U(me.left)).join("|")+")");for(;W=ee.search(xe),W!==-1;){W>0&&(j.push({type:"text",data:ee.slice(0,W)}),ee=ee.slice(W));const me=X.findIndex(Te=>ee.startsWith(Te.left));if(W=$(X[me].right,ee,X[me].left.length),W===-1)break;const ge=ee.slice(0,W+X[me].right.length),Ee=K.test(ge)?ge:ee.slice(X[me].left.length,W);j.push({type:"math",data:Ee,rawData:ge,display:X[me].display}),ee=ee.slice(W+X[me].right.length)}return ee!==""&&j.push({type:"text",data:ee}),j};const _e=function(ee,X){const W=Q(ee,X.delimiters);if(W.length===1&&W[0].type==="text")return null;const j=document.createDocumentFragment();for(let xe=0;xe<W.length;xe++)if(W[xe].type==="text")j.appendChild(document.createTextNode(W[xe].data));else{const me=document.createElement("span");let ge=W[xe].data;X.displayMode=W[xe].display;try{X.preProcess&&(ge=X.preProcess(ge)),B().render(ge,me,X)}catch(Ee){if(!(Ee instanceof B().ParseError))throw Ee;X.errorCallback("KaTeX auto-render: Failed to parse `"+W[xe].data+"` with ",Ee),j.appendChild(document.createTextNode(W[xe].rawData));continue}j.appendChild(me)}return j},ze=function(ee,X){for(let W=0;W<ee.childNodes.length;W++){const j=ee.childNodes[W];if(j.nodeType===3){let xe=j.textContent,me=j.nextSibling,ge=0;for(;me&&me.nodeType===Node.TEXT_NODE;)xe+=me.textContent,me=me.nextSibling,ge++;const Ee=_e(xe,X);if(Ee){for(let Te=0;Te<ge;Te++)j.nextSibling.remove();W+=Ee.childNodes.length-1,ee.replaceChild(Ee,j)}else W+=ge}else if(j.nodeType===1){const xe=" "+j.className+" ";X.ignoredTags.indexOf(j.nodeName.toLowerCase())===-1&&X.ignoredClasses.every(ge=>xe.indexOf(" "+ge+" ")===-1)&&ze(j,X)}}};var N=function(ee,X){if(!ee)throw new Error("No element provided to render");const W={};for(const j in X)X.hasOwnProperty(j)&&(W[j]=X[j]);W.delimiters=W.delimiters||[{left:"$$",right:"$$",display:!0},{left:"\\(",right:"\\)",display:!1},{left:"\\begin{equation}",right:"\\end{equation}",display:!0},{left:"\\begin{align}",right:"\\end{align}",display:!0},{left:"\\begin{alignat}",right:"\\end{alignat}",display:!0},{left:"\\begin{gather}",right:"\\end{gather}",display:!0},{left:"\\begin{CD}",right:"\\end{CD}",display:!0},{left:"\\[",right:"\\]",display:!0}],W.ignoredTags=W.ignoredTags||["script","noscript","style","textarea","pre","code","option"],W.ignoredClasses=W.ignoredClasses||[],W.errorCallback=W.errorCallback||console.error,W.macros=W.macros||{},ze(ee,W)}}(),D=D.default,D}()})})(Zs);var $a=Zs.exports;const Va=ka($a);function Jn(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let G0=Jn();function Ks(F){G0=F}const Qs=/[&<>"']/,Wa=new RegExp(Qs.source,"g"),Js=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,Ya=new RegExp(Js.source,"g"),Xa={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},zs=F=>Xa[F];function Ke(F,u){if(u){if(Qs.test(F))return F.replace(Wa,zs)}else if(Js.test(F))return F.replace(Ya,zs);return F}const ja=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig;function Za(F){return F.replace(ja,(u,c)=>(c=c.toLowerCase(),c==="colon"?":":c.charAt(0)==="#"?c.charAt(1)==="x"?String.fromCharCode(parseInt(c.substring(2),16)):String.fromCharCode(+c.substring(1)):""))}const Ka=/(^|[^\[])\^/g;function he(F,u){let c=typeof F=="string"?F:F.source;u=u||"";const h={replace:(x,f)=>{let D=typeof f=="string"?f:f.source;return D=D.replace(Ka,"$1"),c=c.replace(x,D),h},getRegex:()=>new RegExp(c,u)};return h}function Bs(F){try{F=encodeURI(F).replace(/%25/g,"%")}catch{return null}return F}const xt={exec:()=>null};function _s(F,u){const c=F.replace(/\|/g,(f,D,C)=>{let B=!1,$=D;for(;--$>=0&&C[$]==="\\";)B=!B;return B?"|":" |"}),h=c.split(/ \|/);let x=0;if(h[0].trim()||h.shift(),h.length>0&&!h[h.length-1].trim()&&h.pop(),u)if(h.length>u)h.splice(u);else for(;h.length<u;)h.push("");for(;x<h.length;x++)h[x]=h[x].trim().replace(/\\\|/g,"|");return h}function Kt(F,u,c){const h=F.length;if(h===0)return"";let x=0;for(;x<h;){const f=F.charAt(h-x-1);if(f===u&&!c)x++;else if(f!==u&&c)x++;else break}return F.slice(0,h-x)}function Qa(F,u){if(F.indexOf(u[1])===-1)return-1;let c=0;for(let h=0;h<F.length;h++)if(F[h]==="\\")h++;else if(F[h]===u[0])c++;else if(F[h]===u[1]&&(c--,c<0))return h;return-1}function Ns(F,u,c,h){const x=u.href,f=u.title?Ke(u.title):null,D=F[1].replace(/\\([\[\]])/g,"$1");if(F[0].charAt(0)!=="!"){h.state.inLink=!0;const C={type:"link",raw:c,href:x,title:f,text:D,tokens:h.inlineTokens(D)};return h.state.inLink=!1,C}return{type:"image",raw:c,href:x,title:f,text:Ke(D)}}function Ja(F,u){const c=F.match(/^(\s+)(?:```)/);if(c===null)return u;const h=c[1];return u.split(`
`).map(x=>{const f=x.match(/^\s+/);if(f===null)return x;const[D]=f;return D.length>=h.length?x.slice(h.length):x}).join(`
`)}class tn{options;rules;lexer;constructor(u){this.options=u||G0}space(u){const c=this.rules.block.newline.exec(u);if(c&&c[0].length>0)return{type:"space",raw:c[0]}}code(u){const c=this.rules.block.code.exec(u);if(c){const h=c[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:c[0],codeBlockStyle:"indented",text:this.options.pedantic?h:Kt(h,`
`)}}}fences(u){const c=this.rules.block.fences.exec(u);if(c){const h=c[0],x=Ja(h,c[3]||"");return{type:"code",raw:h,lang:c[2]?c[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):c[2],text:x}}}heading(u){const c=this.rules.block.heading.exec(u);if(c){let h=c[2].trim();if(/#$/.test(h)){const x=Kt(h,"#");(this.options.pedantic||!x||/ $/.test(x))&&(h=x.trim())}return{type:"heading",raw:c[0],depth:c[1].length,text:h,tokens:this.lexer.inline(h)}}}hr(u){const c=this.rules.block.hr.exec(u);if(c)return{type:"hr",raw:c[0]}}blockquote(u){const c=this.rules.block.blockquote.exec(u);if(c){const h=Kt(c[0].replace(/^ *>[ \t]?/gm,""),`
`),x=this.lexer.state.top;this.lexer.state.top=!0;const f=this.lexer.blockTokens(h);return this.lexer.state.top=x,{type:"blockquote",raw:c[0],tokens:f,text:h}}}list(u){let c=this.rules.block.list.exec(u);if(c){let h=c[1].trim();const x=h.length>1,f={type:"list",raw:"",ordered:x,start:x?+h.slice(0,-1):"",loose:!1,items:[]};h=x?`\\d{1,9}\\${h.slice(-1)}`:`\\${h}`,this.options.pedantic&&(h=x?h:"[*+-]");const D=new RegExp(`^( {0,3}${h})((?:[	 ][^\\n]*)?(?:\\n|$))`);let C="",B="",$=!1;for(;u;){let U=!1;if(!(c=D.exec(u))||this.rules.block.hr.test(u))break;C=c[0],u=u.substring(C.length);let K=c[2].split(`
`,1)[0].replace(/^\t+/,N=>" ".repeat(3*N.length)),Y=u.split(`
`,1)[0],Q=0;this.options.pedantic?(Q=2,B=K.trimStart()):(Q=c[2].search(/[^ ]/),Q=Q>4?1:Q,B=K.slice(Q),Q+=c[1].length);let _e=!1;if(!K&&/^ *$/.test(Y)&&(C+=Y+`
`,u=u.substring(Y.length+1),U=!0),!U){const N=new RegExp(`^ {0,${Math.min(3,Q-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),ee=new RegExp(`^ {0,${Math.min(3,Q-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),X=new RegExp(`^ {0,${Math.min(3,Q-1)}}(?:\`\`\`|~~~)`),W=new RegExp(`^ {0,${Math.min(3,Q-1)}}#`);for(;u;){const j=u.split(`
`,1)[0];if(Y=j,this.options.pedantic&&(Y=Y.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),X.test(Y)||W.test(Y)||N.test(Y)||ee.test(u))break;if(Y.search(/[^ ]/)>=Q||!Y.trim())B+=`
`+Y.slice(Q);else{if(_e||K.search(/[^ ]/)>=4||X.test(K)||W.test(K)||ee.test(K))break;B+=`
`+Y}!_e&&!Y.trim()&&(_e=!0),C+=j+`
`,u=u.substring(j.length+1),K=Y.slice(Q)}}f.loose||($?f.loose=!0:/\n *\n *$/.test(C)&&($=!0));let ze=null,We;this.options.gfm&&(ze=/^\[[ xX]\] /.exec(B),ze&&(We=ze[0]!=="[ ] ",B=B.replace(/^\[[ xX]\] +/,""))),f.items.push({type:"list_item",raw:C,task:!!ze,checked:We,loose:!1,text:B,tokens:[]}),f.raw+=C}f.items[f.items.length-1].raw=C.trimEnd(),f.items[f.items.length-1].text=B.trimEnd(),f.raw=f.raw.trimEnd();for(let U=0;U<f.items.length;U++)if(this.lexer.state.top=!1,f.items[U].tokens=this.lexer.blockTokens(f.items[U].text,[]),!f.loose){const K=f.items[U].tokens.filter(Q=>Q.type==="space"),Y=K.length>0&&K.some(Q=>/\n.*\n/.test(Q.raw));f.loose=Y}if(f.loose)for(let U=0;U<f.items.length;U++)f.items[U].loose=!0;return f}}html(u){const c=this.rules.block.html.exec(u);if(c)return{type:"html",block:!0,raw:c[0],pre:c[1]==="pre"||c[1]==="script"||c[1]==="style",text:c[0]}}def(u){const c=this.rules.block.def.exec(u);if(c){const h=c[1].toLowerCase().replace(/\s+/g," "),x=c[2]?c[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",f=c[3]?c[3].substring(1,c[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):c[3];return{type:"def",tag:h,raw:c[0],href:x,title:f}}}table(u){const c=this.rules.block.table.exec(u);if(!c||!/[:|]/.test(c[2]))return;const h=_s(c[1]),x=c[2].replace(/^\||\| *$/g,"").split("|"),f=c[3]&&c[3].trim()?c[3].replace(/\n[ \t]*$/,"").split(`
`):[],D={type:"table",raw:c[0],header:[],align:[],rows:[]};if(h.length===x.length){for(const C of x)/^ *-+: *$/.test(C)?D.align.push("right"):/^ *:-+: *$/.test(C)?D.align.push("center"):/^ *:-+ *$/.test(C)?D.align.push("left"):D.align.push(null);for(const C of h)D.header.push({text:C,tokens:this.lexer.inline(C)});for(const C of f)D.rows.push(_s(C,D.header.length).map(B=>({text:B,tokens:this.lexer.inline(B)})));return D}}lheading(u){const c=this.rules.block.lheading.exec(u);if(c)return{type:"heading",raw:c[0],depth:c[2].charAt(0)==="="?1:2,text:c[1],tokens:this.lexer.inline(c[1])}}paragraph(u){const c=this.rules.block.paragraph.exec(u);if(c){const h=c[1].charAt(c[1].length-1)===`
`?c[1].slice(0,-1):c[1];return{type:"paragraph",raw:c[0],text:h,tokens:this.lexer.inline(h)}}}text(u){const c=this.rules.block.text.exec(u);if(c)return{type:"text",raw:c[0],text:c[0],tokens:this.lexer.inline(c[0])}}escape(u){const c=this.rules.inline.escape.exec(u);if(c)return{type:"escape",raw:c[0],text:Ke(c[1])}}tag(u){const c=this.rules.inline.tag.exec(u);if(c)return!this.lexer.state.inLink&&/^<a /i.test(c[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(c[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(c[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(c[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:c[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:c[0]}}link(u){const c=this.rules.inline.link.exec(u);if(c){const h=c[2].trim();if(!this.options.pedantic&&/^</.test(h)){if(!/>$/.test(h))return;const D=Kt(h.slice(0,-1),"\\");if((h.length-D.length)%2===0)return}else{const D=Qa(c[2],"()");if(D>-1){const B=(c[0].indexOf("!")===0?5:4)+c[1].length+D;c[2]=c[2].substring(0,D),c[0]=c[0].substring(0,B).trim(),c[3]=""}}let x=c[2],f="";if(this.options.pedantic){const D=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(x);D&&(x=D[1],f=D[3])}else f=c[3]?c[3].slice(1,-1):"";return x=x.trim(),/^</.test(x)&&(this.options.pedantic&&!/>$/.test(h)?x=x.slice(1):x=x.slice(1,-1)),Ns(c,{href:x&&x.replace(this.rules.inline.anyPunctuation,"$1"),title:f&&f.replace(this.rules.inline.anyPunctuation,"$1")},c[0],this.lexer)}}reflink(u,c){let h;if((h=this.rules.inline.reflink.exec(u))||(h=this.rules.inline.nolink.exec(u))){const x=(h[2]||h[1]).replace(/\s+/g," "),f=c[x.toLowerCase()];if(!f){const D=h[0].charAt(0);return{type:"text",raw:D,text:D}}return Ns(h,f,h[0],this.lexer)}}emStrong(u,c,h=""){let x=this.rules.inline.emStrongLDelim.exec(u);if(!x||x[3]&&h.match(/[\p{L}\p{N}]/u))return;if(!(x[1]||x[2]||"")||!h||this.rules.inline.punctuation.exec(h)){const D=[...x[0]].length-1;let C,B,$=D,U=0;const K=x[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(K.lastIndex=0,c=c.slice(-1*u.length+D);(x=K.exec(c))!=null;){if(C=x[1]||x[2]||x[3]||x[4]||x[5]||x[6],!C)continue;if(B=[...C].length,x[3]||x[4]){$+=B;continue}else if((x[5]||x[6])&&D%3&&!((D+B)%3)){U+=B;continue}if($-=B,$>0)continue;B=Math.min(B,B+$+U);const Y=[...x[0]][0].length,Q=u.slice(0,D+x.index+Y+B);if(Math.min(D,B)%2){const ze=Q.slice(1,-1);return{type:"em",raw:Q,text:ze,tokens:this.lexer.inlineTokens(ze)}}const _e=Q.slice(2,-2);return{type:"strong",raw:Q,text:_e,tokens:this.lexer.inlineTokens(_e)}}}}codespan(u){const c=this.rules.inline.code.exec(u);if(c){let h=c[2].replace(/\n/g," ");const x=/[^ ]/.test(h),f=/^ /.test(h)&&/ $/.test(h);return x&&f&&(h=h.substring(1,h.length-1)),h=Ke(h,!0),{type:"codespan",raw:c[0],text:h}}}br(u){const c=this.rules.inline.br.exec(u);if(c)return{type:"br",raw:c[0]}}del(u){const c=this.rules.inline.del.exec(u);if(c)return{type:"del",raw:c[0],text:c[2],tokens:this.lexer.inlineTokens(c[2])}}autolink(u){const c=this.rules.inline.autolink.exec(u);if(c){let h,x;return c[2]==="@"?(h=Ke(c[1]),x="mailto:"+h):(h=Ke(c[1]),x=h),{type:"link",raw:c[0],text:h,href:x,tokens:[{type:"text",raw:h,text:h}]}}}url(u){let c;if(c=this.rules.inline.url.exec(u)){let h,x;if(c[2]==="@")h=Ke(c[0]),x="mailto:"+h;else{let f;do f=c[0],c[0]=this.rules.inline._backpedal.exec(c[0])?.[0]??"";while(f!==c[0]);h=Ke(c[0]),c[1]==="www."?x="http://"+c[0]:x=c[0]}return{type:"link",raw:c[0],text:h,href:x,tokens:[{type:"text",raw:h,text:h}]}}}inlineText(u){const c=this.rules.inline.text.exec(u);if(c){let h;return this.lexer.state.inRawBlock?h=c[0]:h=Ke(c[0]),{type:"text",raw:c[0],text:h}}}}const eo=/^(?: *(?:\n|$))+/,to=/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,no=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,wt=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,ro=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,ei=/(?:[*+-]|\d{1,9}[.)])/,ti=he(/^(?!bull )((?:.|\n(?!\s*?\n|bull ))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,ei).getRegex(),er=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,so=/^[^\n]+/,tr=/(?!\s*\])(?:\\.|[^\[\]\\])+/,io=he(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",tr).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),ao=he(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,ei).getRegex(),sn="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",nr=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,oo=he("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",nr).replace("tag",sn).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),ni=he(er).replace("hr",wt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",sn).getRegex(),lo=he(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",ni).getRegex(),rr={blockquote:lo,code:to,def:io,fences:no,heading:ro,hr:wt,html:oo,lheading:ti,list:ao,newline:eo,paragraph:ni,table:xt,text:so},Rs=he("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",wt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",sn).getRegex(),uo={...rr,table:Rs,paragraph:he(er).replace("hr",wt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Rs).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",sn).getRegex()},co={...rr,html:he(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",nr).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:xt,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:he(er).replace("hr",wt).replace("heading",` *#{1,6} *[^
]`).replace("lheading",ti).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},ri=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,ho=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,si=/^( {2,}|\\)\n(?!\s*$)/,mo=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,Dt="\\p{P}\\p{S}",po=he(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,Dt).getRegex(),fo=/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,go=he(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,Dt).getRegex(),bo=he("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,Dt).getRegex(),yo=he("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,Dt).getRegex(),xo=he(/\\([punct])/,"gu").replace(/punct/g,Dt).getRegex(),wo=he(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Do=he(nr).replace("(?:-->|$)","-->").getRegex(),ko=he("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Do).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),nn=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Ao=he(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",nn).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),ii=he(/^!?\[(label)\]\[(ref)\]/).replace("label",nn).replace("ref",tr).getRegex(),ai=he(/^!?\[(ref)\](?:\[\])?/).replace("ref",tr).getRegex(),So=he("reflink|nolink(?!\\()","g").replace("reflink",ii).replace("nolink",ai).getRegex(),sr={_backpedal:xt,anyPunctuation:xo,autolink:wo,blockSkip:fo,br:si,code:ho,del:xt,emStrongLDelim:go,emStrongRDelimAst:bo,emStrongRDelimUnd:yo,escape:ri,link:Ao,nolink:ai,punctuation:po,reflink:ii,reflinkSearch:So,tag:ko,text:mo,url:xt},vo={...sr,link:he(/^!?\[(label)\]\((.*?)\)/).replace("label",nn).getRegex(),reflink:he(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",nn).getRegex()},Qn={...sr,escape:he(ri).replace("])","~|])").getRegex(),url:he(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Fo={...Qn,br:he(si).replace("{2,}","*").getRegex(),text:he(Qn.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},Qt={normal:rr,gfm:uo,pedantic:co},yt={normal:sr,gfm:Qn,breaks:Fo,pedantic:vo};class y0{tokens;options;state;tokenizer;inlineQueue;constructor(u){this.tokens=[],this.tokens.links=Object.create(null),this.options=u||G0,this.options.tokenizer=this.options.tokenizer||new tn,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const c={block:Qt.normal,inline:yt.normal};this.options.pedantic?(c.block=Qt.pedantic,c.inline=yt.pedantic):this.options.gfm&&(c.block=Qt.gfm,this.options.breaks?c.inline=yt.breaks:c.inline=yt.gfm),this.tokenizer.rules=c}static get rules(){return{block:Qt,inline:yt}}static lex(u,c){return new y0(c).lex(u)}static lexInline(u,c){return new y0(c).inlineTokens(u)}lex(u){u=u.replace(/\r\n|\r/g,`
`),this.blockTokens(u,this.tokens);for(let c=0;c<this.inlineQueue.length;c++){const h=this.inlineQueue[c];this.inlineTokens(h.src,h.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(u,c=[]){this.options.pedantic?u=u.replace(/\t/g,"    ").replace(/^ +$/gm,""):u=u.replace(/^( *)(\t+)/gm,(C,B,$)=>B+"    ".repeat($.length));let h,x,f,D;for(;u;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(C=>(h=C.call({lexer:this},u,c))?(u=u.substring(h.raw.length),c.push(h),!0):!1))){if(h=this.tokenizer.space(u)){u=u.substring(h.raw.length),h.raw.length===1&&c.length>0?c[c.length-1].raw+=`
`:c.push(h);continue}if(h=this.tokenizer.code(u)){u=u.substring(h.raw.length),x=c[c.length-1],x&&(x.type==="paragraph"||x.type==="text")?(x.raw+=`
`+h.raw,x.text+=`
`+h.text,this.inlineQueue[this.inlineQueue.length-1].src=x.text):c.push(h);continue}if(h=this.tokenizer.fences(u)){u=u.substring(h.raw.length),c.push(h);continue}if(h=this.tokenizer.heading(u)){u=u.substring(h.raw.length),c.push(h);continue}if(h=this.tokenizer.hr(u)){u=u.substring(h.raw.length),c.push(h);continue}if(h=this.tokenizer.blockquote(u)){u=u.substring(h.raw.length),c.push(h);continue}if(h=this.tokenizer.list(u)){u=u.substring(h.raw.length),c.push(h);continue}if(h=this.tokenizer.html(u)){u=u.substring(h.raw.length),c.push(h);continue}if(h=this.tokenizer.def(u)){u=u.substring(h.raw.length),x=c[c.length-1],x&&(x.type==="paragraph"||x.type==="text")?(x.raw+=`
`+h.raw,x.text+=`
`+h.raw,this.inlineQueue[this.inlineQueue.length-1].src=x.text):this.tokens.links[h.tag]||(this.tokens.links[h.tag]={href:h.href,title:h.title});continue}if(h=this.tokenizer.table(u)){u=u.substring(h.raw.length),c.push(h);continue}if(h=this.tokenizer.lheading(u)){u=u.substring(h.raw.length),c.push(h);continue}if(f=u,this.options.extensions&&this.options.extensions.startBlock){let C=1/0;const B=u.slice(1);let $;this.options.extensions.startBlock.forEach(U=>{$=U.call({lexer:this},B),typeof $=="number"&&$>=0&&(C=Math.min(C,$))}),C<1/0&&C>=0&&(f=u.substring(0,C+1))}if(this.state.top&&(h=this.tokenizer.paragraph(f))){x=c[c.length-1],D&&x.type==="paragraph"?(x.raw+=`
`+h.raw,x.text+=`
`+h.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=x.text):c.push(h),D=f.length!==u.length,u=u.substring(h.raw.length);continue}if(h=this.tokenizer.text(u)){u=u.substring(h.raw.length),x=c[c.length-1],x&&x.type==="text"?(x.raw+=`
`+h.raw,x.text+=`
`+h.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=x.text):c.push(h);continue}if(u){const C="Infinite loop on byte: "+u.charCodeAt(0);if(this.options.silent){console.error(C);break}else throw new Error(C)}}return this.state.top=!0,c}inline(u,c=[]){return this.inlineQueue.push({src:u,tokens:c}),c}inlineTokens(u,c=[]){let h,x,f,D=u,C,B,$;if(this.tokens.links){const U=Object.keys(this.tokens.links);if(U.length>0)for(;(C=this.tokenizer.rules.inline.reflinkSearch.exec(D))!=null;)U.includes(C[0].slice(C[0].lastIndexOf("[")+1,-1))&&(D=D.slice(0,C.index)+"["+"a".repeat(C[0].length-2)+"]"+D.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(C=this.tokenizer.rules.inline.blockSkip.exec(D))!=null;)D=D.slice(0,C.index)+"["+"a".repeat(C[0].length-2)+"]"+D.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(C=this.tokenizer.rules.inline.anyPunctuation.exec(D))!=null;)D=D.slice(0,C.index)+"++"+D.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;u;)if(B||($=""),B=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(U=>(h=U.call({lexer:this},u,c))?(u=u.substring(h.raw.length),c.push(h),!0):!1))){if(h=this.tokenizer.escape(u)){u=u.substring(h.raw.length),c.push(h);continue}if(h=this.tokenizer.tag(u)){u=u.substring(h.raw.length),x=c[c.length-1],x&&h.type==="text"&&x.type==="text"?(x.raw+=h.raw,x.text+=h.text):c.push(h);continue}if(h=this.tokenizer.link(u)){u=u.substring(h.raw.length),c.push(h);continue}if(h=this.tokenizer.reflink(u,this.tokens.links)){u=u.substring(h.raw.length),x=c[c.length-1],x&&h.type==="text"&&x.type==="text"?(x.raw+=h.raw,x.text+=h.text):c.push(h);continue}if(h=this.tokenizer.emStrong(u,D,$)){u=u.substring(h.raw.length),c.push(h);continue}if(h=this.tokenizer.codespan(u)){u=u.substring(h.raw.length),c.push(h);continue}if(h=this.tokenizer.br(u)){u=u.substring(h.raw.length),c.push(h);continue}if(h=this.tokenizer.del(u)){u=u.substring(h.raw.length),c.push(h);continue}if(h=this.tokenizer.autolink(u)){u=u.substring(h.raw.length),c.push(h);continue}if(!this.state.inLink&&(h=this.tokenizer.url(u))){u=u.substring(h.raw.length),c.push(h);continue}if(f=u,this.options.extensions&&this.options.extensions.startInline){let U=1/0;const K=u.slice(1);let Y;this.options.extensions.startInline.forEach(Q=>{Y=Q.call({lexer:this},K),typeof Y=="number"&&Y>=0&&(U=Math.min(U,Y))}),U<1/0&&U>=0&&(f=u.substring(0,U+1))}if(h=this.tokenizer.inlineText(f)){u=u.substring(h.raw.length),h.raw.slice(-1)!=="_"&&($=h.raw.slice(-1)),B=!0,x=c[c.length-1],x&&x.type==="text"?(x.raw+=h.raw,x.text+=h.text):c.push(h);continue}if(u){const U="Infinite loop on byte: "+u.charCodeAt(0);if(this.options.silent){console.error(U);break}else throw new Error(U)}}return c}}class rn{options;constructor(u){this.options=u||G0}code(u,c,h){const x=(c||"").match(/^\S*/)?.[0];return u=u.replace(/\n$/,"")+`
`,x?'<pre><code class="language-'+Ke(x)+'">'+(h?u:Ke(u,!0))+`</code></pre>
`:"<pre><code>"+(h?u:Ke(u,!0))+`</code></pre>
`}blockquote(u){return`<blockquote>
${u}</blockquote>
`}html(u,c){return u}heading(u,c,h){return`<h${c}>${u}</h${c}>
`}hr(){return`<hr>
`}list(u,c,h){const x=c?"ol":"ul",f=c&&h!==1?' start="'+h+'"':"";return"<"+x+f+`>
`+u+"</"+x+`>
`}listitem(u,c,h){return`<li>${u}</li>
`}checkbox(u){return"<input "+(u?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(u){return`<p>${u}</p>
`}table(u,c){return c&&(c=`<tbody>${c}</tbody>`),`<table>
<thead>
`+u+`</thead>
`+c+`</table>
`}tablerow(u){return`<tr>
${u}</tr>
`}tablecell(u,c){const h=c.header?"th":"td";return(c.align?`<${h} align="${c.align}">`:`<${h}>`)+u+`</${h}>
`}strong(u){return`<strong>${u}</strong>`}em(u){return`<em>${u}</em>`}codespan(u){return`<code>${u}</code>`}br(){return"<br>"}del(u){return`<del>${u}</del>`}link(u,c,h){const x=Bs(u);if(x===null)return h;u=x;let f='<a href="'+u+'"';return c&&(f+=' title="'+c+'"'),f+=">"+h+"</a>",f}image(u,c,h){const x=Bs(u);if(x===null)return h;u=x;let f=`<img src="${u}" alt="${h}"`;return c&&(f+=` title="${c}"`),f+=">",f}text(u){return u}}class ir{strong(u){return u}em(u){return u}codespan(u){return u}del(u){return u}html(u){return u}text(u){return u}link(u,c,h){return""+h}image(u,c,h){return""+h}br(){return""}}class x0{options;renderer;textRenderer;constructor(u){this.options=u||G0,this.options.renderer=this.options.renderer||new rn,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new ir}static parse(u,c){return new x0(c).parse(u)}static parseInline(u,c){return new x0(c).parseInline(u)}parse(u,c=!0){let h="";for(let x=0;x<u.length;x++){const f=u[x];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[f.type]){const D=f,C=this.options.extensions.renderers[D.type].call({parser:this},D);if(C!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(D.type)){h+=C||"";continue}}switch(f.type){case"space":continue;case"hr":{h+=this.renderer.hr();continue}case"heading":{const D=f;h+=this.renderer.heading(this.parseInline(D.tokens),D.depth,Za(this.parseInline(D.tokens,this.textRenderer)));continue}case"code":{const D=f;h+=this.renderer.code(D.text,D.lang,!!D.escaped);continue}case"table":{const D=f;let C="",B="";for(let U=0;U<D.header.length;U++)B+=this.renderer.tablecell(this.parseInline(D.header[U].tokens),{header:!0,align:D.align[U]});C+=this.renderer.tablerow(B);let $="";for(let U=0;U<D.rows.length;U++){const K=D.rows[U];B="";for(let Y=0;Y<K.length;Y++)B+=this.renderer.tablecell(this.parseInline(K[Y].tokens),{header:!1,align:D.align[Y]});$+=this.renderer.tablerow(B)}h+=this.renderer.table(C,$);continue}case"blockquote":{const D=f,C=this.parse(D.tokens);h+=this.renderer.blockquote(C);continue}case"list":{const D=f,C=D.ordered,B=D.start,$=D.loose;let U="";for(let K=0;K<D.items.length;K++){const Y=D.items[K],Q=Y.checked,_e=Y.task;let ze="";if(Y.task){const We=this.renderer.checkbox(!!Q);$?Y.tokens.length>0&&Y.tokens[0].type==="paragraph"?(Y.tokens[0].text=We+" "+Y.tokens[0].text,Y.tokens[0].tokens&&Y.tokens[0].tokens.length>0&&Y.tokens[0].tokens[0].type==="text"&&(Y.tokens[0].tokens[0].text=We+" "+Y.tokens[0].tokens[0].text)):Y.tokens.unshift({type:"text",text:We+" "}):ze+=We+" "}ze+=this.parse(Y.tokens,$),U+=this.renderer.listitem(ze,_e,!!Q)}h+=this.renderer.list(U,C,B);continue}case"html":{const D=f;h+=this.renderer.html(D.text,D.block);continue}case"paragraph":{const D=f;h+=this.renderer.paragraph(this.parseInline(D.tokens));continue}case"text":{let D=f,C=D.tokens?this.parseInline(D.tokens):D.text;for(;x+1<u.length&&u[x+1].type==="text";)D=u[++x],C+=`
`+(D.tokens?this.parseInline(D.tokens):D.text);h+=c?this.renderer.paragraph(C):C;continue}default:{const D='Token with "'+f.type+'" type was not found.';if(this.options.silent)return console.error(D),"";throw new Error(D)}}}return h}parseInline(u,c){c=c||this.renderer;let h="";for(let x=0;x<u.length;x++){const f=u[x];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[f.type]){const D=this.options.extensions.renderers[f.type].call({parser:this},f);if(D!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(f.type)){h+=D||"";continue}}switch(f.type){case"escape":{const D=f;h+=c.text(D.text);break}case"html":{const D=f;h+=c.html(D.text);break}case"link":{const D=f;h+=c.link(D.href,D.title,this.parseInline(D.tokens,c));break}case"image":{const D=f;h+=c.image(D.href,D.title,D.text);break}case"strong":{const D=f;h+=c.strong(this.parseInline(D.tokens,c));break}case"em":{const D=f;h+=c.em(this.parseInline(D.tokens,c));break}case"codespan":{const D=f;h+=c.codespan(D.text);break}case"br":{h+=c.br();break}case"del":{const D=f;h+=c.del(this.parseInline(D.tokens,c));break}case"text":{const D=f;h+=c.text(D.text);break}default:{const D='Token with "'+f.type+'" type was not found.';if(this.options.silent)return console.error(D),"";throw new Error(D)}}}return h}}class en{options;constructor(u){this.options=u||G0}static passThroughHooks=new Set(["preprocess","postprocess","processAllTokens"]);preprocess(u){return u}postprocess(u){return u}processAllTokens(u){return u}}class oi{defaults=Jn();options=this.setOptions;parse=this.#e(y0.lex,x0.parse);parseInline=this.#e(y0.lexInline,x0.parseInline);Parser=x0;Renderer=rn;TextRenderer=ir;Lexer=y0;Tokenizer=tn;Hooks=en;constructor(...u){this.use(...u)}walkTokens(u,c){let h=[];for(const x of u)switch(h=h.concat(c.call(this,x)),x.type){case"table":{const f=x;for(const D of f.header)h=h.concat(this.walkTokens(D.tokens,c));for(const D of f.rows)for(const C of D)h=h.concat(this.walkTokens(C.tokens,c));break}case"list":{const f=x;h=h.concat(this.walkTokens(f.items,c));break}default:{const f=x;this.defaults.extensions?.childTokens?.[f.type]?this.defaults.extensions.childTokens[f.type].forEach(D=>{const C=f[D].flat(1/0);h=h.concat(this.walkTokens(C,c))}):f.tokens&&(h=h.concat(this.walkTokens(f.tokens,c)))}}return h}use(...u){const c=this.defaults.extensions||{renderers:{},childTokens:{}};return u.forEach(h=>{const x={...h};if(x.async=this.defaults.async||x.async||!1,h.extensions&&(h.extensions.forEach(f=>{if(!f.name)throw new Error("extension name required");if("renderer"in f){const D=c.renderers[f.name];D?c.renderers[f.name]=function(...C){let B=f.renderer.apply(this,C);return B===!1&&(B=D.apply(this,C)),B}:c.renderers[f.name]=f.renderer}if("tokenizer"in f){if(!f.level||f.level!=="block"&&f.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const D=c[f.level];D?D.unshift(f.tokenizer):c[f.level]=[f.tokenizer],f.start&&(f.level==="block"?c.startBlock?c.startBlock.push(f.start):c.startBlock=[f.start]:f.level==="inline"&&(c.startInline?c.startInline.push(f.start):c.startInline=[f.start]))}"childTokens"in f&&f.childTokens&&(c.childTokens[f.name]=f.childTokens)}),x.extensions=c),h.renderer){const f=this.defaults.renderer||new rn(this.defaults);for(const D in h.renderer){if(!(D in f))throw new Error(`renderer '${D}' does not exist`);if(D==="options")continue;const C=D,B=h.renderer[C],$=f[C];f[C]=(...U)=>{let K=B.apply(f,U);return K===!1&&(K=$.apply(f,U)),K||""}}x.renderer=f}if(h.tokenizer){const f=this.defaults.tokenizer||new tn(this.defaults);for(const D in h.tokenizer){if(!(D in f))throw new Error(`tokenizer '${D}' does not exist`);if(["options","rules","lexer"].includes(D))continue;const C=D,B=h.tokenizer[C],$=f[C];f[C]=(...U)=>{let K=B.apply(f,U);return K===!1&&(K=$.apply(f,U)),K}}x.tokenizer=f}if(h.hooks){const f=this.defaults.hooks||new en;for(const D in h.hooks){if(!(D in f))throw new Error(`hook '${D}' does not exist`);if(D==="options")continue;const C=D,B=h.hooks[C],$=f[C];en.passThroughHooks.has(D)?f[C]=U=>{if(this.defaults.async)return Promise.resolve(B.call(f,U)).then(Y=>$.call(f,Y));const K=B.call(f,U);return $.call(f,K)}:f[C]=(...U)=>{let K=B.apply(f,U);return K===!1&&(K=$.apply(f,U)),K}}x.hooks=f}if(h.walkTokens){const f=this.defaults.walkTokens,D=h.walkTokens;x.walkTokens=function(C){let B=[];return B.push(D.call(this,C)),f&&(B=B.concat(f.call(this,C))),B}}this.defaults={...this.defaults,...x}}),this}setOptions(u){return this.defaults={...this.defaults,...u},this}lexer(u,c){return y0.lex(u,c??this.defaults)}parser(u,c){return x0.parse(u,c??this.defaults)}#e(u,c){return(h,x)=>{const f={...x},D={...this.defaults,...f};this.defaults.async===!0&&f.async===!1&&(D.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),D.async=!0);const C=this.#t(!!D.silent,!!D.async);if(typeof h>"u"||h===null)return C(new Error("marked(): input parameter is undefined or null"));if(typeof h!="string")return C(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(h)+", string expected"));if(D.hooks&&(D.hooks.options=D),D.async)return Promise.resolve(D.hooks?D.hooks.preprocess(h):h).then(B=>u(B,D)).then(B=>D.hooks?D.hooks.processAllTokens(B):B).then(B=>D.walkTokens?Promise.all(this.walkTokens(B,D.walkTokens)).then(()=>B):B).then(B=>c(B,D)).then(B=>D.hooks?D.hooks.postprocess(B):B).catch(C);try{D.hooks&&(h=D.hooks.preprocess(h));let B=u(h,D);D.hooks&&(B=D.hooks.processAllTokens(B)),D.walkTokens&&this.walkTokens(B,D.walkTokens);let $=c(B,D);return D.hooks&&($=D.hooks.postprocess($)),$}catch(B){return C(B)}}}#t(u,c){return h=>{if(h.message+=`
Please report this to https://github.com/markedjs/marked.`,u){const x="<p>An error occurred:</p><pre>"+Ke(h.message+"",!0)+"</pre>";return c?Promise.resolve(x):x}if(c)return Promise.reject(h);throw h}}}const U0=new oi;function ce(F,u){return U0.parse(F,u)}ce.options=ce.setOptions=function(F){return U0.setOptions(F),ce.defaults=U0.defaults,Ks(ce.defaults),ce};ce.getDefaults=Jn;ce.defaults=G0;ce.use=function(...F){return U0.use(...F),ce.defaults=U0.defaults,Ks(ce.defaults),ce};ce.walkTokens=function(F,u){return U0.walkTokens(F,u)};ce.parseInline=U0.parseInline;ce.Parser=x0;ce.parser=x0.parse;ce.Renderer=rn;ce.TextRenderer=ir;ce.Lexer=y0;ce.lexer=y0.lex;ce.Tokenizer=tn;ce.Hooks=en;ce.parse=ce;ce.options;ce.setOptions;ce.use;ce.walkTokens;ce.parseInline;x0.parse;y0.lex;function Eo(F){if(typeof F=="function"&&(F={highlight:F}),!F||typeof F.highlight!="function")throw new Error("Must provide highlight function");return typeof F.langPrefix!="string"&&(F.langPrefix="language-"),{async:!!F.async,walkTokens(u){if(u.type!=="code")return;const c=To(u);if(F.async)return Promise.resolve(F.highlight(u.text,c)).then(Is(u));const h=F.highlight(u.text,c);Is(u)(h)},renderer:{code(u,c,h){const x=(c||"").match(/\S*/)[0],f=x?` class="${F.langPrefix}${Ls(x)}"`:"";return u=u.replace(/\n$/,""),`<pre><code${f}>${h?u:Ls(u,!0)}
</code></pre>`}}}}function To(F){return(F.lang||"").match(/\S*/)[0]}function Is(F){return u=>{typeof u=="string"&&u!==F.text&&(F.escaped=!0,F.text=u)}}const li=/[&<>"']/,Co=new RegExp(li.source,"g"),ui=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,Mo=new RegExp(ui.source,"g"),zo={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Os=F=>zo[F];function Ls(F,u){if(u){if(li.test(F))return F.replace(Co,Os)}else if(ui.test(F))return F.replace(Mo,Os);return F}const Bo=/[\0-\x1F!-,\.\/:-@\[-\^`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482\u0530\u0557\u0558\u055A-\u055F\u0589-\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05EB-\u05EE\u05F3-\u060F\u061B-\u061F\u066A-\u066D\u06D4\u06DD\u06DE\u06E9\u06FD\u06FE\u0700-\u070F\u074B\u074C\u07B2-\u07BF\u07F6-\u07F9\u07FB\u07FC\u07FE\u07FF\u082E-\u083F\u085C-\u085F\u086B-\u089F\u08B5\u08C8-\u08D2\u08E2\u0964\u0965\u0970\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09F2-\u09FB\u09FD\u09FF\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF0-\u0AF8\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B54\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B70\u0B72-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BF0-\u0BFF\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5B-\u0C5F\u0C64\u0C65\u0C70-\u0C7F\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0CFF\u0D0D\u0D11\u0D45\u0D49\u0D4F-\u0D53\u0D58-\u0D5E\u0D64\u0D65\u0D70-\u0D79\u0D80\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DE5\u0DF0\u0DF1\u0DF4-\u0E00\u0E3B-\u0E3F\u0E4F\u0E5A-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F01-\u0F17\u0F1A-\u0F1F\u0F2A-\u0F34\u0F36\u0F38\u0F3A-\u0F3D\u0F48\u0F6D-\u0F70\u0F85\u0F98\u0FBD-\u0FC5\u0FC7-\u0FFF\u104A-\u104F\u109E\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u1360-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16ED\u16F9-\u16FF\u170D\u1715-\u171F\u1735-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17D4-\u17D6\u17D8-\u17DB\u17DE\u17DF\u17EA-\u180A\u180E\u180F\u181A-\u181F\u1879-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191F\u192C-\u192F\u193C-\u1945\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DA-\u19FF\u1A1C-\u1A1F\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1AA6\u1AA8-\u1AAF\u1AC1-\u1AFF\u1B4C-\u1B4F\u1B5A-\u1B6A\u1B74-\u1B7F\u1BF4-\u1BFF\u1C38-\u1C3F\u1C4A-\u1C4C\u1C7E\u1C7F\u1C89-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CCF\u1CD3\u1CFB-\u1CFF\u1DFA\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u203E\u2041-\u2053\u2055-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u20CF\u20F1-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u215F\u2189-\u24B5\u24EA-\u2BFF\u2C2F\u2C5F\u2CE5-\u2CEA\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E00-\u2E2E\u2E30-\u3004\u3008-\u3020\u3030\u3036\u3037\u303D-\u3040\u3097\u3098\u309B\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\u9FFD-\u9FFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA62C-\uA63F\uA673\uA67E\uA6F2-\uA716\uA720\uA721\uA789\uA78A\uA7C0\uA7C1\uA7CB-\uA7F4\uA828-\uA82B\uA82D-\uA83F\uA874-\uA87F\uA8C6-\uA8CF\uA8DA-\uA8DF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA954-\uA95F\uA97D-\uA97F\uA9C1-\uA9CE\uA9DA-\uA9DF\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A-\uAA5F\uAA77-\uAA79\uAAC3-\uAADA\uAADE\uAADF\uAAF0\uAAF1\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABEB\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFDFF\uFE10-\uFE1F\uFE30-\uFE32\uFE35-\uFE4C\uFE50-\uFE6F\uFE75\uFEFD-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF3E\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDD3F\uDD75-\uDDFC\uDDFE-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEDF\uDEE1-\uDEFF\uDF20-\uDF2C\uDF4B-\uDF4F\uDF7B-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0\uDFD6-\uDFFF]|\uD801[\uDC9E\uDC9F\uDCAA-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE04\uDE07-\uDE0B\uDE14\uDE18\uDE36\uDE37\uDE3B-\uDE3E\uDE40-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE7-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD28-\uDD2F\uDD3A-\uDE7F\uDEAA\uDEAD-\uDEAF\uDEB2-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF51-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC47-\uDC65\uDC70-\uDC7E\uDCBB-\uDCCF\uDCE9-\uDCEF\uDCFA-\uDCFF\uDD35\uDD40-\uDD43\uDD48-\uDD4F\uDD74\uDD75\uDD77-\uDD7F\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDFF\uDE12\uDE38-\uDE3D\uDE3F-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEEB-\uDEEF\uDEFA-\uDEFF\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A\uDF45\uDF46\uDF49\uDF4A\uDF4E\uDF4F\uDF51-\uDF56\uDF58-\uDF5C\uDF64\uDF65\uDF6D-\uDF6F\uDF75-\uDFFF]|\uD805[\uDC4B-\uDC4F\uDC5A-\uDC5D\uDC62-\uDC7F\uDCC6\uDCC8-\uDCCF\uDCDA-\uDD7F\uDDB6\uDDB7\uDDC1-\uDDD7\uDDDE-\uDDFF\uDE41-\uDE43\uDE45-\uDE4F\uDE5A-\uDE7F\uDEB9-\uDEBF\uDECA-\uDEFF\uDF1B\uDF1C\uDF2C-\uDF2F\uDF3A-\uDFFF]|\uD806[\uDC3B-\uDC9F\uDCEA-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD36\uDD39\uDD3A\uDD44-\uDD4F\uDD5A-\uDD9F\uDDA8\uDDA9\uDDD8\uDDD9\uDDE2\uDDE5-\uDDFF\uDE3F-\uDE46\uDE48-\uDE4F\uDE9A-\uDE9C\uDE9E-\uDEBF\uDEF9-\uDFFF]|\uD807[\uDC09\uDC37\uDC41-\uDC4F\uDC5A-\uDC71\uDC90\uDC91\uDCA8\uDCB7-\uDCFF\uDD07\uDD0A\uDD37-\uDD39\uDD3B\uDD3E\uDD48-\uDD4F\uDD5A-\uDD5F\uDD66\uDD69\uDD8F\uDD92\uDD99-\uDD9F\uDDAA-\uDEDF\uDEF7-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC6F-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD80B\uD80E-\uD810\uD812-\uD819\uD824-\uD82B\uD82D\uD82E\uD830-\uD833\uD837\uD839\uD83D\uD83F\uD87B-\uD87D\uD87F\uD885-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD80D[\uDC2F-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F\uDE6A-\uDECF\uDEEE\uDEEF\uDEF5-\uDEFF\uDF37-\uDF3F\uDF44-\uDF4F\uDF5A-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4E\uDF88-\uDF8E\uDFA0-\uDFDF\uDFE2\uDFE5-\uDFEF\uDFF2-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFF\uDD09-\uDFFF]|\uD82C[\uDD1F-\uDD4F\uDD53-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDC9C\uDC9F-\uDFFF]|\uD834[\uDC00-\uDD64\uDD6A-\uDD6C\uDD73-\uDD7A\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDE41\uDE45-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC\uDFCD]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85-\uDE9A\uDEA0\uDEB0-\uDFFF]|\uD838[\uDC07\uDC19\uDC1A\uDC22\uDC25\uDC2B-\uDCFF\uDD2D-\uDD2F\uDD3E\uDD3F\uDD4A-\uDD4D\uDD4F-\uDEBF\uDEFA-\uDFFF]|\uD83A[\uDCC5-\uDCCF\uDCD7-\uDCFF\uDD4C-\uDD4F\uDD5A-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD83C[\uDC00-\uDD2F\uDD4A-\uDD4F\uDD6A-\uDD6F\uDD8A-\uDFFF]|\uD83E[\uDC00-\uDFEF\uDFFA-\uDFFF]|\uD869[\uDEDE-\uDEFF]|\uD86D[\uDF35-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDFFF]|\uDB40[\uDC00-\uDCFF\uDDF0-\uDFFF]/g,_o=Object.hasOwnProperty;class ci{constructor(){this.occurrences,this.reset()}slug(u,c){const h=this;let x=No(u,c===!0);const f=x;for(;_o.call(h.occurrences,x);)h.occurrences[f]++,x=f+"-"+h.occurrences[f];return h.occurrences[x]=0,x}reset(){this.occurrences=Object.create(null)}}function No(F,u){return typeof F!="string"?"":(u||(F=F.toLowerCase()),F.replace(Bo,"").replace(/ /g,"-"))}let Hs,qs=[];function Ro({prefix:F=""}={}){return{headerIds:!1,hooks:{preprocess(u){return qs=[],Hs=new ci,u}},renderer:{heading(u,c,h){h=h.toLowerCase().trim().replace(/<[!\/a-z].*?>/gi,"");const x=`${F}${Hs.slug(h)}`,f={level:c,text:u,id:x};return qs.push(f),`<h${c} id="${x}">${u}</h${c}>
`}}}}(function(F){var u=/\\(?:[^a-z()[\]]|[a-z*]+)/i,c={"equation-command":{pattern:u,alias:"regex"}};F.languages.latex={comment:/%.*/,cdata:{pattern:/(\\begin\{((?:lstlisting|verbatim)\*?)\})[\s\S]*?(?=\\end\{\2\})/,lookbehind:!0},equation:[{pattern:/\$\$(?:\\[\s\S]|[^\\$])+\$\$|\$(?:\\[\s\S]|[^\\$])+\$|\\\([\s\S]*?\\\)|\\\[[\s\S]*?\\\]/,inside:c,alias:"string"},{pattern:/(\\begin\{((?:align|eqnarray|equation|gather|math|multline)\*?)\})[\s\S]*?(?=\\end\{\2\})/,lookbehind:!0,inside:c,alias:"string"}],keyword:{pattern:/(\\(?:begin|cite|documentclass|end|label|ref|usepackage)(?:\[[^\]]+\])?\{)[^}]+(?=\})/,lookbehind:!0},url:{pattern:/(\\url\{)[^}]+(?=\})/,lookbehind:!0},headline:{pattern:/(\\(?:chapter|frametitle|paragraph|part|section|subparagraph|subsection|subsubparagraph|subsubsection|subsubsubparagraph)\*?(?:\[[^\]]+\])?\{)[^}]+(?=\})/,lookbehind:!0,alias:"class-name"},function:{pattern:u,alias:"selector"},punctuation:/[[\]{}&]/},F.languages.tex=F.languages.latex,F.languages.context=F.languages.latex})(Prism);(function(F){var u="\\b(?:BASH|BASHOPTS|BASH_ALIASES|BASH_ARGC|BASH_ARGV|BASH_CMDS|BASH_COMPLETION_COMPAT_DIR|BASH_LINENO|BASH_REMATCH|BASH_SOURCE|BASH_VERSINFO|BASH_VERSION|COLORTERM|COLUMNS|COMP_WORDBREAKS|DBUS_SESSION_BUS_ADDRESS|DEFAULTS_PATH|DESKTOP_SESSION|DIRSTACK|DISPLAY|EUID|GDMSESSION|GDM_LANG|GNOME_KEYRING_CONTROL|GNOME_KEYRING_PID|GPG_AGENT_INFO|GROUPS|HISTCONTROL|HISTFILE|HISTFILESIZE|HISTSIZE|HOME|HOSTNAME|HOSTTYPE|IFS|INSTANCE|JOB|LANG|LANGUAGE|LC_ADDRESS|LC_ALL|LC_IDENTIFICATION|LC_MEASUREMENT|LC_MONETARY|LC_NAME|LC_NUMERIC|LC_PAPER|LC_TELEPHONE|LC_TIME|LESSCLOSE|LESSOPEN|LINES|LOGNAME|LS_COLORS|MACHTYPE|MAILCHECK|MANDATORY_PATH|NO_AT_BRIDGE|OLDPWD|OPTERR|OPTIND|ORBIT_SOCKETDIR|OSTYPE|PAPERSIZE|PATH|PIPESTATUS|PPID|PS1|PS2|PS3|PS4|PWD|RANDOM|REPLY|SECONDS|SELINUX_INIT|SESSION|SESSIONTYPE|SESSION_MANAGER|SHELL|SHELLOPTS|SHLVL|SSH_AUTH_SOCK|TERM|UID|UPSTART_EVENTS|UPSTART_INSTANCE|UPSTART_JOB|UPSTART_SESSION|USER|WINDOWID|XAUTHORITY|XDG_CONFIG_DIRS|XDG_CURRENT_DESKTOP|XDG_DATA_DIRS|XDG_GREETER_DATA_DIR|XDG_MENU_PREFIX|XDG_RUNTIME_DIR|XDG_SEAT|XDG_SEAT_PATH|XDG_SESSION_DESKTOP|XDG_SESSION_ID|XDG_SESSION_PATH|XDG_SESSION_TYPE|XDG_VTNR|XMODIFIERS)\\b",c={pattern:/(^(["']?)\w+\2)[ \t]+\S.*/,lookbehind:!0,alias:"punctuation",inside:null},h={bash:c,environment:{pattern:RegExp("\\$"+u),alias:"constant"},variable:[{pattern:/\$?\(\([\s\S]+?\)\)/,greedy:!0,inside:{variable:[{pattern:/(^\$\(\([\s\S]+)\)\)/,lookbehind:!0},/^\$\(\(/],number:/\b0x[\dA-Fa-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:[Ee]-?\d+)?/,operator:/--|\+\+|\*\*=?|<<=?|>>=?|&&|\|\||[=!+\-*/%<>^&|]=?|[?~:]/,punctuation:/\(\(?|\)\)?|,|;/}},{pattern:/\$\((?:\([^)]+\)|[^()])+\)|`[^`]+`/,greedy:!0,inside:{variable:/^\$\(|^`|\)$|`$/}},{pattern:/\$\{[^}]+\}/,greedy:!0,inside:{operator:/:[-=?+]?|[!\/]|##?|%%?|\^\^?|,,?/,punctuation:/[\[\]]/,environment:{pattern:RegExp("(\\{)"+u),lookbehind:!0,alias:"constant"}}},/\$(?:\w+|[#?*!@$])/],entity:/\\(?:[abceEfnrtv\\"]|O?[0-7]{1,3}|U[0-9a-fA-F]{8}|u[0-9a-fA-F]{4}|x[0-9a-fA-F]{1,2})/};F.languages.bash={shebang:{pattern:/^#!\s*\/.*/,alias:"important"},comment:{pattern:/(^|[^"{\\$])#.*/,lookbehind:!0},"function-name":[{pattern:/(\bfunction\s+)[\w-]+(?=(?:\s*\(?:\s*\))?\s*\{)/,lookbehind:!0,alias:"function"},{pattern:/\b[\w-]+(?=\s*\(\s*\)\s*\{)/,alias:"function"}],"for-or-select":{pattern:/(\b(?:for|select)\s+)\w+(?=\s+in\s)/,alias:"variable",lookbehind:!0},"assign-left":{pattern:/(^|[\s;|&]|[<>]\()\w+(?:\.\w+)*(?=\+?=)/,inside:{environment:{pattern:RegExp("(^|[\\s;|&]|[<>]\\()"+u),lookbehind:!0,alias:"constant"}},alias:"variable",lookbehind:!0},parameter:{pattern:/(^|\s)-{1,2}(?:\w+:[+-]?)?\w+(?:\.\w+)*(?=[=\s]|$)/,alias:"variable",lookbehind:!0},string:[{pattern:/((?:^|[^<])<<-?\s*)(\w+)\s[\s\S]*?(?:\r?\n|\r)\2/,lookbehind:!0,greedy:!0,inside:h},{pattern:/((?:^|[^<])<<-?\s*)(["'])(\w+)\2\s[\s\S]*?(?:\r?\n|\r)\3/,lookbehind:!0,greedy:!0,inside:{bash:c}},{pattern:/(^|[^\\](?:\\\\)*)"(?:\\[\s\S]|\$\([^)]+\)|\$(?!\()|`[^`]+`|[^"\\`$])*"/,lookbehind:!0,greedy:!0,inside:h},{pattern:/(^|[^$\\])'[^']*'/,lookbehind:!0,greedy:!0},{pattern:/\$'(?:[^'\\]|\\[\s\S])*'/,greedy:!0,inside:{entity:h.entity}}],environment:{pattern:RegExp("\\$?"+u),alias:"constant"},variable:h.variable,function:{pattern:/(^|[\s;|&]|[<>]\()(?:add|apropos|apt|apt-cache|apt-get|aptitude|aspell|automysqlbackup|awk|basename|bash|bc|bconsole|bg|bzip2|cal|cargo|cat|cfdisk|chgrp|chkconfig|chmod|chown|chroot|cksum|clear|cmp|column|comm|composer|cp|cron|crontab|csplit|curl|cut|date|dc|dd|ddrescue|debootstrap|df|diff|diff3|dig|dir|dircolors|dirname|dirs|dmesg|docker|docker-compose|du|egrep|eject|env|ethtool|expand|expect|expr|fdformat|fdisk|fg|fgrep|file|find|fmt|fold|format|free|fsck|ftp|fuser|gawk|git|gparted|grep|groupadd|groupdel|groupmod|groups|grub-mkconfig|gzip|halt|head|hg|history|host|hostname|htop|iconv|id|ifconfig|ifdown|ifup|import|install|ip|java|jobs|join|kill|killall|less|link|ln|locate|logname|logrotate|look|lpc|lpr|lprint|lprintd|lprintq|lprm|ls|lsof|lynx|make|man|mc|mdadm|mkconfig|mkdir|mke2fs|mkfifo|mkfs|mkisofs|mknod|mkswap|mmv|more|most|mount|mtools|mtr|mutt|mv|nano|nc|netstat|nice|nl|node|nohup|notify-send|npm|nslookup|op|open|parted|passwd|paste|pathchk|ping|pkill|pnpm|podman|podman-compose|popd|pr|printcap|printenv|ps|pushd|pv|quota|quotacheck|quotactl|ram|rar|rcp|reboot|remsync|rename|renice|rev|rm|rmdir|rpm|rsync|scp|screen|sdiff|sed|sendmail|seq|service|sftp|sh|shellcheck|shuf|shutdown|sleep|slocate|sort|split|ssh|stat|strace|su|sudo|sum|suspend|swapon|sync|sysctl|tac|tail|tar|tee|time|timeout|top|touch|tr|traceroute|tsort|tty|umount|uname|unexpand|uniq|units|unrar|unshar|unzip|update-grub|uptime|useradd|userdel|usermod|users|uudecode|uuencode|v|vcpkg|vdir|vi|vim|virsh|vmstat|wait|watch|wc|wget|whereis|which|who|whoami|write|xargs|xdg-open|yarn|yes|zenity|zip|zsh|zypper)(?=$|[)\s;|&])/,lookbehind:!0},keyword:{pattern:/(^|[\s;|&]|[<>]\()(?:case|do|done|elif|else|esac|fi|for|function|if|in|select|then|until|while)(?=$|[)\s;|&])/,lookbehind:!0},builtin:{pattern:/(^|[\s;|&]|[<>]\()(?:\.|:|alias|bind|break|builtin|caller|cd|command|continue|declare|echo|enable|eval|exec|exit|export|getopts|hash|help|let|local|logout|mapfile|printf|pwd|read|readarray|readonly|return|set|shift|shopt|source|test|times|trap|type|typeset|ulimit|umask|unalias|unset)(?=$|[)\s;|&])/,lookbehind:!0,alias:"class-name"},boolean:{pattern:/(^|[\s;|&]|[<>]\()(?:false|true)(?=$|[)\s;|&])/,lookbehind:!0},"file-descriptor":{pattern:/\B&\d\b/,alias:"important"},operator:{pattern:/\d?<>|>\||\+=|=[=~]?|!=?|<<[<-]?|[&\d]?>>|\d[<>]&?|[<>][&=]?|&[>&]?|\|[&|]?/,inside:{"file-descriptor":{pattern:/^\d/,alias:"important"}}},punctuation:/\$?\(\(?|\)\)?|\.\.|[{}[\];\\]/,number:{pattern:/(^|\s)(?:[1-9]\d*|0)(?:[.,]\d+)?\b/,lookbehind:!0}},c.inside=F.languages.bash;for(var x=["comment","function-name","for-or-select","assign-left","parameter","string","environment","function","keyword","builtin","boolean","file-descriptor","operator","punctuation","number"],f=h.variable[1].inside,D=0;D<x.length;D++)f[x[D]]=F.languages.bash[x[D]];F.languages.sh=F.languages.bash,F.languages.shell=F.languages.bash})(Prism);const Io='<svg class="md-link-icon" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true" fill="currentColor"><path d="m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z"></path></svg>',Oo=`<svg
xmlns="http://www.w3.org/2000/svg"
width="100%"
height="100%"
viewBox="0 0 32 32"
><path
  fill="currentColor"
  d="M28 10v18H10V10h18m0-2H10a2 2 0 0 0-2 2v18a2 2 0 0 0 2 2h18a2 2 0 0 0 2-2V10a2 2 0 0 0-2-2Z"
/><path fill="currentColor" d="M4 18H2V4a2 2 0 0 1 2-2h14v2H4Z" /></svg>`,Lo=`<svg
xmlns="http://www.w3.org/2000/svg"
width="100%"
height="100%"
viewBox="0 0 24 24"
fill="none"
stroke="currentColor"
stroke-width="3"
stroke-linecap="round"
stroke-linejoin="round"><polyline points="20 6 9 17 4 12" /></svg>`,Ps=`<button title="copy" class="copy_code_button">
<span class="copy-text">${Oo}</span>
<span class="check">${Lo}</span>
</button>`,hi=/[&<>"']/,Ho=new RegExp(hi.source,"g"),mi=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,qo=new RegExp(mi.source,"g"),Po={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Us=F=>Po[F]||"";function jn(F,u){if(u){if(hi.test(F))return F.replace(Ho,Us)}else if(mi.test(F))return F.replace(qo,Us);return F}const Uo={code(F,u,c){const h=(u??"").match(/\S*/)?.[0]??"";return F=F.replace(/\n$/,"")+`
`,h?'<div class="code_wrap">'+Ps+'<pre><code class="language-'+jn(h)+'">'+(c?F:jn(F,!0))+`</code></pre></div>
`:'<div class="code_wrap">'+Ps+"<pre><code>"+(c?F:jn(F,!0))+`</code></pre></div>
`}},Go=new ci;function $o({header_links:F,line_breaks:u}){const c=new oi;return c.use({gfm:!0,pedantic:!1,breaks:u},Eo({highlight:(h,x)=>Un.languages[x]?Un.highlight(h,Un.languages[x],x):h}),{renderer:Uo}),F&&(c.use(Ro()),c.use({extensions:[{name:"heading",level:"block",renderer(h){const x=h.raw.toLowerCase().trim().replace(/<[!\/a-z].*?>/gi,""),f="h"+Go.slug(x),D=h.depth,C=this.parser.parseInline(h.tokens);return`<h${D} id="${f}"><a class="md-header-anchor" href="#${f}">${Io}</a>${C}</h${D}>
`}}]})),c}const{HtmlTag:Vo,SvelteComponent:Wo,attr:Yo,binding_callbacks:Xo,detach:ar,element:jo,empty:Zo,init:Ko,insert:or,noop:Gs,safe_not_equal:Qo,set_data:Jo,text:el,toggle_class:$s}=window.__gradio__svelte__internal,{afterUpdate:tl,createEventDispatcher:Dl}=window.__gradio__svelte__internal;function nl(F){let u;return{c(){u=el(F[3])},m(c,h){or(c,u,h)},p(c,h){h&8&&Jo(u,c[3])},d(c){c&&ar(u)}}}function rl(F){let u,c;return{c(){u=new Vo(!1),c=Zo(),u.a=c},m(h,x){u.m(F[3],h,x),or(h,c,x)},p(h,x){x&8&&u.p(h[3])},d(h){h&&(ar(c),u.d())}}}function sl(F){let u;function c(f,D){return f[1]?rl:nl}let h=c(F),x=h(F);return{c(){u=jo("span"),x.c(),Yo(u,"class","md svelte-ftv0x7"),$s(u,"chatbot",F[0])},m(f,D){or(f,u,D),x.m(u,null),F[9](u)},p(f,[D]){h===(h=c(f))&&x?x.p(f,D):(x.d(1),x=h(f),x&&(x.c(),x.m(u,null))),D&1&&$s(u,"chatbot",f[0])},i:Gs,o:Gs,d(f){f&&ar(u),x.d(),F[9](null)}}}function il(F,u,c){let{chatbot:h=!0}=u,{message:x}=u,{sanitize_html:f=!0}=u,{latex_delimiters:D=[]}=u,{render_markdown:C=!0}=u,{line_breaks:B=!0}=u,{header_links:$=!1}=u,U,K;const Y=$o({header_links:$,line_breaks:B}),Q=N=>{try{return!!N&&new URL(N,location.href).origin!==location.origin}catch{return!1}};Cs.addHook("afterSanitizeAttributes",function(N){"target"in N&&Q(N.getAttribute("href"))&&(N.setAttribute("target","_blank"),N.setAttribute("rel","noopener noreferrer"))});function _e(N){return C&&(N=Y.parse(N)),f&&(N=Cs.sanitize(N)),N}async function ze(N){D.length>0&&N&&D.some(X=>N.includes(X.left)&&N.includes(X.right))&&Va(U,{delimiters:D,throwOnError:!1})}tl(()=>ze(x));function We(N){Xo[N?"unshift":"push"](()=>{U=N,c(2,U)})}return F.$$set=N=>{"chatbot"in N&&c(0,h=N.chatbot),"message"in N&&c(4,x=N.message),"sanitize_html"in N&&c(5,f=N.sanitize_html),"latex_delimiters"in N&&c(6,D=N.latex_delimiters),"render_markdown"in N&&c(1,C=N.render_markdown),"line_breaks"in N&&c(7,B=N.line_breaks),"header_links"in N&&c(8,$=N.header_links)},F.$$.update=()=>{F.$$.dirty&16&&(x&&x.trim()?c(3,K=_e(x)):c(3,K=""))},[h,C,U,K,x,f,D,B,$,We]}class al extends Wo{constructor(u){super(),Ko(this,u,il,sl,Qo,{chatbot:0,message:4,sanitize_html:5,latex_delimiters:6,render_markdown:1,line_breaks:7,header_links:8})}}const kl=al;export{kl as M};
//# sourceMappingURL=Example.svelte_svelte_type_style_lang-648fc18a.js.map
