import"./ImageUploader-528d6ef1.js";import"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import"./Index-26cfc80a.js";import{I as m}from"./Image-21c02477.js";/* empty css                                              */import"./utils-572af92b.js";import"./BlockLabel-f27805b1.js";import"./IconButton-7294c90b.js";import"./Empty-28f63bf0.js";import"./ShareButton-7dae44e7.js";import"./DownloadLink-7ff36416.js";import"./file-url-bef2dc1b.js";import"./Image-eaba773f.js";import"./index-a80d931b.js";import"./svelte/svelte.js";import"./SelectSource-f5281119.js";import"./Upload-351cc897.js";import"./DropdownArrow-bb2afb7e.js";import"./ModifyUpload.svelte_svelte_type_style_lang-4ba51100.js";import"./Clear-2c7bae91.js";const{SvelteComponent:s,attr:c,create_component:u,destroy_component:p,detach:_,element:f,init:g,insert:d,mount_component:v,safe_not_equal:y,toggle_class:a,transition_in:b,transition_out:h}=window.__gradio__svelte__internal;function w(o){let e,l,r;return l=new m({props:{src:o[0].composite?.url||o[0].background?.url,alt:""}}),{c(){e=f("div"),u(l.$$.fragment),c(e,"class","container svelte-jhlhb0"),a(e,"table",o[1]==="table"),a(e,"gallery",o[1]==="gallery"),a(e,"selected",o[2])},m(t,n){d(t,e,n),v(l,e,null),r=!0},p(t,[n]){const i={};n&1&&(i.src=t[0].composite?.url||t[0].background?.url),l.$set(i),(!r||n&2)&&a(e,"table",t[1]==="table"),(!r||n&2)&&a(e,"gallery",t[1]==="gallery"),(!r||n&4)&&a(e,"selected",t[2])},i(t){r||(b(l.$$.fragment,t),r=!0)},o(t){h(l.$$.fragment,t),r=!1},d(t){t&&_(e),p(l)}}}function k(o,e,l){let{value:r}=e,{type:t}=e,{selected:n=!1}=e;return o.$$set=i=>{"value"in i&&l(0,r=i.value),"type"in i&&l(1,t=i.type),"selected"in i&&l(2,n=i.selected)},[r,t,n]}class Q extends s{constructor(e){super(),g(this,e,k,w,y,{value:0,type:1,selected:2})}}export{Q as default};
//# sourceMappingURL=Example-40dc08d9.js.map
