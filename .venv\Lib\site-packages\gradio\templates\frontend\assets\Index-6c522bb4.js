import{T as U}from"./Textbox-1709c01b.js";import{B as V}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{S as W}from"./Index-26cfc80a.js";import{default as Be}from"./Example-baada562.js";import"./BlockTitle-7f7c9ef8.js";import"./Info-84f5385d.js";import"./Check-965babbe.js";import"./Copy-b365948f.js";import"./index-a80d931b.js";import"./svelte/svelte.js";/* empty css                                              */const{SvelteComponent:X,add_flush_callback:D,assign:Y,bind:F,binding_callbacks:G,check_outros:Z,create_component:w,destroy_component:v,detach:y,flush:_,get_spread_object:p,get_spread_update:$,group_outros:ee,init:te,insert:le,mount_component:k,safe_not_equal:ie,space:se,transition_in:g,transition_out:m}=window.__gradio__svelte__internal;function H(i){let e,l;const n=[{autoscroll:i[2].autoscroll},{i18n:i[2].i18n},i[17]];let f={};for(let u=0;u<n.length;u+=1)f=Y(f,n[u]);return e=new W({props:f}),{c(){w(e.$$.fragment)},m(u,a){k(e,u,a),l=!0},p(u,a){const r=a[0]&131076?$(n,[a[0]&4&&{autoscroll:u[2].autoscroll},a[0]&4&&{i18n:u[2].i18n},a[0]&131072&&p(u[17])]):{};e.$set(r)},i(u){l||(g(e.$$.fragment,u),l=!0)},o(u){m(e.$$.fragment,u),l=!1},d(u){v(e,u)}}}function ne(i){let e,l,n,f,u,a=i[17]&&H(i);function r(s){i[23](s)}function b(s){i[24](s)}let c={label:i[3],info:i[4],show_label:i[10],lines:i[8],type:i[12],rtl:i[18],text_align:i[19],max_lines:i[11]?i[11]:i[8]+1,placeholder:i[9],show_copy_button:i[16],autofocus:i[20],container:i[13],autoscroll:i[21],disabled:!i[22]};return i[0]!==void 0&&(c.value=i[0]),i[1]!==void 0&&(c.value_is_output=i[1]),l=new U({props:c}),G.push(()=>F(l,"value",r)),G.push(()=>F(l,"value_is_output",b)),l.$on("change",i[25]),l.$on("input",i[26]),l.$on("submit",i[27]),l.$on("blur",i[28]),l.$on("select",i[29]),l.$on("focus",i[30]),{c(){a&&a.c(),e=se(),w(l.$$.fragment)},m(s,o){a&&a.m(s,o),le(s,e,o),k(l,s,o),u=!0},p(s,o){s[17]?a?(a.p(s,o),o[0]&131072&&g(a,1)):(a=H(s),a.c(),g(a,1),a.m(e.parentNode,e)):a&&(ee(),m(a,1,1,()=>{a=null}),Z());const h={};o[0]&8&&(h.label=s[3]),o[0]&16&&(h.info=s[4]),o[0]&1024&&(h.show_label=s[10]),o[0]&256&&(h.lines=s[8]),o[0]&4096&&(h.type=s[12]),o[0]&262144&&(h.rtl=s[18]),o[0]&524288&&(h.text_align=s[19]),o[0]&2304&&(h.max_lines=s[11]?s[11]:s[8]+1),o[0]&512&&(h.placeholder=s[9]),o[0]&65536&&(h.show_copy_button=s[16]),o[0]&1048576&&(h.autofocus=s[20]),o[0]&8192&&(h.container=s[13]),o[0]&2097152&&(h.autoscroll=s[21]),o[0]&4194304&&(h.disabled=!s[22]),!n&&o[0]&1&&(n=!0,h.value=s[0],D(()=>n=!1)),!f&&o[0]&2&&(f=!0,h.value_is_output=s[1],D(()=>f=!1)),l.$set(h)},i(s){u||(g(a),g(l.$$.fragment,s),u=!0)},o(s){m(a),m(l.$$.fragment,s),u=!1},d(s){s&&y(e),a&&a.d(s),v(l,s)}}}function ue(i){let e,l;return e=new V({props:{visible:i[7],elem_id:i[5],elem_classes:i[6],scale:i[14],min_width:i[15],allow_overflow:!1,padding:i[13],$$slots:{default:[ne]},$$scope:{ctx:i}}}),{c(){w(e.$$.fragment)},m(n,f){k(e,n,f),l=!0},p(n,f){const u={};f[0]&128&&(u.visible=n[7]),f[0]&32&&(u.elem_id=n[5]),f[0]&64&&(u.elem_classes=n[6]),f[0]&16384&&(u.scale=n[14]),f[0]&32768&&(u.min_width=n[15]),f[0]&8192&&(u.padding=n[13]),f[0]&8339231|f[1]&1&&(u.$$scope={dirty:f,ctx:n}),e.$set(u)},i(n){l||(g(e.$$.fragment,n),l=!0)},o(n){m(e.$$.fragment,n),l=!1},d(n){v(e,n)}}}function ae(i,e,l){let{gradio:n}=e,{label:f="Textbox"}=e,{info:u=void 0}=e,{elem_id:a=""}=e,{elem_classes:r=[]}=e,{visible:b=!0}=e,{value:c=""}=e,{lines:s}=e,{placeholder:o=""}=e,{show_label:h}=e,{max_lines:B}=e,{type:T="text"}=e,{container:S=!0}=e,{scale:j=null}=e,{min_width:q=void 0}=e,{show_copy_button:C=!1}=e,{loading_status:E=void 0}=e,{value_is_output:d=!1}=e,{rtl:I=!1}=e,{text_align:N=void 0}=e,{autofocus:x=!1}=e,{autoscroll:z=!0}=e,{interactive:A}=e;function J(t){c=t,l(0,c)}function K(t){d=t,l(1,d)}const L=()=>n.dispatch("change",c),M=()=>n.dispatch("input"),O=()=>n.dispatch("submit"),P=()=>n.dispatch("blur"),Q=t=>n.dispatch("select",t.detail),R=()=>n.dispatch("focus");return i.$$set=t=>{"gradio"in t&&l(2,n=t.gradio),"label"in t&&l(3,f=t.label),"info"in t&&l(4,u=t.info),"elem_id"in t&&l(5,a=t.elem_id),"elem_classes"in t&&l(6,r=t.elem_classes),"visible"in t&&l(7,b=t.visible),"value"in t&&l(0,c=t.value),"lines"in t&&l(8,s=t.lines),"placeholder"in t&&l(9,o=t.placeholder),"show_label"in t&&l(10,h=t.show_label),"max_lines"in t&&l(11,B=t.max_lines),"type"in t&&l(12,T=t.type),"container"in t&&l(13,S=t.container),"scale"in t&&l(14,j=t.scale),"min_width"in t&&l(15,q=t.min_width),"show_copy_button"in t&&l(16,C=t.show_copy_button),"loading_status"in t&&l(17,E=t.loading_status),"value_is_output"in t&&l(1,d=t.value_is_output),"rtl"in t&&l(18,I=t.rtl),"text_align"in t&&l(19,N=t.text_align),"autofocus"in t&&l(20,x=t.autofocus),"autoscroll"in t&&l(21,z=t.autoscroll),"interactive"in t&&l(22,A=t.interactive)},[c,d,n,f,u,a,r,b,s,o,h,B,T,S,j,q,C,E,I,N,x,z,A,J,K,L,M,O,P,Q,R]}class we extends X{constructor(e){super(),te(this,e,ae,ue,ie,{gradio:2,label:3,info:4,elem_id:5,elem_classes:6,visible:7,value:0,lines:8,placeholder:9,show_label:10,max_lines:11,type:12,container:13,scale:14,min_width:15,show_copy_button:16,loading_status:17,value_is_output:1,rtl:18,text_align:19,autofocus:20,autoscroll:21,interactive:22},null,[-1,-1])}get gradio(){return this.$$.ctx[2]}set gradio(e){this.$$set({gradio:e}),_()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),_()}get info(){return this.$$.ctx[4]}set info(e){this.$$set({info:e}),_()}get elem_id(){return this.$$.ctx[5]}set elem_id(e){this.$$set({elem_id:e}),_()}get elem_classes(){return this.$$.ctx[6]}set elem_classes(e){this.$$set({elem_classes:e}),_()}get visible(){return this.$$.ctx[7]}set visible(e){this.$$set({visible:e}),_()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),_()}get lines(){return this.$$.ctx[8]}set lines(e){this.$$set({lines:e}),_()}get placeholder(){return this.$$.ctx[9]}set placeholder(e){this.$$set({placeholder:e}),_()}get show_label(){return this.$$.ctx[10]}set show_label(e){this.$$set({show_label:e}),_()}get max_lines(){return this.$$.ctx[11]}set max_lines(e){this.$$set({max_lines:e}),_()}get type(){return this.$$.ctx[12]}set type(e){this.$$set({type:e}),_()}get container(){return this.$$.ctx[13]}set container(e){this.$$set({container:e}),_()}get scale(){return this.$$.ctx[14]}set scale(e){this.$$set({scale:e}),_()}get min_width(){return this.$$.ctx[15]}set min_width(e){this.$$set({min_width:e}),_()}get show_copy_button(){return this.$$.ctx[16]}set show_copy_button(e){this.$$set({show_copy_button:e}),_()}get loading_status(){return this.$$.ctx[17]}set loading_status(e){this.$$set({loading_status:e}),_()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(e){this.$$set({value_is_output:e}),_()}get rtl(){return this.$$.ctx[18]}set rtl(e){this.$$set({rtl:e}),_()}get text_align(){return this.$$.ctx[19]}set text_align(e){this.$$set({text_align:e}),_()}get autofocus(){return this.$$.ctx[20]}set autofocus(e){this.$$set({autofocus:e}),_()}get autoscroll(){return this.$$.ctx[21]}set autoscroll(e){this.$$set({autoscroll:e}),_()}get interactive(){return this.$$.ctx[22]}set interactive(e){this.$$set({interactive:e}),_()}}export{Be as BaseExample,U as BaseTextbox,we as default};
//# sourceMappingURL=Index-6c522bb4.js.map
