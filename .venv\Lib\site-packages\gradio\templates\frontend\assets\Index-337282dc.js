import{a as Zt,B as ql}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{S as Dl}from"./Index-26cfc80a.js";import{d as ue}from"./index-2f00b72c.js";import{c as Ll}from"./utils-572af92b.js";import{U as Rl}from"./ModifyUpload.svelte_svelte_type_style_lang-4ba51100.js";import"./index-a80d931b.js";import"./Index.svelte_svelte_type_style_lang-e1d4a36d.js";import{M as Bl}from"./Example.svelte_svelte_type_style_lang-648fc18a.js";import{d as El}from"./dsv-576afacd.js";import{default as mi}from"./Example-6955c51f.js";import"./svelte/svelte.js";import"./prism-python-b0b31d02.js";const{HtmlTag:Tl,SvelteComponent:Hl,action_destroyer:Nl,attr:de,binding_callbacks:Ol,bubble:it,check_outros:Fl,create_component:jl,destroy_component:Pl,detach:Te,element:Gt,empty:Ul,group_outros:Jl,init:Vl,insert:He,listen:Ee,mount_component:Wl,noop:Ie,prevent_default:Il,run_all:Qt,safe_not_equal:Kl,set_data:Yl,set_input_value:vt,space:Zl,text:Gl,toggle_class:Ke,transition_in:st,transition_out:at}=window.__gradio__svelte__internal,{createEventDispatcher:Ql}=window.__gradio__svelte__internal;function yt(n){let e,t,l;return{c(){e=Gt("input"),de(e,"role","textbox"),de(e,"tabindex","-1"),de(e,"class","svelte-z9gpua"),Ke(e,"header",n[5])},m(i,r){He(i,e,r),n[18](e),vt(e,n[10]),t||(l=[Ee(e,"input",n[19]),Ee(e,"blur",n[12]),Nl(n[11].call(null,e)),Ee(e,"keydown",n[17])],t=!0)},p(i,r){r&1024&&e.value!==i[10]&&vt(e,i[10]),r&32&&Ke(e,"header",i[5])},d(i){i&&Te(e),n[18](null),t=!1,Qt(l)}}}function Xl(n){let e=(n[9]?n[0]:n[3]||n[0])+"",t;return{c(){t=Gl(e)},m(l,i){He(l,t,i)},p(l,i){i&521&&e!==(e=(l[9]?l[0]:l[3]||l[0])+"")&&Yl(t,e)},i:Ie,o:Ie,d(l){l&&Te(t)}}}function xl(n){let e,t;return e=new Bl({props:{message:n[0].toLocaleString(),latex_delimiters:n[7],line_breaks:n[8],chatbot:!1}}),{c(){jl(e.$$.fragment)},m(l,i){Wl(e,l,i),t=!0},p(l,i){const r={};i&1&&(r.message=l[0].toLocaleString()),i&128&&(r.latex_delimiters=l[7]),i&256&&(r.line_breaks=l[8]),e.$set(r)},i(l){t||(st(e.$$.fragment,l),t=!0)},o(l){at(e.$$.fragment,l),t=!1},d(l){Pl(e,l)}}}function $l(n){let e,t;return{c(){e=new Tl(!1),t=Ul(),e.a=t},m(l,i){e.m(n[0],l,i),He(l,t,i)},p(l,i){i&1&&e.p(l[0])},i:Ie,o:Ie,d(l){l&&(Te(t),e.d())}}}function en(n){let e,t,l,i,r,u,s,a=n[2]&&yt(n);const d=[$l,xl,Xl],f=[];function b(m,k){return m[6]==="html"?0:m[6]==="markdown"?1:2}return l=b(n),i=f[l]=d[l](n),{c(){a&&a.c(),e=Zl(),t=Gt("span"),i.c(),de(t,"tabindex","-1"),de(t,"role","button"),de(t,"style",n[4]),de(t,"class","svelte-z9gpua"),Ke(t,"edit",n[2])},m(m,k){a&&a.m(m,k),He(m,e,k),He(m,t,k),f[l].m(t,null),r=!0,u||(s=[Ee(t,"dblclick",n[15]),Ee(t,"focus",Il(n[16]))],u=!0)},p(m,[k]){m[2]?a?a.p(m,k):(a=yt(m),a.c(),a.m(e.parentNode,e)):a&&(a.d(1),a=null);let A=l;l=b(m),l===A?f[l].p(m,k):(Jl(),at(f[A],1,1,()=>{f[A]=null}),Fl(),i=f[l],i?i.p(m,k):(i=f[l]=d[l](m),i.c()),st(i,1),i.m(t,null)),(!r||k&16)&&de(t,"style",m[4]),(!r||k&4)&&Ke(t,"edit",m[2])},i(m){r||(st(i),r=!0)},o(m){at(i),r=!1},d(m){m&&(Te(e),Te(t)),a&&a.d(m),f[l].d(),u=!1,Qt(s)}}}function tn(n,e,t){let l,{edit:i}=e,{value:r=""}=e,{display_value:u=null}=e,{styling:s=""}=e,{header:a=!1}=e,{datatype:d="str"}=e,{latex_delimiters:f}=e,{clear_on_focus:b=!1}=e,{select_on_focus:m=!1}=e,{line_breaks:k=!0}=e,{editable:A=!0}=e;const L=Ql();let{el:S}=e;function H(p){return b&&t(10,l=""),m&&p.select(),p.focus(),{}}function R({currentTarget:p}){t(0,r=p.value),L("blur")}function y(p){it.call(this,n,p)}function c(p){it.call(this,n,p)}function B(p){it.call(this,n,p)}function q(p){Ol[p?"unshift":"push"](()=>{S=p,t(1,S)})}function M(){l=this.value,t(10,l),t(0,r)}return n.$$set=p=>{"edit"in p&&t(2,i=p.edit),"value"in p&&t(0,r=p.value),"display_value"in p&&t(3,u=p.display_value),"styling"in p&&t(4,s=p.styling),"header"in p&&t(5,a=p.header),"datatype"in p&&t(6,d=p.datatype),"latex_delimiters"in p&&t(7,f=p.latex_delimiters),"clear_on_focus"in p&&t(13,b=p.clear_on_focus),"select_on_focus"in p&&t(14,m=p.select_on_focus),"line_breaks"in p&&t(8,k=p.line_breaks),"editable"in p&&t(9,A=p.editable),"el"in p&&t(1,S=p.el)},n.$$.update=()=>{n.$$.dirty&1&&t(10,l=r)},[r,S,i,u,s,a,d,f,k,A,l,H,R,b,m,y,c,B,q,M]}class Ge extends Hl{constructor(e){super(),Vl(this,e,tn,en,Kl,{edit:2,value:0,display_value:3,styling:4,header:5,datatype:6,latex_delimiters:7,clear_on_focus:13,select_on_focus:14,line_breaks:8,editable:9,el:1})}}const{ResizeObserverSingleton:ln,SvelteComponent:nn,add_iframe_resize_listener:At,add_render_callback:St,append:qe,attr:Je,binding_callbacks:zt,check_outros:Xt,create_slot:ot,detach:Qe,element:Re,empty:xt,ensure_array_like:Mt,get_all_dirty_from_scope:rt,get_slot_changes:ft,group_outros:$t,init:sn,insert:Xe,listen:an,outro_and_destroy_block:on,resize_observer_content_box:rn,safe_not_equal:fn,set_style:oe,space:Ct,text:un,transition_in:Se,transition_out:De,update_keyed_each:_n,update_slot_base:ut}=window.__gradio__svelte__internal,{onMount:cn,tick:Ve}=window.__gradio__svelte__internal,hn=n=>({}),qt=n=>({});function Dt(n,e,t){const l=n.slice();return l[34]=e[t],l}const dn=n=>({item:n[0]&256,index:n[0]&256}),Lt=n=>({item:n[34].data,index:n[34].index}),gn=n=>({}),Rt=n=>({});function Bt(n){let e=[],t=new Map,l,i,r=Mt(n[8]);const u=s=>s[34].data[0].id;for(let s=0;s<r.length;s+=1){let a=Dt(n,r,s),d=u(a);t.set(d,e[s]=Et(d,a))}return{c(){for(let s=0;s<e.length;s+=1)e[s].c();l=xt()},m(s,a){for(let d=0;d<e.length;d+=1)e[d]&&e[d].m(s,a);Xe(s,l,a),i=!0},p(s,a){a[0]&1048832&&(r=Mt(s[8]),$t(),e=_n(e,a,u,1,s,r,t,l.parentNode,on,Et,l,Dt),Xt())},i(s){if(!i){for(let a=0;a<r.length;a+=1)Se(e[a]);i=!0}},o(s){for(let a=0;a<e.length;a+=1)De(e[a]);i=!1},d(s){s&&Qe(l);for(let a=0;a<e.length;a+=1)e[a].d(s)}}}function bn(n){let e;return{c(){e=un(`Missing Table Row
					`)},m(t,l){Xe(t,e,l)},d(t){t&&Qe(e)}}}function Et(n,e){let t,l;const i=e[21].tbody,r=ot(i,e,e[20],Lt),u=r||bn();return{key:n,first:null,c(){t=xt(),u&&u.c(),this.first=t},m(s,a){Xe(s,t,a),u&&u.m(s,a),l=!0},p(s,a){e=s,r&&r.p&&(!l||a[0]&1048832)&&ut(r,i,e,e[20],l?ft(i,e[20],a,dn):rt(e[20]),Lt)},i(s){l||(Se(u,s),l=!0)},o(s){De(u,s),l=!1},d(s){s&&Qe(t),u&&u.d(s)}}}function mn(n){let e,t,l,i,r,u,s,a,d,f,b,m,k;const A=n[21].thead,L=ot(A,n,n[20],Rt);let S=n[8].length&&n[8][0].data.length&&Bt(n);const H=n[21].tfoot,R=ot(H,n,n[20],qt);return{c(){e=Re("svelte-virtual-table-viewport"),t=Re("table"),l=Re("thead"),L&&L.c(),r=Ct(),u=Re("tbody"),S&&S.c(),s=Ct(),a=Re("tfoot"),R&&R.c(),Je(l,"class","thead svelte-1txh5yn"),St(()=>n[22].call(l)),Je(u,"class","tbody svelte-1txh5yn"),Je(a,"class","tfoot svelte-1txh5yn"),St(()=>n[24].call(a)),Je(t,"class","table svelte-1txh5yn"),oe(t,"height",wn),oe(t,"--bw-svt-p-top",n[6]+"px"),oe(t,"--bw-svt-p-bottom",n[2]+"px"),oe(t,"--bw-svt-head-height",n[4]+"px"),oe(t,"--bw-svt-foot-height",n[5]+"px"),oe(t,"--bw-svt-avg-row-height",n[1]+"px")},m(y,c){Xe(y,e,c),qe(e,t),qe(t,l),L&&L.m(l,null),i=At(l,n[22].bind(l)),qe(t,r),qe(t,u),S&&S.m(u,null),n[23](u),qe(t,s),qe(t,a),R&&R.m(a,null),d=At(a,n[24].bind(a)),n[25](t),f=rn.observe(t,n[26].bind(t)),b=!0,m||(k=an(t,"scroll",n[9]),m=!0)},p(y,c){L&&L.p&&(!b||c[0]&1048576)&&ut(L,A,y,y[20],b?ft(A,y[20],c,gn):rt(y[20]),Rt),y[8].length&&y[8][0].data.length?S?(S.p(y,c),c[0]&256&&Se(S,1)):(S=Bt(y),S.c(),Se(S,1),S.m(u,null)):S&&($t(),De(S,1,1,()=>{S=null}),Xt()),R&&R.p&&(!b||c[0]&1048576)&&ut(R,H,y,y[20],b?ft(H,y[20],c,hn):rt(y[20]),qt),(!b||c[0]&64)&&oe(t,"--bw-svt-p-top",y[6]+"px"),(!b||c[0]&4)&&oe(t,"--bw-svt-p-bottom",y[2]+"px"),(!b||c[0]&16)&&oe(t,"--bw-svt-head-height",y[4]+"px"),(!b||c[0]&32)&&oe(t,"--bw-svt-foot-height",y[5]+"px"),(!b||c[0]&2)&&oe(t,"--bw-svt-avg-row-height",y[1]+"px")},i(y){b||(Se(L,y),Se(S),Se(R,y),b=!0)},o(y){De(L,y),De(S),De(R,y),b=!1},d(y){y&&Qe(e),L&&L.d(y),i(),S&&S.d(),n[23](null),R&&R.d(y),d(),n[25](null),f(),m=!1,k()}}}let wn="100%";function kn(n,e){if(!n)return 0;const t=getComputedStyle(n);return parseInt(t.getPropertyValue(e))}function pn(n,e,t){let l,{$$slots:i={},$$scope:r}=e,{items:u=[]}=e,{max_height:s}=e,{actual_height:a}=e,{table_scrollbar_width:d}=e,{start:f=0}=e,{end:b=0}=e,{selected:m}=e,k,A=0,L,S=0,H=0,R=[],y,c,B=0,q,M=0,p=[],T,X=0;async function N(C){if(M===0)return;const{scrollTop:W}=q;t(13,d=q.offsetWidth-q.clientWidth),X=B-(W-S);let Q=f;for(;X<s&&Q<C.length;){let F=c[Q-f];F||(t(11,b=Q+1),await Ve(),F=c[Q-f]);let Z=F?.getBoundingClientRect().height;Z||(Z=k);const Ce=R[Q]=Z;X+=Ce,Q+=1}t(11,b=Q);const le=C.length-b,$=q.offsetHeight-q.clientHeight;$>0&&(X+=$);let ne=R.filter(F=>typeof F=="number");t(1,k=ne.reduce((F,Z)=>F+Z,0)/ne.length),t(2,A=le*k),R.length=C.length,await Ve(),s?X<s?t(12,a=X+2):t(12,a=s):t(12,a=X+1),await Ve()}async function se(C){requestAnimationFrame(async()=>{if(typeof C!="number")return;const W=typeof C!="number"?!1:he(C);W!==!0&&(W==="back"&&await O(C,{behavior:"instant"}),W==="forwards"&&await O(C,{behavior:"instant"},!0))})}function he(C){const W=c&&c[C-f];if(!W&&C<f)return"back";if(!W&&C>=b-1)return"forwards";const{top:Q}=q.getBoundingClientRect(),{top:le,bottom:$}=W.getBoundingClientRect();return le-Q<37?"back":$-Q>M?"forwards":!0}async function Y(C){const W=q.scrollTop;c=L.children;const Q=l.length<f,le=kn(c[1],"border-top-width"),$=0;Q&&await O(l.length-1,{behavior:"auto"});let ne=0;for(let j=0;j<c.length;j+=1)R[f+j]=c[j].getBoundingClientRect().height;let F=0,Z=S+le/2,Ce=[];for(;F<l.length;){const j=R[F]||k;if(Ce[F]=j,Z+j+$>W){ne=F,t(6,B=Z-(S+le/2));break}Z+=j,F+=1}for(ne=Math.max(0,ne);F<l.length;){const j=R[F]||k;if(Z+=j,F+=1,Z>W+M)break}t(10,f=ne),t(11,b=F);const fe=l.length-b;b===0&&t(11,b=10),t(1,k=(Z-S)/b);let Fe=fe*k;for(;F<l.length;)F+=1,R[F]=k;t(2,A=Fe),isFinite(A)||t(2,A=2e5)}async function O(C,W,Q=!1){await Ve();const le=k;let $=C*le;Q&&($=$-M+le+S);const ne=q.offsetHeight-q.clientHeight;ne>0&&($+=ne);const F={top:$,behavior:"smooth",...W};q.scrollTo(F)}cn(()=>{c=L.children,t(18,y=!0),N(u)});function v(){S=this.offsetHeight,t(4,S)}function h(C){zt[C?"unshift":"push"](()=>{L=C,t(3,L)})}function g(){H=this.offsetHeight,t(5,H)}function x(C){zt[C?"unshift":"push"](()=>{q=C,t(7,q)})}function xe(){T=ln.entries.get(this)?.contentRect,t(0,T)}return n.$$set=C=>{"items"in C&&t(14,u=C.items),"max_height"in C&&t(15,s=C.max_height),"actual_height"in C&&t(12,a=C.actual_height),"table_scrollbar_width"in C&&t(13,d=C.table_scrollbar_width),"start"in C&&t(10,f=C.start),"end"in C&&t(11,b=C.end),"selected"in C&&t(16,m=C.selected),"$$scope"in C&&t(20,r=C.$$scope)},n.$$.update=()=>{n.$$.dirty[0]&1&&(M=T?.height||0),n.$$.dirty[0]&16384&&t(19,l=u),n.$$.dirty[0]&786432&&y&&requestAnimationFrame(()=>N(l)),n.$$.dirty[0]&65536&&se(m),n.$$.dirty[0]&527360&&t(8,p=l.slice(f,b).map((C,W)=>({index:W+f,data:C})))},[T,k,A,L,S,H,B,q,p,Y,f,b,a,d,u,s,m,O,y,l,r,i,v,h,g,x,xe]}class vn extends nn{constructor(e){super(),sn(this,e,pn,mn,fn,{items:14,max_height:15,actual_height:12,table_scrollbar_width:13,start:10,end:11,selected:16,scroll_to_index:17},null,[-1,-1])}get scroll_to_index(){return this.$$.ctx[17]}}const{ResizeObserverSingleton:yn,SvelteComponent:An,action_destroyer:Sn,add_flush_callback:me,append:E,attr:w,bind:we,binding_callbacks:re,check_outros:ze,create_component:ve,destroy_component:ye,detach:I,element:U,empty:zn,ensure_array_like:ge,group_outros:Me,init:Mn,insert:K,listen:be,mount_component:Ae,outro_and_destroy_block:Ye,resize_observer_content_box:Cn,run_all:el,safe_not_equal:qn,set_data:Ne,set_style:ke,space:te,svg_element:pe,text:Oe,toggle_class:J,transition_in:V,transition_out:G,update_keyed_each:Ze}=window.__gradio__svelte__internal,{createEventDispatcher:Dn,tick:We,onMount:Ln}=window.__gradio__svelte__internal;function Tt(n,e,t){const l=n.slice();return l[88]=e[t].value,l[89]=e[t].id,l[92]=e,l[93]=t,l}function Ht(n,e,t){const l=n.slice();return l[88]=e[t].value,l[89]=e[t].id,l[90]=e,l[91]=t,l}function Nt(n,e,t){const l=n.slice();return l[88]=e[t].value,l[89]=e[t].id,l[94]=e,l[91]=t,l}function Ot(n,e,t){const l=n.slice();return l[88]=e[t].value,l[89]=e[t].id,l[93]=t,l}function Ft(n){let e,t;return{c(){e=U("p"),t=Oe(n[1]),w(e,"class","svelte-1bvc1p0")},m(l,i){K(l,e,i),E(e,t)},p(l,i){i[0]&2&&Ne(t,l[1])},d(l){l&&I(e)}}}function jt(n){let e,t;return{c(){e=U("caption"),t=Oe(n[1]),w(e,"class","sr-only")},m(l,i){K(l,e,i),E(e,t)},p(l,i){i[0]&2&&Ne(t,l[1])},d(l){l&&I(e)}}}function Pt(n,e){let t,l,i,r,u,s,a,d,f,b,m;return i=new Ge({props:{value:e[88],latex_delimiters:e[5],line_breaks:e[11],header:!0,edit:!1,el:null}}),{key:n,first:null,c(){t=U("th"),l=U("div"),ve(i.$$.fragment),r=te(),u=U("div"),s=pe("svg"),a=pe("path"),f=te(),w(a,"d","M4.49999 0L8.3971 6.75H0.602875L4.49999 0Z"),w(s,"width","1em"),w(s,"height","1em"),w(s,"viewBox","0 0 9 7"),w(s,"fill","none"),w(s,"xmlns","http://www.w3.org/2000/svg"),w(s,"class","svelte-1bvc1p0"),w(u,"class",d="sort-button "+e[17]+" svelte-1bvc1p0"),J(u,"sorted",e[18]===e[93]),J(u,"des",e[18]===e[93]&&e[17]==="des"),w(l,"class","cell-wrap svelte-1bvc1p0"),w(t,"aria-sort",b=e[35](e[88],e[18],e[17])),w(t,"class","svelte-1bvc1p0"),J(t,"editing",e[25]===e[93]),ke(t,"width",e[12].length?e[12][e[93]]:void 0),this.first=t},m(k,A){K(k,t,A),E(t,l),Ae(i,l,null),E(l,r),E(l,u),E(u,s),E(s,a),E(t,f),m=!0},p(k,A){e=k;const L={};A[0]&8388608&&(L.value=e[88]),A[0]&32&&(L.latex_delimiters=e[5]),A[0]&2048&&(L.line_breaks=e[11]),i.$set(L),(!m||A[0]&131072&&d!==(d="sort-button "+e[17]+" svelte-1bvc1p0"))&&w(u,"class",d),(!m||A[0]&8781824)&&J(u,"sorted",e[18]===e[93]),(!m||A[0]&8781824)&&J(u,"des",e[18]===e[93]&&e[17]==="des"),(!m||A[0]&8781824&&b!==(b=e[35](e[88],e[18],e[17])))&&w(t,"aria-sort",b),(!m||A[0]&41943040)&&J(t,"editing",e[25]===e[93]),A[0]&8392704&&ke(t,"width",e[12].length?e[12][e[93]]:void 0)},i(k){m||(V(i.$$.fragment,k),m=!0)},o(k){G(i.$$.fragment,k),m=!1},d(k){k&&I(t),ye(i)}}}function Ut(n,e){let t,l,i,r,u=e[91],s;i=new Ge({props:{value:e[88],latex_delimiters:e[5],line_breaks:e[11],datatype:Array.isArray(e[0])?e[0][e[91]]:e[0],edit:!1,el:null}});const a=()=>e[52](t,u),d=()=>e[52](null,u);return{key:n,first:null,c(){t=U("td"),l=U("div"),ve(i.$$.fragment),r=te(),w(l,"class","cell-wrap svelte-1bvc1p0"),w(t,"tabindex","-1"),w(t,"class","svelte-1bvc1p0"),this.first=t},m(f,b){K(f,t,b),E(t,l),Ae(i,l,null),E(t,r),a(),s=!0},p(f,b){e=f;const m={};b[1]&8&&(m.value=e[88]),b[0]&32&&(m.latex_delimiters=e[5]),b[0]&2048&&(m.line_breaks=e[11]),b[0]&1|b[1]&8&&(m.datatype=Array.isArray(e[0])?e[0][e[91]]:e[0]),i.$set(m),u!==e[91]&&(d(),u=e[91],a())},i(f){s||(V(i.$$.fragment,f),s=!0)},o(f){G(i.$$.fragment,f),s=!1},d(f){f&&I(t),ye(i),d()}}}function Jt(n){let e,t;return{c(){e=U("caption"),t=Oe(n[1]),w(e,"class","sr-only")},m(l,i){K(l,e,i),E(e,t)},p(l,i){i[0]&2&&Ne(t,l[1])},d(l){l&&I(e)}}}function Rn(n){let e,t=n[1]&&n[1].length!==0&&Jt(n);return{c(){t&&t.c(),e=zn()},m(l,i){t&&t.m(l,i),K(l,e,i)},p(l,i){l[1]&&l[1].length!==0?t?t.p(l,i):(t=Jt(l),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null)},d(l){l&&I(e),t&&t.d(l)}}}function Vt(n,e){let t,l,i,r,u,s,a,d,f,b,m,k,A,L,S;function H(q){e[61](q,e[93])}function R(q){e[62](q,e[89])}function y(){return e[63](e[93])}let c={latex_delimiters:e[5],line_breaks:e[11],edit:e[25]===e[93],select_on_focus:e[26],header:!0};e[23][e[93]].value!==void 0&&(c.value=e[23][e[93]].value),e[22][e[89]].input!==void 0&&(c.el=e[22][e[89]].input),i=new Ge({props:c}),re.push(()=>we(i,"value",H)),re.push(()=>we(i,"el",R)),i.$on("keydown",e[41]),i.$on("dblclick",y);function B(){return e[64](e[93])}return{key:n,first:null,c(){t=U("th"),l=U("div"),ve(i.$$.fragment),s=te(),a=U("div"),d=pe("svg"),f=pe("path"),m=te(),w(f,"d","M4.49999 0L8.3971 6.75H0.602875L4.49999 0Z"),w(d,"width","1em"),w(d,"height","1em"),w(d,"viewBox","0 0 9 7"),w(d,"fill","none"),w(d,"xmlns","http://www.w3.org/2000/svg"),w(d,"class","svelte-1bvc1p0"),w(a,"class",b="sort-button "+e[17]+" svelte-1bvc1p0"),J(a,"sorted",e[18]===e[93]),J(a,"des",e[18]===e[93]&&e[17]==="des"),w(l,"class","cell-wrap svelte-1bvc1p0"),w(t,"aria-sort",k=e[35](e[88],e[18],e[17])),ke(t,"width","var(--cell-width-"+e[93]+")"),w(t,"class","svelte-1bvc1p0"),J(t,"focus",e[25]===e[93]||e[27]===e[93]),this.first=t},m(q,M){K(q,t,M),E(t,l),Ae(i,l,null),E(l,s),E(l,a),E(a,d),E(d,f),E(t,m),A=!0,L||(S=be(a,"click",B),L=!0)},p(q,M){e=q;const p={};M[0]&32&&(p.latex_delimiters=e[5]),M[0]&2048&&(p.line_breaks=e[11]),M[0]&41943040&&(p.edit=e[25]===e[93]),M[0]&67108864&&(p.select_on_focus=e[26]),!r&&M[0]&8388608&&(r=!0,p.value=e[23][e[93]].value,me(()=>r=!1)),!u&&M[0]&12582912&&(u=!0,p.el=e[22][e[89]].input,me(()=>u=!1)),i.$set(p),(!A||M[0]&131072&&b!==(b="sort-button "+e[17]+" svelte-1bvc1p0"))&&w(a,"class",b),(!A||M[0]&8781824)&&J(a,"sorted",e[18]===e[93]),(!A||M[0]&8781824)&&J(a,"des",e[18]===e[93]&&e[17]==="des"),(!A||M[0]&8781824&&k!==(k=e[35](e[88],e[18],e[17])))&&w(t,"aria-sort",k),(!A||M[0]&8388608)&&ke(t,"width","var(--cell-width-"+e[93]+")"),(!A||M[0]&176160768)&&J(t,"focus",e[25]===e[93]||e[27]===e[93])},i(q){A||(V(i.$$.fragment,q),A=!0)},o(q){G(i.$$.fragment,q),A=!1},d(q){q&&I(t),ye(i),L=!1,S()}}}function Bn(n){let e,t=[],l=new Map,i,r=ge(n[23]);const u=s=>s[89];for(let s=0;s<r.length;s+=1){let a=Tt(n,r,s),d=u(a);l.set(d,t[s]=Vt(d,a))}return{c(){e=U("tr");for(let s=0;s<t.length;s+=1)t[s].c();w(e,"slot","thead"),w(e,"class","svelte-1bvc1p0")},m(s,a){K(s,e,a);for(let d=0;d<t.length;d+=1)t[d]&&t[d].m(e,null);i=!0},p(s,a){a[0]&247859232|a[1]&1808&&(r=ge(s[23]),Me(),t=Ze(t,a,u,1,s,r,l,e,Ye,Vt,null,Tt),ze())},i(s){if(!i){for(let a=0;a<r.length;a+=1)V(t[a]);i=!0}},o(s){for(let a=0;a<t.length;a+=1)G(t[a]);i=!1},d(s){s&&I(e);for(let a=0;a<t.length;a+=1)t[a].d()}}}function Wt(n,e){let t,l,i,r,u,s,a,d=`var(--cell-width-${e[91]})`,f,b,m;function k(y){e[55](y,e[86],e[91])}function A(y){e[56](y,e[89])}let L={display_value:e[13]?.[e[86]]?.[e[91]],latex_delimiters:e[5],line_breaks:e[11],editable:e[6],edit:ue(e[21],[e[86],e[91]]),datatype:Array.isArray(e[0])?e[0][e[91]]:e[0],clear_on_focus:e[24]};e[16][e[86]][e[91]].value!==void 0&&(L.value=e[16][e[86]][e[91]].value),e[22][e[89]].input!==void 0&&(L.el=e[22][e[89]].input),i=new Ge({props:L}),re.push(()=>we(i,"value",k)),re.push(()=>we(i,"el",A)),i.$on("blur",e[57]);function S(){return e[58](e[86],e[91])}function H(){return e[59](e[86],e[91])}function R(){return e[60](e[86],e[91])}return{key:n,first:null,c(){t=U("td"),l=U("div"),ve(i.$$.fragment),s=te(),w(l,"class","cell-wrap svelte-1bvc1p0"),w(t,"tabindex","0"),w(t,"style",a=e[14]?.[e[86]]?.[e[91]]||""),w(t,"class","svelte-1bvc1p0"),J(t,"focus",ue(e[15],[e[86],e[91]])),ke(t,"width",d),this.first=t},m(y,c){K(y,t,c),E(t,l),Ae(i,l,null),E(t,s),f=!0,b||(m=[be(t,"touchstart",S,{passive:!0}),be(t,"click",H),be(t,"dblclick",R)],b=!0)},p(y,c){e=y;const B={};c[0]&8192|c[2]&50331648&&(B.display_value=e[13]?.[e[86]]?.[e[91]]),c[0]&32&&(B.latex_delimiters=e[5]),c[0]&2048&&(B.line_breaks=e[11]),c[0]&64&&(B.editable=e[6]),c[0]&2097152|c[2]&50331648&&(B.edit=ue(e[21],[e[86],e[91]])),c[0]&1|c[2]&33554432&&(B.datatype=Array.isArray(e[0])?e[0][e[91]]:e[0]),c[0]&16777216&&(B.clear_on_focus=e[24]),!r&&c[0]&65536|c[2]&50331648&&(r=!0,B.value=e[16][e[86]][e[91]].value,me(()=>r=!1)),!u&&c[0]&4194304|c[2]&33554432&&(u=!0,B.el=e[22][e[89]].input,me(()=>u=!1)),i.$set(B),(!f||c[0]&16384|c[2]&50331648&&a!==(a=e[14]?.[e[86]]?.[e[91]]||""))&&w(t,"style",a),(!f||c[0]&32768|c[2]&50331648)&&J(t,"focus",ue(e[15],[e[86],e[91]]));const q=c[0]&16384|c[2]&50331648;(c[0]&16384|c[2]&50331648&&d!==(d=`var(--cell-width-${e[91]})`)||q)&&ke(t,"width",d)},i(y){f||(V(i.$$.fragment,y),f=!0)},o(y){G(i.$$.fragment,y),f=!1},d(y){y&&I(t),ye(i),b=!1,el(m)}}}function En(n){let e,t=[],l=new Map,i,r=ge(n[87]);const u=s=>s[89];for(let s=0;s<r.length;s+=1){let a=Ht(n,r,s),d=u(a);l.set(d,t[s]=Wt(d,a))}return{c(){e=U("tr");for(let s=0;s<t.length;s+=1)t[s].c();w(e,"slot","tbody"),w(e,"class","svelte-1bvc1p0"),J(e,"row_odd",n[86]%2===0)},m(s,a){K(s,e,a);for(let d=0;d<t.length;d+=1)t[d]&&t[d].m(e,null);i=!0},p(s,a){a[0]&560064609|a[1]&160|a[2]&50331648&&(r=ge(s[87]),Me(),t=Ze(t,a,u,1,s,r,l,e,Ye,Wt,null,Ht),ze()),(!i||a[2]&16777216)&&J(e,"row_odd",s[86]%2===0)},i(s){if(!i){for(let a=0;a<r.length;a+=1)V(t[a]);i=!0}},o(s){for(let a=0;a<t.length;a+=1)G(t[a]);i=!1},d(s){s&&I(e);for(let a=0;a<t.length;a+=1)t[a].d()}}}function Tn(n){let e,t,l,i,r;function u(f){n[65](f)}function s(f){n[66](f)}function a(f){n[67](f)}let d={max_height:n[10],selected:n[33],$$slots:{tbody:[En,({index:f,item:b})=>({86:f,87:b}),({index:f,item:b})=>[0,0,(f?16777216:0)|(b?33554432:0)]],thead:[Bn],default:[Rn]},$$scope:{ctx:n}};return n[16]!==void 0&&(d.items=n[16]),n[31]!==void 0&&(d.actual_height=n[31]),n[32]!==void 0&&(d.table_scrollbar_width=n[32]),e=new vn({props:d}),re.push(()=>we(e,"items",u)),re.push(()=>we(e,"actual_height",s)),re.push(()=>we(e,"table_scrollbar_width",a)),{c(){ve(e.$$.fragment)},m(f,b){Ae(e,f,b),r=!0},p(f,b){const m={};b[0]&1024&&(m.max_height=f[10]),b[1]&4&&(m.selected=f[33]),b[0]&803727459|b[2]&50331648|b[3]&8&&(m.$$scope={dirty:b,ctx:f}),!t&&b[0]&65536&&(t=!0,m.items=f[16],me(()=>t=!1)),!l&&b[1]&1&&(l=!0,m.actual_height=f[31],me(()=>l=!1)),!i&&b[1]&2&&(i=!0,m.table_scrollbar_width=f[32],me(()=>i=!1)),e.$set(m)},i(f){r||(V(e.$$.fragment,f),r=!0)},o(f){G(e.$$.fragment,f),r=!1},d(f){ye(e,f)}}}function It(n){let e,t,l,i=n[4][1]==="dynamic"&&Kt(n),r=n[3][1]==="dynamic"&&Yt(n);return{c(){e=U("div"),i&&i.c(),t=te(),r&&r.c(),w(e,"class","controls-wrap svelte-1bvc1p0")},m(u,s){K(u,e,s),i&&i.m(e,null),E(e,t),r&&r.m(e,null),l=!0},p(u,s){u[4][1]==="dynamic"?i?(i.p(u,s),s[0]&16&&V(i,1)):(i=Kt(u),i.c(),V(i,1),i.m(e,t)):i&&(Me(),G(i,1,1,()=>{i=null}),ze()),u[3][1]==="dynamic"?r?(r.p(u,s),s[0]&8&&V(r,1)):(r=Yt(u),r.c(),V(r,1),r.m(e,null)):r&&(Me(),G(r,1,1,()=>{r=null}),ze())},i(u){l||(V(i),V(r),l=!0)},o(u){G(i),G(r),l=!1},d(u){u&&I(e),i&&i.d(),r&&r.d()}}}function Kt(n){let e,t,l;return t=new Zt({props:{variant:"secondary",size:"sm",$$slots:{default:[Hn]},$$scope:{ctx:n}}}),t.$on("click",n[72]),{c(){e=U("span"),ve(t.$$.fragment),w(e,"class","button-wrap svelte-1bvc1p0")},m(i,r){K(i,e,r),Ae(t,e,null),l=!0},p(i,r){const u={};r[0]&512|r[3]&8&&(u.$$scope={dirty:r,ctx:i}),t.$set(u)},i(i){l||(V(t.$$.fragment,i),l=!0)},o(i){G(t.$$.fragment,i),l=!1},d(i){i&&I(e),ye(t)}}}function Hn(n){let e,t,l,i=n[9]("dataframe.new_row")+"",r;return{c(){e=pe("svg"),t=pe("path"),l=te(),r=Oe(i),w(t,"fill","currentColor"),w(t,"d","M24.59 16.59L17 24.17V4h-2v20.17l-7.59-7.58L6 18l10 10l10-10l-1.41-1.41z"),w(e,"xmlns","http://www.w3.org/2000/svg"),w(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),w(e,"aria-hidden","true"),w(e,"role","img"),w(e,"width","1em"),w(e,"height","1em"),w(e,"preserveAspectRatio","xMidYMid meet"),w(e,"viewBox","0 0 32 32"),w(e,"class","svelte-1bvc1p0")},m(u,s){K(u,e,s),E(e,t),K(u,l,s),K(u,r,s)},p(u,s){s[0]&512&&i!==(i=u[9]("dataframe.new_row")+"")&&Ne(r,i)},d(u){u&&(I(e),I(l),I(r))}}}function Yt(n){let e,t,l;return t=new Zt({props:{variant:"secondary",size:"sm",$$slots:{default:[Nn]},$$scope:{ctx:n}}}),t.$on("click",n[73]),{c(){e=U("span"),ve(t.$$.fragment),w(e,"class","button-wrap svelte-1bvc1p0")},m(i,r){K(i,e,r),Ae(t,e,null),l=!0},p(i,r){const u={};r[0]&512|r[3]&8&&(u.$$scope={dirty:r,ctx:i}),t.$set(u)},i(i){l||(V(t.$$.fragment,i),l=!0)},o(i){G(t.$$.fragment,i),l=!1},d(i){i&&I(e),ye(t)}}}function Nn(n){let e,t,l,i=n[9]("dataframe.new_column")+"",r;return{c(){e=pe("svg"),t=pe("path"),l=te(),r=Oe(i),w(t,"fill","currentColor"),w(t,"d","m18 6l-1.43 1.393L24.15 15H4v2h20.15l-7.58 7.573L18 26l10-10L18 6z"),w(e,"xmlns","http://www.w3.org/2000/svg"),w(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),w(e,"aria-hidden","true"),w(e,"role","img"),w(e,"width","1em"),w(e,"height","1em"),w(e,"preserveAspectRatio","xMidYMid meet"),w(e,"viewBox","0 0 32 32"),w(e,"class","svelte-1bvc1p0")},m(u,s){K(u,e,s),E(e,t),K(u,l,s),K(u,r,s)},p(u,s){s[0]&512&&i!==(i=u[9]("dataframe.new_column")+"")&&Ne(r,i)},d(u){u&&(I(e),I(l),I(r))}}}function On(n){let e,t,l,i,r,u,s,a=[],d=new Map,f,b,m,k=[],A=new Map,L,S,H,R,y,c,B,q,M=n[1]&&n[1].length!==0&&n[2]&&Ft(n),p=n[1]&&n[1].length!==0&&jt(n),T=ge(n[23]);const X=v=>v[89];for(let v=0;v<T.length;v+=1){let h=Ot(n,T,v),g=X(h);d.set(g,a[v]=Pt(g,h))}let N=ge(n[34]);const se=v=>v[89];for(let v=0;v<N.length;v+=1){let h=Nt(n,N,v),g=se(h);A.set(g,k[v]=Ut(g,h))}function he(v){n[68](v)}let Y={flex:!1,center:!1,boundedheight:!1,disable_click:!0,root:n[8],$$slots:{default:[Tn]},$$scope:{ctx:n}};n[28]!==void 0&&(Y.dragging=n[28]),H=new Rl({props:Y}),re.push(()=>we(H,"dragging",he)),H.$on("load",n[69]);let O=n[6]&&It(n);return{c(){e=U("div"),M&&M.c(),t=te(),l=U("div"),i=U("table"),p&&p.c(),r=te(),u=U("thead"),s=U("tr");for(let v=0;v<a.length;v+=1)a[v].c();f=te(),b=U("tbody"),m=U("tr");for(let v=0;v<k.length;v+=1)k[v].c();S=te(),ve(H.$$.fragment),y=te(),O&&O.c(),w(s,"class","svelte-1bvc1p0"),w(u,"class","svelte-1bvc1p0"),w(m,"class","svelte-1bvc1p0"),w(i,"class","svelte-1bvc1p0"),J(i,"fixed-layout",n[12].length!=0),w(l,"class","table-wrap svelte-1bvc1p0"),ke(l,"height",n[31]+"px"),w(l,"role","grid"),w(l,"tabindex","0"),J(l,"dragging",n[28]),J(l,"no-wrap",!n[7]),w(e,"class","svelte-1bvc1p0"),J(e,"label",n[1]&&n[1].length!==0)},m(v,h){K(v,e,h),M&&M.m(e,null),E(e,t),E(e,l),E(l,i),p&&p.m(i,null),E(i,r),E(i,u),E(u,s);for(let g=0;g<a.length;g+=1)a[g]&&a[g].m(s,null);E(i,f),E(i,b),E(b,m);for(let g=0;g<k.length;g+=1)k[g]&&k[g].m(m,null);L=Cn.observe(i,n[53].bind(i)),n[54](i),E(l,S),Ae(H,l,null),n[70](l),E(e,y),O&&O.m(e,null),c=!0,B||(q=[be(window,"click",n[44]),be(window,"touchstart",n[44]),be(window,"resize",n[51]),be(l,"keydown",n[71]),Sn(Ll.call(null,e))],B=!0)},p(v,h){v[1]&&v[1].length!==0&&v[2]?M?M.p(v,h):(M=Ft(v),M.c(),M.m(e,t)):M&&(M.d(1),M=null),v[1]&&v[1].length!==0?p?p.p(v,h):(p=jt(v),p.c(),p.m(i,r)):p&&(p.d(1),p=null),h[0]&42342432|h[1]&16&&(T=ge(v[23]),Me(),a=Ze(a,h,X,1,v,T,d,s,Ye,Pt,null,Ot),ze()),h[0]&526369|h[1]&8&&(N=ge(v[34]),Me(),k=Ze(k,h,se,1,v,N,A,m,Ye,Ut,null,Nt),ze()),(!c||h[0]&4096)&&J(i,"fixed-layout",v[12].length!=0);const g={};h[0]&256&&(g.root=v[8]),h[0]&803728483|h[1]&7|h[3]&8&&(g.$$scope={dirty:h,ctx:v}),!R&&h[0]&268435456&&(R=!0,g.dragging=v[28],me(()=>R=!1)),H.$set(g),(!c||h[1]&1)&&ke(l,"height",v[31]+"px"),(!c||h[0]&268435456)&&J(l,"dragging",v[28]),(!c||h[0]&128)&&J(l,"no-wrap",!v[7]),v[6]?O?(O.p(v,h),h[0]&64&&V(O,1)):(O=It(v),O.c(),V(O,1),O.m(e,null)):O&&(Me(),G(O,1,1,()=>{O=null}),ze()),(!c||h[0]&2)&&J(e,"label",v[1]&&v[1].length!==0)},i(v){if(!c){for(let h=0;h<T.length;h+=1)V(a[h]);for(let h=0;h<N.length;h+=1)V(k[h]);V(H.$$.fragment,v),V(O),c=!0}},o(v){for(let h=0;h<a.length;h+=1)G(a[h]);for(let h=0;h<k.length;h+=1)G(k[h]);G(H.$$.fragment,v),G(O),c=!1},d(v){v&&I(e),M&&M.d(),p&&p.d();for(let h=0;h<a.length;h+=1)a[h].d();for(let h=0;h<k.length;h+=1)k[h].d();L(),n[54](null),ye(H),n[70](null),O&&O.d(),B=!1,el(q)}}}function Be(){return Math.random().toString(36).substring(2,15)}function Fn(n,e){return e.filter(t);function t(l){var i=-1;return n.split(`
`).every(r);function r(u){if(!u)return!0;var s=u.split(l).length;return i<0&&(i=s),i===s&&s>1}}}function jn(n){const e=atob(n.split(",")[1]),t=n.split(",")[0].split(":")[1].split(";")[0],l=new ArrayBuffer(e.length),i=new Uint8Array(l);for(let r=0;r<e.length;r++)i[r]=e.charCodeAt(r);return new Blob([l],{type:t})}function Pn(n,e,t){let l,i,{datatype:r}=e,{label:u=null}=e,{show_label:s=!0}=e,{headers:a=[]}=e,{values:d=[]}=e,{col_count:f}=e,{row_count:b}=e,{latex_delimiters:m}=e,{editable:k=!0}=e,{wrap:A=!1}=e,{root:L}=e,{i18n:S}=e,{height:H=500}=e,{line_breaks:R=!0}=e,{column_widths:y=[]}=e,c=!1,{display_value:B=null}=e,{styling:q=null}=e,M;const p=Dn();let T=!1;const X=(o,_)=>h?.[o]?.[_]?.value;let N={};function se(o){let _=o||[];if(f[1]==="fixed"&&_.length<f[0]){const z=Array(f[0]-_.length).fill("").map((D,P)=>`${P+_.length}`);_=_.concat(z)}return!_||_.length===0?Array(f[0]).fill(0).map((z,D)=>{const P=Be();return t(22,N[P]={cell:null,input:null},N),{id:P,value:JSON.stringify(D+1)}}):_.map((z,D)=>{const P=Be();return t(22,N[P]={cell:null,input:null},N),{id:P,value:z??""}})}function he(o){const _=o.length;return Array(b[1]==="fixed"||_<b[0]?b[0]:_).fill(0).map((z,D)=>Array(f[1]==="fixed"?f[0]:_>0?o[0].length:a.length).fill(0).map((P,ee)=>{const ae=Be();return t(22,N[ae]=N[ae]||{input:null,cell:null},N),{value:o?.[D]?.[ee]??"",id:ae}}))}let Y=se(a),O;function v(){t(23,Y=se(a)),t(49,O=a.slice()),x()}let h=[[]],g;async function x(){p("change",{data:h.map(o=>o.map(({value:_})=>_)),headers:Y.map(o=>o.value),metadata:k?null:{display_value:B,styling:q}})}function xe(o,_,z){if(!_)return"none";if(a[_]===o){if(z==="asc")return"ascending";if(z==="des")return"descending"}return"none"}function C(o){return h.reduce((_,z,D)=>{const P=z.reduce((ee,ae,Ue)=>o===ae.id?Ue:ee,-1);return P===-1?_:[D,P]},[-1,-1])}async function W(o,_){!k||ue(T,[o,_])||t(21,T=[o,_])}function Q(o,_){const z={ArrowRight:[0,1],ArrowLeft:[0,-1],ArrowDown:[1,0],ArrowUp:[-1,0]}[o],D=_[0]+z[0],P=_[1]+z[1];if(D<0&&P<=0)t(27,j=P),t(15,c=!1);else{const ee=h[D]?.[P];t(15,c=ee?[D,P]:c)}}let le=!1;async function $(o){if(j!==!1&&fe===!1)switch(o.key){case"ArrowDown":t(15,c=[0,j]),t(27,j=!1);return;case"ArrowLeft":t(27,j=j>0?j-1:j);return;case"ArrowRight":t(27,j=j<Y.length-1?j+1:j);return;case"Escape":o.preventDefault(),t(27,j=!1);break;case"Enter":o.preventDefault();break}if(!c)return;const[_,z]=c;switch(o.key){case"ArrowRight":case"ArrowLeft":case"ArrowDown":case"ArrowUp":if(T)break;o.preventDefault(),Q(o.key,[_,z]);break;case"Escape":if(!k)break;o.preventDefault(),t(21,T=!1);break;case"Enter":if(!k)break;o.preventDefault(),o.shiftKey?(et(_),await We(),t(15,c=[_+1,z])):ue(T,[_,z])?(t(21,T=!1),await We(),t(15,c=[_,z])):t(21,T=[_,z]);break;case"Backspace":if(!k)break;T||(o.preventDefault(),t(16,h[_][z].value="",h));break;case"Delete":if(!k)break;T||(o.preventDefault(),t(16,h[_][z].value="",h));break;case"Tab":let D=o.shiftKey?-1:1,P=h[_][z+D],ee=h?.[_+D]?.[D>0?0:Y.length-1];(P||ee)&&(o.preventDefault(),t(15,c=P?[_,z+D]:[_+D,D>0?0:Y.length-1])),t(21,T=!1);break;default:if(!k)break;(!T||T&&ue(T,[_,z]))&&o.key.length===1&&(t(24,le=!0),t(21,T=[_,z]))}}async function ne(o,_){ue(T,[o,_])||(t(25,fe=!1),t(27,j=!1),t(21,T=!1),t(15,c=[o,_]),await We(),ie.focus())}let F,Z;function Ce(o){typeof Z!="number"||Z!==o?(t(17,F="asc"),t(18,Z=o)):F==="asc"?t(17,F="des"):F==="des"&&t(17,F="asc")}let fe,Fe=!1,j=!1;async function $e(o,_=!1){!k||f[1]!=="dynamic"||fe===o||(t(15,c=!1),t(27,j=o),t(25,fe=o),t(26,Fe=_))}function tl(o){if(k)switch(o.key){case"Escape":case"Enter":case"Tab":o.preventDefault(),t(15,c=!1),t(27,j=fe),t(25,fe=!1),ie.focus();break}}async function et(o){if(ie.focus(),b[1]==="dynamic"){if(h.length===0){t(48,d=[Array(a.length).fill("")]);return}h.splice(o?o+1:h.length,0,Array(h[0].length).fill(0).map((_,z)=>{const D=Be();return t(22,N[D]={cell:null,input:null},N),{id:D,value:""}})),t(16,h),t(48,d),t(50,g),t(15,c=[o?o+1:h.length-1,0])}}async function bt(){if(ie.focus(),f[1]==="dynamic"){for(let o=0;o<h.length;o++){const _=Be();t(22,N[_]={cell:null,input:null},N),h[o].push({id:_,value:""})}a.push(`Header ${a.length+1}`),t(16,h),t(48,d),t(50,g),t(47,a),await We(),requestAnimationFrame(()=>{$e(a.length-1,!0);const o=ie.querySelectorAll("tbody")[1].offsetWidth;ie.querySelectorAll("table")[1].scrollTo({left:o})})}}function ll(o){o.stopImmediatePropagation();const[_]=o.composedPath();ie.contains(_)||(t(21,T=!1),t(25,fe=!1),t(27,j=!1),t(15,c=!1))}function mt(o){const _=new FileReader;function z(D){if(!D?.target?.result||typeof D.target.result!="string")return;const[P]=Fn(D.target.result,[",","	"]),[ee,...ae]=El(P).parseRows(D.target.result);t(23,Y=se(f[1]==="fixed"?ee.slice(0,f[0]):ee)),t(48,d=ae),_.removeEventListener("loadend",z)}_.addEventListener("loadend",z),_.readAsText(o)}let tt=!1;function nl(o){let _=o[0].slice();for(let z=0;z<o.length;z++)for(let D=0;D<o[z].length;D++)`${_[D].value}`.length<`${o[z][D].value}`.length&&(_[D]=o[z][D]);return _}let Le=[],ie,lt;function je(){const o=Le.map((_,z)=>_?.clientWidth||0);if(o.length!==0)for(let _=0;_<o.length;_++)ie.style.setProperty(`--cell-width-${_}`,`${o[_]-Pe/o.length}px`)}let nt=H,Pe=0;function il(o,_,z,D,P){let ee=null;if(c&&c[0]in h&&c[1]in h[c[0]]&&(ee=h[c[0]][c[1]].id),typeof D!="number"||!P)return;const ae=[...Array(o.length).keys()];if(P==="asc")ae.sort((_e,ce)=>o[_e][D].value<o[ce][D].value?-1:1);else if(P==="des")ae.sort((_e,ce)=>o[_e][D].value>o[ce][D].value?-1:1);else return;const Ue=[...o],kt=_?[..._]:null,pt=z?[...z]:null;if(ae.forEach((_e,ce)=>{o[ce]=Ue[_e],_&&kt&&(_[ce]=kt[_e]),z&&pt&&(z[ce]=pt[_e])}),t(16,h),t(48,d),t(50,g),ee){const[_e,ce]=C(ee);t(15,c=[_e,ce])}}let wt=!1;Ln(()=>{const o=new IntersectionObserver((_,z)=>{_.forEach(D=>{D.isIntersecting&&!wt&&(je(),t(16,h),t(48,d),t(50,g)),wt=D.isIntersecting})});return o.observe(ie),()=>{o.disconnect()}});const sl=()=>je();function al(o,_){re[o?"unshift":"push"](()=>{Le[_]=o,t(19,Le)})}function ol(){M=yn.entries.get(this)?.contentRect,t(20,M)}function rl(o){re[o?"unshift":"push"](()=>{lt=o,t(30,lt)})}function fl(o,_,z){n.$$.not_equal(h[_][z].value,o)&&(h[_][z].value=o,t(16,h),t(48,d),t(50,g))}function ul(o,_){n.$$.not_equal(N[_].input,o)&&(N[_].input=o,t(22,N))}const _l=()=>(t(24,le=!1),ie.focus()),cl=(o,_)=>W(o,_),hl=(o,_)=>ne(o,_),dl=(o,_)=>W(o,_);function gl(o,_){n.$$.not_equal(Y[_].value,o)&&(Y[_].value=o,t(23,Y))}function bl(o,_){n.$$.not_equal(N[_].input,o)&&(N[_].input=o,t(22,N))}const ml=o=>$e(o),wl=o=>Ce(o);function kl(o){h=o,t(16,h),t(48,d),t(50,g)}function pl(o){nt=o,t(31,nt)}function vl(o){Pe=o,t(32,Pe)}function yl(o){tt=o,t(28,tt)}const Al=o=>mt(jn(o.detail.data));function Sl(o){re[o?"unshift":"push"](()=>{ie=o,t(29,ie)})}const zl=o=>$(o),Ml=o=>(o.stopPropagation(),et()),Cl=o=>(o.stopPropagation(),bt());return n.$$set=o=>{"datatype"in o&&t(0,r=o.datatype),"label"in o&&t(1,u=o.label),"show_label"in o&&t(2,s=o.show_label),"headers"in o&&t(47,a=o.headers),"values"in o&&t(48,d=o.values),"col_count"in o&&t(3,f=o.col_count),"row_count"in o&&t(4,b=o.row_count),"latex_delimiters"in o&&t(5,m=o.latex_delimiters),"editable"in o&&t(6,k=o.editable),"wrap"in o&&t(7,A=o.wrap),"root"in o&&t(8,L=o.root),"i18n"in o&&t(9,S=o.i18n),"height"in o&&t(10,H=o.height),"line_breaks"in o&&t(11,R=o.line_breaks),"column_widths"in o&&t(12,y=o.column_widths),"display_value"in o&&t(13,B=o.display_value),"styling"in o&&t(14,q=o.styling)},n.$$.update=()=>{if(n.$$.dirty[0]&32768&&c!==!1){const[o,_]=c;!isNaN(o)&&!isNaN(_)&&p("select",{index:[o,_],value:X(o,_)})}n.$$.dirty[1]&327680&&(ue(a,O)||v()),n.$$.dirty[1]&655360&&(ue(d,g)||(t(16,h=he(d)),t(50,g=d))),n.$$.dirty[0]&65536&&h&&x(),n.$$.dirty[0]&65536&&t(34,l=nl(h)),n.$$.dirty[0]&524288&&Le[0]&&je(),n.$$.dirty[0]&483328&&il(h,B,q,Z,F),n.$$.dirty[0]&32768&&t(33,i=!!c&&c[0])},[r,u,s,f,b,m,k,A,L,S,H,R,y,B,q,c,h,F,Z,Le,M,T,N,Y,le,fe,Fe,j,tt,ie,lt,nt,Pe,i,l,xe,W,$,ne,Ce,$e,tl,et,bt,ll,mt,je,a,d,O,g,sl,al,ol,rl,fl,ul,_l,cl,hl,dl,gl,bl,ml,wl,kl,pl,vl,yl,Al,Sl,zl,Ml,Cl]}class Un extends An{constructor(e){super(),Mn(this,e,Pn,On,qn,{datatype:0,label:1,show_label:2,headers:47,values:48,col_count:3,row_count:4,latex_delimiters:5,editable:6,wrap:7,root:8,i18n:9,height:10,line_breaks:11,column_widths:12,display_value:13,styling:14},null,[-1,-1,-1,-1])}}const Jn=Un,{SvelteComponent:Vn,assign:Wn,create_component:_t,destroy_component:ct,detach:In,get_spread_object:Kn,get_spread_update:Yn,init:Zn,insert:Gn,mount_component:ht,safe_not_equal:Qn,space:Xn,transition_in:dt,transition_out:gt}=window.__gradio__svelte__internal,{afterUpdate:xn,tick:$n}=window.__gradio__svelte__internal;function ei(n){let e,t,l,i;const r=[{autoscroll:n[14].autoscroll},{i18n:n[14].i18n},n[17]];let u={};for(let s=0;s<r.length;s+=1)u=Wn(u,r[s]);return e=new Dl({props:u}),l=new Jn({props:{root:n[11],label:n[5],show_label:n[6],row_count:n[4],col_count:n[3],values:n[22],display_value:n[20],styling:n[21],headers:n[19],wrap:n[7],datatype:n[8],latex_delimiters:n[15],editable:n[18],height:n[16],i18n:n[14].i18n,line_breaks:n[12],column_widths:n[13]}}),l.$on("change",n[28]),l.$on("select",n[29]),{c(){_t(e.$$.fragment),t=Xn(),_t(l.$$.fragment)},m(s,a){ht(e,s,a),Gn(s,t,a),ht(l,s,a),i=!0},p(s,a){const d=a[0]&147456?Yn(r,[a[0]&16384&&{autoscroll:s[14].autoscroll},a[0]&16384&&{i18n:s[14].i18n},a[0]&131072&&Kn(s[17])]):{};e.$set(d);const f={};a[0]&2048&&(f.root=s[11]),a[0]&32&&(f.label=s[5]),a[0]&64&&(f.show_label=s[6]),a[0]&16&&(f.row_count=s[4]),a[0]&8&&(f.col_count=s[3]),a[0]&4194304&&(f.values=s[22]),a[0]&1048576&&(f.display_value=s[20]),a[0]&2097152&&(f.styling=s[21]),a[0]&524288&&(f.headers=s[19]),a[0]&128&&(f.wrap=s[7]),a[0]&256&&(f.datatype=s[8]),a[0]&32768&&(f.latex_delimiters=s[15]),a[0]&262144&&(f.editable=s[18]),a[0]&65536&&(f.height=s[16]),a[0]&16384&&(f.i18n=s[14].i18n),a[0]&4096&&(f.line_breaks=s[12]),a[0]&8192&&(f.column_widths=s[13]),l.$set(f)},i(s){i||(dt(e.$$.fragment,s),dt(l.$$.fragment,s),i=!0)},o(s){gt(e.$$.fragment,s),gt(l.$$.fragment,s),i=!1},d(s){s&&In(t),ct(e,s),ct(l,s)}}}function ti(n){let e,t;return e=new ql({props:{visible:n[2],padding:!1,elem_id:n[0],elem_classes:n[1],container:!1,scale:n[9],min_width:n[10],allow_overflow:!1,$$slots:{default:[ei]},$$scope:{ctx:n}}}),{c(){_t(e.$$.fragment)},m(l,i){ht(e,l,i),t=!0},p(l,i){const r={};i[0]&4&&(r.visible=l[2]),i[0]&1&&(r.elem_id=l[0]),i[0]&2&&(r.elem_classes=l[1]),i[0]&512&&(r.scale=l[9]),i[0]&1024&&(r.min_width=l[10]),i[0]&8387064|i[1]&1&&(r.$$scope={dirty:i,ctx:l}),e.$set(r)},i(l){t||(dt(e.$$.fragment,l),t=!0)},o(l){gt(e.$$.fragment,l),t=!1},d(l){ct(e,l)}}}function li(n,e,t){let{headers:l=[]}=e,{elem_id:i=""}=e,{elem_classes:r=[]}=e,{visible:u=!0}=e,{value:s={data:[["","",""]],headers:["1","2","3"],metadata:null}}=e,a="",{value_is_output:d=!1}=e,{col_count:f}=e,{row_count:b}=e,{label:m=null}=e,{show_label:k=!0}=e,{wrap:A}=e,{datatype:L}=e,{scale:S=null}=e,{min_width:H=void 0}=e,{root:R}=e,{line_breaks:y=!0}=e,{column_widths:c=[]}=e,{gradio:B}=e,{latex_delimiters:q}=e,{height:M=void 0}=e,{loading_status:p}=e,{interactive:T}=e,X,N,se,he;async function Y(g){let x=g||s;t(19,X=[...x.headers||l]),t(22,he=x.data?[...x.data]:[]),t(20,N=x?.metadata?.display_value?[...x?.metadata?.display_value]:null),t(21,se=x?.metadata?.styling?[...x?.metadata?.styling]:null),await $n(),B.dispatch("change"),d||B.dispatch("input")}Y(),xn(()=>{t(25,d=!1)}),(Array.isArray(s)&&s?.[0]?.length===0||s.data?.[0]?.length===0)&&(s={data:[Array(f?.[0]||3).fill("")],headers:Array(f?.[0]||3).fill("").map((g,x)=>`${x+1}`),metadata:null});async function O(g){JSON.stringify(g)!==a&&(t(24,s={...g}),t(27,a=JSON.stringify(s)),Y(g))}const v=g=>O(g.detail),h=g=>B.dispatch("select",g.detail);return n.$$set=g=>{"headers"in g&&t(26,l=g.headers),"elem_id"in g&&t(0,i=g.elem_id),"elem_classes"in g&&t(1,r=g.elem_classes),"visible"in g&&t(2,u=g.visible),"value"in g&&t(24,s=g.value),"value_is_output"in g&&t(25,d=g.value_is_output),"col_count"in g&&t(3,f=g.col_count),"row_count"in g&&t(4,b=g.row_count),"label"in g&&t(5,m=g.label),"show_label"in g&&t(6,k=g.show_label),"wrap"in g&&t(7,A=g.wrap),"datatype"in g&&t(8,L=g.datatype),"scale"in g&&t(9,S=g.scale),"min_width"in g&&t(10,H=g.min_width),"root"in g&&t(11,R=g.root),"line_breaks"in g&&t(12,y=g.line_breaks),"column_widths"in g&&t(13,c=g.column_widths),"gradio"in g&&t(14,B=g.gradio),"latex_delimiters"in g&&t(15,q=g.latex_delimiters),"height"in g&&t(16,M=g.height),"loading_status"in g&&t(17,p=g.loading_status),"interactive"in g&&t(18,T=g.interactive)},n.$$.update=()=>{n.$$.dirty[0]&150994944&&a&&JSON.stringify(s)!==a&&(t(27,a=JSON.stringify(s)),Y())},[i,r,u,f,b,m,k,A,L,S,H,R,y,c,B,q,M,p,T,X,N,se,he,O,s,d,l,a,v,h]}class di extends Vn{constructor(e){super(),Zn(this,e,li,ti,Qn,{headers:26,elem_id:0,elem_classes:1,visible:2,value:24,value_is_output:25,col_count:3,row_count:4,label:5,show_label:6,wrap:7,datatype:8,scale:9,min_width:10,root:11,line_breaks:12,column_widths:13,gradio:14,latex_delimiters:15,height:16,loading_status:17,interactive:18},null,[-1,-1])}}export{Jn as BaseDataFrame,mi as BaseExample,di as default};
//# sourceMappingURL=Index-337282dc.js.map
