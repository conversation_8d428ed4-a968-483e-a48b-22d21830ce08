import"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{c as k}from"./utils-572af92b.js";import{M as g}from"./Example.svelte_svelte_type_style_lang-648fc18a.js";const{SvelteComponent:v,action_destroyer:w,attr:d,create_component:b,destroy_component:z,detach:M,element:j,init:y,insert:C,mount_component:q,safe_not_equal:D,toggle_class:h,transition_in:E,transition_out:I}=window.__gradio__svelte__internal,{createEventDispatcher:S}=window.__gradio__svelte__internal;function A(n){let e,l,r,m,a,f,o;return l=new g({props:{message:n[2],latex_delimiters:n[7],sanitize_html:n[5],line_breaks:n[6],chatbot:!1,header_links:n[8]}}),{c(){e=j("div"),b(l.$$.fragment),d(e,"class",r="prose "+n[0].join(" ")+" svelte-1yrv54"),d(e,"data-testid","markdown"),d(e,"dir",m=n[4]?"rtl":"ltr"),h(e,"min",n[3]),h(e,"hide",!n[1])},m(t,s){C(t,e,s),q(l,e,null),a=!0,f||(o=w(k.call(null,e)),f=!0)},p(t,[s]){const _={};s&4&&(_.message=t[2]),s&128&&(_.latex_delimiters=t[7]),s&32&&(_.sanitize_html=t[5]),s&64&&(_.line_breaks=t[6]),s&256&&(_.header_links=t[8]),l.$set(_),(!a||s&1&&r!==(r="prose "+t[0].join(" ")+" svelte-1yrv54"))&&d(e,"class",r),(!a||s&16&&m!==(m=t[4]?"rtl":"ltr"))&&d(e,"dir",m),(!a||s&9)&&h(e,"min",t[3]),(!a||s&3)&&h(e,"hide",!t[1])},i(t){a||(E(l.$$.fragment,t),a=!0)},o(t){I(l.$$.fragment,t),a=!1},d(t){t&&M(e),z(l),f=!1,o()}}}function B(n,e,l){let{elem_classes:r=[]}=e,{visible:m=!0}=e,{value:a}=e,{min_height:f=!1}=e,{rtl:o=!1}=e,{sanitize_html:t=!0}=e,{line_breaks:s=!1}=e,{latex_delimiters:_}=e,{header_links:u=!1}=e;const c=S();return n.$$set=i=>{"elem_classes"in i&&l(0,r=i.elem_classes),"visible"in i&&l(1,m=i.visible),"value"in i&&l(2,a=i.value),"min_height"in i&&l(3,f=i.min_height),"rtl"in i&&l(4,o=i.rtl),"sanitize_html"in i&&l(5,t=i.sanitize_html),"line_breaks"in i&&l(6,s=i.line_breaks),"latex_delimiters"in i&&l(7,_=i.latex_delimiters),"header_links"in i&&l(8,u=i.header_links)},n.$$.update=()=>{n.$$.dirty&4&&c("change")},[r,m,a,f,o,t,s,_,u]}class F extends v{constructor(e){super(),y(this,e,B,A,D,{elem_classes:0,visible:1,value:2,min_height:3,rtl:4,sanitize_html:5,line_breaks:6,latex_delimiters:7,header_links:8})}}const K=F;export{K as M};
//# sourceMappingURL=Index.svelte_svelte_type_style_lang-e1d4a36d.js.map
