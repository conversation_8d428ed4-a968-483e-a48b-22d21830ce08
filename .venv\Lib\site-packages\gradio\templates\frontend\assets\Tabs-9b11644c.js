import{w as S}from"./Index-26cfc80a.js";const{SvelteComponent:K,append:p,attr:c,component_subscribe:q,create_slot:L,destroy_block:O,detach:w,element:I,empty:y,ensure_array_like:B,get_all_dirty_from_scope:P,get_slot_changes:Q,init:R,insert:T,listen:U,safe_not_equal:V,set_data:G,set_store_value:D,space:A,text:H,toggle_class:E,transition_in:W,transition_out:X,update_keyed_each:Y,update_slot_base:Z}=window.__gradio__svelte__internal,{setContext:x,createEventDispatcher:$}=window.__gradio__svelte__internal;function M(l,e,n){const s=l.slice();return s[14]=e[n],s[16]=n,s}function z(l){let e;function n(a,o){return a[14].id===a[4]?le:ee}let s=n(l),i=s(l);return{c(){i.c(),e=y()},m(a,o){i.m(a,o),T(a,e,o)},p(a,o){s===(s=n(a))&&i?i.p(a,o):(i.d(1),i=s(a),i&&(i.c(),i.m(e.parentNode,e)))},d(a){a&&w(e),i.d(a)}}}function ee(l){let e,n=l[14].name+"",s,i,a,o,r,f,k,m;function d(){return l[12](l[14],l[16])}return{c(){e=I("button"),s=H(n),i=A(),c(e,"role","tab"),c(e,"aria-selected",!1),c(e,"aria-controls",a=l[14].elem_id),e.disabled=o=!l[14].interactive,c(e,"aria-disabled",r=!l[14].interactive),c(e,"id",f=l[14].elem_id?l[14].elem_id+"-button":null),c(e,"class","svelte-1uw5tnk")},m(t,u){T(t,e,u),p(e,s),p(e,i),k||(m=U(e,"click",d),k=!0)},p(t,u){l=t,u&8&&n!==(n=l[14].name+"")&&G(s,n),u&8&&a!==(a=l[14].elem_id)&&c(e,"aria-controls",a),u&8&&o!==(o=!l[14].interactive)&&(e.disabled=o),u&8&&r!==(r=!l[14].interactive)&&c(e,"aria-disabled",r),u&8&&f!==(f=l[14].elem_id?l[14].elem_id+"-button":null)&&c(e,"id",f)},d(t){t&&w(e),k=!1,m()}}}function le(l){let e,n=l[14].name+"",s,i,a,o;return{c(){e=I("button"),s=H(n),i=A(),c(e,"role","tab"),c(e,"class","selected svelte-1uw5tnk"),c(e,"aria-selected",!0),c(e,"aria-controls",a=l[14].elem_id),c(e,"id",o=l[14].elem_id?l[14].elem_id+"-button":null)},m(r,f){T(r,e,f),p(e,s),p(e,i)},p(r,f){f&8&&n!==(n=r[14].name+"")&&G(s,n),f&8&&a!==(a=r[14].elem_id)&&c(e,"aria-controls",a),f&8&&o!==(o=r[14].elem_id?r[14].elem_id+"-button":null)&&c(e,"id",o)},d(r){r&&w(e)}}}function F(l,e){let n,s,i=e[14].visible&&z(e);return{key:l,first:null,c(){n=y(),i&&i.c(),s=y(),this.first=n},m(a,o){T(a,n,o),i&&i.m(a,o),T(a,s,o)},p(a,o){e=a,e[14].visible?i?i.p(e,o):(i=z(e),i.c(),i.m(s.parentNode,s)):i&&(i.d(1),i=null)},d(a){a&&(w(n),w(s)),i&&i.d(a)}}}function te(l){let e,n,s=[],i=new Map,a,o,r,f=B(l[3]);const k=t=>t[14].id;for(let t=0;t<f.length;t+=1){let u=M(l,f,t),v=k(u);i.set(v,s[t]=F(v,u))}const m=l[11].default,d=L(m,l,l[10],null);return{c(){e=I("div"),n=I("div");for(let t=0;t<s.length;t+=1)s[t].c();a=A(),d&&d.c(),c(n,"class","tab-nav scroll-hide svelte-1uw5tnk"),c(n,"role","tablist"),c(e,"class",o="tabs "+l[2].join(" ")+" svelte-1uw5tnk"),c(e,"id",l[1]),E(e,"hide",!l[0])},m(t,u){T(t,e,u),p(e,n);for(let v=0;v<s.length;v+=1)s[v]&&s[v].m(n,null);p(e,a),d&&d.m(e,null),r=!0},p(t,[u]){u&408&&(f=B(t[3]),s=Y(s,u,k,1,t,f,i,n,O,F,null,M)),d&&d.p&&(!r||u&1024)&&Z(d,m,t,t[10],r?Q(m,t[10],u,null):P(t[10]),null),(!r||u&4&&o!==(o="tabs "+t[2].join(" ")+" svelte-1uw5tnk"))&&c(e,"class",o),(!r||u&2)&&c(e,"id",t[1]),(!r||u&5)&&E(e,"hide",!t[0])},i(t){r||(W(d,t),r=!0)},o(t){X(d,t),r=!1},d(t){t&&w(e);for(let u=0;u<s.length;u+=1)s[u].d();d&&d.d(t)}}}const ie={};function ne(l,e,n){let s,i,{$$slots:a={},$$scope:o}=e,{visible:r=!0}=e,{elem_id:f="id"}=e,{elem_classes:k=[]}=e,{selected:m}=e,d=[];const t=S(!1);q(l,t,_=>n(4,i=_));const u=S(0);q(l,u,_=>n(13,s=_));const v=$();x(ie,{register_tab:_=>{let b;return d.find(g=>g.id===_.id)?(b=d.findIndex(g=>g.id===_.id),n(3,d[b]={...d[b],..._},d)):(d.push({name:_.name,id:_.id,elem_id:_.elem_id,visible:_.visible,interactive:_.interactive}),b=d.length-1),t.update(g=>{if(g===!1&&_.visible&&_.interactive)return _.id;let C=d.find(N=>N.visible&&N.interactive);return C?C.id:g}),n(3,d),b},unregister_tab:_=>{const b=d.findIndex(h=>h.id===_.id);d.splice(b,1),t.update(h=>h===_.id?d[b]?.id||d[d.length-1]?.id:h)},selected_tab:t,selected_tab_index:u});function j(_){const b=d.find(h=>h.id===_);b&&b.interactive&&b.visible?(n(9,m=_),D(t,i=_,i),D(u,s=d.findIndex(h=>h.id===_),s),v("change")):console.warn("Attempted to select a non-interactive or hidden tab.")}const J=(_,b)=>{j(_.id),v("select",{value:_.name,index:b})};return l.$$set=_=>{"visible"in _&&n(0,r=_.visible),"elem_id"in _&&n(1,f=_.elem_id),"elem_classes"in _&&n(2,k=_.elem_classes),"selected"in _&&n(9,m=_.selected),"$$scope"in _&&n(10,o=_.$$scope)},l.$$.update=()=>{l.$$.dirty&520&&m!==null&&j(m)},[r,f,k,d,i,t,u,v,j,m,o,a,J]}class _e extends K{constructor(e){super(),R(this,e,ne,te,V,{visible:0,elem_id:1,elem_classes:2,selected:9})}}export{_e as T,ie as a};
//# sourceMappingURL=Tabs-9b11644c.js.map
