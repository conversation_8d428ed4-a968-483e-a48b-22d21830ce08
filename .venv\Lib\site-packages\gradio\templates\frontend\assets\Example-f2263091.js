import{I as M}from"./Image-21c02477.js";import"./file-url-bef2dc1b.js";import"./Index-26cfc80a.js";import"./index-a80d931b.js";import"./svelte/svelte.js";const{SvelteComponent:N,add_iframe_resize_listener:P,add_render_callback:W,append:h,attr:j,binding_callbacks:A,check_outros:S,create_component:D,destroy_component:F,destroy_each:G,detach:b,element:v,empty:H,ensure_array_like:w,group_outros:q,init:J,insert:k,mount_component:K,noop:z,safe_not_equal:L,set_data:B,space:O,text:C,toggle_class:p,transition_in:d,transition_out:g}=window.__gradio__svelte__internal,{onMount:Q}=window.__gradio__svelte__internal;function E(o,e,i){const n=o.slice();return n[8]=e[i],n}function R(o){let e=o[8].path+"",i;return{c(){i=C(e)},m(n,s){k(n,i,s)},p(n,s){s&1&&e!==(e=n[8].path+"")&&B(i,e)},i:z,o:z,d(n){n&&b(i)}}}function T(o){let e,i;return e=new M({props:{src:o[8].url,alt:""}}),{c(){D(e.$$.fragment)},m(n,s){K(e,n,s),i=!0},p(n,s){const a={};s&1&&(a.src=n[8].url),e.$set(a)},i(n){i||(d(e.$$.fragment,n),i=!0)},o(n){g(e.$$.fragment,n),i=!1},d(n){F(e,n)}}}function I(o){let e,i,n,s,a;const m=[T,R],r=[];function f(l,u){return u&1&&(e=null),e==null&&(e=!!(l[8].mime_type&&l[8].mime_type.includes("image"))),e?0:1}return i=f(o,-1),n=r[i]=m[i](o),{c(){n.c(),s=H()},m(l,u){r[i].m(l,u),k(l,s,u),a=!0},p(l,u){let t=i;i=f(l,u),i===t?r[i].p(l,u):(q(),g(r[t],1,1,()=>{r[t]=null}),S(),n=r[i],n?n.p(l,u):(n=r[i]=m[i](l),n.c()),d(n,1),n.m(s.parentNode,s))},i(l){a||(d(n),a=!0)},o(l){g(n),a=!1},d(l){l&&b(s),r[i].d(l)}}}function U(o){let e,i,n=(o[0].text?o[0].text:"")+"",s,a,m,r,f=w(o[0].files),l=[];for(let t=0;t<f.length;t+=1)l[t]=I(E(o,f,t));const u=t=>g(l[t],1,1,()=>{l[t]=null});return{c(){e=v("div"),i=v("p"),s=C(n),a=O();for(let t=0;t<l.length;t+=1)l[t].c();j(e,"class","svelte-ou7fr2"),W(()=>o[5].call(e)),p(e,"table",o[1]==="table"),p(e,"gallery",o[1]==="gallery"),p(e,"selected",o[2])},m(t,_){k(t,e,_),h(e,i),h(i,s),h(e,a);for(let c=0;c<l.length;c+=1)l[c]&&l[c].m(e,null);m=P(e,o[5].bind(e)),o[6](e),r=!0},p(t,[_]){if((!r||_&1)&&n!==(n=(t[0].text?t[0].text:"")+"")&&B(s,n),_&1){f=w(t[0].files);let c;for(c=0;c<f.length;c+=1){const y=E(t,f,c);l[c]?(l[c].p(y,_),d(l[c],1)):(l[c]=I(y),l[c].c(),d(l[c],1),l[c].m(e,null))}for(q(),c=f.length;c<l.length;c+=1)u(c);S()}(!r||_&2)&&p(e,"table",t[1]==="table"),(!r||_&2)&&p(e,"gallery",t[1]==="gallery"),(!r||_&4)&&p(e,"selected",t[2])},i(t){if(!r){for(let _=0;_<f.length;_+=1)d(l[_]);r=!0}},o(t){l=l.filter(Boolean);for(let _=0;_<l.length;_+=1)g(l[_]);r=!1},d(t){t&&b(e),G(l,t),m(),o[6](null)}}}function V(o,e,i){let{value:n={text:"",files:[]}}=e,{type:s}=e,{selected:a=!1}=e,m,r;function f(t,_){!t||!_||(r.style.setProperty("--local-text-width",`${_<150?_:200}px`),i(4,r.style.whiteSpace="unset",r))}Q(()=>{f(r,m)});function l(){m=this.clientWidth,i(3,m)}function u(t){A[t?"unshift":"push"](()=>{r=t,i(4,r)})}return o.$$set=t=>{"value"in t&&i(0,n=t.value),"type"in t&&i(1,s=t.type),"selected"in t&&i(2,a=t.selected)},[n,s,a,m,r,l,u]}class ee extends N{constructor(e){super(),J(this,e,V,U,L,{value:0,type:1,selected:2})}}export{ee as default};
//# sourceMappingURL=Example-f2263091.js.map
