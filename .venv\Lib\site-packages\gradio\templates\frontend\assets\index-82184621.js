import{U as mn}from"./ModifyUpload.svelte_svelte_type_style_lang-4ba51100.js";import{M as hn}from"./ModifyUpload-66b0c302.js";import{B as Jt}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{B as Gt}from"./BlockLabel-f27805b1.js";import{S as Kt}from"./Index-26cfc80a.js";import{V as ht}from"./Video-8670328f.js";import{S as gn}from"./SelectSource-f5281119.js";import{W as bn}from"./ImageUploader-528d6ef1.js";import"./Image-21c02477.js";/* empty css                                              */import{l as wn,t as vn,V as pn,p as kt,a as kn}from"./Video-fd4d29ec.js";import{b as $i}from"./Video-fd4d29ec.js";import{T as yn,P as En,a as Tn}from"./Trim-1b343e72.js";import{U as Qt}from"./Undo-b088de14.js";import{f as Te,u as Sn}from"./utils-572af92b.js";import{p as Vn,u as qn}from"./index-a80d931b.js";import{I as Dn}from"./IconButton-7294c90b.js";import{E as Cn}from"./Empty-28f63bf0.js";import{S as Bn}from"./ShareButton-7dae44e7.js";import{D as Ln,a as Mn}from"./DownloadLink-7ff36416.js";import{default as eo}from"./Example-f78abbcc.js";import{U as Rn}from"./UploadText-39c67ae9.js";import"./Clear-2c7bae91.js";import"./Upload-351cc897.js";import"./Image-eaba773f.js";import"./DropdownArrow-bb2afb7e.js";import"./file-url-bef2dc1b.js";import"./svelte/svelte.js";const{SvelteComponent:Pn,append:In,attr:$,detach:Nn,init:Un,insert:An,noop:rt,safe_not_equal:Hn,svg_element:yt}=window.__gradio__svelte__internal;function Xn(l){let e,n;return{c(){e=yt("svg"),n=yt("path"),$(n,"d","M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"),$(e,"xmlns","http://www.w3.org/2000/svg"),$(e,"width","100%"),$(e,"height","100%"),$(e,"viewBox","0 0 24 24"),$(e,"fill","none"),$(e,"stroke","currentColor"),$(e,"stroke-width","1.5"),$(e,"stroke-linecap","round"),$(e,"stroke-linejoin","round")},m(t,i){An(t,e,i),In(e,n)},p:rt,i:rt,o:rt,d(t){t&&Nn(e)}}}class zn extends Pn{constructor(e){super(),Un(this,e,null,Xn,Hn,{})}}const{SvelteComponent:Fn,append:pe,attr:A,destroy_block:jn,detach:tt,element:de,ensure_array_like:Et,init:On,insert:nt,listen:ke,noop:_t,run_all:Wn,safe_not_equal:Jn,set_style:ue,space:at,src_url_equal:Tt,update_keyed_each:Gn}=window.__gradio__svelte__internal,{onMount:St,onDestroy:Kn}=window.__gradio__svelte__internal;function Vt(l,e,n){const t=l.slice();return t[20]=e[n],t[22]=n,t}function Qn(l){let e,n,t,i,o,r=[],s=new Map,a,_,u,f,c=Et(l[1]);const d=h=>h[22];for(let h=0;h<c.length;h+=1){let m=Vt(l,c,h),k=d(m);s.set(k,r[h]=qt(k,m))}return{c(){e=de("div"),n=de("button"),t=at(),i=de("div"),o=at();for(let h=0;h<r.length;h+=1)r[h].c();a=at(),_=de("button"),A(n,"aria-label","start drag handle for trimming video"),A(n,"class","handle left svelte-10c4beq"),ue(n,"left",l[2]+"%"),A(i,"class","opaque-layer svelte-10c4beq"),ue(i,"left",l[2]+"%"),ue(i,"right",100-l[3]+"%"),A(_,"aria-label","end drag handle for trimming video"),A(_,"class","handle right svelte-10c4beq"),ue(_,"left",l[3]+"%"),A(e,"id","timeline"),A(e,"class","thumbnail-wrapper svelte-10c4beq")},m(h,m){nt(h,e,m),pe(e,n),pe(e,t),pe(e,i),pe(e,o);for(let k=0;k<r.length;k+=1)r[k]&&r[k].m(e,null);pe(e,a),pe(e,_),u||(f=[ke(n,"mousedown",l[10]),ke(n,"blur",l[5]),ke(n,"keydown",l[11]),ke(_,"mousedown",l[12]),ke(_,"blur",l[5]),ke(_,"keydown",l[13])],u=!0)},p(h,m){m&4&&ue(n,"left",h[2]+"%"),m&4&&ue(i,"left",h[2]+"%"),m&8&&ue(i,"right",100-h[3]+"%"),m&2&&(c=Et(h[1]),r=Gn(r,m,d,1,h,c,s,e,jn,qt,a,Vt)),m&8&&ue(_,"left",h[3]+"%")},d(h){h&&tt(e);for(let m=0;m<r.length;m+=1)r[m].d();u=!1,Wn(f)}}}function Yn(l){let e;return{c(){e=de("div"),e.innerHTML='<span aria-label="loading timeline" class="loader svelte-10c4beq"></span>',A(e,"class","load-wrap svelte-10c4beq")},m(n,t){nt(n,e,t)},p:_t,d(n){n&&tt(e)}}}function qt(l,e){let n,t,i;return{key:l,first:null,c(){n=de("img"),Tt(n.src,t=e[20])||A(n,"src",t),A(n,"alt",i=`frame-${e[22]}`),A(n,"draggable","false"),A(n,"class","svelte-10c4beq"),this.first=n},m(o,r){nt(o,n,r)},p(o,r){e=o,r&2&&!Tt(n.src,t=e[20])&&A(n,"src",t),r&2&&i!==(i=`frame-${e[22]}`)&&A(n,"alt",i)},d(o){o&&tt(n)}}}function Zn(l){let e;function n(o,r){return o[0]?Yn:Qn}let t=n(l),i=t(l);return{c(){e=de("div"),i.c(),A(e,"class","container svelte-10c4beq")},m(o,r){nt(o,e,r),i.m(e,null)},p(o,[r]){t===(t=n(o))&&i?i.p(o,r):(i.d(1),i=t(o),i&&(i.c(),i.m(e,null)))},i:_t,o:_t,d(o){o&&tt(e),i.d()}}}let st=10;function $n(l,e,n){let{videoElement:t}=e,{trimmedDuration:i}=e,{dragStart:o}=e,{dragEnd:r}=e,{loadingTimeline:s}=e,a=[],_,u=0,f=100,c=null;const d=g=>{c=g},h=()=>{c=null},m=(g,T)=>{if(c){const I=document.getElementById("timeline");if(!I)return;const S=I.getBoundingClientRect();let C=(g.clientX-S.left)/S.width*100;if(T?C=c==="left"?u+T:f+T:C=(g.clientX-S.left)/S.width*100,C=Math.max(0,Math.min(C,100)),c==="left"){n(2,u=Math.min(C,f));const L=u/100*_;n(6,t.currentTime=L,t),n(8,o=L)}else if(c==="right"){n(3,f=Math.max(C,u));const L=f/100*_;n(6,t.currentTime=L,t),n(9,r=L)}const W=u/100*_,R=f/100*_;n(7,i=R-W),n(2,u),n(3,f)}},k=g=>{if(c){const T=1/_*100;g.key==="ArrowLeft"?m({clientX:0},-T):g.key==="ArrowRight"&&m({clientX:0},T)}},q=()=>{const g=document.createElement("canvas"),T=g.getContext("2d");if(!T)return;g.width=t.videoWidth,g.height=t.videoHeight,T.drawImage(t,0,0,g.width,g.height);const I=g.toDataURL("image/jpeg",.7);n(1,a=[...a,I])};St(()=>{const g=()=>{_=t.duration;const T=_/st;let I=0;const S=()=>{q(),I++,I<st?n(6,t.currentTime+=T,t):t.removeEventListener("seeked",S)};t.addEventListener("seeked",S),n(6,t.currentTime=0,t)};t.readyState>=1?g():t.addEventListener("loadedmetadata",g)}),Kn(()=>{window.removeEventListener("mousemove",m),window.removeEventListener("mouseup",h),window.removeEventListener("keydown",k)}),St(()=>{window.addEventListener("mousemove",m),window.addEventListener("mouseup",h),window.addEventListener("keydown",k)});const E=()=>d("left"),D=g=>{(g.key==="ArrowLeft"||g.key=="ArrowRight")&&d("left")},P=()=>d("right"),w=g=>{(g.key==="ArrowLeft"||g.key=="ArrowRight")&&d("right")};return l.$$set=g=>{"videoElement"in g&&n(6,t=g.videoElement),"trimmedDuration"in g&&n(7,i=g.trimmedDuration),"dragStart"in g&&n(8,o=g.dragStart),"dragEnd"in g&&n(9,r=g.dragEnd),"loadingTimeline"in g&&n(0,s=g.loadingTimeline)},l.$$.update=()=>{l.$$.dirty&2&&n(0,s=a.length!==st)},[s,a,u,f,d,h,t,i,o,r,E,D,P,w]}class xn extends Fn{constructor(e){super(),On(this,e,$n,Zn,Jn,{videoElement:6,trimmedDuration:7,dragStart:8,dragEnd:9,loadingTimeline:0})}}const{SvelteComponent:el,add_flush_callback:Xe,append:Ee,attr:O,bind:ze,binding_callbacks:Fe,check_outros:je,create_component:gt,destroy_component:bt,detach:ee,element:te,empty:tl,group_outros:Oe,init:nl,insert:ne,listen:Ye,mount_component:wt,noop:ft,run_all:ll,safe_not_equal:il,set_data:ol,space:We,text:rl,toggle_class:Se,transition_in:j,transition_out:Y}=window.__gradio__svelte__internal,{onMount:al}=window.__gradio__svelte__internal;function Dt(l){let e,n,t,i,o,r,s;function a(d){l[13](d)}function _(d){l[14](d)}function u(d){l[15](d)}function f(d){l[16](d)}let c={videoElement:l[2]};return l[9]!==void 0&&(c.dragStart=l[9]),l[10]!==void 0&&(c.dragEnd=l[10]),l[7]!==void 0&&(c.trimmedDuration=l[7]),l[11]!==void 0&&(c.loadingTimeline=l[11]),n=new xn({props:c}),Fe.push(()=>ze(n,"dragStart",a)),Fe.push(()=>ze(n,"dragEnd",_)),Fe.push(()=>ze(n,"trimmedDuration",u)),Fe.push(()=>ze(n,"loadingTimeline",f)),{c(){e=te("div"),gt(n.$$.fragment),O(e,"class","timeline-wrapper svelte-sxyn79")},m(d,h){ne(d,e,h),wt(n,e,null),s=!0},p(d,h){const m={};h&4&&(m.videoElement=d[2]),!t&&h&512&&(t=!0,m.dragStart=d[9],Xe(()=>t=!1)),!i&&h&1024&&(i=!0,m.dragEnd=d[10],Xe(()=>i=!1)),!o&&h&128&&(o=!0,m.trimmedDuration=d[7],Xe(()=>o=!1)),!r&&h&2048&&(r=!0,m.loadingTimeline=d[11],Xe(()=>r=!1)),n.$set(m)},i(d){s||(j(n.$$.fragment,d),s=!0)},o(d){Y(n.$$.fragment,d),s=!1},d(d){d&&ee(e),bt(n)}}}function sl(l){let e;return{c(){e=te("div"),O(e,"class","svelte-sxyn79")},m(n,t){ne(n,e,t)},p:ft,d(n){n&&ee(e)}}}function ul(l){let e,n=Te(l[7])+"",t;return{c(){e=te("time"),t=rl(n),O(e,"aria-label","duration of selected region in seconds"),O(e,"class","svelte-sxyn79"),Se(e,"hidden",l[11])},m(i,o){ne(i,e,o),Ee(e,t)},p(i,o){o&128&&n!==(n=Te(i[7])+"")&&ol(t,n),o&2048&&Se(e,"hidden",i[11])},d(i){i&&ee(e)}}}function Ct(l){let e,n,t,i,o;return n=new Qt({}),{c(){e=te("button"),gt(n.$$.fragment),O(e,"class","action icon svelte-sxyn79"),e.disabled=l[1],O(e,"aria-label","Reset video to initial value")},m(r,s){ne(r,e,s),wt(n,e,null),t=!0,i||(o=Ye(e,"click",l[17]),i=!0)},p(r,s){(!t||s&2)&&(e.disabled=r[1])},i(r){t||(j(n.$$.fragment,r),t=!0)},o(r){Y(n.$$.fragment,r),t=!1},d(r){r&&ee(e),bt(n),i=!1,o()}}}function Bt(l){let e,n,t,i;const o=[fl,_l],r=[];function s(a,_){return a[0]===""?0:1}return e=s(l),n=r[e]=o[e](l),{c(){n.c(),t=tl()},m(a,_){r[e].m(a,_),ne(a,t,_),i=!0},p(a,_){let u=e;e=s(a),e===u?r[e].p(a,_):(Oe(),Y(r[u],1,1,()=>{r[u]=null}),je(),n=r[e],n?n.p(a,_):(n=r[e]=o[e](a),n.c()),j(n,1),n.m(t.parentNode,t))},i(a){i||(j(n),i=!0)},o(a){Y(n),i=!1},d(a){a&&ee(t),r[e].d(a)}}}function _l(l){let e,n,t,i,o;return{c(){e=te("button"),e.textContent="Trim",n=We(),t=te("button"),t.textContent="Cancel",O(e,"class","text-button svelte-sxyn79"),Se(e,"hidden",l[11]),O(t,"class","text-button svelte-sxyn79"),Se(t,"hidden",l[11])},m(r,s){ne(r,e,s),ne(r,n,s),ne(r,t,s),i||(o=[Ye(e,"click",l[18]),Ye(t,"click",l[12])],i=!0)},p(r,s){s&2048&&Se(e,"hidden",r[11]),s&2048&&Se(t,"hidden",r[11])},i:ft,o:ft,d(r){r&&(ee(e),ee(n),ee(t)),i=!1,ll(o)}}}function fl(l){let e,n,t,i,o;return n=new yn({}),{c(){e=te("button"),gt(n.$$.fragment),e.disabled=l[1],O(e,"class","action icon svelte-sxyn79"),O(e,"aria-label","Trim video to selection")},m(r,s){ne(r,e,s),wt(n,e,null),t=!0,i||(o=Ye(e,"click",l[12]),i=!0)},p(r,s){(!t||s&2)&&(e.disabled=r[1])},i(r){t||(j(n.$$.fragment,r),t=!0)},o(r){Y(n.$$.fragment,r),t=!1},d(r){r&&ee(e),bt(n),i=!1,o()}}}function cl(l){let e,n,t,i,o,r,s,a=l[0]==="edit"&&Dt(l);function _(h,m){return h[0]==="edit"&&h[7]!==null?ul:sl}let u=_(l),f=u(l),c=l[3]&&l[0]===""&&Ct(l),d=l[4]&&Bt(l);return{c(){e=te("div"),a&&a.c(),n=We(),t=te("div"),f.c(),i=We(),o=te("div"),c&&c.c(),r=We(),d&&d.c(),O(o,"class","settings-wrapper svelte-sxyn79"),O(t,"class","controls svelte-sxyn79"),O(t,"data-testid","waveform-controls"),O(e,"class","container svelte-sxyn79")},m(h,m){ne(h,e,m),a&&a.m(e,null),Ee(e,n),Ee(e,t),f.m(t,null),Ee(t,i),Ee(t,o),c&&c.m(o,null),Ee(o,r),d&&d.m(o,null),s=!0},p(h,[m]){h[0]==="edit"?a?(a.p(h,m),m&1&&j(a,1)):(a=Dt(h),a.c(),j(a,1),a.m(e,n)):a&&(Oe(),Y(a,1,1,()=>{a=null}),je()),u===(u=_(h))&&f?f.p(h,m):(f.d(1),f=u(h),f&&(f.c(),f.m(t,i))),h[3]&&h[0]===""?c?(c.p(h,m),m&9&&j(c,1)):(c=Ct(h),c.c(),j(c,1),c.m(o,r)):c&&(Oe(),Y(c,1,1,()=>{c=null}),je()),h[4]?d?(d.p(h,m),m&16&&j(d,1)):(d=Bt(h),d.c(),j(d,1),d.m(o,null)):d&&(Oe(),Y(d,1,1,()=>{d=null}),je())},i(h){s||(j(a),j(c),j(d),s=!0)},o(h){Y(a),Y(c),Y(d),s=!1},d(h){h&&ee(e),a&&a.d(),f.d(),c&&c.d(),d&&d.d()}}}function dl(l,e,n){let{videoElement:t}=e,{showRedo:i=!1}=e,{interactive:o=!0}=e,{mode:r=""}=e,{handle_reset_value:s}=e,{handle_trim_video:a}=e,{processingVideo:_=!1}=e,u;al(async()=>{n(8,u=await wn())});let f=null,c=0,d=0,h=!1;const m=()=>{r==="edit"?(n(0,r=""),n(7,f=t.duration)):n(0,r="edit")};function k(g){c=g,n(9,c)}function q(g){d=g,n(10,d)}function E(g){f=g,n(7,f),n(0,r),n(2,t)}function D(g){h=g,n(11,h)}const P=()=>{s(),n(0,r="")},w=()=>{n(0,r=""),n(1,_=!0),vn(u,c,d,t).then(g=>{a(g)}).then(()=>{n(1,_=!1)})};return l.$$set=g=>{"videoElement"in g&&n(2,t=g.videoElement),"showRedo"in g&&n(3,i=g.showRedo),"interactive"in g&&n(4,o=g.interactive),"mode"in g&&n(0,r=g.mode),"handle_reset_value"in g&&n(5,s=g.handle_reset_value),"handle_trim_video"in g&&n(6,a=g.handle_trim_video),"processingVideo"in g&&n(1,_=g.processingVideo)},l.$$.update=()=>{l.$$.dirty&133&&r==="edit"&&f===null&&t&&n(7,f=t.duration)},[r,_,t,i,o,s,a,f,u,c,d,h,m,k,q,E,D,P,w]}class ml extends el{constructor(e){super(),nl(this,e,dl,cl,il,{videoElement:2,showRedo:3,interactive:4,mode:0,handle_reset_value:5,handle_trim_video:6,processingVideo:1})}}const{SvelteComponent:hl,add_flush_callback:Pe,append:F,attr:N,bind:Ie,binding_callbacks:Ne,bubble:Lt,check_outros:Mt,create_component:Ve,destroy_component:qe,detach:Je,element:ie,empty:gl,group_outros:Rt,init:bl,insert:Ge,listen:fe,mount_component:De,prevent_default:Pt,run_all:wl,safe_not_equal:vl,set_data:It,space:Re,src_url_equal:Nt,stop_propagation:pl,text:ut,toggle_class:Ut,transition_in:Q,transition_out:x}=window.__gradio__svelte__internal,{createEventDispatcher:kl}=window.__gradio__svelte__internal;function yl(l){let e,n;return{c(){e=ie("track"),N(e,"kind","captions"),Nt(e.src,n=l[1])||N(e,"src",n),e.default=!0},m(t,i){Ge(t,e,i)},p(t,i){i&2&&!Nt(e.src,n=t[1])&&N(e,"src",n)},d(t){t&&Je(e)}}}function El(l){let e,n;return e=new En({}),{c(){Ve(e.$$.fragment)},m(t,i){De(e,t,i),n=!0},i(t){n||(Q(e.$$.fragment,t),n=!0)},o(t){x(e.$$.fragment,t),n=!1},d(t){qe(e,t)}}}function Tl(l){let e,n;return e=new Tn({}),{c(){Ve(e.$$.fragment)},m(t,i){De(e,t,i),n=!0},i(t){n||(Q(e.$$.fragment,t),n=!0)},o(t){x(e.$$.fragment,t),n=!1},d(t){qe(e,t)}}}function Sl(l){let e,n;return e=new Qt({}),{c(){Ve(e.$$.fragment)},m(t,i){De(e,t,i),n=!0},i(t){n||(Q(e.$$.fragment,t),n=!0)},o(t){x(e.$$.fragment,t),n=!1},d(t){qe(e,t)}}}function At(l){let e,n,t;function i(r){l[26](r)}let o={videoElement:l[10],showRedo:!0,handle_trim_video:l[16],handle_reset_value:l[6]};return l[11]!==void 0&&(o.processingVideo=l[11]),e=new ml({props:o}),Ne.push(()=>Ie(e,"processingVideo",i)),{c(){Ve(e.$$.fragment)},m(r,s){De(e,r,s),t=!0},p(r,s){const a={};s&1024&&(a.videoElement=r[10]),s&64&&(a.handle_reset_value=r[6]),!n&&s&2048&&(n=!0,a.processingVideo=r[11],Pe(()=>n=!1)),e.$set(a)},i(r){t||(Q(e.$$.fragment,r),t=!0)},o(r){x(e.$$.fragment,r),t=!1},d(r){qe(e,r)}}}function Vl(l){let e,n,t,i,o,r,s,a,_,u,f,c,d,h,m,k=Te(l[7])+"",q,E,D=Te(l[8])+"",P,w,g,T,I,S,C,W,R,L,Z,p;function J(y){l[20](y)}function G(y){l[21](y)}function b(y){l[22](y)}function ot(y){l[23](y)}let _e={src:l[0],preload:"auto",autoplay:l[3],"data-testid":`${l[4]}-player`,processingVideo:l[11],$$slots:{default:[yl]},$$scope:{ctx:l}};l[7]!==void 0&&(_e.currentTime=l[7]),l[8]!==void 0&&(_e.duration=l[8]),l[9]!==void 0&&(_e.paused=l[9]),l[10]!==void 0&&(_e.node=l[10]),t=new pn({props:_e}),Ne.push(()=>Ie(t,"currentTime",J)),Ne.push(()=>Ie(t,"duration",G)),Ne.push(()=>Ie(t,"paused",b)),Ne.push(()=>Ie(t,"node",ot)),t.$on("click",l[13]),t.$on("play",l[24]),t.$on("pause",l[25]),t.$on("ended",l[15]);const Ae=[Sl,Tl,El],le=[];function He(y,V){return y[7]===y[8]?0:y[9]?1:2}c=He(l),d=le[c]=Ae[c](l),C=new zn({});let M=l[5]&&At(l);return{c(){e=ie("div"),n=ie("div"),Ve(t.$$.fragment),a=Re(),_=ie("div"),u=ie("div"),f=ie("span"),d.c(),h=Re(),m=ie("span"),q=ut(k),E=ut(" / "),P=ut(D),w=Re(),g=ie("progress"),I=Re(),S=ie("div"),Ve(C.$$.fragment),W=Re(),M&&M.c(),R=gl(),N(n,"class","mirror-wrap svelte-euo1cw"),Ut(n,"mirror",l[2]),N(f,"role","button"),N(f,"tabindex","0"),N(f,"class","icon svelte-euo1cw"),N(f,"aria-label","play-pause-replay-button"),N(m,"class","time svelte-euo1cw"),g.value=T=l[7]/l[8]||0,N(g,"class","svelte-euo1cw"),N(S,"role","button"),N(S,"tabindex","0"),N(S,"class","icon svelte-euo1cw"),N(S,"aria-label","full-screen"),N(u,"class","inner svelte-euo1cw"),N(_,"class","controls svelte-euo1cw"),N(e,"class","wrap svelte-euo1cw")},m(y,V){Ge(y,e,V),F(e,n),De(t,n,null),F(e,a),F(e,_),F(_,u),F(u,f),le[c].m(f,null),F(u,h),F(u,m),F(m,q),F(m,E),F(m,P),F(u,w),F(u,g),F(u,I),F(u,S),De(C,S,null),Ge(y,W,V),M&&M.m(y,V),Ge(y,R,V),L=!0,Z||(p=[fe(f,"click",l[13]),fe(f,"keydown",l[13]),fe(g,"mousemove",l[12]),fe(g,"touchmove",Pt(l[12])),fe(g,"click",pl(Pt(l[14]))),fe(S,"click",l[17]),fe(S,"keypress",l[17])],Z=!0)},p(y,[V]){const K={};V&1&&(K.src=y[0]),V&8&&(K.autoplay=y[3]),V&16&&(K["data-testid"]=`${y[4]}-player`),V&2048&&(K.processingVideo=y[11]),V&268435458&&(K.$$scope={dirty:V,ctx:y}),!i&&V&128&&(i=!0,K.currentTime=y[7],Pe(()=>i=!1)),!o&&V&256&&(o=!0,K.duration=y[8],Pe(()=>o=!1)),!r&&V&512&&(r=!0,K.paused=y[9],Pe(()=>r=!1)),!s&&V&1024&&(s=!0,K.node=y[10],Pe(()=>s=!1)),t.$set(K),(!L||V&4)&&Ut(n,"mirror",y[2]);let Me=c;c=He(y),c!==Me&&(Rt(),x(le[Me],1,1,()=>{le[Me]=null}),Mt(),d=le[c],d||(d=le[c]=Ae[c](y),d.c()),Q(d,1),d.m(f,null)),(!L||V&128)&&k!==(k=Te(y[7])+"")&&It(q,k),(!L||V&256)&&D!==(D=Te(y[8])+"")&&It(P,D),(!L||V&384&&T!==(T=y[7]/y[8]||0))&&(g.value=T),y[5]?M?(M.p(y,V),V&32&&Q(M,1)):(M=At(y),M.c(),Q(M,1),M.m(R.parentNode,R)):M&&(Rt(),x(M,1,1,()=>{M=null}),Mt())},i(y){L||(Q(t.$$.fragment,y),Q(d),Q(C.$$.fragment,y),Q(M),L=!0)},o(y){x(t.$$.fragment,y),x(d),x(C.$$.fragment,y),x(M),L=!1},d(y){y&&(Je(e),Je(W),Je(R)),qe(t),le[c].d(),qe(C),M&&M.d(y),Z=!1,wl(p)}}}function ql(l,e,n){let{root:t=""}=e,{src:i}=e,{subtitle:o=null}=e,{mirror:r}=e,{autoplay:s}=e,{label:a="test"}=e,{interactive:_=!1}=e,{handle_change:u=()=>{}}=e,{handle_reset_value:f=()=>{}}=e;const c=kl();let d=0,h,m=!0,k,q=!1;function E(p){if(!h)return;if(p.type==="click"){P(p);return}if(p.type!=="touchmove"&&!(p.buttons&1))return;const J=p.type==="touchmove"?p.touches[0].clientX:p.clientX,{left:G,right:b}=p.currentTarget.getBoundingClientRect();n(7,d=h*(J-G)/(b-G))}async function D(){document.fullscreenElement!=k&&(k.currentTime>0&&!k.paused&&!k.ended&&k.readyState>k.HAVE_CURRENT_DATA?k.pause():await k.play())}function P(p){const{left:J,right:G}=p.currentTarget.getBoundingClientRect();n(7,d=h*(p.clientX-J)/(G-J))}function w(){c("stop"),c("end")}const g=async p=>{let J=new File([p],"video.mp4");const G=await Vn([J]);let b=(await qn(G,t))?.filter(Boolean)[0];u(b)};function T(){k.requestFullscreen()}function I(p){d=p,n(7,d)}function S(p){h=p,n(8,h)}function C(p){m=p,n(9,m)}function W(p){k=p,n(10,k)}function R(p){Lt.call(this,l,p)}function L(p){Lt.call(this,l,p)}function Z(p){q=p,n(11,q)}return l.$$set=p=>{"root"in p&&n(18,t=p.root),"src"in p&&n(0,i=p.src),"subtitle"in p&&n(1,o=p.subtitle),"mirror"in p&&n(2,r=p.mirror),"autoplay"in p&&n(3,s=p.autoplay),"label"in p&&n(4,a=p.label),"interactive"in p&&n(5,_=p.interactive),"handle_change"in p&&n(19,u=p.handle_change),"handle_reset_value"in p&&n(6,f=p.handle_reset_value)},[i,o,r,s,a,_,f,d,h,m,k,q,E,D,P,w,g,T,t,u,I,S,C,W,R,L,Z]}class Dl extends hl{constructor(e){super(),bl(this,e,ql,Vl,vl,{root:18,src:0,subtitle:1,mirror:2,autoplay:3,label:4,interactive:5,handle_change:19,handle_reset_value:6})}}const Yt=Dl;const{SvelteComponent:Cl,add_flush_callback:Zt,append:ct,attr:Ue,bind:$t,binding_callbacks:xt,bubble:ce,check_outros:lt,create_component:Ce,create_slot:Bl,destroy_component:Be,detach:oe,element:Ze,empty:en,get_all_dirty_from_scope:Ll,get_slot_changes:Ml,group_outros:it,init:Rl,insert:re,mount_component:Le,noop:dt,safe_not_equal:tn,set_data:Ht,space:$e,text:Xt,transition_in:X,transition_out:z,update_slot_base:Pl}=window.__gradio__svelte__internal,{createEventDispatcher:Il}=window.__gradio__svelte__internal;function Nl(l){let e,n,t,i,o,r,s;e=new hn({props:{i18n:l[11],download:l[5]?l[0].url:null}}),e.$on("clear",l[16]);const a=[Hl,Al],_=[];function u(f,c){return t==null&&(t=!!kn()),t?0:f[0].size?1:-1}return~(i=u(l))&&(o=_[i]=a[i](l)),{c(){Ce(e.$$.fragment),n=$e(),o&&o.c(),r=en()},m(f,c){Le(e,f,c),re(f,n,c),~i&&_[i].m(f,c),re(f,r,c),s=!0},p(f,c){const d={};c&2048&&(d.i18n=f[11]),c&33&&(d.download=f[5]?f[0].url:null),e.$set(d);let h=i;i=u(f),i===h?~i&&_[i].p(f,c):(o&&(it(),z(_[h],1,1,()=>{_[h]=null}),lt()),~i?(o=_[i],o?o.p(f,c):(o=_[i]=a[i](f),o.c()),X(o,1),o.m(r.parentNode,r)):o=null)},i(f){s||(X(e.$$.fragment,f),X(o),s=!0)},o(f){z(e.$$.fragment,f),z(o),s=!1},d(f){f&&(oe(n),oe(r)),Be(e,f),~i&&_[i].d(f)}}}function Ul(l){let e,n,t,i;const o=[zl,Xl],r=[];function s(a,_){return a[1]==="upload"?0:a[1]==="webcam"?1:-1}return~(n=s(l))&&(t=r[n]=o[n](l)),{c(){e=Ze("div"),t&&t.c(),Ue(e,"class","upload-container svelte-1cs6pot")},m(a,_){re(a,e,_),~n&&r[n].m(e,null),i=!0},p(a,_){let u=n;n=s(a),n===u?~n&&r[n].p(a,_):(t&&(it(),z(r[u],1,1,()=>{r[u]=null}),lt()),~n?(t=r[n],t?t.p(a,_):(t=r[n]=o[n](a),t.c()),X(t,1),t.m(e,null)):t=null)},i(a){i||(X(t),i=!0)},o(a){z(t),i=!1},d(a){a&&oe(e),~n&&r[n].d()}}}function Al(l){let e,n=(l[0].orig_name||l[0].url)+"",t,i,o,r=kt(l[0].size)+"",s;return{c(){e=Ze("div"),t=Xt(n),i=$e(),o=Ze("div"),s=Xt(r),Ue(e,"class","file-name svelte-1cs6pot"),Ue(o,"class","file-size svelte-1cs6pot")},m(a,_){re(a,e,_),ct(e,t),re(a,i,_),re(a,o,_),ct(o,s)},p(a,_){_&1&&n!==(n=(a[0].orig_name||a[0].url)+"")&&Ht(t,n),_&1&&r!==(r=kt(a[0].size)+"")&&Ht(s,r)},i:dt,o:dt,d(a){a&&(oe(e),oe(i),oe(o))}}}function Hl(l){let e=l[0]?.url,n,t,i=zt(l);return{c(){i.c(),n=en()},m(o,r){i.m(o,r),re(o,n,r),t=!0},p(o,r){r&1&&tn(e,e=o[0]?.url)?(it(),z(i,1,1,dt),lt(),i=zt(o),i.c(),X(i,1),i.m(n.parentNode,n)):i.p(o,r)},i(o){t||(X(i),t=!0)},o(o){z(i),t=!1},d(o){o&&oe(n),i.d(o)}}}function zt(l){let e,n;return e=new Yt({props:{root:l[10],interactive:!0,autoplay:l[9],src:l[0].url,subtitle:l[2]?.url,mirror:l[7]&&l[1]==="webcam",label:l[4],handle_change:l[17],handle_reset_value:l[12]}}),e.$on("play",l[25]),e.$on("pause",l[26]),e.$on("stop",l[27]),e.$on("end",l[28]),{c(){Ce(e.$$.fragment)},m(t,i){Le(e,t,i),n=!0},p(t,i){const o={};i&1024&&(o.root=t[10]),i&512&&(o.autoplay=t[9]),i&1&&(o.src=t[0].url),i&4&&(o.subtitle=t[2]?.url),i&130&&(o.mirror=t[7]&&t[1]==="webcam"),i&16&&(o.label=t[4]),i&4096&&(o.handle_reset_value=t[12]),e.$set(o)},i(t){n||(X(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){Be(e,t)}}}function Xl(l){let e,n;return e=new bn({props:{root:l[10],mirror_webcam:l[7],include_audio:l[8],mode:"video",i18n:l[11]}}),e.$on("error",l[22]),e.$on("capture",l[18]),e.$on("start_recording",l[23]),e.$on("stop_recording",l[24]),{c(){Ce(e.$$.fragment)},m(t,i){Le(e,t,i),n=!0},p(t,i){const o={};i&1024&&(o.root=t[10]),i&128&&(o.mirror_webcam=t[7]),i&256&&(o.include_audio=t[8]),i&2048&&(o.i18n=t[11]),e.$set(o)},i(t){n||(X(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){Be(e,t)}}}function zl(l){let e,n,t;function i(r){l[20](r)}let o={filetype:"video/x-m4v,video/*",root:l[10],$$slots:{default:[Fl]},$$scope:{ctx:l}};return l[13]!==void 0&&(o.dragging=l[13]),e=new mn({props:o}),xt.push(()=>$t(e,"dragging",i)),e.$on("load",l[15]),e.$on("error",l[21]),{c(){Ce(e.$$.fragment)},m(r,s){Le(e,r,s),t=!0},p(r,s){const a={};s&1024&&(a.root=r[10]),s&1073741824&&(a.$$scope={dirty:s,ctx:r}),!n&&s&8192&&(n=!0,a.dragging=r[13],Zt(()=>n=!1)),e.$set(a)},i(r){t||(X(e.$$.fragment,r),t=!0)},o(r){z(e.$$.fragment,r),t=!1},d(r){Be(e,r)}}}function Fl(l){let e;const n=l[19].default,t=Bl(n,l,l[30],null);return{c(){t&&t.c()},m(i,o){t&&t.m(i,o),e=!0},p(i,o){t&&t.p&&(!e||o&1073741824)&&Pl(t,n,i,i[30],e?Ml(n,i[30],o,null):Ll(i[30]),null)},i(i){e||(X(t,i),e=!0)},o(i){z(t,i),e=!1},d(i){t&&t.d(i)}}}function jl(l){let e,n,t,i,o,r,s,a,_;e=new Gt({props:{show_label:l[6],Icon:ht,label:l[4]||"Video"}});const u=[Ul,Nl],f=[];function c(m,k){return m[0]===null||m[0].url===void 0?0:1}i=c(l),o=f[i]=u[i](l);function d(m){l[29](m)}let h={sources:l[3],handle_clear:l[16]};return l[1]!==void 0&&(h.active_source=l[1]),s=new gn({props:h}),xt.push(()=>$t(s,"active_source",d)),{c(){Ce(e.$$.fragment),n=$e(),t=Ze("div"),o.c(),r=$e(),Ce(s.$$.fragment),Ue(t,"data-testid","video"),Ue(t,"class","video-container svelte-1cs6pot")},m(m,k){Le(e,m,k),re(m,n,k),re(m,t,k),f[i].m(t,null),ct(t,r),Le(s,t,null),_=!0},p(m,[k]){const q={};k&64&&(q.show_label=m[6]),k&16&&(q.label=m[4]||"Video"),e.$set(q);let E=i;i=c(m),i===E?f[i].p(m,k):(it(),z(f[E],1,1,()=>{f[E]=null}),lt(),o=f[i],o?o.p(m,k):(o=f[i]=u[i](m),o.c()),X(o,1),o.m(t,r));const D={};k&8&&(D.sources=m[3]),!a&&k&2&&(a=!0,D.active_source=m[1],Zt(()=>a=!1)),s.$set(D)},i(m){_||(X(e.$$.fragment,m),X(o),X(s.$$.fragment,m),_=!0)},o(m){z(e.$$.fragment,m),z(o),z(s.$$.fragment,m),_=!1},d(m){m&&(oe(n),oe(t)),Be(e,m),f[i].d(),Be(s)}}}function Ol(l,e,n){let{$$slots:t={},$$scope:i}=e,{value:o=null}=e,{subtitle:r=null}=e,{sources:s=["webcam","upload"]}=e,{label:a=void 0}=e,{show_download_button:_=!1}=e,{show_label:u=!0}=e,{mirror_webcam:f=!1}=e,{include_audio:c}=e,{autoplay:d}=e,{root:h}=e,{i18n:m}=e,{active_source:k="webcam"}=e,{handle_reset_value:q=()=>{}}=e;const E=Il();function D({detail:b}){n(0,o=b),E("change",b),E("upload",b)}function P(){n(0,o=null),E("change",null),E("clear")}function w(b){E("change",b)}function g({detail:b}){E("change",b)}let T=!1;function I(b){T=b,n(13,T)}const S=({detail:b})=>E("error",b);function C(b){ce.call(this,l,b)}function W(b){ce.call(this,l,b)}function R(b){ce.call(this,l,b)}function L(b){ce.call(this,l,b)}function Z(b){ce.call(this,l,b)}function p(b){ce.call(this,l,b)}function J(b){ce.call(this,l,b)}function G(b){k=b,n(1,k)}return l.$$set=b=>{"value"in b&&n(0,o=b.value),"subtitle"in b&&n(2,r=b.subtitle),"sources"in b&&n(3,s=b.sources),"label"in b&&n(4,a=b.label),"show_download_button"in b&&n(5,_=b.show_download_button),"show_label"in b&&n(6,u=b.show_label),"mirror_webcam"in b&&n(7,f=b.mirror_webcam),"include_audio"in b&&n(8,c=b.include_audio),"autoplay"in b&&n(9,d=b.autoplay),"root"in b&&n(10,h=b.root),"i18n"in b&&n(11,m=b.i18n),"active_source"in b&&n(1,k=b.active_source),"handle_reset_value"in b&&n(12,q=b.handle_reset_value),"$$scope"in b&&n(30,i=b.$$scope)},l.$$.update=()=>{l.$$.dirty&8192&&E("drag",T)},[o,k,r,s,a,_,u,f,c,d,h,m,q,T,E,D,P,w,g,t,I,S,C,W,R,L,Z,p,J,G,i]}class Wl extends Cl{constructor(e){super(),Rl(this,e,Ol,jl,tn,{value:0,subtitle:2,sources:3,label:4,show_download_button:5,show_label:6,mirror_webcam:7,include_audio:8,autoplay:9,root:10,i18n:11,active_source:1,handle_reset_value:12})}}const Jl=Wl;const{SvelteComponent:Gl,append:Kl,attr:Ft,bubble:ye,check_outros:Ke,create_component:be,destroy_component:we,detach:xe,element:Ql,empty:Yl,group_outros:Qe,init:Zl,insert:et,mount_component:ve,noop:nn,safe_not_equal:ln,space:mt,transition_in:U,transition_out:H}=window.__gradio__svelte__internal,{createEventDispatcher:$l,afterUpdate:xl,tick:ei}=window.__gradio__svelte__internal;function ti(l){let e=l[0].url,n,t,i,o,r=jt(l),s=l[6]&&Ot(l),a=l[5]&&Wt(l);return{c(){r.c(),n=mt(),t=Ql("div"),s&&s.c(),i=mt(),a&&a.c(),Ft(t,"class","icon-buttons svelte-rvdo70"),Ft(t,"data-testid","download-div")},m(_,u){r.m(_,u),et(_,n,u),et(_,t,u),s&&s.m(t,null),Kl(t,i),a&&a.m(t,null),o=!0},p(_,u){u&1&&ln(e,e=_[0].url)?(Qe(),H(r,1,1,nn),Ke(),r=jt(_),r.c(),U(r,1),r.m(n.parentNode,n)):r.p(_,u),_[6]?s?(s.p(_,u),u&64&&U(s,1)):(s=Ot(_),s.c(),U(s,1),s.m(t,i)):s&&(Qe(),H(s,1,1,()=>{s=null}),Ke()),_[5]?a?(a.p(_,u),u&32&&U(a,1)):(a=Wt(_),a.c(),U(a,1),a.m(t,null)):a&&(Qe(),H(a,1,1,()=>{a=null}),Ke())},i(_){o||(U(r),U(s),U(a),o=!0)},o(_){H(r),H(s),H(a),o=!1},d(_){_&&(xe(n),xe(t)),r.d(_),s&&s.d(),a&&a.d()}}}function ni(l){let e,n;return e=new Cn({props:{unpadded_box:!0,size:"large",$$slots:{default:[ii]},$$scope:{ctx:l}}}),{c(){be(e.$$.fragment)},m(t,i){ve(e,t,i),n=!0},p(t,i){const o={};i&262144&&(o.$$scope={dirty:i,ctx:t}),e.$set(o)},i(t){n||(U(e.$$.fragment,t),n=!0)},o(t){H(e.$$.fragment,t),n=!1},d(t){we(e,t)}}}function jt(l){let e,n;return e=new Yt({props:{src:l[0].url,subtitle:l[1]?.url,autoplay:l[4],mirror:!1,label:l[2],interactive:!1}}),e.$on("play",l[8]),e.$on("pause",l[9]),e.$on("stop",l[10]),e.$on("end",l[11]),{c(){be(e.$$.fragment)},m(t,i){ve(e,t,i),n=!0},p(t,i){const o={};i&1&&(o.src=t[0].url),i&2&&(o.subtitle=t[1]?.url),i&16&&(o.autoplay=t[4]),i&4&&(o.label=t[2]),e.$set(o)},i(t){n||(U(e.$$.fragment,t),n=!0)},o(t){H(e.$$.fragment,t),n=!1},d(t){we(e,t)}}}function Ot(l){let e,n;return e=new Ln({props:{href:l[0].url,download:l[0].orig_name||l[0].path,$$slots:{default:[li]},$$scope:{ctx:l}}}),{c(){be(e.$$.fragment)},m(t,i){ve(e,t,i),n=!0},p(t,i){const o={};i&1&&(o.href=t[0].url),i&1&&(o.download=t[0].orig_name||t[0].path),i&262144&&(o.$$scope={dirty:i,ctx:t}),e.$set(o)},i(t){n||(U(e.$$.fragment,t),n=!0)},o(t){H(e.$$.fragment,t),n=!1},d(t){we(e,t)}}}function li(l){let e,n;return e=new Dn({props:{Icon:Mn,label:"Download"}}),{c(){be(e.$$.fragment)},m(t,i){ve(e,t,i),n=!0},p:nn,i(t){n||(U(e.$$.fragment,t),n=!0)},o(t){H(e.$$.fragment,t),n=!1},d(t){we(e,t)}}}function Wt(l){let e,n;return e=new Bn({props:{i18n:l[7],value:l[0],formatter:l[12]}}),e.$on("error",l[13]),e.$on("share",l[14]),{c(){be(e.$$.fragment)},m(t,i){ve(e,t,i),n=!0},p(t,i){const o={};i&128&&(o.i18n=t[7]),i&1&&(o.value=t[0]),e.$set(o)},i(t){n||(U(e.$$.fragment,t),n=!0)},o(t){H(e.$$.fragment,t),n=!1},d(t){we(e,t)}}}function ii(l){let e,n;return e=new ht({}),{c(){be(e.$$.fragment)},m(t,i){ve(e,t,i),n=!0},i(t){n||(U(e.$$.fragment,t),n=!0)},o(t){H(e.$$.fragment,t),n=!1},d(t){we(e,t)}}}function oi(l){let e,n,t,i,o,r;e=new Gt({props:{show_label:l[3],Icon:ht,label:l[2]||"Video"}});const s=[ni,ti],a=[];function _(u,f){return u[0]===null||u[0].url===void 0?0:1}return t=_(l),i=a[t]=s[t](l),{c(){be(e.$$.fragment),n=mt(),i.c(),o=Yl()},m(u,f){ve(e,u,f),et(u,n,f),a[t].m(u,f),et(u,o,f),r=!0},p(u,[f]){const c={};f&8&&(c.show_label=u[3]),f&4&&(c.label=u[2]||"Video"),e.$set(c);let d=t;t=_(u),t===d?a[t].p(u,f):(Qe(),H(a[d],1,1,()=>{a[d]=null}),Ke(),i=a[t],i?i.p(u,f):(i=a[t]=s[t](u),i.c()),U(i,1),i.m(o.parentNode,o))},i(u){r||(U(e.$$.fragment,u),U(i),r=!0)},o(u){H(e.$$.fragment,u),H(i),r=!1},d(u){u&&(xe(n),xe(o)),we(e,u),a[t].d(u)}}}function ri(l,e,n){let{value:t=null}=e,{subtitle:i=null}=e,{label:o=void 0}=e,{show_label:r=!0}=e,{autoplay:s}=e,{show_share_button:a=!0}=e,{show_download_button:_=!0}=e,{i18n:u}=e,f=null,c=null;const d=$l();xl(async()=>{t!==f&&i!==c&&c!==null&&(f=t,n(0,t=null),await ei(),n(0,t=f)),f=t,c=i});function h(w){ye.call(this,l,w)}function m(w){ye.call(this,l,w)}function k(w){ye.call(this,l,w)}function q(w){ye.call(this,l,w)}const E=async w=>w?await Sn(w.data,"url"):"";function D(w){ye.call(this,l,w)}function P(w){ye.call(this,l,w)}return l.$$set=w=>{"value"in w&&n(0,t=w.value),"subtitle"in w&&n(1,i=w.subtitle),"label"in w&&n(2,o=w.label),"show_label"in w&&n(3,r=w.show_label),"autoplay"in w&&n(4,s=w.autoplay),"show_share_button"in w&&n(5,a=w.show_share_button),"show_download_button"in w&&n(6,_=w.show_download_button),"i18n"in w&&n(7,u=w.i18n)},l.$$.update=()=>{l.$$.dirty&1&&t&&d("change",t)},[t,i,o,r,s,a,_,u,h,m,k,q,E,D,P]}class ai extends Gl{constructor(e){super(),Zl(this,e,ri,oi,ln,{value:0,subtitle:1,label:2,show_label:3,autoplay:4,show_share_button:5,show_download_button:6,i18n:7})}}const si=ai,{SvelteComponent:ui,assign:on,check_outros:_i,create_component:me,destroy_component:he,detach:vt,empty:fi,flush:B,get_spread_object:rn,get_spread_update:an,group_outros:ci,init:di,insert:pt,mount_component:ge,safe_not_equal:mi,space:sn,transition_in:ae,transition_out:se}=window.__gradio__svelte__internal;function hi(l){let e,n;return e=new Jt({props:{visible:l[4],variant:l[0]===null&&l[21]==="upload"?"dashed":"solid",border_mode:l[24]?"focus":"base",padding:!1,elem_id:l[2],elem_classes:l[3],height:l[9],width:l[10],container:l[11],scale:l[12],min_width:l[13],allow_overflow:!1,$$slots:{default:[wi]},$$scope:{ctx:l}}}),{c(){me(e.$$.fragment)},m(t,i){ge(e,t,i),n=!0},p(t,i){const o={};i[0]&16&&(o.visible=t[4]),i[0]&2097153&&(o.variant=t[0]===null&&t[21]==="upload"?"dashed":"solid"),i[0]&16777216&&(o.border_mode=t[24]?"focus":"base"),i[0]&4&&(o.elem_id=t[2]),i[0]&8&&(o.elem_classes=t[3]),i[0]&512&&(o.height=t[9]),i[0]&1024&&(o.width=t[10]),i[0]&2048&&(o.container=t[11]),i[0]&4096&&(o.scale=t[12]),i[0]&8192&&(o.min_width=t[13]),i[0]&33243618|i[1]&16384&&(o.$$scope={dirty:i,ctx:t}),e.$set(o)},i(t){n||(ae(e.$$.fragment,t),n=!0)},o(t){se(e.$$.fragment,t),n=!1},d(t){he(e,t)}}}function gi(l){let e,n;return e=new Jt({props:{visible:l[4],variant:l[0]===null&&l[21]==="upload"?"dashed":"solid",border_mode:l[24]?"focus":"base",padding:!1,elem_id:l[2],elem_classes:l[3],height:l[9],width:l[10],container:l[11],scale:l[12],min_width:l[13],allow_overflow:!1,$$slots:{default:[vi]},$$scope:{ctx:l}}}),{c(){me(e.$$.fragment)},m(t,i){ge(e,t,i),n=!0},p(t,i){const o={};i[0]&16&&(o.visible=t[4]),i[0]&2097153&&(o.variant=t[0]===null&&t[21]==="upload"?"dashed":"solid"),i[0]&16777216&&(o.border_mode=t[24]?"focus":"base"),i[0]&4&&(o.elem_id=t[2]),i[0]&8&&(o.elem_classes=t[3]),i[0]&512&&(o.height=t[9]),i[0]&1024&&(o.width=t[10]),i[0]&2048&&(o.container=t[11]),i[0]&4096&&(o.scale=t[12]),i[0]&8192&&(o.min_width=t[13]),i[0]&12828962|i[1]&16384&&(o.$$scope={dirty:i,ctx:t}),e.$set(o)},i(t){n||(ae(e.$$.fragment,t),n=!0)},o(t){se(e.$$.fragment,t),n=!1},d(t){he(e,t)}}}function bi(l){let e,n;return e=new Rn({props:{i18n:l[17].i18n,type:"video"}}),{c(){me(e.$$.fragment)},m(t,i){ge(e,t,i),n=!0},p(t,i){const o={};i[0]&131072&&(o.i18n=t[17].i18n),e.$set(o)},i(t){n||(ae(e.$$.fragment,t),n=!0)},o(t){se(e.$$.fragment,t),n=!1},d(t){he(e,t)}}}function wi(l){let e,n,t,i;const o=[{autoscroll:l[17].autoscroll},{i18n:l[17].i18n},l[1]];let r={};for(let s=0;s<o.length;s+=1)r=on(r,o[s]);return e=new Kt({props:r}),t=new Jl({props:{value:l[22],subtitle:l[23],label:l[5],show_label:l[8],show_download_button:l[16],sources:l[6],active_source:l[21],mirror_webcam:l[19],include_audio:l[20],autoplay:l[14],root:l[7],handle_reset_value:l[25],i18n:l[17].i18n,$$slots:{default:[bi]},$$scope:{ctx:l}}}),t.$on("change",l[26]),t.$on("drag",l[36]),t.$on("error",l[27]),t.$on("clear",l[37]),t.$on("play",l[38]),t.$on("pause",l[39]),t.$on("upload",l[40]),t.$on("stop",l[41]),t.$on("end",l[42]),t.$on("start_recording",l[43]),t.$on("stop_recording",l[44]),{c(){me(e.$$.fragment),n=sn(),me(t.$$.fragment)},m(s,a){ge(e,s,a),pt(s,n,a),ge(t,s,a),i=!0},p(s,a){const _=a[0]&131074?an(o,[a[0]&131072&&{autoscroll:s[17].autoscroll},a[0]&131072&&{i18n:s[17].i18n},a[0]&2&&rn(s[1])]):{};e.$set(_);const u={};a[0]&4194304&&(u.value=s[22]),a[0]&8388608&&(u.subtitle=s[23]),a[0]&32&&(u.label=s[5]),a[0]&256&&(u.show_label=s[8]),a[0]&65536&&(u.show_download_button=s[16]),a[0]&64&&(u.sources=s[6]),a[0]&2097152&&(u.active_source=s[21]),a[0]&524288&&(u.mirror_webcam=s[19]),a[0]&1048576&&(u.include_audio=s[20]),a[0]&16384&&(u.autoplay=s[14]),a[0]&128&&(u.root=s[7]),a[0]&131072&&(u.i18n=s[17].i18n),a[0]&131072|a[1]&16384&&(u.$$scope={dirty:a,ctx:s}),t.$set(u)},i(s){i||(ae(e.$$.fragment,s),ae(t.$$.fragment,s),i=!0)},o(s){se(e.$$.fragment,s),se(t.$$.fragment,s),i=!1},d(s){s&&vt(n),he(e,s),he(t,s)}}}function vi(l){let e,n,t,i;const o=[{autoscroll:l[17].autoscroll},{i18n:l[17].i18n},l[1]];let r={};for(let s=0;s<o.length;s+=1)r=on(r,o[s]);return e=new Kt({props:r}),t=new si({props:{value:l[22],subtitle:l[23],label:l[5],show_label:l[8],autoplay:l[14],show_share_button:l[15],show_download_button:l[16],i18n:l[17].i18n}}),t.$on("play",l[30]),t.$on("pause",l[31]),t.$on("stop",l[32]),t.$on("end",l[33]),t.$on("share",l[34]),t.$on("error",l[35]),{c(){me(e.$$.fragment),n=sn(),me(t.$$.fragment)},m(s,a){ge(e,s,a),pt(s,n,a),ge(t,s,a),i=!0},p(s,a){const _=a[0]&131074?an(o,[a[0]&131072&&{autoscroll:s[17].autoscroll},a[0]&131072&&{i18n:s[17].i18n},a[0]&2&&rn(s[1])]):{};e.$set(_);const u={};a[0]&4194304&&(u.value=s[22]),a[0]&8388608&&(u.subtitle=s[23]),a[0]&32&&(u.label=s[5]),a[0]&256&&(u.show_label=s[8]),a[0]&16384&&(u.autoplay=s[14]),a[0]&32768&&(u.show_share_button=s[15]),a[0]&65536&&(u.show_download_button=s[16]),a[0]&131072&&(u.i18n=s[17].i18n),t.$set(u)},i(s){i||(ae(e.$$.fragment,s),ae(t.$$.fragment,s),i=!0)},o(s){se(e.$$.fragment,s),se(t.$$.fragment,s),i=!1},d(s){s&&vt(n),he(e,s),he(t,s)}}}function pi(l){let e,n,t,i;const o=[gi,hi],r=[];function s(a,_){return a[18]?1:0}return e=s(l),n=r[e]=o[e](l),{c(){n.c(),t=fi()},m(a,_){r[e].m(a,_),pt(a,t,_),i=!0},p(a,_){let u=e;e=s(a),e===u?r[e].p(a,_):(ci(),se(r[u],1,1,()=>{r[u]=null}),_i(),n=r[e],n?n.p(a,_):(n=r[e]=o[e](a),n.c()),ae(n,1),n.m(t.parentNode,t))},i(a){i||(ae(n),i=!0)},o(a){se(n),i=!1},d(a){a&&vt(t),r[e].d(a)}}}function ki(l,e,n){let{elem_id:t=""}=e,{elem_classes:i=[]}=e,{visible:o=!0}=e,{value:r=null}=e,s=null,{label:a}=e,{sources:_}=e,{root:u}=e,{show_label:f}=e,{loading_status:c}=e,{height:d}=e,{width:h}=e,{container:m=!1}=e,{scale:k=null}=e,{min_width:q=void 0}=e,{autoplay:E=!1}=e,{show_share_button:D=!0}=e,{show_download_button:P}=e,{gradio:w}=e,{interactive:g}=e,{mirror_webcam:T}=e,{include_audio:I}=e,S=null,C=null,W,R=r;const L=()=>{R===null||r===R||n(0,r=R)};let Z=!1;function p({detail:v}){v!=null?n(0,r={video:v,subtitles:null}):n(0,r=null)}function J({detail:v}){const[cn,dn]=v.includes("Invalid file type")?["warning","complete"]:["error","error"];n(1,c=c||{}),n(1,c.status=dn,c),n(1,c.message=v,c),w.dispatch(cn,v)}const G=()=>w.dispatch("play"),b=()=>w.dispatch("pause"),ot=()=>w.dispatch("stop"),_e=()=>w.dispatch("end"),Ae=({detail:v})=>w.dispatch("share",v),le=({detail:v})=>w.dispatch("error",v),He=({detail:v})=>n(24,Z=v),M=()=>w.dispatch("clear"),y=()=>w.dispatch("play"),V=()=>w.dispatch("pause"),K=()=>w.dispatch("upload"),Me=()=>w.dispatch("stop"),un=()=>w.dispatch("end"),_n=()=>w.dispatch("start_recording"),fn=()=>w.dispatch("stop_recording");return l.$$set=v=>{"elem_id"in v&&n(2,t=v.elem_id),"elem_classes"in v&&n(3,i=v.elem_classes),"visible"in v&&n(4,o=v.visible),"value"in v&&n(0,r=v.value),"label"in v&&n(5,a=v.label),"sources"in v&&n(6,_=v.sources),"root"in v&&n(7,u=v.root),"show_label"in v&&n(8,f=v.show_label),"loading_status"in v&&n(1,c=v.loading_status),"height"in v&&n(9,d=v.height),"width"in v&&n(10,h=v.width),"container"in v&&n(11,m=v.container),"scale"in v&&n(12,k=v.scale),"min_width"in v&&n(13,q=v.min_width),"autoplay"in v&&n(14,E=v.autoplay),"show_share_button"in v&&n(15,D=v.show_share_button),"show_download_button"in v&&n(16,P=v.show_download_button),"gradio"in v&&n(17,w=v.gradio),"interactive"in v&&n(18,g=v.interactive),"mirror_webcam"in v&&n(19,T=v.mirror_webcam),"include_audio"in v&&n(20,I=v.include_audio)},l.$$.update=()=>{l.$$.dirty[0]&536870913&&r&&R===null&&n(29,R=r),l.$$.dirty[0]&2097216&&_&&!W&&n(21,W=_[0]),l.$$.dirty[0]&1&&(r!=null?(n(22,S=r.video),n(23,C=r.subtitles)):(n(22,S=null),n(23,C=null))),l.$$.dirty[0]&268566529&&JSON.stringify(r)!==JSON.stringify(s)&&(n(28,s=r),w.dispatch("change"))},[r,c,t,i,o,a,_,u,f,d,h,m,k,q,E,D,P,w,g,T,I,W,S,C,Z,L,p,J,s,R,G,b,ot,_e,Ae,le,He,M,y,V,K,Me,un,_n,fn]}class yi extends ui{constructor(e){super(),di(this,e,ki,pi,mi,{elem_id:2,elem_classes:3,visible:4,value:0,label:5,sources:6,root:7,show_label:8,loading_status:1,height:9,width:10,container:11,scale:12,min_width:13,autoplay:14,show_share_button:15,show_download_button:16,gradio:17,interactive:18,mirror_webcam:19,include_audio:20},null,[-1,-1])}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),B()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),B()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),B()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),B()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),B()}get sources(){return this.$$.ctx[6]}set sources(e){this.$$set({sources:e}),B()}get root(){return this.$$.ctx[7]}set root(e){this.$$set({root:e}),B()}get show_label(){return this.$$.ctx[8]}set show_label(e){this.$$set({show_label:e}),B()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),B()}get height(){return this.$$.ctx[9]}set height(e){this.$$set({height:e}),B()}get width(){return this.$$.ctx[10]}set width(e){this.$$set({width:e}),B()}get container(){return this.$$.ctx[11]}set container(e){this.$$set({container:e}),B()}get scale(){return this.$$.ctx[12]}set scale(e){this.$$set({scale:e}),B()}get min_width(){return this.$$.ctx[13]}set min_width(e){this.$$set({min_width:e}),B()}get autoplay(){return this.$$.ctx[14]}set autoplay(e){this.$$set({autoplay:e}),B()}get show_share_button(){return this.$$.ctx[15]}set show_share_button(e){this.$$set({show_share_button:e}),B()}get show_download_button(){return this.$$.ctx[16]}set show_download_button(e){this.$$set({show_download_button:e}),B()}get gradio(){return this.$$.ctx[17]}set gradio(e){this.$$set({gradio:e}),B()}get interactive(){return this.$$.ctx[18]}set interactive(e){this.$$set({interactive:e}),B()}get mirror_webcam(){return this.$$.ctx[19]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),B()}get include_audio(){return this.$$.ctx[20]}set include_audio(e){this.$$set({include_audio:e}),B()}}const Qi=yi;export{eo as BaseExample,Jl as BaseInteractiveVideo,Yt as BasePlayer,si as BaseStaticVideo,Qi as default,$i as loaded,kn as playable,kt as prettyBytes};
//# sourceMappingURL=index-82184621.js.map
