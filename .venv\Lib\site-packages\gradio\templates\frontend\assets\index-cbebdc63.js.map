{"version": 3, "file": "index-cbebdc63.js", "sources": ["../../../../node_modules/.pnpm/@lezer+lr@1.3.14/node_modules/@lezer/lr/dist/index.js"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>, NodeProp, NodeSet, NodeType, <PERSON><PERSON>ult<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tree, IterMode } from '@lezer/common';\n\n/**\nA parse stack. These are used internally by the parser to track\nparsing progress. They also provide some properties and methods\nthat external code such as a tokenizer can use to get information\nabout the parse state.\n*/\nclass Stack {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The parse that this stack is part of @internal\n    */\n    p, \n    /**\n    Holds state, input pos, buffer index triplets for all but the\n    top state @internal\n    */\n    stack, \n    /**\n    The current parse state @internal\n    */\n    state, \n    // The position at which the next reduce should take place. This\n    // can be less than `this.pos` when skipped expressions have been\n    // added to the stack (which should be moved outside of the next\n    // reduction)\n    /**\n    @internal\n    */\n    reducePos, \n    /**\n    The input position up to which this stack has parsed.\n    */\n    pos, \n    /**\n    The dynamic score of the stack, including dynamic precedence\n    and error-recovery penalties\n    @internal\n    */\n    score, \n    // The output buffer. Holds (type, start, end, size) quads\n    // representing nodes created by the parser, where `size` is\n    // amount of buffer array entries covered by this node.\n    /**\n    @internal\n    */\n    buffer, \n    // The base offset of the buffer. When stacks are split, the split\n    // instance shared the buffer history with its parent up to\n    // `bufferBase`, which is the absolute offset (including the\n    // offset of previous splits) into the buffer at which this stack\n    // starts writing.\n    /**\n    @internal\n    */\n    bufferBase, \n    /**\n    @internal\n    */\n    curContext, \n    /**\n    @internal\n    */\n    lookAhead = 0, \n    // A parent stack from which this was split off, if any. This is\n    // set up so that it always points to a stack that has some\n    // additional buffer content, never to a stack with an equal\n    // `bufferBase`.\n    /**\n    @internal\n    */\n    parent) {\n        this.p = p;\n        this.stack = stack;\n        this.state = state;\n        this.reducePos = reducePos;\n        this.pos = pos;\n        this.score = score;\n        this.buffer = buffer;\n        this.bufferBase = bufferBase;\n        this.curContext = curContext;\n        this.lookAhead = lookAhead;\n        this.parent = parent;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return `[${this.stack.filter((_, i) => i % 3 == 0).concat(this.state)}]@${this.pos}${this.score ? \"!\" + this.score : \"\"}`;\n    }\n    // Start an empty stack\n    /**\n    @internal\n    */\n    static start(p, state, pos = 0) {\n        let cx = p.parser.context;\n        return new Stack(p, [], state, pos, pos, 0, [], 0, cx ? new StackContext(cx, cx.start) : null, 0, null);\n    }\n    /**\n    The stack's current [context](#lr.ContextTracker) value, if\n    any. Its type will depend on the context tracker's type\n    parameter, or it will be `null` if there is no context\n    tracker.\n    */\n    get context() { return this.curContext ? this.curContext.context : null; }\n    // Push a state onto the stack, tracking its start position as well\n    // as the buffer base at that point.\n    /**\n    @internal\n    */\n    pushState(state, start) {\n        this.stack.push(this.state, start, this.bufferBase + this.buffer.length);\n        this.state = state;\n    }\n    // Apply a reduce action\n    /**\n    @internal\n    */\n    reduce(action) {\n        var _a;\n        let depth = action >> 19 /* Action.ReduceDepthShift */, type = action & 65535 /* Action.ValueMask */;\n        let { parser } = this.p;\n        let dPrec = parser.dynamicPrecedence(type);\n        if (dPrec)\n            this.score += dPrec;\n        if (depth == 0) {\n            this.pushState(parser.getGoto(this.state, type, true), this.reducePos);\n            // Zero-depth reductions are a special case—they add stuff to\n            // the stack without popping anything off.\n            if (type < parser.minRepeatTerm)\n                this.storeNode(type, this.reducePos, this.reducePos, 4, true);\n            this.reduceContext(type, this.reducePos);\n            return;\n        }\n        // Find the base index into `this.stack`, content after which will\n        // be dropped. Note that with `StayFlag` reductions we need to\n        // consume two extra frames (the dummy parent node for the skipped\n        // expression and the state that we'll be staying in, which should\n        // be moved to `this.state`).\n        let base = this.stack.length - ((depth - 1) * 3) - (action & 262144 /* Action.StayFlag */ ? 6 : 0);\n        let start = base ? this.stack[base - 2] : this.p.ranges[0].from, size = this.reducePos - start;\n        // This is a kludge to try and detect overly deep left-associative\n        // trees, which will not increase the parse stack depth and thus\n        // won't be caught by the regular stack-depth limit check.\n        if (size >= 2000 /* Recover.MinBigReduction */ && !((_a = this.p.parser.nodeSet.types[type]) === null || _a === void 0 ? void 0 : _a.isAnonymous)) {\n            if (start == this.p.lastBigReductionStart) {\n                this.p.bigReductionCount++;\n                this.p.lastBigReductionSize = size;\n            }\n            else if (this.p.lastBigReductionSize < size) {\n                this.p.bigReductionCount = 1;\n                this.p.lastBigReductionStart = start;\n                this.p.lastBigReductionSize = size;\n            }\n        }\n        let bufferBase = base ? this.stack[base - 1] : 0, count = this.bufferBase + this.buffer.length - bufferBase;\n        // Store normal terms or `R -> R R` repeat reductions\n        if (type < parser.minRepeatTerm || (action & 131072 /* Action.RepeatFlag */)) {\n            let pos = parser.stateFlag(this.state, 1 /* StateFlag.Skipped */) ? this.pos : this.reducePos;\n            this.storeNode(type, start, pos, count + 4, true);\n        }\n        if (action & 262144 /* Action.StayFlag */) {\n            this.state = this.stack[base];\n        }\n        else {\n            let baseStateID = this.stack[base - 3];\n            this.state = parser.getGoto(baseStateID, type, true);\n        }\n        while (this.stack.length > base)\n            this.stack.pop();\n        this.reduceContext(type, start);\n    }\n    // Shift a value into the buffer\n    /**\n    @internal\n    */\n    storeNode(term, start, end, size = 4, isReduce = false) {\n        if (term == 0 /* Term.Err */ &&\n            (!this.stack.length || this.stack[this.stack.length - 1] < this.buffer.length + this.bufferBase)) {\n            // Try to omit/merge adjacent error nodes\n            let cur = this, top = this.buffer.length;\n            if (top == 0 && cur.parent) {\n                top = cur.bufferBase - cur.parent.bufferBase;\n                cur = cur.parent;\n            }\n            if (top > 0 && cur.buffer[top - 4] == 0 /* Term.Err */ && cur.buffer[top - 1] > -1) {\n                if (start == end)\n                    return;\n                if (cur.buffer[top - 2] >= start) {\n                    cur.buffer[top - 2] = end;\n                    return;\n                }\n            }\n        }\n        if (!isReduce || this.pos == end) { // Simple case, just append\n            this.buffer.push(term, start, end, size);\n        }\n        else { // There may be skipped nodes that have to be moved forward\n            let index = this.buffer.length;\n            if (index > 0 && this.buffer[index - 4] != 0 /* Term.Err */)\n                while (index > 0 && this.buffer[index - 2] > end) {\n                    // Move this record forward\n                    this.buffer[index] = this.buffer[index - 4];\n                    this.buffer[index + 1] = this.buffer[index - 3];\n                    this.buffer[index + 2] = this.buffer[index - 2];\n                    this.buffer[index + 3] = this.buffer[index - 1];\n                    index -= 4;\n                    if (size > 4)\n                        size -= 4;\n                }\n            this.buffer[index] = term;\n            this.buffer[index + 1] = start;\n            this.buffer[index + 2] = end;\n            this.buffer[index + 3] = size;\n        }\n    }\n    // Apply a shift action\n    /**\n    @internal\n    */\n    shift(action, type, start, end) {\n        if (action & 131072 /* Action.GotoFlag */) {\n            this.pushState(action & 65535 /* Action.ValueMask */, this.pos);\n        }\n        else if ((action & 262144 /* Action.StayFlag */) == 0) { // Regular shift\n            let nextState = action, { parser } = this.p;\n            if (end > this.pos || type <= parser.maxNode) {\n                this.pos = end;\n                if (!parser.stateFlag(nextState, 1 /* StateFlag.Skipped */))\n                    this.reducePos = end;\n            }\n            this.pushState(nextState, start);\n            this.shiftContext(type, start);\n            if (type <= parser.maxNode)\n                this.buffer.push(type, start, end, 4);\n        }\n        else { // Shift-and-stay, which means this is a skipped token\n            this.pos = end;\n            this.shiftContext(type, start);\n            if (type <= this.p.parser.maxNode)\n                this.buffer.push(type, start, end, 4);\n        }\n    }\n    // Apply an action\n    /**\n    @internal\n    */\n    apply(action, next, nextStart, nextEnd) {\n        if (action & 65536 /* Action.ReduceFlag */)\n            this.reduce(action);\n        else\n            this.shift(action, next, nextStart, nextEnd);\n    }\n    // Add a prebuilt (reused) node into the buffer.\n    /**\n    @internal\n    */\n    useNode(value, next) {\n        let index = this.p.reused.length - 1;\n        if (index < 0 || this.p.reused[index] != value) {\n            this.p.reused.push(value);\n            index++;\n        }\n        let start = this.pos;\n        this.reducePos = this.pos = start + value.length;\n        this.pushState(next, start);\n        this.buffer.push(index, start, this.reducePos, -1 /* size == -1 means this is a reused value */);\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.reuse(this.curContext.context, value, this, this.p.stream.reset(this.pos - value.length)));\n    }\n    // Split the stack. Due to the buffer sharing and the fact\n    // that `this.stack` tends to stay quite shallow, this isn't very\n    // expensive.\n    /**\n    @internal\n    */\n    split() {\n        let parent = this;\n        let off = parent.buffer.length;\n        // Because the top of the buffer (after this.pos) may be mutated\n        // to reorder reductions and skipped tokens, and shared buffers\n        // should be immutable, this copies any outstanding skipped tokens\n        // to the new buffer, and puts the base pointer before them.\n        while (off > 0 && parent.buffer[off - 2] > parent.reducePos)\n            off -= 4;\n        let buffer = parent.buffer.slice(off), base = parent.bufferBase + off;\n        // Make sure parent points to an actual parent with content, if there is such a parent.\n        while (parent && base == parent.bufferBase)\n            parent = parent.parent;\n        return new Stack(this.p, this.stack.slice(), this.state, this.reducePos, this.pos, this.score, buffer, base, this.curContext, this.lookAhead, parent);\n    }\n    // Try to recover from an error by 'deleting' (ignoring) one token.\n    /**\n    @internal\n    */\n    recoverByDelete(next, nextEnd) {\n        let isNode = next <= this.p.parser.maxNode;\n        if (isNode)\n            this.storeNode(next, this.pos, nextEnd, 4);\n        this.storeNode(0 /* Term.Err */, this.pos, nextEnd, isNode ? 8 : 4);\n        this.pos = this.reducePos = nextEnd;\n        this.score -= 190 /* Recover.Delete */;\n    }\n    /**\n    Check if the given term would be able to be shifted (optionally\n    after some reductions) on this stack. This can be useful for\n    external tokenizers that want to make sure they only provide a\n    given token when it applies.\n    */\n    canShift(term) {\n        for (let sim = new SimulatedStack(this);;) {\n            let action = this.p.parser.stateSlot(sim.state, 4 /* ParseState.DefaultReduce */) || this.p.parser.hasAction(sim.state, term);\n            if (action == 0)\n                return false;\n            if ((action & 65536 /* Action.ReduceFlag */) == 0)\n                return true;\n            sim.reduce(action);\n        }\n    }\n    // Apply up to Recover.MaxNext recovery actions that conceptually\n    // inserts some missing token or rule.\n    /**\n    @internal\n    */\n    recoverByInsert(next) {\n        if (this.stack.length >= 300 /* Recover.MaxInsertStackDepth */)\n            return [];\n        let nextStates = this.p.parser.nextStates(this.state);\n        if (nextStates.length > 4 /* Recover.MaxNext */ << 1 || this.stack.length >= 120 /* Recover.DampenInsertStackDepth */) {\n            let best = [];\n            for (let i = 0, s; i < nextStates.length; i += 2) {\n                if ((s = nextStates[i + 1]) != this.state && this.p.parser.hasAction(s, next))\n                    best.push(nextStates[i], s);\n            }\n            if (this.stack.length < 120 /* Recover.DampenInsertStackDepth */)\n                for (let i = 0; best.length < 4 /* Recover.MaxNext */ << 1 && i < nextStates.length; i += 2) {\n                    let s = nextStates[i + 1];\n                    if (!best.some((v, i) => (i & 1) && v == s))\n                        best.push(nextStates[i], s);\n                }\n            nextStates = best;\n        }\n        let result = [];\n        for (let i = 0; i < nextStates.length && result.length < 4 /* Recover.MaxNext */; i += 2) {\n            let s = nextStates[i + 1];\n            if (s == this.state)\n                continue;\n            let stack = this.split();\n            stack.pushState(s, this.pos);\n            stack.storeNode(0 /* Term.Err */, stack.pos, stack.pos, 4, true);\n            stack.shiftContext(nextStates[i], this.pos);\n            stack.reducePos = this.pos;\n            stack.score -= 200 /* Recover.Insert */;\n            result.push(stack);\n        }\n        return result;\n    }\n    // Force a reduce, if possible. Return false if that can't\n    // be done.\n    /**\n    @internal\n    */\n    forceReduce() {\n        let { parser } = this.p;\n        let reduce = parser.stateSlot(this.state, 5 /* ParseState.ForcedReduce */);\n        if ((reduce & 65536 /* Action.ReduceFlag */) == 0)\n            return false;\n        if (!parser.validAction(this.state, reduce)) {\n            let depth = reduce >> 19 /* Action.ReduceDepthShift */, term = reduce & 65535 /* Action.ValueMask */;\n            let target = this.stack.length - depth * 3;\n            if (target < 0 || parser.getGoto(this.stack[target], term, false) < 0) {\n                let backup = this.findForcedReduction();\n                if (backup == null)\n                    return false;\n                reduce = backup;\n            }\n            this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n            this.score -= 100 /* Recover.Reduce */;\n        }\n        this.reducePos = this.pos;\n        this.reduce(reduce);\n        return true;\n    }\n    /**\n    Try to scan through the automaton to find some kind of reduction\n    that can be applied. Used when the regular ForcedReduce field\n    isn't a valid action. @internal\n    */\n    findForcedReduction() {\n        let { parser } = this.p, seen = [];\n        let explore = (state, depth) => {\n            if (seen.includes(state))\n                return;\n            seen.push(state);\n            return parser.allActions(state, (action) => {\n                if (action & (262144 /* Action.StayFlag */ | 131072 /* Action.GotoFlag */)) ;\n                else if (action & 65536 /* Action.ReduceFlag */) {\n                    let rDepth = (action >> 19 /* Action.ReduceDepthShift */) - depth;\n                    if (rDepth > 1) {\n                        let term = action & 65535 /* Action.ValueMask */, target = this.stack.length - rDepth * 3;\n                        if (target >= 0 && parser.getGoto(this.stack[target], term, false) >= 0)\n                            return (rDepth << 19 /* Action.ReduceDepthShift */) | 65536 /* Action.ReduceFlag */ | term;\n                    }\n                }\n                else {\n                    let found = explore(action, depth + 1);\n                    if (found != null)\n                        return found;\n                }\n            });\n        };\n        return explore(this.state, 0);\n    }\n    /**\n    @internal\n    */\n    forceAll() {\n        while (!this.p.parser.stateFlag(this.state, 2 /* StateFlag.Accepting */)) {\n            if (!this.forceReduce()) {\n                this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n                break;\n            }\n        }\n        return this;\n    }\n    /**\n    Check whether this state has no further actions (assumed to be a direct descendant of the\n    top state, since any other states must be able to continue\n    somehow). @internal\n    */\n    get deadEnd() {\n        if (this.stack.length != 3)\n            return false;\n        let { parser } = this.p;\n        return parser.data[parser.stateSlot(this.state, 1 /* ParseState.Actions */)] == 65535 /* Seq.End */ &&\n            !parser.stateSlot(this.state, 4 /* ParseState.DefaultReduce */);\n    }\n    /**\n    Restart the stack (put it back in its start state). Only safe\n    when this.stack.length == 3 (state is directly below the top\n    state). @internal\n    */\n    restart() {\n        this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n        this.state = this.stack[0];\n        this.stack.length = 0;\n    }\n    /**\n    @internal\n    */\n    sameState(other) {\n        if (this.state != other.state || this.stack.length != other.stack.length)\n            return false;\n        for (let i = 0; i < this.stack.length; i += 3)\n            if (this.stack[i] != other.stack[i])\n                return false;\n        return true;\n    }\n    /**\n    Get the parser used by this stack.\n    */\n    get parser() { return this.p.parser; }\n    /**\n    Test whether a given dialect (by numeric ID, as exported from\n    the terms file) is enabled.\n    */\n    dialectEnabled(dialectID) { return this.p.parser.dialect.flags[dialectID]; }\n    shiftContext(term, start) {\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.shift(this.curContext.context, term, this, this.p.stream.reset(start)));\n    }\n    reduceContext(term, start) {\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.reduce(this.curContext.context, term, this, this.p.stream.reset(start)));\n    }\n    /**\n    @internal\n    */\n    emitContext() {\n        let last = this.buffer.length - 1;\n        if (last < 0 || this.buffer[last] != -3)\n            this.buffer.push(this.curContext.hash, this.pos, this.pos, -3);\n    }\n    /**\n    @internal\n    */\n    emitLookAhead() {\n        let last = this.buffer.length - 1;\n        if (last < 0 || this.buffer[last] != -4)\n            this.buffer.push(this.lookAhead, this.pos, this.pos, -4);\n    }\n    updateContext(context) {\n        if (context != this.curContext.context) {\n            let newCx = new StackContext(this.curContext.tracker, context);\n            if (newCx.hash != this.curContext.hash)\n                this.emitContext();\n            this.curContext = newCx;\n        }\n    }\n    /**\n    @internal\n    */\n    setLookAhead(lookAhead) {\n        if (lookAhead > this.lookAhead) {\n            this.emitLookAhead();\n            this.lookAhead = lookAhead;\n        }\n    }\n    /**\n    @internal\n    */\n    close() {\n        if (this.curContext && this.curContext.tracker.strict)\n            this.emitContext();\n        if (this.lookAhead > 0)\n            this.emitLookAhead();\n    }\n}\nclass StackContext {\n    constructor(tracker, context) {\n        this.tracker = tracker;\n        this.context = context;\n        this.hash = tracker.strict ? tracker.hash(context) : 0;\n    }\n}\n// Used to cheaply run some reductions to scan ahead without mutating\n// an entire stack\nclass SimulatedStack {\n    constructor(start) {\n        this.start = start;\n        this.state = start.state;\n        this.stack = start.stack;\n        this.base = this.stack.length;\n    }\n    reduce(action) {\n        let term = action & 65535 /* Action.ValueMask */, depth = action >> 19 /* Action.ReduceDepthShift */;\n        if (depth == 0) {\n            if (this.stack == this.start.stack)\n                this.stack = this.stack.slice();\n            this.stack.push(this.state, 0, 0);\n            this.base += 3;\n        }\n        else {\n            this.base -= (depth - 1) * 3;\n        }\n        let goto = this.start.p.parser.getGoto(this.stack[this.base - 3], term, true);\n        this.state = goto;\n    }\n}\n// This is given to `Tree.build` to build a buffer, and encapsulates\n// the parent-stack-walking necessary to read the nodes.\nclass StackBufferCursor {\n    constructor(stack, pos, index) {\n        this.stack = stack;\n        this.pos = pos;\n        this.index = index;\n        this.buffer = stack.buffer;\n        if (this.index == 0)\n            this.maybeNext();\n    }\n    static create(stack, pos = stack.bufferBase + stack.buffer.length) {\n        return new StackBufferCursor(stack, pos, pos - stack.bufferBase);\n    }\n    maybeNext() {\n        let next = this.stack.parent;\n        if (next != null) {\n            this.index = this.stack.bufferBase - next.bufferBase;\n            this.stack = next;\n            this.buffer = next.buffer;\n        }\n    }\n    get id() { return this.buffer[this.index - 4]; }\n    get start() { return this.buffer[this.index - 3]; }\n    get end() { return this.buffer[this.index - 2]; }\n    get size() { return this.buffer[this.index - 1]; }\n    next() {\n        this.index -= 4;\n        this.pos -= 4;\n        if (this.index == 0)\n            this.maybeNext();\n    }\n    fork() {\n        return new StackBufferCursor(this.stack, this.pos, this.index);\n    }\n}\n\n// See lezer-generator/src/encode.ts for comments about the encoding\n// used here\nfunction decodeArray(input, Type = Uint16Array) {\n    if (typeof input != \"string\")\n        return input;\n    let array = null;\n    for (let pos = 0, out = 0; pos < input.length;) {\n        let value = 0;\n        for (;;) {\n            let next = input.charCodeAt(pos++), stop = false;\n            if (next == 126 /* Encode.BigValCode */) {\n                value = 65535 /* Encode.BigVal */;\n                break;\n            }\n            if (next >= 92 /* Encode.Gap2 */)\n                next--;\n            if (next >= 34 /* Encode.Gap1 */)\n                next--;\n            let digit = next - 32 /* Encode.Start */;\n            if (digit >= 46 /* Encode.Base */) {\n                digit -= 46 /* Encode.Base */;\n                stop = true;\n            }\n            value += digit;\n            if (stop)\n                break;\n            value *= 46 /* Encode.Base */;\n        }\n        if (array)\n            array[out++] = value;\n        else\n            array = new Type(value);\n    }\n    return array;\n}\n\nclass CachedToken {\n    constructor() {\n        this.start = -1;\n        this.value = -1;\n        this.end = -1;\n        this.extended = -1;\n        this.lookAhead = 0;\n        this.mask = 0;\n        this.context = 0;\n    }\n}\nconst nullToken = new CachedToken;\n/**\n[Tokenizers](#lr.ExternalTokenizer) interact with the input\nthrough this interface. It presents the input as a stream of\ncharacters, tracking lookahead and hiding the complexity of\n[ranges](#common.Parser.parse^ranges) from tokenizer code.\n*/\nclass InputStream {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    input, \n    /**\n    @internal\n    */\n    ranges) {\n        this.input = input;\n        this.ranges = ranges;\n        /**\n        @internal\n        */\n        this.chunk = \"\";\n        /**\n        @internal\n        */\n        this.chunkOff = 0;\n        /**\n        Backup chunk\n        */\n        this.chunk2 = \"\";\n        this.chunk2Pos = 0;\n        /**\n        The character code of the next code unit in the input, or -1\n        when the stream is at the end of the input.\n        */\n        this.next = -1;\n        /**\n        @internal\n        */\n        this.token = nullToken;\n        this.rangeIndex = 0;\n        this.pos = this.chunkPos = ranges[0].from;\n        this.range = ranges[0];\n        this.end = ranges[ranges.length - 1].to;\n        this.readNext();\n    }\n    /**\n    @internal\n    */\n    resolveOffset(offset, assoc) {\n        let range = this.range, index = this.rangeIndex;\n        let pos = this.pos + offset;\n        while (pos < range.from) {\n            if (!index)\n                return null;\n            let next = this.ranges[--index];\n            pos -= range.from - next.to;\n            range = next;\n        }\n        while (assoc < 0 ? pos > range.to : pos >= range.to) {\n            if (index == this.ranges.length - 1)\n                return null;\n            let next = this.ranges[++index];\n            pos += next.from - range.to;\n            range = next;\n        }\n        return pos;\n    }\n    /**\n    @internal\n    */\n    clipPos(pos) {\n        if (pos >= this.range.from && pos < this.range.to)\n            return pos;\n        for (let range of this.ranges)\n            if (range.to > pos)\n                return Math.max(pos, range.from);\n        return this.end;\n    }\n    /**\n    Look at a code unit near the stream position. `.peek(0)` equals\n    `.next`, `.peek(-1)` gives you the previous character, and so\n    on.\n    \n    Note that looking around during tokenizing creates dependencies\n    on potentially far-away content, which may reduce the\n    effectiveness incremental parsing—when looking forward—or even\n    cause invalid reparses when looking backward more than 25 code\n    units, since the library does not track lookbehind.\n    */\n    peek(offset) {\n        let idx = this.chunkOff + offset, pos, result;\n        if (idx >= 0 && idx < this.chunk.length) {\n            pos = this.pos + offset;\n            result = this.chunk.charCodeAt(idx);\n        }\n        else {\n            let resolved = this.resolveOffset(offset, 1);\n            if (resolved == null)\n                return -1;\n            pos = resolved;\n            if (pos >= this.chunk2Pos && pos < this.chunk2Pos + this.chunk2.length) {\n                result = this.chunk2.charCodeAt(pos - this.chunk2Pos);\n            }\n            else {\n                let i = this.rangeIndex, range = this.range;\n                while (range.to <= pos)\n                    range = this.ranges[++i];\n                this.chunk2 = this.input.chunk(this.chunk2Pos = pos);\n                if (pos + this.chunk2.length > range.to)\n                    this.chunk2 = this.chunk2.slice(0, range.to - pos);\n                result = this.chunk2.charCodeAt(0);\n            }\n        }\n        if (pos >= this.token.lookAhead)\n            this.token.lookAhead = pos + 1;\n        return result;\n    }\n    /**\n    Accept a token. By default, the end of the token is set to the\n    current stream position, but you can pass an offset (relative to\n    the stream position) to change that.\n    */\n    acceptToken(token, endOffset = 0) {\n        let end = endOffset ? this.resolveOffset(endOffset, -1) : this.pos;\n        if (end == null || end < this.token.start)\n            throw new RangeError(\"Token end out of bounds\");\n        this.token.value = token;\n        this.token.end = end;\n    }\n    getChunk() {\n        if (this.pos >= this.chunk2Pos && this.pos < this.chunk2Pos + this.chunk2.length) {\n            let { chunk, chunkPos } = this;\n            this.chunk = this.chunk2;\n            this.chunkPos = this.chunk2Pos;\n            this.chunk2 = chunk;\n            this.chunk2Pos = chunkPos;\n            this.chunkOff = this.pos - this.chunkPos;\n        }\n        else {\n            this.chunk2 = this.chunk;\n            this.chunk2Pos = this.chunkPos;\n            let nextChunk = this.input.chunk(this.pos);\n            let end = this.pos + nextChunk.length;\n            this.chunk = end > this.range.to ? nextChunk.slice(0, this.range.to - this.pos) : nextChunk;\n            this.chunkPos = this.pos;\n            this.chunkOff = 0;\n        }\n    }\n    readNext() {\n        if (this.chunkOff >= this.chunk.length) {\n            this.getChunk();\n            if (this.chunkOff == this.chunk.length)\n                return this.next = -1;\n        }\n        return this.next = this.chunk.charCodeAt(this.chunkOff);\n    }\n    /**\n    Move the stream forward N (defaults to 1) code units. Returns\n    the new value of [`next`](#lr.InputStream.next).\n    */\n    advance(n = 1) {\n        this.chunkOff += n;\n        while (this.pos + n >= this.range.to) {\n            if (this.rangeIndex == this.ranges.length - 1)\n                return this.setDone();\n            n -= this.range.to - this.pos;\n            this.range = this.ranges[++this.rangeIndex];\n            this.pos = this.range.from;\n        }\n        this.pos += n;\n        if (this.pos >= this.token.lookAhead)\n            this.token.lookAhead = this.pos + 1;\n        return this.readNext();\n    }\n    setDone() {\n        this.pos = this.chunkPos = this.end;\n        this.range = this.ranges[this.rangeIndex = this.ranges.length - 1];\n        this.chunk = \"\";\n        return this.next = -1;\n    }\n    /**\n    @internal\n    */\n    reset(pos, token) {\n        if (token) {\n            this.token = token;\n            token.start = pos;\n            token.lookAhead = pos + 1;\n            token.value = token.extended = -1;\n        }\n        else {\n            this.token = nullToken;\n        }\n        if (this.pos != pos) {\n            this.pos = pos;\n            if (pos == this.end) {\n                this.setDone();\n                return this;\n            }\n            while (pos < this.range.from)\n                this.range = this.ranges[--this.rangeIndex];\n            while (pos >= this.range.to)\n                this.range = this.ranges[++this.rangeIndex];\n            if (pos >= this.chunkPos && pos < this.chunkPos + this.chunk.length) {\n                this.chunkOff = pos - this.chunkPos;\n            }\n            else {\n                this.chunk = \"\";\n                this.chunkOff = 0;\n            }\n            this.readNext();\n        }\n        return this;\n    }\n    /**\n    @internal\n    */\n    read(from, to) {\n        if (from >= this.chunkPos && to <= this.chunkPos + this.chunk.length)\n            return this.chunk.slice(from - this.chunkPos, to - this.chunkPos);\n        if (from >= this.chunk2Pos && to <= this.chunk2Pos + this.chunk2.length)\n            return this.chunk2.slice(from - this.chunk2Pos, to - this.chunk2Pos);\n        if (from >= this.range.from && to <= this.range.to)\n            return this.input.read(from, to);\n        let result = \"\";\n        for (let r of this.ranges) {\n            if (r.from >= to)\n                break;\n            if (r.to > from)\n                result += this.input.read(Math.max(r.from, from), Math.min(r.to, to));\n        }\n        return result;\n    }\n}\n/**\n@internal\n*/\nclass TokenGroup {\n    constructor(data, id) {\n        this.data = data;\n        this.id = id;\n    }\n    token(input, stack) {\n        let { parser } = stack.p;\n        readToken(this.data, input, stack, this.id, parser.data, parser.tokenPrecTable);\n    }\n}\nTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\n/**\n@hide\n*/\nclass LocalTokenGroup {\n    constructor(data, precTable, elseToken) {\n        this.precTable = precTable;\n        this.elseToken = elseToken;\n        this.data = typeof data == \"string\" ? decodeArray(data) : data;\n    }\n    token(input, stack) {\n        let start = input.pos, skipped = 0;\n        for (;;) {\n            let atEof = input.next < 0, nextPos = input.resolveOffset(1, 1);\n            readToken(this.data, input, stack, 0, this.data, this.precTable);\n            if (input.token.value > -1)\n                break;\n            if (this.elseToken == null)\n                return;\n            if (!atEof)\n                skipped++;\n            if (nextPos == null)\n                break;\n            input.reset(nextPos, input.token);\n        }\n        if (skipped) {\n            input.reset(start, input.token);\n            input.acceptToken(this.elseToken, skipped);\n        }\n    }\n}\nLocalTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\n/**\n`@external tokens` declarations in the grammar should resolve to\nan instance of this class.\n*/\nclass ExternalTokenizer {\n    /**\n    Create a tokenizer. The first argument is the function that,\n    given an input stream, scans for the types of tokens it\n    recognizes at the stream's position, and calls\n    [`acceptToken`](#lr.InputStream.acceptToken) when it finds\n    one.\n    */\n    constructor(\n    /**\n    @internal\n    */\n    token, options = {}) {\n        this.token = token;\n        this.contextual = !!options.contextual;\n        this.fallback = !!options.fallback;\n        this.extend = !!options.extend;\n    }\n}\n// Tokenizer data is stored a big uint16 array containing, for each\n// state:\n//\n//  - A group bitmask, indicating what token groups are reachable from\n//    this state, so that paths that can only lead to tokens not in\n//    any of the current groups can be cut off early.\n//\n//  - The position of the end of the state's sequence of accepting\n//    tokens\n//\n//  - The number of outgoing edges for the state\n//\n//  - The accepting tokens, as (token id, group mask) pairs\n//\n//  - The outgoing edges, as (start character, end character, state\n//    index) triples, with end character being exclusive\n//\n// This function interprets that data, running through a stream as\n// long as new states with the a matching group mask can be reached,\n// and updating `input.token` when it matches a token.\nfunction readToken(data, input, stack, group, precTable, precOffset) {\n    let state = 0, groupMask = 1 << group, { dialect } = stack.p.parser;\n    scan: for (;;) {\n        if ((groupMask & data[state]) == 0)\n            break;\n        let accEnd = data[state + 1];\n        // Check whether this state can lead to a token in the current group\n        // Accept tokens in this state, possibly overwriting\n        // lower-precedence / shorter tokens\n        for (let i = state + 3; i < accEnd; i += 2)\n            if ((data[i + 1] & groupMask) > 0) {\n                let term = data[i];\n                if (dialect.allows(term) &&\n                    (input.token.value == -1 || input.token.value == term ||\n                        overrides(term, input.token.value, precTable, precOffset))) {\n                    input.acceptToken(term);\n                    break;\n                }\n            }\n        let next = input.next, low = 0, high = data[state + 2];\n        // Special case for EOF\n        if (input.next < 0 && high > low && data[accEnd + high * 3 - 3] == 65535 /* Seq.End */) {\n            state = data[accEnd + high * 3 - 1];\n            continue scan;\n        }\n        // Do a binary search on the state's edges\n        for (; low < high;) {\n            let mid = (low + high) >> 1;\n            let index = accEnd + mid + (mid << 1);\n            let from = data[index], to = data[index + 1] || 0x10000;\n            if (next < from)\n                high = mid;\n            else if (next >= to)\n                low = mid + 1;\n            else {\n                state = data[index + 2];\n                input.advance();\n                continue scan;\n            }\n        }\n        break;\n    }\n}\nfunction findOffset(data, start, term) {\n    for (let i = start, next; (next = data[i]) != 65535 /* Seq.End */; i++)\n        if (next == term)\n            return i - start;\n    return -1;\n}\nfunction overrides(token, prev, tableData, tableOffset) {\n    let iPrev = findOffset(tableData, tableOffset, prev);\n    return iPrev < 0 || findOffset(tableData, tableOffset, token) < iPrev;\n}\n\n// Environment variable used to control console output\nconst verbose = typeof process != \"undefined\" && process.env && /\\bparse\\b/.test(process.env.LOG);\nlet stackIDs = null;\nfunction cutAt(tree, pos, side) {\n    let cursor = tree.cursor(IterMode.IncludeAnonymous);\n    cursor.moveTo(pos);\n    for (;;) {\n        if (!(side < 0 ? cursor.childBefore(pos) : cursor.childAfter(pos)))\n            for (;;) {\n                if ((side < 0 ? cursor.to < pos : cursor.from > pos) && !cursor.type.isError)\n                    return side < 0 ? Math.max(0, Math.min(cursor.to - 1, pos - 25 /* Safety.Margin */))\n                        : Math.min(tree.length, Math.max(cursor.from + 1, pos + 25 /* Safety.Margin */));\n                if (side < 0 ? cursor.prevSibling() : cursor.nextSibling())\n                    break;\n                if (!cursor.parent())\n                    return side < 0 ? 0 : tree.length;\n            }\n    }\n}\nclass FragmentCursor {\n    constructor(fragments, nodeSet) {\n        this.fragments = fragments;\n        this.nodeSet = nodeSet;\n        this.i = 0;\n        this.fragment = null;\n        this.safeFrom = -1;\n        this.safeTo = -1;\n        this.trees = [];\n        this.start = [];\n        this.index = [];\n        this.nextFragment();\n    }\n    nextFragment() {\n        let fr = this.fragment = this.i == this.fragments.length ? null : this.fragments[this.i++];\n        if (fr) {\n            this.safeFrom = fr.openStart ? cutAt(fr.tree, fr.from + fr.offset, 1) - fr.offset : fr.from;\n            this.safeTo = fr.openEnd ? cutAt(fr.tree, fr.to + fr.offset, -1) - fr.offset : fr.to;\n            while (this.trees.length) {\n                this.trees.pop();\n                this.start.pop();\n                this.index.pop();\n            }\n            this.trees.push(fr.tree);\n            this.start.push(-fr.offset);\n            this.index.push(0);\n            this.nextStart = this.safeFrom;\n        }\n        else {\n            this.nextStart = 1e9;\n        }\n    }\n    // `pos` must be >= any previously given `pos` for this cursor\n    nodeAt(pos) {\n        if (pos < this.nextStart)\n            return null;\n        while (this.fragment && this.safeTo <= pos)\n            this.nextFragment();\n        if (!this.fragment)\n            return null;\n        for (;;) {\n            let last = this.trees.length - 1;\n            if (last < 0) { // End of tree\n                this.nextFragment();\n                return null;\n            }\n            let top = this.trees[last], index = this.index[last];\n            if (index == top.children.length) {\n                this.trees.pop();\n                this.start.pop();\n                this.index.pop();\n                continue;\n            }\n            let next = top.children[index];\n            let start = this.start[last] + top.positions[index];\n            if (start > pos) {\n                this.nextStart = start;\n                return null;\n            }\n            if (next instanceof Tree) {\n                if (start == pos) {\n                    if (start < this.safeFrom)\n                        return null;\n                    let end = start + next.length;\n                    if (end <= this.safeTo) {\n                        let lookAhead = next.prop(NodeProp.lookAhead);\n                        if (!lookAhead || end + lookAhead < this.fragment.to)\n                            return next;\n                    }\n                }\n                this.index[last]++;\n                if (start + next.length >= Math.max(this.safeFrom, pos)) { // Enter this node\n                    this.trees.push(next);\n                    this.start.push(start);\n                    this.index.push(0);\n                }\n            }\n            else {\n                this.index[last]++;\n                this.nextStart = start + next.length;\n            }\n        }\n    }\n}\nclass TokenCache {\n    constructor(parser, stream) {\n        this.stream = stream;\n        this.tokens = [];\n        this.mainToken = null;\n        this.actions = [];\n        this.tokens = parser.tokenizers.map(_ => new CachedToken);\n    }\n    getActions(stack) {\n        let actionIndex = 0;\n        let main = null;\n        let { parser } = stack.p, { tokenizers } = parser;\n        let mask = parser.stateSlot(stack.state, 3 /* ParseState.TokenizerMask */);\n        let context = stack.curContext ? stack.curContext.hash : 0;\n        let lookAhead = 0;\n        for (let i = 0; i < tokenizers.length; i++) {\n            if (((1 << i) & mask) == 0)\n                continue;\n            let tokenizer = tokenizers[i], token = this.tokens[i];\n            if (main && !tokenizer.fallback)\n                continue;\n            if (tokenizer.contextual || token.start != stack.pos || token.mask != mask || token.context != context) {\n                this.updateCachedToken(token, tokenizer, stack);\n                token.mask = mask;\n                token.context = context;\n            }\n            if (token.lookAhead > token.end + 25 /* Safety.Margin */)\n                lookAhead = Math.max(token.lookAhead, lookAhead);\n            if (token.value != 0 /* Term.Err */) {\n                let startIndex = actionIndex;\n                if (token.extended > -1)\n                    actionIndex = this.addActions(stack, token.extended, token.end, actionIndex);\n                actionIndex = this.addActions(stack, token.value, token.end, actionIndex);\n                if (!tokenizer.extend) {\n                    main = token;\n                    if (actionIndex > startIndex)\n                        break;\n                }\n            }\n        }\n        while (this.actions.length > actionIndex)\n            this.actions.pop();\n        if (lookAhead)\n            stack.setLookAhead(lookAhead);\n        if (!main && stack.pos == this.stream.end) {\n            main = new CachedToken;\n            main.value = stack.p.parser.eofTerm;\n            main.start = main.end = stack.pos;\n            actionIndex = this.addActions(stack, main.value, main.end, actionIndex);\n        }\n        this.mainToken = main;\n        return this.actions;\n    }\n    getMainToken(stack) {\n        if (this.mainToken)\n            return this.mainToken;\n        let main = new CachedToken, { pos, p } = stack;\n        main.start = pos;\n        main.end = Math.min(pos + 1, p.stream.end);\n        main.value = pos == p.stream.end ? p.parser.eofTerm : 0 /* Term.Err */;\n        return main;\n    }\n    updateCachedToken(token, tokenizer, stack) {\n        let start = this.stream.clipPos(stack.pos);\n        tokenizer.token(this.stream.reset(start, token), stack);\n        if (token.value > -1) {\n            let { parser } = stack.p;\n            for (let i = 0; i < parser.specialized.length; i++)\n                if (parser.specialized[i] == token.value) {\n                    let result = parser.specializers[i](this.stream.read(token.start, token.end), stack);\n                    if (result >= 0 && stack.p.parser.dialect.allows(result >> 1)) {\n                        if ((result & 1) == 0 /* Specialize.Specialize */)\n                            token.value = result >> 1;\n                        else\n                            token.extended = result >> 1;\n                        break;\n                    }\n                }\n        }\n        else {\n            token.value = 0 /* Term.Err */;\n            token.end = this.stream.clipPos(start + 1);\n        }\n    }\n    putAction(action, token, end, index) {\n        // Don't add duplicate actions\n        for (let i = 0; i < index; i += 3)\n            if (this.actions[i] == action)\n                return index;\n        this.actions[index++] = action;\n        this.actions[index++] = token;\n        this.actions[index++] = end;\n        return index;\n    }\n    addActions(stack, token, end, index) {\n        let { state } = stack, { parser } = stack.p, { data } = parser;\n        for (let set = 0; set < 2; set++) {\n            for (let i = parser.stateSlot(state, set ? 2 /* ParseState.Skip */ : 1 /* ParseState.Actions */);; i += 3) {\n                if (data[i] == 65535 /* Seq.End */) {\n                    if (data[i + 1] == 1 /* Seq.Next */) {\n                        i = pair(data, i + 2);\n                    }\n                    else {\n                        if (index == 0 && data[i + 1] == 2 /* Seq.Other */)\n                            index = this.putAction(pair(data, i + 2), token, end, index);\n                        break;\n                    }\n                }\n                if (data[i] == token)\n                    index = this.putAction(pair(data, i + 1), token, end, index);\n            }\n        }\n        return index;\n    }\n}\nclass Parse {\n    constructor(parser, input, fragments, ranges) {\n        this.parser = parser;\n        this.input = input;\n        this.ranges = ranges;\n        this.recovering = 0;\n        this.nextStackID = 0x2654; // ♔, ♕, ♖, ♗, ♘, ♙, ♠, ♡, ♢, ♣, ♤, ♥, ♦, ♧\n        this.minStackPos = 0;\n        this.reused = [];\n        this.stoppedAt = null;\n        this.lastBigReductionStart = -1;\n        this.lastBigReductionSize = 0;\n        this.bigReductionCount = 0;\n        this.stream = new InputStream(input, ranges);\n        this.tokens = new TokenCache(parser, this.stream);\n        this.topTerm = parser.top[1];\n        let { from } = ranges[0];\n        this.stacks = [Stack.start(this, parser.top[0], from)];\n        this.fragments = fragments.length && this.stream.end - from > parser.bufferLength * 4\n            ? new FragmentCursor(fragments, parser.nodeSet) : null;\n    }\n    get parsedPos() {\n        return this.minStackPos;\n    }\n    // Move the parser forward. This will process all parse stacks at\n    // `this.pos` and try to advance them to a further position. If no\n    // stack for such a position is found, it'll start error-recovery.\n    //\n    // When the parse is finished, this will return a syntax tree. When\n    // not, it returns `null`.\n    advance() {\n        let stacks = this.stacks, pos = this.minStackPos;\n        // This will hold stacks beyond `pos`.\n        let newStacks = this.stacks = [];\n        let stopped, stoppedTokens;\n        // If a large amount of reductions happened with the same start\n        // position, force the stack out of that production in order to\n        // avoid creating a tree too deep to recurse through.\n        // (This is an ugly kludge, because unfortunately there is no\n        // straightforward, cheap way to check for this happening, due to\n        // the history of reductions only being available in an\n        // expensive-to-access format in the stack buffers.)\n        if (this.bigReductionCount > 300 /* Rec.MaxLeftAssociativeReductionCount */ && stacks.length == 1) {\n            let [s] = stacks;\n            while (s.forceReduce() && s.stack.length && s.stack[s.stack.length - 2] >= this.lastBigReductionStart) { }\n            this.bigReductionCount = this.lastBigReductionSize = 0;\n        }\n        // Keep advancing any stacks at `pos` until they either move\n        // forward or can't be advanced. Gather stacks that can't be\n        // advanced further in `stopped`.\n        for (let i = 0; i < stacks.length; i++) {\n            let stack = stacks[i];\n            for (;;) {\n                this.tokens.mainToken = null;\n                if (stack.pos > pos) {\n                    newStacks.push(stack);\n                }\n                else if (this.advanceStack(stack, newStacks, stacks)) {\n                    continue;\n                }\n                else {\n                    if (!stopped) {\n                        stopped = [];\n                        stoppedTokens = [];\n                    }\n                    stopped.push(stack);\n                    let tok = this.tokens.getMainToken(stack);\n                    stoppedTokens.push(tok.value, tok.end);\n                }\n                break;\n            }\n        }\n        if (!newStacks.length) {\n            let finished = stopped && findFinished(stopped);\n            if (finished) {\n                if (verbose)\n                    console.log(\"Finish with \" + this.stackID(finished));\n                return this.stackToTree(finished);\n            }\n            if (this.parser.strict) {\n                if (verbose && stopped)\n                    console.log(\"Stuck with token \" + (this.tokens.mainToken ? this.parser.getName(this.tokens.mainToken.value) : \"none\"));\n                throw new SyntaxError(\"No parse at \" + pos);\n            }\n            if (!this.recovering)\n                this.recovering = 5 /* Rec.Distance */;\n        }\n        if (this.recovering && stopped) {\n            let finished = this.stoppedAt != null && stopped[0].pos > this.stoppedAt ? stopped[0]\n                : this.runRecovery(stopped, stoppedTokens, newStacks);\n            if (finished) {\n                if (verbose)\n                    console.log(\"Force-finish \" + this.stackID(finished));\n                return this.stackToTree(finished.forceAll());\n            }\n        }\n        if (this.recovering) {\n            let maxRemaining = this.recovering == 1 ? 1 : this.recovering * 3 /* Rec.MaxRemainingPerStep */;\n            if (newStacks.length > maxRemaining) {\n                newStacks.sort((a, b) => b.score - a.score);\n                while (newStacks.length > maxRemaining)\n                    newStacks.pop();\n            }\n            if (newStacks.some(s => s.reducePos > pos))\n                this.recovering--;\n        }\n        else if (newStacks.length > 1) {\n            // Prune stacks that are in the same state, or that have been\n            // running without splitting for a while, to avoid getting stuck\n            // with multiple successful stacks running endlessly on.\n            outer: for (let i = 0; i < newStacks.length - 1; i++) {\n                let stack = newStacks[i];\n                for (let j = i + 1; j < newStacks.length; j++) {\n                    let other = newStacks[j];\n                    if (stack.sameState(other) ||\n                        stack.buffer.length > 500 /* Rec.MinBufferLengthPrune */ && other.buffer.length > 500 /* Rec.MinBufferLengthPrune */) {\n                        if (((stack.score - other.score) || (stack.buffer.length - other.buffer.length)) > 0) {\n                            newStacks.splice(j--, 1);\n                        }\n                        else {\n                            newStacks.splice(i--, 1);\n                            continue outer;\n                        }\n                    }\n                }\n            }\n            if (newStacks.length > 12 /* Rec.MaxStackCount */)\n                newStacks.splice(12 /* Rec.MaxStackCount */, newStacks.length - 12 /* Rec.MaxStackCount */);\n        }\n        this.minStackPos = newStacks[0].pos;\n        for (let i = 1; i < newStacks.length; i++)\n            if (newStacks[i].pos < this.minStackPos)\n                this.minStackPos = newStacks[i].pos;\n        return null;\n    }\n    stopAt(pos) {\n        if (this.stoppedAt != null && this.stoppedAt < pos)\n            throw new RangeError(\"Can't move stoppedAt forward\");\n        this.stoppedAt = pos;\n    }\n    // Returns an updated version of the given stack, or null if the\n    // stack can't advance normally. When `split` and `stacks` are\n    // given, stacks split off by ambiguous operations will be pushed to\n    // `split`, or added to `stacks` if they move `pos` forward.\n    advanceStack(stack, stacks, split) {\n        let start = stack.pos, { parser } = this;\n        let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n        if (this.stoppedAt != null && start > this.stoppedAt)\n            return stack.forceReduce() ? stack : null;\n        if (this.fragments) {\n            let strictCx = stack.curContext && stack.curContext.tracker.strict, cxHash = strictCx ? stack.curContext.hash : 0;\n            for (let cached = this.fragments.nodeAt(start); cached;) {\n                let match = this.parser.nodeSet.types[cached.type.id] == cached.type ? parser.getGoto(stack.state, cached.type.id) : -1;\n                if (match > -1 && cached.length && (!strictCx || (cached.prop(NodeProp.contextHash) || 0) == cxHash)) {\n                    stack.useNode(cached, match);\n                    if (verbose)\n                        console.log(base + this.stackID(stack) + ` (via reuse of ${parser.getName(cached.type.id)})`);\n                    return true;\n                }\n                if (!(cached instanceof Tree) || cached.children.length == 0 || cached.positions[0] > 0)\n                    break;\n                let inner = cached.children[0];\n                if (inner instanceof Tree && cached.positions[0] == 0)\n                    cached = inner;\n                else\n                    break;\n            }\n        }\n        let defaultReduce = parser.stateSlot(stack.state, 4 /* ParseState.DefaultReduce */);\n        if (defaultReduce > 0) {\n            stack.reduce(defaultReduce);\n            if (verbose)\n                console.log(base + this.stackID(stack) + ` (via always-reduce ${parser.getName(defaultReduce & 65535 /* Action.ValueMask */)})`);\n            return true;\n        }\n        if (stack.stack.length >= 8400 /* Rec.CutDepth */) {\n            while (stack.stack.length > 6000 /* Rec.CutTo */ && stack.forceReduce()) { }\n        }\n        let actions = this.tokens.getActions(stack);\n        for (let i = 0; i < actions.length;) {\n            let action = actions[i++], term = actions[i++], end = actions[i++];\n            let last = i == actions.length || !split;\n            let localStack = last ? stack : stack.split();\n            let main = this.tokens.mainToken;\n            localStack.apply(action, term, main ? main.start : localStack.pos, end);\n            if (verbose)\n                console.log(base + this.stackID(localStack) + ` (via ${(action & 65536 /* Action.ReduceFlag */) == 0 ? \"shift\"\n                    : `reduce of ${parser.getName(action & 65535 /* Action.ValueMask */)}`} for ${parser.getName(term)} @ ${start}${localStack == stack ? \"\" : \", split\"})`);\n            if (last)\n                return true;\n            else if (localStack.pos > start)\n                stacks.push(localStack);\n            else\n                split.push(localStack);\n        }\n        return false;\n    }\n    // Advance a given stack forward as far as it will go. Returns the\n    // (possibly updated) stack if it got stuck, or null if it moved\n    // forward and was given to `pushStackDedup`.\n    advanceFully(stack, newStacks) {\n        let pos = stack.pos;\n        for (;;) {\n            if (!this.advanceStack(stack, null, null))\n                return false;\n            if (stack.pos > pos) {\n                pushStackDedup(stack, newStacks);\n                return true;\n            }\n        }\n    }\n    runRecovery(stacks, tokens, newStacks) {\n        let finished = null, restarted = false;\n        for (let i = 0; i < stacks.length; i++) {\n            let stack = stacks[i], token = tokens[i << 1], tokenEnd = tokens[(i << 1) + 1];\n            let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n            if (stack.deadEnd) {\n                if (restarted)\n                    continue;\n                restarted = true;\n                stack.restart();\n                if (verbose)\n                    console.log(base + this.stackID(stack) + \" (restarted)\");\n                let done = this.advanceFully(stack, newStacks);\n                if (done)\n                    continue;\n            }\n            let force = stack.split(), forceBase = base;\n            for (let j = 0; force.forceReduce() && j < 10 /* Rec.ForceReduceLimit */; j++) {\n                if (verbose)\n                    console.log(forceBase + this.stackID(force) + \" (via force-reduce)\");\n                let done = this.advanceFully(force, newStacks);\n                if (done)\n                    break;\n                if (verbose)\n                    forceBase = this.stackID(force) + \" -> \";\n            }\n            for (let insert of stack.recoverByInsert(token)) {\n                if (verbose)\n                    console.log(base + this.stackID(insert) + \" (via recover-insert)\");\n                this.advanceFully(insert, newStacks);\n            }\n            if (this.stream.end > stack.pos) {\n                if (tokenEnd == stack.pos) {\n                    tokenEnd++;\n                    token = 0 /* Term.Err */;\n                }\n                stack.recoverByDelete(token, tokenEnd);\n                if (verbose)\n                    console.log(base + this.stackID(stack) + ` (via recover-delete ${this.parser.getName(token)})`);\n                pushStackDedup(stack, newStacks);\n            }\n            else if (!finished || finished.score < stack.score) {\n                finished = stack;\n            }\n        }\n        return finished;\n    }\n    // Convert the stack's buffer to a syntax tree.\n    stackToTree(stack) {\n        stack.close();\n        return Tree.build({ buffer: StackBufferCursor.create(stack),\n            nodeSet: this.parser.nodeSet,\n            topID: this.topTerm,\n            maxBufferLength: this.parser.bufferLength,\n            reused: this.reused,\n            start: this.ranges[0].from,\n            length: stack.pos - this.ranges[0].from,\n            minRepeatType: this.parser.minRepeatTerm });\n    }\n    stackID(stack) {\n        let id = (stackIDs || (stackIDs = new WeakMap)).get(stack);\n        if (!id)\n            stackIDs.set(stack, id = String.fromCodePoint(this.nextStackID++));\n        return id + stack;\n    }\n}\nfunction pushStackDedup(stack, newStacks) {\n    for (let i = 0; i < newStacks.length; i++) {\n        let other = newStacks[i];\n        if (other.pos == stack.pos && other.sameState(stack)) {\n            if (newStacks[i].score < stack.score)\n                newStacks[i] = stack;\n            return;\n        }\n    }\n    newStacks.push(stack);\n}\nclass Dialect {\n    constructor(source, flags, disabled) {\n        this.source = source;\n        this.flags = flags;\n        this.disabled = disabled;\n    }\n    allows(term) { return !this.disabled || this.disabled[term] == 0; }\n}\nconst id = x => x;\n/**\nContext trackers are used to track stateful context (such as\nindentation in the Python grammar, or parent elements in the XML\ngrammar) needed by external tokenizers. You declare them in a\ngrammar file as `@context exportName from \"module\"`.\n\nContext values should be immutable, and can be updated (replaced)\non shift or reduce actions.\n\nThe export used in a `@context` declaration should be of this\ntype.\n*/\nclass ContextTracker {\n    /**\n    Define a context tracker.\n    */\n    constructor(spec) {\n        this.start = spec.start;\n        this.shift = spec.shift || id;\n        this.reduce = spec.reduce || id;\n        this.reuse = spec.reuse || id;\n        this.hash = spec.hash || (() => 0);\n        this.strict = spec.strict !== false;\n    }\n}\n/**\nHolds the parse tables for a given grammar, as generated by\n`lezer-generator`, and provides [methods](#common.Parser) to parse\ncontent with.\n*/\nclass LRParser extends Parser {\n    /**\n    @internal\n    */\n    constructor(spec) {\n        super();\n        /**\n        @internal\n        */\n        this.wrappers = [];\n        if (spec.version != 14 /* File.Version */)\n            throw new RangeError(`Parser version (${spec.version}) doesn't match runtime version (${14 /* File.Version */})`);\n        let nodeNames = spec.nodeNames.split(\" \");\n        this.minRepeatTerm = nodeNames.length;\n        for (let i = 0; i < spec.repeatNodeCount; i++)\n            nodeNames.push(\"\");\n        let topTerms = Object.keys(spec.topRules).map(r => spec.topRules[r][1]);\n        let nodeProps = [];\n        for (let i = 0; i < nodeNames.length; i++)\n            nodeProps.push([]);\n        function setProp(nodeID, prop, value) {\n            nodeProps[nodeID].push([prop, prop.deserialize(String(value))]);\n        }\n        if (spec.nodeProps)\n            for (let propSpec of spec.nodeProps) {\n                let prop = propSpec[0];\n                if (typeof prop == \"string\")\n                    prop = NodeProp[prop];\n                for (let i = 1; i < propSpec.length;) {\n                    let next = propSpec[i++];\n                    if (next >= 0) {\n                        setProp(next, prop, propSpec[i++]);\n                    }\n                    else {\n                        let value = propSpec[i + -next];\n                        for (let j = -next; j > 0; j--)\n                            setProp(propSpec[i++], prop, value);\n                        i++;\n                    }\n                }\n            }\n        this.nodeSet = new NodeSet(nodeNames.map((name, i) => NodeType.define({\n            name: i >= this.minRepeatTerm ? undefined : name,\n            id: i,\n            props: nodeProps[i],\n            top: topTerms.indexOf(i) > -1,\n            error: i == 0,\n            skipped: spec.skippedNodes && spec.skippedNodes.indexOf(i) > -1\n        })));\n        if (spec.propSources)\n            this.nodeSet = this.nodeSet.extend(...spec.propSources);\n        this.strict = false;\n        this.bufferLength = DefaultBufferLength;\n        let tokenArray = decodeArray(spec.tokenData);\n        this.context = spec.context;\n        this.specializerSpecs = spec.specialized || [];\n        this.specialized = new Uint16Array(this.specializerSpecs.length);\n        for (let i = 0; i < this.specializerSpecs.length; i++)\n            this.specialized[i] = this.specializerSpecs[i].term;\n        this.specializers = this.specializerSpecs.map(getSpecializer);\n        this.states = decodeArray(spec.states, Uint32Array);\n        this.data = decodeArray(spec.stateData);\n        this.goto = decodeArray(spec.goto);\n        this.maxTerm = spec.maxTerm;\n        this.tokenizers = spec.tokenizers.map(value => typeof value == \"number\" ? new TokenGroup(tokenArray, value) : value);\n        this.topRules = spec.topRules;\n        this.dialects = spec.dialects || {};\n        this.dynamicPrecedences = spec.dynamicPrecedences || null;\n        this.tokenPrecTable = spec.tokenPrec;\n        this.termNames = spec.termNames || null;\n        this.maxNode = this.nodeSet.types.length - 1;\n        this.dialect = this.parseDialect();\n        this.top = this.topRules[Object.keys(this.topRules)[0]];\n    }\n    createParse(input, fragments, ranges) {\n        let parse = new Parse(this, input, fragments, ranges);\n        for (let w of this.wrappers)\n            parse = w(parse, input, fragments, ranges);\n        return parse;\n    }\n    /**\n    Get a goto table entry @internal\n    */\n    getGoto(state, term, loose = false) {\n        let table = this.goto;\n        if (term >= table[0])\n            return -1;\n        for (let pos = table[term + 1];;) {\n            let groupTag = table[pos++], last = groupTag & 1;\n            let target = table[pos++];\n            if (last && loose)\n                return target;\n            for (let end = pos + (groupTag >> 1); pos < end; pos++)\n                if (table[pos] == state)\n                    return target;\n            if (last)\n                return -1;\n        }\n    }\n    /**\n    Check if this state has an action for a given terminal @internal\n    */\n    hasAction(state, terminal) {\n        let data = this.data;\n        for (let set = 0; set < 2; set++) {\n            for (let i = this.stateSlot(state, set ? 2 /* ParseState.Skip */ : 1 /* ParseState.Actions */), next;; i += 3) {\n                if ((next = data[i]) == 65535 /* Seq.End */) {\n                    if (data[i + 1] == 1 /* Seq.Next */)\n                        next = data[i = pair(data, i + 2)];\n                    else if (data[i + 1] == 2 /* Seq.Other */)\n                        return pair(data, i + 2);\n                    else\n                        break;\n                }\n                if (next == terminal || next == 0 /* Term.Err */)\n                    return pair(data, i + 1);\n            }\n        }\n        return 0;\n    }\n    /**\n    @internal\n    */\n    stateSlot(state, slot) {\n        return this.states[(state * 6 /* ParseState.Size */) + slot];\n    }\n    /**\n    @internal\n    */\n    stateFlag(state, flag) {\n        return (this.stateSlot(state, 0 /* ParseState.Flags */) & flag) > 0;\n    }\n    /**\n    @internal\n    */\n    validAction(state, action) {\n        return !!this.allActions(state, a => a == action ? true : null);\n    }\n    /**\n    @internal\n    */\n    allActions(state, action) {\n        let deflt = this.stateSlot(state, 4 /* ParseState.DefaultReduce */);\n        let result = deflt ? action(deflt) : undefined;\n        for (let i = this.stateSlot(state, 1 /* ParseState.Actions */); result == null; i += 3) {\n            if (this.data[i] == 65535 /* Seq.End */) {\n                if (this.data[i + 1] == 1 /* Seq.Next */)\n                    i = pair(this.data, i + 2);\n                else\n                    break;\n            }\n            result = action(pair(this.data, i + 1));\n        }\n        return result;\n    }\n    /**\n    Get the states that can follow this one through shift actions or\n    goto jumps. @internal\n    */\n    nextStates(state) {\n        let result = [];\n        for (let i = this.stateSlot(state, 1 /* ParseState.Actions */);; i += 3) {\n            if (this.data[i] == 65535 /* Seq.End */) {\n                if (this.data[i + 1] == 1 /* Seq.Next */)\n                    i = pair(this.data, i + 2);\n                else\n                    break;\n            }\n            if ((this.data[i + 2] & (65536 /* Action.ReduceFlag */ >> 16)) == 0) {\n                let value = this.data[i + 1];\n                if (!result.some((v, i) => (i & 1) && v == value))\n                    result.push(this.data[i], value);\n            }\n        }\n        return result;\n    }\n    /**\n    Configure the parser. Returns a new parser instance that has the\n    given settings modified. Settings not provided in `config` are\n    kept from the original parser.\n    */\n    configure(config) {\n        // Hideous reflection-based kludge to make it easy to create a\n        // slightly modified copy of a parser.\n        let copy = Object.assign(Object.create(LRParser.prototype), this);\n        if (config.props)\n            copy.nodeSet = this.nodeSet.extend(...config.props);\n        if (config.top) {\n            let info = this.topRules[config.top];\n            if (!info)\n                throw new RangeError(`Invalid top rule name ${config.top}`);\n            copy.top = info;\n        }\n        if (config.tokenizers)\n            copy.tokenizers = this.tokenizers.map(t => {\n                let found = config.tokenizers.find(r => r.from == t);\n                return found ? found.to : t;\n            });\n        if (config.specializers) {\n            copy.specializers = this.specializers.slice();\n            copy.specializerSpecs = this.specializerSpecs.map((s, i) => {\n                let found = config.specializers.find(r => r.from == s.external);\n                if (!found)\n                    return s;\n                let spec = Object.assign(Object.assign({}, s), { external: found.to });\n                copy.specializers[i] = getSpecializer(spec);\n                return spec;\n            });\n        }\n        if (config.contextTracker)\n            copy.context = config.contextTracker;\n        if (config.dialect)\n            copy.dialect = this.parseDialect(config.dialect);\n        if (config.strict != null)\n            copy.strict = config.strict;\n        if (config.wrap)\n            copy.wrappers = copy.wrappers.concat(config.wrap);\n        if (config.bufferLength != null)\n            copy.bufferLength = config.bufferLength;\n        return copy;\n    }\n    /**\n    Tells you whether any [parse wrappers](#lr.ParserConfig.wrap)\n    are registered for this parser.\n    */\n    hasWrappers() {\n        return this.wrappers.length > 0;\n    }\n    /**\n    Returns the name associated with a given term. This will only\n    work for all terms when the parser was generated with the\n    `--names` option. By default, only the names of tagged terms are\n    stored.\n    */\n    getName(term) {\n        return this.termNames ? this.termNames[term] : String(term <= this.maxNode && this.nodeSet.types[term].name || term);\n    }\n    /**\n    The eof term id is always allocated directly after the node\n    types. @internal\n    */\n    get eofTerm() { return this.maxNode + 1; }\n    /**\n    The type of top node produced by the parser.\n    */\n    get topNode() { return this.nodeSet.types[this.top[1]]; }\n    /**\n    @internal\n    */\n    dynamicPrecedence(term) {\n        let prec = this.dynamicPrecedences;\n        return prec == null ? 0 : prec[term] || 0;\n    }\n    /**\n    @internal\n    */\n    parseDialect(dialect) {\n        let values = Object.keys(this.dialects), flags = values.map(() => false);\n        if (dialect)\n            for (let part of dialect.split(\" \")) {\n                let id = values.indexOf(part);\n                if (id >= 0)\n                    flags[id] = true;\n            }\n        let disabled = null;\n        for (let i = 0; i < values.length; i++)\n            if (!flags[i]) {\n                for (let j = this.dialects[values[i]], id; (id = this.data[j++]) != 65535 /* Seq.End */;)\n                    (disabled || (disabled = new Uint8Array(this.maxTerm + 1)))[id] = 1;\n            }\n        return new Dialect(dialect, flags, disabled);\n    }\n    /**\n    Used by the output of the parser generator. Not available to\n    user code. @hide\n    */\n    static deserialize(spec) {\n        return new LRParser(spec);\n    }\n}\nfunction pair(data, off) { return data[off] | (data[off + 1] << 16); }\nfunction findFinished(stacks) {\n    let best = null;\n    for (let stack of stacks) {\n        let stopped = stack.p.stoppedAt;\n        if ((stack.pos == stack.p.stream.end || stopped != null && stack.pos > stopped) &&\n            stack.p.parser.stateFlag(stack.state, 2 /* StateFlag.Accepting */) &&\n            (!best || best.score < stack.score))\n            best = stack;\n    }\n    return best;\n}\nfunction getSpecializer(spec) {\n    if (spec.external) {\n        let mask = spec.extend ? 1 /* Specialize.Extend */ : 0 /* Specialize.Specialize */;\n        return (value, stack) => (spec.external(value, stack) << 1) | mask;\n    }\n    return spec.get;\n}\n\nexport { ContextTracker, ExternalTokenizer, InputStream, LRParser, LocalTokenGroup, Stack };\n"], "names": ["<PERSON><PERSON>", "p", "stack", "state", "reducePos", "pos", "score", "buffer", "bufferBase", "cur<PERSON><PERSON><PERSON><PERSON>", "lookAhead", "parent", "_", "i", "cx", "StackContext", "start", "action", "_a", "depth", "type", "parser", "dPrec", "base", "size", "count", "baseStateID", "term", "end", "isReduce", "cur", "top", "index", "nextState", "next", "nextStart", "nextEnd", "value", "off", "isNode", "sim", "SimulatedStack", "nextStates", "best", "s", "v", "result", "reduce", "target", "backup", "seen", "explore", "r<PERSON><PERSON><PERSON>", "found", "other", "dialectID", "last", "context", "newCx", "tracker", "goto", "StackBufferCursor", "decodeArray", "input", "Type", "array", "out", "stop", "digit", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nullToken", "InputStream", "ranges", "offset", "assoc", "range", "idx", "resolved", "token", "endOffset", "chunk", "chunkPos", "nextChunk", "n", "from", "to", "r", "TokenGroup", "data", "id", "readToken", "LocalTokenGroup", "precTable", "elseToken", "skipped", "atEof", "nextPos", "ExternalTokenizer", "options", "group", "precOffset", "groupMask", "dialect", "scan", "accEnd", "overrides", "low", "high", "mid", "findOffset", "prev", "tableData", "tableOffset", "iPrev", "verbose", "stackIDs", "cutAt", "tree", "side", "cursor", "IterMode", "FragmentCursor", "fragments", "nodeSet", "fr", "Tree", "NodeProp", "TokenCache", "stream", "actionIndex", "main", "tokenizers", "mask", "tokenizer", "startIndex", "set", "pair", "Parse", "stacks", "newStacks", "stopped", "stoppedTokens", "tok", "finished", "findFinished", "maxRemaining", "a", "b", "outer", "j", "split", "strictCx", "cxHash", "cached", "match", "inner", "defaultReduce", "actions", "localStack", "pushStackDedup", "tokens", "restarted", "tokenEnd", "force", "forceBase", "insert", "Dialect", "source", "flags", "disabled", "x", "ContextTracker", "spec", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "nodeNames", "topTerms", "nodeProps", "setProp", "nodeID", "prop", "propSpec", "NodeSet", "name", "NodeType", "De<PERSON>ult<PERSON><PERSON>er<PERSON><PERSON><PERSON>", "tokenArray", "getSpecializer", "parse", "w", "loose", "table", "groupTag", "terminal", "slot", "flag", "deflt", "config", "copy", "info", "t", "prec", "values", "part"], "mappings": "kFAQA,MAAMA,CAAM,CAIR,YAIAC,EAKAC,EAIAC,EAQAC,EAIAC,EAMAC,EAOAC,EASAC,EAIAC,EAIAC,EAAY,EAQZC,EAAQ,CACJ,KAAK,EAAIV,EACT,KAAK,MAAQC,EACb,KAAK,MAAQC,EACb,KAAK,UAAYC,EACjB,KAAK,IAAMC,EACX,KAAK,MAAQC,EACb,KAAK,OAASC,EACd,KAAK,WAAaC,EAClB,KAAK,WAAaC,EAClB,KAAK,UAAYC,EACjB,KAAK,OAASC,CACjB,CAID,UAAW,CACP,MAAO,IAAI,KAAK,MAAM,OAAO,CAACC,EAAGC,IAAMA,EAAI,GAAK,CAAC,EAAE,OAAO,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,MAAQ,IAAM,KAAK,MAAQ,IACxH,CAKD,OAAO,MAAMZ,EAAGE,EAAOE,EAAM,EAAG,CAC5B,IAAIS,EAAKb,EAAE,OAAO,QAClB,OAAO,IAAID,EAAMC,EAAG,CAAE,EAAEE,EAAOE,EAAKA,EAAK,EAAG,GAAI,EAAGS,EAAK,IAAIC,EAAaD,EAAIA,EAAG,KAAK,EAAI,KAAM,EAAG,IAAI,CACzG,CAOD,IAAI,SAAU,CAAE,OAAO,KAAK,WAAa,KAAK,WAAW,QAAU,IAAO,CAM1E,UAAUX,EAAOa,EAAO,CACpB,KAAK,MAAM,KAAK,KAAK,MAAOA,EAAO,KAAK,WAAa,KAAK,OAAO,MAAM,EACvE,KAAK,MAAQb,CAChB,CAKD,OAAOc,EAAQ,CACX,IAAIC,EACJ,IAAIC,EAAQF,GAAU,GAAkCG,EAAOH,EAAS,MACpE,CAAE,OAAAI,CAAM,EAAK,KAAK,EAClBC,EAAQD,EAAO,kBAAkBD,CAAI,EAGzC,GAFIE,IACA,KAAK,OAASA,GACdH,GAAS,EAAG,CACZ,KAAK,UAAUE,EAAO,QAAQ,KAAK,MAAOD,EAAM,EAAI,EAAG,KAAK,SAAS,EAGjEA,EAAOC,EAAO,eACd,KAAK,UAAUD,EAAM,KAAK,UAAW,KAAK,UAAW,EAAG,EAAI,EAChE,KAAK,cAAcA,EAAM,KAAK,SAAS,EACvC,OAOJ,IAAIG,EAAO,KAAK,MAAM,QAAWJ,EAAQ,GAAK,GAAMF,EAAS,OAA+B,EAAI,GAC5FD,EAAQO,EAAO,KAAK,MAAMA,EAAO,CAAC,EAAI,KAAK,EAAE,OAAO,CAAC,EAAE,KAAMC,EAAO,KAAK,UAAYR,EAIrFQ,GAAQ,KAAsC,EAAG,GAAAN,EAAK,KAAK,EAAE,OAAO,QAAQ,MAAME,CAAI,KAAO,MAAQF,IAAO,SAAkBA,EAAG,eAC7HF,GAAS,KAAK,EAAE,uBAChB,KAAK,EAAE,oBACP,KAAK,EAAE,qBAAuBQ,GAEzB,KAAK,EAAE,qBAAuBA,IACnC,KAAK,EAAE,kBAAoB,EAC3B,KAAK,EAAE,sBAAwBR,EAC/B,KAAK,EAAE,qBAAuBQ,IAGtC,IAAIhB,EAAae,EAAO,KAAK,MAAMA,EAAO,CAAC,EAAI,EAAGE,EAAQ,KAAK,WAAa,KAAK,OAAO,OAASjB,EAEjG,GAAIY,EAAOC,EAAO,eAAkBJ,EAAS,OAAiC,CAC1E,IAAIZ,EAAMgB,EAAO,UAAU,KAAK,MAAO,CAAC,EAA4B,KAAK,IAAM,KAAK,UACpF,KAAK,UAAUD,EAAMJ,EAAOX,EAAKoB,EAAQ,EAAG,EAAI,EAEpD,GAAIR,EAAS,OACT,KAAK,MAAQ,KAAK,MAAMM,CAAI,MAE3B,CACD,IAAIG,EAAc,KAAK,MAAMH,EAAO,CAAC,EACrC,KAAK,MAAQF,EAAO,QAAQK,EAAaN,EAAM,EAAI,EAEvD,KAAO,KAAK,MAAM,OAASG,GACvB,KAAK,MAAM,MACf,KAAK,cAAcH,EAAMJ,CAAK,CACjC,CAKD,UAAUW,EAAMX,EAAOY,EAAKJ,EAAO,EAAGK,EAAW,GAAO,CACpD,GAAIF,GAAQ,IACP,CAAC,KAAK,MAAM,QAAU,KAAK,MAAM,KAAK,MAAM,OAAS,CAAC,EAAI,KAAK,OAAO,OAAS,KAAK,YAAa,CAElG,IAAIG,EAAM,KAAMC,EAAM,KAAK,OAAO,OAKlC,GAJIA,GAAO,GAAKD,EAAI,SAChBC,EAAMD,EAAI,WAAaA,EAAI,OAAO,WAClCA,EAAMA,EAAI,QAEVC,EAAM,GAAKD,EAAI,OAAOC,EAAM,CAAC,GAAK,GAAoBD,EAAI,OAAOC,EAAM,CAAC,EAAI,GAAI,CAChF,GAAIf,GAASY,EACT,OACJ,GAAIE,EAAI,OAAOC,EAAM,CAAC,GAAKf,EAAO,CAC9Bc,EAAI,OAAOC,EAAM,CAAC,EAAIH,EACtB,SAIZ,GAAI,CAACC,GAAY,KAAK,KAAOD,EACzB,KAAK,OAAO,KAAKD,EAAMX,EAAOY,EAAKJ,CAAI,MAEtC,CACD,IAAIQ,EAAQ,KAAK,OAAO,OACxB,GAAIA,EAAQ,GAAK,KAAK,OAAOA,EAAQ,CAAC,GAAK,EACvC,KAAOA,EAAQ,GAAK,KAAK,OAAOA,EAAQ,CAAC,EAAIJ,GAEzC,KAAK,OAAOI,CAAK,EAAI,KAAK,OAAOA,EAAQ,CAAC,EAC1C,KAAK,OAAOA,EAAQ,CAAC,EAAI,KAAK,OAAOA,EAAQ,CAAC,EAC9C,KAAK,OAAOA,EAAQ,CAAC,EAAI,KAAK,OAAOA,EAAQ,CAAC,EAC9C,KAAK,OAAOA,EAAQ,CAAC,EAAI,KAAK,OAAOA,EAAQ,CAAC,EAC9CA,GAAS,EACLR,EAAO,IACPA,GAAQ,GAEpB,KAAK,OAAOQ,CAAK,EAAIL,EACrB,KAAK,OAAOK,EAAQ,CAAC,EAAIhB,EACzB,KAAK,OAAOgB,EAAQ,CAAC,EAAIJ,EACzB,KAAK,OAAOI,EAAQ,CAAC,EAAIR,EAEhC,CAKD,MAAMP,EAAQG,EAAMJ,EAAOY,EAAK,CAC5B,GAAIX,EAAS,OACT,KAAK,UAAUA,EAAS,MAA8B,KAAK,GAAG,UAExDA,EAAS,OAaf,KAAK,IAAMW,EACX,KAAK,aAAaR,EAAMJ,CAAK,EACzBI,GAAQ,KAAK,EAAE,OAAO,SACtB,KAAK,OAAO,KAAKA,EAAMJ,EAAOY,EAAK,CAAC,MAhBW,CACnD,IAAIK,EAAYhB,EAAQ,CAAE,OAAAI,CAAM,EAAK,KAAK,GACtCO,EAAM,KAAK,KAAOR,GAAQC,EAAO,WACjC,KAAK,IAAMO,EACNP,EAAO,UAAUY,EAAW,CAA0B,IACvD,KAAK,UAAYL,IAEzB,KAAK,UAAUK,EAAWjB,CAAK,EAC/B,KAAK,aAAaI,EAAMJ,CAAK,EACzBI,GAAQC,EAAO,SACf,KAAK,OAAO,KAAKD,EAAMJ,EAAOY,EAAK,CAAC,EAQ/C,CAKD,MAAMX,EAAQiB,EAAMC,EAAWC,EAAS,CAChCnB,EAAS,MACT,KAAK,OAAOA,CAAM,EAElB,KAAK,MAAMA,EAAQiB,EAAMC,EAAWC,CAAO,CAClD,CAKD,QAAQC,EAAOH,EAAM,CACjB,IAAIF,EAAQ,KAAK,EAAE,OAAO,OAAS,GAC/BA,EAAQ,GAAK,KAAK,EAAE,OAAOA,CAAK,GAAKK,KACrC,KAAK,EAAE,OAAO,KAAKA,CAAK,EACxBL,KAEJ,IAAIhB,EAAQ,KAAK,IACjB,KAAK,UAAY,KAAK,IAAMA,EAAQqB,EAAM,OAC1C,KAAK,UAAUH,EAAMlB,CAAK,EAC1B,KAAK,OAAO,KAAKgB,EAAOhB,EAAO,KAAK,UAAW,IAC3C,KAAK,YACL,KAAK,cAAc,KAAK,WAAW,QAAQ,MAAM,KAAK,WAAW,QAASqB,EAAO,KAAM,KAAK,EAAE,OAAO,MAAM,KAAK,IAAMA,EAAM,MAAM,CAAC,CAAC,CAC3I,CAOD,OAAQ,CACJ,IAAI1B,EAAS,KACT2B,EAAM3B,EAAO,OAAO,OAKxB,KAAO2B,EAAM,GAAK3B,EAAO,OAAO2B,EAAM,CAAC,EAAI3B,EAAO,WAC9C2B,GAAO,EACX,IAAI/B,EAASI,EAAO,OAAO,MAAM2B,CAAG,EAAGf,EAAOZ,EAAO,WAAa2B,EAElE,KAAO3B,GAAUY,GAAQZ,EAAO,YAC5BA,EAASA,EAAO,OACpB,OAAO,IAAIX,EAAM,KAAK,EAAG,KAAK,MAAM,QAAS,KAAK,MAAO,KAAK,UAAW,KAAK,IAAK,KAAK,MAAOO,EAAQgB,EAAM,KAAK,WAAY,KAAK,UAAWZ,CAAM,CACvJ,CAKD,gBAAgBuB,EAAME,EAAS,CAC3B,IAAIG,EAASL,GAAQ,KAAK,EAAE,OAAO,QAC/BK,GACA,KAAK,UAAUL,EAAM,KAAK,IAAKE,EAAS,CAAC,EAC7C,KAAK,UAAU,EAAkB,KAAK,IAAKA,EAASG,EAAS,EAAI,CAAC,EAClE,KAAK,IAAM,KAAK,UAAYH,EAC5B,KAAK,OAAS,GACjB,CAOD,SAAST,EAAM,CACX,QAASa,EAAM,IAAIC,EAAe,IAAI,IAAK,CACvC,IAAIxB,EAAS,KAAK,EAAE,OAAO,UAAUuB,EAAI,MAAO,CAAiC,GAAI,KAAK,EAAE,OAAO,UAAUA,EAAI,MAAOb,CAAI,EAC5H,GAAIV,GAAU,EACV,MAAO,GACX,GAAK,EAAAA,EAAS,OACV,MAAO,GACXuB,EAAI,OAAOvB,CAAM,EAExB,CAMD,gBAAgBiB,EAAM,CAClB,GAAI,KAAK,MAAM,QAAU,IACrB,MAAO,GACX,IAAIQ,EAAa,KAAK,EAAE,OAAO,WAAW,KAAK,KAAK,EACpD,GAAIA,EAAW,OAAS,GAAgC,KAAK,MAAM,QAAU,IAA0C,CACnH,IAAIC,EAAO,CAAA,EACX,QAAS9B,EAAI,EAAG+B,EAAG/B,EAAI6B,EAAW,OAAQ7B,GAAK,GACtC+B,EAAIF,EAAW7B,EAAI,CAAC,IAAM,KAAK,OAAS,KAAK,EAAE,OAAO,UAAU+B,EAAGV,CAAI,GACxES,EAAK,KAAKD,EAAW7B,CAAC,EAAG+B,CAAC,EAElC,GAAI,KAAK,MAAM,OAAS,IACpB,QAAS/B,EAAI,EAAG8B,EAAK,OAAS,GAAgC9B,EAAI6B,EAAW,OAAQ7B,GAAK,EAAG,CACzF,IAAI+B,EAAIF,EAAW7B,EAAI,CAAC,EACnB8B,EAAK,KAAK,CAACE,EAAGhC,IAAOA,EAAI,GAAMgC,GAAKD,CAAC,GACtCD,EAAK,KAAKD,EAAW7B,CAAC,EAAG+B,CAAC,EAEtCF,EAAaC,EAEjB,IAAIG,EAAS,CAAA,EACb,QAAS,EAAI,EAAG,EAAIJ,EAAW,QAAUI,EAAO,OAAS,EAAyB,GAAK,EAAG,CACtF,IAAIF,EAAIF,EAAW,EAAI,CAAC,EACxB,GAAIE,GAAK,KAAK,MACV,SACJ,IAAI1C,EAAQ,KAAK,QACjBA,EAAM,UAAU0C,EAAG,KAAK,GAAG,EAC3B1C,EAAM,UAAU,EAAkBA,EAAM,IAAKA,EAAM,IAAK,EAAG,EAAI,EAC/DA,EAAM,aAAawC,EAAW,CAAC,EAAG,KAAK,GAAG,EAC1CxC,EAAM,UAAY,KAAK,IACvBA,EAAM,OAAS,IACf4C,EAAO,KAAK5C,CAAK,EAErB,OAAO4C,CACV,CAMD,aAAc,CACV,GAAI,CAAE,OAAAzB,CAAM,EAAK,KAAK,EAClB0B,EAAS1B,EAAO,UAAU,KAAK,MAAO,GAC1C,GAAK,EAAA0B,EAAS,OACV,MAAO,GACX,GAAI,CAAC1B,EAAO,YAAY,KAAK,MAAO0B,CAAM,EAAG,CACzC,IAAI5B,EAAQ4B,GAAU,GAAkCpB,EAAOoB,EAAS,MACpEC,EAAS,KAAK,MAAM,OAAS7B,EAAQ,EACzC,GAAI6B,EAAS,GAAK3B,EAAO,QAAQ,KAAK,MAAM2B,CAAM,EAAGrB,EAAM,EAAK,EAAI,EAAG,CACnE,IAAIsB,EAAS,KAAK,sBAClB,GAAIA,GAAU,KACV,MAAO,GACXF,EAASE,EAEb,KAAK,UAAU,EAAkB,KAAK,IAAK,KAAK,IAAK,EAAG,EAAI,EAC5D,KAAK,OAAS,IAElB,YAAK,UAAY,KAAK,IACtB,KAAK,OAAOF,CAAM,EACX,EACV,CAMD,qBAAsB,CAClB,GAAI,CAAE,OAAA1B,CAAM,EAAK,KAAK,EAAG6B,EAAO,CAAA,EAC5BC,EAAU,CAAChD,EAAOgB,IAAU,CAC5B,GAAI,CAAA+B,EAAK,SAAS/C,CAAK,EAEvB,OAAA+C,EAAK,KAAK/C,CAAK,EACRkB,EAAO,WAAWlB,EAAQc,GAAW,CACxC,GAAI,EAAAA,EAAU,QACT,GAAIA,EAAS,MAA+B,CAC7C,IAAImC,GAAUnC,GAAU,IAAoCE,EAC5D,GAAIiC,EAAS,EAAG,CACZ,IAAIzB,EAAOV,EAAS,MAA8B+B,EAAS,KAAK,MAAM,OAASI,EAAS,EACxF,GAAIJ,GAAU,GAAK3B,EAAO,QAAQ,KAAK,MAAM2B,CAAM,EAAGrB,EAAM,EAAK,GAAK,EAClE,OAAQyB,GAAU,GAAoC,MAAgCzB,OAG7F,CACD,IAAI0B,EAAQF,EAAQlC,EAAQE,EAAQ,CAAC,EACrC,GAAIkC,GAAS,KACT,OAAOA,EAE/B,CAAa,CACb,EACQ,OAAOF,EAAQ,KAAK,MAAO,CAAC,CAC/B,CAID,UAAW,CACP,KAAO,CAAC,KAAK,EAAE,OAAO,UAAU,KAAK,MAAO,IACxC,GAAI,CAAC,KAAK,cAAe,CACrB,KAAK,UAAU,EAAkB,KAAK,IAAK,KAAK,IAAK,EAAG,EAAI,EAC5D,MAGR,OAAO,IACV,CAMD,IAAI,SAAU,CACV,GAAI,KAAK,MAAM,QAAU,EACrB,MAAO,GACX,GAAI,CAAE,OAAA9B,CAAM,EAAK,KAAK,EACtB,OAAOA,EAAO,KAAKA,EAAO,UAAU,KAAK,MAAO,CAA2B,CAAA,GAAK,OAC5E,CAACA,EAAO,UAAU,KAAK,MAAO,CAAC,CACtC,CAMD,SAAU,CACN,KAAK,UAAU,EAAkB,KAAK,IAAK,KAAK,IAAK,EAAG,EAAI,EAC5D,KAAK,MAAQ,KAAK,MAAM,CAAC,EACzB,KAAK,MAAM,OAAS,CACvB,CAID,UAAUiC,EAAO,CACb,GAAI,KAAK,OAASA,EAAM,OAAS,KAAK,MAAM,QAAUA,EAAM,MAAM,OAC9D,MAAO,GACX,QAASzC,EAAI,EAAGA,EAAI,KAAK,MAAM,OAAQA,GAAK,EACxC,GAAI,KAAK,MAAMA,CAAC,GAAKyC,EAAM,MAAMzC,CAAC,EAC9B,MAAO,GACf,MAAO,EACV,CAID,IAAI,QAAS,CAAE,OAAO,KAAK,EAAE,MAAS,CAKtC,eAAe0C,EAAW,CAAE,OAAO,KAAK,EAAE,OAAO,QAAQ,MAAMA,CAAS,CAAI,CAC5E,aAAa5B,EAAMX,EAAO,CAClB,KAAK,YACL,KAAK,cAAc,KAAK,WAAW,QAAQ,MAAM,KAAK,WAAW,QAASW,EAAM,KAAM,KAAK,EAAE,OAAO,MAAMX,CAAK,CAAC,CAAC,CACxH,CACD,cAAcW,EAAMX,EAAO,CACnB,KAAK,YACL,KAAK,cAAc,KAAK,WAAW,QAAQ,OAAO,KAAK,WAAW,QAASW,EAAM,KAAM,KAAK,EAAE,OAAO,MAAMX,CAAK,CAAC,CAAC,CACzH,CAID,aAAc,CACV,IAAIwC,EAAO,KAAK,OAAO,OAAS,GAC5BA,EAAO,GAAK,KAAK,OAAOA,CAAI,GAAK,KACjC,KAAK,OAAO,KAAK,KAAK,WAAW,KAAM,KAAK,IAAK,KAAK,IAAK,EAAE,CACpE,CAID,eAAgB,CACZ,IAAIA,EAAO,KAAK,OAAO,OAAS,GAC5BA,EAAO,GAAK,KAAK,OAAOA,CAAI,GAAK,KACjC,KAAK,OAAO,KAAK,KAAK,UAAW,KAAK,IAAK,KAAK,IAAK,EAAE,CAC9D,CACD,cAAcC,EAAS,CACnB,GAAIA,GAAW,KAAK,WAAW,QAAS,CACpC,IAAIC,EAAQ,IAAI3C,EAAa,KAAK,WAAW,QAAS0C,CAAO,EACzDC,EAAM,MAAQ,KAAK,WAAW,MAC9B,KAAK,YAAW,EACpB,KAAK,WAAaA,EAEzB,CAID,aAAahD,EAAW,CAChBA,EAAY,KAAK,YACjB,KAAK,cAAa,EAClB,KAAK,UAAYA,EAExB,CAID,OAAQ,CACA,KAAK,YAAc,KAAK,WAAW,QAAQ,QAC3C,KAAK,YAAW,EAChB,KAAK,UAAY,GACjB,KAAK,cAAa,CACzB,CACL,CACA,MAAMK,CAAa,CACf,YAAY4C,EAASF,EAAS,CAC1B,KAAK,QAAUE,EACf,KAAK,QAAUF,EACf,KAAK,KAAOE,EAAQ,OAASA,EAAQ,KAAKF,CAAO,EAAI,CACxD,CACL,CAGA,MAAMhB,CAAe,CACjB,YAAYzB,EAAO,CACf,KAAK,MAAQA,EACb,KAAK,MAAQA,EAAM,MACnB,KAAK,MAAQA,EAAM,MACnB,KAAK,KAAO,KAAK,MAAM,MAC1B,CACD,OAAOC,EAAQ,CACX,IAAIU,EAAOV,EAAS,MAA8BE,EAAQF,GAAU,GAChEE,GAAS,GACL,KAAK,OAAS,KAAK,MAAM,QACzB,KAAK,MAAQ,KAAK,MAAM,MAAK,GACjC,KAAK,MAAM,KAAK,KAAK,MAAO,EAAG,CAAC,EAChC,KAAK,MAAQ,GAGb,KAAK,OAASA,EAAQ,GAAK,EAE/B,IAAIyC,EAAO,KAAK,MAAM,EAAE,OAAO,QAAQ,KAAK,MAAM,KAAK,KAAO,CAAC,EAAGjC,EAAM,EAAI,EAC5E,KAAK,MAAQiC,CAChB,CACL,CAGA,MAAMC,CAAkB,CACpB,YAAY3D,EAAOG,EAAK2B,EAAO,CAC3B,KAAK,MAAQ9B,EACb,KAAK,IAAMG,EACX,KAAK,MAAQ2B,EACb,KAAK,OAAS9B,EAAM,OAChB,KAAK,OAAS,GACd,KAAK,UAAS,CACrB,CACD,OAAO,OAAOA,EAAOG,EAAMH,EAAM,WAAaA,EAAM,OAAO,OAAQ,CAC/D,OAAO,IAAI2D,EAAkB3D,EAAOG,EAAKA,EAAMH,EAAM,UAAU,CAClE,CACD,WAAY,CACR,IAAIgC,EAAO,KAAK,MAAM,OAClBA,GAAQ,OACR,KAAK,MAAQ,KAAK,MAAM,WAAaA,EAAK,WAC1C,KAAK,MAAQA,EACb,KAAK,OAASA,EAAK,OAE1B,CACD,IAAI,IAAK,CAAE,OAAO,KAAK,OAAO,KAAK,MAAQ,CAAC,CAAI,CAChD,IAAI,OAAQ,CAAE,OAAO,KAAK,OAAO,KAAK,MAAQ,CAAC,CAAI,CACnD,IAAI,KAAM,CAAE,OAAO,KAAK,OAAO,KAAK,MAAQ,CAAC,CAAI,CACjD,IAAI,MAAO,CAAE,OAAO,KAAK,OAAO,KAAK,MAAQ,CAAC,CAAI,CAClD,MAAO,CACH,KAAK,OAAS,EACd,KAAK,KAAO,EACR,KAAK,OAAS,GACd,KAAK,UAAS,CACrB,CACD,MAAO,CACH,OAAO,IAAI2B,EAAkB,KAAK,MAAO,KAAK,IAAK,KAAK,KAAK,CAChE,CACL,CAIA,SAASC,EAAYC,EAAOC,EAAO,YAAa,CAC5C,GAAI,OAAOD,GAAS,SAChB,OAAOA,EACX,IAAIE,EAAQ,KACZ,QAAS5D,EAAM,EAAG6D,EAAM,EAAG7D,EAAM0D,EAAM,QAAS,CAC5C,IAAI1B,EAAQ,EACZ,OAAS,CACL,IAAIH,EAAO6B,EAAM,WAAW1D,GAAK,EAAG8D,EAAO,GAC3C,GAAIjC,GAAQ,IAA6B,CACrCG,EAAQ,MACR,MAEAH,GAAQ,IACRA,IACAA,GAAQ,IACRA,IACJ,IAAIkC,EAAQlC,EAAO,GAMnB,GALIkC,GAAS,KACTA,GAAS,GACTD,EAAO,IAEX9B,GAAS+B,EACLD,EACA,MACJ9B,GAAS,GAET4B,EACAA,EAAMC,GAAK,EAAI7B,EAEf4B,EAAQ,IAAID,EAAK3B,CAAK,EAE9B,OAAO4B,CACX,CAEA,MAAMI,CAAY,CACd,aAAc,CACV,KAAK,MAAQ,GACb,KAAK,MAAQ,GACb,KAAK,IAAM,GACX,KAAK,SAAW,GAChB,KAAK,UAAY,EACjB,KAAK,KAAO,EACZ,KAAK,QAAU,CAClB,CACL,CACA,MAAMC,EAAY,IAAID,EAOtB,MAAME,CAAY,CAId,YAIAR,EAIAS,EAAQ,CACJ,KAAK,MAAQT,EACb,KAAK,OAASS,EAId,KAAK,MAAQ,GAIb,KAAK,SAAW,EAIhB,KAAK,OAAS,GACd,KAAK,UAAY,EAKjB,KAAK,KAAO,GAIZ,KAAK,MAAQF,EACb,KAAK,WAAa,EAClB,KAAK,IAAM,KAAK,SAAWE,EAAO,CAAC,EAAE,KACrC,KAAK,MAAQA,EAAO,CAAC,EACrB,KAAK,IAAMA,EAAOA,EAAO,OAAS,CAAC,EAAE,GACrC,KAAK,SAAQ,CAChB,CAID,cAAcC,EAAQC,EAAO,CACzB,IAAIC,EAAQ,KAAK,MAAO3C,EAAQ,KAAK,WACjC3B,EAAM,KAAK,IAAMoE,EACrB,KAAOpE,EAAMsE,EAAM,MAAM,CACrB,GAAI,CAAC3C,EACD,OAAO,KACX,IAAIE,EAAO,KAAK,OAAO,EAAEF,CAAK,EAC9B3B,GAAOsE,EAAM,KAAOzC,EAAK,GACzByC,EAAQzC,EAEZ,KAAOwC,EAAQ,EAAIrE,EAAMsE,EAAM,GAAKtE,GAAOsE,EAAM,IAAI,CACjD,GAAI3C,GAAS,KAAK,OAAO,OAAS,EAC9B,OAAO,KACX,IAAIE,EAAO,KAAK,OAAO,EAAEF,CAAK,EAC9B3B,GAAO6B,EAAK,KAAOyC,EAAM,GACzBA,EAAQzC,EAEZ,OAAO7B,CACV,CAID,QAAQA,EAAK,CACT,GAAIA,GAAO,KAAK,MAAM,MAAQA,EAAM,KAAK,MAAM,GAC3C,OAAOA,EACX,QAASsE,KAAS,KAAK,OACnB,GAAIA,EAAM,GAAKtE,EACX,OAAO,KAAK,IAAIA,EAAKsE,EAAM,IAAI,EACvC,OAAO,KAAK,GACf,CAYD,KAAKF,EAAQ,CACT,IAAIG,EAAM,KAAK,SAAWH,EAAQpE,EAAKyC,EACvC,GAAI8B,GAAO,GAAKA,EAAM,KAAK,MAAM,OAC7BvE,EAAM,KAAK,IAAMoE,EACjB3B,EAAS,KAAK,MAAM,WAAW8B,CAAG,MAEjC,CACD,IAAIC,EAAW,KAAK,cAAcJ,EAAQ,CAAC,EAC3C,GAAII,GAAY,KACZ,MAAO,GAEX,GADAxE,EAAMwE,EACFxE,GAAO,KAAK,WAAaA,EAAM,KAAK,UAAY,KAAK,OAAO,OAC5DyC,EAAS,KAAK,OAAO,WAAWzC,EAAM,KAAK,SAAS,MAEnD,CACD,IAAIQ,EAAI,KAAK,WAAY8D,EAAQ,KAAK,MACtC,KAAOA,EAAM,IAAMtE,GACfsE,EAAQ,KAAK,OAAO,EAAE9D,CAAC,EAC3B,KAAK,OAAS,KAAK,MAAM,MAAM,KAAK,UAAYR,CAAG,EAC/CA,EAAM,KAAK,OAAO,OAASsE,EAAM,KACjC,KAAK,OAAS,KAAK,OAAO,MAAM,EAAGA,EAAM,GAAKtE,CAAG,GACrDyC,EAAS,KAAK,OAAO,WAAW,CAAC,GAGzC,OAAIzC,GAAO,KAAK,MAAM,YAClB,KAAK,MAAM,UAAYA,EAAM,GAC1ByC,CACV,CAMD,YAAYgC,EAAOC,EAAY,EAAG,CAC9B,IAAInD,EAAMmD,EAAY,KAAK,cAAcA,EAAW,EAAE,EAAI,KAAK,IAC/D,GAAInD,GAAO,MAAQA,EAAM,KAAK,MAAM,MAChC,MAAM,IAAI,WAAW,yBAAyB,EAClD,KAAK,MAAM,MAAQkD,EACnB,KAAK,MAAM,IAAMlD,CACpB,CACD,UAAW,CACP,GAAI,KAAK,KAAO,KAAK,WAAa,KAAK,IAAM,KAAK,UAAY,KAAK,OAAO,OAAQ,CAC9E,GAAI,CAAE,MAAAoD,EAAO,SAAAC,CAAU,EAAG,KAC1B,KAAK,MAAQ,KAAK,OAClB,KAAK,SAAW,KAAK,UACrB,KAAK,OAASD,EACd,KAAK,UAAYC,EACjB,KAAK,SAAW,KAAK,IAAM,KAAK,aAE/B,CACD,KAAK,OAAS,KAAK,MACnB,KAAK,UAAY,KAAK,SACtB,IAAIC,EAAY,KAAK,MAAM,MAAM,KAAK,GAAG,EACrCtD,EAAM,KAAK,IAAMsD,EAAU,OAC/B,KAAK,MAAQtD,EAAM,KAAK,MAAM,GAAKsD,EAAU,MAAM,EAAG,KAAK,MAAM,GAAK,KAAK,GAAG,EAAIA,EAClF,KAAK,SAAW,KAAK,IACrB,KAAK,SAAW,EAEvB,CACD,UAAW,CACP,OAAI,KAAK,UAAY,KAAK,MAAM,SAC5B,KAAK,SAAQ,EACT,KAAK,UAAY,KAAK,MAAM,QACrB,KAAK,KAAO,GAEpB,KAAK,KAAO,KAAK,MAAM,WAAW,KAAK,QAAQ,CACzD,CAKD,QAAQC,EAAI,EAAG,CAEX,IADA,KAAK,UAAYA,EACV,KAAK,IAAMA,GAAK,KAAK,MAAM,IAAI,CAClC,GAAI,KAAK,YAAc,KAAK,OAAO,OAAS,EACxC,OAAO,KAAK,UAChBA,GAAK,KAAK,MAAM,GAAK,KAAK,IAC1B,KAAK,MAAQ,KAAK,OAAO,EAAE,KAAK,UAAU,EAC1C,KAAK,IAAM,KAAK,MAAM,KAE1B,YAAK,KAAOA,EACR,KAAK,KAAO,KAAK,MAAM,YACvB,KAAK,MAAM,UAAY,KAAK,IAAM,GAC/B,KAAK,UACf,CACD,SAAU,CACN,YAAK,IAAM,KAAK,SAAW,KAAK,IAChC,KAAK,MAAQ,KAAK,OAAO,KAAK,WAAa,KAAK,OAAO,OAAS,CAAC,EACjE,KAAK,MAAQ,GACN,KAAK,KAAO,EACtB,CAID,MAAM9E,EAAKyE,EAAO,CAUd,GATIA,GACA,KAAK,MAAQA,EACbA,EAAM,MAAQzE,EACdyE,EAAM,UAAYzE,EAAM,EACxByE,EAAM,MAAQA,EAAM,SAAW,IAG/B,KAAK,MAAQR,EAEb,KAAK,KAAOjE,EAAK,CAEjB,GADA,KAAK,IAAMA,EACPA,GAAO,KAAK,IACZ,YAAK,QAAO,EACL,KAEX,KAAOA,EAAM,KAAK,MAAM,MACpB,KAAK,MAAQ,KAAK,OAAO,EAAE,KAAK,UAAU,EAC9C,KAAOA,GAAO,KAAK,MAAM,IACrB,KAAK,MAAQ,KAAK,OAAO,EAAE,KAAK,UAAU,EAC1CA,GAAO,KAAK,UAAYA,EAAM,KAAK,SAAW,KAAK,MAAM,OACzD,KAAK,SAAWA,EAAM,KAAK,UAG3B,KAAK,MAAQ,GACb,KAAK,SAAW,GAEpB,KAAK,SAAQ,EAEjB,OAAO,IACV,CAID,KAAK+E,EAAMC,EAAI,CACX,GAAID,GAAQ,KAAK,UAAYC,GAAM,KAAK,SAAW,KAAK,MAAM,OAC1D,OAAO,KAAK,MAAM,MAAMD,EAAO,KAAK,SAAUC,EAAK,KAAK,QAAQ,EACpE,GAAID,GAAQ,KAAK,WAAaC,GAAM,KAAK,UAAY,KAAK,OAAO,OAC7D,OAAO,KAAK,OAAO,MAAMD,EAAO,KAAK,UAAWC,EAAK,KAAK,SAAS,EACvE,GAAID,GAAQ,KAAK,MAAM,MAAQC,GAAM,KAAK,MAAM,GAC5C,OAAO,KAAK,MAAM,KAAKD,EAAMC,CAAE,EACnC,IAAIvC,EAAS,GACb,QAASwC,KAAK,KAAK,OAAQ,CACvB,GAAIA,EAAE,MAAQD,EACV,MACAC,EAAE,GAAKF,IACPtC,GAAU,KAAK,MAAM,KAAK,KAAK,IAAIwC,EAAE,KAAMF,CAAI,EAAG,KAAK,IAAIE,EAAE,GAAID,CAAE,CAAC,GAE5E,OAAOvC,CACV,CACL,CAIA,MAAMyC,CAAW,CACb,YAAYC,EAAMC,EAAI,CAClB,KAAK,KAAOD,EACZ,KAAK,GAAKC,CACb,CACD,MAAM1B,EAAO7D,EAAO,CAChB,GAAI,CAAE,OAAAmB,CAAM,EAAKnB,EAAM,EACvBwF,EAAU,KAAK,KAAM3B,EAAO7D,EAAO,KAAK,GAAImB,EAAO,KAAMA,EAAO,cAAc,CACjF,CACL,CACAkE,EAAW,UAAU,WAAaA,EAAW,UAAU,SAAWA,EAAW,UAAU,OAAS,GAIhG,MAAMI,CAAgB,CAClB,YAAYH,EAAMI,EAAWC,EAAW,CACpC,KAAK,UAAYD,EACjB,KAAK,UAAYC,EACjB,KAAK,KAAO,OAAOL,GAAQ,SAAW1B,EAAY0B,CAAI,EAAIA,CAC7D,CACD,MAAMzB,EAAO7D,EAAO,CAChB,IAAIc,EAAQ+C,EAAM,IAAK+B,EAAU,EACjC,OAAS,CACL,IAAIC,EAAQhC,EAAM,KAAO,EAAGiC,EAAUjC,EAAM,cAAc,EAAG,CAAC,EAE9D,GADA2B,EAAU,KAAK,KAAM3B,EAAO7D,EAAO,EAAG,KAAK,KAAM,KAAK,SAAS,EAC3D6D,EAAM,MAAM,MAAQ,GACpB,MACJ,GAAI,KAAK,WAAa,KAClB,OAGJ,GAFKgC,GACDD,IACAE,GAAW,KACX,MACJjC,EAAM,MAAMiC,EAASjC,EAAM,KAAK,EAEhC+B,IACA/B,EAAM,MAAM/C,EAAO+C,EAAM,KAAK,EAC9BA,EAAM,YAAY,KAAK,UAAW+B,CAAO,EAEhD,CACL,CACAH,EAAgB,UAAU,WAAaJ,EAAW,UAAU,SAAWA,EAAW,UAAU,OAAS,GAKrG,MAAMU,CAAkB,CAQpB,YAIAnB,EAAOoB,EAAU,GAAI,CACjB,KAAK,MAAQpB,EACb,KAAK,WAAa,CAAC,CAACoB,EAAQ,WAC5B,KAAK,SAAW,CAAC,CAACA,EAAQ,SAC1B,KAAK,OAAS,CAAC,CAACA,EAAQ,MAC3B,CACL,CAqBA,SAASR,EAAUF,EAAMzB,EAAO7D,EAAOiG,EAAOP,EAAWQ,EAAY,CACjE,IAAIjG,EAAQ,EAAGkG,EAAY,GAAKF,EAAO,CAAE,QAAAG,GAAYpG,EAAM,EAAE,OAC7DqG,EAAM,KACGF,EAAYb,EAAKrF,CAAK,GADhB,CAGX,IAAIqG,EAAShB,EAAKrF,EAAQ,CAAC,EAI3B,QAASU,EAAIV,EAAQ,EAAGU,EAAI2F,EAAQ3F,GAAK,EACrC,IAAK2E,EAAK3E,EAAI,CAAC,EAAIwF,GAAa,EAAG,CAC/B,IAAI1E,EAAO6D,EAAK3E,CAAC,EACjB,GAAIyF,EAAQ,OAAO3E,CAAI,IAClBoC,EAAM,MAAM,OAAS,IAAMA,EAAM,MAAM,OAASpC,GAC7C8E,EAAU9E,EAAMoC,EAAM,MAAM,MAAO6B,EAAWQ,CAAU,GAAI,CAChErC,EAAM,YAAYpC,CAAI,EACtB,OAGZ,IAAIO,EAAO6B,EAAM,KAAM2C,EAAM,EAAGC,EAAOnB,EAAKrF,EAAQ,CAAC,EAErD,GAAI4D,EAAM,KAAO,GAAK4C,EAAOD,GAAOlB,EAAKgB,EAASG,EAAO,EAAI,CAAC,GAAK,MAAqB,CACpFxG,EAAQqF,EAAKgB,EAASG,EAAO,EAAI,CAAC,EAClC,SAASJ,EAGb,KAAOG,EAAMC,GAAO,CAChB,IAAIC,EAAOF,EAAMC,GAAS,EACtB3E,EAAQwE,EAASI,GAAOA,GAAO,GAC/BxB,EAAOI,EAAKxD,CAAK,EAAGqD,EAAKG,EAAKxD,EAAQ,CAAC,GAAK,MAChD,GAAIE,EAAOkD,EACPuB,EAAOC,UACF1E,GAAQmD,EACbqB,EAAME,EAAM,MACX,CACDzG,EAAQqF,EAAKxD,EAAQ,CAAC,EACtB+B,EAAM,QAAO,EACb,SAASwC,GAGjB,MAER,CACA,SAASM,EAAWrB,EAAMxE,EAAOW,EAAM,CACnC,QAASd,EAAIG,EAAOkB,GAAOA,EAAOsD,EAAK3E,CAAC,IAAM,MAAqBA,IAC/D,GAAIqB,GAAQP,EACR,OAAOd,EAAIG,EACnB,MAAO,EACX,CACA,SAASyF,EAAU3B,EAAOgC,EAAMC,EAAWC,EAAa,CACpD,IAAIC,EAAQJ,EAAWE,EAAWC,EAAaF,CAAI,EACnD,OAAOG,EAAQ,GAAKJ,EAAWE,EAAWC,EAAalC,CAAK,EAAImC,CACpE,CAGA,MAAMC,EAAU,OAAO,QAAW,KAAe,QAAQ,KAAO,YAAY,KAAK,GAAY,GAAG,EAChG,IAAIC,EAAW,KACf,SAASC,EAAMC,EAAMhH,EAAKiH,EAAM,CAC5B,IAAIC,EAASF,EAAK,OAAOG,EAAS,gBAAgB,EAElD,IADAD,EAAO,OAAOlH,CAAG,IAEb,GAAI,EAAEiH,EAAO,EAAIC,EAAO,YAAYlH,CAAG,EAAIkH,EAAO,WAAWlH,CAAG,GAC5D,OAAS,CACL,IAAKiH,EAAO,EAAIC,EAAO,GAAKlH,EAAMkH,EAAO,KAAOlH,IAAQ,CAACkH,EAAO,KAAK,QACjE,OAAOD,EAAO,EAAI,KAAK,IAAI,EAAG,KAAK,IAAIC,EAAO,GAAK,EAAGlH,EAAM,EAAE,CAAqB,EAC7E,KAAK,IAAIgH,EAAK,OAAQ,KAAK,IAAIE,EAAO,KAAO,EAAGlH,EAAM,EAAuB,CAAA,EACvF,GAAIiH,EAAO,EAAIC,EAAO,YAAa,EAAGA,EAAO,YAAa,EACtD,MACJ,GAAI,CAACA,EAAO,OAAQ,EAChB,OAAOD,EAAO,EAAI,EAAID,EAAK,OAG/C,CACA,MAAMI,CAAe,CACjB,YAAYC,EAAWC,EAAS,CAC5B,KAAK,UAAYD,EACjB,KAAK,QAAUC,EACf,KAAK,EAAI,EACT,KAAK,SAAW,KAChB,KAAK,SAAW,GAChB,KAAK,OAAS,GACd,KAAK,MAAQ,GACb,KAAK,MAAQ,GACb,KAAK,MAAQ,GACb,KAAK,aAAY,CACpB,CACD,cAAe,CACX,IAAIC,EAAK,KAAK,SAAW,KAAK,GAAK,KAAK,UAAU,OAAS,KAAO,KAAK,UAAU,KAAK,GAAG,EACzF,GAAIA,EAAI,CAGJ,IAFA,KAAK,SAAWA,EAAG,UAAYR,EAAMQ,EAAG,KAAMA,EAAG,KAAOA,EAAG,OAAQ,CAAC,EAAIA,EAAG,OAASA,EAAG,KACvF,KAAK,OAASA,EAAG,QAAUR,EAAMQ,EAAG,KAAMA,EAAG,GAAKA,EAAG,OAAQ,EAAE,EAAIA,EAAG,OAASA,EAAG,GAC3E,KAAK,MAAM,QACd,KAAK,MAAM,MACX,KAAK,MAAM,MACX,KAAK,MAAM,MAEf,KAAK,MAAM,KAAKA,EAAG,IAAI,EACvB,KAAK,MAAM,KAAK,CAACA,EAAG,MAAM,EAC1B,KAAK,MAAM,KAAK,CAAC,EACjB,KAAK,UAAY,KAAK,cAGtB,KAAK,UAAY,GAExB,CAED,OAAOvH,EAAK,CACR,GAAIA,EAAM,KAAK,UACX,OAAO,KACX,KAAO,KAAK,UAAY,KAAK,QAAUA,GACnC,KAAK,aAAY,EACrB,GAAI,CAAC,KAAK,SACN,OAAO,KACX,OAAS,CACL,IAAImD,EAAO,KAAK,MAAM,OAAS,EAC/B,GAAIA,EAAO,EACP,YAAK,aAAY,EACV,KAEX,IAAIzB,EAAM,KAAK,MAAMyB,CAAI,EAAGxB,EAAQ,KAAK,MAAMwB,CAAI,EACnD,GAAIxB,GAASD,EAAI,SAAS,OAAQ,CAC9B,KAAK,MAAM,MACX,KAAK,MAAM,MACX,KAAK,MAAM,MACX,SAEJ,IAAIG,EAAOH,EAAI,SAASC,CAAK,EACzBhB,EAAQ,KAAK,MAAMwC,CAAI,EAAIzB,EAAI,UAAUC,CAAK,EAClD,GAAIhB,EAAQX,EACR,YAAK,UAAYW,EACV,KAEX,GAAIkB,aAAgB2F,EAAM,CACtB,GAAI7G,GAASX,EAAK,CACd,GAAIW,EAAQ,KAAK,SACb,OAAO,KACX,IAAIY,EAAMZ,EAAQkB,EAAK,OACvB,GAAIN,GAAO,KAAK,OAAQ,CACpB,IAAIlB,EAAYwB,EAAK,KAAK4F,EAAS,SAAS,EAC5C,GAAI,CAACpH,GAAakB,EAAMlB,EAAY,KAAK,SAAS,GAC9C,OAAOwB,GAGnB,KAAK,MAAMsB,CAAI,IACXxC,EAAQkB,EAAK,QAAU,KAAK,IAAI,KAAK,SAAU7B,CAAG,IAClD,KAAK,MAAM,KAAK6B,CAAI,EACpB,KAAK,MAAM,KAAKlB,CAAK,EACrB,KAAK,MAAM,KAAK,CAAC,QAIrB,KAAK,MAAMwC,CAAI,IACf,KAAK,UAAYxC,EAAQkB,EAAK,OAGzC,CACL,CACA,MAAM6F,CAAW,CACb,YAAY1G,EAAQ2G,EAAQ,CACxB,KAAK,OAASA,EACd,KAAK,OAAS,GACd,KAAK,UAAY,KACjB,KAAK,QAAU,GACf,KAAK,OAAS3G,EAAO,WAAW,IAAIT,GAAK,IAAIyD,CAAW,CAC3D,CACD,WAAWnE,EAAO,CACd,IAAI+H,EAAc,EACdC,EAAO,KACP,CAAE,OAAA7G,CAAM,EAAKnB,EAAM,EAAG,CAAE,WAAAiI,CAAY,EAAG9G,EACvC+G,EAAO/G,EAAO,UAAUnB,EAAM,MAAO,GACrCuD,EAAUvD,EAAM,WAAaA,EAAM,WAAW,KAAO,EACrDQ,EAAY,EAChB,QAASG,EAAI,EAAGA,EAAIsH,EAAW,OAAQtH,IAAK,CACxC,GAAM,KAAKA,EAAKuH,GACZ,SACJ,IAAIC,EAAYF,EAAWtH,CAAC,EAAGiE,EAAQ,KAAK,OAAOjE,CAAC,EACpD,GAAI,EAAAqH,GAAQ,CAACG,EAAU,aAEnBA,EAAU,YAAcvD,EAAM,OAAS5E,EAAM,KAAO4E,EAAM,MAAQsD,GAAQtD,EAAM,SAAWrB,KAC3F,KAAK,kBAAkBqB,EAAOuD,EAAWnI,CAAK,EAC9C4E,EAAM,KAAOsD,EACbtD,EAAM,QAAUrB,GAEhBqB,EAAM,UAAYA,EAAM,IAAM,KAC9BpE,EAAY,KAAK,IAAIoE,EAAM,UAAWpE,CAAS,GAC/CoE,EAAM,OAAS,GAAkB,CACjC,IAAIwD,EAAaL,EAIjB,GAHInD,EAAM,SAAW,KACjBmD,EAAc,KAAK,WAAW/H,EAAO4E,EAAM,SAAUA,EAAM,IAAKmD,CAAW,GAC/EA,EAAc,KAAK,WAAW/H,EAAO4E,EAAM,MAAOA,EAAM,IAAKmD,CAAW,EACpE,CAACI,EAAU,SACXH,EAAOpD,EACHmD,EAAcK,GACd,OAIhB,KAAO,KAAK,QAAQ,OAASL,GACzB,KAAK,QAAQ,MACjB,OAAIvH,GACAR,EAAM,aAAaQ,CAAS,EAC5B,CAACwH,GAAQhI,EAAM,KAAO,KAAK,OAAO,MAClCgI,EAAO,IAAI7D,EACX6D,EAAK,MAAQhI,EAAM,EAAE,OAAO,QAC5BgI,EAAK,MAAQA,EAAK,IAAMhI,EAAM,IAC9B+H,EAAc,KAAK,WAAW/H,EAAOgI,EAAK,MAAOA,EAAK,IAAKD,CAAW,GAE1E,KAAK,UAAYC,EACV,KAAK,OACf,CACD,aAAahI,EAAO,CAChB,GAAI,KAAK,UACL,OAAO,KAAK,UAChB,IAAIgI,EAAO,IAAI7D,EAAa,CAAE,IAAAhE,EAAK,EAAAJ,CAAG,EAAGC,EACzC,OAAAgI,EAAK,MAAQ7H,EACb6H,EAAK,IAAM,KAAK,IAAI7H,EAAM,EAAGJ,EAAE,OAAO,GAAG,EACzCiI,EAAK,MAAQ7H,GAAOJ,EAAE,OAAO,IAAMA,EAAE,OAAO,QAAU,EAC/CiI,CACV,CACD,kBAAkBpD,EAAOuD,EAAWnI,EAAO,CACvC,IAAIc,EAAQ,KAAK,OAAO,QAAQd,EAAM,GAAG,EAEzC,GADAmI,EAAU,MAAM,KAAK,OAAO,MAAMrH,EAAO8D,CAAK,EAAG5E,CAAK,EAClD4E,EAAM,MAAQ,GAAI,CAClB,GAAI,CAAE,OAAAzD,CAAM,EAAKnB,EAAM,EACvB,QAASW,EAAI,EAAGA,EAAIQ,EAAO,YAAY,OAAQR,IAC3C,GAAIQ,EAAO,YAAYR,CAAC,GAAKiE,EAAM,MAAO,CACtC,IAAIhC,EAASzB,EAAO,aAAaR,CAAC,EAAE,KAAK,OAAO,KAAKiE,EAAM,MAAOA,EAAM,GAAG,EAAG5E,CAAK,EACnF,GAAI4C,GAAU,GAAK5C,EAAM,EAAE,OAAO,QAAQ,OAAO4C,GAAU,CAAC,EAAG,CACtDA,EAAS,EAGVgC,EAAM,SAAWhC,GAAU,EAF3BgC,EAAM,MAAQhC,GAAU,EAG5B,aAKZgC,EAAM,MAAQ,EACdA,EAAM,IAAM,KAAK,OAAO,QAAQ9D,EAAQ,CAAC,CAEhD,CACD,UAAUC,EAAQ6D,EAAOlD,EAAKI,EAAO,CAEjC,QAASnB,EAAI,EAAGA,EAAImB,EAAOnB,GAAK,EAC5B,GAAI,KAAK,QAAQA,CAAC,GAAKI,EACnB,OAAOe,EACf,YAAK,QAAQA,GAAO,EAAIf,EACxB,KAAK,QAAQe,GAAO,EAAI8C,EACxB,KAAK,QAAQ9C,GAAO,EAAIJ,EACjBI,CACV,CACD,WAAW9B,EAAO4E,EAAOlD,EAAKI,EAAO,CACjC,GAAI,CAAE,MAAA7B,CAAK,EAAKD,EAAO,CAAE,OAAAmB,CAAM,EAAKnB,EAAM,EAAG,CAAE,KAAAsF,CAAM,EAAGnE,EACxD,QAASkH,EAAM,EAAGA,EAAM,EAAGA,IACvB,QAAS1H,EAAIQ,EAAO,UAAUlB,EAAOoI,EAAM,EAA0B,CAAC,GAA6B1H,GAAK,EAAG,CACvG,GAAI2E,EAAK3E,CAAC,GAAK,MACX,GAAI2E,EAAK3E,EAAI,CAAC,GAAK,EACfA,EAAI2H,EAAKhD,EAAM3E,EAAI,CAAC,MAEnB,CACGmB,GAAS,GAAKwD,EAAK3E,EAAI,CAAC,GAAK,IAC7BmB,EAAQ,KAAK,UAAUwG,EAAKhD,EAAM3E,EAAI,CAAC,EAAGiE,EAAOlD,EAAKI,CAAK,GAC/D,MAGJwD,EAAK3E,CAAC,GAAKiE,IACX9C,EAAQ,KAAK,UAAUwG,EAAKhD,EAAM3E,EAAI,CAAC,EAAGiE,EAAOlD,EAAKI,CAAK,GAGvE,OAAOA,CACV,CACL,CACA,MAAMyG,CAAM,CACR,YAAYpH,EAAQ0C,EAAO2D,EAAWlD,EAAQ,CAC1C,KAAK,OAASnD,EACd,KAAK,MAAQ0C,EACb,KAAK,OAASS,EACd,KAAK,WAAa,EAClB,KAAK,YAAc,KACnB,KAAK,YAAc,EACnB,KAAK,OAAS,GACd,KAAK,UAAY,KACjB,KAAK,sBAAwB,GAC7B,KAAK,qBAAuB,EAC5B,KAAK,kBAAoB,EACzB,KAAK,OAAS,IAAID,EAAYR,EAAOS,CAAM,EAC3C,KAAK,OAAS,IAAIuD,EAAW1G,EAAQ,KAAK,MAAM,EAChD,KAAK,QAAUA,EAAO,IAAI,CAAC,EAC3B,GAAI,CAAE,KAAA+D,CAAI,EAAKZ,EAAO,CAAC,EACvB,KAAK,OAAS,CAACxE,EAAM,MAAM,KAAMqB,EAAO,IAAI,CAAC,EAAG+D,CAAI,CAAC,EACrD,KAAK,UAAYsC,EAAU,QAAU,KAAK,OAAO,IAAMtC,EAAO/D,EAAO,aAAe,EAC9E,IAAIoG,EAAeC,EAAWrG,EAAO,OAAO,EAAI,IACzD,CACD,IAAI,WAAY,CACZ,OAAO,KAAK,WACf,CAOD,SAAU,CACN,IAAIqH,EAAS,KAAK,OAAQrI,EAAM,KAAK,YAEjCsI,EAAY,KAAK,OAAS,GAC1BC,EAASC,EAQb,GAAI,KAAK,kBAAoB,KAAkDH,EAAO,QAAU,EAAG,CAC/F,GAAI,CAAC9F,CAAC,EAAI8F,EACV,KAAO9F,EAAE,YAAa,GAAIA,EAAE,MAAM,QAAUA,EAAE,MAAMA,EAAE,MAAM,OAAS,CAAC,GAAK,KAAK,uBAAuB,CACvG,KAAK,kBAAoB,KAAK,qBAAuB,EAKzD,QAAS/B,EAAI,EAAGA,EAAI6H,EAAO,OAAQ7H,IAAK,CACpC,IAAIX,EAAQwI,EAAO7H,CAAC,EACpB,OAAS,CAEL,GADA,KAAK,OAAO,UAAY,KACpBX,EAAM,IAAMG,EACZsI,EAAU,KAAKzI,CAAK,MAEnB,IAAI,KAAK,aAAaA,EAAOyI,EAAWD,CAAM,EAC/C,SAEC,CACIE,IACDA,EAAU,CAAA,EACVC,EAAgB,CAAA,GAEpBD,EAAQ,KAAK1I,CAAK,EAClB,IAAI4I,EAAM,KAAK,OAAO,aAAa5I,CAAK,EACxC2I,EAAc,KAAKC,EAAI,MAAOA,EAAI,GAAG,GAEzC,OAGR,GAAI,CAACH,EAAU,OAAQ,CACnB,IAAII,EAAWH,GAAWI,EAAaJ,CAAO,EAC9C,GAAIG,EACA,OAAI7B,GACA,QAAQ,IAAI,eAAiB,KAAK,QAAQ6B,CAAQ,CAAC,EAChD,KAAK,YAAYA,CAAQ,EAEpC,GAAI,KAAK,OAAO,OACZ,MAAI7B,GAAW0B,GACX,QAAQ,IAAI,qBAAuB,KAAK,OAAO,UAAY,KAAK,OAAO,QAAQ,KAAK,OAAO,UAAU,KAAK,EAAI,OAAO,EACnH,IAAI,YAAY,eAAiBvI,CAAG,EAEzC,KAAK,aACN,KAAK,WAAa,GAE1B,GAAI,KAAK,YAAcuI,EAAS,CAC5B,IAAIG,EAAW,KAAK,WAAa,MAAQH,EAAQ,CAAC,EAAE,IAAM,KAAK,UAAYA,EAAQ,CAAC,EAC9E,KAAK,YAAYA,EAASC,EAAeF,CAAS,EACxD,GAAII,EACA,OAAI7B,GACA,QAAQ,IAAI,gBAAkB,KAAK,QAAQ6B,CAAQ,CAAC,EACjD,KAAK,YAAYA,EAAS,SAAU,CAAA,EAGnD,GAAI,KAAK,WAAY,CACjB,IAAIE,EAAe,KAAK,YAAc,EAAI,EAAI,KAAK,WAAa,EAChE,GAAIN,EAAU,OAASM,EAEnB,IADAN,EAAU,KAAK,CAACO,EAAGC,IAAMA,EAAE,MAAQD,EAAE,KAAK,EACnCP,EAAU,OAASM,GACtBN,EAAU,IAAG,EAEjBA,EAAU,KAAK/F,GAAKA,EAAE,UAAYvC,CAAG,GACrC,KAAK,qBAEJsI,EAAU,OAAS,EAAG,CAI3BS,EAAO,QAASvI,EAAI,EAAGA,EAAI8H,EAAU,OAAS,EAAG9H,IAAK,CAClD,IAAIX,EAAQyI,EAAU9H,CAAC,EACvB,QAASwI,EAAIxI,EAAI,EAAGwI,EAAIV,EAAU,OAAQU,IAAK,CAC3C,IAAI/F,EAAQqF,EAAUU,CAAC,EACvB,GAAInJ,EAAM,UAAUoD,CAAK,GACrBpD,EAAM,OAAO,OAAS,KAAsCoD,EAAM,OAAO,OAAS,IAClF,IAAMpD,EAAM,MAAQoD,EAAM,OAAWpD,EAAM,OAAO,OAASoD,EAAM,OAAO,QAAW,EAC/EqF,EAAU,OAAOU,IAAK,CAAC,MAEtB,CACDV,EAAU,OAAO9H,IAAK,CAAC,EACvB,SAASuI,IAKrBT,EAAU,OAAS,IACnBA,EAAU,OAAO,GAA4BA,EAAU,OAAS,EAAE,EAE1E,KAAK,YAAcA,EAAU,CAAC,EAAE,IAChC,QAAS9H,EAAI,EAAGA,EAAI8H,EAAU,OAAQ9H,IAC9B8H,EAAU9H,CAAC,EAAE,IAAM,KAAK,cACxB,KAAK,YAAc8H,EAAU9H,CAAC,EAAE,KACxC,OAAO,IACV,CACD,OAAOR,EAAK,CACR,GAAI,KAAK,WAAa,MAAQ,KAAK,UAAYA,EAC3C,MAAM,IAAI,WAAW,8BAA8B,EACvD,KAAK,UAAYA,CACpB,CAKD,aAAaH,EAAOwI,EAAQY,EAAO,CAC/B,IAAItI,EAAQd,EAAM,IAAK,CAAE,OAAAmB,CAAM,EAAK,KAChCE,EAAO2F,EAAU,KAAK,QAAQhH,CAAK,EAAI,OAAS,GACpD,GAAI,KAAK,WAAa,MAAQc,EAAQ,KAAK,UACvC,OAAOd,EAAM,cAAgBA,EAAQ,KACzC,GAAI,KAAK,UAAW,CAChB,IAAIqJ,EAAWrJ,EAAM,YAAcA,EAAM,WAAW,QAAQ,OAAQsJ,EAASD,EAAWrJ,EAAM,WAAW,KAAO,EAChH,QAASuJ,EAAS,KAAK,UAAU,OAAOzI,CAAK,EAAGyI,GAAS,CACrD,IAAIC,EAAQ,KAAK,OAAO,QAAQ,MAAMD,EAAO,KAAK,EAAE,GAAKA,EAAO,KAAOpI,EAAO,QAAQnB,EAAM,MAAOuJ,EAAO,KAAK,EAAE,EAAI,GACrH,GAAIC,EAAQ,IAAMD,EAAO,SAAW,CAACF,IAAaE,EAAO,KAAK3B,EAAS,WAAW,GAAK,IAAM0B,GACzF,OAAAtJ,EAAM,QAAQuJ,EAAQC,CAAK,EACvBxC,GACA,QAAQ,IAAI3F,EAAO,KAAK,QAAQrB,CAAK,EAAI,kBAAkBmB,EAAO,QAAQoI,EAAO,KAAK,EAAE,IAAI,EACzF,GAEX,GAAI,EAAEA,aAAkB5B,IAAS4B,EAAO,SAAS,QAAU,GAAKA,EAAO,UAAU,CAAC,EAAI,EAClF,MACJ,IAAIE,EAAQF,EAAO,SAAS,CAAC,EAC7B,GAAIE,aAAiB9B,GAAQ4B,EAAO,UAAU,CAAC,GAAK,EAChDA,EAASE,MAET,QAGZ,IAAIC,EAAgBvI,EAAO,UAAUnB,EAAM,MAAO,GAClD,GAAI0J,EAAgB,EAChB,OAAA1J,EAAM,OAAO0J,CAAa,EACtB1C,GACA,QAAQ,IAAI3F,EAAO,KAAK,QAAQrB,CAAK,EAAI,uBAAuBmB,EAAO,QAAQuI,EAAgB,KAAK,IAA2B,EAC5H,GAEX,GAAI1J,EAAM,MAAM,QAAU,KACtB,KAAOA,EAAM,MAAM,OAAS,KAAwBA,EAAM,YAAW,GAAI,CAE7E,IAAI2J,EAAU,KAAK,OAAO,WAAW3J,CAAK,EAC1C,QAASW,EAAI,EAAGA,EAAIgJ,EAAQ,QAAS,CACjC,IAAI5I,EAAS4I,EAAQhJ,GAAG,EAAGc,EAAOkI,EAAQhJ,GAAG,EAAGe,EAAMiI,EAAQhJ,GAAG,EAC7D2C,EAAO3C,GAAKgJ,EAAQ,QAAU,CAACP,EAC/BQ,EAAatG,EAAOtD,EAAQA,EAAM,MAAK,EACvCgI,EAAO,KAAK,OAAO,UAKvB,GAJA4B,EAAW,MAAM7I,EAAQU,EAAMuG,EAAOA,EAAK,MAAQ4B,EAAW,IAAKlI,CAAG,EAClEsF,GACA,QAAQ,IAAI3F,EAAO,KAAK,QAAQuI,CAAU,EAAI,SAAU7I,EAAS,MAC3D,aAAaI,EAAO,QAAQJ,EAAS,SAD4D,eACrBI,EAAO,QAAQM,CAAI,OAAOX,IAAQ8I,GAAc5J,EAAQ,GAAK,YAAY,EAC3JsD,EACA,MAAO,GACFsG,EAAW,IAAM9I,EACtB0H,EAAO,KAAKoB,CAAU,EAEtBR,EAAM,KAAKQ,CAAU,EAE7B,MAAO,EACV,CAID,aAAa5J,EAAOyI,EAAW,CAC3B,IAAItI,EAAMH,EAAM,IAChB,OAAS,CACL,GAAI,CAAC,KAAK,aAAaA,EAAO,KAAM,IAAI,EACpC,MAAO,GACX,GAAIA,EAAM,IAAMG,EACZ,OAAA0J,EAAe7J,EAAOyI,CAAS,EACxB,GAGlB,CACD,YAAYD,EAAQsB,EAAQrB,EAAW,CACnC,IAAII,EAAW,KAAMkB,EAAY,GACjC,QAASpJ,EAAI,EAAGA,EAAI6H,EAAO,OAAQ7H,IAAK,CACpC,IAAIX,EAAQwI,EAAO7H,CAAC,EAAGiE,EAAQkF,EAAOnJ,GAAK,CAAC,EAAGqJ,EAAWF,GAAQnJ,GAAK,GAAK,CAAC,EACzEU,EAAO2F,EAAU,KAAK,QAAQhH,CAAK,EAAI,OAAS,GACpD,GAAIA,EAAM,UACF+J,IAEJA,EAAY,GACZ/J,EAAM,QAAO,EACTgH,GACA,QAAQ,IAAI3F,EAAO,KAAK,QAAQrB,CAAK,EAAI,cAAc,EAChD,KAAK,aAAaA,EAAOyI,CAAS,IAEzC,SAER,IAAIwB,EAAQjK,EAAM,MAAK,EAAIkK,EAAY7I,EACvC,QAAS8H,EAAI,EAAGc,EAAM,YAAW,GAAMd,EAAI,KACnCnC,GACA,QAAQ,IAAIkD,EAAY,KAAK,QAAQD,CAAK,EAAI,qBAAqB,EAC5D,MAAK,aAAaA,EAAOxB,CAAS,GAHyBU,IAMlEnC,IACAkD,EAAY,KAAK,QAAQD,CAAK,EAAI,QAE1C,QAASE,KAAUnK,EAAM,gBAAgB4E,CAAK,EACtCoC,GACA,QAAQ,IAAI3F,EAAO,KAAK,QAAQ8I,CAAM,EAAI,uBAAuB,EACrE,KAAK,aAAaA,EAAQ1B,CAAS,EAEnC,KAAK,OAAO,IAAMzI,EAAM,KACpBgK,GAAYhK,EAAM,MAClBgK,IACApF,EAAQ,GAEZ5E,EAAM,gBAAgB4E,EAAOoF,CAAQ,EACjChD,GACA,QAAQ,IAAI3F,EAAO,KAAK,QAAQrB,CAAK,EAAI,wBAAwB,KAAK,OAAO,QAAQ4E,CAAK,IAAI,EAClGiF,EAAe7J,EAAOyI,CAAS,IAE1B,CAACI,GAAYA,EAAS,MAAQ7I,EAAM,SACzC6I,EAAW7I,GAGnB,OAAO6I,CACV,CAED,YAAY7I,EAAO,CACf,OAAAA,EAAM,MAAK,EACJ2H,EAAK,MAAM,CAAE,OAAQhE,EAAkB,OAAO3D,CAAK,EACtD,QAAS,KAAK,OAAO,QACrB,MAAO,KAAK,QACZ,gBAAiB,KAAK,OAAO,aAC7B,OAAQ,KAAK,OACb,MAAO,KAAK,OAAO,CAAC,EAAE,KACtB,OAAQA,EAAM,IAAM,KAAK,OAAO,CAAC,EAAE,KACnC,cAAe,KAAK,OAAO,aAAe,CAAA,CACjD,CACD,QAAQA,EAAO,CACX,IAAIuF,GAAM0B,IAAaA,EAAW,IAAI,UAAU,IAAIjH,CAAK,EACzD,OAAKuF,GACD0B,EAAS,IAAIjH,EAAOuF,EAAK,OAAO,cAAc,KAAK,aAAa,CAAC,EAC9DA,EAAKvF,CACf,CACL,CACA,SAAS6J,EAAe7J,EAAOyI,EAAW,CACtC,QAAS9H,EAAI,EAAGA,EAAI8H,EAAU,OAAQ9H,IAAK,CACvC,IAAIyC,EAAQqF,EAAU9H,CAAC,EACvB,GAAIyC,EAAM,KAAOpD,EAAM,KAAOoD,EAAM,UAAUpD,CAAK,EAAG,CAC9CyI,EAAU9H,CAAC,EAAE,MAAQX,EAAM,QAC3ByI,EAAU9H,CAAC,EAAIX,GACnB,QAGRyI,EAAU,KAAKzI,CAAK,CACxB,CACA,MAAMoK,CAAQ,CACV,YAAYC,EAAQC,EAAOC,EAAU,CACjC,KAAK,OAASF,EACd,KAAK,MAAQC,EACb,KAAK,SAAWC,CACnB,CACD,OAAO9I,EAAM,CAAE,MAAO,CAAC,KAAK,UAAY,KAAK,SAASA,CAAI,GAAK,CAAI,CACvE,CACA,MAAM8D,EAAKiF,GAAKA,EAahB,MAAMC,CAAe,CAIjB,YAAYC,EAAM,CACd,KAAK,MAAQA,EAAK,MAClB,KAAK,MAAQA,EAAK,OAASnF,EAC3B,KAAK,OAASmF,EAAK,QAAUnF,EAC7B,KAAK,MAAQmF,EAAK,OAASnF,EAC3B,KAAK,KAAOmF,EAAK,OAAS,IAAM,GAChC,KAAK,OAASA,EAAK,SAAW,EACjC,CACL,CAMA,MAAMC,UAAiBC,CAAO,CAI1B,YAAYF,EAAM,CAMd,GALA,QAIA,KAAK,SAAW,GACZA,EAAK,SAAW,GAChB,MAAM,IAAI,WAAW,mBAAmBA,EAAK,6CAAmE,EACpH,IAAIG,EAAYH,EAAK,UAAU,MAAM,GAAG,EACxC,KAAK,cAAgBG,EAAU,OAC/B,QAASlK,EAAI,EAAGA,EAAI+J,EAAK,gBAAiB/J,IACtCkK,EAAU,KAAK,EAAE,EACrB,IAAIC,EAAW,OAAO,KAAKJ,EAAK,QAAQ,EAAE,IAAItF,GAAKsF,EAAK,SAAStF,CAAC,EAAE,CAAC,CAAC,EAClE2F,EAAY,CAAA,EAChB,QAASpK,EAAI,EAAGA,EAAIkK,EAAU,OAAQlK,IAClCoK,EAAU,KAAK,CAAA,CAAE,EACrB,SAASC,EAAQC,EAAQC,EAAM/I,EAAO,CAClC4I,EAAUE,CAAM,EAAE,KAAK,CAACC,EAAMA,EAAK,YAAY,OAAO/I,CAAK,CAAC,CAAC,CAAC,CACjE,CACD,GAAIuI,EAAK,UACL,QAASS,KAAYT,EAAK,UAAW,CACjC,IAAIQ,EAAOC,EAAS,CAAC,EACjB,OAAOD,GAAQ,WACfA,EAAOtD,EAASsD,CAAI,GACxB,QAASvK,EAAI,EAAGA,EAAIwK,EAAS,QAAS,CAClC,IAAInJ,EAAOmJ,EAASxK,GAAG,EACvB,GAAIqB,GAAQ,EACRgJ,EAAQhJ,EAAMkJ,EAAMC,EAASxK,GAAG,CAAC,MAEhC,CACD,IAAIwB,EAAQgJ,EAASxK,EAAI,CAACqB,CAAI,EAC9B,QAASmH,EAAI,CAACnH,EAAMmH,EAAI,EAAGA,IACvB6B,EAAQG,EAASxK,GAAG,EAAGuK,EAAM/I,CAAK,EACtCxB,MAIhB,KAAK,QAAU,IAAIyK,EAAQP,EAAU,IAAI,CAACQ,EAAM1K,IAAM2K,EAAS,OAAO,CAClE,KAAM3K,GAAK,KAAK,cAAgB,OAAY0K,EAC5C,GAAI1K,EACJ,MAAOoK,EAAUpK,CAAC,EAClB,IAAKmK,EAAS,QAAQnK,CAAC,EAAI,GAC3B,MAAOA,GAAK,EACZ,QAAS+J,EAAK,cAAgBA,EAAK,aAAa,QAAQ/J,CAAC,EAAI,EAChE,CAAA,CAAC,CAAC,EACC+J,EAAK,cACL,KAAK,QAAU,KAAK,QAAQ,OAAO,GAAGA,EAAK,WAAW,GAC1D,KAAK,OAAS,GACd,KAAK,aAAea,EACpB,IAAIC,EAAa5H,EAAY8G,EAAK,SAAS,EAC3C,KAAK,QAAUA,EAAK,QACpB,KAAK,iBAAmBA,EAAK,aAAe,CAAA,EAC5C,KAAK,YAAc,IAAI,YAAY,KAAK,iBAAiB,MAAM,EAC/D,QAAS/J,EAAI,EAAGA,EAAI,KAAK,iBAAiB,OAAQA,IAC9C,KAAK,YAAYA,CAAC,EAAI,KAAK,iBAAiBA,CAAC,EAAE,KACnD,KAAK,aAAe,KAAK,iBAAiB,IAAI8K,CAAc,EAC5D,KAAK,OAAS7H,EAAY8G,EAAK,OAAQ,WAAW,EAClD,KAAK,KAAO9G,EAAY8G,EAAK,SAAS,EACtC,KAAK,KAAO9G,EAAY8G,EAAK,IAAI,EACjC,KAAK,QAAUA,EAAK,QACpB,KAAK,WAAaA,EAAK,WAAW,IAAIvI,GAAS,OAAOA,GAAS,SAAW,IAAIkD,EAAWmG,EAAYrJ,CAAK,EAAIA,CAAK,EACnH,KAAK,SAAWuI,EAAK,SACrB,KAAK,SAAWA,EAAK,UAAY,CAAA,EACjC,KAAK,mBAAqBA,EAAK,oBAAsB,KACrD,KAAK,eAAiBA,EAAK,UAC3B,KAAK,UAAYA,EAAK,WAAa,KACnC,KAAK,QAAU,KAAK,QAAQ,MAAM,OAAS,EAC3C,KAAK,QAAU,KAAK,eACpB,KAAK,IAAM,KAAK,SAAS,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC,CAAC,CACzD,CACD,YAAY7G,EAAO2D,EAAWlD,EAAQ,CAClC,IAAIoH,EAAQ,IAAInD,EAAM,KAAM1E,EAAO2D,EAAWlD,CAAM,EACpD,QAASqH,KAAK,KAAK,SACfD,EAAQC,EAAED,EAAO7H,EAAO2D,EAAWlD,CAAM,EAC7C,OAAOoH,CACV,CAID,QAAQzL,EAAOwB,EAAMmK,EAAQ,GAAO,CAChC,IAAIC,EAAQ,KAAK,KACjB,GAAIpK,GAAQoK,EAAM,CAAC,EACf,MAAO,GACX,QAAS1L,EAAM0L,EAAMpK,EAAO,CAAC,IAAK,CAC9B,IAAIqK,EAAWD,EAAM1L,GAAK,EAAGmD,EAAOwI,EAAW,EAC3ChJ,EAAS+I,EAAM1L,GAAK,EACxB,GAAImD,GAAQsI,EACR,OAAO9I,EACX,QAASpB,EAAMvB,GAAO2L,GAAY,GAAI3L,EAAMuB,EAAKvB,IAC7C,GAAI0L,EAAM1L,CAAG,GAAKF,EACd,OAAO6C,EACf,GAAIQ,EACA,MAAO,GAElB,CAID,UAAUrD,EAAO8L,EAAU,CACvB,IAAIzG,EAAO,KAAK,KAChB,QAAS+C,EAAM,EAAGA,EAAM,EAAGA,IACvB,QAAS1H,EAAI,KAAK,UAAUV,EAAOoI,EAAM,EAA0B,CAA2B,EAAErG,GAAOrB,GAAK,EAAG,CAC3G,IAAKqB,EAAOsD,EAAK3E,CAAC,IAAM,MACpB,GAAI2E,EAAK3E,EAAI,CAAC,GAAK,EACfqB,EAAOsD,EAAK3E,EAAI2H,EAAKhD,EAAM3E,EAAI,CAAC,CAAC,MAChC,IAAI2E,EAAK3E,EAAI,CAAC,GAAK,EACpB,OAAO2H,EAAKhD,EAAM3E,EAAI,CAAC,EAEvB,MAER,GAAIqB,GAAQ+J,GAAY/J,GAAQ,EAC5B,OAAOsG,EAAKhD,EAAM3E,EAAI,CAAC,EAGnC,MAAO,EACV,CAID,UAAUV,EAAO+L,EAAM,CACnB,OAAO,KAAK,OAAQ/L,EAAQ,EAA2B+L,CAAI,CAC9D,CAID,UAAU/L,EAAOgM,EAAM,CACnB,OAAQ,KAAK,UAAUhM,EAAO,CAAyB,EAAGgM,GAAQ,CACrE,CAID,YAAYhM,EAAOc,EAAQ,CACvB,MAAO,CAAC,CAAC,KAAK,WAAWd,EAAO+I,GAAKA,GAAKjI,EAAS,GAAO,IAAI,CACjE,CAID,WAAWd,EAAOc,EAAQ,CACtB,IAAImL,EAAQ,KAAK,UAAUjM,EAAO,CAAC,EAC/B2C,EAASsJ,EAAQnL,EAAOmL,CAAK,EAAI,OACrC,QAASvL,EAAI,KAAK,UAAUV,EAAO,CAA2B,EAAE2C,GAAU,KAAMjC,GAAK,EAAG,CACpF,GAAI,KAAK,KAAKA,CAAC,GAAK,MAChB,GAAI,KAAK,KAAKA,EAAI,CAAC,GAAK,EACpBA,EAAI2H,EAAK,KAAK,KAAM3H,EAAI,CAAC,MAEzB,OAERiC,EAAS7B,EAAOuH,EAAK,KAAK,KAAM3H,EAAI,CAAC,CAAC,EAE1C,OAAOiC,CACV,CAKD,WAAW3C,EAAO,CACd,IAAI2C,EAAS,CAAA,EACb,QAASjC,EAAI,KAAK,UAAUV,EAAO,CAA2B,GAAGU,GAAK,EAAG,CACrE,GAAI,KAAK,KAAKA,CAAC,GAAK,MAChB,GAAI,KAAK,KAAKA,EAAI,CAAC,GAAK,EACpBA,EAAI2H,EAAK,KAAK,KAAM3H,EAAI,CAAC,MAEzB,OAER,GAAK,OAAK,KAAKA,EAAI,CAAC,EAAK,GAA4C,CACjE,IAAIwB,EAAQ,KAAK,KAAKxB,EAAI,CAAC,EACtBiC,EAAO,KAAK,CAACD,EAAGhC,IAAOA,EAAI,GAAMgC,GAAKR,CAAK,GAC5CS,EAAO,KAAK,KAAK,KAAKjC,CAAC,EAAGwB,CAAK,GAG3C,OAAOS,CACV,CAMD,UAAUuJ,EAAQ,CAGd,IAAIC,EAAO,OAAO,OAAO,OAAO,OAAOzB,EAAS,SAAS,EAAG,IAAI,EAGhE,GAFIwB,EAAO,QACPC,EAAK,QAAU,KAAK,QAAQ,OAAO,GAAGD,EAAO,KAAK,GAClDA,EAAO,IAAK,CACZ,IAAIE,EAAO,KAAK,SAASF,EAAO,GAAG,EACnC,GAAI,CAACE,EACD,MAAM,IAAI,WAAW,yBAAyBF,EAAO,KAAK,EAC9DC,EAAK,IAAMC,EAEf,OAAIF,EAAO,aACPC,EAAK,WAAa,KAAK,WAAW,IAAIE,GAAK,CACvC,IAAInJ,EAAQgJ,EAAO,WAAW,KAAK/G,GAAKA,EAAE,MAAQkH,CAAC,EACnD,OAAOnJ,EAAQA,EAAM,GAAKmJ,CAC1C,CAAa,GACDH,EAAO,eACPC,EAAK,aAAe,KAAK,aAAa,MAAK,EAC3CA,EAAK,iBAAmB,KAAK,iBAAiB,IAAI,CAAC,EAAG,IAAM,CACxD,IAAIjJ,EAAQgJ,EAAO,aAAa,KAAK/G,GAAKA,EAAE,MAAQ,EAAE,QAAQ,EAC9D,GAAI,CAACjC,EACD,OAAO,EACX,IAAIuH,EAAO,OAAO,OAAO,OAAO,OAAO,CAAA,EAAI,CAAC,EAAG,CAAE,SAAUvH,EAAM,EAAI,CAAA,EACrE,OAAAiJ,EAAK,aAAa,CAAC,EAAIX,EAAef,CAAI,EACnCA,CACvB,CAAa,GAEDyB,EAAO,iBACPC,EAAK,QAAUD,EAAO,gBACtBA,EAAO,UACPC,EAAK,QAAU,KAAK,aAAaD,EAAO,OAAO,GAC/CA,EAAO,QAAU,OACjBC,EAAK,OAASD,EAAO,QACrBA,EAAO,OACPC,EAAK,SAAWA,EAAK,SAAS,OAAOD,EAAO,IAAI,GAChDA,EAAO,cAAgB,OACvBC,EAAK,aAAeD,EAAO,cACxBC,CACV,CAKD,aAAc,CACV,OAAO,KAAK,SAAS,OAAS,CACjC,CAOD,QAAQ3K,EAAM,CACV,OAAO,KAAK,UAAY,KAAK,UAAUA,CAAI,EAAI,OAAOA,GAAQ,KAAK,SAAW,KAAK,QAAQ,MAAMA,CAAI,EAAE,MAAQA,CAAI,CACtH,CAKD,IAAI,SAAU,CAAE,OAAO,KAAK,QAAU,CAAI,CAI1C,IAAI,SAAU,CAAE,OAAO,KAAK,QAAQ,MAAM,KAAK,IAAI,CAAC,CAAC,CAAI,CAIzD,kBAAkBA,EAAM,CACpB,IAAI8K,EAAO,KAAK,mBAChB,OAAOA,GAAQ,KAAO,EAAIA,EAAK9K,CAAI,GAAK,CAC3C,CAID,aAAa2E,EAAS,CAClB,IAAIoG,EAAS,OAAO,KAAK,KAAK,QAAQ,EAAGlC,EAAQkC,EAAO,IAAI,IAAM,EAAK,EACvE,GAAIpG,EACA,QAASqG,KAAQrG,EAAQ,MAAM,GAAG,EAAG,CACjC,IAAIb,EAAKiH,EAAO,QAAQC,CAAI,EACxBlH,GAAM,IACN+E,EAAM/E,CAAE,EAAI,IAExB,IAAIgF,EAAW,KACf,QAAS5J,EAAI,EAAGA,EAAI6L,EAAO,OAAQ7L,IAC/B,GAAI,CAAC2J,EAAM3J,CAAC,EACR,QAASwI,EAAI,KAAK,SAASqD,EAAO7L,CAAC,CAAC,EAAG4E,GAAKA,EAAK,KAAK,KAAK4D,GAAG,IAAM,QAC/DoB,IAAaA,EAAW,IAAI,WAAW,KAAK,QAAU,CAAC,IAAIhF,CAAE,EAAI,EAE9E,OAAO,IAAI6E,EAAQhE,EAASkE,EAAOC,CAAQ,CAC9C,CAKD,OAAO,YAAYG,EAAM,CACrB,OAAO,IAAIC,EAASD,CAAI,CAC3B,CACL,CACA,SAASpC,EAAKhD,EAAMlD,EAAK,CAAE,OAAOkD,EAAKlD,CAAG,EAAKkD,EAAKlD,EAAM,CAAC,GAAK,EAAM,CACtE,SAAS0G,EAAaN,EAAQ,CAC1B,IAAI/F,EAAO,KACX,QAASzC,KAASwI,EAAQ,CACtB,IAAIE,EAAU1I,EAAM,EAAE,WACjBA,EAAM,KAAOA,EAAM,EAAE,OAAO,KAAO0I,GAAW,MAAQ1I,EAAM,IAAM0I,IACnE1I,EAAM,EAAE,OAAO,UAAUA,EAAM,MAAO,CAA4B,IACjE,CAACyC,GAAQA,EAAK,MAAQzC,EAAM,SAC7ByC,EAAOzC,GAEf,OAAOyC,CACX,CACA,SAASgJ,EAAef,EAAM,CAC1B,GAAIA,EAAK,SAAU,CACf,IAAIxC,EAAOwC,EAAK,OAAS,EAA4B,EACrD,MAAO,CAACvI,EAAOnC,IAAW0K,EAAK,SAASvI,EAAOnC,CAAK,GAAK,EAAKkI,EAElE,OAAOwC,EAAK,GAChB", "x_google_ignoreList": [0]}