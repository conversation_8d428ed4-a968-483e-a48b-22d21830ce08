import"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";const{SvelteComponent:h,append:b,attr:r,binding_callbacks:v,create_slot:z,detach:E,element:g,get_all_dirty_from_scope:C,get_slot_changes:w,init:B,insert:R,safe_not_equal:k,toggle_class:_,transition_in:q,transition_out:S,update_slot_base:j}=window.__gradio__svelte__internal;function y(n){let e,o,i;const u=n[5].default,l=z(u,n,n[4],null);return{c(){e=g("div"),o=g("div"),l&&l.c(),r(o,"class","icon svelte-1oiin9d"),r(e,"class","empty svelte-1oiin9d"),r(e,"aria-label","Empty value"),_(e,"small",n[0]==="small"),_(e,"large",n[0]==="large"),_(e,"unpadded_box",n[1]),_(e,"small_parent",n[3])},m(t,s){R(t,e,s),b(e,o),l&&l.m(o,null),n[6](e),i=!0},p(t,[s]){l&&l.p&&(!i||s&16)&&j(l,u,t,t[4],i?w(u,t[4],s,null):C(t[4]),null),(!i||s&1)&&_(e,"small",t[0]==="small"),(!i||s&1)&&_(e,"large",t[0]==="large"),(!i||s&2)&&_(e,"unpadded_box",t[1]),(!i||s&8)&&_(e,"small_parent",t[3])},i(t){i||(q(l,t),i=!0)},o(t){S(l,t),i=!1},d(t){t&&E(e),l&&l.d(t),n[6](null)}}}function A(n,e,o){let i,{$$slots:u={},$$scope:l}=e,{size:t="small"}=e,{unpadded_box:s=!1}=e,d;function m(a){if(!a)return!1;const{height:f}=a.getBoundingClientRect(),{height:c}=a.parentElement?.getBoundingClientRect()||{height:f};return f>c+2}function p(a){v[a?"unshift":"push"](()=>{d=a,o(2,d)})}return n.$$set=a=>{"size"in a&&o(0,t=a.size),"unpadded_box"in a&&o(1,s=a.unpadded_box),"$$scope"in a&&o(4,l=a.$$scope)},n.$$.update=()=>{n.$$.dirty&4&&o(3,i=m(d))},[t,s,d,i,l,u,p]}class F extends h{constructor(e){super(),B(this,e,A,y,k,{size:0,unpadded_box:1})}}export{F as E};
//# sourceMappingURL=Empty-28f63bf0.js.map
