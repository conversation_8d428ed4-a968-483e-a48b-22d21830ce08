const{SvelteComponent:v,append:g,attr:l,detach:u,init:w,insert:m,noop:h,safe_not_equal:f,svg_element:d}=window.__gradio__svelte__internal;function x(r){let e,n;return{c(){e=d("svg"),n=d("path"),l(n,"fill","currentColor"),l(n,"d","M13.75 2a2.25 2.25 0 0 1 2.236 2.002V4h1.764A2.25 2.25 0 0 1 20 6.25V11h-1.5V6.25a.75.75 0 0 0-.75-.75h-2.129c-.404.603-1.091 1-1.871 1h-3.5c-.78 0-1.467-.397-1.871-1H6.25a.75.75 0 0 0-.75.75v13.5c0 .414.336.75.75.75h4.78a3.99 3.99 0 0 0 .505 1.5H6.25A2.25 2.25 0 0 1 4 19.75V6.25A2.25 2.25 0 0 1 6.25 4h1.764a2.25 2.25 0 0 1 2.236-2h3.5Zm2.245 2.096L16 4.25c0-.052-.002-.103-.005-.154ZM13.75 3.5h-3.5a.75.75 0 0 0 0 1.5h3.5a.75.75 0 0 0 0-1.5ZM15 12a3 3 0 0 0-3 3v5c0 .556.151 1.077.415 1.524l3.494-3.494a2.25 2.25 0 0 1 3.182 0l3.494 3.494c.264-.447.415-.968.415-1.524v-5a3 3 0 0 0-3-3h-5Zm0 11a2.985 2.985 0 0 1-1.524-.415l3.494-3.494a.75.75 0 0 1 1.06 0l3.494 3.494A2.985 2.985 0 0 1 20 23h-5Zm5-7a1 1 0 1 1 0-2a1 1 0 0 1 0 2Z"),l(e,"xmlns","http://www.w3.org/2000/svg"),l(e,"width","100%"),l(e,"height","100%"),l(e,"viewBox","0 0 24 24")},m(o,a){m(o,e,a),g(e,n)},p:h,i:h,o:h,d(o){o&&u(e)}}}class M extends v{constructor(e){super(),w(this,e,null,x,f,{})}}const{SvelteComponent:$,append:c,attr:t,detach:Z,init:C,insert:k,noop:p,safe_not_equal:y,svg_element:s}=window.__gradio__svelte__internal;function A(r){let e,n,o,a;return{c(){e=s("svg"),n=s("path"),o=s("polyline"),a=s("line"),t(n,"d","M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"),t(o,"points","17 8 12 3 7 8"),t(a,"x1","12"),t(a,"y1","3"),t(a,"x2","12"),t(a,"y2","15"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"width","90%"),t(e,"height","90%"),t(e,"viewBox","0 0 24 24"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"stroke-width","2"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round"),t(e,"class","feather feather-upload")},m(i,_){k(i,e,_),c(e,n),c(e,o),c(e,a)},p,i:p,o:p,d(i){i&&Z(e)}}}class V extends ${constructor(e){super(),C(this,e,null,A,y,{})}}export{M as I,V as U};
//# sourceMappingURL=Upload-351cc897.js.map
