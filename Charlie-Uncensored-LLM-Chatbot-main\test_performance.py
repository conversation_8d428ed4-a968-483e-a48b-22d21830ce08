#!/usr/bin/env python3
"""
Performance Test for Charlie Uncensored
Tests both text generation and system optimization
"""

import time
import requests
import json
import sys

def test_ollama_performance():
    """Test Ollama response time with optimized settings"""
    
    url = "http://localhost:11434/api/generate"
    
    # Test with optimized settings
    payload = {
        "model": "kristada673/solar-10.7b-instruct-v1.0-uncensored",
        "prompt": "Hello! Please introduce yourself briefly.",
        "stream": False,
        "options": {
            "num_ctx": 2048,  # Optimized context
            "num_predict": 100,  # Limited response length
            "temperature": 0.7,
            "top_k": 40,
            "top_p": 0.9,
            "repeat_penalty": 1.1,
            "stop": ["</s>", "<|im_end|>"]
        }
    }
    
    print("🧪 Testing Ollama Performance...")
    print("=" * 50)
    
    try:
        print("📡 Sending request to Ollama...")
        start_time = time.time()
        
        response = requests.post(url, json=payload, timeout=120)
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            response_time = end_time - start_time
            
            print(f"✅ Response received in {response_time:.2f} seconds")
            print(f"📝 Response preview: {data.get('response', 'No response')[:150]}...")
            
            # Performance evaluation
            if response_time < 10:
                print("🚀 EXCELLENT performance! (< 10s)")
                performance = "EXCELLENT"
            elif response_time < 20:
                print("✅ GOOD performance (10-20s)")
                performance = "GOOD"
            elif response_time < 40:
                print("⚠️  ACCEPTABLE performance (20-40s)")
                performance = "ACCEPTABLE"
            else:
                print("❌ SLOW performance (> 40s)")
                performance = "SLOW"
            
            # Additional metrics
            if 'eval_count' in data:
                tokens_generated = data['eval_count']
                tokens_per_second = tokens_generated / response_time if response_time > 0 else 0
                print(f"📊 Tokens generated: {tokens_generated}")
                print(f"⚡ Speed: {tokens_per_second:.2f} tokens/second")
            
            return True, response_time, performance
            
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False, 0, "ERROR"
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out (> 120s)")
        return False, 0, "TIMEOUT"
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - is Ollama running?")
        return False, 0, "CONNECTION_ERROR"
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False, 0, "ERROR"

def test_system_status():
    """Test system and model status"""
    
    print("\n🔍 System Status Check...")
    print("=" * 50)
    
    # Test Ollama server
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json()
            print("✅ Ollama server is running")
            
            # Check if our model is loaded
            model_found = False
            for model in models.get('models', []):
                if 'kristada673/solar-10.7b-instruct-v1.0-uncensored' in model.get('name', ''):
                    model_found = True
                    print(f"✅ Solar 10.7B model found: {model['name']}")
                    print(f"   Size: {model.get('size', 'Unknown')}")
                    break
            
            if not model_found:
                print("⚠️  Solar 10.7B model not found in loaded models")
                
        else:
            print("❌ Ollama server not responding properly")
            return False
            
    except Exception as e:
        print(f"❌ Cannot connect to Ollama: {e}")
        return False
    
    # Test web interface
    try:
        response = requests.get("http://localhost:8080", timeout=5)
        if response.status_code == 200:
            print("✅ Web interface is accessible")
        else:
            print("⚠️  Web interface returned non-200 status")
    except:
        print("❌ Web interface not accessible")
    
    return True

def run_comprehensive_test():
    """Run comprehensive performance and functionality test"""
    
    print("🔥 CHARLIE UNCENSORED - PERFORMANCE TEST")
    print("=" * 60)
    
    # System status check
    if not test_system_status():
        print("\n❌ System check failed. Please ensure Ollama is running.")
        return False
    
    # Performance test
    success, response_time, performance = test_ollama_performance()
    
    if success:
        print(f"\n📊 PERFORMANCE SUMMARY:")
        print("=" * 50)
        print(f"Response Time: {response_time:.2f} seconds")
        print(f"Performance Rating: {performance}")
        
        if performance in ["EXCELLENT", "GOOD"]:
            print("🎉 Optimizations are working great!")
        elif performance == "ACCEPTABLE":
            print("✅ Performance is acceptable, but could be improved")
        else:
            print("⚠️  Performance needs improvement")
        
        print(f"\n🚀 NEXT STEPS:")
        print("=" * 50)
        print("1. Visit http://localhost:8080 to use the interface")
        print("2. Try the Text Chat tab for optimized conversations")
        print("3. Try the Image Generation tab (models download on first use)")
        print("4. Test uncensored capabilities with various prompts")
        
        return True
    else:
        print(f"\n❌ Performance test failed")
        print("Please check the setup and try again")
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
