import"./index-a80d931b.js";import{a as b}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import"./svelte/svelte.js";import"./Index-26cfc80a.js";const{SvelteComponent:k,create_component:w,destroy_component:r,detach:z,init:B,insert:q,mount_component:C,safe_not_equal:I,set_data:S,text:j,transition_in:A,transition_out:D}=window.__gradio__svelte__internal;function E(a){let i=(a[3]?a[11].i18n(a[3]):"")+"",l;return{c(){l=j(i)},m(e,t){q(e,l,t)},p(e,t){t&2056&&i!==(i=(e[3]?e[11].i18n(e[3]):"")+"")&&S(l,i)},d(e){e&&z(l)}}}function F(a){let i,l;return i=new b({props:{value:a[3],variant:a[4],elem_id:a[0],elem_classes:a[1],size:a[6],scale:a[7],link:a[9],icon:a[8],min_width:a[10],visible:a[2],disabled:!a[5],$$slots:{default:[E]},$$scope:{ctx:a}}}),i.$on("click",a[12]),{c(){w(i.$$.fragment)},m(e,t){C(i,e,t),l=!0},p(e,[t]){const f={};t&8&&(f.value=e[3]),t&16&&(f.variant=e[4]),t&1&&(f.elem_id=e[0]),t&2&&(f.elem_classes=e[1]),t&64&&(f.size=e[6]),t&128&&(f.scale=e[7]),t&512&&(f.link=e[9]),t&256&&(f.icon=e[8]),t&1024&&(f.min_width=e[10]),t&4&&(f.visible=e[2]),t&32&&(f.disabled=!e[5]),t&10248&&(f.$$scope={dirty:t,ctx:e}),i.$set(f)},i(e){l||(A(i.$$.fragment,e),l=!0)},o(e){D(i.$$.fragment,e),l=!1},d(e){r(i,e)}}}function G(a,i,l){let{elem_id:e=""}=i,{elem_classes:t=[]}=i,{visible:f=!0}=i,{value:s}=i,{variant:m="secondary"}=i,{interactive:u}=i,{size:c="lg"}=i,{scale:o=null}=i,{icon:d=null}=i,{link:v=null}=i,{min_width:g=void 0}=i,{gradio:_}=i;const h=()=>_.dispatch("click");return a.$$set=n=>{"elem_id"in n&&l(0,e=n.elem_id),"elem_classes"in n&&l(1,t=n.elem_classes),"visible"in n&&l(2,f=n.visible),"value"in n&&l(3,s=n.value),"variant"in n&&l(4,m=n.variant),"interactive"in n&&l(5,u=n.interactive),"size"in n&&l(6,c=n.size),"scale"in n&&l(7,o=n.scale),"icon"in n&&l(8,d=n.icon),"link"in n&&l(9,v=n.link),"min_width"in n&&l(10,g=n.min_width),"gradio"in n&&l(11,_=n.gradio)},[e,t,f,s,m,u,c,o,d,v,g,_,h]}class M extends k{constructor(i){super(),B(this,i,G,F,I,{elem_id:0,elem_classes:1,visible:2,value:3,variant:4,interactive:5,size:6,scale:7,icon:8,link:9,min_width:10,gradio:11})}}export{b as BaseButton,M as default};
//# sourceMappingURL=Index-035d2c1f.js.map
