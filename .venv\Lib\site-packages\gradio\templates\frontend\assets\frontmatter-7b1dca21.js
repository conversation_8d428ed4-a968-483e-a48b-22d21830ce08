import{s as m,t as a,f as s,c as i,p,S as l}from"./Index-26d165be.js";import{yaml as f}from"./yaml-95012b83.js";import"./index-a80d931b.js";import"./svelte/svelte.js";import"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import"./Index-26cfc80a.js";import"./Check-965babbe.js";import"./Copy-b365948f.js";import"./DownloadLink-7ff36416.js";import"./file-url-bef2dc1b.js";import"./BlockLabel-f27805b1.js";import"./Empty-28f63bf0.js";import"./Example-0b8f33de.js";const n=/^---\s*$/m,I={defineNodes:[{name:"Frontmatter",block:!0},"FrontmatterMark"],props:[m({Frontmatter:[a.documentMeta,a.monospace],FrontmatterMark:a.processingInstruction}),s.add({Frontmatter:i,FrontmatterMark:()=>null})],wrap:p(t=>{const{parser:e}=l.define(f);return t.type.name==="Frontmatter"?{parser:e,overlay:[{from:t.from+4,to:t.to-4}]}:null}),parseBlock:[{name:"Frontmatter",before:"HorizontalRule",parse:(t,e)=>{let r;const o=new Array;if(t.lineStart===0&&n.test(e.text)){for(o.push(t.elt("FrontmatterMark",0,4));t.nextLine();)if(n.test(e.text)){r=t.lineStart+4;break}return r!==void 0&&(o.push(t.elt("FrontmatterMark",r-4,r)),t.addElement(t.elt("Frontmatter",0,r,o))),!0}return!1}}]};export{I as frontmatter};
//# sourceMappingURL=frontmatter-7b1dca21.js.map
