const{SvelteComponent:C,append:m,attr:w,destroy_each:E,detach:o,element:d,empty:N,ensure_array_like:h,init:q,insert:u,noop:g,safe_not_equal:S,set_data:j,space:z,text:b,toggle_class:_}=window.__gradio__svelte__internal;function v(s,e,l){const i=s.slice();return i[3]=e[l],i}function k(s){let e,l=Array.isArray(s[0])&&s[0].length>3,i,a=h(Array.isArray(s[0])?s[0].slice(0,3):[s[0]]),f=[];for(let t=0;t<a.length;t+=1)f[t]=p(v(s,a,t));let n=l&&A();return{c(){for(let t=0;t<f.length;t+=1)f[t].c();e=z(),n&&n.c(),i=N()},m(t,c){for(let r=0;r<f.length;r+=1)f[r]&&f[r].m(t,c);u(t,e,c),n&&n.m(t,c),u(t,i,c)},p(t,c){if(c&1){a=h(Array.isArray(t[0])?t[0].slice(0,3):[t[0]]);let r;for(r=0;r<a.length;r+=1){const y=v(t,a,r);f[r]?f[r].p(y,c):(f[r]=p(y),f[r].c(),f[r].m(e.parentNode,e))}for(;r<f.length;r+=1)f[r].d(1);f.length=a.length}c&1&&(l=Array.isArray(t[0])&&t[0].length>3),l?n||(n=A(),n.c(),n.m(i.parentNode,i)):n&&(n.d(1),n=null)},d(t){t&&(o(e),o(i)),E(f,t),n&&n.d(t)}}}function p(s){let e,l,i,a=s[3]+"",f;return{c(){e=d("li"),l=d("code"),i=b("./"),f=b(a)},m(n,t){u(n,e,t),m(e,l),m(l,i),m(l,f)},p(n,t){t&1&&a!==(a=n[3]+"")&&j(f,a)},d(n){n&&o(e)}}}function A(s){let e;return{c(){e=d("li"),e.textContent="...",w(e,"class","extra svelte-4tf8f")},m(l,i){u(l,e,i)},d(l){l&&o(e)}}}function B(s){let e,l=s[0]&&k(s);return{c(){e=d("ul"),l&&l.c(),w(e,"class","svelte-4tf8f"),_(e,"table",s[1]==="table"),_(e,"gallery",s[1]==="gallery"),_(e,"selected",s[2])},m(i,a){u(i,e,a),l&&l.m(e,null)},p(i,[a]){i[0]?l?l.p(i,a):(l=k(i),l.c(),l.m(e,null)):l&&(l.d(1),l=null),a&2&&_(e,"table",i[1]==="table"),a&2&&_(e,"gallery",i[1]==="gallery"),a&4&&_(e,"selected",i[2])},i:g,o:g,d(i){i&&o(e),l&&l.d()}}}function D(s,e,l){let{value:i}=e,{type:a}=e,{selected:f=!1}=e;return s.$$set=n=>{"value"in n&&l(0,i=n.value),"type"in n&&l(1,a=n.type),"selected"in n&&l(2,f=n.selected)},[i,a,f]}class F extends C{constructor(e){super(),q(this,e,D,B,S,{value:0,type:1,selected:2})}}export{F as default};
//# sourceMappingURL=Example-4231f92e.js.map
