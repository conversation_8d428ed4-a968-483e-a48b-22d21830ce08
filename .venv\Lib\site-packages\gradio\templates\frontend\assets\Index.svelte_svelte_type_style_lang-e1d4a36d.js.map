{"version": 3, "file": "Index.svelte_svelte_type_style_lang-e1d4a36d.js", "sources": ["../../../../js/markdown/shared/Markdown.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { copy } from \"@gradio/utils\";\n\n\timport MarkdownCode from \"./MarkdownCode.svelte\";\n\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: string;\n\texport let min_height = false;\n\texport let rtl = false;\n\texport let sanitize_html = true;\n\texport let line_breaks = false;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let header_links = false;\n\n\tconst dispatch = createEventDispatcher<{ change: undefined }>();\n\n\t$: value, dispatch(\"change\");\n</script>\n\n<div\n\tclass:min={min_height}\n\tclass=\"prose {elem_classes.join(' ')}\"\n\tclass:hide={!visible}\n\tdata-testid=\"markdown\"\n\tdir={rtl ? \"rtl\" : \"ltr\"}\n\tuse:copy\n>\n\t<MarkdownCode\n\t\tmessage={value}\n\t\t{latex_delimiters}\n\t\t{sanitize_html}\n\t\t{line_breaks}\n\t\tchatbot={false}\n\t\t{header_links}\n\t/>\n</div>\n\n<style>\n\tdiv :global(.math.inline) {\n\t\tfill: var(--body-text-color);\n\t\tdisplay: inline-block;\n\t\tvertical-align: middle;\n\t\tpadding: var(--size-1-5) -var(--size-1);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\tdiv :global(.math.inline svg) {\n\t\tdisplay: inline;\n\t\tmargin-bottom: 0.22em;\n\t}\n\n\tdiv {\n\t\tmax-width: 100%;\n\t}\n\n\t.min {\n\t\tmin-height: var(--size-24);\n\t}\n\t.hide {\n\t\tdisplay: none;\n\t}\n</style>\n"], "names": ["createEventDispatcher", "ctx", "insert", "target", "div", "anchor", "elem_classes", "$$props", "visible", "value", "min_height", "rtl", "sanitize_html", "line_breaks", "latex_delimiters", "header_links", "dispatch"], "mappings": "maACU,CAAA,sBAAAA,CAAA,SAAqC,0FAiCpCC,EAAK,CAAA,oEAIL,gFAXIA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,gBAAA,4CAG9BA,EAAG,CAAA,EAAG,MAAQ,KAAK,YAJbA,EAAU,CAAA,CAAA,cAERA,EAAO,CAAA,CAAA,UAHrBC,EAgBKC,EAAAC,EAAAC,CAAA,sFAPMJ,EAAK,CAAA,6JAPDA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,qDAG9BA,EAAG,CAAA,EAAG,MAAQ,2CAJRA,EAAU,CAAA,CAAA,yBAERA,EAAO,CAAA,CAAA,uHAtBT,aAAAK,EAAY,EAAA,EAAAC,EACZ,CAAA,QAAAC,EAAU,EAAI,EAAAD,GACd,MAAAE,CAAa,EAAAF,EACb,CAAA,WAAAG,EAAa,EAAK,EAAAH,EAClB,CAAA,IAAAI,EAAM,EAAK,EAAAJ,EACX,CAAA,cAAAK,EAAgB,EAAI,EAAAL,EACpB,CAAA,YAAAM,EAAc,EAAK,EAAAN,GACnB,iBAAAO,CAIR,EAAAP,EACQ,CAAA,aAAAQ,EAAe,EAAK,EAAAR,EAEzB,MAAAS,EAAWhB,2YAEPgB,EAAS,QAAQ"}