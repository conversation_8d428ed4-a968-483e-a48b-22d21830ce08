{"version": 3, "file": "Index-3bd93696.js", "sources": ["../../../../js/uploadbutton/shared/UploadButton.svelte", "../../../../js/uploadbutton/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { tick, createEventDispatcher, getContext } from \"svelte\";\n\timport { BaseButton } from \"@gradio/button\";\n\timport {\n\t\tupload,\n\t\tprepare_files,\n\t\ttype FileData,\n\t\ttype upload_files\n\t} from \"@gradio/client\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let label: string | null;\n\texport let value: null | FileData | FileData[];\n\texport let file_count: string;\n\texport let file_types: string[] = [];\n\texport let root: string;\n\texport let size: \"sm\" | \"lg\" = \"lg\";\n\texport let icon: FileData | null = null;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let variant: \"primary\" | \"secondary\" | \"stop\" = \"secondary\";\n\texport let disabled = false;\n\n\tconst dispatch = createEventDispatcher();\n\n\t// Needed for wasm support\n\tconst upload_fn = getContext<typeof upload_files>(\"upload_files\");\n\n\tlet hidden_upload: HTMLInputElement;\n\tlet accept_file_types: string | null;\n\n\tif (file_types == null) {\n\t\taccept_file_types = null;\n\t} else {\n\t\tfile_types = file_types.map((x) => {\n\t\t\tif (x.startsWith(\".\")) {\n\t\t\t\treturn x;\n\t\t\t}\n\t\t\treturn x + \"/*\";\n\t\t});\n\t\taccept_file_types = file_types.join(\", \");\n\t}\n\n\tfunction open_file_upload(): void {\n\t\tdispatch(\"click\");\n\t\thidden_upload.click();\n\t}\n\n\tasync function load_files(files: FileList): Promise<void> {\n\t\tlet _files: File[] = Array.from(files);\n\t\tif (!files.length) {\n\t\t\treturn;\n\t\t}\n\t\tif (file_count === \"single\") {\n\t\t\t_files = [files[0]];\n\t\t}\n\t\tlet all_file_data = await prepare_files(_files);\n\t\tawait tick();\n\n\t\tall_file_data = (\n\t\t\tawait upload(all_file_data, root, undefined, upload_fn)\n\t\t)?.filter((x) => x !== null) as FileData[];\n\t\tvalue = file_count === \"single\" ? all_file_data?.[0] : all_file_data;\n\t\tdispatch(\"change\", value);\n\t\tdispatch(\"upload\", value);\n\t}\n\n\tasync function load_files_from_upload(e: Event): Promise<void> {\n\t\tconst target = e.target as HTMLInputElement;\n\n\t\tif (!target.files) return;\n\t\tawait load_files(target.files);\n\t}\n\n\tfunction clear_input_value(e: Event): void {\n\t\tconst target = e.target as HTMLInputElement;\n\t\tif (target.value) target.value = \"\";\n\t}\n</script>\n\n<input\n\tclass=\"hide\"\n\taccept={accept_file_types}\n\ttype=\"file\"\n\tbind:this={hidden_upload}\n\ton:change={load_files_from_upload}\n\ton:click={clear_input_value}\n\tmultiple={file_count === \"multiple\" || undefined}\n\twebkitdirectory={file_count === \"directory\" || undefined}\n\tmozdirectory={file_count === \"directory\" || undefined}\n\tdata-testid=\"{label}-upload-button\"\n/>\n\n<BaseButton\n\t{size}\n\t{variant}\n\t{elem_id}\n\t{elem_classes}\n\t{visible}\n\ton:click={open_file_upload}\n\t{scale}\n\t{min_width}\n\t{disabled}\n>\n\t{#if icon}\n\t\t<img class=\"button-icon\" src={icon.url} alt={`${value} icon`} />\n\t{/if}\n\t<slot />\n</BaseButton>\n\n<style>\n\t.hide {\n\t\tdisplay: none;\n\t}\n\t.button-icon {\n\t\twidth: var(--text-xl);\n\t\theight: var(--text-xl);\n\t\tmargin-right: var(--spacing-xl);\n\t}\n</style>\n", "<script lang=\"ts\" context=\"module\">\n\texport { default as BaseUploadButton } from \"./shared/UploadButton.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport type { FileData } from \"@gradio/client\";\n\timport UploadButton from \"./shared/UploadButton.svelte\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let label: string | null;\n\texport let value: null | FileData | FileData[];\n\texport let file_count: string;\n\texport let file_types: string[] = [];\n\texport let root: string;\n\texport let size: \"sm\" | \"lg\" = \"lg\";\n\texport let scale: number | null = null;\n\texport let icon: FileData | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let variant: \"primary\" | \"secondary\" | \"stop\" = \"secondary\";\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tupload: never;\n\t\tclick: never;\n\t}>;\n\texport let interactive: boolean;\n\n\t$: disabled = !interactive;\n\n\tasync function handle_event(\n\t\tdetail: null | FileData | FileData[],\n\t\tevent: \"change\" | \"upload\" | \"click\"\n\t): Promise<void> {\n\t\tvalue = detail;\n\t\tgradio.dispatch(event);\n\t}\n</script>\n\n<UploadButton\n\t{elem_id}\n\t{elem_classes}\n\t{visible}\n\t{file_count}\n\t{file_types}\n\t{size}\n\t{scale}\n\t{icon}\n\t{min_width}\n\t{root}\n\t{value}\n\t{disabled}\n\t{variant}\n\t{label}\n\ton:click={() => gradio.dispatch(\"click\")}\n\ton:change={({ detail }) => handle_event(detail, \"change\")}\n\ton:upload={({ detail }) => handle_event(detail, \"upload\")}\n>\n\t{label ? gradio.i18n(label) : \"\"}\n</UploadButton>\n"], "names": ["tick", "createEventDispatcher", "getContext", "src_url_equal", "img", "img_src_value", "ctx", "attr", "insert", "target", "anchor", "dirty", "create_if_block", "input", "clear_input_value", "e", "elem_id", "$$props", "elem_classes", "visible", "label", "value", "file_count", "file_types", "root", "size", "icon", "scale", "min_width", "variant", "disabled", "dispatch", "upload_fn", "hidden_upload", "accept_file_types", "x", "open_file_upload", "load_files", "files", "_files", "all_file_data", "prepare_files", "upload", "load_files_from_upload", "$$value", "set_data", "t", "t_value", "gradio", "interactive", "handle_event", "detail", "event", "$$invalidate", "change_handler", "upload_handler"], "mappings": "mhBACU,CAAA,KAAAA,GAAM,sBAAAC,GAAuB,WAAAC,EAAA,SAA0B,mHA0GjCC,EAAAC,EAAA,IAAAC,EAAAC,KAAK,GAAG,GAAAC,EAAAH,EAAA,MAAAC,CAAA,iBAAUC,EAAK,CAAA,QAAA,UAArDE,EAA+DC,EAAAL,EAAAM,CAAA,UAAjCC,EAAA,KAAA,CAAAR,EAAAC,EAAA,IAAAC,EAAAC,KAAK,GAAG,+BAAUA,EAAK,CAAA,iEADjDA,EAAI,CAAA,GAAAM,EAAAN,CAAA,qIAAJA,EAAI,CAAA,8bALCA,EAAgB,EAAA,CAAA,2FAjBlBA,EAAiB,EAAA,CAAA,kCAKfA,EAAU,CAAA,IAAK,YAAc,+BACtBA,EAAU,CAAA,IAAK,aAAe,MAAS,uBAC1CA,EAAU,CAAA,IAAK,aAAe,MAAS,sBACvCA,EAAK,CAAA,EAAA,gBAAA,UAVpBE,EAWCC,EAAAI,EAAAH,CAAA,sDANWJ,EAAsB,EAAA,CAAA,cACvBQ,EAAiB,8CAJnBR,EAAiB,EAAA,CAAA,oBAKfA,EAAU,CAAA,IAAK,YAAc,2CACtBA,EAAU,CAAA,IAAK,aAAe,qDACjCA,EAAU,CAAA,IAAK,aAAe,kDAC9BA,EAAK,CAAA,EAAA,0ZAhBV,SAAAQ,GAAkBC,EAAQ,OAC5BN,EAASM,EAAE,OACbN,EAAO,QAAOA,EAAO,MAAQ,qDApEvB,CAAA,QAAAO,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,EACZ,CAAA,QAAAE,EAAU,EAAI,EAAAF,GACd,MAAAG,CAAoB,EAAAH,GACpB,MAAAI,CAAmC,EAAAJ,GACnC,WAAAK,CAAkB,EAAAL,GAClB,WAAAM,EAAU,EAAA,EAAAN,GACV,KAAAO,CAAY,EAAAP,EACZ,CAAA,KAAAQ,EAAoB,IAAI,EAAAR,EACxB,CAAA,KAAAS,EAAwB,IAAI,EAAAT,EAC5B,CAAA,MAAAU,EAAuB,IAAI,EAAAV,EAC3B,CAAA,UAAAW,EAAgC,MAAS,EAAAX,EACzC,CAAA,QAAAY,EAA4C,WAAW,EAAAZ,EACvD,CAAA,SAAAa,EAAW,EAAK,EAAAb,EAErB,MAAAc,EAAW9B,KAGX+B,EAAY9B,GAAgC,cAAc,MAE5D+B,EACAC,EAEAX,GAAc,KACjBW,EAAoB,MAEpBX,EAAaA,EAAW,IAAKY,GACxBA,EAAE,WAAW,GAAG,EACZA,EAEDA,EAAI,MAEZD,EAAoBX,EAAW,KAAK,IAAI,YAGhCa,GAAgB,CACxBL,EAAS,OAAO,EAChBE,EAAc,MAAK,EAGL,eAAAI,EAAWC,EAAe,CACpC,IAAAC,EAAiB,MAAM,KAAKD,CAAK,EAChC,GAAA,CAAAA,EAAM,cAGPhB,IAAe,WAClBiB,EAAM,CAAID,EAAM,CAAC,CAAA,OAEdE,EAAa,MAASC,EAAcF,CAAM,QACxCvC,GAAI,EAEVwC,SACOE,EAAOF,EAAehB,EAAM,OAAWQ,CAAS,IACpD,OAAQG,GAAMA,IAAM,IAAI,MAC3Bd,EAAQC,IAAe,SAAWkB,IAAgB,CAAC,EAAIA,CAAa,EACpET,EAAS,SAAUV,CAAK,EACxBU,EAAS,SAAUV,CAAK,EAGV,eAAAsB,EAAuB5B,EAAQ,OACvCN,EAASM,EAAE,OAEZN,EAAO,aACN4B,EAAW5B,EAAO,KAAK,2CAanBwB,EAAaW,w/BC3BvBtC,EAAK,CAAA,EAAGA,EAAM,EAAA,EAAC,KAAKA,EAAK,CAAA,CAAA,EAAI,IAAE,kEAA/BA,EAAK,CAAA,EAAGA,EAAM,EAAA,EAAC,KAAKA,EAAK,CAAA,CAAA,EAAI,IAAE,KAAAuC,GAAAC,EAAAC,CAAA,m5BAlDrB,CAAA,QAAA/B,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,EACZ,CAAA,QAAAE,EAAU,EAAI,EAAAF,GACd,MAAAG,CAAoB,EAAAH,GACpB,MAAAI,CAAmC,EAAAJ,GACnC,WAAAK,CAAkB,EAAAL,GAClB,WAAAM,EAAU,EAAA,EAAAN,GACV,KAAAO,CAAY,EAAAP,EACZ,CAAA,KAAAQ,EAAoB,IAAI,EAAAR,EACxB,CAAA,MAAAU,EAAuB,IAAI,EAAAV,EAC3B,CAAA,KAAAS,EAAwB,IAAI,EAAAT,EAC5B,CAAA,UAAAW,EAAgC,MAAS,EAAAX,EACzC,CAAA,QAAAY,EAA4C,WAAW,EAAAZ,GACvD,OAAA+B,CAIT,EAAA/B,GACS,YAAAgC,CAAoB,EAAAhC,iBAIhBiC,EACdC,EACAC,EAAoC,CAEpCC,EAAA,EAAAhC,EAAQ8B,CAAM,EACdH,EAAO,SAASI,CAAK,cAmBNJ,EAAO,SAAS,OAAO,EACzBM,EAAA,CAAA,CAAA,OAAAH,CAAM,IAAOD,EAAaC,EAAQ,QAAQ,EAC1CI,EAAA,CAAA,CAAA,OAAAJ,CAAM,IAAOD,EAAaC,EAAQ,QAAQ,giBA5BvDE,EAAA,GAAEvB,EAAQ,CAAImB,CAAW"}