import{g as l}from"./Index-26cfc80a.js";function a(t){return t.host===window.location.host||t.host==="localhost:7860"||t.host==="127.0.0.1:7860"||t.host==="lite.local"}function i(t,o){const n=o.toLowerCase();for(const[r,e]of Object.entries(t))if(r.toLowerCase()===n)return e}function u(t){if(t==null)return!1;const o=new URL(t,window.location.href);return!(!a(o)||o.protocol!=="http:"&&o.protocol!=="https:")}async function h(t){if(t==null||!u(t))return t;const o=l();if(o==null)return t;const r=new URL(t,window.location.href).pathname;return o.httpRequest({method:"GET",path:r,headers:{},query_string:""}).then(e=>{if(e.status!==200)throw new Error(`Failed to get file ${r} from the Wasm worker.`);const s=new Blob([e.body],{type:i(e.headers,"content-type")});return URL.createObjectURL(s)})}export{i as g,h as r,u as s};
//# sourceMappingURL=file-url-bef2dc1b.js.map
