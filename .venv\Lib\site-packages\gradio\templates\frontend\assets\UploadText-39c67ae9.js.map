{"version": 3, "file": "UploadText-39c67ae9.js", "sources": ["../../../../js/atoms/src/UploadText.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport { Upload as UploadIcon, ImagePaste } from \"@gradio/icons\";\n\texport let type:\n\t\t| \"video\"\n\t\t| \"image\"\n\t\t| \"audio\"\n\t\t| \"file\"\n\t\t| \"csv\"\n\t\t| \"clipboard\"\n\t\t| \"gallery\" = \"file\";\n\texport let i18n: I18nFormatter;\n\texport let message: string | undefined = undefined;\n\texport let mode: \"full\" | \"short\" = \"full\";\n\texport let hovered = false;\n\n\tconst defs = {\n\t\timage: \"upload_text.drop_image\",\n\t\tvideo: \"upload_text.drop_video\",\n\t\taudio: \"upload_text.drop_audio\",\n\t\tfile: \"upload_text.drop_file\",\n\t\tcsv: \"upload_text.drop_csv\",\n\t\tgallery: \"upload_text.drop_gallery\",\n\t\tclipboard: \"upload_text.paste_clipboard\"\n\t};\n</script>\n\n<div class=\"wrap\">\n\t<span class=\"icon-wrap\" class:hovered>\n\t\t{#if type === \"clipboard\"}\n\t\t\t<ImagePaste />\n\t\t{:else}\n\t\t\t<UploadIcon />\n\t\t{/if}\n\t</span>\n\n\t{i18n(defs[type] || defs.file)}\n\n\t{#if mode !== \"short\"}\n\t\t<span class=\"or\">- {i18n(\"common.or\")} -</span>\n\t\t{message || i18n(\"upload_text.click_to_upload\")}\n\t{/if}\n</div>\n\n<style>\n\t.wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmin-height: var(--size-60);\n\t\tcolor: var(--block-label-text-color);\n\t\tline-height: var(--line-md);\n\t\theight: 100%;\n\t\tpadding-top: var(--size-3);\n\t}\n\n\t.or {\n\t\tcolor: var(--body-text-color-subdued);\n\t\tdisplay: flex;\n\t}\n\n\t.icon-wrap {\n\t\twidth: 30px;\n\t\tmargin-bottom: var(--spacing-lg);\n\t}\n\n\t@media (--screen-md) {\n\t\t.wrap {\n\t\t\tfont-size: var(--text-lg);\n\t\t}\n\t}\n\n\t.hovered {\n\t\tcolor: var(--color-accent);\n\t}\n</style>\n"], "names": ["t1_value", "ctx", "insert", "target", "span", "anchor", "dirty", "set_data", "t1", "t4", "t4_value", "if_block1", "create_if_block", "div", "append", "current", "type", "$$props", "i18n", "message", "mode", "hovered", "defs"], "mappings": "6wBAuCsBA,EAAAC,KAAK,WAAW,EAAA,YACnCA,EAAO,CAAA,GAAIA,EAAI,CAAA,EAAC,6BAA6B,GAAA,gCAD7B,IAAE,aAAmB,IAAE,uDAAxCC,EAA8CC,EAAAC,EAAAC,CAAA,iDAA1BC,EAAA,GAAAN,KAAAA,EAAAC,KAAK,WAAW,EAAA,KAAAM,EAAAC,EAAAR,CAAA,eACnCC,EAAO,CAAA,GAAIA,EAAI,CAAA,EAAC,6BAA6B,GAAA,KAAAM,EAAAE,EAAAC,CAAA,0DAJ9CV,EAAAC,KAAKA,EAAI,CAAA,EAACA,EAAS,CAAA,CAAA,GAAAA,KAAK,IAAI,EAAA,4CAPvB,OAAAA,OAAS,YAAW,0BASrB,IAAAU,EAAAV,OAAS,SAAOW,EAAAX,CAAA,2KAXtBC,EAeKC,EAAAU,EAAAR,CAAA,EAdJS,EAMMD,EAAAT,CAAA,qNAEL,CAAAW,GAAAT,EAAA,IAAAN,KAAAA,EAAAC,KAAKA,EAAI,CAAA,EAACA,EAAS,CAAA,CAAA,GAAAA,KAAK,IAAI,EAAA,KAAAM,EAAAC,EAAAR,CAAA,EAExBC,OAAS,0JAnCH,GAAA,CAAA,KAAAe,EAOI,MAAM,EAAAC,GACV,KAAAC,CAAmB,EAAAD,EACnB,CAAA,QAAAE,EAA8B,MAAS,EAAAF,EACvC,CAAA,KAAAG,EAAyB,MAAM,EAAAH,EAC/B,CAAA,QAAAI,EAAU,EAAK,EAAAJ,QAEpBK,EAAI,CACT,MAAO,yBACP,MAAO,yBACP,MAAO,yBACP,KAAM,wBACN,IAAK,uBACL,QAAS,2BACT,UAAW"}