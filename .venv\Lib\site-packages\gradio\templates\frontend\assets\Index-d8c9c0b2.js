import{B as R}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{B as U}from"./BlockTitle-7f7c9ef8.js";import{S as V}from"./Index-26cfc80a.js";import"./index-a80d931b.js";import"./svelte/svelte.js";import"./Info-84f5385d.js";const{SvelteComponent:W,append:S,assign:X,attr:k,create_component:A,destroy_component:D,destroy_each:Y,detach:j,element:T,ensure_array_like:J,get_spread_object:Z,get_spread_update:y,init:p,insert:B,listen:z,mount_component:F,not_equal:x,run_all:$,set_data:M,space:q,text:N,toggle_class:I,transition_in:G,transition_out:H}=window.__gradio__svelte__internal;function K(e,l,t){const i=e.slice();return i[19]=l[t][0],i[20]=l[t][1],i[22]=t,i}function ee(e){let l;return{c(){l=N(e[9])},m(t,i){B(t,l,i)},p(t,i){i&512&&M(l,t[9])},d(t){t&&j(l)}}}function L(e){let l,t,i,a,f,b,h,c=e[19]+"",d,_,n,u;function g(){return e[16](e[20])}function w(...r){return e[17](e[22],e[20],...r)}function o(...r){return e[18](e[20],e[22],...r)}return{c(){l=T("label"),t=T("input"),b=q(),h=T("span"),d=N(c),_=q(),t.disabled=e[13],t.checked=i=e[0].includes(e[20]),k(t,"type","checkbox"),k(t,"name",a=e[20]?.toString()),k(t,"title",f=e[20]?.toString()),k(t,"class","svelte-1k4wjf2"),k(h,"class","ml-2 svelte-1k4wjf2"),k(l,"class","svelte-1k4wjf2"),I(l,"disabled",e[13]),I(l,"selected",e[0].includes(e[20]))},m(r,m){B(r,l,m),S(l,t),S(l,b),S(l,h),S(h,d),S(l,_),n||(u=[z(t,"change",g),z(t,"input",w),z(t,"keydown",o)],n=!0)},p(r,m){e=r,m&8192&&(t.disabled=e[13]),m&33&&i!==(i=e[0].includes(e[20]))&&(t.checked=i),m&32&&a!==(a=e[20]?.toString())&&k(t,"name",a),m&32&&f!==(f=e[20]?.toString())&&k(t,"title",f),m&32&&c!==(c=e[19]+"")&&M(d,c),m&8192&&I(l,"disabled",e[13]),m&33&&I(l,"selected",e[0].includes(e[20]))},d(r){r&&j(l),n=!1,$(u)}}}function le(e){let l,t,i,a,f,b;const h=[{autoscroll:e[1].autoscroll},{i18n:e[1].i18n},e[12]];let c={};for(let n=0;n<h.length;n+=1)c=X(c,h[n]);l=new V({props:c}),i=new U({props:{show_label:e[11],info:e[10],$$slots:{default:[ee]},$$scope:{ctx:e}}});let d=J(e[5]),_=[];for(let n=0;n<d.length;n+=1)_[n]=L(K(e,d,n));return{c(){A(l.$$.fragment),t=q(),A(i.$$.fragment),a=q(),f=T("div");for(let n=0;n<_.length;n+=1)_[n].c();k(f,"class","wrap svelte-1k4wjf2"),k(f,"data-testid","checkbox-group")},m(n,u){F(l,n,u),B(n,t,u),F(i,n,u),B(n,a,u),B(n,f,u);for(let g=0;g<_.length;g+=1)_[g]&&_[g].m(f,null);b=!0},p(n,u){const g=u&4098?y(h,[u&2&&{autoscroll:n[1].autoscroll},u&2&&{i18n:n[1].i18n},u&4096&&Z(n[12])]):{};l.$set(g);const w={};if(u&2048&&(w.show_label=n[11]),u&1024&&(w.info=n[10]),u&8389120&&(w.$$scope={dirty:u,ctx:n}),i.$set(w),u&24611){d=J(n[5]);let o;for(o=0;o<d.length;o+=1){const r=K(n,d,o);_[o]?_[o].p(r,u):(_[o]=L(r),_[o].c(),_[o].m(f,null))}for(;o<_.length;o+=1)_[o].d(1);_.length=d.length}},i(n){b||(G(l.$$.fragment,n),G(i.$$.fragment,n),b=!0)},o(n){H(l.$$.fragment,n),H(i.$$.fragment,n),b=!1},d(n){n&&(j(t),j(a),j(f)),D(l,n),D(i,n),Y(_,n)}}}function te(e){let l,t;return l=new R({props:{visible:e[4],elem_id:e[2],elem_classes:e[3],type:"fieldset",container:e[6],scale:e[7],min_width:e[8],$$slots:{default:[le]},$$scope:{ctx:e}}}),{c(){A(l.$$.fragment)},m(i,a){F(l,i,a),t=!0},p(i,[a]){const f={};a&16&&(f.visible=i[4]),a&4&&(f.elem_id=i[2]),a&8&&(f.elem_classes=i[3]),a&64&&(f.container=i[6]),a&128&&(f.scale=i[7]),a&256&&(f.min_width=i[8]),a&8404515&&(f.$$scope={dirty:a,ctx:i}),l.$set(f)},i(i){t||(G(l.$$.fragment,i),t=!0)},o(i){H(l.$$.fragment,i),t=!1},d(i){D(l,i)}}}function ne(e,l,t){let i,{gradio:a}=l,{elem_id:f=""}=l,{elem_classes:b=[]}=l,{visible:h=!0}=l,{value:c=[]}=l,{choices:d}=l,{container:_=!0}=l,{scale:n=null}=l,{min_width:u=void 0}=l,{label:g=a.i18n("checkbox.checkbox_group")}=l,{info:w=void 0}=l,{show_label:o=!0}=l,{loading_status:r}=l,{interactive:m=!0}=l;function C(s){c.includes(s)?t(0,c=c.filter(v=>v!==s)):t(0,c=[...c,s]),a.dispatch("input")}const O=s=>C(s),P=(s,v,E)=>a.dispatch("select",{index:s,value:v,selected:E.currentTarget.checked}),Q=(s,v,E)=>{E.key==="Enter"&&(C(s),a.dispatch("select",{index:v,value:s,selected:!c.includes(s)}))};return e.$$set=s=>{"gradio"in s&&t(1,a=s.gradio),"elem_id"in s&&t(2,f=s.elem_id),"elem_classes"in s&&t(3,b=s.elem_classes),"visible"in s&&t(4,h=s.visible),"value"in s&&t(0,c=s.value),"choices"in s&&t(5,d=s.choices),"container"in s&&t(6,_=s.container),"scale"in s&&t(7,n=s.scale),"min_width"in s&&t(8,u=s.min_width),"label"in s&&t(9,g=s.label),"info"in s&&t(10,w=s.info),"show_label"in s&&t(11,o=s.show_label),"loading_status"in s&&t(12,r=s.loading_status),"interactive"in s&&t(15,m=s.interactive)},e.$$.update=()=>{e.$$.dirty&32768&&t(13,i=!m),e.$$.dirty&3&&c&&a.dispatch("change")},[c,a,f,b,h,d,_,n,u,g,w,o,r,i,C,m,O,P,Q]}class ce extends W{constructor(l){super(),p(this,l,ne,te,x,{gradio:1,elem_id:2,elem_classes:3,visible:4,value:0,choices:5,container:6,scale:7,min_width:8,label:9,info:10,show_label:11,loading_status:12,interactive:15})}}export{ce as default};
//# sourceMappingURL=Index-d8c9c0b2.js.map
