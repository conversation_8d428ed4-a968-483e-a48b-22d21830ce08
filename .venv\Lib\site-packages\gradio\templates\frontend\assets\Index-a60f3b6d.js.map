{"version": 3, "file": "Index-a60f3b6d.js", "sources": ["../../../../node_modules/.pnpm/prismjs@1.29.0/node_modules/prismjs/components/prism-typescript.js", "../../../../js/paramviewer/ParamViewer.svelte", "../../../../js/paramviewer/Index.svelte"], "sourcesContent": ["(function (Prism) {\n\n\tPrism.languages.typescript = Prism.languages.extend('javascript', {\n\t\t'class-name': {\n\t\t\tpattern: /(\\b(?:class|extends|implements|instanceof|interface|new|type)\\s+)(?!keyof\\b)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?:\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true,\n\t\t\tinside: null // see below\n\t\t},\n\t\t'builtin': /\\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\\b/,\n\t});\n\n\t// The keywords TypeScript adds to JavaScript\n\tPrism.languages.typescript.keyword.push(\n\t\t/\\b(?:abstract|declare|is|keyof|readonly|require)\\b/,\n\t\t// keywords that have to be followed by an identifier\n\t\t/\\b(?:asserts|infer|interface|module|namespace|type)\\b(?=\\s*(?:[{_$a-zA-Z\\xA0-\\uFFFF]|$))/,\n\t\t// This is for `import type *, {}`\n\t\t/\\btype\\b(?=\\s*(?:[\\{*]|$))/\n\t);\n\n\t// doesn't work with TS because TS is too complex\n\tdelete Prism.languages.typescript['parameter'];\n\tdelete Prism.languages.typescript['literal-property'];\n\n\t// a version of typescript specifically for highlighting types\n\tvar typeInside = Prism.languages.extend('typescript', {});\n\tdelete typeInside['class-name'];\n\n\tPrism.languages.typescript['class-name'].inside = typeInside;\n\n\tPrism.languages.insertBefore('typescript', 'function', {\n\t\t'decorator': {\n\t\t\tpattern: /@[$\\w\\xA0-\\uFFFF]+/,\n\t\t\tinside: {\n\t\t\t\t'at': {\n\t\t\t\t\tpattern: /^@/,\n\t\t\t\t\talias: 'operator'\n\t\t\t\t},\n\t\t\t\t'function': /^[\\s\\S]+/\n\t\t\t}\n\t\t},\n\t\t'generic-function': {\n\t\t\t// e.g. foo<T extends \"bar\" | \"baz\">( ...\n\t\t\tpattern: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\\s*\\()/,\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'function': /^#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*/,\n\t\t\t\t'generic': {\n\t\t\t\t\tpattern: /<[\\s\\S]+/, // everything after the first <\n\t\t\t\t\talias: 'class-name',\n\t\t\t\t\tinside: typeInside\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t});\n\n\tPrism.languages.ts = Prism.languages.typescript;\n\n}(Prism));\n", "<script lang=\"ts\">\n\timport \"./prism.css\";\n\n\timport Prism from \"prismjs\";\n\timport \"prismjs/components/prism-python\";\n\timport \"prismjs/components/prism-typescript\";\n\n\tinterface Param {\n\t\ttype: string | null;\n\t\tdescription: string;\n\t\tdefault: string | null;\n\t\tname?: string;\n\t}\n\n\texport let docs: Record<string, Param>;\n\n\texport let lang: \"python\" | \"typescript\" = \"python\";\n\texport let linkify: string[] = [];\n\tlet _docs: Param[];\n\n\t$: {\n\t\tsetTimeout(() => {\n\t\t\t_docs = highlight_code(docs, lang);\n\t\t}, 0);\n\t}\n\t$: show_desc = _docs && _docs.map((x) => false);\n\n\tfunction highlight(code: string, lang: \"python\" | \"typescript\"): string {\n\t\tlet highlighted = Prism.highlight(code, Prism.languages[lang], lang);\n\n\t\tfor (const link of linkify) {\n\t\t\thighlighted = highlighted.replace(\n\t\t\t\tnew RegExp(link, \"g\"),\n\t\t\t\t`<a href=\"#h-${link.toLocaleLowerCase()}\">${link}</a>`\n\t\t\t);\n\t\t}\n\n\t\treturn highlighted;\n\t}\n\n\tfunction highlight_code(\n\t\t_docs: typeof docs,\n\t\tlang: \"python\" | \"typescript\"\n\t): Param[] {\n\t\treturn Object.entries(_docs).map(\n\t\t\t([name, { type, description, default: _default }]) => {\n\t\t\t\tlet highlighted_type = type ? highlight(type, lang) : null;\n\n\t\t\t\treturn {\n\t\t\t\t\tname: name,\n\t\t\t\t\ttype: highlighted_type,\n\t\t\t\t\tdescription: description,\n\t\t\t\t\tdefault: _default ? highlight(_default, lang) : null\n\t\t\t\t};\n\t\t\t}\n\t\t);\n\t}\n\tlet el = [];\n</script>\n\n<div class=\"wrap\">\n\t{#if _docs}\n\t\t{#each _docs as { type, description, default: _default, name }, i (name)}\n\t\t\t<div class=\"param md\" class:open={show_desc[i]}>\n\t\t\t\t<div class=\"type\">\n\t\t\t\t\t<pre class=\"language-{lang}\"><code bind:this={el[i]}\n\t\t\t\t\t\t\t>{name}{#if type}: {@html type}{/if}</code\n\t\t\t\t\t\t></pre>\n\t\t\t\t\t<button\n\t\t\t\t\t\ton:click={() => (show_desc[i] = !show_desc[i])}\n\t\t\t\t\t\tclass=\"arrow\"\n\t\t\t\t\t\tclass:disabled={!description && !_default}\n\t\t\t\t\t\tclass:hidden={!show_desc[i]}>▲</button\n\t\t\t\t\t>\n\t\t\t\t</div>\n\t\t\t\t{#if show_desc[i]}\n\t\t\t\t\t{#if _default}\n\t\t\t\t\t\t<div class=\"default\" class:last={!description}>\n\t\t\t\t\t\t\t<span style:padding-right={\"4px\"}>default</span>\n\t\t\t\t\t\t\t<code>= {@html _default}</code>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t\t{#if description}\n\t\t\t\t\t\t<div class=\"description\"><p>{description}</p></div>\n\t\t\t\t\t{/if}\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t{/each}\n\t{/if}\n</div>\n\n<style>\n\t.default :global(pre),\n\t.default :global(.highlight) {\n\t\tdisplay: inline-block;\n\t}\n\n\t.disbaled {\n\t\topacity: 0;\n\t}\n\n\t.wrap :global(pre),\n\t.wrap :global(.highlight) {\n\t\tmargin: 0 !important;\n\t\tbackground: transparent !important;\n\t\tfont-family: var(--font-mono);\n\t\tfont-weight: 400;\n\t\tpadding: 0 !important;\n\t}\n\n\t.wrap :global(pre a) {\n\t\tcolor: var(--link-text-color-hover);\n\t\ttext-decoration: underline;\n\t}\n\n\t.wrap :global(pre a:hover) {\n\t\tcolor: var(--link-text-color-hover);\n\t}\n\n\t.default > span {\n\t\ttext-transform: uppercase;\n\t\tfont-size: 0.7rem;\n\t\tfont-weight: 600;\n\t}\n\n\t.default > code {\n\t\tborder: none;\n\t}\n\tcode {\n\t\tbackground: none;\n\t\tfont-family: var(--font-mono);\n\t}\n\n\t.wrap {\n\t\tpadding: 0rem;\n\t\tborder-radius: 5px;\n\t\tborder: 1px solid #eee;\n\t\toverflow: hidden;\n\t\tposition: relative;\n\t\tmargin: 0;\n\t\tbox-shadow: var(--block-shadow);\n\t\tborder-width: var(--block-border-width);\n\t\tborder-color: var(--block-border-color);\n\t\tborder-radius: var(--block-radius);\n\t\tbackground: var(--table-odd-background-fill);\n\t\twidth: 100%;\n\t\tline-height: var(--line-sm);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.type {\n\t\tposition: relative;\n\t\tpadding: 0.7rem 1rem;\n\t\tbackground: var(--table-odd-background-fill);\n\t\tborder-bottom: 0px solid var(--table-border-color);\n\t}\n\n\t.arrow {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tbottom: 0;\n\t\tright: 15px;\n\t\ttransform: rotate(180deg);\n\t\theight: 100;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tcursor: pointer;\n\t}\n\n\t.arrow.hidden {\n\t\ttransform: rotate(270deg);\n\t}\n\n\t.default {\n\t\tpadding: 0.2rem 1rem 0.3rem 1rem;\n\t\tborder-bottom: 1px solid var(--table-border-color);\n\t\tbackground: var(--block-background-fill);\n\t}\n\n\t.default.last {\n\t\tborder-bottom: none;\n\t}\n\n\t.description {\n\t\tpadding: 0.7rem 1rem;\n\t\tfont-size: var(--scale-00);\n\t\tfont-family: var(--font-sans);\n\t\tbackground: var(--block-background-fill);\n\t}\n\n\t.param {\n\t\tborder-bottom: 1px solid var(--table-border-color);\n\t}\n\n\t.param:last-child {\n\t\tborder-bottom: none;\n\t}\n\n\t.param:last-child .description {\n\t\tborder-bottom: none;\n\t}\n\n\t.open .type {\n\t\tborder-bottom-width: 1px;\n\t}\n\n\t.param.md code {\n\t\tbackground: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport ParamViewer from \"./ParamViewer.svelte\";\n\n\texport let value: Record<\n\t\tstring,\n\t\t{\n\t\t\ttype: string;\n\t\t\tdescription: string;\n\t\t\tdefault: string;\n\t\t}\n\t>;\n\n\texport let linkify: string[] = [];\n</script>\n\n<ParamViewer docs={value} {linkify} />\n"], "names": ["Prism", "typeInside", "ctx", "i", "html_tag", "raw_value", "create_if_block_3", "create_if_block_2", "insert", "target", "div", "anchor", "append", "span", "code", "p", "set_data", "t", "t_value", "create_if_block_4", "if_block1", "create_if_block_1", "toggle_class", "button", "div1", "div0", "pre", "t0", "t0_value", "create_if_block", "docs", "$$props", "lang", "linkify", "_docs", "highlight", "highlighted", "link", "highlight_code", "name", "type", "description", "_default", "highlighted_type", "el", "$$value", "click_handler", "$$invalidate", "show_desc", "x", "value"], "mappings": "mIAAC,SAAUA,EAAO,CAEjBA,EAAM,UAAU,WAAaA,EAAM,UAAU,OAAO,aAAc,CACjE,aAAc,CACb,QAAS,+KACT,WAAY,GACZ,OAAQ,GACR,OAAQ,IACR,EACD,QAAW,uFACb,CAAE,EAGDA,EAAM,UAAU,WAAW,QAAQ,KAClC,qDAEA,2FAEA,4BACF,EAGC,OAAOA,EAAM,UAAU,WAAW,UAClC,OAAOA,EAAM,UAAU,WAAW,kBAAkB,EAGpD,IAAIC,EAAaD,EAAM,UAAU,OAAO,aAAc,CAAA,CAAE,EACxD,OAAOC,EAAW,YAAY,EAE9BD,EAAM,UAAU,WAAW,YAAY,EAAE,OAASC,EAElDD,EAAM,UAAU,aAAa,aAAc,WAAY,CACtD,UAAa,CACZ,QAAS,qBACT,OAAQ,CACP,GAAM,CACL,QAAS,KACT,MAAO,UACP,EACD,SAAY,UACZ,CACD,EACD,mBAAoB,CAEnB,QAAS,yGACT,OAAQ,GACR,OAAQ,CACP,SAAY,4DACZ,QAAW,CACV,QAAS,WACT,MAAO,aACP,OAAQC,CACR,CACD,CACD,CACH,CAAE,EAEDD,EAAM,UAAU,GAAKA,EAAM,UAAU,UAEtC,GAAE,KAAK,kdCGEE,EAAK,CAAA,CAAA,aAAuDA,EAAI,EAAA,kBAArE,OAAIC,GAAA,EAAA,oLAACD,EAAK,CAAA,CAAA,yHAImBA,EAAI,EAAA,EAAA,oBAAb,IAAE,mFAAOA,EAAI,EAAA,EAAA,KAAAE,EAAA,EAAAC,CAAA,uDAU3BH,EAAQ,EAAA,GAAAI,EAAAJ,CAAA,IAMRA,EAAW,EAAA,GAAAK,EAAAL,CAAA,qGANXA,EAAQ,EAAA,mEAMRA,EAAW,EAAA,8IAHCA,EAAQ,EAAA,EAAA,mFAAjB,IAAE,gEADmB,KAAK,2FADCA,EAAW,EAAA,CAAA,UAA7CM,EAGKC,EAAAC,EAAAC,CAAA,EAFJC,EAA+CF,EAAAG,CAAA,SAC/CD,EAA8BF,EAAAI,CAAA,sCAAfZ,EAAQ,EAAA,EAAA,KAAAE,EAAA,EAAAC,CAAA,mBAFUH,EAAW,EAAA,CAAA,0CAMhBA,EAAW,EAAA,EAAA,6FAAxCM,EAAkDC,EAAAC,EAAAC,CAAA,EAAzBC,EAAoBF,EAAAK,CAAA,6BAAhBb,EAAW,EAAA,EAAA,KAAAc,EAAAC,EAAAC,CAAA,gDAjBrChB,EAAI,EAAA,EAAA,6BAAMA,EAAI,EAAA,GAAAiB,EAAAjB,CAAA,2EASd,IAAAkB,EAAAlB,KAAUA,EAAC,EAAA,CAAA,GAAAmB,EAAAnB,CAAA,8MAVOA,EAAI,CAAA,EAAA,iBAAA,sCAMRoB,EAAAC,EAAA,WAAA,CAAArB,QAAgBA,EAAQ,EAAA,CAAA,EAC1BoB,EAAAC,EAAA,SAAA,CAAArB,KAAUA,EAAC,EAAA,CAAA,CAAA,4EATKoB,EAAAE,EAAA,OAAAtB,KAAUA,EAAC,EAAA,CAAA,CAAA,uBAA7CM,EAuBKC,EAAAe,EAAAb,CAAA,EAtBJC,EAUKY,EAAAC,CAAA,EATJb,EAEOa,EAAAC,CAAA,EAFsBd,EAE3Bc,EAAAZ,CAAA,mCACFF,EAKAa,EAAAF,CAAA,kFAPIrB,EAAI,EAAA,EAAA,KAAAc,EAAAW,EAAAC,CAAA,EAAM1B,EAAI,EAAA,gHADIA,EAAI,CAAA,EAAA,wCAMRoB,EAAAC,EAAA,WAAA,CAAArB,QAAgBA,EAAQ,EAAA,CAAA,QAC1BoB,EAAAC,EAAA,SAAA,CAAArB,KAAUA,EAAC,EAAA,CAAA,CAAA,EAGvBA,KAAUA,EAAC,EAAA,CAAA,8DAZiBoB,EAAAE,EAAA,OAAAtB,KAAUA,EAAC,EAAA,CAAA,CAAA,uEAF1CA,EAAK,CAAA,GAAA2B,EAAA3B,CAAA,4EADXM,EA6BKC,EAAAC,EAAAC,CAAA,2BA5BCT,EAAK,CAAA,qHA/CC,KAAA4B,CAA2B,EAAAC,EAE3B,CAAA,KAAAC,EAAgC,QAAQ,EAAAD,GACxC,QAAAE,EAAO,EAAA,EAAAF,EACdG,WASKC,EAAUrB,EAAckB,EAA6B,CACzD,IAAAI,EAAcpC,EAAM,UAAUc,EAAMd,EAAM,UAAUgC,CAAI,EAAGA,CAAI,EAExD,UAAAK,KAAQJ,EAClBG,EAAcA,EAAY,YACrB,OAAOC,EAAM,GAAG,EACL,eAAAA,EAAK,wBAAwBA,OAAI,SAI3CD,WAGCE,EACRJ,EACAF,EAA6B,CAEtB,OAAA,OAAO,QAAQE,CAAK,EAAE,IAAG,CAAA,CAC7BK,EAAI,CAAI,KAAAC,EAAM,YAAAC,EAAa,QAASC,CAAQ,CAAA,IAAA,KACzCC,EAAmBH,EAAOL,EAAUK,EAAMR,CAAI,EAAI,YAG/C,KAAAO,EACN,KAAMI,EACO,YAAAF,EACb,QAASC,EAAWP,EAAUO,EAAUV,CAAI,EAAI,YAKhDY,EAAE,CAAA,6CAQ4CA,EAAGzC,CAAC,EAAA0C,WAIhC,MAAAC,EAAA3C,GAAA4C,EAAA,EAAAC,EAAU7C,CAAC,EAAK,CAAA6C,EAAU7C,CAAC,EAAA6C,CAAA,0IAhDhD,gBACCD,EAAA,EAAAb,EAAQI,EAAeR,EAAME,CAAI,CAAA,GAC/B,iBAEHe,EAAA,EAAEC,EAAYd,GAASA,EAAM,IAAKe,GAAM,EAAK,CAAA,mVCV5B/C,EAAK,CAAA,kGAALA,EAAK,CAAA,6IAZZ,MAAAgD,CAOV,EAAAnB,GAEU,QAAAE,EAAO,EAAA,EAAAF", "x_google_ignoreList": [0]}