{"version": 3, "file": "Index-0bdb78da.js", "sources": ["../../../../js/checkbox/shared/Checkbox.svelte", "../../../../js/checkbox/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { createEventDispatcher } from \"svelte\";\n\n\texport let value = false;\n\texport let label = \"Checkbox\";\n\texport let interactive: boolean;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: boolean;\n\t\tselect: SelectData;\n\t}>();\n\n\t// When the value changes, dispatch the change event via handle_change()\n\t// See the docs for an explanation: https://svelte.dev/docs/svelte-components#script-3-$-marks-a-statement-as-reactive\n\t$: value, dispatch(\"change\", value);\n\t$: disabled = !interactive;\n\n\tasync function handle_enter(\n\t\tevent: KeyboardEvent & { currentTarget: EventTarget & HTMLInputElement }\n\t): Promise<void> {\n\t\tif (event.key === \"Enter\") {\n\t\t\tvalue = !value;\n\t\t\tdispatch(\"select\", {\n\t\t\t\tindex: 0,\n\t\t\t\tvalue: event.currentTarget.checked,\n\t\t\t\tselected: event.currentTarget.checked\n\t\t\t});\n\t\t}\n\t}\n\n\tasync function handle_input(\n\t\tevent: Event & { currentTarget: EventTarget & HTMLInputElement }\n\t): Promise<void> {\n\t\tvalue = event.currentTarget.checked;\n\t\tdispatch(\"select\", {\n\t\t\tindex: 0,\n\t\t\tvalue: event.currentTarget.checked,\n\t\t\tselected: event.currentTarget.checked\n\t\t});\n\t}\n</script>\n\n<label class:disabled>\n\t<input\n\t\tbind:checked={value}\n\t\ton:keydown={handle_enter}\n\t\ton:input={handle_input}\n\t\t{disabled}\n\t\ttype=\"checkbox\"\n\t\tname=\"test\"\n\t\tdata-testid=\"checkbox\"\n\t/>\n\t<span class=\"ml-2\">{label}</span>\n</label>\n\n<style>\n\tlabel {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tcursor: pointer;\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--checkbox-label-text-weight);\n\t\tfont-size: var(--checkbox-label-text-size);\n\t\tline-height: var(--line-md);\n\t}\n\n\tlabel > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\tinput {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\tbox-shadow: var(--input-shadow);\n\t\tborder: 1px solid var(--checkbox-border-color);\n\t\tborder-radius: var(--checkbox-border-radius);\n\t\tbackground-color: var(--checkbox-background-color);\n\t\tline-height: var(--line-sm);\n\t}\n\n\tinput:checked,\n\tinput:checked:hover,\n\tinput:checked:focus {\n\t\tborder-color: var(--checkbox-border-color-selected);\n\t\tbackground-image: var(--checkbox-check);\n\t\tbackground-color: var(--checkbox-background-color-selected);\n\t}\n\n\tinput:checked:focus {\n\t\tbackground-image: var(--checkbox-check);\n\t\tbackground-color: var(--checkbox-background-color-selected);\n\t\tborder-color: var(--checkbox-border-color-focus);\n\t}\n\n\tinput:hover {\n\t\tborder-color: var(--checkbox-border-color-hover);\n\t\tbackground-color: var(--checkbox-background-color-hover);\n\t}\n\n\tinput:focus {\n\t\tborder-color: var(--checkbox-border-color-focus);\n\t\tbackground-color: var(--checkbox-background-color-focus);\n\t}\n\n\tinput[disabled],\n\t.disabled {\n\t\tcursor: not-allowed;\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseCheckbox } from \"./shared/Checkbox.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport { Block, Info } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { afterUpdate } from \"svelte\";\n\timport BaseCheckbox from \"./shared/Checkbox.svelte\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value = false;\n\texport let value_is_output = false;\n\texport let label = \"Checkbox\";\n\texport let info: string | undefined = undefined;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tselect: SelectData;\n\t\tinput: never;\n\t}>;\n\texport let interactive: boolean;\n\n\tfunction handle_change(): void {\n\t\tgradio.dispatch(\"change\");\n\t\tif (!value_is_output) {\n\t\t\tgradio.dispatch(\"input\");\n\t\t}\n\t}\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\n\t// When the value changes, dispatch the change event via handle_change()\n\t// See the docs for an explanation: https://svelte.dev/docs/svelte-components#script-3-$-marks-a-statement-as-reactive\n</script>\n\n<Block {visible} {elem_id} {elem_classes} {container} {scale} {min_width}>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t/>\n\n\t{#if info}\n\t\t<Info>{info}</Info>\n\t{/if}\n\n\t<BaseCheckbox\n\t\tbind:value\n\t\t{label}\n\t\t{interactive}\n\t\ton:change={handle_change}\n\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t/>\n</Block>\n"], "names": ["createEventDispatcher", "ctx", "insert", "target", "label_1", "anchor", "append", "input", "span", "value", "$$props", "label", "interactive", "dispatch", "handle_enter", "event", "$$invalidate", "handle_input", "disabled", "afterUpdate", "create_if_block", "dirty", "elem_id", "elem_classes", "visible", "value_is_output", "info", "container", "scale", "min_width", "loading_status", "gradio", "handle_change", "e"], "mappings": "qZAEU,CAAA,sBAAAA,CAAA,SAAqC,sHAmD1BC,EAAK,CAAA,CAAA,mNAV1BC,EAWOC,EAAAC,EAAAC,CAAA,EAVNC,EAQCF,EAAAG,CAAA,YAPcN,EAAK,CAAA,SAQpBK,EAAgCF,EAAAI,CAAA,iDAPnBP,EAAY,CAAA,CAAA,cACdA,EAAY,CAAA,CAAA,0DAFRA,EAAK,CAAA,YAQAA,EAAK,CAAA,CAAA,sFAjDd,CAAA,MAAAQ,EAAQ,EAAK,EAAAC,EACb,CAAA,MAAAC,EAAQ,UAAU,EAAAD,GAClB,YAAAE,CAAoB,EAAAF,EAEzB,MAAAG,EAAWb,IAUF,eAAAc,EACdC,EAAwE,CAEpEA,EAAM,MAAQ,UACjBC,EAAA,EAAAP,GAASA,CAAK,EACdI,EAAS,SAAQ,CAChB,MAAO,EACP,MAAOE,EAAM,cAAc,QAC3B,SAAUA,EAAM,cAAc,WAKlB,eAAAE,EACdF,EAAgE,CAEhEC,EAAA,EAAAP,EAAQM,EAAM,cAAc,OAAO,EACnCF,EAAS,SAAQ,CAChB,MAAO,EACP,MAAOE,EAAM,cAAc,QAC3B,SAAUA,EAAM,cAAc,uBAOjBN,EAAK,KAAA,kKA9BVI,EAAS,SAAUJ,CAAK,iBACjCO,EAAA,EAAEE,EAAQ,CAAIN,CAAW,qdCNjB,CAAA,YAAAO,WAA2B,qVA2C5BlB,EAAI,CAAA,CAAA,sCAAJA,EAAI,CAAA,CAAA,2DANC,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,8EAGdA,EAAI,CAAA,GAAAmB,EAAAnB,CAAA,kKAQGA,EAAa,EAAA,CAAA,uLAbZ,WAAAA,MAAO,YACboB,EAAA,MAAA,CAAA,KAAApB,MAAO,IAAI,YACbA,EAAc,CAAA,CAAA,iBAGdA,EAAI,CAAA,44BAvCE,GAAA,CAAA,QAAAqB,EAAU,EAAE,EAAAZ,GACZ,aAAAa,EAAY,EAAA,EAAAb,EACZ,CAAA,QAAAc,EAAU,EAAI,EAAAd,EACd,CAAA,MAAAD,EAAQ,EAAK,EAAAC,EACb,CAAA,gBAAAe,EAAkB,EAAK,EAAAf,EACvB,CAAA,MAAAC,EAAQ,UAAU,EAAAD,EAClB,CAAA,KAAAgB,EAA2B,MAAS,EAAAhB,EACpC,CAAA,UAAAiB,EAAY,EAAI,EAAAjB,EAChB,CAAA,MAAAkB,EAAuB,IAAI,EAAAlB,EAC3B,CAAA,UAAAmB,EAAgC,MAAS,EAAAnB,GACzC,eAAAoB,CAA6B,EAAApB,GAC7B,OAAAqB,CAIT,EAAArB,GACS,YAAAE,CAAoB,EAAAF,WAEtBsB,GAAa,CACrBD,EAAO,SAAS,QAAQ,EACnBN,GACJM,EAAO,SAAS,OAAO,EAGzBZ,GAAW,IAAA,CACVH,EAAA,GAAAS,EAAkB,EAAK,qCAuBXQ,GAAMF,EAAO,SAAS,SAAUE,EAAE,MAAM"}