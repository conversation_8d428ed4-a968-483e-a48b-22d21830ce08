{"version": 3, "mappings": "+NAaO,SAASA,GAAKC,EAAM,CAAE,KAAAC,EAAM,GAAAC,CAAI,EAAEC,EAAS,GAAI,CACrD,MAAMC,EAAQ,iBAAiBJ,CAAI,EAC7BK,EAAYD,EAAM,YAAc,OAAS,GAAKA,EAAM,UACpD,CAACE,EAAIC,CAAE,EAAIH,EAAM,gBAAgB,MAAM,GAAG,EAAE,IAAI,UAAU,EAC1DI,EAAKP,EAAK,KAAQA,EAAK,MAAQK,EAAMJ,EAAG,OAASA,EAAG,KAAOI,GAC3DG,EAAKR,EAAK,IAAOA,EAAK,OAASM,EAAML,EAAG,QAAUA,EAAG,IAAMK,GAC3D,CAAE,MAAAG,EAAQ,EAAG,SAAAC,EAAYC,GAAM,KAAK,KAAKA,CAAC,EAAI,IAAK,OAAAC,EAASC,EAAQ,EAAKX,EAC/E,MAAO,CACN,MAAAO,EACA,SAAUK,GAAYJ,CAAQ,EAAIA,EAAS,KAAK,KAAKH,EAAKA,EAAKC,EAAKA,CAAE,CAAC,EAAIE,EAC3E,OAAAE,EACA,IAAK,CAACG,EAAGC,IAAM,CACd,MAAMC,EAAID,EAAIT,EACRW,EAAIF,EAAIR,EACRW,EAAKJ,EAAKC,EAAIhB,EAAK,MAASC,EAAG,MAC/BmB,EAAKL,EAAKC,EAAIhB,EAAK,OAAUC,EAAG,OACtC,MAAO,cAAcG,eAAuBa,QAAQC,cAAcC,MAAOC,KACzE,CACH,CACA,koBChCAC,GAiBKC,EAAAC,EAAAC,CAAA,EALJC,GAICF,EAAAG,CAAA,uzBChBFL,GAiBKC,EAAAC,EAAAC,CAAA,EALJC,GAICF,EAAAG,CAAA,g1BChBFL,GAiBKC,EAAAC,EAAAC,CAAA,EALJC,GAICF,EAAAG,CAAA,8hBCd6B,QAAAC,EAAS,SAAgB,mpBAiCjD,OAAAC,OAAS,UAAS,EAEbA,OAAS,OAAM,EAEfA,OAAS,QAAO,kHAMOA,EAAI,4BAEnCA,EAAO,wGAbcA,EAAI,kDAWFA,EAAI,iDACLA,EAAI,oDAFFA,EAAI,4EASVA,EAAI,4HAQNA,EAAI,iDAnCJA,EAAI,iFADxBP,GAqCKC,EAAAO,EAAAL,CAAA,EA5BJC,GAQKI,EAAAC,CAAA,6BAELL,GAKKI,EAAAE,CAAA,EAJJN,GAA2CM,EAAAC,CAAA,kBAC3CP,GAEKM,EAAAE,CAAA,kBAGNR,GAQQI,EAAAK,CAAA,EADPT,GAAqCS,EAAAC,CAAA,UAGtCV,GAA2BI,EAAAO,CAAA,2BAThBR,EAAa,0OAlBAA,EAAI,sDAWMA,EAAI,oCAAZA,EAAI,sDAE3BA,EAAO,mCADeA,EAAI,wEAFFA,EAAI,sEASVA,EAAI,gEAQNA,EAAI,qEAnCJA,EAAI,iFAKZS,EAAAC,GAAAT,EAAAU,GAAA,UAAU,IAAK,MAAO,GAAG,wDACxBC,EAAAC,GAAAZ,EAAAU,GAAA,UAAU,GAAG,mFA1Bd,YAAAG,EAAU,EAAE,EAAAC,GACZ,KAAAC,CAA0B,EAAAD,GAC1B,GAAAE,CAAU,EAAAF,EAEf,MAAAG,EAAWC,cAERC,GAAa,CACrBF,EAAS,QAASD,CAAE,EAGrBlB,GAAO,KACN,gBACCqB,KACE,m+BCCH3B,GAEKC,EAAA2B,EAAAzB,CAAA,gLAFgB0B,EAAAC,GAAAF,EAAAG,EAAAtD,GAAA,UAAU,GAAG,wIAD5B8B,EAAQ,eAA2BA,EAAE,mBAA1C,OAAIyB,GAAA,6JADPhC,GAMKC,EAAA2B,EAAAzB,CAAA,+EALGI,EAAQ,0JAAb,OAAIyB,GAAA,uHAVG,SAAAC,GAAcC,EAAyB,CAC3CA,EAAU,OAAS,GAClB,iBAAkB,QACrB,OAAO,cAAc,SAAS,EAAG,CAAC,yBAP1B,SAAAC,EAAQ,IAAAb,uHAEhBW,GAAcE,CAAQ,inCCP1BnC,GAkBKC,EAAAC,EAAAC,CAAA,EARJC,GAOGF,EAAAkC,CAAA,EANFhC,GAECgC,EAAAC,CAAA,EACDjC,GAECgC,EAAAE,CAAA,oXCfO,uBAAAZ,EAAA,SAAqC,2KAU1B;AAAA,GAEnB,oBACEnB,EAAI;;;;;;;;;wMALRP,GAwBKC,EAAA2B,EAAAzB,CAAA,EAvBJC,GAAgBwB,EAAAW,CAAA,UAChBnC,GAKGwB,EAAAY,CAAA,UAHFpC,GAEMoC,EAAAC,CAAA,kBAEPrC,GAeGwB,EAAAc,CAAA,YAGJ1C,GAEQC,EAAAY,EAAAV,CAAA,6EAvBJI,EAAI,wIAVD,MAAAkB,EAAWC,SAEN,KAAAiB,CAAY,EAAArB,EA6BA,MAAAsB,EAAA,IAAAnB,EAAS,OAAO,iIClCjC,SAASoB,GACfC,EACAvB,EACAwB,EAA2B,KACkC,CAC7D,OAAIxB,IAAS,OACLwB,IAAS,KAAO,OAAS,KAE7BD,IAAU,MAAQC,IAAS,KACvB,OAEJxB,IAAS,UAAYA,IAAS,MAC1BwB,IAAS,KAAOD,EAAQ,IAAMA,EAAQ,IACnCvB,IAAS,SACZwB,IAAS,KAAO,WAAWD,CAAK,EAAIA,EACjCvB,IAAS,WAAaA,GAAQ,OACpCwB,IAAS,MACZD,EAAQ,OAAOA,CAAK,EACbA,IAAU,OAAS,OAAS,SACzBC,IAAS,KACZD,EAEDA,IAAU,OACPvB,IAAS,aACXuB,EAAA,KAAK,UAAUA,CAAK,EACrBA,GACGvB,EAAK,WAAW,WAAW,EAE9B,IAAMuB,EAAQ,IAGlBC,IAAS,KACLD,IAAU,GAAK,KAAO,KAAK,MAAMA,CAAK,EACnC,OAAOA,GAAU,SACvBA,IAAU,GACNC,IAAS,KAAO,OAAS,OAE1BD,GAEJC,IAAS,OACZD,EAAQE,GAAqCF,CAAK,GAE5CG,GAA+BH,CAAK,EAC5C,CAEO,SAASI,GAAgCC,EAAmB,CAClE,GAAI,OAAOA,GAAQ,UAAYA,IAAQ,MAClCA,EAAI,eAAe,KAAK,GAAKA,EAAI,eAAe,MAAM,GAExD,OAAOA,EAAI,MAAS,UACpBA,EAAI,OAAS,MACbA,EAAI,KAAK,QAAU,kBAEZ,SAIV,GAAI,OAAOA,GAAQ,UAAYA,IAAQ,MACtC,QAASC,KAAOD,EACf,GAAI,OAAOA,EAAIC,CAAG,GAAM,UACVF,GAAgCC,EAAIC,CAAG,CAAC,EAE7C,SAKJ,QACR,CAEA,SAASJ,GAAqCG,EAAe,CACxD,cAAOA,GAAQ,UAAYA,IAAQ,MAAQ,CAAC,MAAM,QAAQA,CAAG,GAE/D,QAASA,GACT,SAAUA,GACVA,EAAI,MAAM,QAAU,kBAEb,SAASA,EAAI,SAGlB,MAAM,QAAQA,CAAG,EAChBA,EAAA,QAAQ,CAACE,EAAMC,IAAU,CACxB,OAAOD,GAAS,UAAYA,IAAS,OACpCF,EAAAG,CAAK,EAAIN,GAAqCK,CAAI,EACvD,CACA,EACS,OAAOF,GAAQ,UAAYA,IAAQ,MAC7C,OAAO,KAAKA,CAAG,EAAE,QAASC,GAAQ,CACjCD,EAAIC,CAAG,EAAIJ,GAAqCG,EAAIC,CAAG,CAAC,EACxD,EAEKD,EACR,CAEA,SAASF,GAA+BE,EAAkB,CACzD,MAAMI,EAAa,KAAK,UAAUJ,EAAK,CAACC,EAAKN,IAE3C,OAAOA,GAAU,UACjBA,EAAM,WAAW,OAAO,GACxBA,EAAM,SAAS,GAAG,EAEX,WAAWA,IAEZA,CACP,EACKU,EAAQ,6BACd,OAAOD,EAAW,QAAQC,EAAO,CAACC,EAAOf,IAAO,QAAQA,IAAK,CAC9D,CC5GA,MAAegB,GAAA,uVCCL,uBAAAhC,EAAA,SAAqC,gEAmBuB,GAAC,8FAAfiC,EAAApD,KAAY,GAACqD,GAAA,0EAPhE;AAAA,GAEH,mBACErD,EAAI,0CAIaA,EAAS,SAAQ,eAAa,8DARxCmD,EAAQ,GAAAG,GAAAC,EAAA,MAAAC,CAAA,4NADnB/D,GAWIC,EAAA+D,EAAA7D,CAAA,EAVHC,GAA4B4D,EAAAF,CAAA,UAC5B1D,GAKK4D,EAAArD,CAAA,UAHJP,GAEKO,EAAAF,CAAA,kBAENL,GAEM4D,EAAAC,CAAA,EADL7D,GAAoC6D,EAAAC,CAAA,2CAItClE,GAEQC,EAAAY,EAAAV,CAAA,6EAVJI,EAAI,oBAIaA,EAAS,IAA0BA,KAAY,oMAfxD,KAAAoC,CAAY,EAAArB,GACZ,UAAA6C,CAAiB,EAAA7C,EAEtB,MAAAG,EAAWC,KAgBMkB,EAAA,IAAAnB,EAAS,OAAO,mxBCVsC,GAAC,0DAevDlB,EAAU,GAACA,EAAC,KAAE,KAAI,8DAAlBA,EAAU,GAACA,EAAC,KAAE,KAAI,KAAA6D,GAAA,EAAAC,CAAA,kCADA,IAAAA,EAAA9D,KAAY,KAAI,SAAMA,EAAqB,IAAIA,EAAiB,KAAK,MAAI+D,GAAA,qFAAzEC,EAAA,GAAAF,OAAA9D,KAAY,KAAI,KAAA6D,GAAA,EAAAC,CAAA,EAAM9D,EAAqB,IAAIA,EAAiB,KAAK,sIAAK;AAAA,YACzG,4DAOHiE,EAAA3B,GAAgBtC,EAAiB,GAAEA,EAAY,QAAM,IAAI,6JAHpDP,GAAuBC,EAAAiE,EAAA/D,CAAA,EAAAH,GAI9BC,EAAAgE,EAAA9D,CAAA,kBADEoE,EAAA,GAAAC,OAAA3B,GAAgBtC,EAAiB,GAAEA,EAAY,QAAM,IAAI,OAAA6D,GAAAK,EAAAD,CAAA,oIALGxE,GAE9DC,EAAAa,EAAAX,CAAA,wDAVCI,EAAc,IAAIA,EAAgB,IAAI,SACrCA,EAAA,GACA,IAAMA,EAAC,IAAG,KAAG,mBAeyBA,EAAK,YAC9CA,EAAS,4BAbH,OAAAA,OAAqB,SAAQmE,+CAG9BnE,EAAqB,IAAIA,EAAgB,IAAI,aAAYoE,6JAQjD,2CAC2B,eAAO,IAAE,eAGlD;AAAA,eACD,8SAzBD3E,GAAgBC,EAAA2E,EAAAzE,CAAA,YAChBH,GAyBKC,EAAA2B,EAAAzB,CAAA,EAxBJC,GAiBGwB,EAAAY,CAAA,EAhBFpC,GAIAoC,EAAA0B,CAAA,kBACA9D,GAGAoC,EAAAyB,CAAA,0CASD7D,GAKGwB,EAAAc,CAAA,wEArBCnC,EAAc,IAAIA,EAAgB,IAAI,SACrCA,EAAA,GACA,IAAMA,EAAC,IAAG,KAAG,KAAA6D,GAAAK,EAAAD,CAAA,8IAeyBjE,EAAK,QAAA6D,GAAAS,EAAAC,CAAA,cAC9CvE,EAAS,QAAA6D,GAAAW,EAAAC,CAAA,qGASI,EAAK,yEADtBhF,GAEKC,EAAA2B,EAAAzB,CAAA,uIArCIqE,EAAAjE,KAAiB,OAAM,qBAAgBA,EAAgB,GAAC,QAAU,GAAC0E,GAAA,OAIrE1E,EAAgB,yBAArB,OAAIyB,GAAA,2BA8BFzB,EAAU,IAAAqD,GAAA,qGAnCT;AAAA,UACG,eAAyB,YAAU,gBAAwC,GACpF,uKAEiBrD,EAAU,YAP3BP,GAKIC,EAAAiF,EAAA/E,CAAA,EAJHC,GAEK8E,EAAAvE,CAAA,2DAINX,GA8BKC,EAAAW,EAAAT,CAAA,sGAjCK,CAAAgF,GAAAZ,EAAA,IAAAC,OAAAjE,KAAiB,OAAM,KAAA6D,GAAAK,EAAAD,CAAA,EAAgBjE,EAAgB,GAAC,QAAU,2DAIpEA,EAAgB,sBAArB,OAAIyB,GAAA,qHAAJ,8BADczB,EAAU,IA+BtBA,EAAU,uOA5CH,WAAA6E,CAAmB,EAAA9D,GACnB,iBAAA+D,CAAqB,EAAA/D,GACrB,WAAAgE,CAAe,EAAAhE,GACf,iBAAAiE,CAAyC,EAAAjE,olBCQnDf,EAAS,wCAATA,EAAS,sIADqBA,EAAI,4NAZxB,KAAAiF,CAAY,EAAAlE,EACnBmE,EAAY,gBAEPC,GAAI,CACZ,UAAU,UAAU,UAAUF,CAAI,EAClCG,EAAA,EAAAF,EAAY,SAAS,EACrB,gBACCE,EAAA,EAAAF,EAAY,MAAM,GAChB,wdCWiBG,EAAU,yFAGpBA,8EAJT5F,GAEKC,EAAAQ,EAAAN,CAAA,yBACLH,GAEKC,EAAAU,EAAAR,CAAA,EADJC,GAAwBO,EAAAkF,CAAA,gLAVNC,EAAU,yFAGpBA,8EAJT9F,GAEKC,EAAAQ,EAAAN,CAAA,yBACLH,GAEKC,EAAAU,EAAAR,CAAA,EADJC,GAAwBO,EAAAkF,CAAA,oLALrB,OAAAtF,OAAqB,SAAQ,EAOxBA,OAAqB,aAAY,wGAR5CP,GAgBMC,EAAAuF,EAAArF,CAAA,ojBArBF,IAAA2F,GAAa,4BACbF,GAAa,iDAHN,iBAAAL,CAAyC,EAAAjE,mYCQjD;AAAA,GAEF,oBAAoBf,EAAQ,6EAF7BP,GAGIC,EAAA8F,EAAA5F,CAAA,UADHC,GAAmC2F,EAAAjF,CAAA,4BAAfP,EAAQ,8CALRiE,EAAA,IAAMjE,EAAQ,gCAFhC;AAAA,GAEF,gGAFDP,GAGIC,EAAA8F,EAAA5F,CAAA,UADHC,GAAyC2F,EAAAjF,CAAA,kBAArByD,EAAA,GAAAC,OAAA,IAAMjE,EAAQ,KAAA6D,GAAAK,EAAAD,CAAA,+DAH/BjE,EAAK,GAAAqD,kNALE,aAAAoC,EAA0B,IAAI,EAAA1E,EAC9B,UAAA2E,EAA0B,IAAI,EAAA3E,GAC9B,MAAA4E,CAAc,EAAA5E,6qCCsCUf,EAAgB,6GAAhBA,EAAgB,2JAFhB,SAAAA,KAAW,kGAAXgE,EAAA,IAAA4B,EAAA,SAAA5F,KAAW,kKAsCxB,YAAAA,MAAS,SAAS,aAIlCA,EAAa,0BAAlB,OAAIyB,GAAA,4CAOgCzB,EAAK,GAAA6F,6BACjC7F,EAAmB,yBAAxB,OAAIyB,GAAA,iGATC;AAAA,CACV,2CAKE;AAAA,0BACuB,mBAA2B,GAAC,MAACzB,EAAI,QAAC,GAAC,MAAO;AAAA,kCAClC,YAAiE,KAAG,2CAiCzF;AAAA;AAAA;AAAA;AAAA,CAIZ,yHAjDIP,EAEKC,EAAAQ,EAAAN,CAAA,wBACLH,EA+CKC,EAAAU,EAAAR,CAAA,EA9CJC,EA6CAO,EAAAkF,CAAA,iEAtCoBzF,EAA0CyF,EAAA/E,CAAA,6IAV5CyD,EAAA,MAAA8B,EAAA,KAAA9F,MAAS,kCAIzBA,EAAa,uBAAlB,OAAIyB,GAAA,kHAAJ,0BAMoDzB,EAAI,8EAEhDA,EAAmB,sBAAxB,OAAIyB,GAAA,kHAAJ,0MAnBgCsE,EAAA/F,KAAW,SAAQ,yBAvBjC,YAAAA,MAAa,SAAS,UAK1BA,EAAa,IAAAoE,GAAA,QAKtBpE,EAAmB,0BAAxB,OAAIyB,GAAA,oIAPoC,iBAAe,0CAErD,SAAO,eAA6B;AAAA;AAAA,iBAE3B,mBAA2B,GAAC,MAACzB,EAAI,QAAC,GAAC,MAAO;AAAA,iBAC1C,2CACT,GAAC,2CAaJ;AAAA,YACO,mBAAuB,IAAE,aAAqB,GAAC,MACtD;AAAA;AAAA,CAEJ,yCAAoC,UAAQ,yQA3BxCP,EAEKC,EAAAQ,EAAAN,CAAA,wBACLH,EAyBKC,EAAAU,EAAAR,CAAA,EAxBJC,EAuB4CO,EAAAkF,CAAA,EAvBvCzF,EAAmCyF,EAAA3B,CAAA,SAAe9D,EAErDyF,EAAA5B,CAAA,+BAES7D,EAA0CyF,EAAAU,CAAA,8BAC1CnG,EACTyF,EAAAW,CAAA,iEAcIpG,EAAsDyF,EAAAY,CAAA,8BAGjErG,EAAoCyF,EAAAa,CAAA,6CA1BbnC,EAAA,MAAA8B,EAAA,KAAA9F,MAAa,qCAOSA,EAAI,eAGvCA,EAAmB,uBAAxB,OAAIyB,GAAA,oHAAJ,QAagC,CAAAmD,GAAAZ,EAAA,IAAA+B,OAAA/F,KAAW,SAAQ,KAAA6D,GAAAuC,EAAAL,CAAA,6JAarBM,EAAArG,MAAc,IAAG,SACtCA,EAAS,8BAFrB;AAAA,gBACa,MAACA,EAAC,SAAC,kBAAgB,aAAmB;AAAA,cACxC,aAAW,oBAAkB,MAACA,EAAC,SAAC;AAAA,OACvC,8LAGsEA,EAAgB,uCAAhBA,EAAgB,yCAA7CiE,EAAAjE,KAAW,SAAQ,sBAAtB,IAAE,aAAqB,GAAC,6CAArBgE,EAAA,GAAAC,OAAAjE,KAAW,SAAQ,KAAA6D,GAAAK,EAAAD,CAAA,qDAiBvDA,EAAA3B,GACDtC,EAAa,IACbA,EAAY,SACZ,IAAG,eAKNA,EAAa,GAACA,EAAC,KACX,KAAI,WAENA,EAAK,aACVA,EAAS,aAHSA,EAAa,GAACA,EAAC,KAAE,aAAW0E,GAAA1E,CAAA,iBAX9C;AAAA,KACA,0BAMK,IAAE,mBAGR,KAAG,aACU,GAAC,eAEd,OAAK,aAAO,IAAE,aACH,YAAU,qGAbpBP,EAMKC,EAAAiE,EAAA/D,CAAA,kBACNH,EAQMC,EAAAgE,EAAA9D,CAAA,iFAdEoE,EAAA,GAAAC,OAAA3B,GACDtC,EAAa,IACbA,EAAY,SACZ,IAAG,OAAA6D,GAAAK,EAAAD,CAAA,eAKNjE,EAAa,GAACA,EAAC,KACX,KAAI,KAAA6D,GAAAyC,EAAAC,CAAA,EAAOvG,EAAa,GAACA,EAAC,KAAE,+EAE9BA,EAAK,SAAA6D,GAAA2C,EAAAC,CAAA,cACVzG,EAAS,SAAA6D,GAAA6C,EAAAC,CAAA,uEAxB2B3G,EAAS,iBAKvCA,EAAK,aACTA,EAAS,0BARZ;AAAA,KACC,mBAC4B,SAAO,aAC9B,IAAE,mBAIN,eAAI,aAAO,IAAE,aACF,YAAU,qGAPtBP,EAEKC,EAAAiE,EAAA/D,CAAA,yBACJH,EAMIC,EAAAgE,EAAA9D,CAAA,yDAR+BI,EAAS,SAAA6D,GAAA+C,EAAAC,CAAA,cAKvC7G,EAAK,SAAA6D,GAAAiD,EAAAC,CAAA,cACT/G,EAAS,SAAA6D,GAAA2C,EAAAC,CAAA,0DAesCzG,EAAa,GAACA,EAAC,KACxD,YAAW,sBAD4B,GAAC,aAC5B,GAAC,0DAD4BA,EAAa,GAACA,EAAC,KACxD,YAAW,KAAA6D,GAAAK,EAAAD,CAAA,wGAzBbjE,EAAe,IAAC,SAASA,EAAS,kOAtCX,QAAM,wDAOpCiE,EAAAjE,EAAA,IACOA,MAAiB,IACjB,SACAsC,GACDtC,EAAqB,IAAGA,EAAiB,IAAGA,EAAa,IACzDA,MAAY,KACZ,4BAPC;AAAA,GACR,iCAQM,GAAC,0EANIP,EAMLC,EAAAa,EAAAX,CAAA,0BARLoE,EAAA,GAAAC,OAAAjE,EAAA,IACOA,MAAiB,IACjB,KAAE6D,GAAAK,EAAAD,CAAA,cACF3B,GACDtC,EAAqB,IAAGA,EAAiB,IAAGA,EAAa,IACzDA,MAAY,KACZ,oHApBD,OAAAA,OAAqB,SAAQ,EA8BxBA,OAAqB,aAAY,yGA/B5CP,EAoFMC,EAAAuF,EAAArF,CAAA,6UA1FFI,EAAK,gMADXP,EA6FKC,EAAA2B,EAAAzB,CAAA,2XAlHO,WAAAoH,CAAsB,EAAAjG,GACtB,iBAAAkG,CAAwB,EAAAlG,GACxB,KAAAqB,CAAY,EAAArB,GACZ,oBAAAmG,CAAwB,EAAAnG,GACxB,cAAAoG,CAAkB,EAAApG,GAClB,MAAA4E,CAAc,EAAA5E,GAEd,iBAAAiE,CAAyC,EAAAjE,EAEhDqG,EACAC,EAEAC,EAAgBJ,EAAoB,KAAMK,GAC7C5E,GAAgC4E,EAAM,aAAa,GAEhDC,GAAmB,QAAS,OAAQ,QAAS,OAAO,EACpDC,EAAuBP,EAAoB,OAC7CK,GAA6BC,EAAgB,SAASD,EAAM,SAAS,6CAgBpDH,EAAWM,qDA8BXL,EAAOK,2gBC/E3B,MAAeC,GAAA,uDCAAC,GAAA,0mBCeC,WACL,yDAFR,IAAAC,EAAA7H,MAAoB,SAAW,QAAU,WAAY6G,EAAA7G,KAAiB,OAAM,+BAA5B,MAAI,eAAwB;AAAA,WACrE,0DADPgE,EAAA,GAAA6D,OAAA7H,MAAoB,SAAW,QAAU,SAAM6D,GAAAiE,EAAAD,CAAA,EAAM7D,EAAA,GAAA6C,OAAA7G,KAAiB,OAAM,KAAA6D,GAAA+C,EAAAC,CAAA,sGAWrD7G,EAAC,mDAAtBP,GAA8BC,EAAAa,EAAAX,CAAA,wCAGgCI,EAAU,GACtEA,OACC,KAAI,8DAFuDA,EAAU,GACtEA,OACC,KAAI,KAAA6D,GAAA,EAAAC,CAAA,kCAF8B,IAAAA,EAAA9D,KAAY,KAAI,kDAAhBgE,EAAA,GAAAF,OAAA9D,KAAY,KAAI,KAAA6D,GAAA,EAAAC,CAAA,0DAMf9D,EAAK,YAC3CA,EAAS,cAXLA,EAAgB,GAAC,OAAS,GAACmE,GAAAnE,CAAA,kBAIzB,OAAAA,OAAqB,SAAQoE,oIAKtB,wCACwB,eAAO,IAAE,eAG/C;AAAA,eACD,2JAjBD3E,GAAgBC,EAAA2E,EAAAzE,CAAA,YAChBH,GAiBKC,EAAA2B,EAAAzB,CAAA,EAhBJC,GASGwB,EAAAY,CAAA,yBALFpC,GAIAoC,EAAA1B,CAAA,sBAEDV,GAKGwB,EAAAc,CAAA,0DAdGnC,EAAgB,GAAC,OAAS,6HAUQA,EAAK,QAAA6D,GAAAyC,EAAAC,CAAA,cAC3CvG,EAAS,QAAA6D,GAAAS,EAAAC,CAAA,wGASI,EAAK,yEADtB9E,GAEKC,EAAA2B,EAAAzB,CAAA,wKAhCQI,EAAgB,GAAC,OAAS,EAAC+D,6BAOjC/D,EAAgB,yBAArB,OAAIyB,GAAA,2BAsBFzB,EAAU,IAAAqD,GAAA,kHA9BT;AAAA,UACG,6KAMQrD,EAAU,YAV3BP,GAQIC,EAAAiF,EAAA/E,CAAA,EAPHC,GAEK8E,EAAAvE,CAAA,gCAONX,GAsBKC,EAAAW,EAAAT,CAAA,gLArBGI,EAAgB,sBAArB,OAAIyB,GAAA,qHAAJ,8BADczB,EAAU,IAuBtBA,EAAU,oOAvCH,WAAA6E,CAAmB,EAAA9D,GACnB,iBAAA+D,CAAqB,EAAA/D,GACrB,WAAAgE,CAAe,EAAAhE,GACf,iBAAAiE,CAAyC,EAAAjE,ssBCJpCI,0BAAuB,OAAgB,6OA8KlDnB,EAAS,+gBAEc,KAAAA,MAAYA,EAAI,mDAcjCA,EAAK,yBAAV,OAAIyB,GAAA,qEAsBDzB,EAAQ,IAAA+D,GAAA/D,CAAA,OASPA,EAAY,yBAAjB,OAAIyB,GAAA,qJAzCJ,UACO,kDAAyC;AAAA,sBACjC,iBAAkC,MAAI,OAAI;AAAA,MAC1D,mDAA0C;AAAA,0BACtB,iBAAkC,MAAI,OAAI;AAAA,4BAE/D,oNAsBiB;AAAA;AAAA;AAAA,MAIhB,gBAMS,2BACV,wIArC0BsG,EAAO,sGAEHC,EAAO,iSATvCvI,GAEKC,EAAAQ,EAAAN,CAAA,yBACLH,GAmFKC,EAAAc,EAAAZ,CAAA,EAlFJC,EAQKW,EAAAJ,CAAA,EAPJP,EAMGO,EAAA6B,CAAA,SALMpC,EAAyCoC,EAAAC,CAAA,SACjCrC,EAA0CoC,EAAAgG,CAAA,gBAC1DpI,EAA0CoC,EAAAiG,CAAA,SACtBrI,EAA0CoC,EAAAkG,CAAA,uBAIhEtI,EAwEKW,EAAAL,CAAA,EAvEJN,EAWKM,EAAAE,CAAA,+DAELR,EAEGM,EAAAgC,CAAA,6BAIHtC,EAWGM,EAAAiI,CAAA,yHA3CsBpE,EAAA,IAAAqE,GAAA,KAAArI,MAAYA,EAAI,2BAcjCA,EAAK,sBAAV,OAAIyB,GAAA,wHAAJ,iEAsBGzB,EAAQ,sEASPA,EAAY,sBAAjB,OAAIyB,GAAA,mHAAJ,OAAIA,EAAA6G,EAAA,OAAA7G,GAAA,mFAAJ,OAAIA,GAAA,8NAxBFzB,EAAQ,qHADCA,EAAG,MAAAsD,EAAAiF,EAAA,MAAAC,CAAA,6CAHZlF,EAAAmF,EAAA,QAAAC,EAAA,YAAA1I,OAAqBA,EAAQ,IAAG,eAAiB,iBAAe,0BAFlEP,GAOIC,EAAA+I,EAAA7I,CAAA,EAFHC,EAAuB4I,EAAAF,CAAA,+DAHtBvE,EAAA,GAAA0E,OAAA,YAAA1I,OAAqBA,EAAQ,IAAG,eAAiB,iBAAe,yGAmBrD;AAAA,mCACe,iBAIX,WAAS,OACzB,IAAE,EAJKsD,EAAAqF,EAAA,OAAAC,GAAA5I,MAAoB,SAAW+H,GAAUC,IAC/Ca,EAAkB,kFAFQpJ,GAK3BC,EAAAiJ,EAAA/I,CAAA,2BAJOoE,EAAA,GAAA4E,QAAA5I,MAAoB,SAAW+H,GAAUC,IAC/Ca,mHAUO,uBACc7I,EAAI,GAAC,gBACzB,IAAMA,EAAU,IAAC,UAChB,yBACaA,EAAO,GAAC,gBACtB,IAAMA,EAAU,IAAC,UAChB,yEAII,KAAAA,MAAYA,EAAI,wCAIJA,EAAI,GAAC,gBACtB,IAAMA,EAAU,IAAC,UAChB,sBACUA,EAAO,GAAC,gBAAgB,IAAMA,EAAU,IAAC,QAAQ,EAC3D,sFAMgBA,EAAI,GAAC,gBACtB,IAAMA,EAAU,IAAC,UAChB,mBACUA,EAAO,GAAC,gBAAgB,IAAMA,EAAU,IAAC,QAAQ,EAC3D,qMA9BJP,GAkCKC,EAAA2B,EAAAzB,CAAA,mHA/BkBI,EAAI,GAAC,gBACzB,IAAMA,EAAU,IAAC,UAChB,mCACaA,EAAO,GAAC,gBACtB,IAAMA,EAAU,IAAC,UAChB,qEAIIgE,EAAA,IAAA8E,EAAA,KAAA9I,MAAYA,EAAI,mDAIJA,EAAI,GAAC,gBACtB,IAAMA,EAAU,IAAC,UAChB,gCACUA,EAAO,GAAC,gBAAgB,IAAMA,EAAU,IAAC,QAAQ,EAC3D,oHAMgBA,EAAI,GAAC,gBACtB,IAAMA,EAAU,IAAC,UAChB,6BACUA,EAAO,GAAC,gBAAgB,IAAMA,EAAU,IAAC,QAAQ,EAC3D,yRA/BAoD,EAAApD,MAAW,UAAQmE,GAAAnE,CAAA,wEAAnBA,MAAW,gNAjDhBA,EAAI,IAAAqD,GAAArD,CAAA,0EAAJA,EAAI,kLAvJF,MAAAgI,GACL,mEACKD,GACL,uEACKc,GAAqB,+CAsBlBE,GACR5K,EACA8C,EAAU,IAEN9C,EAAK,KAAO8C,SACR9C,EAEJ,GAAAA,EAAK,iBACC6K,KAAS7K,EAAK,SAAQ,CAC1B,IAAA8K,EAASF,GAAeC,EAAO/H,CAAE,KACjCgI,SACIA,SAIH,4BA9CG,aAAAC,CAA0B,EAAAnI,GAC1B,KAAAqB,CAAY,EAAArB,GACZ,IAAAoI,CAAuC,EAAApI,GACvC,SAAAqI,CAAuB,EAAArI,GACvB,UAAAsI,CAAwB,EAAAtI,EAO/B6C,EAAYsF,EAAa,OAC3BlC,GAAeA,EAAW,QAAQ,EAClC,OAEE5E,IAAS,KACZA,EAAO,SAAS,SAAW,KAAO,SAAS,KAAO,SAAS,UAEvDA,EAAK,SAAS,GAAG,IACrBA,GAAQ,KAGL,IAAA4C,EAA4C,SAE1C,MAAAsE,IACJ,SAAU3B,EAAM,EAChB,cAAcC,EAAU,GAGtB,IAAA/C,EAAa,GAoBOqE,EAAa,IAAKlC,GACzCA,EAAW,OAAO,IAAKuC,GAAG,KACrBC,EAAeT,GAAeM,EAAWE,CAAG,GAAG,OAAO,QACtD,OAAAC,IAAiB,OACpBA,EAAe,GACE,OAAAA,GAAiB,WAClCA,EAAe,KAAK,UAAUA,CAAY,GAEpCA,KAIyBN,EAAa,IAC7ClC,GAAmB,UAAMA,EAAW,QAAQ,MAAM,GAGbkC,EAAa,IAAKlC,GACpD,UAAMA,EAAW,OAAO,MAAM,EAAE,KAAK,EAAK,kBAGhCyC,GAAQ,QAKd,MADa,YAAMrH,EAAO,MAAM,GACd,sBAGZsH,GAAW,QACV,MAASP,EAAI,eAIzBQ,EAKAC,EAEJH,EAAQ,EAAG,KAAMI,GAAI,CACpBzE,EAAA,EAAAuE,EAAOE,CAAI,IAGZH,EAAW,EAAG,KAAMI,GAAW,CAC9B1E,EAAA,EAAAwE,EAAUE,CAAW,IAmDtB/J,GAAO,KACN,SAAS,KAAK,MAAM,SAAW,SAC3B,iBAAkB,QACrB,OAAO,cAAc,SAAS,EAAG,CAAC,OAGlC,SAAS,KAAK,MAAM,SAAW,0CA0BV,MAAAsC,EAAA0H,GAAA3E,EAAA,EAAAJ,EAAmB+E,CAAQ,6WCjMzCC,GAAIC,EAAM,EAGb,MAAMC,EAA4D,CACxEC,GAIAC,GAIA,YACCnJ,EACAoJ,EACAC,EACAC,EACAnI,EACAoI,EACC,CACD,KAAKL,GAAMlJ,EACX,KAAK,MAAQqJ,EACb,KAAK,QAAUC,EACf,KAAKH,GAAMC,EACN,UAAOL,GAAIC,EAAM,EACtB,KAAK,KAAO7H,EACZ,KAAK,WAAaoI,CACnB,CAEA,SAA4BC,EAAeZ,EAAmB,CACvD,MAAAa,EAAI,IAAI,YAAY,SAAU,CACnC,QAAS,GACT,OAAQ,CAAE,KAAAb,EAAM,GAAI,KAAKM,GAAK,MAAOM,CAAW,EAChD,EAEI,KAAAL,GAAI,cAAcM,CAAC,CACzB,CACD,ygBCjCcC,sBAAmB,OAAyB,6WAuDrD3K,EAAW,6EAPTA,EAAU,4KAOZA,EAAW,mVAPTA,EAAU,qUAOZA,EAAW,2bArDJ,KAAAoC,CAAY,EAAArB,GACZ,UAAA6J,CAAqC,EAAA7J,GACrC,OAAArB,CAAmB,EAAAqB,GACnB,WAAA8J,CAAqB,EAAA9J,GACrB,SAAA+J,CAAmC,EAAA/J,GACnC,MAAAwB,CAAU,EAAAxB,GACV,OAAAgK,CAAc,EAAAhK,GACd,QAAAiK,CAAe,EAAAjK,GACf,aAAAkK,CAAsB,EAAAlK,GACtB,GAAAE,CAAU,EAAAF,QAEfmK,EAAC,CAAIjK,EAAYkK,EAAWC,IAAM,IACnC,YAAY,eAAiB,OAAM,CAAI,GAAAnK,EAAI,KAAMkK,EAAG,MAAOC,CAAC,IAExD,SAAAC,EACRT,EAAyC,QAErB,IAAO,MAAMA,GAChC,UAAUU,EAASC,EAA2B,OAEvCT,EAAQ,IAAOQ,EAAO,GAAIC,CAAI,EAC9BC,EAAQ,OAAO,oBAAoBV,CAAQ,EAAE,OACjDI,GAAO,CAAAA,EAAE,WAAW,GAAG,GAGhB,SAAAO,GAAOD,EAAa,iBACXE,EAAa,CACvB,MAAAC,EAAKT,EAAEjK,EAAIuK,EAAOE,CAAQ,EAChChM,EAAO,cAAciM,CAAE,GAGzB,OAAAH,EAAM,QAASJ,GAAC,CACfT,GAAkB,KAAI,IAAOiB,GAAKd,EAAUM,EAAGK,GAAOL,CAAC,MAGjDN,WAOJe,EAAaR,EAAKT,CAAS,4CAKtBE,EAAQpD,glCCtDV,CAAA3H,WAASoB,yBAAuB,WAAA2K,EAAA,SAA0B,0HA6E3DC,EAAAC,GAAAhM,KAAK,QAAQ,EAAW,MAAAiM,EAAAjM,SAAM,mBAAnC,OAAIyB,GAAA,2LAACsK,EAAAC,GAAAhM,KAAK,QAAQ,sFAAlB,OAAIyB,GAAA,gLAEEzB,EAAK,IACA,UAAAA,MAAM,sBAEb,GAAAA,MAAM,2MAHJA,EAAK,KACAgE,EAAA,IAAAkI,EAAA,UAAAlM,MAAM,gCAEbgE,EAAA,IAAAkI,EAAA,GAAAlM,MAAM,6KANRoD,EAAApD,KAAK,UAAYA,EAAK,YAAS,QAAMqD,GAAArD,CAAA,wEAArCA,KAAK,UAAYA,EAAK,YAAS,yNAdhC,IAAAA,KAAK,EAAE,EACA,WAAAA,KAAK,SAAS,GAGf,oBAAaA,EAAK,UAASA,EAAK,SAAM,SAClC,aAAAA,KAAK,oBACJ,iBAAkBA,EAAK,UAASA,EAAI,GAAC,MAAM,cAAY,kBAGlEA,KAAK,qCAGG,WAAAkK,GAAOlK,EAAK,MAAIA,EAAQ,GAAAA,EAAY,GAAAA,EAAS,GAAAA,KAAMA,EAAU,2IAV1D,OAAAA,KAAK,WAAQ,SAAbmM,EAAA,SAAAnM,KAAK,UACRA,EAAI,GAAC,MAAM,QAAK,iBAAhBA,EAAI,GAAC,MAAM,4GAIPoM,EAAkB,6EAP9BpI,EAAA,OAAAhE,KAAK,EAAE,EACAgE,EAAA,cAAAhE,KAAK,SAAS,QAGf,oBAAaA,EAAK,UAASA,EAAK,SAAM,SAClC,aAAAA,KAAK,yBACJ,iBAAkBA,EAAK,UAASA,EAAI,GAAC,MAAM,cAAY,uBAGlEgE,EAAA,GAAAqI,GAAArM,KAAK,KAAK,2CAGF,WAAAkK,GAAOlK,EAAK,MAAIA,EAAQ,GAAAA,EAAY,GAAAA,EAAS,GAAAA,KAAMA,EAAU,8DAV1DsM,EAAA,SAAAtM,KAAK,8CACRA,EAAI,GAAC,MAAM,iHArBd,SAAAoM,GAAmB1B,EAAkC,yBAxCnD,KAAAtI,CAAY,EAAArB,GAEZ,KAAA5C,CAAmB,EAAA4C,EACnB,QAAAwL,EAAwB,IAAI,EAAAxL,GAC5B,OAAArB,CAAmB,EAAAqB,GACnB,WAAA8J,CAAqB,EAAA9J,GACrB,QAAAwJ,CAAe,EAAAxJ,GACf,WAAAyJ,CAAmB,EAAAzJ,EAExB,MAAAG,EAAWC,SACbqL,EAAiB,GAErBzM,GAAO,KACNmB,EAAS,QAAS/C,EAAK,EAAE,EAEd,UAAA6K,KAASwD,EACnBtL,EAAS,QAAS8H,EAAM,EAAE,aAI1B9H,EAAS,UAAW/C,EAAK,EAAE,EAEhB,UAAA6K,KAASwD,EACnBtL,EAAS,QAAS8H,EAAM,EAAE,KAe7B8C,GAAW,YAAaS,CAAM,gFAsBfE,EAAA,aAAAtO,EAAK,SAAQoE,CAAA,IAAbpE,EAAK,SAAQoE,+CAChBpE,EAAK,MAAM,MAAKoE,CAAA,IAAhBpE,EAAK,MAAM,MAAKoE,iSAjC3B6C,IAAEjH,EAAK,SACPA,EAAK,UACLA,EAAK,SAAS,OAAQiN,GAAC,CAChB,MAAAsB,EAAavO,EAAK,OAAS,uBAC5BuO,GACJF,EAAkB,KAAKpB,CAAC,EAElBsB,qBAYJvO,EAAK,OAAS,SACbA,EAAK,UAAU,MAAOwO,GAAC,CAAMA,EAAE,MAAM,OAAO,EAC/CvH,EAAA,EAAAjH,EAAK,MAAM,QAAU,GAAKA,CAAA,EAE1BiH,EAAA,EAAAjH,EAAK,MAAM,QAAU,GAAIA,CAAA,mWCxDZ,sBAAAgD,IAAuB,OAAgB,+EAgB1CnB,EAAQ,yJAARA,EAAQ,0OAbV,SAAA4M,CAAa,EAAA7L,GACb,KAAAqB,CAAS,EAAArB,GACT,OAAArB,CAAW,EAAAqB,GACX,WAAA8J,CAAe,EAAA9J,GACf,QAAAwJ,CAAY,EAAAxJ,GACZ,WAAAyJ,CAAmB,EAAAzJ,EAExB,MAAAG,EAAWC,KACjB,OAAApB,GAAO,KACNmB,EAAS,OAAO,sWCblB,MAAe2L,GAAA,uitBC+BR,SAASC,IAAkD,CAC3D,MAAAC,EAAQC,GAAkC,EAAE,EAE5CC,EAAwB,GACxBC,EAAyB,GACzBC,MAAsB,IACtBC,MAAqB,IAErBC,MAAuB,IACvBC,EAAuC,GAE7C,SAASC,EAAO,CACf,SAAA7H,EACA,OAAA8H,EACA,MAAAC,EAAQ,GACR,KAAAC,EACA,SAAAC,EAAW,KACX,IAAAC,EAAM,KACN,QAAA9M,EAAU,KACV,SAAA+M,CAAA,EAUQ,CACF,MAAAC,EAAUZ,EAAWxH,CAAQ,EAC7BqI,EAASd,EAAUvH,CAAQ,EAC3BsI,EAAcV,EAAU5H,CAAQ,EAEhCuI,EAAoBH,EAAQ,IAAK7M,GAAO,CACzC,IAAAiN,EAEJ,MAAMC,EAAgBhB,EAAgB,IAAIlM,CAAE,GAAK,EAG7C,GAAA+M,IAAgB,WAAaR,IAAW,UAAW,CACtD,IAAIY,EAAYD,EAAgB,EAEhChB,EAAgB,IAAIlM,EAAImN,EAAY,EAAI,EAAIA,CAAS,EAExCF,EAAAE,EAAY,EAAI,UAAYZ,OAG/BQ,IAAgB,WAAaR,IAAW,UACrCU,EAAA,UAGHF,IAAgB,WAAaR,IAAW,WACrCU,EAAA,UACGf,EAAA,IAAIlM,EAAIkN,EAAgB,CAAC,GAE5BD,EAAAV,EAGP,OACN,GAAAvM,EACA,eAAgB0M,EAChB,WAAYD,EACZ,IAAAE,EACA,OAAQM,EACR,QAAApN,EACA,SAAA+M,CAAA,CACD,CACA,EAEME,EAAA,QAAS9M,GAAO,CACtB,MAAMkN,EAAgBf,EAAe,IAAInM,CAAE,GAAK,EAG5C,GAAA+M,IAAgB,WAAaR,IAAW,UAAW,CACtD,IAAIY,EAAYD,EAAgB,EAChCf,EAAe,IAAInM,EAAImN,EAAY,EAAI,EAAIA,CAAS,EACnCf,EAAA,IAAIpM,EAAIuM,CAAM,OACrBQ,IAAgB,WAAaR,IAAW,WACnCJ,EAAA,IAAInM,EAAIkN,EAAgB,CAAC,EACvBd,EAAA,IAAIpM,EAAIuM,CAAM,GAE/BH,EAAiB,OAAOpM,CAAE,CAC3B,CACA,EAEK8L,EAAA,OAAQe,IACKG,EAAA,QACjB,CAAC,CACA,GAAAhN,EACA,eAAAoN,EACA,WAAAC,EACA,IAAAV,EACA,OAAAJ,EACA,QAAA1M,EACA,SAAA+M,CAAA,IACK,CACLC,EAAQ7M,CAAE,EAAI,CACb,MAAAwM,EACA,WAAAa,EACA,eAAAD,EACA,IAAKT,EACL,QAAS9M,EACT,SAAA+M,EACA,OAAAL,EACA,SAAA9H,CAAA,CAEF,GAGMoI,EACP,EACDR,EAAU5H,CAAQ,EAAI8H,CACvB,CAES,SAAAe,EAASxL,EAAegL,EAAkBD,EAAyB,CAC3Eb,EAAUlK,CAAK,EAAIgL,EACnBb,EAAWnK,CAAK,EAAI+K,CACrB,CAEO,OACN,OAAAP,EACA,SAAAgB,EACA,UAAWxB,EAAM,UACjB,kBAAkBtL,EAAW,CAC5B,OAAO6L,EAAU7L,CAAC,CACnB,EACA,sBAAuB,CACf,OAAA4L,CACR,EAEF,CChJA,IAAImB,GAAyC,GAMtC,SAASC,IAiBd,CACG,IAAAC,EAEAC,EAAkC3B,GAAS,EAAE,EAC7C4B,EAAyB,GACzBb,EACAD,EACAe,EACAC,EACAC,EACHjC,KACD,MAAMkC,EAAwChC,KAC1C,IAAA5K,EACA6M,EACA9F,EAEJ,SAAS+F,EAAc,CACtB,IAAKC,EACL,WAAAC,EACA,OAAAC,EACA,aAAAnG,EACA,KAAA9G,EACA,QAAAkN,CAAA,EAUQ,CACFnG,EAAAgG,EACQF,EAAAG,EACdrB,MAAa,IACbD,MAAc,IACdU,GAAkB,GAClBK,MAAsB,IACtBH,MAAqB,IAErBI,EAAe,GAEf,MAAMS,EAA2B,CAChC,GAAIF,EAAO,GACX,KAAM,SACN,MAAO,CAAE,YAAa,GAAO,MAAOC,EAAQ,YAAc,EAAI,IAAK,EACnE,UAAW,GACX,SAAU,KACV,UAAW,KACX,mBAAoB,IAGrBF,EAAW,KAAKG,CAAS,EAEZrG,EAAA,QAAQ,CAACsG,EAAK9J,IAAa,CACvCqJ,EAAe,SAASrJ,EAAU8J,EAAI,OAAQA,EAAI,OAAO,EACzDA,EAAI,YAAcC,GACjBD,EAAI,GACJ,CAAC,CAACA,EAAI,WACNA,EAAI,OAAO,OACXA,EAAI,QAAQ,QAEME,GAAAF,EAAI,QAAS9J,EAAUkJ,CAAW,EAClCe,GAAAH,EAAKzB,EAAQD,CAAO,EACvC,EAEDa,EAAW,IAAIC,CAAW,EAERC,EAAAe,GAAuBR,EAAYhN,CAAI,EAEzD0M,EAAeM,EAAW,OACzB,CAACS,EAAKlD,KACDkD,EAAAlD,EAAE,EAAE,EAAIA,EACLkD,GAER,CAAC,GAGUC,EAAAT,CAAM,EAAE,KAAK,IAAM,CAC9BL,EAAa,IAAIO,CAAS,EAC1B,CACF,CAEA,eAAeO,EAAY3R,EAA0C,CAC9D,MAAA2M,EAAWgE,EAAa3Q,EAAK,EAAE,EAE5B,OAAA2M,EAAA,WAAa,MAAM+D,EAAgB,IAC3C/D,EAAS,kBACL,YAEDA,EAAS,OAAS,YACrBA,EAAS,MAAM,cAAgBiF,GAC9BjF,EAAS,KACTA,EAAS,mBACT1I,EACA6M,EACAnE,EAAS,MAAM,UACd,sBAGC8D,EAAY9D,EAAS,EAAE,IAC1BA,EAAS,MAAM,gBAAkB,OAAO,KAAK8D,EAAY9D,EAAS,EAAE,CAAC,GAGtEA,EAAS,MAAM,YAAckF,GAC5BlF,EAAS,GACTA,EAAS,MAAM,YACfA,EAAS,MAAM,MACfiD,EACAD,CAAA,EAGDhD,EAAS,MAAM,OAASmF,GACvBnF,EAAS,GACTA,EAAS,MAAM,WACf3B,CAAA,EAGcuF,EAAA,IAAI5D,EAAS,GAAIA,CAAQ,EAEpC3M,EAAK,WACC2M,EAAA,SAAW,MAAM,QAAQ,IACjC3M,EAAK,SAAS,IAAKiN,GAAM0E,EAAY1E,CAAC,CAAC,IAIlCN,CACR,CAEA,IAAIoF,EAAmB,GACnBC,EAAyBnD,GAAS,EAAK,EAE3C,SAASoD,GAAc,CACTpB,EAAA,OAAQK,GAAW,CAC/B,QAAS5N,EAAI,EAAGA,EAAI+M,GAAgB,OAAQ/M,IAC3C,QAAS4O,EAAI,EAAGA,EAAI7B,GAAgB/M,CAAC,EAAE,OAAQ4O,IAAK,CACnD,MAAM9C,EAASiB,GAAgB/M,CAAC,EAAE4O,CAAC,EAC7BvF,EAAWgE,EAAavB,EAAO,EAAE,EACvC,GAAI,CAACzC,EAAU,SACX,IAAAwF,EACA,MAAM,QAAQ/C,EAAO,KAAK,EAAe+C,EAAA,CAAC,GAAG/C,EAAO,KAAK,EACpDA,EAAO,QAAU,KAAkB+C,EAAA,KACnC,OAAO/C,EAAO,OAAU,SACpB+C,EAAA,CAAE,GAAG/C,EAAO,OACpB+C,EAAY/C,EAAO,MACfzC,EAAA,MAAMyC,EAAO,IAAI,EAAI+C,EAGzB,OAAAjB,CAAA,CACP,EAEDb,GAAkB,GACC0B,EAAA,GACnBC,EAAuB,IAAI,EAAK,CACjC,CAEA,SAASI,EAAaC,EAAgD,CAChEA,IACLhC,GAAgB,KAAKgC,CAAO,EAEvBN,IACeA,EAAA,GACnBC,EAAuB,IAAI,EAAI,EAC/B,sBAAsBC,CAAK,GAE7B,CAEA,SAASK,EAASxP,EAAgC,CAC3C,MAAAyP,EAAOhC,EAAe,IAAIzN,CAAE,EAClC,OAAKyP,EAGDA,EAAK,SAAS,UACVA,EAAK,SAAS,YAEfA,EAAK,MAAM,MALV,IAMT,CAEO,OACN,OAAQ1B,EACR,QAASL,EACT,aAAA4B,EACA,SAAAE,EACA,eAAA1B,EACA,kBAAmBoB,EACnB,cAAe,IAAI5E,IAClB,sBAAsB,IAAM2D,EAAc,GAAG3D,CAAI,CAAC,EAErD,CAGO,MAAMoF,GAE2B,OAAO,eAC9C,gBAAkB,CAAC,CACpB,EAAE,YAUK,SAASlB,GACfmB,EACAC,EACAC,EACAC,EACsD,CACtD,GAAI,CAACH,EAAe,YAEpB,MAAMvF,EAAOwF,EAAaC,IAAiB,EAAIC,IAAkB,EAC7D,IACH,OAAO,IAAIJ,GACV,YACA,yBAAyBC;AAAA,YAChBvF,mDAAA,QAEFX,GACR,eAAQ,MAAM,mCAAmC,EACjD,QAAQ,MAAMA,CAAC,EACR,IACR,CACD,CAUgB,SAAAgF,GACfsB,EACAtL,EACAiJ,EACY,CACZ,OAAAqC,EAAQ,QAAQ,CAAC,CAAC/P,EAAIgQ,CAAO,IAAM,CAC7BtC,EAAW1N,CAAE,IACN0N,EAAA1N,CAAE,EAAI,IAGjB0N,EAAW1N,CAAE,IAAIgQ,CAAO,GACxB,CAACtC,EAAW1N,CAAE,IAAIgQ,CAAO,EAAE,SAASvL,CAAQ,EAE5CiJ,EAAW1N,CAAE,EAAEgQ,CAAO,EAAE,KAAKvL,CAAQ,EAErCiJ,EAAW1N,CAAE,EAAEgQ,CAAO,EAAI,CAACvL,CAAQ,CACpC,CACA,EAEMiJ,CACR,CASgB,SAAAgB,GACfH,EACAzB,EACAD,EAC6B,CAC7B,OAAA0B,EAAI,OAAO,QAAS0B,GAAUnD,EAAO,IAAImD,CAAK,CAAC,EAC/C1B,EAAI,QAAQ,QAAS2B,GAAWrD,EAAQ,IAAIqD,CAAM,CAAC,EAC5C,CAACpD,EAAQD,CAAO,CACxB,CAOA,SAASsD,GAAqB7O,EAAqB,CAEhD,aAAM,QAAQA,CAAK,GAAKA,EAAM,SAAW,GAC1CA,IAAU,IACVA,IAAU,GACV,CAACA,CAEH,CAWO,SAASyN,GACf/O,EACAoQ,EACA9O,EACAwL,EACAD,EACU,CACV,OAAIuD,IAAqB,GACjB,GACGA,IAAqB,GACxB,GAEP,GAAAtD,EAAO,IAAI9M,CAAE,GACZ,CAAC6M,EAAQ,IAAI7M,CAAE,GAAKmQ,GAAqB7O,CAAK,EAMjD,CAWgB,SAAA0N,GACfhP,EACAqQ,EACAnI,EACkB,CAClB,OAAKmI,EAGEA,EAAW,OAAO,CAACzB,EAAK0B,KAC1B1B,EAAA0B,CAAE,EAAI,SAAUhG,KACfA,EAAK,SAAW,IACnBA,EAAOA,EAAK,CAAC,GAEC,MAAMpC,EAAI,iBAAiBlI,EAAIsQ,EAAIhG,CAAI,GAGhDsE,GACL,CAAqB,GAXhB,EAYT,CAWO,SAASE,GACf/O,EACAwQ,EACApP,EACAgN,EACAqC,EAKC,CACG,IAAAC,MACC,IACD1Q,IAAS,WAAayQ,GACxBA,EAAgC,QAASE,GAAiB,CACtD,GAAAD,EAAsB,IAAIC,CAAI,EACjC,OAEGC,MAEJ,MAAMC,EAAqBzC,EAAW,KAAMzC,GAAMA,EAAE,OAASgF,CAAI,EAC7DE,IACHD,EAAKE,GAAe,CACnB,QAAS1P,EACT,KAAAuP,EACA,GAAIE,EAAmB,mBACvB,QAAS,UACT,EACqBH,EAAA,IAAIC,EAAMC,EAAG,SAAS,EAC7C,CACA,EAGF,MAAMA,EAAKE,GAAe,CACzB,QAAS1P,EACT,KAAMpB,EACN,GAAIwQ,EACJ,QAAS,YACT,EAEM,OACN,UAAWI,EAAG,UACd,KAAMA,EAAG,KACT,mBACCF,EAAsB,KAAO,EAAIA,EAAwB,OAE5D,CAQgB,SAAA9B,GACfR,EACAhN,EAC+C,CAC3C,IAAAyM,MAAoE,IAE7D,OAAAO,EAAA,QAASzC,GAAM,CACnB,MAAE,UAAA/B,EAAW,mBAAA6G,CAAA,EAAuB1B,GACzCpD,EAAE,KACFA,EAAE,mBACFvK,EACAgN,CAAA,EAKD,GAFgBP,EAAA,IAAIlC,EAAE,mBAAoB/B,CAAS,EAE/C6G,EACH,SAAW,CAACE,EAAMI,CAAiB,IAAKN,EACvB5C,EAAA,IAAI8C,EAAMI,CAAiB,CAE7C,CACA,EAEMlD,CACR,+YC3dU,MAAAmD,EAAA,SAAoB,0DAgepBhS,EAAK;;;;;;;6HAGbP,GAISC,EAAAuS,EAAArS,CAAA,YACTH,GASQC,EAAAwS,EAAAtS,CAAA,yFAQII,EAAQ,0FAIRA,EAAY,qHAJZA,EAAQ,oQAgCjBiE,EAAAjE,MAAG,0BAA0B,iBAlB1BA,EAAQ,IAAA+D,GAAA/D,CAAA,4FAmBF6M,EAAI,GAAAvJ,EAAAC,EAAA,MAAAC,CAAA,EAAOF,EAAAC,EAAA,MAAA4O,EAAAnS,MAAG,aAAa,gMApBvCP,GAsBQC,EAAA0S,EAAAxS,CAAA,yBATPC,GAQGuS,EAAAzJ,CAAA,kBADF9I,GAAyC8I,EAAApF,CAAA,UAnBrCvD,EAAQ,0DAkBXgE,EAAA,WAAAC,OAAAjE,MAAG,0BAA0B,OAAA6D,GAAAK,EAAAD,CAAA,EACTD,EAAA,WAAAmO,OAAAnS,MAAG,aAAa,gEAZnC6H,EAAA7H,MAAG,oBAAoB,4HACdmD,EAAQ,GAAAG,EAAAC,EAAA,MAAAC,CAAA,EAAOF,EAAAC,EAAA,MAAA4O,EAAAnS,MAAG,aAAa,8GAP1CP,GAQQC,EAAAY,EAAAV,CAAA,kBADPC,GAA6CS,EAAAiD,CAAA,YAE9C9D,GAAWC,EAAA2B,EAAAzB,CAAA,0CAHToE,EAAA,WAAA6D,OAAA7H,MAAG,oBAAoB,OAAA6D,GAAAiE,EAAAD,CAAA,EACC7D,EAAA,WAAAmO,OAAAnS,MAAG,aAAa,8HA8B/BA,EAAQ,qRAZtBP,GAsBKC,EAAAW,EAAAT,CAAA,EAlBJC,GAKCQ,EAAAH,CAAA,UACDL,GAWKQ,EAAAD,CAAA,gGATQJ,EAAQ,4SAcMA,EAAkB,2OA5FzCA,EAAkB,IAAAqS,GAAArS,CAAA,IAGlBA,EAAiB,IAAA6F,GAAA,IAqBhB7F,EAAQ,KAAA0E,GAAA1E,CAAA,IAcTA,EAAW,IAAAmE,GAAAnE,CAAA,EA2BZsS,EAAAtS,OAAoBA,EAAQ,KAAAoE,GAAApE,CAAA,IA0B5BA,EAAQ,KAAAqD,GAAArD,CAAA,mMApE0BA,EAAQ,GAAG,IAAM,MAAM,uDAD1BA,EAAQ,GAAG,OAAS,MAAM,0FAA9DP,GAyCKC,EAAAU,EAAAR,CAAA,EAxCJC,GAaKO,EAAAF,CAAA,2GApCAF,EAAkB,6DAGlBA,EAAiB,4DAqBhBA,EAAQ,0IADwBA,EAAQ,GAAG,IAAM,MAAM,EAexDA,EAAW,yFAhBmBA,EAAQ,GAAG,OAAS,MAAM,EA2CzDA,OAAoBA,EAAQ,uHA0B5BA,EAAQ,uSA/ZN,MAAAuS,GAAmB,aAKnBC,GAAgC,GAChCC,GAAmC,GAwThC,SAAAC,GAAcC,EAAY,CAC3B,iBAAYA,yDAtcpBC,SAEW,KAAAxQ,CAAY,EAAArB,GACZ,WAAAqO,CAA2B,EAAArO,GAC3B,OAAAsO,CAAkB,EAAAtO,GAClB,aAAAmI,CAA0B,EAAAnI,EAC1B,OAAA8R,EAAQ,QAAQ,EAAA9R,EAChB,mBAAA+R,EAAoB,EAAK,EAAA/R,GACzB,OAAArB,CAAmB,EAAAqB,GACnB,WAAAyJ,CAAmB,EAAAzJ,EACnB,UAAAgS,EAAW,EAAI,EAAAhS,EACf,aAAAiS,EAAc,EAAI,EAAAjS,EAClB,oBAAAkS,EAAqB,EAAK,EAAAlS,GAC1B,SAAAmS,CAAiB,EAAAnS,GACjB,WAAA8J,CAAqB,EAAA9J,GACrB,IAAAoI,CAAuC,EAAApI,GACvC,SAAAqI,CAAuB,EAAArI,GACvB,QAAAwJ,CAAe,EAAAxJ,GACf,GAAAoS,CAAiB,EAAApS,EACjB,aAAAqS,EAAc,EAAK,EAAArS,GACnB,MAAAsS,CAAc,EAAAtS,EAGxB,aAAQuS,EACR,QAAAtC,EACA,aAAAT,GACA,SAAAE,EACA,eAAA1B,EACA,kBAAAwE,EACA,cAAArE,GACGT,GAAiB,0FAkBjB+E,MADa,gBAAgB,OAAO,SAAS,MAAM,EACzB,IAAI,MAAM,IAAM,OAAST,EAC9C,SAAAU,GAAqBC,EAAgB,CAC7CtO,EAAA,GAAAoO,EAAmBE,CAAO,EACtB,IAAApV,MAAa,gBAAgB,OAAO,SAAS,MAAM,EACnDoV,EACHpV,EAAO,IAAI,OAAQ,KAAK,EAExBA,EAAO,OAAO,MAAM,EAErB,QAAQ,aAAa,KAAM,GAAI,IAAMA,EAAO,SAAQ,GAG1C,oBAAAqV,EAAkB,EAAK,EAAA5S,iBACnB6S,EAAc/J,EAAWnE,EAAgB,CACjD,MAAAoI,EAAU5E,EAAaxD,CAAQ,EAAE,QAEjCmO,EAAehK,GAAM,IAAK,CAAAtH,EAAYd,OAE1C,GAAIqM,EAAQrM,EAAC,EACb,KAAM,kBACN,MAAO,MAIT8O,GAAasD,CAAY,QAEnB7B,GAAI,QAEJxB,EAAO,GAEb3G,GAAM,QAAS,CAAAtH,EAAYd,KAAS,WAE3Bc,GAAU,UACjBA,IAAU,MACVA,EAAM,WAAa,SAEP,UAAAuR,EAAYvD,EAAY,IAAK,OAAO,QAAQhO,CAAK,EACxDuR,IAAe,YAGlBtD,EAAQ,KAAI,CACX,GAAI1C,EAAQrM,EAAC,EACb,KAAMqS,EACN,MAAOvD,UAKVC,EAAQ,KACP,IAAI1C,EAAQrM,EAAC,EACb,KAAM,QACN,MAAAc,CAAA,KAIHgO,GAAaC,CAAO,QAEdwB,GAAI,EAGP,IAAA+B,OAA6D,IAE7DC,GAAoB,GAEpBpS,EAAQ,GACH,SAAAqS,GACRnT,EACA4E,EACA1E,EAA0B,CAGzB,eAAAF,EACA,SAAA4E,EACA,KAAA1E,EACA,GAAM,EAAAkT,IAIJ,IAAAA,MAEAC,GAAiB,GACrB,SAAS,iBAAiB,mBAAkB,WACvC,SAAS,kBAAoB,WAChCA,GAAiB,YAMbC,GAAoBC,EAAG,4BAA4B,EACnDC,GAAuBD,EAAG,6BAA6B,EACvDE,GAA2BF,EAAG,wBAAwB,EAGtDG,GACL,iEAAiE,KAChE,UAAU,SAAS,EAEjB,IAAAC,GAA2B,GAC3BC,GAAwB,GAGnB,SAAAC,GACRC,EACAC,EAA4B,KAC5BC,EAAsB,KAAI,KAEtBC,EAAM,gBACDC,GAAK,CACbD,IAEGE,EACHF,EAASxB,EAAkB,UAAW2B,GAAQ,CACxCA,IACJC,GAAiBP,EAAWC,EAAYC,CAAU,EAClDE,OAIFG,GAAiBP,EAAWC,EAAYC,CAAU,EAIrC,eAAAK,GACdP,EACAC,EAA4B,KAC5BC,EAAsB,KAAI,KAEtBtF,EAAMtG,EAAa0L,CAAS,EAE1B,MAAAQ,EAAiBrG,EAAe,kBAAkB6F,CAAS,OACjEhT,EAAWA,EAAS,SAAU,SAAA8D,KAAeA,IAAakP,CAAS,GAC/DpF,EAAI,eACD,QAAQ,IACbA,EAAI,QAAQ,IAAG,MAAQ9J,GAAQ,CACxB,MAAA2P,GAAatB,GAAW,IAAIrO,CAAQ,EAC1C,OAAA2P,IAAY,OAAM,EACXA,OAIND,IAAmB,WAAaA,IAAmB,gBACtD5F,EAAI,gBAAkB,QAGnB8F,EAAO,CACV,SAAUV,EACV,KAAY,cAAQ,IAAIpF,EAAI,OAAO,IAAKvO,GAAOwP,EAASxP,CAAE,IAC1D,WAAYuO,EAAI,oBAAsBsF,EAAa,KACvC,WAAAD,GAGTrF,EAAI,YACPA,EACE,YACA8F,EAAQ,KAAK,OACN,cAAQ,IAAI9F,EAAI,OAAO,IAAKvO,GAAOwP,EAASxP,CAAE,MAGrD,KAAMmK,GAAY,CACdoE,EAAI,YACP8F,EAAQ,KAAOlK,EACfmK,GAAgBD,CAAO,GAEvB1B,EAAcxI,EAAGwJ,CAAS,IAIzBpF,EAAI,aACHA,EAAI,eAAiB,OACnBA,EAAI,iBAAiB+F,GAAgBD,CAAO,EACvC9F,EAAI,eAAiB,WAC/B+F,GAAgBD,CAAO,EACb9F,EAAI,eAAiB,gBAC1BA,EAAI,gBAGRA,EAAI,YAAc8F,EAFlBC,GAAgBD,CAAO,IAQZ,eAAAC,GAAgBD,EAAgB,OACxCD,GAAalM,EACjB,OACAmM,EAAQ,SACRA,EAAQ,KACRA,EAAQ,WACRA,EAAQ,UAAU,EAElB,GAAG,OAAM,EAAK,KAAAzL,GAAM,SAAAnE,MAAQ,CACxB8J,EAAI,iBAAmBA,EAAI,cAC9BA,EAAI,gBAAkB,GACtB+F,GAAgB/F,EAAI,WAAW,GAEhCA,EAAI,gBAAkB,GACtBoE,EAAc/J,GAAMnE,EAAQ,EAC5B8P,GAAWC,CAAe,CAE1B,MAAG,SAAa,WAAA/P,MAAa8H,MAAM,CA6C/B,GA3CJuB,EAAe,OAAM,IACjBvB,GACH,OAAQA,GAAO,MACf,SAAUA,GAAO,cACjB,SAAA9H,KAED8P,GAAWC,CAAe,GAExBhB,IACDrL,IAAa,MACboE,GAAO,WAAa,QACpBA,GAAO,UAAY,GACnBA,GAAO,MAAQ,QACfA,GAAO,IAAMgF,KAEbiC,GAA2B,QAC3B7S,EAAQ,CACPqS,GAAYG,GAAmB1O,GAAU,SAAS,EAC/C,GAAA9D,CAAA,IAIH,CAAA8S,IACDF,IACAhH,GAAO,MAAQ,QACfA,GAAO,IAAMiF,KAEbiC,GAAwB,QACxB9S,EAAQ,CACPqS,GAAYK,GAAsB5O,GAAU,SAAS,EAClD,GAAA9D,CAAA,IAID4L,GAAO,QAAU,aACpBtE,EAAa,IAAW,MAAAsG,GAAK/N,KAAC,CACzB+N,GAAI,gBAAkB9J,IACzBiP,GAA2BlT,GAAG6T,EAAQ,UAAU,IAIlDD,GAAW,QAAO,GAEf7H,GAAO,QAAUgH,IAAoBL,GACxC,OAAO,qBACNvS,EAAQ,CACPqS,GAAYM,GAA0B7O,GAAU,OAAO,EACpD,GAAA9D,KAEF,GACH+S,GACCC,EACAU,EAAQ,WACRR,CAAU,EAEXX,GAAiB,WACP3G,GAAO,QAAU,QAAO,CAC9B,GAAAA,GAAO,QAAO,CACX,MAAAkI,GAAWlI,GAAO,QAAQ,QAC/B+E,GACC,CAAAoD,GAAGC,KAAMA,EAAC,OAEZhU,EAAQ,CACPqS,GAAYyB,GAAUhQ,GAAU,OAAO,EACpC,GAAA9D,CAAA,GAGLsH,EAAa,IAAW,MAAAsG,GAAK/N,KAAC,CAE5B+N,GAAI,gBAAkB9J,IACrB,CAAA8J,GAAI,yBAELmF,GAA2BlT,GAAG6T,EAAQ,UAAU,IAIlDD,GAAW,QAAO,EAGnB,MAAG,MAAK,EAAK,IAAAQ,GAAK,SAAAnQ,GAAU,MAAAoQ,MAAK,MACjClU,EAAQ,CAAIqS,GAAY4B,GAAKnQ,GAAUoQ,EAAK,KAAMlU,CAAQ,KAG5DmS,GAAW,IAAIa,EAAWS,EAAU,YAI7BU,GAAclD,EAA2BmD,EAAmB,CAChE,GAAA5M,IAAa,kBAGX6M,EAAc,IAAO,IAAG,iCACI7M,mBAAQ,EAEtCyJ,IAAU,QAAaA,EAAM,OAAS,GACzCoD,EAAe,aAAa,IAAI,QAASpD,CAAK,EAE/CoD,EAAe,aAAa,IAAI,cAAeD,CAAW,EAC1D,OAAO,KAAKC,EAAe,WAAY,QAAQ,EAGvC,SAAAC,GAAmBxL,EAA6B,OAClDnB,EAAMmB,EAAE,YACd9I,EAAWA,EAAS,OAAQuU,GAAMA,EAAE,KAAO5M,CAAG,GAGzC,MAAA6M,GAAmBC,GAAmB,GACxCA,GAAI,IAAQ,IAAIA,EAAM,SAAS,IAAI,EAAE,SAAW,SAAS,uBAE9CC,IAAY,CACtBnD,SACmB,IAAOxC,GAAa,uBAClBwC;AAAA,4DAGA,QAGnBnB,GAAI,UAENrJ,EAAIjJ,EAAO,qBAAqB,GAAG,EAE9B+B,EAAI,EAAGA,EAAIkH,EAAE,OAAQlH,IAAC,CACxB,MAAA6J,EAAU3C,EAAElH,CAAC,EAAE,aAAa,QAAQ,EACpC8U,EAAQ5N,EAAElH,CAAC,EAAE,aAAa,MAAM,EAGlC2U,GAAgBG,CAAK,GAAKjL,IAAY,UACzC3C,EAAElH,CAAC,EAAE,aAAa,SAAU,QAAQ,EAItCyH,EAAa,QAAS,CAAAsG,EAAK/N,IAAC,CACvB+N,EAAI,QAAQ,CAAC,EAAE,CAAC,IAAM,QACzBmF,GAA2BlT,CAAC,IAI1B,CAAAkS,IAEJjU,EAAO,iBAAiB,cAAgBgL,GAAQ,CAC1C,IAAAgI,GAAchI,CAAC,EAAa,gBAAM,oBAAoB,EACnD,SAAAzJ,EAAI,KAAAuV,EAAM,MAAAjU,CAAK,EAAKmI,EAAE,OAC9B6F,GAAgB,KAAAtP,EAAI,KAAAuV,EAAM,MAAAjU,CAAK,MAEhC7C,EAAO,iBAAiB,SAAWgL,GAAQ,CACrC,IAAAgI,GAAchI,CAAC,EAAa,gBAAM,oBAAoB,EAEnD,SAAAzJ,EAAI,MAAA0R,EAAO,KAAA9I,CAAI,EAAKa,EAAE,OAE1B,GAAAiI,IAAU,QAAO,OACZ,MAAAE,GAAO,YAAAmD,CAAW,EAAKnM,EAC/BkM,GAAclD,GAAOmD,CAAW,OACtBrD,IAAU,SAAWA,IAAU,eACzC/Q,EAAQ,CAAIqS,GAAYpK,KAAU8I,CAAK,KAAM/Q,CAAQ,GAExC6U,EAASxV,CAAE,IAAI0R,CAAK,GAE3B,QAAS+D,GAAM,CACpB,sBAAqB,KACpB/B,GAA2B+B,EAAQzV,EAAI4I,CAAI,QAM/CzE,EAAA,GAAAuO,EAAkB,EAAI,GAGd,SAAAgD,GAAe1V,EAAU,CACjC+S,GAAuBA,GAAqB,IAAKxE,GACzCA,EAAI,OAAQjG,GAAQA,IAAQtI,CAAE,GAM9B,SAAAuU,GAAWoB,EAAiC,OAC9CpG,EAAU,OAAO,QAAQoG,CAAQ,EAAE,IAAG,EAAG3V,EAAI8N,CAAc,KAC5D,IAAA/H,GAAakC,EAAa6F,EAAe,QAAQ,EACrD,OAAAA,EAAe,iBAAmB/H,GAAW,iBAC7C+H,EAAe,cAAgB/H,GAAW,eAEzC,GAAI,SAAS/F,CAAE,EACf,KAAM,iBACN,MAAO8N,KAIH1B,EAAmB0B,EAAe,uBAClC8H,EAAqB,MAAM,KAAKxJ,CAAgB,EAAE,IAAG,EACxDpM,EAAI6V,CAAc,MAElB,GAAA7V,EACA,KAAM,UACN,MAAO6V,IAAmB,aAK7BvG,GAAY,IAAKC,EAAO,GAAKqG,CAAkB,cAwC9B,OAAAE,CAAM,IAAOJ,GAAeI,CAAM,UAY/CtD,IAAsBD,CAAgB,WA8BxCC,GAAqB,EAAK,WAOzBA,GAAqB,EAAK,myBA3f3BvE,EAAa,CACf,WAAAE,EACA,OAAAC,EACA,aAAAnG,EACA,KAAA9G,EACA,IAAA+G,EACA,QACC,aAAAiK,CAAA,yBAKDhO,EAAA,GAAAiO,IAAU2D,CAAQ,mBA6XhBxB,GAAWC,CAAe", "names": ["flip", "node", "from", "to", "params", "style", "transform", "ox", "oy", "dx", "dy", "delay", "duration", "d", "easing", "cubicOut", "is_function", "t", "u", "x", "y", "sx", "sy", "insert", "target", "svg", "anchor", "append", "path", "onMount", "ctx", "div5", "div0", "div3", "div1", "div2", "button", "span", "div4", "div5_intro", "create_in_transition", "fade", "div5_outro", "create_out_transition", "message", "$$props", "type", "id", "dispatch", "createEventDispatcher", "close_message", "div", "stop_animation", "create_animation", "rect", "i", "scroll_to_top", "_messages", "messages", "g", "path0", "path1", "h1", "p0", "code0", "p1", "root", "click_handler", "represent_value", "value", "lang", "replace_file_data_with_file_function", "stringify_except_file_function", "is_potentially_nested_file_data", "obj", "key", "item", "index", "jsonString", "regex", "match", "api_logo", "if_block", "create_if_block", "attr", "img", "img_src_value", "h2", "span1", "span0", "api_count", "set_data", "t_value", "create_if_block_3", "dirty", "t1_value", "t1", "create_if_block_2", "create_if_block_1", "hr", "t6", "t6_value", "t8", "t8_value", "create_if_block_4", "h4", "current", "is_running", "endpoint_returns", "js_returns", "current_language", "code", "copy_text", "copy", "$$invalidate", "js_install", "pre", "py_install", "h3", "api_name", "fn_index", "named", "endpointdetail_changes", "create_if_block_5", "copybutton_changes", "t14_value", "span2", "span3", "span4", "span5", "t14", "t3_value", "t4", "t4_value", "t7", "t7_value", "t9", "t9_value", "t2", "t2_value", "t5", "t5_value", "dependency", "dependency_index", "endpoint_parameters", "js_parameters", "python_code", "js_code", "has_file_path", "param", "blob_components", "blob_examples", "$$value", "python", "javascript", "t0_value", "t0", "py_docs", "js_docs", "a0", "code1", "a1", "p2", "apibanner_changes", "each_blocks", "img_1", "img_1_src_value", "li", "li_class_value", "a", "a_href_value", "spaces_docs_suffix", "codesnippet_changes", "find_recursive", "child", "result", "dependencies", "app", "space_id", "root_node", "langs", "_id", "default_data", "get_info", "get_js_info", "info", "js_info", "data", "js_api_info", "language", "get", "format", "Gradio", "#id", "#el", "el", "theme", "version", "autoscroll", "event_name", "e", "binding_callbacks", "component", "theme_mode", "instance", "gradio", "elem_id", "elem_classes", "s", "p", "v", "wrap", "_target", "args", "props", "report", "propargs", "ev", "bind", "_component", "setContext", "each_value", "ensure_array_like", "get_key", "render_changes", "rendercomponent_props", "handle_prop_change", "get_spread_object", "rendercomponent_changes", "parent", "filtered_children", "$$self", "valid_node", "c", "rootNode", "logo", "create_loading_status_store", "store", "writable", "fn_inputs", "fn_outputs", "pending_outputs", "pending_inputs", "inputs_to_update", "fn_status", "update", "status", "queue", "size", "position", "eta", "progress", "outputs", "inputs", "last_status", "outputs_to_update", "new_status", "pending_count", "new_count", "queue_position", "queue_size", "register", "pending_updates", "create_components", "_component_map", "target_map", "_target_map", "constructor_map", "instance_map", "loading_status", "layout_store", "_components", "create_layout", "_app", "components", "layout", "options", "_rootNode", "dep", "process_frontend_fn", "create_target_meta", "get_inputs_outputs", "preload_all_components", "acc", "walk_layout", "get_component", "determine_interactivity", "process_server_fn", "update_scheduled", "update_scheduled_store", "flush", "j", "new_value", "update_value", "updates", "get_data", "comp", "AsyncFunction", "source", "backend_fn", "input_length", "output_length", "targets", "trigger", "input", "output", "has_no_default_value", "interactive_prop", "server_fns", "fn", "class_id", "example_components", "example_component_map", "name", "_c", "matching_component", "load_component", "example_component", "tick", "script0", "script1", "img_alt_value", "footer", "create_if_block_6", "if_block4", "MESSAGE_QUOTE_RE", "SHOW_DUPLICATE_MESSAGE_ON_ETA", "SHOW_MOBILE_QUEUE_WARNING_ON_ETA", "isCustomEvent", "event", "setupi18n", "title", "analytics_enabled", "show_api", "show_footer", "control_page_title", "app_mode", "js", "fill_height", "ready", "_layout", "scheduled_updates", "api_docs_visible", "set_api_docs_visible", "visible", "render_complete", "handle_update", "meta_updates", "update_key", "submit_map", "handled_dependencies", "new_message", "_error_id", "user_left_page", "DUPLICATE_MESSAGE", "$_", "MOBILE_QUEUE_WARNING", "MOBILE_RECONNECT_MESSAGE", "is_mobile_device", "showed_duplicate_message", "showed_mobile_warning", "wait_then_trigger_api_call", "dep_index", "trigger_id", "event_data", "_unsub", "unsub", "$scheduled_updates", "updating", "trigger_api_call", "current_status", "submission", "payload", "make_prediction", "set_status", "$loading_status", "_message", "_", "b", "log", "level", "trigger_share", "description", "discussion_url", "handle_error_close", "m", "is_external_url", "link", "handle_mount", "_link", "prop", "$targets", "dep_id", "handle_destroy", "statuses", "additional_updates", "pending_status", "detail", "$_layout"], "sources": ["../../../../node_modules/.pnpm/svelte@4.2.2/node_modules/svelte/src/runtime/animate/index.js", "../../../../js/icons/src/Error.svelte", "../../../../js/icons/src/Info.svelte", "../../../../js/icons/src/Warning.svelte", "../../../../js/statustracker/static/ToastContent.svelte", "../../../../js/statustracker/static/Toast.svelte", "../../../../js/app/src/api_docs/img/clear.svelte", "../../../../js/app/src/api_docs/NoApi.svelte", "../../../../js/app/src/api_docs/utils.ts", "../../../../js/app/src/api_docs/img/api-logo.svg", "../../../../js/app/src/api_docs/ApiBanner.svelte", "../../../../js/app/src/api_docs/ParametersSnippet.svelte", "../../../../js/app/src/api_docs/CopyButton.svelte", "../../../../js/app/src/api_docs/InstallSnippet.svelte", "../../../../js/app/src/api_docs/EndpointDetail.svelte", "../../../../js/app/src/api_docs/CodeSnippet.svelte", "../../../../js/app/src/api_docs/img/python.svg", "../../../../js/app/src/api_docs/img/javascript.svg", "../../../../js/app/src/api_docs/ResponseSnippet.svelte", "../../../../js/app/src/api_docs/ApiDocs.svelte", "../../../../js/app/src/gradio_helper.ts", "../../../../js/app/src/RenderComponent.svelte", "../../../../js/app/src/Render.svelte", "../../../../js/app/src/MountComponents.svelte", "../../../../js/app/src/images/logo.svg", "../../../../js/app/src/stores.ts", "../../../../js/app/src/init.ts", "../../../../js/app/src/Blocks.svelte"], "sourcesContent": ["import { cubicOut } from '../easing/index.js';\nimport { is_function } from '../internal/index.js';\n\n/**\n * The flip function calculates the start and end position of an element and animates between them, translating the x and y values.\n * `flip` stands for [First, Last, Invert, Play](https://aerotwist.com/blog/flip-your-animations/).\n *\n * https://svelte.dev/docs/svelte-animate#flip\n * @param {Element} node\n * @param {{ from: DOMRect; to: DOMRect }} fromTo\n * @param {import('./public.js').FlipParams} params\n * @returns {import('./public.js').AnimationConfig}\n */\nexport function flip(node, { from, to }, params = {}) {\n\tconst style = getComputedStyle(node);\n\tconst transform = style.transform === 'none' ? '' : style.transform;\n\tconst [ox, oy] = style.transformOrigin.split(' ').map(parseFloat);\n\tconst dx = from.left + (from.width * ox) / to.width - (to.left + ox);\n\tconst dy = from.top + (from.height * oy) / to.height - (to.top + oy);\n\tconst { delay = 0, duration = (d) => Math.sqrt(d) * 120, easing = cubicOut } = params;\n\treturn {\n\t\tdelay,\n\t\tduration: is_function(duration) ? duration(Math.sqrt(dx * dx + dy * dy)) : duration,\n\t\teasing,\n\t\tcss: (t, u) => {\n\t\t\tconst x = u * dx;\n\t\t\tconst y = u * dy;\n\t\t\tconst sx = t + (u * from.width) / to.width;\n\t\t\tconst sy = t + (u * from.height) / to.height;\n\t\t\treturn `transform: ${transform} translate(${x}px, ${y}px) scale(${sx}, ${sy});`;\n\t\t}\n\t};\n}\n", "<svg\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tviewBox=\"0 0 24 24\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\taria-hidden=\"true\"\n\tstroke-width=\"2\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n>\n\t<path\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t\td=\"M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z\"\n\t/>\n</svg>\n", "<svg\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tviewBox=\"0 0 24 24\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\taria-hidden=\"true\"\n\tstroke-width=\"2\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n>\n\t<path\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t\td=\"M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z\"\n\t/>\n</svg>\n", "<svg\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"2\"\n\tviewBox=\"0 0 24 24\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\taria-hidden=\"true\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n>\n\t<path\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t\td=\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\timport { Error, Info, Warning } from \"@gradio/icons\";\n\timport { createEventDispatcher, onMount } from \"svelte\";\n\timport { fade } from \"svelte/transition\";\n\timport type { ToastMessage } from \"./types\";\n\n\texport let message = \"\";\n\texport let type: ToastMessage[\"type\"];\n\texport let id: number;\n\n\tconst dispatch = createEventDispatcher();\n\n\tfunction close_message(): void {\n\t\tdispatch(\"close\", id);\n\t}\n\n\tonMount(() => {\n\t\tsetTimeout(() => {\n\t\t\tclose_message();\n\t\t}, 10000);\n\t});\n</script>\n\n<!-- TODO: fix-->\n<!-- svelte-ignore a11y-no-noninteractive-element-interactions-->\n<div\n\tclass=\"toast-body {type}\"\n\trole=\"alert\"\n\tdata-testid=\"toast-body\"\n\ton:click|stopPropagation\n\ton:keydown|stopPropagation\n\tin:fade={{ duration: 200, delay: 100 }}\n\tout:fade={{ duration: 200 }}\n>\n\t<div class=\"toast-icon {type}\">\n\t\t{#if type === \"warning\"}\n\t\t\t<Warning />\n\t\t{:else if type === \"info\"}\n\t\t\t<Info />\n\t\t{:else if type === \"error\"}\n\t\t\t<Error />\n\t\t{/if}\n\t</div>\n\n\t<div class=\"toast-details {type}\">\n\t\t<div class=\"toast-title {type}\">{type}</div>\n\t\t<div class=\"toast-text {type}\">\n\t\t\t{message}\n\t\t</div>\n\t</div>\n\n\t<button\n\t\ton:click={close_message}\n\t\tclass=\"toast-close {type}\"\n\t\ttype=\"button\"\n\t\taria-label=\"Close\"\n\t\tdata-testid=\"toast-close\"\n\t>\n\t\t<span aria-hidden=\"true\">&#215;</span>\n\t</button>\n\n\t<div class=\"timer {type}\" />\n</div>\n\n<style>\n\t.toast-body {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tright: 0;\n\t\tleft: 0;\n\t\talign-items: center;\n\t\tmargin: var(--size-6) var(--size-4);\n\t\tmargin: auto;\n\t\tborder-radius: var(--container-radius);\n\t\toverflow: hidden;\n\t\tpointer-events: auto;\n\t}\n\n\t.toast-body.error {\n\t\tborder: 1px solid var(--color-red-700);\n\t\tbackground: var(--color-red-50);\n\t}\n\t:global(.dark) .toast-body.error {\n\t\tborder: 1px solid var(--color-red-500);\n\t\tbackground-color: var(--color-grey-950);\n\t}\n\n\t.toast-body.warning {\n\t\tborder: 1px solid var(--color-yellow-700);\n\t\tbackground: var(--color-yellow-50);\n\t}\n\t:global(.dark) .toast-body.warning {\n\t\tborder: 1px solid var(--color-yellow-500);\n\t\tbackground-color: var(--color-grey-950);\n\t}\n\n\t.toast-body.info {\n\t\tborder: 1px solid var(--color-grey-700);\n\t\tbackground: var(--color-grey-50);\n\t}\n\t:global(.dark) .toast-body.info {\n\t\tborder: 1px solid var(--color-grey-500);\n\t\tbackground-color: var(--color-grey-950);\n\t}\n\n\t.toast-title {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tfont-weight: var(--weight-bold);\n\t\tfont-size: var(--text-lg);\n\t\tline-height: var(--line-sm);\n\t\ttext-transform: capitalize;\n\t}\n\n\t.toast-title.error {\n\t\tcolor: var(--color-red-700);\n\t}\n\t:global(.dark) .toast-title.error {\n\t\tcolor: var(--color-red-50);\n\t}\n\n\t.toast-title.warning {\n\t\tcolor: var(--color-yellow-700);\n\t}\n\t:global(.dark) .toast-title.warning {\n\t\tcolor: var(--color-yellow-50);\n\t}\n\n\t.toast-title.info {\n\t\tcolor: var(--color-grey-700);\n\t}\n\t:global(.dark) .toast-title.info {\n\t\tcolor: var(--color-grey-50);\n\t}\n\n\t.toast-close {\n\t\tmargin: 0 var(--size-3);\n\t\tborder-radius: var(--size-3);\n\t\tpadding: 0px var(--size-1-5);\n\t\tfont-size: var(--size-5);\n\t\tline-height: var(--size-5);\n\t}\n\n\t.toast-close.error {\n\t\tcolor: var(--color-red-700);\n\t}\n\t:global(.dark) .toast-close.error {\n\t\tcolor: var(--color-red-500);\n\t}\n\n\t.toast-close.warning {\n\t\tcolor: var(--color-yellow-700);\n\t}\n\t:global(.dark) .toast-close.warning {\n\t\tcolor: var(--color-yellow-500);\n\t}\n\n\t.toast-close.info {\n\t\tcolor: var(--color-grey-700);\n\t}\n\t:global(.dark) .toast-close.info {\n\t\tcolor: var(--color-grey-500);\n\t}\n\n\t.toast-text {\n\t\tfont-size: var(--text-lg);\n\t}\n\n\t.toast-text.error {\n\t\tcolor: var(--color-red-700);\n\t}\n\t:global(.dark) .toast-text.error {\n\t\tcolor: var(--color-red-50);\n\t}\n\n\t.toast-text.warning {\n\t\tcolor: var(--color-yellow-700);\n\t}\n\n\t:global(.dark) .toast-text.warning {\n\t\tcolor: var(--color-yellow-50);\n\t}\n\n\t.toast-text.info {\n\t\tcolor: var(--color-grey-700);\n\t}\n\n\t:global(.dark) .toast-text.info {\n\t\tcolor: var(--color-grey-50);\n\t}\n\n\t.toast-details {\n\t\tmargin: var(--size-3) var(--size-3) var(--size-3) 0;\n\t\twidth: 100%;\n\t}\n\n\t.toast-icon {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tposition: relative;\n\t\tflex-shrink: 0;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmargin: var(--size-2);\n\t\tborder-radius: var(--radius-full);\n\t\tpadding: var(--size-1);\n\t\tpadding-left: calc(var(--size-1) - 1px);\n\t\twidth: 35px;\n\t\theight: 35px;\n\t}\n\n\t.toast-icon.error {\n\t\tcolor: var(--color-red-700);\n\t}\n\n\t:global(.dark) .toast-icon.error {\n\t\tcolor: var(--color-red-500);\n\t}\n\n\t.toast-icon.warning {\n\t\tcolor: var(--color-yellow-700);\n\t}\n\n\t:global(.dark) .toast-icon.warning {\n\t\tcolor: var(--color-yellow-500);\n\t}\n\n\t.toast-icon.info {\n\t\tcolor: var(--color-grey-700);\n\t}\n\n\t:global(.dark) .toast-icon.info {\n\t\tcolor: var(--color-grey-500);\n\t}\n\n\t@keyframes countdown {\n\t\tfrom {\n\t\t\ttransform: scaleX(1);\n\t\t}\n\t\tto {\n\t\t\ttransform: scaleX(0);\n\t\t}\n\t}\n\n\t.timer {\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\ttransform-origin: 0 0;\n\t\tanimation: countdown 10s linear forwards;\n\t\twidth: 100%;\n\t\theight: var(--size-1);\n\t}\n\n\t.timer.error {\n\t\tbackground: var(--color-red-700);\n\t}\n\n\t:global(.dark) .timer.error {\n\t\tbackground: var(--color-red-500);\n\t}\n\n\t.timer.warning {\n\t\tbackground: var(--color-yellow-700);\n\t}\n\n\t:global(.dark) .timer.warning {\n\t\tbackground: var(--color-yellow-500);\n\t}\n\n\t.timer.info {\n\t\tbackground: var(--color-grey-700);\n\t}\n\n\t:global(.dark) .timer.info {\n\t\tbackground: var(--color-grey-500);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { flip } from \"svelte/animate\";\n\timport type { ToastMessage } from \"./types\";\n\timport ToastContent from \"./ToastContent.svelte\";\n\n\texport let messages: ToastMessage[] = [];\n\n\t$: scroll_to_top(messages);\n\n\tfunction scroll_to_top(_messages: ToastMessage[]): void {\n\t\tif (_messages.length > 0) {\n\t\t\tif (\"parentIFrame\" in window) {\n\t\t\t\twindow.parentIFrame?.scrollTo(0, 0);\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<div class=\"toast-wrap\">\n\t{#each messages as { type, message, id } (id)}\n\t\t<div animate:flip={{ duration: 300 }} style:width=\"100%\">\n\t\t\t<ToastContent {type} {message} on:close {id} />\n\t\t</div>\n\t{/each}\n</div>\n\n<style>\n\t.toast-wrap {\n\t\tdisplay: flex;\n\t\tposition: fixed;\n\t\ttop: var(--size-4);\n\t\tright: var(--size-4);\n\n\t\tflex-direction: column;\n\t\talign-items: end;\n\t\tgap: var(--size-2);\n\t\tz-index: var(--layer-top);\n\t\twidth: calc(100% - var(--size-8));\n\t}\n\n\t@media (--screen-sm) {\n\t\t.toast-wrap {\n\t\t\twidth: calc(var(--size-96) + var(--size-10));\n\t\t}\n\t}\n</style>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 5 5\"\n\tversion=\"1.1\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\txml:space=\"preserve\"\n\tstyle=\"fill:currentColor;fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;\"\n>\n\t<g>\n\t\t<path\n\t\t\td=\"M3.789,0.09C3.903,-0.024 4.088,-0.024 4.202,0.09L4.817,0.705C4.931,0.819 4.931,1.004 4.817,1.118L1.118,4.817C1.004,4.931 0.819,4.931 0.705,4.817L0.09,4.202C-0.024,4.088 -0.024,3.903 0.09,3.789L3.789,0.09Z\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M4.825,3.797C4.934,3.907 4.934,4.084 4.825,4.193L4.193,4.825C4.084,4.934 3.907,4.934 3.797,4.825L0.082,1.11C-0.027,1.001 -0.027,0.823 0.082,0.714L0.714,0.082C0.823,-0.027 1.001,-0.027 1.11,0.082L4.825,3.797Z\"\n\t\t/>\n\t</g>\n</svg>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport Clear from \"./img/clear.svelte\";\n\n\tconst dispatch = createEventDispatcher();\n\n\texport let root: string;\n</script>\n\n<div class=\"wrap prose\">\n\t<h1>API Docs</h1>\n\t<p class=\"attention\">\n\t\tNo API Routes found for\n\t\t<code>\n\t\t\t{root}\n\t\t</code>\n\t</p>\n\t<p>\n\t\tTo expose an API endpoint of your app in this page, set the <code>\n\t\t\tapi_name\n\t\t</code>\n\t\tparameter of the event listener.\n\t\t<br />\n\t\tFor more information, visit the\n\t\t<a href=\"https://gradio.app/sharing_your_app/#api-page\" target=\"_blank\">\n\t\t\tAPI Page guide\n\t\t</a>\n\t\t. To hide the API documentation button and this page, set\n\t\t<code>show_api=False</code>\n\t\tin the\n\t\t<code>Blocks.launch()</code>\n\t\tmethod.\n\t</p>\n</div>\n\n<button on:click={() => dispatch(\"close\")}>\n\t<Clear />\n</button>\n\n<style>\n\t.wrap {\n\t\tpadding: var(--size-6);\n\t}\n\n\t.attention {\n\t\tfont-weight: var(--weight-bold);\n\t\tfont-size: var(--text-lg);\n\t}\n\n\t.attention code {\n\t\tborder: none;\n\t\tbackground: none;\n\t\tcolor: var(--color-accent);\n\t\tfont-weight: var(--weight-bold);\n\t}\n\n\tbutton {\n\t\tposition: absolute;\n\t\ttop: var(--size-5);\n\t\tright: var(--size-6);\n\t\twidth: var(--size-4);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\tbutton:hover {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t@media (--screen-md) {\n\t\tbutton {\n\t\t\ttop: var(--size-6);\n\t\t}\n\t}\n</style>\n", "// eslint-disable-next-line complexity\nexport function represent_value(\n\tvalue: string,\n\ttype: string | undefined,\n\tlang: \"js\" | \"py\" | null = null\n): string | null | number | boolean | Record<string, unknown> {\n\tif (type === undefined) {\n\t\treturn lang === \"py\" ? \"None\" : null;\n\t}\n\tif (value === null && lang === \"py\") {\n\t\treturn \"None\";\n\t}\n\tif (type === \"string\" || type === \"str\") {\n\t\treturn lang === null ? value : '\"' + value + '\"';\n\t} else if (type === \"number\") {\n\t\treturn lang === null ? parseFloat(value) : value;\n\t} else if (type === \"boolean\" || type == \"bool\") {\n\t\tif (lang === \"py\") {\n\t\t\tvalue = String(value);\n\t\t\treturn value === \"true\" ? \"True\" : \"False\";\n\t\t} else if (lang === \"js\") {\n\t\t\treturn value;\n\t\t}\n\t\treturn value === \"true\";\n\t} else if (type === \"List[str]\") {\n\t\tvalue = JSON.stringify(value);\n\t\treturn value;\n\t} else if (type.startsWith(\"Literal['\")) {\n\t\t// a literal of strings\n\t\treturn '\"' + value + '\"';\n\t}\n\t// assume object type\n\tif (lang === null) {\n\t\treturn value === \"\" ? null : JSON.parse(value);\n\t} else if (typeof value === \"string\") {\n\t\tif (value === \"\") {\n\t\t\treturn lang === \"py\" ? \"None\" : \"null\";\n\t\t}\n\t\treturn value;\n\t}\n\tif (lang === \"py\") {\n\t\tvalue = replace_file_data_with_file_function(value);\n\t}\n\treturn stringify_except_file_function(value);\n}\n\nexport function is_potentially_nested_file_data(obj: any): boolean {\n\tif (typeof obj === \"object\" && obj !== null) {\n\t\tif (obj.hasOwnProperty(\"url\") && obj.hasOwnProperty(\"meta\")) {\n\t\t\tif (\n\t\t\t\ttypeof obj.meta === \"object\" &&\n\t\t\t\tobj.meta !== null &&\n\t\t\t\tobj.meta._type === \"gradio.FileData\"\n\t\t\t) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t}\n\tif (typeof obj === \"object\" && obj !== null) {\n\t\tfor (let key in obj) {\n\t\t\tif (typeof obj[key] === \"object\") {\n\t\t\t\tlet result = is_potentially_nested_file_data(obj[key]);\n\t\t\t\tif (result) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\treturn false;\n}\n\nfunction replace_file_data_with_file_function(obj: any): any {\n\tif (typeof obj === \"object\" && obj !== null && !Array.isArray(obj)) {\n\t\tif (\n\t\t\t\"url\" in obj &&\n\t\t\t\"meta\" in obj &&\n\t\t\tobj.meta?._type === \"gradio.FileData\"\n\t\t) {\n\t\t\treturn `file('${obj.url}')`;\n\t\t}\n\t}\n\tif (Array.isArray(obj)) {\n\t\tobj.forEach((item, index) => {\n\t\t\tif (typeof item === \"object\" && item !== null) {\n\t\t\t\tobj[index] = replace_file_data_with_file_function(item); // Recurse and update array elements\n\t\t\t}\n\t\t});\n\t} else if (typeof obj === \"object\" && obj !== null) {\n\t\tObject.keys(obj).forEach((key) => {\n\t\t\tobj[key] = replace_file_data_with_file_function(obj[key]); // Recurse and update object properties\n\t\t});\n\t}\n\treturn obj;\n}\n\nfunction stringify_except_file_function(obj: any): string {\n\tconst jsonString = JSON.stringify(obj, (key, value) => {\n\t\tif (\n\t\t\ttypeof value === \"string\" &&\n\t\t\tvalue.startsWith(\"file(\") &&\n\t\t\tvalue.endsWith(\")\")\n\t\t) {\n\t\t\treturn `UNQUOTED${value}`; // Flag the special strings\n\t\t}\n\t\treturn value;\n\t});\n\tconst regex = /\"UNQUOTEDfile\\(([^)]*)\\)\"/g;\n\treturn jsonString.replace(regex, (match, p1) => `file(${p1})`);\n}\n", "export default \"__VITE_ASSET__ffd29fb0__\"", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport api_logo from \"./img/api-logo.svg\";\n\timport Clear from \"./img/clear.svelte\";\n\n\texport let root: string;\n\texport let api_count: number;\n\n\tconst dispatch = createEventDispatcher();\n</script>\n\n<h2>\n\t<img src={api_logo} alt=\"\" />\n\t<div>\n\t\tAPI documentation\n\t\t<div class=\"url\">\n\t\t\t{root}\n\t\t</div>\n\t</div>\n\t<span class=\"counts\">\n\t\t<span class=\"url\">{api_count}</span> API endpoint{#if api_count > 1}s{/if}\n\t</span>\n</h2>\n\n<button on:click={() => dispatch(\"close\")}>\n\t<Clear />\n</button>\n\n<style>\n\th2 {\n\t\tdisplay: flex;\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-semibold);\n\t\tgap: var(--size-4);\n\t}\n\n\th2 img {\n\t\tmargin-right: var(--size-2);\n\t\twidth: var(--size-4);\n\t\tdisplay: inline-block;\n\t}\n\n\t.url {\n\t\tcolor: var(--color-accent);\n\t\tfont-weight: normal;\n\t}\n\n\tbutton {\n\t\tposition: absolute;\n\t\ttop: var(--size-5);\n\t\tright: var(--size-6);\n\t\twidth: var(--size-4);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\tbutton:hover {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t@media (--screen-md) {\n\t\tbutton {\n\t\t\ttop: var(--size-6);\n\t\t}\n\n\t\th2 img {\n\t\t\twidth: var(--size-5);\n\t\t}\n\t}\n\n\t.counts {\n\t\tmargin-top: auto;\n\t\tmargin-right: var(--size-8);\n\t\tmargin-bottom: auto;\n\t\tmargin-left: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-light);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { Loader } from \"@gradio/statustracker\";\n\timport { represent_value } from \"./utils\";\n\n\texport let is_running: boolean;\n\texport let endpoint_returns: any;\n\texport let js_returns: any;\n\texport let current_language: \"python\" | \"javascript\";\n</script>\n\n<h4>\n\t<div class=\"toggle-icon\">\n\t\t<div class=\"toggle-dot\" />\n\t</div>\n\tAccepts {endpoint_returns.length} parameter{#if endpoint_returns.length != 1}s{/if}:\n</h4>\n\n<div class:hide={is_running}>\n\t{#each endpoint_returns as { label, python_type, component, parameter_name, parameter_has_default, parameter_default }, i}\n\t\t<hr class=\"hr\" />\n\t\t<div style=\"margin:10px;\">\n\t\t\t<p style=\"white-space: nowrap; overflow-x: auto;\">\n\t\t\t\t<span class=\"code\" style=\"margin-right: 10px;\"\n\t\t\t\t\t>{parameter_name && current_language == \"python\"\n\t\t\t\t\t\t? parameter_name\n\t\t\t\t\t\t: \"[\" + i + \"]\"}</span\n\t\t\t\t>\n\t\t\t\t<span class=\"code highlight\" style=\"margin-right: 10px;\"\n\t\t\t\t\t>{#if current_language === \"python\"}{python_type.type}{#if parameter_has_default && parameter_default === null}&nbsp;|\n\t\t\t\t\t\t\tNone{/if}{:else}{js_returns[i].type}{/if}</span\n\t\t\t\t>\n\t\t\t\t{#if !parameter_has_default || current_language == \"javascript\"}<span\n\t\t\t\t\t\tstyle=\"font-weight:bold\">Required</span\n\t\t\t\t\t>{:else}<span> Default: </span><span\n\t\t\t\t\t\tclass=\"code\"\n\t\t\t\t\t\tstyle=\"font-size: var(--text-sm);\"\n\t\t\t\t\t\t>{represent_value(parameter_default, python_type.type, \"py\")}</span\n\t\t\t\t\t>{/if}\n\t\t\t</p>\n\t\t\t<p class=\"desc\">\n\t\t\t\tThe input value that is provided in the \"{label}\" <!--\n\t-->{component}\n\t\t\t\tcomponent<!--\n\t-->.\n\t\t\t</p>\n\t\t</div>\n\t{/each}\n</div>\n{#if is_running}\n\t<div class=\"load-wrap\">\n\t\t<Loader margin={false} />\n\t</div>\n{/if}\n\n<style>\n\t.hr {\n\t\tborder: 0;\n\t\theight: 1px;\n\t\tbackground: var(--color-accent-soft);\n\t\tmargin-bottom: 12px;\n\t}\n\n\t.code {\n\t\tfont-family: var(--font-mono);\n\t\tdisplay: inline;\n\t}\n\n\t.highlight {\n\t\tbackground: var(--color-accent-soft);\n\t\tcolor: var(--color-accent);\n\t\tpadding: var(--size-1);\n\t}\n\n\t.desc {\n\t\tcolor: var(--body-text-color-subdued);\n\t\tfont-size: var(--text-lg);\n\t\tmargin-top: var(--size-1);\n\t}\n\n\th4 {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-top: var(--size-6);\n\t\tmargin-bottom: var(--size-3);\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-bold);\n\t}\n\n\t.toggle-icon {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-right: var(--size-2);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-grey-300);\n\t\twidth: 12px;\n\t\theight: 4px;\n\t}\n\n\t.toggle-dot {\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-grey-700);\n\t\twidth: 6px;\n\t\theight: 6px;\n\t\tmargin-right: auto;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { BaseButton } from \"@gradio/button\";\n\texport let code: string;\n\tlet copy_text = \"copy\";\n\n\tfunction copy(): void {\n\t\tnavigator.clipboard.writeText(code);\n\t\tcopy_text = \"copied!\";\n\t\tsetTimeout(() => {\n\t\t\tcopy_text = \"copy\";\n\t\t}, 1500);\n\t}\n</script>\n\n<BaseButton size=\"sm\" on:click={copy}>\n\t{copy_text}\n</BaseButton>\n", "<script lang=\"ts\">\n\timport CopyButton from \"./CopyButton.svelte\";\n\timport { Block } from \"@gradio/atoms\";\n\n\texport let current_language: \"python\" | \"javascript\";\n\n\tlet py_install = \"pip install gradio_client\";\n\tlet js_install = \"npm i -D @gradio/client\";\n</script>\n\n<Block border_mode=\"contrast\">\n\t<code>\n\t\t{#if current_language === \"python\"}\n\t\t\t<div class=\"copy\">\n\t\t\t\t<CopyButton code={py_install} />\n\t\t\t</div>\n\t\t\t<div>\n\t\t\t\t<pre>$ {py_install}</pre>\n\t\t\t</div>\n\t\t{:else if current_language === \"javascript\"}\n\t\t\t<div class=\"copy\">\n\t\t\t\t<CopyButton code={js_install} />\n\t\t\t</div>\n\t\t\t<div>\n\t\t\t\t<pre>$ {js_install}</pre>\n\t\t\t</div>\n\t\t{/if}\n\t</code>\n</Block>\n\n<style>\n\tcode pre {\n\t\toverflow-x: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-family: var(--font-mono);\n\t\ttab-size: 2;\n\t}\n\n\tcode {\n\t\tposition: relative;\n\t\tdisplay: block;\n\t}\n\n\t.copy {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tmargin-top: -5px;\n\t\tmargin-right: -5px;\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let api_name: string | null = null;\n\texport let fn_index: number | null = null;\n\texport let named: boolean;\n</script>\n\n{#if named}\n\t<h3>\n\t\tapi_name:\n\t\t<span class=\"post\">{\"/\" + api_name}</span>\n\t</h3>\n{:else}\n\t<h3>\n\t\tfn_index:\n\t\t<span class=\"post\">{fn_index}</span>\n\t</h3>\n{/if}\n\n<style>\n\th3 {\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--section-header-text-weight);\n\t\tfont-size: var(--text-lg);\n\t}\n\n\t.post {\n\t\tmargin-right: var(--size-2);\n\t\tborder: 1px solid var(--border-color-accent);\n\t\tborder-radius: var(--radius-sm);\n\t\tbackground: var(--color-accent-soft);\n\t\tpadding-right: var(--size-1);\n\t\tpadding-bottom: var(--size-1);\n\t\tpadding-left: var(--size-1);\n\t\tcolor: var(--color-accent);\n\t\tfont-weight: var(--weight-semibold);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { ComponentMeta, Dependency } from \"../types\";\n\timport CopyButton from \"./CopyButton.svelte\";\n\timport { represent_value, is_potentially_nested_file_data } from \"./utils\";\n\timport { Block } from \"@gradio/atoms\";\n\timport EndpointDetail from \"./EndpointDetail.svelte\";\n\n\tinterface EndpointParameter {\n\t\tlabel: string;\n\t\ttype: string;\n\t\tpython_type: { type: string };\n\t\tcomponent: string;\n\t\texample_input: string;\n\t\tserializer: string;\n\t}\n\n\texport let dependency: Dependency;\n\texport let dependency_index: number;\n\texport let root: string;\n\texport let endpoint_parameters: any;\n\texport let js_parameters: any;\n\texport let named: boolean;\n\n\texport let current_language: \"python\" | \"javascript\";\n\n\tlet python_code: HTMLElement;\n\tlet js_code: HTMLElement;\n\n\tlet has_file_path = endpoint_parameters.some((param: EndpointParameter) =>\n\t\tis_potentially_nested_file_data(param.example_input)\n\t);\n\tlet blob_components = [\"Audio\", \"File\", \"Image\", \"Video\"];\n\tlet blob_examples: any[] = endpoint_parameters.filter(\n\t\t(param: EndpointParameter) => blob_components.includes(param.component)\n\t);\n</script>\n\n<div class=\"container\">\n\t{#if named}\n\t\t<EndpointDetail {named} api_name={dependency.api_name} />\n\t{:else}\n\t\t<EndpointDetail {named} fn_index={dependency_index} />\n\t{/if}\n\t<Block>\n\t\t<code>\n\t\t\t{#if current_language === \"python\"}\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={python_code?.innerText} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={python_code}>\n\t\t\t\t\t<pre><span class=\"highlight\">from</span> gradio_client <span\n\t\t\t\t\t\t\tclass=\"highlight\">import</span\n\t\t\t\t\t\t> Client{#if has_file_path}, file{/if}\n\nclient = Client(<span class=\"token string\">\"{root}\"</span>)\nresult = client.<span class=\"highlight\">predict</span\n\t\t\t\t\t\t>(<!--\n-->{#each endpoint_parameters as { python_type, example_input, parameter_name, parameter_has_default, parameter_default }, i}<!--\n        -->\n\t\t{parameter_name\n\t\t\t\t\t\t\t\t? parameter_name + \"=\"\n\t\t\t\t\t\t\t\t: \"\"}<span class=\"example-inputs\"\n\t\t\t\t\t\t\t\t>{represent_value(\n\t\t\t\t\t\t\t\t\tparameter_has_default ? parameter_default : example_input,\n\t\t\t\t\t\t\t\t\tpython_type.type,\n\t\t\t\t\t\t\t\t\t\"py\"\n\t\t\t\t\t\t\t\t)}</span\n\t\t\t\t\t\t\t>,{/each}<!--\n\n\t\t-->\n\t\tapi_name=<span class=\"api-name\">\"/{dependency.api_name}\"</span><!--\n\t\t-->\n)\n<span class=\"highlight\">print</span>(result)</pre>\n\t\t\t\t</div>\n\t\t\t{:else if current_language === \"javascript\"}\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={js_code?.innerText} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={js_code}>\n\t\t\t\t\t<pre>import &lbrace; client &rbrace; from \"@gradio/client\";\n{#each blob_examples as { label, type, python_type, component, example_input, serializer }, i}<!--\n-->\nconst response_{i} = await fetch(\"{example_input.url}\");\nconst example{component} = await response_{i}.blob();\n\t\t\t\t\t\t{/each}<!--\n-->\nconst app = await client(<span class=\"token string\">\"{root}\"</span>);\nconst result = await app.predict({#if named}\"/{dependency.api_name}\"{:else}{dependency_index}{/if}, [<!--\n-->{#each endpoint_parameters as { label, type, python_type, component, example_input, serializer }, i}<!--\n\t\t-->{#if blob_components.includes(component)}<!--\n\t-->\n\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass=\"example-inputs\">example{component}</span\n\t\t\t\t\t\t\t\t>, <!--\n\t\t--><span class=\"desc\"\n\t\t\t\t\t\t\t\t\t><!--\n\t\t-->\t// blob <!--\n\t\t-->in '{label}' <!--\n\t\t-->{component} component<!--\n\t\t--></span\n\t\t\t\t\t\t\t\t><!--\n\t\t-->{:else}<!--\n\t-->\t\t\n\t\t\t\t<span class=\"example-inputs\"\n\t\t\t\t\t\t\t\t\t>{represent_value(\n\t\t\t\t\t\t\t\t\t\texample_input,\n\t\t\t\t\t\t\t\t\t\tpython_type.type,\n\t\t\t\t\t\t\t\t\t\t\"js\"\n\t\t\t\t\t\t\t\t\t)}</span\n\t\t\t\t\t\t\t\t>, <!--\n--><span class=\"desc\"\n\t\t\t\t\t\t\t\t\t><!--\n-->// {js_parameters[i]\n\t\t\t\t\t\t\t\t\t\t.type} {#if js_parameters[i].description}({js_parameters[i]\n\t\t\t\t\t\t\t\t\t\t\t.description}){/if}<!--\n--> in '{label}' <!--\n-->{component} component<!--\n--></span\n\t\t\t\t\t\t\t\t><!--\n-->{/if}\n\t\t\t\t\t\t{/each}\n\t]);\n\nconsole.log(result.data);\n</pre>\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t</code>\n\t</Block>\n</div>\n\n<style>\n\tcode pre {\n\t\toverflow-x: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-family: var(--font-mono);\n\t\ttab-size: 2;\n\t}\n\n\t.token.string {\n\t\tdisplay: contents;\n\t\tcolor: var(--color-accent-base);\n\t}\n\n\tcode {\n\t\tposition: relative;\n\t\tdisplay: block;\n\t}\n\n\t.copy {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tmargin-top: -5px;\n\t\tmargin-right: -5px;\n\t}\n\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-xxl);\n\t\tmargin-top: var(--size-3);\n\t\tmargin-bottom: var(--size-3);\n\t}\n\n\t.desc {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.example-inputs {\n\t\tcolor: var(--color-accent);\n\t}\n\t.api-name {\n\t\tcolor: var(--color-accent);\n\t}\n</style>\n", "export default \"__VITE_ASSET__dc7a038c__\"", "export default \"__VITE_ASSET__90c1298d__\"", "<script lang=\"ts\">\n\timport { Loader } from \"@gradio/statustracker\";\n\n\texport let is_running: boolean;\n\texport let endpoint_returns: any;\n\texport let js_returns: any;\n\texport let current_language: \"python\" | \"javascript\";\n</script>\n\n<h4>\n\t<div class=\"toggle-icon\">\n\t\t<div class=\"toggle-dot toggle-right\" />\n\t</div>\n\tReturns {#if endpoint_returns.length > 1}\n\t\t{current_language == \"python\" ? \"tuple\" : \"list\"} of {endpoint_returns.length}\n\t\telements{:else}\n\t\t1 element{/if}\n</h4>\n\n<div class:hide={is_running}>\n\t{#each endpoint_returns as { label, type, python_type, component, serializer }, i}\n\t\t<hr class=\"hr\" />\n\t\t<div style=\"margin:10px;\">\n\t\t\t<p>\n\t\t\t\t{#if endpoint_returns.length > 1}\n\t\t\t\t\t<span class=\"code\">[{i}]</span>\n\t\t\t\t{/if}\n\t\t\t\t<span class=\"code highlight\"\n\t\t\t\t\t>{#if current_language === \"python\"}{python_type.type}{:else}{js_returns[\n\t\t\t\t\t\t\ti\n\t\t\t\t\t\t].type}{/if}</span\n\t\t\t\t>\n\t\t\t</p>\n\t\t\t<p class=\"desc\">\n\t\t\t\tThe output value that appears in the \"{label}\" <!--\n\t-->{component}\n\t\t\t\tcomponent<!--\n\t-->.\n\t\t\t</p>\n\t\t</div>\n\t{/each}\n</div>\n{#if is_running}\n\t<div class=\"load-wrap\">\n\t\t<Loader margin={false} />\n\t</div>\n{/if}\n\n<style>\n\t.hr {\n\t\tborder: 0;\n\t\theight: 1px;\n\t\tbackground: var(--color-accent-soft);\n\t}\n\t.code {\n\t\tfont-family: var(--font-mono);\n\t\tmargin-right: 10px;\n\t}\n\n\t.highlight {\n\t\tbackground: var(--color-accent-soft);\n\t\tcolor: var(--color-accent);\n\t\tpadding: var(--size-1);\n\t}\n\n\t.desc {\n\t\tcolor: var(--body-text-color-subdued);\n\t\tfont-size: var(--text-lg);\n\t}\n\n\th4 {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-top: var(--size-6);\n\t\tmargin-bottom: var(--size-3);\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-bold);\n\t}\n\n\t.toggle-icon {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-right: var(--size-2);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-grey-300);\n\t\twidth: 12px;\n\t\theight: 4px;\n\t}\n\n\t.toggle-dot {\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-grey-700);\n\t\twidth: 6px;\n\t\theight: 6px;\n\t\tmargin-left: auto;\n\t}\n</style>\n", "<script lang=\"ts\">\n\t/* eslint-disable */\n\timport { onMount, createEventDispatcher } from \"svelte\";\n\timport type { ComponentMeta, Dependency } from \"../types\";\n\timport { post_data } from \"@gradio/client\";\n\timport NoApi from \"./NoApi.svelte\";\n\timport type { client } from \"@gradio/client\";\n\n\timport { represent_value } from \"./utils\";\n\n\timport ApiBanner from \"./ApiBanner.svelte\";\n\timport ParametersSnippet from \"./ParametersSnippet.svelte\";\n\timport InstallSnippet from \"./InstallSnippet.svelte\";\n\timport CodeSnippet from \"./CodeSnippet.svelte\";\n\n\timport python from \"./img/python.svg\";\n\timport javascript from \"./img/javascript.svg\";\n\timport ResponseSnippet from \"./ResponseSnippet.svelte\";\n\n\texport let dependencies: Dependency[];\n\texport let root: string;\n\texport let app: Awaited<ReturnType<typeof client>>;\n\texport let space_id: string | null;\n\texport let root_node: ComponentMeta;\n\tconst js_docs =\n\t\t\"https://www.gradio.app/guides/getting-started-with-the-js-client\";\n\tconst py_docs =\n\t\t\"https://www.gradio.app/guides/getting-started-with-the-python-client\";\n\tconst spaces_docs_suffix = \"#connecting-to-a-hugging-face-space\";\n\n\tlet api_count = dependencies.filter(\n\t\t(dependency) => dependency.show_api\n\t).length;\n\n\tif (root === \"\") {\n\t\troot = location.protocol + \"//\" + location.host + location.pathname;\n\t}\n\tif (!root.endsWith(\"/\")) {\n\t\troot += \"/\";\n\t}\n\n\tlet current_language: \"python\" | \"javascript\" = \"python\";\n\n\tconst langs = [\n\t\t[\"python\", python],\n\t\t[\"javascript\", javascript]\n\t] as const;\n\n\tlet is_running = false;\n\n\tfunction find_recursive(\n\t\tnode: ComponentMeta,\n\t\tid: number\n\t): ComponentMeta | null {\n\t\tif (node.id === id) {\n\t\t\treturn node;\n\t\t}\n\t\tif (node.children) {\n\t\t\tfor (let child of node.children) {\n\t\t\t\tlet result = find_recursive(child, id);\n\t\t\t\tif (result) {\n\t\t\t\t\treturn result;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn null;\n\t}\n\n\tlet dependency_inputs = dependencies.map((dependency) =>\n\t\tdependency.inputs.map((_id) => {\n\t\t\tlet default_data = find_recursive(root_node, _id)?.props?.default;\n\t\t\tif (default_data === undefined) {\n\t\t\t\tdefault_data = \"\";\n\t\t\t} else if (typeof default_data === \"object\") {\n\t\t\t\tdefault_data = JSON.stringify(default_data);\n\t\t\t}\n\t\t\treturn default_data;\n\t\t})\n\t);\n\n\tlet dependency_outputs: any[][] = dependencies.map(\n\t\t(dependency) => new Array(dependency.outputs.length)\n\t);\n\n\tlet dependency_failures: boolean[][] = dependencies.map((dependency) =>\n\t\tnew Array(dependency.inputs.length).fill(false)\n\t);\n\n\tasync function get_info(): Promise<{\n\t\tnamed_endpoints: any;\n\t\tunnamed_endpoints: any;\n\t}> {\n\t\tlet response = await fetch(root + \"info\");\n\t\tlet data = await response.json();\n\t\treturn data;\n\t}\n\tasync function get_js_info(): Promise<Record<string, any>> {\n\t\tlet js_api_info = await app.view_api();\n\t\treturn js_api_info;\n\t}\n\n\tlet info: {\n\t\tnamed_endpoints: any;\n\t\tunnamed_endpoints: any;\n\t};\n\n\tlet js_info: Record<string, any>;\n\n\tget_info().then((data) => {\n\t\tinfo = data;\n\t});\n\n\tget_js_info().then((js_api_info) => {\n\t\tjs_info = js_api_info;\n\t});\n\n\tasync function run(index: number): Promise<void> {\n\t\tis_running = true;\n\t\tlet dependency = dependencies[index];\n\t\tlet attempted_component_index = 0;\n\t\ttry {\n\t\t\tvar inputs = dependency_inputs[index].map((input_val: any, i: number) => {\n\t\t\t\tattempted_component_index = i;\n\t\t\t\tlet component = find_recursive(root_node, dependency.inputs[i])!;\n\t\t\t\tinput_val = represent_value(\n\t\t\t\t\tinput_val,\n\t\t\t\t\tcomponent.documentation?.type?.input_payload ||\n\t\t\t\t\t\tcomponent.documentation?.type?.payload\n\t\t\t\t);\n\t\t\t\tdependency_failures[index][attempted_component_index] = false;\n\t\t\t\treturn input_val;\n\t\t\t});\n\t\t} catch (err) {\n\t\t\tdependency_failures[index][attempted_component_index] = true;\n\t\t\tis_running = false;\n\t\t\treturn;\n\t\t}\n\t\tlet [response, status_code] = await post_data(\n\t\t\t`${root}run/${dependency.api_name}`,\n\t\t\t{\n\t\t\t\tdata: inputs\n\t\t\t}\n\t\t);\n\t\tis_running = false;\n\t\tif (status_code == 200) {\n\t\t\tdependency_outputs[index] = response.data.map(\n\t\t\t\t(output_val: any, i: number) => {\n\t\t\t\t\tlet component = find_recursive(root_node, dependency.outputs[i])!;\n\n\t\t\t\t\treturn represent_value(\n\t\t\t\t\t\toutput_val,\n\t\t\t\t\t\tcomponent.documentation?.type?.response_object ||\n\t\t\t\t\t\t\tcomponent.documentation?.type?.payload,\n\t\t\t\t\t\t\"js\"\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t);\n\t\t} else {\n\t\t\tdependency_failures[index] = new Array(\n\t\t\t\tdependency_failures[index].length\n\t\t\t).fill(true);\n\t\t}\n\t}\n\n\tonMount(() => {\n\t\tdocument.body.style.overflow = \"hidden\";\n\t\tif (\"parentIFrame\" in window) {\n\t\t\twindow.parentIFrame?.scrollTo(0, 0);\n\t\t}\n\t\treturn () => {\n\t\t\tdocument.body.style.overflow = \"auto\";\n\t\t};\n\t});\n</script>\n\n{#if info}\n\t{#if api_count}\n\t\t<div class=\"banner-wrap\">\n\t\t\t<ApiBanner on:close root={space_id || root} {api_count} />\n\t\t</div>\n\t\t<div class=\"docs-wrap\">\n\t\t\t<div class=\"client-doc\">\n\t\t\t\t<p>\n\t\t\t\t\tUse the <code class=\"library\">gradio_client</code>\n\t\t\t\t\tPython library (<a href={py_docs} target=\"_blank\">docs</a>) or the\n\t\t\t\t\t<code class=\"library\">@gradio/client</code>\n\t\t\t\t\tJavascript package (<a href={js_docs} target=\"_blank\">docs</a>) to\n\t\t\t\t\tquery the app via API.\n\t\t\t\t</p>\n\t\t\t</div>\n\t\t\t<div class=\"endpoint\">\n\t\t\t\t<div class=\"snippets\">\n\t\t\t\t\t{#each langs as [language, img]}\n\t\t\t\t\t\t<li\n\t\t\t\t\t\t\tclass=\"snippet\n\t\t\t\t\t\t\t{current_language === language ? 'current-lang' : 'inactive-lang'}\"\n\t\t\t\t\t\t\ton:click={() => (current_language = language)}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<img src={img} alt=\"\" />\n\t\t\t\t\t\t\t{language}\n\t\t\t\t\t\t</li>\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\n\t\t\t\t<p class=\"padded\">\n\t\t\t\t\t1. Install the client if you don't already have it installed.\n\t\t\t\t</p>\n\n\t\t\t\t<InstallSnippet {current_language} />\n\n\t\t\t\t<p class=\"padded\">\n\t\t\t\t\t2. Find the API endpoint below corresponding to your desired function\n\t\t\t\t\tin the app. Copy the code snippet, replacing the placeholder values\n\t\t\t\t\twith your own input data.\n\t\t\t\t\t{#if space_id}If this is a private Space, you may need to pass your\n\t\t\t\t\t\tHugging Face token as well (<a\n\t\t\t\t\t\t\thref={(current_language == \"python\" ? py_docs : js_docs) +\n\t\t\t\t\t\t\t\tspaces_docs_suffix}\n\t\t\t\t\t\t\tclass=\"underline\"\n\t\t\t\t\t\t\ttarget=\"_blank\">read more</a\n\t\t\t\t\t\t>).{/if} Run the code, that's it!\n\t\t\t\t</p>\n\n\t\t\t\t{#each dependencies as dependency, dependency_index}\n\t\t\t\t\t{#if dependency.show_api}\n\t\t\t\t\t\t<div class=\"endpoint-container\">\n\t\t\t\t\t\t\t<CodeSnippet\n\t\t\t\t\t\t\t\tnamed={true}\n\t\t\t\t\t\t\t\tendpoint_parameters={info.named_endpoints[\n\t\t\t\t\t\t\t\t\t\"/\" + dependency.api_name\n\t\t\t\t\t\t\t\t].parameters}\n\t\t\t\t\t\t\t\tjs_parameters={js_info.named_endpoints[\n\t\t\t\t\t\t\t\t\t\"/\" + dependency.api_name\n\t\t\t\t\t\t\t\t].parameters}\n\t\t\t\t\t\t\t\t{dependency}\n\t\t\t\t\t\t\t\t{dependency_index}\n\t\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t\troot={space_id || root}\n\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t<ParametersSnippet\n\t\t\t\t\t\t\t\tendpoint_returns={info.named_endpoints[\n\t\t\t\t\t\t\t\t\t\"/\" + dependency.api_name\n\t\t\t\t\t\t\t\t].parameters}\n\t\t\t\t\t\t\t\tjs_returns={js_info.named_endpoints[\"/\" + dependency.api_name]\n\t\t\t\t\t\t\t\t\t.parameters}\n\t\t\t\t\t\t\t\t{is_running}\n\t\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t<ResponseSnippet\n\t\t\t\t\t\t\t\tendpoint_returns={info.named_endpoints[\n\t\t\t\t\t\t\t\t\t\"/\" + dependency.api_name\n\t\t\t\t\t\t\t\t].returns}\n\t\t\t\t\t\t\t\tjs_returns={js_info.named_endpoints[\"/\" + dependency.api_name]\n\t\t\t\t\t\t\t\t\t.returns}\n\t\t\t\t\t\t\t\t{is_running}\n\t\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t{/each}\n\t\t\t</div>\n\t\t</div>\n\t{:else}\n\t\t<NoApi {root} on:close />\n\t{/if}\n{/if}\n\n<style>\n\t.banner-wrap {\n\t\tposition: relative;\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t\tpadding: var(--size-4) var(--size-6);\n\t\tfont-size: var(--text-md);\n\t}\n\n\t@media (--screen-md) {\n\t\t.banner-wrap {\n\t\t\tfont-size: var(--text-xl);\n\t\t}\n\t}\n\n\t.docs-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-xxl);\n\t}\n\n\t.endpoint {\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--background-fill-primary);\n\t\tpadding: var(--size-6);\n\t\tpadding-top: var(--size-1);\n\t\tfont-size: var(--text-md);\n\t}\n\n\t.client-doc {\n\t\tpadding-top: var(--size-6);\n\t\tpadding-right: var(--size-6);\n\t\tpadding-left: var(--size-6);\n\t\tfont-size: var(--text-md);\n\t}\n\n\t.library {\n\t\tborder: 1px solid var(--border-color-accent);\n\t\tborder-radius: var(--radius-sm);\n\t\tbackground: var(--color-accent-soft);\n\t\tpadding: var(--size-1);\n\t\tcolor: var(--color-accent);\n\t\tfont-size: var(--text-md);\n\t}\n\n\t.snippets {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: var(--size-4);\n\t}\n\n\t.snippets > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\t.snippet {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tborder: 1px solid var(--border-color-primary);\n\n\t\tborder-radius: var(--radius-md);\n\t\tpadding: var(--size-1) var(--size-1-5);\n\t\tcolor: var(--body-text-color-subdued);\n\t\tcolor: var(--body-text-color);\n\t\tline-height: 1;\n\t\tuser-select: none;\n\t\ttext-transform: capitalize;\n\t}\n\n\t.current-lang {\n\t\tborder: 1px solid var(--body-text-color-subdued);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.inactive-lang {\n\t\tcursor: pointer;\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.inactive-lang:hover,\n\t.inactive-lang:focus {\n\t\tbox-shadow: var(--shadow-drop);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.snippet img {\n\t\tmargin-right: var(--size-1-5);\n\t\twidth: var(--size-3);\n\t}\n\n\t.header {\n\t\tmargin-top: var(--size-6);\n\t\tfont-size: var(--text-xl);\n\t}\n\n\t.endpoint-container {\n\t\tmargin-top: var(--size-3);\n\t\tmargin-bottom: var(--size-3);\n\t\tborder: 1px solid var(--body-text-color);\n\t\tborder-radius: var(--radius-xl);\n\t\tpadding: var(--size-3);\n\t\tpadding-top: 0;\n\t}\n\n\ta {\n\t\ttext-decoration: underline;\n\t}\n\n\tp.padded {\n\t\tpadding: 15px 0px;\n\t\tfont-size: var(--text-lg);\n\t}\n</style>\n", "import { format } from \"svelte-i18n\";\nimport { get } from \"svelte/store\";\n\nconst x = get(format);\n\nexport type I18nFormatter = typeof x;\nexport class Gradio<T extends Record<string, any> = Record<string, any>> {\n\t#id: number;\n\ttheme: string;\n\tversion: string;\n\ti18n: typeof x;\n\t#el: HTMLElement;\n\troot: string;\n\tautoscroll: boolean;\n\n\tconstructor(\n\t\tid: number,\n\t\tel: HTMLElement,\n\t\ttheme: string,\n\t\tversion: string,\n\t\troot: string,\n\t\tautoscroll: boolean\n\t) {\n\t\tthis.#id = id;\n\t\tthis.theme = theme;\n\t\tthis.version = version;\n\t\tthis.#el = el;\n\t\tthis.i18n = get(format);\n\t\tthis.root = root;\n\t\tthis.autoscroll = autoscroll;\n\t}\n\n\tdispatch<E extends keyof T>(event_name: E, data?: T[E]): void {\n\t\tconst e = new CustomEvent(\"gradio\", {\n\t\t\tbubbles: true,\n\t\t\tdetail: { data, id: this.#id, event: event_name }\n\t\t});\n\n\t\tthis.#el.dispatchEvent(e);\n\t}\n}\n", "<svelte:options immutable={true} />\n\n<script lang=\"ts\">\n\timport type { Gradio } from \"./gradio_helper\";\n\timport type { ComponentMeta, ThemeMode } from \"./types\";\n\timport type { SvelteComponent, ComponentType } from \"svelte\";\n\t// @ts-ignore\n\timport { bind, binding_callbacks } from \"svelte/internal\";\n\n\texport let root: string;\n\texport let component: ComponentMeta[\"component\"];\n\texport let target: HTMLElement;\n\texport let theme_mode: ThemeMode;\n\texport let instance: ComponentMeta[\"instance\"];\n\texport let value: any;\n\texport let gradio: Gradio;\n\texport let elem_id: string;\n\texport let elem_classes: string[];\n\texport let id: number;\n\n\tconst s = (id: number, p: string, v: any): CustomEvent =>\n\t\tnew CustomEvent(\"prop_change\", { detail: { id, prop: p, value: v } });\n\n\tfunction wrap(\n\t\tcomponent: ComponentType<SvelteComponent>\n\t): ComponentType<SvelteComponent> {\n\t\tconst ProxiedMyClass = new Proxy(component, {\n\t\t\tconstruct(_target, args: Record<string, any>[]) {\n\t\t\t\t//@ts-ignore\n\t\t\t\tconst instance = new _target(...args);\n\t\t\t\tconst props = Object.getOwnPropertyNames(instance).filter(\n\t\t\t\t\t(s) => !s.startsWith(\"$\")\n\t\t\t\t);\n\n\t\t\t\tfunction report(props: string) {\n\t\t\t\t\treturn function (propargs: any) {\n\t\t\t\t\t\tconst ev = s(id, props, propargs);\n\t\t\t\t\t\ttarget.dispatchEvent(ev);\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tprops.forEach((v) => {\n\t\t\t\t\tbinding_callbacks.push(() => bind(instance, v, report(v)));\n\t\t\t\t});\n\n\t\t\t\treturn instance;\n\t\t\t}\n\t\t});\n\n\t\treturn ProxiedMyClass;\n\t}\n\n\tconst _component = wrap(component);\n</script>\n\n<svelte:component\n\tthis={_component}\n\tbind:this={instance}\n\tbind:value\n\ton:prop_change\n\t{elem_id}\n\t{elem_classes}\n\t{target}\n\t{...$$restProps}\n\t{theme_mode}\n\t{root}\n\t{gradio}\n>\n\t<slot />\n</svelte:component>\n", "<script lang=\"ts\">\n\timport { Gradio } from \"./gradio_helper\";\n\timport { onMount, createEventDispatcher, setContext } from \"svelte\";\n\timport type { ComponentMeta, ThemeMode } from \"./types\";\n\timport RenderComponent from \"./RenderComponent.svelte\";\n\n\texport let root: string;\n\n\texport let node: ComponentMeta;\n\texport let parent: string | null = null;\n\texport let target: HTMLElement;\n\texport let theme_mode: ThemeMode;\n\texport let version: string;\n\texport let autoscroll: boolean;\n\n\tconst dispatch = createEventDispatcher<{ mount: number; destroy: number }>();\n\tlet filtered_children: ComponentMeta[] = [];\n\n\tonMount(() => {\n\t\tdispatch(\"mount\", node.id);\n\n\t\tfor (const child of filtered_children) {\n\t\t\tdispatch(\"mount\", child.id);\n\t\t}\n\n\t\treturn () => {\n\t\t\tdispatch(\"destroy\", node.id);\n\n\t\t\tfor (const child of filtered_children) {\n\t\t\t\tdispatch(\"mount\", child.id);\n\t\t\t}\n\t\t};\n\t});\n\n\t$: node.children =\n\t\tnode.children &&\n\t\tnode.children.filter((v) => {\n\t\t\tconst valid_node = node.type !== \"statustracker\";\n\t\t\tif (!valid_node) {\n\t\t\t\tfiltered_children.push(v);\n\t\t\t}\n\t\t\treturn valid_node;\n\t\t});\n\n\tsetContext(\"BLOCK_KEY\", parent);\n\n\tfunction handle_prop_change(e: { detail: Record<string, any> }): void {\n\t\t// for (const k in e.detail) {\n\t\t// \tinstance_map[id].props[k] = e.detail[k];\n\t\t// }\n\t}\n\n\t$: {\n\t\tif (node.type === \"form\") {\n\t\t\tif (node.children?.every((c) => !c.props.visible)) {\n\t\t\t\tnode.props.visible = false;\n\t\t\t} else {\n\t\t\t\tnode.props.visible = true;\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<RenderComponent\n\tid={node.id}\n\tcomponent={node.component}\n\tbind:instance={node.instance}\n\tbind:value={node.props.value}\n\telem_id={(\"elem_id\" in node.props && node.props.elem_id) ||\n\t\t`component-${node.id}`}\n\telem_classes={(\"elem_classes\" in node.props && node.props.elem_classes) || []}\n\ton:prop_change={handle_prop_change}\n\t{target}\n\t{...node.props}\n\t{theme_mode}\n\t{root}\n\tgradio={new Gradio(node.id, target, theme_mode, version, root, autoscroll)}\n>\n\t{#if node.children && node.children.length}\n\t\t{#each node.children as _node (_node.id)}\n\t\t\t<svelte:self\n\t\t\t\tnode={_node}\n\t\t\t\tcomponent={_node.component}\n\t\t\t\t{target}\n\t\t\t\tid={_node.id}\n\t\t\t\t{root}\n\t\t\t\t{theme_mode}\n\t\t\t\ton:destroy\n\t\t\t\ton:mount\n\t\t\t/>\n\t\t{/each}\n\t{/if}\n</RenderComponent>\n", "<script lang=\"ts\">\n\timport { onMount, createEventDispatcher } from \"svelte\";\n\timport Render from \"./Render.svelte\";\n\n\texport let rootNode: any;\n\texport let root: any;\n\texport let target: any;\n\texport let theme_mode: any;\n\texport let version: any;\n\texport let autoscroll: boolean;\n\n\tconst dispatch = createEventDispatcher<{ mount?: never }>();\n\tonMount(() => {\n\t\tdispatch(\"mount\");\n\t});\n</script>\n\n<Render node={rootNode} {root} {target} {theme_mode} {version} {autoscroll} />\n", "export default \"__VITE_ASSET__c2b3a5f0__\"", "import { type Writable, writable, get } from \"svelte/store\";\n\nexport interface LoadingStatus {\n\teta: number | null;\n\tstatus: \"pending\" | \"error\" | \"complete\" | \"generating\";\n\tqueue: boolean;\n\tqueue_position: number | null;\n\tqueue_size?: number;\n\tfn_index: number;\n\tmessage?: string | null;\n\tscroll_to_output?: boolean;\n\tshow_progress?: \"full\" | \"minimal\" | \"hidden\";\n\tprogress?: {\n\t\tprogress: number | null;\n\t\tindex: number | null;\n\t\tlength: number | null;\n\t\tunit: string | null;\n\t\tdesc: string | null;\n\t}[];\n}\n\nexport type LoadingStatusCollection = Record<number, LoadingStatus>;\n\ninterface LoadingStatusStore {\n\tupdate: (status: LoadingStatus) => void;\n\tsubscribe: Writable<LoadingStatusCollection>[\"subscribe\"];\n\tregister: (index: number, inputs: number[], outputs: number[]) => void;\n\tget_status_for_fn: (i: number) => LoadingStatus[\"status\"];\n\tget_inputs_to_update: () => Map<number, string>;\n}\n\nexport function create_loading_status_store(): LoadingStatusStore {\n\tconst store = writable<LoadingStatusCollection>({});\n\n\tconst fn_inputs: number[][] = [];\n\tconst fn_outputs: number[][] = [];\n\tconst pending_outputs = new Map<number, number>();\n\tconst pending_inputs = new Map<number, number>();\n\n\tconst inputs_to_update = new Map<number, string>();\n\tconst fn_status: LoadingStatus[\"status\"][] = [];\n\n\tfunction update({\n\t\tfn_index,\n\t\tstatus,\n\t\tqueue = true,\n\t\tsize,\n\t\tposition = null,\n\t\teta = null,\n\t\tmessage = null,\n\t\tprogress\n\t}: {\n\t\tfn_index: LoadingStatus[\"fn_index\"];\n\t\tstatus: LoadingStatus[\"status\"];\n\t\tqueue?: LoadingStatus[\"queue\"];\n\t\tsize?: LoadingStatus[\"queue_size\"];\n\t\tposition?: LoadingStatus[\"queue_position\"];\n\t\teta?: LoadingStatus[\"eta\"];\n\t\tmessage?: LoadingStatus[\"message\"];\n\t\tprogress?: LoadingStatus[\"progress\"];\n\t}): void {\n\t\tconst outputs = fn_outputs[fn_index];\n\t\tconst inputs = fn_inputs[fn_index];\n\t\tconst last_status = fn_status[fn_index];\n\n\t\tconst outputs_to_update = outputs.map((id) => {\n\t\t\tlet new_status: LoadingStatus[\"status\"];\n\n\t\t\tconst pending_count = pending_outputs.get(id) || 0;\n\n\t\t\t// from (pending -> error) | complete - decrement pending count\n\t\t\tif (last_status === \"pending\" && status !== \"pending\") {\n\t\t\t\tlet new_count = pending_count - 1;\n\n\t\t\t\tpending_outputs.set(id, new_count < 0 ? 0 : new_count);\n\n\t\t\t\tnew_status = new_count > 0 ? \"pending\" : status;\n\n\t\t\t\t// from pending -> pending - do nothing\n\t\t\t} else if (last_status === \"pending\" && status === \"pending\") {\n\t\t\t\tnew_status = \"pending\";\n\n\t\t\t\t// (error | complete) -> pending - - increment pending count\n\t\t\t} else if (last_status !== \"pending\" && status === \"pending\") {\n\t\t\t\tnew_status = \"pending\";\n\t\t\t\tpending_outputs.set(id, pending_count + 1);\n\t\t\t} else {\n\t\t\t\tnew_status = status;\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\tid,\n\t\t\t\tqueue_position: position,\n\t\t\t\tqueue_size: size,\n\t\t\t\teta: eta,\n\t\t\t\tstatus: new_status,\n\t\t\t\tmessage: message,\n\t\t\t\tprogress: progress\n\t\t\t};\n\t\t});\n\n\t\tinputs.forEach((id) => {\n\t\t\tconst pending_count = pending_inputs.get(id) || 0;\n\n\t\t\t// from (pending -> error) | complete - decrement pending count\n\t\t\tif (last_status === \"pending\" && status !== \"pending\") {\n\t\t\t\tlet new_count = pending_count - 1;\n\t\t\t\tpending_inputs.set(id, new_count < 0 ? 0 : new_count);\n\t\t\t\tinputs_to_update.set(id, status);\n\t\t\t} else if (last_status !== \"pending\" && status === \"pending\") {\n\t\t\t\tpending_inputs.set(id, pending_count + 1);\n\t\t\t\tinputs_to_update.set(id, status);\n\t\t\t} else {\n\t\t\t\tinputs_to_update.delete(id);\n\t\t\t}\n\t\t});\n\n\t\tstore.update((outputs: LoadingStatusCollection) => {\n\t\t\toutputs_to_update.forEach(\n\t\t\t\t({\n\t\t\t\t\tid,\n\t\t\t\t\tqueue_position,\n\t\t\t\t\tqueue_size,\n\t\t\t\t\teta,\n\t\t\t\t\tstatus,\n\t\t\t\t\tmessage,\n\t\t\t\t\tprogress\n\t\t\t\t}) => {\n\t\t\t\t\toutputs[id] = {\n\t\t\t\t\t\tqueue: queue,\n\t\t\t\t\t\tqueue_size: queue_size,\n\t\t\t\t\t\tqueue_position: queue_position,\n\t\t\t\t\t\teta: eta,\n\t\t\t\t\t\tmessage: message,\n\t\t\t\t\t\tprogress,\n\t\t\t\t\t\tstatus,\n\t\t\t\t\t\tfn_index\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t);\n\n\t\t\treturn outputs;\n\t\t});\n\t\tfn_status[fn_index] = status;\n\t}\n\n\tfunction register(index: number, inputs: number[], outputs: number[]): void {\n\t\tfn_inputs[index] = inputs;\n\t\tfn_outputs[index] = outputs;\n\t}\n\n\treturn {\n\t\tupdate,\n\t\tregister,\n\t\tsubscribe: store.subscribe,\n\t\tget_status_for_fn(i: number) {\n\t\t\treturn fn_status[i];\n\t\t},\n\t\tget_inputs_to_update() {\n\t\t\treturn inputs_to_update;\n\t\t}\n\t};\n}\n\nexport type LoadingStatusType = ReturnType<typeof create_loading_status_store>;\n", "import { writable, type Writable } from \"svelte/store\";\nimport type {\n\tComponentMeta,\n\tDependency,\n\tLayoutNode,\n\tTargetMap,\n\tLoadingComponent\n} from \"./types\";\nimport { load_component } from \"virtual:component-loader\";\nimport type { client_return } from \"@gradio/client\";\nimport { create_loading_status_store } from \"./stores\";\n\nexport interface UpdateTransaction {\n\tid: number;\n\tvalue: any;\n\tprop: string;\n}\n\nlet pending_updates: UpdateTransaction[][] = [];\n\n/**\n * Create a store with the layout and a map of targets\n * @returns A store with the layout and a map of targets\n */\nexport function create_components(): {\n\tlayout: Writable<ComponentMeta>;\n\ttargets: Writable<TargetMap>;\n\tupdate_value: (updates: UpdateTransaction[]) => void;\n\tget_data: (id: number) => any | Promise<any>;\n\tloading_status: ReturnType<typeof create_loading_status_store>;\n\tscheduled_updates: Writable<boolean>;\n\tcreate_layout: (args: {\n\t\tapp: client_return;\n\t\tcomponents: ComponentMeta[];\n\t\tlayout: LayoutNode;\n\t\tdependencies: Dependency[];\n\t\troot: string;\n\t\toptions: {\n\t\t\tfill_height: boolean;\n\t\t};\n\t}) => void;\n} {\n\tlet _component_map: Map<number, ComponentMeta>;\n\n\tlet target_map: Writable<TargetMap> = writable({});\n\tlet _target_map: TargetMap = {};\n\tlet inputs: Set<number>;\n\tlet outputs: Set<number>;\n\tlet constructor_map: Map<ComponentMeta[\"type\"], LoadingComponent>;\n\tlet instance_map: { [id: number]: ComponentMeta };\n\tlet loading_status: ReturnType<typeof create_loading_status_store> =\n\t\tcreate_loading_status_store();\n\tconst layout_store: Writable<ComponentMeta> = writable();\n\tlet root: string;\n\tlet _components: ComponentMeta[];\n\tlet app: client_return;\n\n\tfunction create_layout({\n\t\tapp: _app,\n\t\tcomponents,\n\t\tlayout,\n\t\tdependencies,\n\t\troot,\n\t\toptions\n\t}: {\n\t\tapp: client_return;\n\t\tcomponents: ComponentMeta[];\n\t\tlayout: LayoutNode;\n\t\tdependencies: Dependency[];\n\t\troot: string;\n\t\toptions: {\n\t\t\tfill_height: boolean;\n\t\t};\n\t}): void {\n\t\tapp = _app;\n\t\t_components = components;\n\t\tinputs = new Set();\n\t\toutputs = new Set();\n\t\tpending_updates = [];\n\t\tconstructor_map = new Map();\n\t\t_component_map = new Map();\n\n\t\tinstance_map = {};\n\n\t\tconst _rootNode: ComponentMeta = {\n\t\t\tid: layout.id,\n\t\t\ttype: \"column\",\n\t\t\tprops: { interactive: false, scale: options.fill_height ? 1 : null },\n\t\t\thas_modes: false,\n\t\t\tinstance: null as unknown as ComponentMeta[\"instance\"],\n\t\t\tcomponent: null as unknown as ComponentMeta[\"component\"],\n\t\t\tcomponent_class_id: \"\"\n\t\t};\n\n\t\tcomponents.push(_rootNode);\n\t\t// loading_status = create_loading_status_store();\n\t\tdependencies.forEach((dep, fn_index) => {\n\t\t\tloading_status.register(fn_index, dep.inputs, dep.outputs);\n\t\t\tdep.frontend_fn = process_frontend_fn(\n\t\t\t\tdep.js,\n\t\t\t\t!!dep.backend_fn,\n\t\t\t\tdep.inputs.length,\n\t\t\t\tdep.outputs.length\n\t\t\t);\n\t\t\tcreate_target_meta(dep.targets, fn_index, _target_map);\n\t\t\tget_inputs_outputs(dep, inputs, outputs);\n\t\t});\n\n\t\ttarget_map.set(_target_map);\n\n\t\tconstructor_map = preload_all_components(components, root);\n\n\t\tinstance_map = components.reduce(\n\t\t\t(acc, c) => {\n\t\t\t\tacc[c.id] = c;\n\t\t\t\treturn acc;\n\t\t\t},\n\t\t\t{} as { [id: number]: ComponentMeta }\n\t\t);\n\n\t\twalk_layout(layout).then(() => {\n\t\t\tlayout_store.set(_rootNode);\n\t\t});\n\t}\n\n\tasync function walk_layout(node: LayoutNode): Promise<ComponentMeta> {\n\t\tconst instance = instance_map[node.id];\n\n\t\tinstance.component = (await constructor_map.get(\n\t\t\tinstance.component_class_id\n\t\t))!?.default;\n\n\t\tif (instance.type === \"dataset\") {\n\t\t\tinstance.props.component_map = get_component(\n\t\t\t\tinstance.type,\n\t\t\t\tinstance.component_class_id,\n\t\t\t\troot,\n\t\t\t\t_components,\n\t\t\t\tinstance.props.components\n\t\t\t).example_components;\n\t\t}\n\n\t\tif (_target_map[instance.id]) {\n\t\t\tinstance.props.attached_events = Object.keys(_target_map[instance.id]);\n\t\t}\n\n\t\tinstance.props.interactive = determine_interactivity(\n\t\t\tinstance.id,\n\t\t\tinstance.props.interactive,\n\t\t\tinstance.props.value,\n\t\t\tinputs,\n\t\t\toutputs\n\t\t);\n\n\t\tinstance.props.server = process_server_fn(\n\t\t\tinstance.id,\n\t\t\tinstance.props.server_fns,\n\t\t\tapp\n\t\t);\n\n\t\t_component_map.set(instance.id, instance);\n\n\t\tif (node.children) {\n\t\t\tinstance.children = await Promise.all(\n\t\t\t\tnode.children.map((v) => walk_layout(v))\n\t\t\t);\n\t\t}\n\n\t\treturn instance;\n\t}\n\n\tlet update_scheduled = false;\n\tlet update_scheduled_store = writable(false);\n\n\tfunction flush(): void {\n\t\tlayout_store.update((layout) => {\n\t\t\tfor (let i = 0; i < pending_updates.length; i++) {\n\t\t\t\tfor (let j = 0; j < pending_updates[i].length; j++) {\n\t\t\t\t\tconst update = pending_updates[i][j];\n\t\t\t\t\tconst instance = instance_map[update.id];\n\t\t\t\t\tif (!instance) continue;\n\t\t\t\t\tlet new_value;\n\t\t\t\t\tif (Array.isArray(update.value)) new_value = [...update.value];\n\t\t\t\t\telse if (update.value === null) new_value = null;\n\t\t\t\t\telse if (typeof update.value === \"object\")\n\t\t\t\t\t\tnew_value = { ...update.value };\n\t\t\t\t\telse new_value = update.value;\n\t\t\t\t\tinstance.props[update.prop] = new_value;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn layout;\n\t\t});\n\n\t\tpending_updates = [];\n\t\tupdate_scheduled = false;\n\t\tupdate_scheduled_store.set(false);\n\t}\n\n\tfunction update_value(updates: UpdateTransaction[] | undefined): void {\n\t\tif (!updates) return;\n\t\tpending_updates.push(updates);\n\n\t\tif (!update_scheduled) {\n\t\t\tupdate_scheduled = true;\n\t\t\tupdate_scheduled_store.set(true);\n\t\t\trequestAnimationFrame(flush);\n\t\t}\n\t}\n\n\tfunction get_data(id: number): any | Promise<any> {\n\t\tconst comp = _component_map.get(id);\n\t\tif (!comp) {\n\t\t\treturn null;\n\t\t}\n\t\tif (comp.instance.get_value) {\n\t\t\treturn comp.instance.get_value() as Promise<any>;\n\t\t}\n\t\treturn comp.props.value;\n\t}\n\n\treturn {\n\t\tlayout: layout_store,\n\t\ttargets: target_map,\n\t\tupdate_value,\n\t\tget_data,\n\t\tloading_status,\n\t\tscheduled_updates: update_scheduled_store,\n\t\tcreate_layout: (...args) =>\n\t\t\trequestAnimationFrame(() => create_layout(...args))\n\t};\n}\n\n/** An async version of 'new Function' */\nexport const AsyncFunction: new (\n\t...args: string[]\n) => (...args: any[]) => Promise<any> = Object.getPrototypeOf(\n\tasync function () {}\n).constructor;\n\n/**\n * Takes a string of source code and returns a function that can be called with arguments\n * @param source the source code\n * @param backend_fn if there is also a backend function\n * @param input_length the number of inputs\n * @param output_length the number of outputs\n * @returns The function, or null if the source code is invalid or missing\n */\nexport function process_frontend_fn(\n\tsource: string | null | undefined | false,\n\tbackend_fn: boolean,\n\tinput_length: number,\n\toutput_length: number\n): ((...args: unknown[]) => Promise<unknown[]>) | null {\n\tif (!source) return null;\n\n\tconst wrap = backend_fn ? input_length === 1 : output_length === 1;\n\ttry {\n\t\treturn new AsyncFunction(\n\t\t\t\"__fn_args\",\n\t\t\t`  let result = await (${source})(...__fn_args);\n  return (${wrap} && !Array.isArray(result)) ? [result] : result;`\n\t\t);\n\t} catch (e) {\n\t\tconsole.error(\"Could not parse custom js method.\");\n\t\tconsole.error(e);\n\t\treturn null;\n\t}\n}\n\n/**\n * `Dependency.targets` is an array of `[id, trigger]` pairs with the indices as the `fn_index`.\n * This function take a single list of `Dependency.targets` and add those to the target_map.\n * @param targets the targets array\n * @param fn_index the function index\n * @param target_map the target map\n * @returns the tagert map\n */\nexport function create_target_meta(\n\ttargets: Dependency[\"targets\"],\n\tfn_index: number,\n\ttarget_map: TargetMap\n): TargetMap {\n\ttargets.forEach(([id, trigger]) => {\n\t\tif (!target_map[id]) {\n\t\t\ttarget_map[id] = {};\n\t\t}\n\t\tif (\n\t\t\ttarget_map[id]?.[trigger] &&\n\t\t\t!target_map[id]?.[trigger].includes(fn_index)\n\t\t) {\n\t\t\ttarget_map[id][trigger].push(fn_index);\n\t\t} else {\n\t\t\ttarget_map[id][trigger] = [fn_index];\n\t\t}\n\t});\n\n\treturn target_map;\n}\n\n/**\n * Get all component ids that are an input or output of a dependency\n * @param dep the dependency\n * @param inputs the set of inputs\n * @param outputs the set of outputs\n * @returns a tuple of the inputs and outputs\n */\nexport function get_inputs_outputs(\n\tdep: Dependency,\n\tinputs: Set<number>,\n\toutputs: Set<number>\n): [Set<number>, Set<number>] {\n\tdep.inputs.forEach((input) => inputs.add(input));\n\tdep.outputs.forEach((output) => outputs.add(output));\n\treturn [inputs, outputs];\n}\n\n/**\n * Check if a value is not a default value\n * @param value the value to check\n * @returns default value boolean\n */\nfunction has_no_default_value(value: any): boolean {\n\treturn (\n\t\t(Array.isArray(value) && value.length === 0) ||\n\t\tvalue === \"\" ||\n\t\tvalue === 0 ||\n\t\t!value\n\t);\n}\n\n/**\n * Determines if a component is interactive\n * @param id component id\n * @param interactive_prop value of the interactive prop\n * @param value the main value of the component\n * @param inputs set of ids that are inputs to a dependency\n * @param outputs set of ids that are outputs to a dependency\n * @returns if the component is interactive\n */\nexport function determine_interactivity(\n\tid: number,\n\tinteractive_prop: boolean | undefined,\n\tvalue: any,\n\tinputs: Set<number>,\n\toutputs: Set<number>\n): boolean {\n\tif (interactive_prop === false) {\n\t\treturn false;\n\t} else if (interactive_prop === true) {\n\t\treturn true;\n\t} else if (\n\t\tinputs.has(id) ||\n\t\t(!outputs.has(id) && has_no_default_value(value))\n\t) {\n\t\treturn true;\n\t}\n\n\treturn false;\n}\n\ntype ServerFunctions = Record<string, (...args: any[]) => Promise<any>>;\n\n/**\n * Process the server function names and return a dictionary of functions\n * @param id the component id\n * @param server_fns the server function names\n * @param app the client instance\n * @returns the actual server functions\n */\nexport function process_server_fn(\n\tid: number,\n\tserver_fns: string[] | undefined,\n\tapp: client_return\n): ServerFunctions {\n\tif (!server_fns) {\n\t\treturn {};\n\t}\n\treturn server_fns.reduce((acc, fn: string) => {\n\t\tacc[fn] = async (...args: any[]) => {\n\t\t\tif (args.length === 1) {\n\t\t\t\targs = args[0];\n\t\t\t}\n\t\t\tconst result = await app.component_server(id, fn, args);\n\t\t\treturn result;\n\t\t};\n\t\treturn acc;\n\t}, {} as ServerFunctions);\n}\n\n/**\n * Get a component from the backend\n * @param type the type of the component\n * @param class_id the class id of the component\n * @param root the root url of the app\n * @param components the list of component metadata\n * @param example_components the list of example components\n * @returns the component and its name\n */\nexport function get_component(\n\ttype: string,\n\tclass_id: string,\n\troot: string,\n\tcomponents: ComponentMeta[],\n\texample_components?: string[]\n): {\n\tcomponent: LoadingComponent;\n\tname: ComponentMeta[\"type\"];\n\texample_components?: Map<ComponentMeta[\"type\"], LoadingComponent>;\n} {\n\tlet example_component_map: Map<ComponentMeta[\"type\"], LoadingComponent> =\n\t\tnew Map();\n\tif (type === \"dataset\" && example_components) {\n\t\t(example_components as string[]).forEach((name: string) => {\n\t\t\tif (example_component_map.has(name)) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tlet _c;\n\n\t\t\tconst matching_component = components.find((c) => c.type === name);\n\t\t\tif (matching_component) {\n\t\t\t\t_c = load_component({\n\t\t\t\t\tapi_url: root,\n\t\t\t\t\tname,\n\t\t\t\t\tid: matching_component.component_class_id,\n\t\t\t\t\tvariant: \"example\"\n\t\t\t\t});\n\t\t\t\texample_component_map.set(name, _c.component);\n\t\t\t}\n\t\t});\n\t}\n\n\tconst _c = load_component({\n\t\tapi_url: root,\n\t\tname: type,\n\t\tid: class_id,\n\t\tvariant: \"component\"\n\t});\n\n\treturn {\n\t\tcomponent: _c.component,\n\t\tname: _c.name,\n\t\texample_components:\n\t\t\texample_component_map.size > 0 ? example_component_map : undefined\n\t};\n}\n\n/**\n * Preload all components\n * @param components A list of component metadata\n * @param root The root url of the app\n * @returns A map of component ids to their constructors\n */\nexport function preload_all_components(\n\tcomponents: ComponentMeta[],\n\troot: string\n): Map<ComponentMeta[\"type\"], LoadingComponent> {\n\tlet constructor_map: Map<ComponentMeta[\"type\"], LoadingComponent> = new Map();\n\n\tcomponents.forEach((c) => {\n\t\tconst { component, example_components } = get_component(\n\t\t\tc.type,\n\t\t\tc.component_class_id,\n\t\t\troot,\n\t\t\tcomponents\n\t\t);\n\n\t\tconstructor_map.set(c.component_class_id, component);\n\n\t\tif (example_components) {\n\t\t\tfor (const [name, example_component] of example_components) {\n\t\t\t\tconstructor_map.set(name, example_component);\n\t\t\t}\n\t\t}\n\t});\n\n\treturn constructor_map;\n}\n", "<script lang=\"ts\">\n\timport { tick } from \"svelte\";\n\timport { _ } from \"svelte-i18n\";\n\timport { client } from \"@gradio/client\";\n\n\timport type { LoadingStatusCollection } from \"./stores\";\n\n\timport type { ComponentMeta, Dependency, LayoutNode } from \"./types\";\n\timport type { UpdateTransaction } from \"./init\";\n\timport { setupi18n } from \"./i18n\";\n\timport { ApiDocs } from \"./api_docs/\";\n\timport type { ThemeMode, Payload } from \"./types\";\n\timport { Toast } from \"@gradio/statustracker\";\n\timport type { ToastMessage } from \"@gradio/statustracker\";\n\timport type { ShareData } from \"@gradio/utils\";\n\timport MountComponents from \"./MountComponents.svelte\";\n\n\timport logo from \"./images/logo.svg\";\n\timport api_logo from \"./api_docs/img/api-logo.svg\";\n\timport { create_components, AsyncFunction } from \"./init\";\n\n\tsetupi18n();\n\n\texport let root: string;\n\texport let components: ComponentMeta[];\n\texport let layout: LayoutNode;\n\texport let dependencies: Dependency[];\n\texport let title = \"Gradio\";\n\texport let analytics_enabled = false;\n\texport let target: HTMLElement;\n\texport let autoscroll: boolean;\n\texport let show_api = true;\n\texport let show_footer = true;\n\texport let control_page_title = false;\n\texport let app_mode: boolean;\n\texport let theme_mode: ThemeMode;\n\texport let app: Awaited<ReturnType<typeof client>>;\n\texport let space_id: string | null;\n\texport let version: string;\n\texport let js: string | null;\n\texport let fill_height = false;\n\texport let ready: boolean;\n\n\tconst {\n\t\tlayout: _layout,\n\t\ttargets,\n\t\tupdate_value,\n\t\tget_data,\n\t\tloading_status,\n\t\tscheduled_updates,\n\t\tcreate_layout\n\t} = create_components();\n\n\t$: create_layout({\n\t\tcomponents,\n\t\tlayout,\n\t\tdependencies,\n\t\troot,\n\t\tapp,\n\t\toptions: {\n\t\t\tfill_height\n\t\t}\n\t});\n\n\t$: {\n\t\tready = !!$_layout;\n\t}\n\n\tlet params = new URLSearchParams(window.location.search);\n\tlet api_docs_visible = params.get(\"view\") === \"api\" && show_api;\n\tfunction set_api_docs_visible(visible: boolean): void {\n\t\tapi_docs_visible = visible;\n\t\tlet params = new URLSearchParams(window.location.search);\n\t\tif (visible) {\n\t\t\tparams.set(\"view\", \"api\");\n\t\t} else {\n\t\t\tparams.delete(\"view\");\n\t\t}\n\t\thistory.replaceState(null, \"\", \"?\" + params.toString());\n\t}\n\n\texport let render_complete = false;\n\tasync function handle_update(data: any, fn_index: number): Promise<void> {\n\t\tconst outputs = dependencies[fn_index].outputs;\n\n\t\tconst meta_updates = data?.map((value: any, i: number) => {\n\t\t\treturn {\n\t\t\t\tid: outputs[i],\n\t\t\t\tprop: \"value_is_output\",\n\t\t\t\tvalue: true\n\t\t\t};\n\t\t});\n\n\t\tupdate_value(meta_updates);\n\n\t\tawait tick();\n\n\t\tconst updates: UpdateTransaction[] = [];\n\n\t\tdata?.forEach((value: any, i: number) => {\n\t\t\tif (\n\t\t\t\ttypeof value === \"object\" &&\n\t\t\t\tvalue !== null &&\n\t\t\t\tvalue.__type__ === \"update\"\n\t\t\t) {\n\t\t\t\tfor (const [update_key, update_value] of Object.entries(value)) {\n\t\t\t\t\tif (update_key === \"__type__\") {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tupdates.push({\n\t\t\t\t\t\t\tid: outputs[i],\n\t\t\t\t\t\t\tprop: update_key,\n\t\t\t\t\t\t\tvalue: update_value\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tupdates.push({\n\t\t\t\t\tid: outputs[i],\n\t\t\t\t\tprop: \"value\",\n\t\t\t\t\tvalue\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t\tupdate_value(updates);\n\n\t\tawait tick();\n\t}\n\n\tlet submit_map: Map<number, ReturnType<typeof app.submit>> = new Map();\n\n\tlet handled_dependencies: number[][] = [];\n\n\tlet messages: (ToastMessage & { fn_index: number })[] = [];\n\tfunction new_message(\n\t\tmessage: string,\n\t\tfn_index: number,\n\t\ttype: ToastMessage[\"type\"]\n\t): ToastMessage & { fn_index: number } {\n\t\treturn {\n\t\t\tmessage,\n\t\t\tfn_index,\n\t\t\ttype,\n\t\t\tid: ++_error_id\n\t\t};\n\t}\n\n\tlet _error_id = -1;\n\n\tlet user_left_page = false;\n\tdocument.addEventListener(\"visibilitychange\", function () {\n\t\tif (document.visibilityState === \"hidden\") {\n\t\t\tuser_left_page = true;\n\t\t}\n\t});\n\n\tconst MESSAGE_QUOTE_RE = /^'([^]+)'$/;\n\n\tconst DUPLICATE_MESSAGE = $_(\"blocks.long_requests_queue\");\n\tconst MOBILE_QUEUE_WARNING = $_(\"blocks.connection_can_break\");\n\tconst MOBILE_RECONNECT_MESSAGE = $_(\"blocks.lost_connection\");\n\tconst SHOW_DUPLICATE_MESSAGE_ON_ETA = 15;\n\tconst SHOW_MOBILE_QUEUE_WARNING_ON_ETA = 10;\n\tconst is_mobile_device =\n\t\t/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n\t\t\tnavigator.userAgent\n\t\t);\n\tlet showed_duplicate_message = false;\n\tlet showed_mobile_warning = false;\n\n\t// as state updates are not synchronous, we need to ensure updates are flushed before triggering any requests\n\tfunction wait_then_trigger_api_call(\n\t\tdep_index: number,\n\t\ttrigger_id: number | null = null,\n\t\tevent_data: unknown = null\n\t): void {\n\t\tlet _unsub = (): void => {};\n\t\tfunction unsub(): void {\n\t\t\t_unsub();\n\t\t}\n\t\tif ($scheduled_updates) {\n\t\t\t_unsub = scheduled_updates.subscribe((updating) => {\n\t\t\t\tif (!updating) {\n\t\t\t\t\ttrigger_api_call(dep_index, trigger_id, event_data);\n\t\t\t\t\tunsub();\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\ttrigger_api_call(dep_index, trigger_id, event_data);\n\t\t}\n\t}\n\n\tasync function trigger_api_call(\n\t\tdep_index: number,\n\t\ttrigger_id: number | null = null,\n\t\tevent_data: unknown = null\n\t): Promise<void> {\n\t\tlet dep = dependencies[dep_index];\n\n\t\tconst current_status = loading_status.get_status_for_fn(dep_index);\n\t\tmessages = messages.filter(({ fn_index }) => fn_index !== dep_index);\n\t\tif (dep.cancels) {\n\t\t\tawait Promise.all(\n\t\t\t\tdep.cancels.map(async (fn_index) => {\n\t\t\t\t\tconst submission = submit_map.get(fn_index);\n\t\t\t\t\tsubmission?.cancel();\n\t\t\t\t\treturn submission;\n\t\t\t\t})\n\t\t\t);\n\t\t}\n\t\tif (current_status === \"pending\" || current_status === \"generating\") {\n\t\t\tdep.pending_request = true;\n\t\t}\n\n\t\tlet payload: Payload = {\n\t\t\tfn_index: dep_index,\n\t\t\tdata: await Promise.all(dep.inputs.map((id) => get_data(id))),\n\t\t\tevent_data: dep.collects_event_data ? event_data : null,\n\t\t\ttrigger_id: trigger_id\n\t\t};\n\n\t\tif (dep.frontend_fn) {\n\t\t\tdep\n\t\t\t\t.frontend_fn(\n\t\t\t\t\tpayload.data.concat(\n\t\t\t\t\t\tawait Promise.all(dep.inputs.map((id) => get_data(id)))\n\t\t\t\t\t)\n\t\t\t\t)\n\t\t\t\t.then((v: unknown[]) => {\n\t\t\t\t\tif (dep.backend_fn) {\n\t\t\t\t\t\tpayload.data = v;\n\t\t\t\t\t\tmake_prediction(payload);\n\t\t\t\t\t} else {\n\t\t\t\t\t\thandle_update(v, dep_index);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t} else {\n\t\t\tif (dep.backend_fn) {\n\t\t\t\tif (dep.trigger_mode === \"once\") {\n\t\t\t\t\tif (!dep.pending_request) make_prediction(payload);\n\t\t\t\t} else if (dep.trigger_mode === \"multiple\") {\n\t\t\t\t\tmake_prediction(payload);\n\t\t\t\t} else if (dep.trigger_mode === \"always_last\") {\n\t\t\t\t\tif (!dep.pending_request) {\n\t\t\t\t\t\tmake_prediction(payload);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tdep.final_event = payload;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tasync function make_prediction(payload: Payload): Promise<void> {\n\t\t\tconst submission = app\n\t\t\t\t.submit(\n\t\t\t\t\tpayload.fn_index,\n\t\t\t\t\tpayload.data as unknown[],\n\t\t\t\t\tpayload.event_data,\n\t\t\t\t\tpayload.trigger_id\n\t\t\t\t)\n\t\t\t\t.on(\"data\", ({ data, fn_index }) => {\n\t\t\t\t\tif (dep.pending_request && dep.final_event) {\n\t\t\t\t\t\tdep.pending_request = false;\n\t\t\t\t\t\tmake_prediction(dep.final_event);\n\t\t\t\t\t}\n\t\t\t\t\tdep.pending_request = false;\n\t\t\t\t\thandle_update(data, fn_index);\n\t\t\t\t\tset_status($loading_status);\n\t\t\t\t})\n\t\t\t\t.on(\"status\", ({ fn_index, ...status }) => {\n\t\t\t\t\t//@ts-ignore\n\t\t\t\t\tloading_status.update({\n\t\t\t\t\t\t...status,\n\t\t\t\t\t\tstatus: status.stage,\n\t\t\t\t\t\tprogress: status.progress_data,\n\t\t\t\t\t\tfn_index\n\t\t\t\t\t});\n\t\t\t\t\tset_status($loading_status);\n\t\t\t\t\tif (\n\t\t\t\t\t\t!showed_duplicate_message &&\n\t\t\t\t\t\tspace_id !== null &&\n\t\t\t\t\t\tstatus.position !== undefined &&\n\t\t\t\t\t\tstatus.position >= 2 &&\n\t\t\t\t\t\tstatus.eta !== undefined &&\n\t\t\t\t\t\tstatus.eta > SHOW_DUPLICATE_MESSAGE_ON_ETA\n\t\t\t\t\t) {\n\t\t\t\t\t\tshowed_duplicate_message = true;\n\t\t\t\t\t\tmessages = [\n\t\t\t\t\t\t\tnew_message(DUPLICATE_MESSAGE, fn_index, \"warning\"),\n\t\t\t\t\t\t\t...messages\n\t\t\t\t\t\t];\n\t\t\t\t\t}\n\t\t\t\t\tif (\n\t\t\t\t\t\t!showed_mobile_warning &&\n\t\t\t\t\t\tis_mobile_device &&\n\t\t\t\t\t\tstatus.eta !== undefined &&\n\t\t\t\t\t\tstatus.eta > SHOW_MOBILE_QUEUE_WARNING_ON_ETA\n\t\t\t\t\t) {\n\t\t\t\t\t\tshowed_mobile_warning = true;\n\t\t\t\t\t\tmessages = [\n\t\t\t\t\t\t\tnew_message(MOBILE_QUEUE_WARNING, fn_index, \"warning\"),\n\t\t\t\t\t\t\t...messages\n\t\t\t\t\t\t];\n\t\t\t\t\t}\n\n\t\t\t\t\tif (status.stage === \"complete\") {\n\t\t\t\t\t\tdependencies.map(async (dep, i) => {\n\t\t\t\t\t\t\tif (dep.trigger_after === fn_index) {\n\t\t\t\t\t\t\t\twait_then_trigger_api_call(i, payload.trigger_id);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tsubmission.destroy();\n\t\t\t\t\t}\n\t\t\t\t\tif (status.broken && is_mobile_device && user_left_page) {\n\t\t\t\t\t\twindow.setTimeout(() => {\n\t\t\t\t\t\t\tmessages = [\n\t\t\t\t\t\t\t\tnew_message(MOBILE_RECONNECT_MESSAGE, fn_index, \"error\"),\n\t\t\t\t\t\t\t\t...messages\n\t\t\t\t\t\t\t];\n\t\t\t\t\t\t}, 0);\n\t\t\t\t\t\twait_then_trigger_api_call(\n\t\t\t\t\t\t\tdep_index,\n\t\t\t\t\t\t\tpayload.trigger_id,\n\t\t\t\t\t\t\tevent_data\n\t\t\t\t\t\t);\n\t\t\t\t\t\tuser_left_page = false;\n\t\t\t\t\t} else if (status.stage === \"error\") {\n\t\t\t\t\t\tif (status.message) {\n\t\t\t\t\t\t\tconst _message = status.message.replace(\n\t\t\t\t\t\t\t\tMESSAGE_QUOTE_RE,\n\t\t\t\t\t\t\t\t(_, b) => b\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\tmessages = [\n\t\t\t\t\t\t\t\tnew_message(_message, fn_index, \"error\"),\n\t\t\t\t\t\t\t\t...messages\n\t\t\t\t\t\t\t];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tdependencies.map(async (dep, i) => {\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\tdep.trigger_after === fn_index &&\n\t\t\t\t\t\t\t\t!dep.trigger_only_on_success\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\twait_then_trigger_api_call(i, payload.trigger_id);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tsubmission.destroy();\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t.on(\"log\", ({ log, fn_index, level }) => {\n\t\t\t\t\tmessages = [new_message(log, fn_index, level), ...messages];\n\t\t\t\t});\n\n\t\t\tsubmit_map.set(dep_index, submission);\n\t\t}\n\t}\n\n\tfunction trigger_share(title: string | undefined, description: string): void {\n\t\tif (space_id === null) {\n\t\t\treturn;\n\t\t}\n\t\tconst discussion_url = new URL(\n\t\t\t`https://huggingface.co/spaces/${space_id}/discussions/new`\n\t\t);\n\t\tif (title !== undefined && title.length > 0) {\n\t\t\tdiscussion_url.searchParams.set(\"title\", title);\n\t\t}\n\t\tdiscussion_url.searchParams.set(\"description\", description);\n\t\twindow.open(discussion_url.toString(), \"_blank\");\n\t}\n\n\tfunction handle_error_close(e: Event & { detail: number }): void {\n\t\tconst _id = e.detail;\n\t\tmessages = messages.filter((m) => m.id !== _id);\n\t}\n\n\tconst is_external_url = (link: string | null): boolean =>\n\t\t!!(link && new URL(link, location.href).origin !== location.origin);\n\n\tasync function handle_mount(): Promise<void> {\n\t\tif (js) {\n\t\t\tlet blocks_frontend_fn = new AsyncFunction(\n\t\t\t\t`let result = await (${js})();\n\t\t\t\t\treturn (!Array.isArray(result)) ? [result] : result;`\n\t\t\t);\n\t\t\tawait blocks_frontend_fn();\n\t\t}\n\n\t\tawait tick();\n\n\t\tvar a = target.getElementsByTagName(\"a\");\n\n\t\tfor (var i = 0; i < a.length; i++) {\n\t\t\tconst _target = a[i].getAttribute(\"target\");\n\t\t\tconst _link = a[i].getAttribute(\"href\");\n\n\t\t\t// only target anchor tags with external links\n\t\t\tif (is_external_url(_link) && _target !== \"_blank\")\n\t\t\t\ta[i].setAttribute(\"target\", \"_blank\");\n\t\t}\n\n\t\t// handle load triggers\n\t\tdependencies.forEach((dep, i) => {\n\t\t\tif (dep.targets[0][1] === \"load\") {\n\t\t\t\twait_then_trigger_api_call(i);\n\t\t\t}\n\t\t});\n\n\t\tif (render_complete) return;\n\n\t\ttarget.addEventListener(\"prop_change\", (e: Event) => {\n\t\t\tif (!isCustomEvent(e)) throw new Error(\"not a custom event\");\n\t\t\tconst { id, prop, value } = e.detail;\n\t\t\tupdate_value([{ id, prop, value }]);\n\t\t});\n\t\ttarget.addEventListener(\"gradio\", (e: Event) => {\n\t\t\tif (!isCustomEvent(e)) throw new Error(\"not a custom event\");\n\n\t\t\tconst { id, event, data } = e.detail;\n\n\t\t\tif (event === \"share\") {\n\t\t\t\tconst { title, description } = data as ShareData;\n\t\t\t\ttrigger_share(title, description);\n\t\t\t} else if (event === \"error\" || event === \"warning\") {\n\t\t\t\tmessages = [new_message(data, -1, event), ...messages];\n\t\t\t} else {\n\t\t\t\tconst deps = $targets[id]?.[event];\n\n\t\t\t\tdeps?.forEach((dep_id) => {\n\t\t\t\t\trequestAnimationFrame(() => {\n\t\t\t\t\t\twait_then_trigger_api_call(dep_id, id, data);\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\n\t\trender_complete = true;\n\t}\n\n\tfunction handle_destroy(id: number): void {\n\t\thandled_dependencies = handled_dependencies.map((dep) => {\n\t\t\treturn dep.filter((_id) => _id !== id);\n\t\t});\n\t}\n\n\t$: set_status($loading_status);\n\n\tfunction set_status(statuses: LoadingStatusCollection): void {\n\t\tconst updates = Object.entries(statuses).map(([id, loading_status]) => {\n\t\t\tlet dependency = dependencies[loading_status.fn_index];\n\t\t\tloading_status.scroll_to_output = dependency.scroll_to_output;\n\t\t\tloading_status.show_progress = dependency.show_progress;\n\t\t\treturn {\n\t\t\t\tid: parseInt(id),\n\t\t\t\tprop: \"loading_status\",\n\t\t\t\tvalue: loading_status\n\t\t\t};\n\t\t});\n\n\t\tconst inputs_to_update = loading_status.get_inputs_to_update();\n\t\tconst additional_updates = Array.from(inputs_to_update).map(\n\t\t\t([id, pending_status]) => {\n\t\t\t\treturn {\n\t\t\t\t\tid,\n\t\t\t\t\tprop: \"pending\",\n\t\t\t\t\tvalue: pending_status === \"pending\"\n\t\t\t\t};\n\t\t\t}\n\t\t);\n\n\t\tupdate_value([...updates, ...additional_updates]);\n\t}\n\n\tfunction isCustomEvent(event: Event): event is CustomEvent {\n\t\treturn \"detail\" in event;\n\t}\n</script>\n\n<svelte:head>\n\t{#if control_page_title}\n\t\t<title>{title}</title>\n\t{/if}\n\t{#if analytics_enabled}\n\t\t<script\n\t\t\tasync\n\t\t\tdefer\n\t\t\tsrc=\"https://www.googletagmanager.com/gtag/js?id=UA-156449732-1\"\n\t\t></script>\n\t\t<script>\n\t\t\twindow.dataLayer = window.dataLayer || [];\n\t\t\tfunction gtag() {\n\t\t\t\tdataLayer.push(arguments);\n\t\t\t}\n\t\t\tgtag(\"js\", new Date());\n\t\t\tgtag(\"config\", \"UA-156449732-1\", {\n\t\t\t\tcookie_flags: \"samesite=none;secure\"\n\t\t\t});\n\t\t</script>\n\t{/if}\n</svelte:head>\n\n<div class=\"wrap\" style:min-height={app_mode ? \"100%\" : \"auto\"}>\n\t<div class=\"contain\" style:flex-grow={app_mode ? \"1\" : \"auto\"}>\n\t\t{#if $_layout}\n\t\t\t<MountComponents\n\t\t\t\trootNode={$_layout}\n\t\t\t\t{root}\n\t\t\t\t{target}\n\t\t\t\t{theme_mode}\n\t\t\t\ton:mount={handle_mount}\n\t\t\t\ton:destroy={({ detail }) => handle_destroy(detail)}\n\t\t\t\t{version}\n\t\t\t\t{autoscroll}\n\t\t\t/>\n\t\t{/if}\n\t</div>\n\n\t{#if show_footer}\n\t\t<footer>\n\t\t\t{#if show_api}\n\t\t\t\t<button\n\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\tset_api_docs_visible(!api_docs_visible);\n\t\t\t\t\t}}\n\t\t\t\t\tclass=\"show-api\"\n\t\t\t\t>\n\t\t\t\t\t{$_(\"errors.use_via_api\")}\n\t\t\t\t\t<img src={api_logo} alt={$_(\"common.logo\")} />\n\t\t\t\t</button>\n\t\t\t\t<div>·</div>\n\t\t\t{/if}\n\t\t\t<a\n\t\t\t\thref=\"https://gradio.app\"\n\t\t\t\tclass=\"built-with\"\n\t\t\t\ttarget=\"_blank\"\n\t\t\t\trel=\"noreferrer\"\n\t\t\t>\n\t\t\t\t{$_(\"common.built_with_gradio\")}\n\t\t\t\t<img src={logo} alt={$_(\"common.logo\")} />\n\t\t\t</a>\n\t\t</footer>\n\t{/if}\n</div>\n\n{#if api_docs_visible && $_layout}\n\t<div class=\"api-docs\">\n\t\t<!-- TODO: fix -->\n\t\t<!-- svelte-ignore a11y-click-events-have-key-events-->\n\t\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t\t<div\n\t\t\tclass=\"backdrop\"\n\t\t\ton:click={() => {\n\t\t\t\tset_api_docs_visible(false);\n\t\t\t}}\n\t\t/>\n\t\t<div class=\"api-docs-wrap\">\n\t\t\t<ApiDocs\n\t\t\t\troot_node={$_layout}\n\t\t\t\ton:close={() => {\n\t\t\t\t\tset_api_docs_visible(false);\n\t\t\t\t}}\n\t\t\t\t{dependencies}\n\t\t\t\t{root}\n\t\t\t\t{app}\n\t\t\t\t{space_id}\n\t\t\t/>\n\t\t</div>\n\t</div>\n{/if}\n\n{#if messages}\n\t<Toast {messages} on:close={handle_error_close} />\n{/if}\n\n<style>\n\t.wrap {\n\t\tdisplay: flex;\n\t\tflex-grow: 1;\n\t\tflex-direction: column;\n\t\twidth: var(--size-full);\n\t\tfont-weight: var(--body-text-weight);\n\t\tfont-size: var(--body-text-size);\n\t}\n\n\t.contain {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\tfooter {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tmargin-top: var(--size-4);\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\tfooter > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\t.show-api {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\t.show-api:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.show-api img {\n\t\tmargin-right: var(--size-1);\n\t\tmargin-left: var(--size-2);\n\t\twidth: var(--size-3);\n\t}\n\n\t.built-with {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.built-with:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.built-with img {\n\t\tmargin-right: var(--size-1);\n\t\tmargin-left: var(--size-1);\n\t\tmargin-bottom: 1px;\n\t\twidth: var(--size-4);\n\t}\n\n\t.api-docs {\n\t\tdisplay: flex;\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tz-index: var(--layer-5);\n\t\tbackground: rgba(0, 0, 0, 0.5);\n\t\twidth: var(--size-screen);\n\t\theight: var(--size-screen-h);\n\t}\n\n\t.backdrop {\n\t\tflex: 1 1 0%;\n\t\t-webkit-backdrop-filter: blur(4px);\n\t\tbackdrop-filter: blur(4px);\n\t}\n\n\t.api-docs-wrap {\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tbackground: var(--background-fill-primary);\n\t\toverflow-x: hidden;\n\t\toverflow-y: auto;\n\t}\n\n\t@media (--screen-md) {\n\t\t.api-docs-wrap {\n\t\t\tborder-top-left-radius: var(--radius-lg);\n\t\t\tborder-bottom-left-radius: var(--radius-lg);\n\t\t\twidth: 950px;\n\t\t}\n\t}\n\n\t@media (--screen-xxl) {\n\t\t.api-docs-wrap {\n\t\t\twidth: 1150px;\n\t\t}\n\t}\n</style>\n"], "file": "assets/Blocks-3011bbe9.js"}