{"version": 3, "file": "Index-a15403b4.js", "sources": ["../../../../js/icons/src/Remove.svelte", "../../../../js/dropdown/shared/DropdownOptions.svelte", "../../../../js/dropdown/shared/utils.ts", "../../../../js/dropdown/shared/Multiselect.svelte", "../../../../js/dropdown/shared/Dropdown.svelte", "../../../../js/dropdown/Index.svelte"], "sourcesContent": ["<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n\t<path\n\t\td=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\timport { fly } from \"svelte/transition\";\n\timport { createEventDispatcher } from \"svelte\";\n\texport let choices: [string, string | number][];\n\texport let filtered_indices: number[];\n\texport let show_options = false;\n\texport let disabled = false;\n\texport let selected_indices: (string | number)[] = [];\n\texport let active_index: number | null = null;\n\n\tlet distance_from_top: number;\n\tlet distance_from_bottom: number;\n\tlet input_height: number;\n\tlet input_width: number;\n\tlet refElement: HTMLDivElement;\n\tlet listElement: HTMLUListElement;\n\tlet top: string | null, bottom: string | null, max_height: number;\n\tlet innerHeight: number;\n\n\tfunction calculate_window_distance(): void {\n\t\tconst { top: ref_top, bottom: ref_bottom } =\n\t\t\trefElement.getBoundingClientRect();\n\t\tdistance_from_top = ref_top;\n\t\tdistance_from_bottom = innerHeight - ref_bottom;\n\t}\n\n\tlet scroll_timeout: NodeJS.Timeout | null = null;\n\tfunction scroll_listener(): void {\n\t\tif (!show_options) return;\n\t\tif (scroll_timeout !== null) {\n\t\t\tclearTimeout(scroll_timeout);\n\t\t}\n\n\t\tscroll_timeout = setTimeout(() => {\n\t\t\tcalculate_window_distance();\n\t\t\tscroll_timeout = null;\n\t\t}, 10);\n\t}\n\n\t$: {\n\t\tif (show_options && refElement) {\n\t\t\tif (listElement && selected_indices.length > 0) {\n\t\t\t\tlet elements = listElement.querySelectorAll(\"li\");\n\t\t\t\tfor (const element of Array.from(elements)) {\n\t\t\t\t\tif (\n\t\t\t\t\t\telement.getAttribute(\"data-index\") ===\n\t\t\t\t\t\tselected_indices[0].toString()\n\t\t\t\t\t) {\n\t\t\t\t\t\tlistElement?.scrollTo?.(0, (element as HTMLLIElement).offsetTop);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tcalculate_window_distance();\n\t\t\tconst rect = refElement.parentElement?.getBoundingClientRect();\n\t\t\tinput_height = rect?.height || 0;\n\t\t\tinput_width = rect?.width || 0;\n\t\t}\n\t\tif (distance_from_bottom > distance_from_top) {\n\t\t\ttop = `${distance_from_top}px`;\n\t\t\tmax_height = distance_from_bottom;\n\t\t\tbottom = null;\n\t\t} else {\n\t\t\tbottom = `${distance_from_bottom + input_height}px`;\n\t\t\tmax_height = distance_from_top - input_height;\n\t\t\ttop = null;\n\t\t}\n\t}\n\n\tconst dispatch = createEventDispatcher();\n</script>\n\n<svelte:window on:scroll={scroll_listener} bind:innerHeight />\n\n<div class=\"reference\" bind:this={refElement} />\n{#if show_options && !disabled}\n\t<ul\n\t\tclass=\"options\"\n\t\ttransition:fly={{ duration: 200, y: 5 }}\n\t\ton:mousedown|preventDefault={(e) => dispatch(\"change\", e)}\n\t\tstyle:top\n\t\tstyle:bottom\n\t\tstyle:max-height={`calc(${max_height}px - var(--window-padding))`}\n\t\tstyle:width={input_width + \"px\"}\n\t\tbind:this={listElement}\n\t\trole=\"listbox\"\n\t>\n\t\t{#each filtered_indices as index}\n\t\t\t<li\n\t\t\t\tclass=\"item\"\n\t\t\t\tclass:selected={selected_indices.includes(index)}\n\t\t\t\tclass:active={index === active_index}\n\t\t\t\tclass:bg-gray-100={index === active_index}\n\t\t\t\tclass:dark:bg-gray-600={index === active_index}\n\t\t\t\tdata-index={index}\n\t\t\t\taria-label={choices[index][0]}\n\t\t\t\tdata-testid=\"dropdown-option\"\n\t\t\t\trole=\"option\"\n\t\t\t\taria-selected={selected_indices.includes(index)}\n\t\t\t>\n\t\t\t\t<span class:hide={!selected_indices.includes(index)} class=\"inner-item\">\n\t\t\t\t\t✓\n\t\t\t\t</span>\n\t\t\t\t{choices[index][0]}\n\t\t\t</li>\n\t\t{/each}\n\t</ul>\n{/if}\n\n<style>\n\t.options {\n\t\t--window-padding: var(--size-8);\n\t\tposition: fixed;\n\t\tz-index: var(--layer-top);\n\t\tmargin-left: 0;\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tborder-radius: var(--container-radius);\n\t\tbackground: var(--background-fill-primary);\n\t\tmin-width: fit-content;\n\t\tmax-width: inherit;\n\t\toverflow: auto;\n\t\tcolor: var(--body-text-color);\n\t\tlist-style: none;\n\t}\n\n\t.item {\n\t\tdisplay: flex;\n\t\tcursor: pointer;\n\t\tpadding: var(--size-2);\n\t}\n\n\t.item:hover,\n\t.active {\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.inner-item {\n\t\tpadding-right: var(--size-1);\n\t}\n\n\t.hide {\n\t\tvisibility: hidden;\n\t}\n</style>\n", "function positive_mod(n: number, m: number): number {\n\treturn ((n % m) + m) % m;\n}\n\nexport function handle_filter(\n\tchoices: [string, string | number][],\n\tinput_text: string\n): number[] {\n\treturn choices.reduce((filtered_indices, o, index) => {\n\t\tif (\n\t\t\tinput_text ? o[0].toLowerCase().includes(input_text.toLowerCase()) : true\n\t\t) {\n\t\t\tfiltered_indices.push(index);\n\t\t}\n\t\treturn filtered_indices;\n\t}, [] as number[]);\n}\n\nexport function handle_change(\n\tdispatch: any,\n\tvalue: string | number | (string | number)[] | undefined,\n\tvalue_is_output: boolean\n): void {\n\tdispatch(\"change\", value);\n\tif (!value_is_output) {\n\t\tdispatch(\"input\");\n\t}\n}\n\nexport function handle_shared_keys(\n\te: KeyboardEvent,\n\tactive_index: number | null,\n\tfiltered_indices: number[]\n): [boolean, number | null] {\n\tif (e.key === \"Escape\") {\n\t\treturn [false, active_index];\n\t}\n\tif (e.key === \"ArrowDown\" || e.key === \"ArrowUp\") {\n\t\tif (filtered_indices.length >= 0) {\n\t\t\tif (active_index === null) {\n\t\t\t\tactive_index =\n\t\t\t\t\te.key === \"ArrowDown\"\n\t\t\t\t\t\t? filtered_indices[0]\n\t\t\t\t\t\t: filtered_indices[filtered_indices.length - 1];\n\t\t\t} else {\n\t\t\t\tconst index_in_filtered = filtered_indices.indexOf(active_index);\n\t\t\t\tconst increment = e.key === \"ArrowUp\" ? -1 : 1;\n\t\t\t\tactive_index =\n\t\t\t\t\tfiltered_indices[\n\t\t\t\t\t\tpositive_mod(index_in_filtered + increment, filtered_indices.length)\n\t\t\t\t\t];\n\t\t\t}\n\t\t}\n\t}\n\treturn [true, active_index];\n}\n", "<script lang=\"ts\">\n\timport { afterUpdate, createEventDispatcher } from \"svelte\";\n\timport { _, number } from \"svelte-i18n\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\timport { Remove, DropdownArrow } from \"@gradio/icons\";\n\timport type { <PERSON>Up<PERSON><PERSON>, SelectData, I18nFormatter } from \"@gradio/utils\";\n\timport DropdownOptions from \"./DropdownOptions.svelte\";\n\timport { handle_filter, handle_change, handle_shared_keys } from \"./utils\";\n\n\texport let label: string;\n\texport let info: string | undefined = undefined;\n\texport let value: string | number | (string | number)[] | undefined = [];\n\tlet old_value: string | number | (string | number)[] | undefined = [];\n\texport let value_is_output = false;\n\texport let max_choices: number | null = null;\n\texport let choices: [string, string | number][];\n\tlet old_choices: [string, string | number][];\n\texport let disabled = false;\n\texport let show_label: boolean;\n\texport let container = true;\n\texport let allow_custom_value = false;\n\texport let filterable = true;\n\texport let i18n: I18nFormatter;\n\n\tlet filter_input: HTMLElement;\n\tlet input_text = \"\";\n\tlet old_input_text = \"\";\n\tlet show_options = false;\n\tlet choices_names: string[];\n\tlet choices_values: (string | number)[];\n\n\t// All of these are indices with respect to the choices array\n\tlet filtered_indices: number[] = [];\n\tlet active_index: number | null = null;\n\t// selected_index consists of indices from choices or strings if allow_custom_value is true and user types in a custom value\n\tlet selected_indices: (number | string)[] = [];\n\tlet old_selected_index: (number | string)[] = [];\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: string | string[] | undefined;\n\t\tinput: undefined;\n\t\tselect: SelectData;\n\t\tblur: undefined;\n\t\tfocus: undefined;\n\t\tkey_up: KeyUpData;\n\t}>();\n\n\t// Setting the initial value of the multiselect dropdown\n\tif (Array.isArray(value)) {\n\t\tvalue.forEach((element) => {\n\t\t\tconst index = choices.map((c) => c[1]).indexOf(element);\n\t\t\tif (index !== -1) {\n\t\t\t\tselected_indices.push(index);\n\t\t\t} else {\n\t\t\t\tselected_indices.push(element);\n\t\t\t}\n\t\t});\n\t}\n\n\t$: {\n\t\tchoices_names = choices.map((c) => c[0]);\n\t\tchoices_values = choices.map((c) => c[1]);\n\t}\n\n\t$: {\n\t\tif (choices !== old_choices || input_text !== old_input_text) {\n\t\t\tfiltered_indices = handle_filter(choices, input_text);\n\t\t\told_choices = choices;\n\t\t\told_input_text = input_text;\n\t\t\tif (!allow_custom_value) {\n\t\t\t\tactive_index = filtered_indices[0];\n\t\t\t}\n\t\t}\n\t}\n\n\t$: {\n\t\tif (JSON.stringify(value) != JSON.stringify(old_value)) {\n\t\t\thandle_change(dispatch, value, value_is_output);\n\t\t\told_value = Array.isArray(value) ? value.slice() : value;\n\t\t}\n\t}\n\n\t$: {\n\t\tif (\n\t\t\tJSON.stringify(selected_indices) != JSON.stringify(old_selected_index)\n\t\t) {\n\t\t\tvalue = selected_indices.map((index) =>\n\t\t\t\ttypeof index === \"number\" ? choices_values[index] : index\n\t\t\t);\n\t\t\told_selected_index = selected_indices.slice();\n\t\t}\n\t}\n\n\tfunction handle_blur(): void {\n\t\tif (!allow_custom_value) {\n\t\t\tinput_text = \"\";\n\t\t}\n\n\t\tif (allow_custom_value && input_text !== \"\") {\n\t\t\tadd_selected_choice(input_text);\n\t\t\tinput_text = \"\";\n\t\t}\n\n\t\tshow_options = false;\n\t\tactive_index = null;\n\t\tdispatch(\"blur\");\n\t}\n\n\tfunction remove_selected_choice(option_index: number | string): void {\n\t\tselected_indices = selected_indices.filter((v) => v !== option_index);\n\t\tdispatch(\"select\", {\n\t\t\tindex: typeof option_index === \"number\" ? option_index : -1,\n\t\t\tvalue:\n\t\t\t\ttypeof option_index === \"number\"\n\t\t\t\t\t? choices_values[option_index]\n\t\t\t\t\t: option_index,\n\t\t\tselected: false\n\t\t});\n\t}\n\n\tfunction add_selected_choice(option_index: number | string): void {\n\t\tif (max_choices === null || selected_indices.length < max_choices) {\n\t\t\tselected_indices = [...selected_indices, option_index];\n\t\t\tdispatch(\"select\", {\n\t\t\t\tindex: typeof option_index === \"number\" ? option_index : -1,\n\t\t\t\tvalue:\n\t\t\t\t\ttypeof option_index === \"number\"\n\t\t\t\t\t\t? choices_values[option_index]\n\t\t\t\t\t\t: option_index,\n\t\t\t\tselected: true\n\t\t\t});\n\t\t}\n\t\tif (selected_indices.length === max_choices) {\n\t\t\tshow_options = false;\n\t\t\tactive_index = null;\n\t\t\tfilter_input.blur();\n\t\t}\n\t}\n\n\tfunction handle_option_selected(e: any): void {\n\t\tconst option_index = parseInt(e.detail.target.dataset.index);\n\t\tadd_or_remove_index(option_index);\n\t}\n\n\tfunction add_or_remove_index(option_index: number): void {\n\t\tif (selected_indices.includes(option_index)) {\n\t\t\tremove_selected_choice(option_index);\n\t\t} else {\n\t\t\tadd_selected_choice(option_index);\n\t\t}\n\t\tinput_text = \"\";\n\t}\n\n\tfunction remove_all(e: any): void {\n\t\tselected_indices = [];\n\t\tinput_text = \"\";\n\t\te.preventDefault();\n\t}\n\n\tfunction handle_focus(e: FocusEvent): void {\n\t\tfiltered_indices = choices.map((_, i) => i);\n\t\tif (max_choices === null || selected_indices.length < max_choices) {\n\t\t\tshow_options = true;\n\t\t}\n\t\tdispatch(\"focus\");\n\t}\n\n\tfunction handle_key_down(e: KeyboardEvent): void {\n\t\t[show_options, active_index] = handle_shared_keys(\n\t\t\te,\n\t\t\tactive_index,\n\t\t\tfiltered_indices\n\t\t);\n\t\tif (e.key === \"Enter\") {\n\t\t\tif (active_index !== null) {\n\t\t\t\tadd_or_remove_index(active_index);\n\t\t\t} else {\n\t\t\t\tif (allow_custom_value) {\n\t\t\t\t\tadd_selected_choice(input_text);\n\t\t\t\t\tinput_text = \"\";\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (e.key === \"Backspace\" && input_text === \"\") {\n\t\t\tselected_indices = [...selected_indices.slice(0, -1)];\n\t\t}\n\t\tif (selected_indices.length === max_choices) {\n\t\t\tshow_options = false;\n\t\t\tactive_index = null;\n\t\t}\n\t}\n\n\tfunction set_selected_indices(): void {\n\t\tif (value === undefined) {\n\t\t\tselected_indices = [];\n\t\t} else if (Array.isArray(value)) {\n\t\t\tselected_indices = value\n\t\t\t\t.map((v) => {\n\t\t\t\t\tconst index = choices_values.indexOf(v);\n\t\t\t\t\tif (index !== -1) {\n\t\t\t\t\t\treturn index;\n\t\t\t\t\t}\n\t\t\t\t\tif (allow_custom_value) {\n\t\t\t\t\t\treturn v;\n\t\t\t\t\t}\n\t\t\t\t\t// Instead of returning null, skip this iteration\n\t\t\t\t\treturn undefined;\n\t\t\t\t})\n\t\t\t\t.filter((val): val is string | number => val !== undefined);\n\t\t}\n\t}\n\n\t$: value, set_selected_indices();\n\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n</script>\n\n<label class:container>\n\t<BlockTitle {show_label} {info}>{label}</BlockTitle>\n\n\t<div class=\"wrap\">\n\t\t<div class=\"wrap-inner\" class:show_options>\n\t\t\t{#each selected_indices as s}\n\t\t\t\t<div class=\"token\">\n\t\t\t\t\t<span>\n\t\t\t\t\t\t{#if typeof s === \"number\"}\n\t\t\t\t\t\t\t{choices_names[s]}\n\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t{s}\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</span>\n\t\t\t\t\t{#if !disabled}\n\t\t\t\t\t\t<div\n\t\t\t\t\t\t\tclass=\"token-remove\"\n\t\t\t\t\t\t\ton:click|preventDefault={() => remove_selected_choice(s)}\n\t\t\t\t\t\t\ton:keydown|preventDefault={(event) => {\n\t\t\t\t\t\t\t\tif (event.key === \"Enter\") {\n\t\t\t\t\t\t\t\t\tremove_selected_choice(s);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\t\ttitle={i18n(\"common.remove\") + \" \" + s}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<Remove />\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t</div>\n\t\t\t{/each}\n\t\t\t<div class=\"secondary-wrap\">\n\t\t\t\t<input\n\t\t\t\t\tclass=\"border-none\"\n\t\t\t\t\tclass:subdued={(!choices_names.includes(input_text) &&\n\t\t\t\t\t\t!allow_custom_value) ||\n\t\t\t\t\t\tselected_indices.length === max_choices}\n\t\t\t\t\t{disabled}\n\t\t\t\t\tautocomplete=\"off\"\n\t\t\t\t\tbind:value={input_text}\n\t\t\t\t\tbind:this={filter_input}\n\t\t\t\t\ton:keydown={handle_key_down}\n\t\t\t\t\ton:keyup={(e) =>\n\t\t\t\t\t\tdispatch(\"key_up\", {\n\t\t\t\t\t\t\tkey: e.key,\n\t\t\t\t\t\t\tinput_value: input_text\n\t\t\t\t\t\t})}\n\t\t\t\t\ton:blur={handle_blur}\n\t\t\t\t\ton:focus={handle_focus}\n\t\t\t\t\treadonly={!filterable}\n\t\t\t\t/>\n\n\t\t\t\t{#if !disabled}\n\t\t\t\t\t{#if selected_indices.length > 0}\n\t\t\t\t\t\t<div\n\t\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\t\tclass=\"token-remove remove-all\"\n\t\t\t\t\t\t\ttitle={i18n(\"common.clear\")}\n\t\t\t\t\t\t\ton:click={remove_all}\n\t\t\t\t\t\t\ton:keydown={(event) => {\n\t\t\t\t\t\t\t\tif (event.key === \"Enter\") {\n\t\t\t\t\t\t\t\t\tremove_all(event);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<Remove />\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t\t<span class=\"icon-wrap\"> <DropdownArrow /></span>\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t</div>\n\t\t<DropdownOptions\n\t\t\t{show_options}\n\t\t\t{choices}\n\t\t\t{filtered_indices}\n\t\t\t{disabled}\n\t\t\t{selected_indices}\n\t\t\t{active_index}\n\t\t\ton:change={handle_option_selected}\n\t\t/>\n\t</div>\n</label>\n\n<style>\n\t.icon-wrap {\n\t\tcolor: var(--body-text-color);\n\t\tmargin-right: var(--size-2);\n\t\twidth: var(--size-5);\n\t}\n\tlabel:not(.container),\n\tlabel:not(.container) .wrap,\n\tlabel:not(.container) .wrap-inner,\n\tlabel:not(.container) .secondary-wrap,\n\tlabel:not(.container) .token,\n\tlabel:not(.container) input {\n\t\theight: 100%;\n\t}\n\t.container .wrap {\n\t\tbox-shadow: var(--input-shadow);\n\t\tborder: var(--input-border-width) solid var(--border-color-primary);\n\t}\n\n\t.wrap {\n\t\tposition: relative;\n\t\tborder-radius: var(--input-radius);\n\t\tbackground: var(--input-background-fill);\n\t}\n\n\t.wrap:focus-within {\n\t\tbox-shadow: var(--input-shadow-focus);\n\t\tborder-color: var(--input-border-color-focus);\n\t}\n\n\t.wrap-inner {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-wrap: wrap;\n\t\talign-items: center;\n\t\tgap: var(--checkbox-label-gap);\n\t\tpadding: var(--checkbox-label-padding);\n\t}\n\n\t.token {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\ttransition: var(--button-transition);\n\t\tcursor: pointer;\n\t\tbox-shadow: var(--checkbox-label-shadow);\n\t\tborder: var(--checkbox-label-border-width) solid\n\t\t\tvar(--checkbox-label-border-color);\n\t\tborder-radius: var(--button-small-radius);\n\t\tbackground: var(--checkbox-label-background-fill);\n\t\tpadding: var(--checkbox-label-padding);\n\t\tcolor: var(--checkbox-label-text-color);\n\t\tfont-weight: var(--checkbox-label-text-weight);\n\t\tfont-size: var(--checkbox-label-text-size);\n\t\tline-height: var(--line-md);\n\t}\n\n\t.token > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\t.token-remove {\n\t\tfill: var(--body-text-color);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tcursor: pointer;\n\t\tborder: var(--checkbox-border-width) solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--background-fill-primary);\n\t\tpadding: var(--size-0-5);\n\t\twidth: 16px;\n\t\theight: 16px;\n\t}\n\n\t.secondary-wrap {\n\t\tdisplay: flex;\n\t\tflex: 1 1 0%;\n\t\talign-items: center;\n\t\tborder: none;\n\t\tmin-width: min-content;\n\t}\n\n\tinput {\n\t\tmargin: var(--spacing-sm);\n\t\toutline: none;\n\t\tborder: none;\n\t\tbackground: inherit;\n\t\twidth: var(--size-full);\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t}\n\n\tinput:disabled {\n\t\t-webkit-text-fill-color: var(--body-text-color);\n\t\t-webkit-opacity: 1;\n\t\topacity: 1;\n\t\tcursor: not-allowed;\n\t}\n\n\t.remove-all {\n\t\tmargin-left: var(--size-1);\n\t\twidth: 20px;\n\t\theight: 20px;\n\t}\n\t.subdued {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\tinput[readonly] {\n\t\tcursor: pointer;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport DropdownOptions from \"./DropdownOptions.svelte\";\n\timport { createEventDispatcher, afterUpdate } from \"svelte\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\timport { DropdownArrow } from \"@gradio/icons\";\n\timport type { SelectData, KeyUpData } from \"@gradio/utils\";\n\timport { handle_filter, handle_change, handle_shared_keys } from \"./utils\";\n\n\texport let label: string;\n\texport let info: string | undefined = undefined;\n\texport let value: string | number | (string | number)[] | undefined = [];\n\tlet old_value: string | number | (string | number)[] | undefined = [];\n\texport let value_is_output = false;\n\texport let choices: [string, string | number][];\n\tlet old_choices: [string, string | number][];\n\texport let disabled = false;\n\texport let show_label: boolean;\n\texport let container = true;\n\texport let allow_custom_value = false;\n\texport let filterable = true;\n\n\tlet filter_input: HTMLElement;\n\n\tlet show_options = false;\n\tlet choices_names: string[];\n\tlet choices_values: (string | number)[];\n\tlet input_text = \"\";\n\tlet old_input_text = \"\";\n\tlet initialized = false;\n\n\t// All of these are indices with respect to the choices array\n\tlet filtered_indices: number[] = [];\n\tlet active_index: number | null = null;\n\t// selected_index is null if allow_custom_value is true and the input_text is not in choices_names\n\tlet selected_index: number | null = null;\n\tlet old_selected_index: number | null;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: string | undefined;\n\t\tinput: undefined;\n\t\tselect: SelectData;\n\t\tblur: undefined;\n\t\tfocus: undefined;\n\t\tkey_up: KeyUpData;\n\t}>();\n\n\t// Setting the initial value of the dropdown\n\tif (value) {\n\t\told_selected_index = choices.map((c) => c[1]).indexOf(value as string);\n\t\tselected_index = old_selected_index;\n\t\tif (selected_index === -1) {\n\t\t\told_value = value;\n\t\t\tselected_index = null;\n\t\t} else {\n\t\t\t[input_text, old_value] = choices[selected_index];\n\t\t\told_input_text = input_text;\n\t\t}\n\t\tset_input_text();\n\t} else if (choices.length > 0) {\n\t\told_selected_index = 0;\n\t\tselected_index = 0;\n\t\t[input_text, value] = choices[selected_index];\n\t\told_value = value;\n\t\told_input_text = input_text;\n\t}\n\n\t$: {\n\t\tif (\n\t\t\tselected_index !== old_selected_index &&\n\t\t\tselected_index !== null &&\n\t\t\tinitialized\n\t\t) {\n\t\t\t[input_text, value] = choices[selected_index];\n\t\t\told_selected_index = selected_index;\n\t\t\tdispatch(\"select\", {\n\t\t\t\tindex: selected_index,\n\t\t\t\tvalue: choices_values[selected_index],\n\t\t\t\tselected: true\n\t\t\t});\n\t\t}\n\t}\n\n\t$: {\n\t\tif (value != old_value) {\n\t\t\tset_input_text();\n\t\t\thandle_change(dispatch, value, value_is_output);\n\t\t\told_value = value;\n\t\t}\n\t}\n\n\tfunction set_choice_names_values(): void {\n\t\tchoices_names = choices.map((c) => c[0]);\n\t\tchoices_values = choices.map((c) => c[1]);\n\t}\n\n\t$: choices, set_choice_names_values();\n\n\t$: {\n\t\tif (choices !== old_choices) {\n\t\t\tif (!allow_custom_value) {\n\t\t\t\tset_input_text();\n\t\t\t}\n\t\t\told_choices = choices;\n\t\t\tfiltered_indices = handle_filter(choices, input_text);\n\t\t\tif (!allow_custom_value && filtered_indices.length > 0) {\n\t\t\t\tactive_index = filtered_indices[0];\n\t\t\t}\n\t\t\tif (filter_input == document.activeElement) {\n\t\t\t\tshow_options = true;\n\t\t\t}\n\t\t}\n\t}\n\n\t$: {\n\t\tif (input_text !== old_input_text) {\n\t\t\tfiltered_indices = handle_filter(choices, input_text);\n\t\t\told_input_text = input_text;\n\t\t\tif (!allow_custom_value && filtered_indices.length > 0) {\n\t\t\t\tactive_index = filtered_indices[0];\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction set_input_text(): void {\n\t\tset_choice_names_values();\n\t\tif (value === undefined || (Array.isArray(value) && value.length === 0)) {\n\t\t\tinput_text = \"\";\n\t\t\tselected_index = null;\n\t\t} else if (choices_values.includes(value as string)) {\n\t\t\tinput_text = choices_names[choices_values.indexOf(value as string)];\n\t\t\tselected_index = choices_values.indexOf(value as string);\n\t\t} else if (allow_custom_value) {\n\t\t\tinput_text = value as string;\n\t\t\tselected_index = null;\n\t\t} else {\n\t\t\tinput_text = \"\";\n\t\t\tselected_index = null;\n\t\t}\n\t\told_selected_index = selected_index;\n\t}\n\n\tfunction handle_option_selected(e: any): void {\n\t\tselected_index = parseInt(e.detail.target.dataset.index);\n\t\tif (isNaN(selected_index)) {\n\t\t\t// This is the case when the user clicks on the scrollbar\n\t\t\tselected_index = null;\n\t\t\treturn;\n\t\t}\n\t\tshow_options = false;\n\t\tactive_index = null;\n\t\tfilter_input.blur();\n\t}\n\n\tfunction handle_focus(e: FocusEvent): void {\n\t\tfiltered_indices = choices.map((_, i) => i);\n\t\tshow_options = true;\n\t\tdispatch(\"focus\");\n\t}\n\n\tfunction handle_blur(): void {\n\t\tif (!allow_custom_value) {\n\t\t\tinput_text = choices_names[choices_values.indexOf(value as string)];\n\t\t} else {\n\t\t\tvalue = input_text;\n\t\t}\n\t\tshow_options = false;\n\t\tactive_index = null;\n\t\tdispatch(\"blur\");\n\t}\n\n\tfunction handle_key_down(e: KeyboardEvent): void {\n\t\t[show_options, active_index] = handle_shared_keys(\n\t\t\te,\n\t\t\tactive_index,\n\t\t\tfiltered_indices\n\t\t);\n\t\tif (e.key === \"Enter\") {\n\t\t\tif (active_index !== null) {\n\t\t\t\tselected_index = active_index;\n\t\t\t\tshow_options = false;\n\t\t\t\tfilter_input.blur();\n\t\t\t\tactive_index = null;\n\t\t\t} else if (choices_names.includes(input_text)) {\n\t\t\t\tselected_index = choices_names.indexOf(input_text);\n\t\t\t\tshow_options = false;\n\t\t\t\tactive_index = null;\n\t\t\t\tfilter_input.blur();\n\t\t\t} else if (allow_custom_value) {\n\t\t\t\tvalue = input_text;\n\t\t\t\tselected_index = null;\n\t\t\t\tshow_options = false;\n\t\t\t\tactive_index = null;\n\t\t\t\tfilter_input.blur();\n\t\t\t}\n\t\t}\n\t}\n\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t\tinitialized = true;\n\t});\n</script>\n\n<div class:container>\n\t<BlockTitle {show_label} {info}>{label}</BlockTitle>\n\n\t<div class=\"wrap\">\n\t\t<div class=\"wrap-inner\" class:show_options>\n\t\t\t<div class=\"secondary-wrap\">\n\t\t\t\t<input\n\t\t\t\t\trole=\"listbox\"\n\t\t\t\t\taria-controls=\"dropdown-options\"\n\t\t\t\t\taria-expanded={show_options}\n\t\t\t\t\taria-label={label}\n\t\t\t\t\tclass=\"border-none\"\n\t\t\t\t\tclass:subdued={!choices_names.includes(input_text) &&\n\t\t\t\t\t\t!allow_custom_value}\n\t\t\t\t\t{disabled}\n\t\t\t\t\tautocomplete=\"off\"\n\t\t\t\t\tbind:value={input_text}\n\t\t\t\t\tbind:this={filter_input}\n\t\t\t\t\ton:keydown={handle_key_down}\n\t\t\t\t\ton:keyup={(e) =>\n\t\t\t\t\t\tdispatch(\"key_up\", {\n\t\t\t\t\t\t\tkey: e.key,\n\t\t\t\t\t\t\tinput_value: input_text\n\t\t\t\t\t\t})}\n\t\t\t\t\ton:blur={handle_blur}\n\t\t\t\t\ton:focus={handle_focus}\n\t\t\t\t\treadonly={!filterable}\n\t\t\t\t/>\n\t\t\t\t{#if !disabled}\n\t\t\t\t\t<div class=\"icon-wrap\">\n\t\t\t\t\t\t<DropdownArrow />\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t</div>\n\t\t<DropdownOptions\n\t\t\t{show_options}\n\t\t\t{choices}\n\t\t\t{filtered_indices}\n\t\t\t{disabled}\n\t\t\tselected_indices={selected_index === null ? [] : [selected_index]}\n\t\t\t{active_index}\n\t\t\ton:change={handle_option_selected}\n\t\t/>\n\t</div>\n</div>\n\n<style>\n\t.icon-wrap {\n\t\tcolor: var(--body-text-color);\n\t\tmargin-right: var(--size-2);\n\t\twidth: var(--size-5);\n\t}\n\t.container {\n\t\theight: 100%;\n\t}\n\t.container .wrap {\n\t\tbox-shadow: var(--input-shadow);\n\t\tborder: var(--input-border-width) solid var(--border-color-primary);\n\t}\n\n\t.wrap {\n\t\tposition: relative;\n\t\tborder-radius: var(--input-radius);\n\t\tbackground: var(--input-background-fill);\n\t}\n\n\t.wrap:focus-within {\n\t\tbox-shadow: var(--input-shadow-focus);\n\t\tborder-color: var(--input-border-color-focus);\n\t}\n\n\t.wrap-inner {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-wrap: wrap;\n\t\talign-items: center;\n\t\tgap: var(--checkbox-label-gap);\n\t\tpadding: var(--checkbox-label-padding);\n\t\theight: 100%;\n\t}\n\t.secondary-wrap {\n\t\tdisplay: flex;\n\t\tflex: 1 1 0%;\n\t\talign-items: center;\n\t\tborder: none;\n\t\tmin-width: min-content;\n\t\theight: 100%;\n\t}\n\n\tinput {\n\t\tmargin: var(--spacing-sm);\n\t\toutline: none;\n\t\tborder: none;\n\t\tbackground: inherit;\n\t\twidth: var(--size-full);\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t\theight: 100%;\n\t}\n\n\tinput:disabled {\n\t\t-webkit-text-fill-color: var(--body-text-color);\n\t\t-webkit-opacity: 1;\n\t\topacity: 1;\n\t\tcursor: not-allowed;\n\t}\n\n\t.subdued {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\tinput[readonly] {\n\t\tcursor: pointer;\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseDropdown } from \"./shared/Dropdown.svelte\";\n\texport { default as BaseMultiselect } from \"./shared/Multiselect.svelte\";\n\texport { default as BaseExample } from \"./Example.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, KeyUpData, SelectData } from \"@gradio/utils\";\n\timport Multiselect from \"./shared/Multiselect.svelte\";\n\timport Dropdown from \"./shared/Dropdown.svelte\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\texport let label = \"Dropdown\";\n\texport let info: string | undefined = undefined;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: string | string[] | undefined = undefined;\n\texport let value_is_output = false;\n\texport let multiselect = false;\n\texport let max_choices: number | null = null;\n\texport let choices: [string, string | number][];\n\texport let show_label: boolean;\n\texport let filterable: boolean;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let allow_custom_value = false;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tinput: never;\n\t\tselect: SelectData;\n\t\tblur: never;\n\t\tfocus: never;\n\t\tkey_up: KeyUpData;\n\t}>;\n\texport let interactive: boolean;\n</script>\n\n<Block\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\tpadding={container}\n\tallow_overflow={false}\n\t{scale}\n\t{min_width}\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t/>\n\n\t{#if multiselect}\n\t\t<Multiselect\n\t\t\tbind:value\n\t\t\tbind:value_is_output\n\t\t\t{choices}\n\t\t\t{max_choices}\n\t\t\t{label}\n\t\t\t{info}\n\t\t\t{show_label}\n\t\t\t{allow_custom_value}\n\t\t\t{filterable}\n\t\t\t{container}\n\t\t\ti18n={gradio.i18n}\n\t\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\t\ton:input={() => gradio.dispatch(\"input\")}\n\t\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\t\ton:blur={() => gradio.dispatch(\"blur\")}\n\t\t\ton:focus={() => gradio.dispatch(\"focus\")}\n\t\t\ton:key_up={() => gradio.dispatch(\"key_up\")}\n\t\t\tdisabled={!interactive}\n\t\t/>\n\t{:else}\n\t\t<Dropdown\n\t\t\tbind:value\n\t\t\tbind:value_is_output\n\t\t\t{choices}\n\t\t\t{label}\n\t\t\t{info}\n\t\t\t{show_label}\n\t\t\t{filterable}\n\t\t\t{allow_custom_value}\n\t\t\t{container}\n\t\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\t\ton:input={() => gradio.dispatch(\"input\")}\n\t\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\t\ton:blur={() => gradio.dispatch(\"blur\")}\n\t\t\ton:focus={() => gradio.dispatch(\"focus\")}\n\t\t\ton:key_up={(e) => gradio.dispatch(\"key_up\", e.detail)}\n\t\t\tdisabled={!interactive}\n\t\t/>\n\t{/if}\n</Block>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "createEventDispatcher", "ctx", "i", "set_style", "ul", "ul_transition", "create_bidirectional_transition", "fly", "toggle_class", "li", "span", "set_data", "t2", "t2_value", "if_block", "create_if_block", "div", "choices", "$$props", "filtered_indices", "show_options", "disabled", "selected_indices", "active_index", "distance_from_top", "distance_from_bottom", "input_height", "input_width", "refElement", "listElement", "top", "bottom", "max_height", "innerHeight", "calculate_window_distance", "ref_top", "ref_bottom", "$$invalidate", "scroll_timeout", "scroll_listener", "dispatch", "$$value", "mousedown_handler", "e", "elements", "element", "rect", "positive_mod", "m", "handle_filter", "input_text", "o", "index", "handle_change", "value", "value_is_output", "handle_shared_keys", "index_in_filtered", "increment", "t", "t_value", "dirty", "attr", "div_title_value", "current", "create_if_block_3", "create_if_block_2", "create_if_block_1", "label_1", "div2", "div1", "div0", "input", "each_blocks", "label", "info", "old_value", "max_choices", "old_choices", "show_label", "container", "allow_custom_value", "filterable", "i18n", "filter_input", "old_input_text", "choices_names", "choices_values", "old_selected_index", "c", "handle_blur", "add_selected_choice", "remove_selected_choice", "option_index", "v", "handle_option_selected", "add_or_remove_index", "remove_all", "handle_focus", "_", "handle_key_down", "set_selected_indices", "val", "afterUpdate", "click_handler", "s", "event", "keyup_handler", "div3", "dropdownoptions_changes", "initialized", "selected_index", "set_input_text", "set_choice_names_values", "multiselect_1_changes", "elem_id", "elem_classes", "visible", "multiselect", "scale", "min_width", "loading_status", "gradio", "interactive"], "mappings": "gvBAAAA,GAIKC,EAAAC,EAAAC,CAAA,EAHJC,GAECF,EAAAG,CAAA,6gBCDQ,CAAAC,sBAAAA,WAAqC,sHAqFtCC,EAAgB,CAAA,CAAA,uBAArB,OAAIC,GAAA,yMALoBD,EAAU,EAAA,8BAAA,EACvBE,EAAAC,EAAA,QAAAH,KAAc,IAAI,UAPhCP,GA8BIC,EAAAS,EAAAP,CAAA,+HAnBII,EAAgB,CAAA,CAAA,oBAArB,OAAIC,GAAA,EAAA,mHAAJ,2FALwBD,EAAU,EAAA,8BAAA,SACvBE,EAAAC,EAAA,QAAAH,KAAc,IAAI,2BALbI,IAAAA,EAAAC,GAAAF,EAAAG,GAAA,CAAA,SAAU,IAAK,EAAG,CAAC,EAAA,EAAA,+BAAnBF,IAAAA,EAAAC,GAAAF,EAAAG,GAAA,CAAA,SAAU,IAAK,EAAG,CAAC,EAAA,EAAA,yGAyBlCN,EAAO,CAAA,EAACA,EAAK,EAAA,CAAA,EAAE,CAAC,EAAA,6IAHEA,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,CAAA,wDANtCA,EAAK,EAAA,CAAA,qBACLA,EAAO,CAAA,EAACA,EAAK,EAAA,CAAA,EAAE,CAAC,CAAA,kFAGbA,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,CAAA,iBAR9BA,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,CAAA,EACjCO,EAAAC,EAAA,SAAAR,QAAUA,EAAY,CAAA,CAAA,EACjBO,EAAAC,EAAA,cAAAR,QAAUA,EAAY,CAAA,CAAA,EACjBO,EAAAC,EAAA,mBAAAR,QAAUA,EAAY,CAAA,CAAA,UAL/CP,GAgBIC,EAAAc,EAAAZ,CAAA,EAJHC,GAEMW,EAAAC,CAAA,oDAFaT,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,CAAA,cAGjDA,EAAO,CAAA,EAACA,EAAK,EAAA,CAAA,EAAE,CAAC,EAAA,KAAAU,GAAAC,EAAAC,CAAA,cATLZ,EAAK,EAAA,oCACLA,EAAO,CAAA,EAACA,EAAK,EAAA,CAAA,EAAE,CAAC,qCAGbA,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,gDAR9BA,EAAgB,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,CAAA,QACjCO,EAAAC,EAAA,SAAAR,QAAUA,EAAY,CAAA,CAAA,QACjBO,EAAAC,EAAA,cAAAR,QAAUA,EAAY,CAAA,CAAA,QACjBO,EAAAC,EAAA,mBAAAR,QAAUA,EAAY,CAAA,CAAA,0DAlB7C,IAAAa,EAAAb,OAAiBA,EAAQ,CAAA,GAAAc,GAAAd,CAAA,iFAD9BP,GAA+CC,EAAAqB,EAAAnB,CAAA,qEAFrBI,EAAe,EAAA,CAAA,6CAGpCA,OAAiBA,EAAQ,CAAA,8NAxElB,QAAAgB,CAAoC,EAAAC,GACpC,iBAAAC,CAA0B,EAAAD,EAC1B,CAAA,aAAAE,EAAe,EAAK,EAAAF,EACpB,CAAA,SAAAG,EAAW,EAAK,EAAAH,GAChB,iBAAAI,EAAgB,EAAA,EAAAJ,EAChB,CAAA,aAAAK,EAA8B,IAAI,EAAAL,EAEzCM,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAAoBC,EAAuBC,EAC3CC,WAEKC,GAAyB,OACzB,IAAKC,EAAS,OAAQC,GAC7BR,EAAW,wBACZS,EAAA,GAAAb,EAAoBW,CAAO,OAC3BV,EAAuBQ,EAAcG,CAAU,EAG5C,IAAAE,EAAwC,cACnCC,GAAe,CAClBnB,IACDkB,IAAmB,MACtB,aAAaA,CAAc,EAG5BA,EAAiB,gBAChBJ,IACAI,EAAiB,MACf,KAiCE,MAAAE,EAAWxC,uFAKgB4B,EAAUa,WAKZ,MAAAC,EAAAC,GAAMH,EAAS,SAAUG,CAAC,4CAK7Cd,EAAWY,+TA7CtB,CACI,GAAArB,GAAgBQ,EAAU,CACzB,GAAAC,GAAeP,EAAiB,OAAS,EAAC,CACzC,IAAAsB,EAAWf,EAAY,iBAAiB,IAAI,EACrC,UAAAgB,KAAW,MAAM,KAAKD,CAAQ,KAEvCC,EAAQ,aAAa,YAAY,IACjCvB,EAAiB,CAAC,EAAE,WAAQ,CAE5BO,GAAa,WAAW,EAAIgB,EAA0B,SAAS,SAKlEX,IACM,MAAAY,EAAOlB,EAAW,eAAe,sBAAqB,EAC5DS,EAAA,GAAAX,EAAeoB,GAAM,QAAU,CAAC,EAChCT,EAAA,EAAAV,EAAcmB,GAAM,OAAS,CAAC,EAE3BrB,EAAuBD,GAC1Ba,EAAA,EAAAP,KAASN,KAAiB,EAC1Ba,EAAA,GAAAL,EAAaP,CAAoB,EACjCY,EAAA,GAAAN,EAAS,IAAI,SAEbA,EAAM,GAAMN,EAAuBC,KAAY,OAC/CM,EAAaR,EAAoBE,CAAY,EAC7CW,EAAA,EAAAP,EAAM,IAAI,8MCjEb,SAASiB,GAAa,EAAWC,EAAmB,CAC1C,OAAA,EAAIA,EAAKA,GAAKA,CACxB,CAEgB,SAAAC,GACfhC,EACAiC,EACW,CACX,OAAOjC,EAAQ,OAAO,CAACE,EAAkBgC,EAAGC,MAE1C,CAAAF,GAAaC,EAAE,CAAC,EAAE,YAAA,EAAc,SAASD,EAAW,aAAa,IAEjE/B,EAAiB,KAAKiC,CAAK,EAErBjC,GACL,CAAc,CAAA,CAClB,CAEgB,SAAAkC,GACfb,EACAc,EACAC,EACO,CACPf,EAAS,SAAUc,CAAK,EACnBC,GACJf,EAAS,OAAO,CAElB,CAEgB,SAAAgB,GACfb,EACApB,EACAJ,EAC2B,CACvB,GAAAwB,EAAE,MAAQ,SACN,MAAA,CAAC,GAAOpB,CAAY,EAE5B,IAAIoB,EAAE,MAAQ,aAAeA,EAAE,MAAQ,YAClCxB,EAAiB,QAAU,EAC9B,GAAII,IAAiB,KAEnBA,EAAAoB,EAAE,MAAQ,YACPxB,EAAiB,CAAC,EAClBA,EAAiBA,EAAiB,OAAS,CAAC,MAC1C,CACA,MAAAsC,EAAoBtC,EAAiB,QAAQI,CAAY,EACzDmC,EAAYf,EAAE,MAAQ,UAAY,GAAK,EAC7CpB,EACCJ,EACC4B,GAAaU,EAAoBC,EAAWvC,EAAiB,MAAM,CACpE,EAIG,MAAA,CAAC,GAAMI,CAAY,CAC3B,gbCtDqBvB,sBAAAA,IAAuB,OAAgB,yHA2N1BC,EAAK,CAAA,CAAA,wCAALA,EAAK,CAAA,CAAA,uCAU/BA,EAAC,EAAA,EAAA,mEAADA,EAAC,EAAA,EAAA,KAAAU,GAAAgD,EAAAC,CAAA,iCAFD,IAAAA,EAAA3D,MAAcA,EAAC,EAAA,CAAA,EAAA,iDAAf4D,EAAA,CAAA,EAAA,OAAAD,KAAAA,EAAA3D,MAAcA,EAAC,EAAA,CAAA,EAAA,KAAAU,GAAAgD,EAAAC,CAAA,oQAgBTE,EAAA9C,EAAA,QAAA+C,EAAA9D,EAAK,CAAA,EAAA,eAAe,EAAI,IAAMA,EAAC,EAAA,CAAA,UAVvCP,EAaKC,EAAAqB,EAAAnB,CAAA,uFAHG,CAAAmE,GAAAH,EAAA,CAAA,EAAA,MAAAE,KAAAA,EAAA9D,EAAK,CAAA,EAAA,eAAe,EAAI,IAAMA,EAAC,EAAA,iKAjB3B,OAAA,OAAAA,OAAM,SAAQgE,2BAMrBhE,EAAQ,CAAA,GAAAiE,GAAAjE,CAAA,kIARfP,EAwBKC,EAAAqB,EAAAnB,CAAA,EAvBJC,EAMMkB,EAAAN,CAAA,iHACAT,EAAQ,CAAA,wMAwCTA,EAAgB,EAAA,EAAC,OAAS,GAACkE,GAAAlE,CAAA,6IAgBhCP,EAAgDC,EAAAe,EAAAb,CAAA,4BAhB3CI,EAAgB,EAAA,EAAC,OAAS,sZAKtB6D,EAAA9C,EAAA,QAAA+C,EAAA9D,KAAK,cAAc,CAAA,UAJ3BP,EAaKC,EAAAqB,EAAAnB,CAAA,uCARMI,EAAU,EAAA,CAAA,uCADb,CAAA+D,GAAAH,EAAA,CAAA,EAAA,KAAAE,KAAAA,EAAA9D,KAAK,cAAc,qQAtDvBA,EAAgB,EAAA,CAAA,uBAArB,OAAIC,GAAA,mEAgDCD,EAAQ,CAAA,GAAAc,GAAAd,CAAA,2JA4BJA,EAAsB,EAAA,CAAA,iRA/BpBA,EAAU,CAAA,iBAfJA,EAAa,EAAA,EAAC,SAASA,EAAU,EAAA,CAAA,GAAA,CAChDA,EAAkB,CAAA,GACnBA,EAAgB,EAAA,EAAC,SAAWA,EAAW,CAAA,CAAA,6MArC7CP,EAoFOC,EAAAyE,EAAAvE,CAAA,sBAjFNC,EAgFKsE,EAAAC,CAAA,EA/EJvE,EAqEKuE,EAAAC,CAAA,0DAzCJxE,EAwCKwE,EAAAC,CAAA,EAvCJzE,EAkBCyE,EAAAC,CAAA,OAXYvE,EAAU,EAAA,CAAA,kGAEVA,EAAe,EAAA,CAAA,gCAMlBA,EAAW,EAAA,CAAA,cACVA,EAAY,EAAA,CAAA,4JA5CjBA,EAAgB,EAAA,CAAA,oBAArB,OAAIC,GAAA,EAAA,yGAAJ,OAAIA,EAAAuE,EAAA,OAAAvE,GAAA,oEA6COD,EAAU,CAAA,yCAVTA,EAAU,EAAA,QAAVA,EAAU,EAAA,CAAA,mCALLA,EAAa,EAAA,EAAC,SAASA,EAAU,EAAA,CAAA,GAAA,CAChDA,EAAkB,CAAA,GACnBA,EAAgB,EAAA,EAAC,SAAWA,EAAW,CAAA,CAAA,EAgBnCA,EAAQ,CAAA,mcAhDb,OAAIC,GAAA,iPAvNG,MAAAwE,CAAa,EAAAxD,EACb,CAAA,KAAAyD,EAA2B,MAAS,EAAAzD,GACpC,MAAAoC,EAAK,EAAA,EAAApC,EACZ0D,EAAS,CAAA,EACF,CAAA,gBAAArB,EAAkB,EAAK,EAAArC,EACvB,CAAA,YAAA2D,EAA6B,IAAI,EAAA3D,GACjC,QAAAD,CAAoC,EAAAC,EAC3C4D,EACO,CAAA,SAAAzD,EAAW,EAAK,EAAAH,GAChB,WAAA6D,CAAmB,EAAA7D,EACnB,CAAA,UAAA8D,EAAY,EAAI,EAAA9D,EAChB,CAAA,mBAAA+D,EAAqB,EAAK,EAAA/D,EAC1B,CAAA,WAAAgE,EAAa,EAAI,EAAAhE,GACjB,KAAAiE,CAAmB,EAAAjE,EAE1BkE,EACAlC,EAAa,GACbmC,EAAiB,GACjBjE,EAAe,GACfkE,EACAC,EAGApE,EAAgB,CAAA,EAChBI,EAA8B,KAE9BD,EAAgB,CAAA,EAChBkE,EAAkB,CAAA,EAEhB,MAAAhD,EAAWxC,KAUb,MAAM,QAAQsD,CAAK,GACtBA,EAAM,QAAST,GAAO,CACf,MAAAO,EAAQnC,EAAQ,IAAKwE,IAAMA,GAAE,CAAC,CAAG,EAAA,QAAQ5C,CAAO,EAClDO,OACH9B,EAAiB,KAAK8B,CAAK,EAE3B9B,EAAiB,KAAKuB,CAAO,aAuCvB6C,GAAW,CACdT,GACJ5C,EAAA,GAAAa,EAAa,EAAE,EAGZ+B,GAAsB/B,IAAe,KACxCyC,EAAoBzC,CAAU,EAC9Bb,EAAA,GAAAa,EAAa,EAAE,GAGhBb,EAAA,GAAAjB,EAAe,EAAK,EACpBiB,EAAA,GAAAd,EAAe,IAAI,EACnBiB,EAAS,MAAM,EAGP,SAAAoD,EAAuBC,EAA6B,MAC5DvE,EAAmBA,EAAiB,OAAQwE,GAAMA,IAAMD,CAAY,CAAA,EACpErD,EAAS,SAAQ,CAChB,aAAcqD,GAAiB,SAAWA,KAC1C,MAAK,OACGA,GAAiB,SACrBN,EAAeM,CAAY,EAC3BA,EACJ,SAAU,KAIH,SAAAF,EAAoBE,EAA6B,EACrDhB,IAAgB,MAAQvD,EAAiB,OAASuD,UACrDvD,EAAgB,CAAA,GAAOA,EAAkBuE,CAAY,CAAA,EACrDrD,EAAS,SAAQ,CAChB,aAAcqD,GAAiB,SAAWA,KAC1C,MAAK,OACGA,GAAiB,SACrBN,EAAeM,CAAY,EAC3BA,EACJ,SAAU,MAGRvE,EAAiB,SAAWuD,IAC/BxC,EAAA,GAAAjB,EAAe,EAAK,EACpBiB,EAAA,GAAAd,EAAe,IAAI,EACnB6D,EAAa,KAAI,GAIV,SAAAW,GAAuBpD,EAAM,OAC/BkD,EAAe,SAASlD,EAAE,OAAO,OAAO,QAAQ,KAAK,EAC3DqD,EAAoBH,CAAY,EAGxB,SAAAG,EAAoBH,EAAoB,CAC5CvE,EAAiB,SAASuE,CAAY,EACzCD,EAAuBC,CAAY,EAEnCF,EAAoBE,CAAY,EAEjCxD,EAAA,GAAAa,EAAa,EAAE,EAGP,SAAA+C,EAAWtD,EAAM,MACzBrB,EAAgB,CAAA,CAAA,EAChBe,EAAA,GAAAa,EAAa,EAAE,EACfP,EAAE,eAAc,EAGR,SAAAuD,GAAavD,EAAa,MAClCxB,EAAmBF,EAAQ,KAAKkF,EAAGjG,KAAMA,EAAC,CAAA,GACtC2E,IAAgB,MAAQvD,EAAiB,OAASuD,IACrDxC,EAAA,GAAAjB,EAAe,EAAI,EAEpBoB,EAAS,OAAO,EAGR,SAAA4D,GAAgBzD,EAAgB,OACvCvB,EAAcG,CAAY,EAAIiC,GAC9Bb,EACApB,EACAJ,CAAgB,EAAAC,GAAAiB,EAAA,GAAAd,CAAA,EAAAc,EAAA,EAAApB,CAAA,EAAAoB,EAAA,GAAAyC,CAAA,EAAAzC,EAAA,GAAAa,CAAA,EAAAb,EAAA,GAAAgD,CAAA,EAAAhD,EAAA,EAAA4C,CAAA,EAAA5C,EAAA,GAAAlB,CAAA,IAEbwB,EAAE,MAAQ,UACTpB,IAAiB,KACpByE,EAAoBzE,CAAY,EAE5B0D,IACHU,EAAoBzC,CAAU,EAC9Bb,EAAA,GAAAa,EAAa,EAAE,IAIdP,EAAE,MAAQ,aAAeO,IAAe,IAC3Cb,EAAA,GAAAf,MAAuBA,EAAiB,MAAM,IAAK,CAAA,CAAA,EAEhDA,EAAiB,SAAWuD,IAC/BxC,EAAA,GAAAjB,EAAe,EAAK,EACpBiB,EAAA,GAAAd,EAAe,IAAI,YAIZ8E,GAAoB,CACxB/C,IAAU,YACbhC,EAAgB,CAAA,CAAA,EACN,MAAM,QAAQgC,CAAK,GAC7BjB,EAAA,GAAAf,EAAmBgC,EACjB,IAAKwC,GAAC,CACA,MAAA1C,EAAQmC,EAAe,QAAQO,CAAC,EAClC,GAAA1C,cACIA,KAEJ6B,SACIa,CAKR,CAAA,EAAA,OAAQQ,GAAgCA,IAAQ,MAAS,CAAA,EAM7DC,GAAW,IAAA,CACVlE,EAAA,GAAAkB,EAAkB,EAAK,IAqBa,MAAAiD,GAAAC,GAAAb,EAAuBa,CAAC,OAC3BC,IAAK,CAC5BA,EAAM,MAAQ,SACjBd,EAAuBa,CAAC,iBAoBhBvD,EAAU,KAAA,yDACXkC,EAAY3C,YAEZ,MAAAkE,GAAAhE,GACVH,EAAS,SACR,CAAA,IAAKG,EAAE,IACP,YAAaO,CAAA,CAAA,KAeAwD,GAAK,CACbA,EAAM,MAAQ,SACjBT,EAAWS,CAAK,6eA9NvBpB,EAAgBrE,EAAQ,IAAK,GAAM,EAAE,CAAC,CAAA,CAAA,OACtCsE,EAAiBtE,EAAQ,IAAK,GAAM,EAAE,CAAC,CAAA,CAAA,6BAInCA,IAAY6D,GAAe5B,IAAemC,KAC7ChD,EAAA,GAAAlB,EAAmB8B,GAAchC,EAASiC,CAAU,CAAA,EACpDb,EAAA,GAAAyC,EAAc7D,CAAO,EACrBoB,EAAA,GAAAgD,EAAiBnC,CAAU,EACtB+B,QACJ1D,EAAeJ,EAAiB,CAAC,CAAA,6BAclC,KAAK,UAAUG,CAAgB,GAAK,KAAK,UAAUkE,CAAkB,SAErElC,EAAQhC,EAAiB,IAAK8B,GAAK,OAC3BA,GAAU,SAAWmC,EAAenC,CAAK,EAAIA,CAAK,CAAA,OAE1DoC,EAAqBlE,EAAiB,MAAK,CAAA,4BAbxC,KAAK,UAAUgC,CAAK,GAAK,KAAK,UAAUsB,CAAS,IACpDvB,GAAcb,EAAUc,EAAOC,CAAe,OAC9CqB,EAAY,MAAM,QAAQtB,CAAK,EAAIA,EAAM,QAAUA,CAAK,2BAsIhD+C,EAAoB,usBClNA,YAAAE,EAAa,EAAA,OAAgB,gEA0M1BtG,EAAK,CAAA,CAAA,yCAALA,EAAK,CAAA,CAAA,iJA4BlCP,GAEKC,EAAAqB,EAAAnB,CAAA,wPAHAI,EAAQ,CAAA,GAAAc,GAAA,+FAYG,iBAAAd,QAAmB,SAAaA,EAAc,EAAA,CAAA,uCAErDA,EAAsB,EAAA,CAAA,oNAjChBA,EAAY,EAAA,CAAA,mBACfA,EAAK,CAAA,CAAA,mGAgBNA,EAAU,CAAA,EAdLO,EAAAgE,EAAA,UAAA,CAAAvE,EAAc,EAAA,EAAA,SAASA,QACrCA,EAAkB,CAAA,CAAA,6MAbzBP,GA6CKC,EAAAiH,EAAA/G,CAAA,sBA1CJC,EAyCK8G,EAAAvC,CAAA,EAxCJvE,EA8BKuE,EAAAC,CAAA,EA7BJxE,EA4BKwE,EAAAC,CAAA,EA3BJzE,EAqBCyE,EAAAC,CAAA,OAXYvE,EAAU,CAAA,CAAA,oGAEVA,EAAe,EAAA,CAAA,kCAMlBA,EAAW,EAAA,CAAA,eACVA,EAAY,EAAA,CAAA,yKAhBPA,EAAY,EAAA,CAAA,iCACfA,EAAK,CAAA,CAAA,yDAgBNA,EAAU,CAAA,wCAVTA,EAAU,CAAA,QAAVA,EAAU,CAAA,CAAA,mBAJNO,EAAAgE,EAAA,UAAA,CAAAvE,EAAc,EAAA,EAAA,SAASA,QACrCA,EAAkB,CAAA,CAAA,EAefA,EAAQ,CAAA,4QAYG4D,EAAA,CAAA,EAAA,OAAAgD,EAAA,iBAAA5G,QAAmB,SAAaA,EAAc,EAAA,CAAA,6RA3OvD,MAAAyE,CAAa,EAAAxD,EACb,CAAA,KAAAyD,EAA2B,MAAS,EAAAzD,GACpC,MAAAoC,EAAK,EAAA,EAAApC,EACZ0D,EAAS,CAAA,EACF,CAAA,gBAAArB,EAAkB,EAAK,EAAArC,GACvB,QAAAD,CAAoC,EAAAC,EAC3C4D,EACO,CAAA,SAAAzD,EAAW,EAAK,EAAAH,GAChB,WAAA6D,CAAmB,EAAA7D,EACnB,CAAA,UAAA8D,EAAY,EAAI,EAAA9D,EAChB,CAAA,mBAAA+D,EAAqB,EAAK,EAAA/D,EAC1B,CAAA,WAAAgE,EAAa,EAAI,EAAAhE,EAExBkE,EAEAhE,EAAe,GACfkE,EACAC,EACArC,EAAa,GACbmC,EAAiB,GACjByB,EAAc,GAGd3F,EAAgB,CAAA,EAChBI,EAA8B,KAE9BwF,EAAgC,KAChCvB,EAEE,MAAAhD,EAAWxC,KAUbsD,GACHkC,EAAqBvE,EAAQ,IAAKwE,GAAMA,EAAE,CAAC,CAAG,EAAA,QAAQnC,CAAe,EACrEyD,EAAiBvB,EACbuB,QACHnC,EAAYtB,EACZyD,EAAiB,OAEhB,CAAA7D,EAAY0B,CAAS,EAAI3D,EAAQ8F,CAAc,EAChD1B,EAAiBnC,GAElB8D,KACU/F,EAAQ,OAAS,IAC3BuE,EAAqB,EACrBuB,EAAiB,EAChB,CAAA7D,EAAYI,CAAK,EAAIrC,EAAQ8F,CAAc,EAC5CnC,EAAYtB,EACZ+B,EAAiBnC,YA2BT+D,GAAuB,MAC/B3B,EAAgBrE,EAAQ,IAAKwE,GAAMA,EAAE,CAAC,CAAA,CAAA,OACtCF,EAAiBtE,EAAQ,IAAKwE,GAAMA,EAAE,CAAC,CAAA,CAAA,WA+B/BuB,GAAc,CACtBC,IACI3D,IAAU,QAAc,MAAM,QAAQA,CAAK,GAAKA,EAAM,SAAW,GACpEjB,EAAA,EAAAa,EAAa,EAAE,EACfb,EAAA,GAAA0E,EAAiB,IAAI,GACXxB,EAAe,SAASjC,CAAe,GACjDjB,EAAA,EAAAa,EAAaoC,EAAcC,EAAe,QAAQjC,CAAe,CAAA,CAAA,EACjEjB,EAAA,GAAA0E,EAAiBxB,EAAe,QAAQjC,CAAe,CAAA,GAC7C2B,GACV5C,EAAA,EAAAa,EAAaI,CAAe,EAC5BjB,EAAA,GAAA0E,EAAiB,IAAI,IAErB1E,EAAA,EAAAa,EAAa,EAAE,EACfb,EAAA,GAAA0E,EAAiB,IAAI,GAEtB1E,EAAA,GAAAmD,EAAqBuB,CAAc,EAG3B,SAAAhB,EAAuBpD,EAAM,CAEjC,QADJoE,EAAiB,SAASpE,EAAE,OAAO,OAAO,QAAQ,KAAK,CAAA,EACnD,MAAMoE,CAAc,EAAA,CAEvB1E,EAAA,GAAA0E,EAAiB,IAAI,SAGtB1E,EAAA,GAAAjB,EAAe,EAAK,EACpBiB,EAAA,GAAAd,EAAe,IAAI,EACnB6D,EAAa,KAAI,EAGT,SAAAc,EAAavD,EAAa,MAClCxB,EAAmBF,EAAQ,KAAKkF,GAAGjG,IAAMA,CAAC,CAAA,EAC1CmC,EAAA,GAAAjB,EAAe,EAAI,EACnBoB,EAAS,OAAO,WAGRkD,IAAW,CACdT,EAGJ5C,EAAA,GAAAiB,EAAQJ,CAAU,EAFlBb,EAAA,EAAAa,EAAaoC,EAAcC,EAAe,QAAQjC,CAAe,CAAA,CAAA,EAIlEjB,EAAA,GAAAjB,EAAe,EAAK,EACpBiB,EAAA,GAAAd,EAAe,IAAI,EACnBiB,EAAS,MAAM,EAGP,SAAA4D,EAAgBzD,EAAgB,OACvCvB,EAAcG,CAAY,EAAIiC,GAC9Bb,EACApB,EACAJ,CAAgB,EAAAC,GAAAiB,EAAA,GAAAd,CAAA,EAAAc,EAAA,EAAApB,CAAA,EAAAoB,EAAA,GAAAyC,CAAA,EAAAzC,EAAA,EAAA4C,CAAA,EAAA5C,EAAA,EAAAa,CAAA,EAAAb,EAAA,GAAAlB,CAAA,EAAAkB,EAAA,EAAA+C,CAAA,EAAA/C,EAAA,GAAAgD,CAAA,EAAAhD,EAAA,GAAA0E,CAAA,EAAA1E,EAAA,GAAAmD,CAAA,EAAAnD,EAAA,GAAAyE,CAAA,EAAAzE,EAAA,GAAAkD,CAAA,EAAA,EAEb5C,EAAE,MAAQ,UACTpB,IAAiB,MACpBc,EAAA,GAAA0E,EAAiBxF,CAAY,EAC7Bc,EAAA,GAAAjB,EAAe,EAAK,EACpBgE,EAAa,KAAI,EACjB/C,EAAA,GAAAd,EAAe,IAAI,GACT+D,EAAc,SAASpC,CAAU,GAC3Cb,EAAA,GAAA0E,EAAiBzB,EAAc,QAAQpC,CAAU,CAAA,EACjDb,EAAA,GAAAjB,EAAe,EAAK,EACpBiB,EAAA,GAAAd,EAAe,IAAI,EACnB6D,EAAa,KAAI,GACPH,IACV5C,EAAA,GAAAiB,EAAQJ,CAAU,EAClBb,EAAA,GAAA0E,EAAiB,IAAI,EACrB1E,EAAA,GAAAjB,EAAe,EAAK,EACpBiB,EAAA,GAAAd,EAAe,IAAI,EACnB6D,EAAa,KAAI,IAKpBmB,GAAW,IAAA,CACVlE,EAAA,GAAAkB,EAAkB,EAAK,EACvBlB,EAAA,GAAAyE,EAAc,EAAI,iBAoBH5D,EAAU,KAAA,+FACXkC,EAAY3C,WAEZ,MAAAkE,GAAAhE,GACVH,EAAS,SACR,CAAA,IAAKG,EAAE,IACP,YAAaO,CAAA,CAAA,4aA7JjB6D,IAAmBvB,GACnBuB,IAAmB,MACnBD,IAECzE,EAAA,EAAA,CAAAa,EAAYI,CAAK,EAAIrC,EAAQ8F,CAAc,EAAA7D,GAAAb,EAAA,GAAAiB,CAAA,EAAAjB,EAAA,GAAA0E,CAAA,EAAA1E,EAAA,GAAAmD,CAAA,EAAAnD,EAAA,GAAAyE,CAAA,EAAAzE,EAAA,EAAApB,CAAA,EAAAoB,EAAA,GAAAkD,CAAA,IAC5ClD,EAAA,GAAAmD,EAAqBuB,CAAc,EACnCvE,EAAS,SAAQ,CAChB,MAAOuE,EACP,MAAOxB,EAAewB,CAAc,EACpC,SAAU,6BAMRzD,GAASsB,IACZoC,IACA3D,GAAcb,EAAUc,EAAOC,CAAe,EAC9ClB,EAAA,GAAAuC,EAAYtB,CAAK,oBASP2D,EAAuB,yBAG9BhG,IAAY6D,IACVG,GACJ+B,IAED3E,EAAA,GAAAyC,EAAc7D,CAAO,EACrBoB,EAAA,GAAAlB,EAAmB8B,GAAchC,EAASiC,CAAU,CAAA,EAC/C,CAAA+B,GAAsB9D,EAAiB,OAAS,QACpDI,EAAeJ,EAAiB,CAAC,CAAA,EAE9BiE,GAAgB,SAAS,eAC5B/C,EAAA,GAAAjB,EAAe,EAAI,2BAMjB8B,IAAemC,IAClBhD,EAAA,GAAAlB,EAAmB8B,GAAchC,EAASiC,CAAU,CAAA,EACpDb,EAAA,GAAAgD,EAAiBnC,CAAU,EACtB,CAAA+B,GAAsB9D,EAAiB,OAAS,QACpDI,EAAeJ,EAAiB,CAAC,CAAA,i0BCvBvBlB,EAAW,EAAA,mlBAAXA,EAAW,EAAA,kZA1BhB,KAAAA,MAAO,eAOFA,EAAW,EAAA,wlBAPhB4D,EAAA,CAAA,EAAA,SAAAqD,EAAA,KAAAjH,MAAO,gCAOFA,EAAW,EAAA,6OAxBX,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,EAAA,qHAGdA,EAAW,CAAA,EAAA,mLALH,WAAAA,MAAO,YACb4D,EAAA,CAAA,EAAA,QAAA,CAAA,KAAA5D,MAAO,IAAI,iBACbA,EAAc,EAAA,CAAA,6XARVA,EAAS,EAAA,iBACF,mPADPA,EAAS,EAAA,+NAhCP,GAAA,CAAA,MAAAyE,EAAQ,UAAU,EAAAxD,EAClB,CAAA,KAAAyD,EAA2B,MAAS,EAAAzD,EACpC,CAAA,QAAAiG,EAAU,EAAE,EAAAjG,GACZ,aAAAkG,EAAY,EAAA,EAAAlG,EACZ,CAAA,QAAAmG,EAAU,EAAI,EAAAnG,EACd,CAAA,MAAAoC,EAAuC,MAAS,EAAApC,EAChD,CAAA,gBAAAqC,EAAkB,EAAK,EAAArC,EACvB,CAAA,YAAAoG,EAAc,EAAK,EAAApG,EACnB,CAAA,YAAA2D,EAA6B,IAAI,EAAA3D,GACjC,QAAAD,CAAoC,EAAAC,GACpC,WAAA6D,CAAmB,EAAA7D,GACnB,WAAAgE,CAAmB,EAAAhE,EACnB,CAAA,UAAA8D,EAAY,EAAI,EAAA9D,EAChB,CAAA,MAAAqG,EAAuB,IAAI,EAAArG,EAC3B,CAAA,UAAAsG,EAAgC,MAAS,EAAAtG,GACzC,eAAAuG,CAA6B,EAAAvG,EAC7B,CAAA,mBAAA+D,EAAqB,EAAK,EAAA/D,GAC1B,OAAAwG,CAOT,EAAAxG,GACS,YAAAyG,CAAoB,EAAAzG,gEA+BZwG,EAAO,SAAS,QAAQ,QACzBA,EAAO,SAAS,OAAO,IAC3B/E,GAAM+E,EAAO,SAAS,SAAU/E,EAAE,MAAM,QACrC+E,EAAO,SAAS,MAAM,QACrBA,EAAO,SAAS,OAAO,QACtBA,EAAO,SAAS,QAAQ,iEAcxBA,EAAO,SAAS,QAAQ,QACzBA,EAAO,SAAS,OAAO,KAC3B/E,GAAM+E,EAAO,SAAS,SAAU/E,EAAE,MAAM,SACrC+E,EAAO,SAAS,MAAM,QACrBA,EAAO,SAAS,OAAO,KAC3B/E,GAAM+E,EAAO,SAAS,SAAU/E,EAAE,MAAM"}