import{M as o}from"./Example.svelte_svelte_type_style_lang-648fc18a.js";import"./Index-26cfc80a.js";import"./index-a80d931b.js";import"./svelte/svelte.js";import"./prism-python-b0b31d02.js";const{SvelteComponent:u,attr:d,create_component:c,destroy_component:g,detach:b,element:h,init:k,insert:w,mount_component:z,safe_not_equal:v,toggle_class:_,transition_in:y,transition_out:q}=window.__gradio__svelte__internal;function C(a){let e,l,s;return l=new o({props:{message:a[0]?a[0]:"",latex_delimiters:a[5],sanitize_html:a[3],line_breaks:a[4],chatbot:!1}}),{c(){e=h("div"),c(l.$$.fragment),d(e,"class","prose svelte-1ayixqk"),_(e,"table",a[1]==="table"),_(e,"gallery",a[1]==="gallery"),_(e,"selected",a[2])},m(t,i){w(t,e,i),z(l,e,null),s=!0},p(t,[i]){const m={};i&1&&(m.message=t[0]?t[0]:""),i&32&&(m.latex_delimiters=t[5]),i&8&&(m.sanitize_html=t[3]),i&16&&(m.line_breaks=t[4]),l.$set(m),(!s||i&2)&&_(e,"table",t[1]==="table"),(!s||i&2)&&_(e,"gallery",t[1]==="gallery"),(!s||i&4)&&_(e,"selected",t[2])},i(t){s||(y(l.$$.fragment,t),s=!0)},o(t){q(l.$$.fragment,t),s=!1},d(t){t&&b(e),g(l)}}}function M(a,e,l){let{value:s}=e,{type:t}=e,{selected:i=!1}=e,{sanitize_html:m}=e,{line_breaks:r}=e,{latex_delimiters:f}=e;return a.$$set=n=>{"value"in n&&l(0,s=n.value),"type"in n&&l(1,t=n.type),"selected"in n&&l(2,i=n.selected),"sanitize_html"in n&&l(3,m=n.sanitize_html),"line_breaks"in n&&l(4,r=n.line_breaks),"latex_delimiters"in n&&l(5,f=n.latex_delimiters)},[s,t,i,m,r,f]}class D extends u{constructor(e){super(),k(this,e,M,C,v,{value:0,type:1,selected:2,sanitize_html:3,line_breaks:4,latex_delimiters:5})}}export{D as default};
//# sourceMappingURL=Example-49f98efe.js.map
