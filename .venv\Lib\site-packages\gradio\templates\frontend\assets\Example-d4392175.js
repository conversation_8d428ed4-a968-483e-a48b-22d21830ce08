const{SvelteComponent:y,attr:d,detach:o,element:g,init:m,insert:v,noop:f,safe_not_equal:h,toggle_class:i}=window.__gradio__svelte__internal;function b(n){let e;return{c(){e=g("div"),e.textContent=`${n[2]}`,d(e,"class","svelte-1ayixqk"),i(e,"table",n[0]==="table"),i(e,"gallery",n[0]==="gallery"),i(e,"selected",n[1])},m(t,l){v(t,e,l)},p(t,[l]){l&1&&i(e,"table",t[0]==="table"),l&1&&i(e,"gallery",t[0]==="gallery"),l&2&&i(e,"selected",t[1])},i:f,o:f,d(t){t&&o(e)}}}function q(n,e,t){let{value:l}=e,{type:c}=e,{selected:_=!1}=e,{choices:s}=e,r=(l?Array.isArray(l)?l:[l]:[]).map(a=>s.find(u=>u[1]===a)?.[0]).filter(a=>a!==void 0).join(", ");return n.$$set=a=>{"value"in a&&t(3,l=a.value),"type"in a&&t(0,c=a.type),"selected"in a&&t(1,_=a.selected),"choices"in a&&t(4,s=a.choices)},[c,_,r,l,s]}class C extends y{constructor(e){super(),m(this,e,q,b,h,{value:3,type:0,selected:1,choices:4})}}export{C as default};
//# sourceMappingURL=Example-d4392175.js.map
