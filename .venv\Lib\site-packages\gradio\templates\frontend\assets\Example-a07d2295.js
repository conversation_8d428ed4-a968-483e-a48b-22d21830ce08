const{SvelteComponent:f,append:c,attr:o,detach:d,element:g,init:r,insert:v,noop:u,safe_not_equal:y,set_data:m,text:b,toggle_class:_}=window.__gradio__svelte__internal;function S(a){let e,n=(a[0]!==null?a[0].toLocaleString():"")+"",s;return{c(){e=g("div"),s=b(n),o(e,"class","svelte-1ayixqk"),_(e,"table",a[1]==="table"),_(e,"gallery",a[1]==="gallery"),_(e,"selected",a[2])},m(l,t){v(l,e,t),c(e,s)},p(l,[t]){t&1&&n!==(n=(l[0]!==null?l[0].toLocaleString():"")+"")&&m(s,n),t&2&&_(e,"table",l[1]==="table"),t&2&&_(e,"gallery",l[1]==="gallery"),t&4&&_(e,"selected",l[2])},i:u,o:u,d(l){l&&d(e)}}}function h(a,e,n){let{value:s}=e,{type:l}=e,{selected:t=!1}=e;return a.$$set=i=>{"value"in i&&n(0,s=i.value),"type"in i&&n(1,l=i.type),"selected"in i&&n(2,t=i.selected)},[s,l,t]}class q extends f{constructor(e){super(),r(this,e,h,S,y,{value:0,type:1,selected:2})}}export{q as default};
//# sourceMappingURL=Example-a07d2295.js.map
