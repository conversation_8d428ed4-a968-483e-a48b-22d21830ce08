const{SvelteComponent:c,append:a,attr:t,detach:h,init:u,insert:x,noop:p,safe_not_equal:g,set_style:n,svg_element:i}=window.__gradio__svelte__internal;function v(d){let e,o,l,r;return{c(){e=i("svg"),o=i("g"),l=i("path"),r=i("path"),t(l,"d","M18,6L6.087,17.913"),n(l,"fill","none"),n(l,"fill-rule","nonzero"),n(l,"stroke-width","2px"),t(o,"transform","matrix(1.14096,-0.140958,-0.140958,1.14096,-0.0559523,0.0559523)"),t(r,"d","M4.364,4.364L19.636,19.636"),n(r,"fill","none"),n(r,"fill-rule","nonzero"),n(r,"stroke-width","2px"),t(e,"width","100%"),t(e,"height","100%"),t(e,"viewBox","0 0 24 24"),t(e,"version","1.1"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),t(e,"xml:space","preserve"),t(e,"stroke","currentColor"),n(e,"fill-rule","evenodd"),n(e,"clip-rule","evenodd"),n(e,"stroke-linecap","round"),n(e,"stroke-linejoin","round")},m(s,w){x(s,e,w),a(e,o),a(o,l),a(e,r)},p,i:p,o:p,d(s){s&&h(e)}}}class _ extends c{constructor(e){super(),u(this,e,null,v,g,{})}}export{_ as C};
//# sourceMappingURL=Clear-2c7bae91.js.map
