import{b as ei,L as ul,d as cl,$ as vn,w as St,e as ti}from"./Index-26cfc80a.js";import{_ as P}from"./index-a80d931b.js";import{d as ni,f as Fn,a as oi,B as fl}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";function li(o,{from:e,to:t},n={}){const l=getComputedStyle(o),i=l.transform==="none"?"":l.transform,[r,a]=l.transformOrigin.split(" ").map(parseFloat),_=e.left+e.width*r/t.width-(t.left+r),s=e.top+e.height*a/t.height-(t.top+a),{delay:u=0,duration:f=m=>Math.sqrt(m)*120,easing:d=ni}=n;return{delay:u,duration:ei(f)?f(Math.sqrt(_*_+s*s)):f,easing:d,css:(m,k)=>{const g=k*_,p=k*s,c=m+k*e.width/t.width,E=m+k*e.height/t.height;return`transform: ${i} translate(${g}px, ${p}px) scale(${c}, ${E});`}}}const{SvelteComponent:ii,append:ri,attr:he,detach:si,init:ai,insert:_i,noop:Gt,safe_not_equal:ui,svg_element:Hn}=window.__gradio__svelte__internal;function ci(o){let e,t;return{c(){e=Hn("svg"),t=Hn("path"),he(t,"stroke-linecap","round"),he(t,"stroke-linejoin","round"),he(t,"d","M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z"),he(e,"fill","none"),he(e,"stroke","currentColor"),he(e,"viewBox","0 0 24 24"),he(e,"width","100%"),he(e,"height","100%"),he(e,"xmlns","http://www.w3.org/2000/svg"),he(e,"aria-hidden","true"),he(e,"stroke-width","2"),he(e,"stroke-linecap","round"),he(e,"stroke-linejoin","round")},m(n,l){_i(n,e,l),ri(e,t)},p:Gt,i:Gt,o:Gt,d(n){n&&si(e)}}}let fi=class extends ii{constructor(e){super(),ai(this,e,null,ci,ui,{})}};const{SvelteComponent:pi,append:mi,attr:ve,detach:di,init:gi,insert:hi,noop:Jt,safe_not_equal:vi,svg_element:Wn}=window.__gradio__svelte__internal;function bi(o){let e,t;return{c(){e=Wn("svg"),t=Wn("path"),ve(t,"stroke-linecap","round"),ve(t,"stroke-linejoin","round"),ve(t,"d","M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"),ve(e,"fill","none"),ve(e,"stroke","currentColor"),ve(e,"viewBox","0 0 24 24"),ve(e,"width","100%"),ve(e,"height","100%"),ve(e,"xmlns","http://www.w3.org/2000/svg"),ve(e,"aria-hidden","true"),ve(e,"stroke-width","2"),ve(e,"stroke-linecap","round"),ve(e,"stroke-linejoin","round")},m(n,l){hi(n,e,l),mi(e,t)},p:Jt,i:Jt,o:Jt,d(n){n&&di(e)}}}class ki extends pi{constructor(e){super(),gi(this,e,null,bi,vi,{})}}const{SvelteComponent:wi,append:yi,attr:be,detach:Ei,init:Ai,insert:Li,noop:Qt,safe_not_equal:Pi,svg_element:Gn}=window.__gradio__svelte__internal;function Oi(o){let e,t;return{c(){e=Gn("svg"),t=Gn("path"),be(t,"stroke-linecap","round"),be(t,"stroke-linejoin","round"),be(t,"d","M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"),be(e,"fill","none"),be(e,"stroke","currentColor"),be(e,"stroke-width","2"),be(e,"viewBox","0 0 24 24"),be(e,"width","100%"),be(e,"height","100%"),be(e,"xmlns","http://www.w3.org/2000/svg"),be(e,"aria-hidden","true"),be(e,"stroke-linecap","round"),be(e,"stroke-linejoin","round")},m(n,l){Li(n,e,l),yi(e,t)},p:Qt,i:Qt,o:Qt,d(n){n&&Ei(e)}}}class Ti extends wi{constructor(e){super(),Ai(this,e,null,Oi,Pi,{})}}const{SvelteComponent:qi,add_render_callback:Ii,append:ke,attr:x,bubble:Jn,check_outros:Ci,create_component:bn,create_in_transition:Di,create_out_transition:$i,destroy_component:kn,detach:Ri,element:Fe,group_outros:Si,init:Vi,insert:ji,listen:Kt,mount_component:wn,run_all:Ni,safe_not_equal:Mi,set_data:Qn,space:It,stop_propagation:Kn,text:Zn,transition_in:kt,transition_out:wt}=window.__gradio__svelte__internal,{createEventDispatcher:zi,onMount:Bi}=window.__gradio__svelte__internal;function Ui(o){let e,t;return e=new fi({}),{c(){bn(e.$$.fragment)},m(n,l){wn(e,n,l),t=!0},i(n){t||(kt(e.$$.fragment,n),t=!0)},o(n){wt(e.$$.fragment,n),t=!1},d(n){kn(e,n)}}}function Fi(o){let e,t;return e=new ki({}),{c(){bn(e.$$.fragment)},m(n,l){wn(e,n,l),t=!0},i(n){t||(kt(e.$$.fragment,n),t=!0)},o(n){wt(e.$$.fragment,n),t=!1},d(n){kn(e,n)}}}function Hi(o){let e,t;return e=new Ti({}),{c(){bn(e.$$.fragment)},m(n,l){wn(e,n,l),t=!0},i(n){t||(kt(e.$$.fragment,n),t=!0)},o(n){wt(e.$$.fragment,n),t=!1},d(n){kn(e,n)}}}function Wi(o){let e,t,n,l,i,r,a,_,s,u,f,d,m,k,g,p,c,E,h,b,w,L,v,I,R,A,T,B;const ne=[Hi,Fi,Ui],V=[];function S(q,M){return q[1]==="warning"?0:q[1]==="info"?1:q[1]==="error"?2:-1}return~(n=S(o))&&(l=V[n]=ne[n](o)),{c(){e=Fe("div"),t=Fe("div"),l&&l.c(),r=It(),a=Fe("div"),_=Fe("div"),s=Zn(o[1]),f=It(),d=Fe("div"),m=Zn(o[0]),p=It(),c=Fe("button"),E=Fe("span"),E.textContent="×",b=It(),w=Fe("div"),x(t,"class",i="toast-icon "+o[1]+" svelte-z3l7qj"),x(_,"class",u="toast-title "+o[1]+" svelte-z3l7qj"),x(d,"class",k="toast-text "+o[1]+" svelte-z3l7qj"),x(a,"class",g="toast-details "+o[1]+" svelte-z3l7qj"),x(E,"aria-hidden","true"),x(c,"class",h="toast-close "+o[1]+" svelte-z3l7qj"),x(c,"type","button"),x(c,"aria-label","Close"),x(c,"data-testid","toast-close"),x(w,"class",L="timer "+o[1]+" svelte-z3l7qj"),x(e,"class",v="toast-body "+o[1]+" svelte-z3l7qj"),x(e,"role","alert"),x(e,"data-testid","toast-body")},m(q,M){ji(q,e,M),ke(e,t),~n&&V[n].m(t,null),ke(e,r),ke(e,a),ke(a,_),ke(_,s),ke(a,f),ke(a,d),ke(d,m),ke(e,p),ke(e,c),ke(c,E),ke(e,b),ke(e,w),A=!0,T||(B=[Kt(c,"click",o[2]),Kt(e,"click",Kn(o[4])),Kt(e,"keydown",Kn(o[5]))],T=!0)},p(q,[M]){let J=n;n=S(q),n!==J&&(l&&(Si(),wt(V[J],1,1,()=>{V[J]=null}),Ci()),~n?(l=V[n],l||(l=V[n]=ne[n](q),l.c()),kt(l,1),l.m(t,null)):l=null),(!A||M&2&&i!==(i="toast-icon "+q[1]+" svelte-z3l7qj"))&&x(t,"class",i),(!A||M&2)&&Qn(s,q[1]),(!A||M&2&&u!==(u="toast-title "+q[1]+" svelte-z3l7qj"))&&x(_,"class",u),(!A||M&1)&&Qn(m,q[0]),(!A||M&2&&k!==(k="toast-text "+q[1]+" svelte-z3l7qj"))&&x(d,"class",k),(!A||M&2&&g!==(g="toast-details "+q[1]+" svelte-z3l7qj"))&&x(a,"class",g),(!A||M&2&&h!==(h="toast-close "+q[1]+" svelte-z3l7qj"))&&x(c,"class",h),(!A||M&2&&L!==(L="timer "+q[1]+" svelte-z3l7qj"))&&x(w,"class",L),(!A||M&2&&v!==(v="toast-body "+q[1]+" svelte-z3l7qj"))&&x(e,"class",v)},i(q){A||(kt(l),q&&Ii(()=>{A&&(R&&R.end(1),I=Di(e,Fn,{duration:200,delay:100}),I.start())}),A=!0)},o(q){wt(l),I&&I.invalidate(),q&&(R=$i(e,Fn,{duration:200})),A=!1},d(q){q&&Ri(e),~n&&V[n].d(),q&&R&&R.end(),T=!1,Ni(B)}}}function Gi(o,e,t){let{message:n=""}=e,{type:l}=e,{id:i}=e;const r=zi();function a(){r("close",i)}Bi(()=>{setTimeout(()=>{a()},1e4)});function _(u){Jn.call(this,o,u)}function s(u){Jn.call(this,o,u)}return o.$$set=u=>{"message"in u&&t(0,n=u.message),"type"in u&&t(1,l=u.type),"id"in u&&t(3,i=u.id)},[n,l,a,i,_,s]}class Ji extends qi{constructor(e){super(),Vi(this,e,Gi,Wi,Mi,{message:0,type:1,id:3})}}const{SvelteComponent:Qi,append:Ki,attr:Zi,bubble:Yi,check_outros:Xi,create_animation:xi,create_component:er,destroy_component:tr,detach:pl,element:ml,ensure_array_like:Yn,fix_and_outro_and_destroy_block:nr,fix_position:or,group_outros:lr,init:ir,insert:dl,mount_component:rr,noop:sr,safe_not_equal:ar,set_style:_r,space:ur,transition_in:gl,transition_out:hl,update_keyed_each:cr}=window.__gradio__svelte__internal;function Xn(o,e,t){const n=o.slice();return n[2]=e[t].type,n[3]=e[t].message,n[4]=e[t].id,n}function xn(o,e){let t,n,l,i,r=sr,a;return n=new Ji({props:{type:e[2],message:e[3],id:e[4]}}),n.$on("close",e[1]),{key:o,first:null,c(){t=ml("div"),er(n.$$.fragment),l=ur(),_r(t,"width","100%"),this.first=t},m(_,s){dl(_,t,s),rr(n,t,null),Ki(t,l),a=!0},p(_,s){e=_;const u={};s&1&&(u.type=e[2]),s&1&&(u.message=e[3]),s&1&&(u.id=e[4]),n.$set(u)},r(){i=t.getBoundingClientRect()},f(){or(t),r()},a(){r(),r=xi(t,i,li,{duration:300})},i(_){a||(gl(n.$$.fragment,_),a=!0)},o(_){hl(n.$$.fragment,_),a=!1},d(_){_&&pl(t),tr(n)}}}function fr(o){let e,t=[],n=new Map,l,i=Yn(o[0]);const r=a=>a[4];for(let a=0;a<i.length;a+=1){let _=Xn(o,i,a),s=r(_);n.set(s,t[a]=xn(s,_))}return{c(){e=ml("div");for(let a=0;a<t.length;a+=1)t[a].c();Zi(e,"class","toast-wrap svelte-pu0yf1")},m(a,_){dl(a,e,_);for(let s=0;s<t.length;s+=1)t[s]&&t[s].m(e,null);l=!0},p(a,[_]){if(_&1){i=Yn(a[0]),lr();for(let s=0;s<t.length;s+=1)t[s].r();t=cr(t,_,r,1,a,i,n,e,nr,xn,null,Xn);for(let s=0;s<t.length;s+=1)t[s].a();Xi()}},i(a){if(!l){for(let _=0;_<i.length;_+=1)gl(t[_]);l=!0}},o(a){for(let _=0;_<t.length;_+=1)hl(t[_]);l=!1},d(a){a&&pl(e);for(let _=0;_<t.length;_+=1)t[_].d()}}}function pr(o){o.length>0&&"parentIFrame"in window&&window.parentIFrame?.scrollTo(0,0)}function mr(o,e,t){let{messages:n=[]}=e;function l(i){Yi.call(this,o,i)}return o.$$set=i=>{"messages"in i&&t(0,n=i.messages)},o.$$.update=()=>{o.$$.dirty&1&&pr(n)},[n,l]}class dr extends Qi{constructor(e){super(),ir(this,e,mr,fr,ar,{messages:0})}}const{SvelteComponent:gr,append:Zt,attr:je,detach:hr,init:vr,insert:br,noop:Yt,safe_not_equal:kr,set_style:gt,svg_element:Ct}=window.__gradio__svelte__internal;function wr(o){let e,t,n,l;return{c(){e=Ct("svg"),t=Ct("g"),n=Ct("path"),l=Ct("path"),je(n,"d","M3.789,0.09C3.903,-0.024 4.088,-0.024 4.202,0.09L4.817,0.705C4.931,0.819 4.931,1.004 4.817,1.118L1.118,4.817C1.004,4.931 0.819,4.931 0.705,4.817L0.09,4.202C-0.024,4.088 -0.024,3.903 0.09,3.789L3.789,0.09Z"),je(l,"d","M4.825,3.797C4.934,3.907 4.934,4.084 4.825,4.193L4.193,4.825C4.084,4.934 3.907,4.934 3.797,4.825L0.082,1.11C-0.027,1.001 -0.027,0.823 0.082,0.714L0.714,0.082C0.823,-0.027 1.001,-0.027 1.11,0.082L4.825,3.797Z"),je(e,"width","100%"),je(e,"height","100%"),je(e,"viewBox","0 0 5 5"),je(e,"version","1.1"),je(e,"xmlns","http://www.w3.org/2000/svg"),je(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),je(e,"xml:space","preserve"),gt(e,"fill","currentColor"),gt(e,"fill-rule","evenodd"),gt(e,"clip-rule","evenodd"),gt(e,"stroke-linejoin","round"),gt(e,"stroke-miterlimit","2")},m(i,r){br(i,e,r),Zt(e,t),Zt(t,n),Zt(t,l)},p:Yt,i:Yt,o:Yt,d(i){i&&hr(e)}}}class vl extends gr{constructor(e){super(),vr(this,e,null,wr,kr,{})}}const{SvelteComponent:yr,append:He,attr:Dt,create_component:Er,destroy_component:Ar,detach:Xt,element:ot,init:Lr,insert:xt,listen:Pr,mount_component:Or,safe_not_equal:Tr,set_data:qr,space:en,text:eo,transition_in:Ir,transition_out:Cr}=window.__gradio__svelte__internal,{createEventDispatcher:Dr}=window.__gradio__svelte__internal;function $r(o){let e,t,n,l,i,r,a,_,s,u,f,d,m,k,g;return d=new vl({}),{c(){e=ot("div"),t=ot("h1"),t.textContent="API Docs",n=en(),l=ot("p"),i=eo(`No API Routes found for
		`),r=ot("code"),a=eo(o[0]),_=en(),s=ot("p"),s.innerHTML=`To expose an API endpoint of your app in this page, set the <code>api_name</code>
		parameter of the event listener.
		<br/>
		For more information, visit the
		<a href="https://gradio.app/sharing_your_app/#api-page" target="_blank">API Page guide</a>
		. To hide the API documentation button and this page, set
		<code>show_api=False</code>
		in the
		<code>Blocks.launch()</code>
		method.`,u=en(),f=ot("button"),Er(d.$$.fragment),Dt(r,"class","svelte-e1ha0f"),Dt(l,"class","attention svelte-e1ha0f"),Dt(e,"class","wrap prose svelte-e1ha0f"),Dt(f,"class","svelte-e1ha0f")},m(p,c){xt(p,e,c),He(e,t),He(e,n),He(e,l),He(l,i),He(l,r),He(r,a),He(e,_),He(e,s),xt(p,u,c),xt(p,f,c),Or(d,f,null),m=!0,k||(g=Pr(f,"click",o[2]),k=!0)},p(p,[c]){(!m||c&1)&&qr(a,p[0])},i(p){m||(Ir(d.$$.fragment,p),m=!0)},o(p){Cr(d.$$.fragment,p),m=!1},d(p){p&&(Xt(e),Xt(u),Xt(f)),Ar(d),k=!1,g()}}}function Rr(o,e,t){const n=Dr();let{root:l}=e;const i=()=>n("close");return o.$$set=r=>{"root"in r&&t(0,l=r.root)},[l,n,i]}class Sr extends yr{constructor(e){super(),Lr(this,e,Rr,$r,Tr,{root:0})}}function ft(o,e,t=null){return e===void 0?t==="py"?"None":null:o===null&&t==="py"?"None":e==="string"||e==="str"?t===null?o:'"'+o+'"':e==="number"?t===null?parseFloat(o):o:e==="boolean"||e=="bool"?t==="py"?(o=String(o),o==="true"?"True":"False"):t==="js"?o:o==="true":e==="List[str]"?(o=JSON.stringify(o),o):e.startsWith("Literal['")?'"'+o+'"':t===null?o===""?null:JSON.parse(o):typeof o=="string"?o===""?t==="py"?"None":"null":o:(t==="py"&&(o=an(o)),Vr(o))}function bl(o){if(typeof o=="object"&&o!==null&&o.hasOwnProperty("url")&&o.hasOwnProperty("meta")&&typeof o.meta=="object"&&o.meta!==null&&o.meta._type==="gradio.FileData")return!0;if(typeof o=="object"&&o!==null){for(let e in o)if(typeof o[e]=="object"&&bl(o[e]))return!0}return!1}function an(o){return typeof o=="object"&&o!==null&&!Array.isArray(o)&&"url"in o&&"meta"in o&&o.meta?._type==="gradio.FileData"?`file('${o.url}')`:(Array.isArray(o)?o.forEach((e,t)=>{typeof e=="object"&&e!==null&&(o[t]=an(e))}):typeof o=="object"&&o!==null&&Object.keys(o).forEach(e=>{o[e]=an(o[e])}),o)}function Vr(o){const e=JSON.stringify(o,(n,l)=>typeof l=="string"&&l.startsWith("file(")&&l.endsWith(")")?`UNQUOTED${l}`:l),t=/"UNQUOTEDfile\(([^)]*)\)"/g;return e.replace(t,(n,l)=>`file(${l})`)}const kl=""+new URL("api-logo-5346f193.svg",import.meta.url).href;const{SvelteComponent:jr,append:Ce,attr:We,create_component:Nr,destroy_component:Mr,detach:Vt,element:Qe,init:zr,insert:jt,listen:Br,mount_component:Ur,safe_not_equal:Fr,set_data:to,space:tn,src_url_equal:Hr,text:vt,transition_in:Wr,transition_out:Gr}=window.__gradio__svelte__internal,{createEventDispatcher:Jr}=window.__gradio__svelte__internal;function no(o){let e;return{c(){e=vt("s")},m(t,n){jt(t,e,n)},d(t){t&&Vt(e)}}}function Qr(o){let e,t,n,l,i,r,a,_,s,u,f,d,m,k,g,p,c,E,h,b=o[1]>1&&no();return p=new vl({}),{c(){e=Qe("h2"),t=Qe("img"),l=tn(),i=Qe("div"),r=vt(`API documentation
		`),a=Qe("div"),_=vt(o[0]),s=tn(),u=Qe("span"),f=Qe("span"),d=vt(o[1]),m=vt(" API endpoint"),b&&b.c(),k=tn(),g=Qe("button"),Nr(p.$$.fragment),Hr(t.src,n=kl)||We(t,"src",n),We(t,"alt",""),We(t,"class","svelte-3n2nxs"),We(a,"class","url svelte-3n2nxs"),We(f,"class","url svelte-3n2nxs"),We(u,"class","counts svelte-3n2nxs"),We(e,"class","svelte-3n2nxs"),We(g,"class","svelte-3n2nxs")},m(w,L){jt(w,e,L),Ce(e,t),Ce(e,l),Ce(e,i),Ce(i,r),Ce(i,a),Ce(a,_),Ce(e,s),Ce(e,u),Ce(u,f),Ce(f,d),Ce(u,m),b&&b.m(u,null),jt(w,k,L),jt(w,g,L),Ur(p,g,null),c=!0,E||(h=Br(g,"click",o[3]),E=!0)},p(w,[L]){(!c||L&1)&&to(_,w[0]),(!c||L&2)&&to(d,w[1]),w[1]>1?b||(b=no(),b.c(),b.m(u,null)):b&&(b.d(1),b=null)},i(w){c||(Wr(p.$$.fragment,w),c=!0)},o(w){Gr(p.$$.fragment,w),c=!1},d(w){w&&(Vt(e),Vt(k),Vt(g)),b&&b.d(),Mr(p),E=!1,h()}}}function Kr(o,e,t){let{root:n}=e,{api_count:l}=e;const i=Jr(),r=()=>i("close");return o.$$set=a=>{"root"in a&&t(0,n=a.root),"api_count"in a&&t(1,l=a.api_count)},[n,l,i,r]}class Zr extends jr{constructor(e){super(),zr(this,e,Kr,Qr,Fr,{root:0,api_count:1})}}const{SvelteComponent:Yr,append:ee,attr:Je,check_outros:Xr,create_component:xr,destroy_component:es,destroy_each:ts,detach:ie,element:Ee,empty:wl,ensure_array_like:oo,group_outros:ns,init:os,insert:re,mount_component:ls,noop:is,safe_not_equal:rs,set_data:Ye,set_style:Ke,space:Ze,text:me,toggle_class:lo,transition_in:Nt,transition_out:_n}=window.__gradio__svelte__internal;function io(o,e,t){const n=o.slice();return n[4]=e[t].label,n[5]=e[t].python_type,n[6]=e[t].component,n[7]=e[t].parameter_name,n[8]=e[t].parameter_has_default,n[9]=e[t].parameter_default,n[11]=t,n}function ro(o){let e;return{c(){e=me("s")},m(t,n){re(t,e,n)},d(t){t&&ie(e)}}}function ss(o){let e=o[2][o[11]].type+"",t;return{c(){t=me(e)},m(n,l){re(n,t,l)},p(n,l){l&4&&e!==(e=n[2][n[11]].type+"")&&Ye(t,e)},d(n){n&&ie(t)}}}function as(o){let e=o[5].type+"",t,n,l=o[8]&&o[9]===null&&so();return{c(){t=me(e),l&&l.c(),n=wl()},m(i,r){re(i,t,r),l&&l.m(i,r),re(i,n,r)},p(i,r){r&2&&e!==(e=i[5].type+"")&&Ye(t,e),i[8]&&i[9]===null?l||(l=so(),l.c(),l.m(n.parentNode,n)):l&&(l.d(1),l=null)},d(i){i&&(ie(t),ie(n)),l&&l.d(i)}}}function so(o){let e;return{c(){e=me(` |
							None`)},m(t,n){re(t,e,n)},d(t){t&&ie(e)}}}function _s(o){let e,t,n=ft(o[9],o[5].type,"py")+"",l;return{c(){e=Ee("span"),e.textContent="Default: ",t=Ee("span"),l=me(n),Je(t,"class","code svelte-1yt946s"),Ke(t,"font-size","var(--text-sm)")},m(i,r){re(i,e,r),re(i,t,r),ee(t,l)},p(i,r){r&2&&n!==(n=ft(i[9],i[5].type,"py")+"")&&Ye(l,n)},d(i){i&&(ie(e),ie(t))}}}function us(o){let e;return{c(){e=Ee("span"),e.textContent="Required",Ke(e,"font-weight","bold")},m(t,n){re(t,e,n)},p:is,d(t){t&&ie(e)}}}function ao(o){let e,t,n,l,i,r=(o[7]&&o[3]=="python"?o[7]:"["+o[11]+"]")+"",a,_,s,u,f,d,m,k=o[4]+"",g,p,c=o[6]+"",E,h,b;function w(T,B){return T[3]==="python"?as:ss}let L=w(o),v=L(o);function I(T,B){return!T[8]||T[3]=="javascript"?us:_s}let R=I(o),A=R(o);return{c(){e=Ee("hr"),t=Ze(),n=Ee("div"),l=Ee("p"),i=Ee("span"),a=me(r),_=Ze(),s=Ee("span"),v.c(),u=Ze(),A.c(),f=Ze(),d=Ee("p"),m=me('The input value that is provided in the "'),g=me(k),p=me('" '),E=me(c),h=me(`
				component.`),b=Ze(),Je(e,"class","hr svelte-1yt946s"),Je(i,"class","code svelte-1yt946s"),Ke(i,"margin-right","10px"),Je(s,"class","code highlight svelte-1yt946s"),Ke(s,"margin-right","10px"),Ke(l,"white-space","nowrap"),Ke(l,"overflow-x","auto"),Je(d,"class","desc svelte-1yt946s"),Ke(n,"margin","10px")},m(T,B){re(T,e,B),re(T,t,B),re(T,n,B),ee(n,l),ee(l,i),ee(i,a),ee(l,_),ee(l,s),v.m(s,null),ee(l,u),A.m(l,null),ee(n,f),ee(n,d),ee(d,m),ee(d,g),ee(d,p),ee(d,E),ee(d,h),ee(n,b)},p(T,B){B&10&&r!==(r=(T[7]&&T[3]=="python"?T[7]:"["+T[11]+"]")+"")&&Ye(a,r),L===(L=w(T))&&v?v.p(T,B):(v.d(1),v=L(T),v&&(v.c(),v.m(s,null))),R===(R=I(T))&&A?A.p(T,B):(A.d(1),A=R(T),A&&(A.c(),A.m(l,null))),B&2&&k!==(k=T[4]+"")&&Ye(g,k),B&2&&c!==(c=T[6]+"")&&Ye(E,c)},d(T){T&&(ie(e),ie(t),ie(n)),v.d(),A.d()}}}function _o(o){let e,t,n;return t=new ul({props:{margin:!1}}),{c(){e=Ee("div"),xr(t.$$.fragment),Je(e,"class","load-wrap")},m(l,i){re(l,e,i),ls(t,e,null),n=!0},i(l){n||(Nt(t.$$.fragment,l),n=!0)},o(l){_n(t.$$.fragment,l),n=!1},d(l){l&&ie(e),es(t)}}}function cs(o){let e,t,n,l=o[1].length+"",i,r,a,_,s,u,f,d,m=o[1].length!=1&&ro(),k=oo(o[1]),g=[];for(let c=0;c<k.length;c+=1)g[c]=ao(io(o,k,c));let p=o[0]&&_o();return{c(){e=Ee("h4"),t=Ee("div"),t.innerHTML='<div class="toggle-dot svelte-1yt946s"></div>',n=me(`
	Accepts `),i=me(l),r=me(" parameter"),m&&m.c(),a=me(":"),_=Ze(),s=Ee("div");for(let c=0;c<g.length;c+=1)g[c].c();u=Ze(),p&&p.c(),f=wl(),Je(t,"class","toggle-icon svelte-1yt946s"),Je(e,"class","svelte-1yt946s"),lo(s,"hide",o[0])},m(c,E){re(c,e,E),ee(e,t),ee(e,n),ee(e,i),ee(e,r),m&&m.m(e,null),ee(e,a),re(c,_,E),re(c,s,E);for(let h=0;h<g.length;h+=1)g[h]&&g[h].m(s,null);re(c,u,E),p&&p.m(c,E),re(c,f,E),d=!0},p(c,[E]){if((!d||E&2)&&l!==(l=c[1].length+"")&&Ye(i,l),c[1].length!=1?m||(m=ro(),m.c(),m.m(e,a)):m&&(m.d(1),m=null),E&14){k=oo(c[1]);let h;for(h=0;h<k.length;h+=1){const b=io(c,k,h);g[h]?g[h].p(b,E):(g[h]=ao(b),g[h].c(),g[h].m(s,null))}for(;h<g.length;h+=1)g[h].d(1);g.length=k.length}(!d||E&1)&&lo(s,"hide",c[0]),c[0]?p?E&1&&Nt(p,1):(p=_o(),p.c(),Nt(p,1),p.m(f.parentNode,f)):p&&(ns(),_n(p,1,1,()=>{p=null}),Xr())},i(c){d||(Nt(p),d=!0)},o(c){_n(p),d=!1},d(c){c&&(ie(e),ie(_),ie(s),ie(u),ie(f)),m&&m.d(),ts(g,c),p&&p.d(c)}}}function fs(o,e,t){let{is_running:n}=e,{endpoint_returns:l}=e,{js_returns:i}=e,{current_language:r}=e;return o.$$set=a=>{"is_running"in a&&t(0,n=a.is_running),"endpoint_returns"in a&&t(1,l=a.endpoint_returns),"js_returns"in a&&t(2,i=a.js_returns),"current_language"in a&&t(3,r=a.current_language)},[n,l,i,r]}class ps extends Yr{constructor(e){super(),os(this,e,fs,cs,rs,{is_running:0,endpoint_returns:1,js_returns:2,current_language:3})}}const{SvelteComponent:ms,create_component:ds,destroy_component:gs,detach:hs,init:vs,insert:bs,mount_component:ks,safe_not_equal:ws,set_data:ys,text:Es,transition_in:As,transition_out:Ls}=window.__gradio__svelte__internal;function Ps(o){let e;return{c(){e=Es(o[0])},m(t,n){bs(t,e,n)},p(t,n){n&1&&ys(e,t[0])},d(t){t&&hs(e)}}}function Os(o){let e,t;return e=new oi({props:{size:"sm",$$slots:{default:[Ps]},$$scope:{ctx:o}}}),e.$on("click",o[1]),{c(){ds(e.$$.fragment)},m(n,l){ks(e,n,l),t=!0},p(n,[l]){const i={};l&9&&(i.$$scope={dirty:l,ctx:n}),e.$set(i)},i(n){t||(As(e.$$.fragment,n),t=!0)},o(n){Ls(e.$$.fragment,n),t=!1},d(n){gs(e,n)}}}function Ts(o,e,t){let{code:n}=e,l="copy";function i(){navigator.clipboard.writeText(n),t(0,l="copied!"),setTimeout(()=>{t(0,l="copy")},1500)}return o.$$set=r=>{"code"in r&&t(2,n=r.code)},[l,i,n]}class Ft extends ms{constructor(e){super(),vs(this,e,Ts,Os,ws,{code:2})}}const{SvelteComponent:qs,append:yl,attr:yt,check_outros:Is,create_component:yn,destroy_component:En,detach:Xe,element:xe,group_outros:Cs,init:Ds,insert:et,mount_component:An,noop:El,safe_not_equal:$s,space:Al,transition_in:Et,transition_out:At}=window.__gradio__svelte__internal;function Rs(o){let e,t,n,l,i,r;return t=new Ft({props:{code:co}}),{c(){e=xe("div"),yn(t.$$.fragment),n=Al(),l=xe("div"),i=xe("pre"),i.textContent=`$ ${co}`,yt(e,"class","copy svelte-hq8ezf"),yt(i,"class","svelte-hq8ezf")},m(a,_){et(a,e,_),An(t,e,null),et(a,n,_),et(a,l,_),yl(l,i),r=!0},p:El,i(a){r||(Et(t.$$.fragment,a),r=!0)},o(a){At(t.$$.fragment,a),r=!1},d(a){a&&(Xe(e),Xe(n),Xe(l)),En(t)}}}function Ss(o){let e,t,n,l,i,r;return t=new Ft({props:{code:uo}}),{c(){e=xe("div"),yn(t.$$.fragment),n=Al(),l=xe("div"),i=xe("pre"),i.textContent=`$ ${uo}`,yt(e,"class","copy svelte-hq8ezf"),yt(i,"class","svelte-hq8ezf")},m(a,_){et(a,e,_),An(t,e,null),et(a,n,_),et(a,l,_),yl(l,i),r=!0},p:El,i(a){r||(Et(t.$$.fragment,a),r=!0)},o(a){At(t.$$.fragment,a),r=!1},d(a){a&&(Xe(e),Xe(n),Xe(l)),En(t)}}}function Vs(o){let e,t,n,l;const i=[Ss,Rs],r=[];function a(_,s){return _[0]==="python"?0:_[0]==="javascript"?1:-1}return~(t=a(o))&&(n=r[t]=i[t](o)),{c(){e=xe("code"),n&&n.c(),yt(e,"class","svelte-hq8ezf")},m(_,s){et(_,e,s),~t&&r[t].m(e,null),l=!0},p(_,s){let u=t;t=a(_),t===u?~t&&r[t].p(_,s):(n&&(Cs(),At(r[u],1,1,()=>{r[u]=null}),Is()),~t?(n=r[t],n?n.p(_,s):(n=r[t]=i[t](_),n.c()),Et(n,1),n.m(e,null)):n=null)},i(_){l||(Et(n),l=!0)},o(_){At(n),l=!1},d(_){_&&Xe(e),~t&&r[t].d()}}}function js(o){let e,t;return e=new fl({props:{border_mode:"contrast",$$slots:{default:[Vs]},$$scope:{ctx:o}}}),{c(){yn(e.$$.fragment)},m(n,l){An(e,n,l),t=!0},p(n,[l]){const i={};l&3&&(i.$$scope={dirty:l,ctx:n}),e.$set(i)},i(n){t||(Et(e.$$.fragment,n),t=!0)},o(n){At(e.$$.fragment,n),t=!1},d(n){En(e,n)}}}let uo="pip install gradio_client",co="npm i -D @gradio/client";function Ns(o,e,t){let{current_language:n}=e;return o.$$set=l=>{"current_language"in l&&t(0,n=l.current_language)},[n]}class Ms extends qs{constructor(e){super(),Ds(this,e,Ns,js,$s,{current_language:0})}}const{SvelteComponent:zs,append:st,attr:zt,detach:Ln,element:Bt,empty:Bs,init:Us,insert:Pn,noop:fo,safe_not_equal:Fs,set_data:Ll,text:Ut}=window.__gradio__svelte__internal;function Hs(o){let e,t,n,l;return{c(){e=Bt("h3"),t=Ut(`fn_index:
		`),n=Bt("span"),l=Ut(o[1]),zt(n,"class","post svelte-41kcm6"),zt(e,"class","svelte-41kcm6")},m(i,r){Pn(i,e,r),st(e,t),st(e,n),st(n,l)},p(i,r){r&2&&Ll(l,i[1])},d(i){i&&Ln(e)}}}function Ws(o){let e,t,n,l="/"+o[0],i;return{c(){e=Bt("h3"),t=Ut(`api_name:
		`),n=Bt("span"),i=Ut(l),zt(n,"class","post svelte-41kcm6"),zt(e,"class","svelte-41kcm6")},m(r,a){Pn(r,e,a),st(e,t),st(e,n),st(n,i)},p(r,a){a&1&&l!==(l="/"+r[0])&&Ll(i,l)},d(r){r&&Ln(e)}}}function Gs(o){let e;function t(i,r){return i[2]?Ws:Hs}let n=t(o),l=n(o);return{c(){l.c(),e=Bs()},m(i,r){l.m(i,r),Pn(i,e,r)},p(i,[r]){n===(n=t(i))&&l?l.p(i,r):(l.d(1),l=n(i),l&&(l.c(),l.m(e.parentNode,e)))},i:fo,o:fo,d(i){i&&Ln(e),l.d(i)}}}function Js(o,e,t){let{api_name:n=null}=e,{fn_index:l=null}=e,{named:i}=e;return o.$$set=r=>{"api_name"in r&&t(0,n=r.api_name),"fn_index"in r&&t(1,l=r.fn_index),"named"in r&&t(2,i=r.named)},[n,l,i]}class Pl extends zs{constructor(e){super(),Us(this,e,Js,Gs,Fs,{api_name:0,fn_index:1,named:2})}}const{SvelteComponent:Qs,append:D,attr:le,binding_callbacks:po,check_outros:Ol,create_component:Pt,destroy_component:Ot,destroy_each:un,detach:j,element:te,empty:Ks,ensure_array_like:it,group_outros:Tl,init:Zs,insert:N,mount_component:Tt,noop:Ys,safe_not_equal:Xs,set_data:ge,space:On,text:O,transition_in:Me,transition_out:ze}=window.__gradio__svelte__internal;function mo(o,e,t){const n=o.slice();return n[21]=e[t].label,n[22]=e[t].type,n[14]=e[t].python_type,n[23]=e[t].component,n[15]=e[t].example_input,n[24]=e[t].serializer,n[20]=t,n}function go(o,e,t){const n=o.slice();return n[21]=e[t].label,n[22]=e[t].type,n[14]=e[t].python_type,n[23]=e[t].component,n[15]=e[t].example_input,n[24]=e[t].serializer,n[20]=t,n}function ho(o,e,t){const n=o.slice();return n[14]=e[t].python_type,n[15]=e[t].example_input,n[16]=e[t].parameter_name,n[17]=e[t].parameter_has_default,n[18]=e[t].parameter_default,n[20]=t,n}function xs(o){let e,t;return e=new Pl({props:{named:o[5],fn_index:o[1]}}),{c(){Pt(e.$$.fragment)},m(n,l){Tt(e,n,l),t=!0},p(n,l){const i={};l&32&&(i.named=n[5]),l&2&&(i.fn_index=n[1]),e.$set(i)},i(n){t||(Me(e.$$.fragment,n),t=!0)},o(n){ze(e.$$.fragment,n),t=!1},d(n){Ot(e,n)}}}function ea(o){let e,t;return e=new Pl({props:{named:o[5],api_name:o[0].api_name}}),{c(){Pt(e.$$.fragment)},m(n,l){Tt(e,n,l),t=!0},p(n,l){const i={};l&32&&(i.named=n[5]),l&1&&(i.api_name=n[0].api_name),e.$set(i)},i(n){t||(Me(e.$$.fragment,n),t=!0)},o(n){ze(e.$$.fragment,n),t=!1},d(n){Ot(e,n)}}}function ta(o){let e,t,n,l,i,r,a,_,s,u,f,d,m,k,g;t=new Ft({props:{code:o[8]?.innerText}});let p=it(o[11]),c=[];for(let v=0;v<p.length;v+=1)c[v]=vo(go(o,p,v));function E(v,I){return v[5]?la:oa}let h=E(o),b=h(o),w=it(o[3]),L=[];for(let v=0;v<w.length;v+=1)L[v]=ko(mo(o,w,v));return{c(){e=te("div"),Pt(t.$$.fragment),n=On(),l=te("div"),i=te("pre"),r=O(`import { client } from "@gradio/client";
`);for(let v=0;v<c.length;v+=1)c[v].c();a=O(`
const app = await client(`),_=te("span"),s=O('"'),u=O(o[2]),f=O('"'),d=O(`);
const result = await app.predict(`),b.c(),m=O(", [");for(let v=0;v<L.length;v+=1)L[v].c();k=O(`
	]);

console.log(result.data);
`),le(e,"class","copy svelte-12dbwrq"),le(_,"class","token string svelte-12dbwrq"),le(i,"class","svelte-12dbwrq")},m(v,I){N(v,e,I),Tt(t,e,null),N(v,n,I),N(v,l,I),D(l,i),D(i,r);for(let R=0;R<c.length;R+=1)c[R]&&c[R].m(i,null);D(i,a),D(i,_),D(_,s),D(_,u),D(_,f),D(i,d),b.m(i,null),D(i,m);for(let R=0;R<L.length;R+=1)L[R]&&L[R].m(i,null);D(i,k),o[13](l),g=!0},p(v,I){const R={};if(I&256&&(R.code=v[8]?.innerText),t.$set(R),I&2048){p=it(v[11]);let A;for(A=0;A<p.length;A+=1){const T=go(v,p,A);c[A]?c[A].p(T,I):(c[A]=vo(T),c[A].c(),c[A].m(i,a))}for(;A<c.length;A+=1)c[A].d(1);c.length=p.length}if((!g||I&4)&&ge(u,v[2]),h===(h=E(v))&&b?b.p(v,I):(b.d(1),b=h(v),b&&(b.c(),b.m(i,m))),I&1048){w=it(v[3]);let A;for(A=0;A<w.length;A+=1){const T=mo(v,w,A);L[A]?L[A].p(T,I):(L[A]=ko(T),L[A].c(),L[A].m(i,k))}for(;A<L.length;A+=1)L[A].d(1);L.length=w.length}},i(v){g||(Me(t.$$.fragment,v),g=!0)},o(v){ze(t.$$.fragment,v),g=!1},d(v){v&&(j(e),j(n),j(l)),Ot(t),un(c,v),b.d(),un(L,v),o[13](null)}}}function na(o){let e,t,n,l,i,r,a,_,s,u,f,d,m,k,g,p,c,E,h,b,w=o[0].api_name+"",L,v,I,R,A,T;t=new Ft({props:{code:o[7]?.innerText}});let B=o[9]&&sa(),ne=it(o[3]),V=[];for(let S=0;S<ne.length;S+=1)V[S]=wo(ho(o,ne,S));return{c(){e=te("div"),Pt(t.$$.fragment),n=On(),l=te("div"),i=te("pre"),r=te("span"),r.textContent="from",a=O(" gradio_client "),_=te("span"),_.textContent="import",s=O(" Client"),B&&B.c(),u=O(`

client = Client(`),f=te("span"),d=O('"'),m=O(o[2]),k=O('"'),g=O(`)
result = client.`),p=te("span"),p.textContent="predict",c=O("(");for(let S=0;S<V.length;S+=1)V[S].c();E=O(`
		api_name=`),h=te("span"),b=O('"/'),L=O(w),v=O('"'),I=O(`
)
`),R=te("span"),R.textContent="print",A=O("(result)"),le(e,"class","copy svelte-12dbwrq"),le(r,"class","highlight"),le(_,"class","highlight"),le(f,"class","token string svelte-12dbwrq"),le(p,"class","highlight"),le(h,"class","api-name svelte-12dbwrq"),le(R,"class","highlight"),le(i,"class","svelte-12dbwrq")},m(S,q){N(S,e,q),Tt(t,e,null),N(S,n,q),N(S,l,q),D(l,i),D(i,r),D(i,a),D(i,_),D(i,s),B&&B.m(i,null),D(i,u),D(i,f),D(f,d),D(f,m),D(f,k),D(i,g),D(i,p),D(i,c);for(let M=0;M<V.length;M+=1)V[M]&&V[M].m(i,null);D(i,E),D(i,h),D(h,b),D(h,L),D(h,v),D(i,I),D(i,R),D(i,A),o[12](l),T=!0},p(S,q){const M={};if(q&128&&(M.code=S[7]?.innerText),t.$set(M),(!T||q&4)&&ge(m,S[2]),q&8){ne=it(S[3]);let J;for(J=0;J<ne.length;J+=1){const H=ho(S,ne,J);V[J]?V[J].p(H,q):(V[J]=wo(H),V[J].c(),V[J].m(i,E))}for(;J<V.length;J+=1)V[J].d(1);V.length=ne.length}(!T||q&1)&&w!==(w=S[0].api_name+"")&&ge(L,w)},i(S){T||(Me(t.$$.fragment,S),T=!0)},o(S){ze(t.$$.fragment,S),T=!1},d(S){S&&(j(e),j(n),j(l)),Ot(t),B&&B.d(),un(V,S),o[12](null)}}}function vo(o){let e,t,n,l=o[15].url+"",i,r,a=o[23]+"",_,s,u,f;return{c(){e=O(`
const response_`),t=O(o[20]),n=O(' = await fetch("'),i=O(l),r=O(`");
const example`),_=O(a),s=O(" = await response_"),u=O(o[20]),f=O(`.blob();
						`)},m(d,m){N(d,e,m),N(d,t,m),N(d,n,m),N(d,i,m),N(d,r,m),N(d,_,m),N(d,s,m),N(d,u,m),N(d,f,m)},p:Ys,d(d){d&&(j(e),j(t),j(n),j(i),j(r),j(_),j(s),j(u),j(f))}}}function oa(o){let e;return{c(){e=O(o[1])},m(t,n){N(t,e,n)},p(t,n){n&2&&ge(e,t[1])},d(t){t&&j(e)}}}function la(o){let e,t=o[0].api_name+"",n,l;return{c(){e=O('"/'),n=O(t),l=O('"')},m(i,r){N(i,e,r),N(i,n,r),N(i,l,r)},p(i,r){r&1&&t!==(t=i[0].api_name+"")&&ge(n,t)},d(i){i&&(j(e),j(n),j(l))}}}function ia(o){let e,t,n=ft(o[15],o[14].type,"js")+"",l,i,r,a,_=o[4][o[20]].type+"",s,u,f,d=o[21]+"",m,k,g=o[23]+"",p,c,E=o[4][o[20]].description&&bo(o);return{c(){e=O(`		
				`),t=te("span"),l=O(n),i=O(", "),r=te("span"),a=O("// "),s=O(_),u=O(" "),E&&E.c(),f=O(" in '"),m=O(d),k=O("' "),p=O(g),c=O(" component"),le(t,"class","example-inputs svelte-12dbwrq"),le(r,"class","desc svelte-12dbwrq")},m(h,b){N(h,e,b),N(h,t,b),D(t,l),N(h,i,b),N(h,r,b),D(r,a),D(r,s),D(r,u),E&&E.m(r,null),D(r,f),D(r,m),D(r,k),D(r,p),D(r,c)},p(h,b){b&8&&n!==(n=ft(h[15],h[14].type,"js")+"")&&ge(l,n),b&16&&_!==(_=h[4][h[20]].type+"")&&ge(s,_),h[4][h[20]].description?E?E.p(h,b):(E=bo(h),E.c(),E.m(r,f)):E&&(E.d(1),E=null),b&8&&d!==(d=h[21]+"")&&ge(m,d),b&8&&g!==(g=h[23]+"")&&ge(p,g)},d(h){h&&(j(e),j(t),j(i),j(r)),E&&E.d()}}}function ra(o){let e,t,n,l=o[23]+"",i,r,a,_,s=o[21]+"",u,f,d=o[23]+"",m,k;return{c(){e=O(`
				`),t=te("span"),n=O("example"),i=O(l),r=O(", "),a=te("span"),_=O("	// blob in '"),u=O(s),f=O("' "),m=O(d),k=O(" component"),le(t,"class","example-inputs svelte-12dbwrq"),le(a,"class","desc svelte-12dbwrq")},m(g,p){N(g,e,p),N(g,t,p),D(t,n),D(t,i),N(g,r,p),N(g,a,p),D(a,_),D(a,u),D(a,f),D(a,m),D(a,k)},p(g,p){p&8&&l!==(l=g[23]+"")&&ge(i,l),p&8&&s!==(s=g[21]+"")&&ge(u,s),p&8&&d!==(d=g[23]+"")&&ge(m,d)},d(g){g&&(j(e),j(t),j(r),j(a))}}}function bo(o){let e,t=o[4][o[20]].description+"",n,l;return{c(){e=O("("),n=O(t),l=O(")")},m(i,r){N(i,e,r),N(i,n,r),N(i,l,r)},p(i,r){r&16&&t!==(t=i[4][i[20]].description+"")&&ge(n,t)},d(i){i&&(j(e),j(n),j(l))}}}function ko(o){let e,t;function n(r,a){return a&8&&(e=null),e==null&&(e=!!r[10].includes(r[23])),e?ra:ia}let l=n(o,-1),i=l(o);return{c(){i.c(),t=Ks()},m(r,a){i.m(r,a),N(r,t,a)},p(r,a){l===(l=n(r,a))&&i?i.p(r,a):(i.d(1),i=l(r),i&&(i.c(),i.m(t.parentNode,t)))},d(r){r&&j(t),i.d(r)}}}function sa(o){let e;return{c(){e=O(", file")},m(t,n){N(t,e,n)},d(t){t&&j(e)}}}function wo(o){let e,t=o[16]?o[16]+"=":"",n,l,i=ft(o[17]?o[18]:o[15],o[14].type,"py")+"",r,a;return{c(){e=O(`
		`),n=O(t),l=te("span"),r=O(i),a=O(","),le(l,"class","example-inputs svelte-12dbwrq")},m(_,s){N(_,e,s),N(_,n,s),N(_,l,s),D(l,r),N(_,a,s)},p(_,s){s&8&&t!==(t=_[16]?_[16]+"=":"")&&ge(n,t),s&8&&i!==(i=ft(_[17]?_[18]:_[15],_[14].type,"py")+"")&&ge(r,i)},d(_){_&&(j(e),j(n),j(l),j(a))}}}function aa(o){let e,t,n,l;const i=[na,ta],r=[];function a(_,s){return _[6]==="python"?0:_[6]==="javascript"?1:-1}return~(t=a(o))&&(n=r[t]=i[t](o)),{c(){e=te("code"),n&&n.c(),le(e,"class","svelte-12dbwrq")},m(_,s){N(_,e,s),~t&&r[t].m(e,null),l=!0},p(_,s){let u=t;t=a(_),t===u?~t&&r[t].p(_,s):(n&&(Tl(),ze(r[u],1,1,()=>{r[u]=null}),Ol()),~t?(n=r[t],n?n.p(_,s):(n=r[t]=i[t](_),n.c()),Me(n,1),n.m(e,null)):n=null)},i(_){l||(Me(n),l=!0)},o(_){ze(n),l=!1},d(_){_&&j(e),~t&&r[t].d()}}}function _a(o){let e,t,n,l,i,r;const a=[ea,xs],_=[];function s(u,f){return u[5]?0:1}return t=s(o),n=_[t]=a[t](o),i=new fl({props:{$$slots:{default:[aa]},$$scope:{ctx:o}}}),{c(){e=te("div"),n.c(),l=On(),Pt(i.$$.fragment),le(e,"class","container svelte-12dbwrq")},m(u,f){N(u,e,f),_[t].m(e,null),D(e,l),Tt(i,e,null),r=!0},p(u,[f]){let d=t;t=s(u),t===d?_[t].p(u,f):(Tl(),ze(_[d],1,1,()=>{_[d]=null}),Ol(),n=_[t],n?n.p(u,f):(n=_[t]=a[t](u),n.c()),Me(n,1),n.m(e,l));const m={};f&134218239&&(m.$$scope={dirty:f,ctx:u}),i.$set(m)},i(u){r||(Me(n),Me(i.$$.fragment,u),r=!0)},o(u){ze(n),ze(i.$$.fragment,u),r=!1},d(u){u&&j(e),_[t].d(),Ot(i)}}}function ua(o,e,t){let{dependency:n}=e,{dependency_index:l}=e,{root:i}=e,{endpoint_parameters:r}=e,{js_parameters:a}=e,{named:_}=e,{current_language:s}=e,u,f,d=r.some(c=>bl(c.example_input)),m=["Audio","File","Image","Video"],k=r.filter(c=>m.includes(c.component));function g(c){po[c?"unshift":"push"](()=>{u=c,t(7,u)})}function p(c){po[c?"unshift":"push"](()=>{f=c,t(8,f)})}return o.$$set=c=>{"dependency"in c&&t(0,n=c.dependency),"dependency_index"in c&&t(1,l=c.dependency_index),"root"in c&&t(2,i=c.root),"endpoint_parameters"in c&&t(3,r=c.endpoint_parameters),"js_parameters"in c&&t(4,a=c.js_parameters),"named"in c&&t(5,_=c.named),"current_language"in c&&t(6,s=c.current_language)},[n,l,i,r,a,_,s,u,f,d,m,k,g,p]}class ca extends Qs{constructor(e){super(),Zs(this,e,ua,_a,Xs,{dependency:0,dependency_index:1,root:2,endpoint_parameters:3,js_parameters:4,named:5,current_language:6})}}const fa=""+new URL("python-20e39c92.svg",import.meta.url).href,pa=""+new URL("javascript-850cf94b.svg",import.meta.url).href;const{SvelteComponent:ma,append:we,attr:tt,check_outros:da,create_component:ga,destroy_component:ha,destroy_each:va,detach:se,element:Se,empty:ba,ensure_array_like:yo,group_outros:ka,init:wa,insert:ae,mount_component:ya,noop:Ea,safe_not_equal:Aa,set_data:pt,set_style:La,space:rt,text:Ae,toggle_class:Eo,transition_in:Mt,transition_out:cn}=window.__gradio__svelte__internal;function Ao(o,e,t){const n=o.slice();return n[4]=e[t].label,n[5]=e[t].type,n[6]=e[t].python_type,n[7]=e[t].component,n[8]=e[t].serializer,n[10]=t,n}function Pa(o){let e;return{c(){e=Ae("1 element")},m(t,n){ae(t,e,n)},p:Ea,d(t){t&&se(e)}}}function Oa(o){let e=o[3]=="python"?"tuple":"list",t,n,l=o[1].length+"",i,r;return{c(){t=Ae(e),n=Ae(" of "),i=Ae(l),r=Ae(`
		elements`)},m(a,_){ae(a,t,_),ae(a,n,_),ae(a,i,_),ae(a,r,_)},p(a,_){_&8&&e!==(e=a[3]=="python"?"tuple":"list")&&pt(t,e),_&2&&l!==(l=a[1].length+"")&&pt(i,l)},d(a){a&&(se(t),se(n),se(i),se(r))}}}function Lo(o){let e;return{c(){e=Se("span"),e.textContent=`[${o[10]}]`,tt(e,"class","code svelte-16h224k")},m(t,n){ae(t,e,n)},d(t){t&&se(e)}}}function Ta(o){let e=o[2][o[10]].type+"",t;return{c(){t=Ae(e)},m(n,l){ae(n,t,l)},p(n,l){l&4&&e!==(e=n[2][n[10]].type+"")&&pt(t,e)},d(n){n&&se(t)}}}function qa(o){let e=o[6].type+"",t;return{c(){t=Ae(e)},m(n,l){ae(n,t,l)},p(n,l){l&2&&e!==(e=n[6].type+"")&&pt(t,e)},d(n){n&&se(t)}}}function Po(o){let e,t,n,l,i,r,a,_,s,u=o[4]+"",f,d,m=o[7]+"",k,g,p,c=o[1].length>1&&Lo(o);function E(w,L){return w[3]==="python"?qa:Ta}let h=E(o),b=h(o);return{c(){e=Se("hr"),t=rt(),n=Se("div"),l=Se("p"),c&&c.c(),i=rt(),r=Se("span"),b.c(),a=rt(),_=Se("p"),s=Ae('The output value that appears in the "'),f=Ae(u),d=Ae('" '),k=Ae(m),g=Ae(`
				component.`),p=rt(),tt(e,"class","hr svelte-16h224k"),tt(r,"class","code highlight svelte-16h224k"),tt(_,"class","desc svelte-16h224k"),La(n,"margin","10px")},m(w,L){ae(w,e,L),ae(w,t,L),ae(w,n,L),we(n,l),c&&c.m(l,null),we(l,i),we(l,r),b.m(r,null),we(n,a),we(n,_),we(_,s),we(_,f),we(_,d),we(_,k),we(_,g),we(n,p)},p(w,L){w[1].length>1?c||(c=Lo(w),c.c(),c.m(l,i)):c&&(c.d(1),c=null),h===(h=E(w))&&b?b.p(w,L):(b.d(1),b=h(w),b&&(b.c(),b.m(r,null))),L&2&&u!==(u=w[4]+"")&&pt(f,u),L&2&&m!==(m=w[7]+"")&&pt(k,m)},d(w){w&&(se(e),se(t),se(n)),c&&c.d(),b.d()}}}function Oo(o){let e,t,n;return t=new ul({props:{margin:!1}}),{c(){e=Se("div"),ga(t.$$.fragment),tt(e,"class","load-wrap")},m(l,i){ae(l,e,i),ya(t,e,null),n=!0},i(l){n||(Mt(t.$$.fragment,l),n=!0)},o(l){cn(t.$$.fragment,l),n=!1},d(l){l&&se(e),ha(t)}}}function Ia(o){let e,t,n,l,i,r,a,_;function s(g,p){return g[1].length>1?Oa:Pa}let u=s(o),f=u(o),d=yo(o[1]),m=[];for(let g=0;g<d.length;g+=1)m[g]=Po(Ao(o,d,g));let k=o[0]&&Oo();return{c(){e=Se("h4"),t=Se("div"),t.innerHTML='<div class="toggle-dot toggle-right svelte-16h224k"></div>',n=Ae(`
	Returns `),f.c(),l=rt(),i=Se("div");for(let g=0;g<m.length;g+=1)m[g].c();r=rt(),k&&k.c(),a=ba(),tt(t,"class","toggle-icon svelte-16h224k"),tt(e,"class","svelte-16h224k"),Eo(i,"hide",o[0])},m(g,p){ae(g,e,p),we(e,t),we(e,n),f.m(e,null),ae(g,l,p),ae(g,i,p);for(let c=0;c<m.length;c+=1)m[c]&&m[c].m(i,null);ae(g,r,p),k&&k.m(g,p),ae(g,a,p),_=!0},p(g,[p]){if(u===(u=s(g))&&f?f.p(g,p):(f.d(1),f=u(g),f&&(f.c(),f.m(e,null))),p&14){d=yo(g[1]);let c;for(c=0;c<d.length;c+=1){const E=Ao(g,d,c);m[c]?m[c].p(E,p):(m[c]=Po(E),m[c].c(),m[c].m(i,null))}for(;c<m.length;c+=1)m[c].d(1);m.length=d.length}(!_||p&1)&&Eo(i,"hide",g[0]),g[0]?k?p&1&&Mt(k,1):(k=Oo(),k.c(),Mt(k,1),k.m(a.parentNode,a)):k&&(ka(),cn(k,1,1,()=>{k=null}),da())},i(g){_||(Mt(k),_=!0)},o(g){cn(k),_=!1},d(g){g&&(se(e),se(l),se(i),se(r),se(a)),f.d(),va(m,g),k&&k.d(g)}}}function Ca(o,e,t){let{is_running:n}=e,{endpoint_returns:l}=e,{js_returns:i}=e,{current_language:r}=e;return o.$$set=a=>{"is_running"in a&&t(0,n=a.is_running),"endpoint_returns"in a&&t(1,l=a.endpoint_returns),"js_returns"in a&&t(2,i=a.js_returns),"current_language"in a&&t(3,r=a.current_language)},[n,l,i,r]}class Da extends ma{constructor(e){super(),wa(this,e,Ca,Ia,Aa,{is_running:0,endpoint_returns:1,js_returns:2,current_language:3})}}const{SvelteComponent:$a,append:F,attr:K,bubble:To,check_outros:Tn,create_component:at,destroy_component:_t,destroy_each:qo,detach:$e,element:ue,empty:qn,ensure_array_like:$t,group_outros:In,init:Ra,insert:Re,listen:Sa,mount_component:ut,safe_not_equal:Va,space:De,src_url_equal:ja,text:ye,transition_in:ce,transition_out:Pe}=window.__gradio__svelte__internal,{onMount:Na,createEventDispatcher:fu}=window.__gradio__svelte__internal;function Io(o,e,t){const n=o.slice();return n[20]=e[t],n[22]=t,n}function Co(o,e,t){const n=o.slice();return n[23]=e[t][0],n[24]=e[t][1],n}function Do(o){let e,t,n,l;const i=[za,Ma],r=[];function a(_,s){return _[7]?0:1}return e=a(o),t=r[e]=i[e](o),{c(){t.c(),n=qn()},m(_,s){r[e].m(_,s),Re(_,n,s),l=!0},p(_,s){t.p(_,s)},i(_){l||(ce(t),l=!0)},o(_){Pe(t),l=!1},d(_){_&&$e(n),r[e].d(_)}}}function Ma(o){let e,t;return e=new Sr({props:{root:o[0]}}),e.$on("close",o[13]),{c(){at(e.$$.fragment)},m(n,l){ut(e,n,l),t=!0},p(n,l){const i={};l&1&&(i.root=n[0]),e.$set(i)},i(n){t||(ce(e.$$.fragment,n),t=!0)},o(n){Pe(e.$$.fragment,n),t=!1},d(n){_t(e,n)}}}function za(o){let e,t,n,l,i,r,a,_,s,u,f,d,m,k,g,p,c,E,h,b,w,L,v,I,R,A,T,B,ne,V;t=new Zr({props:{root:o[2]||o[0],api_count:o[7]}}),t.$on("close",o[11]);let S=$t(o[8]),q=[];for(let C=0;C<S.length;C+=1)q[C]=$o(Co(o,S,C));I=new Ms({props:{current_language:o[3]}});let M=o[2]&&Ro(o),J=$t(o[1]),H=[];for(let C=0;C<J.length;C+=1)H[C]=Vo(Io(o,J,C));const nt=C=>Pe(H[C],1,1,()=>{H[C]=null});return{c(){e=ue("div"),at(t.$$.fragment),n=De(),l=ue("div"),i=ue("div"),r=ue("p"),a=ye("Use the "),_=ue("code"),_.textContent="gradio_client",s=ye(`
					Python library (`),u=ue("a"),f=ye("docs"),d=ye(`) or the
					`),m=ue("code"),m.textContent="@gradio/client",k=ye(`
					Javascript package (`),g=ue("a"),p=ye("docs"),c=ye(`) to
					query the app via API.`),E=De(),h=ue("div"),b=ue("div");for(let C=0;C<q.length;C+=1)q[C].c();w=De(),L=ue("p"),L.textContent="1. Install the client if you don't already have it installed.",v=De(),at(I.$$.fragment),R=De(),A=ue("p"),T=ye(`2. Find the API endpoint below corresponding to your desired function
					in the app. Copy the code snippet, replacing the placeholder values
					with your own input data.
					`),M&&M.c(),B=ye(" Run the code, that's it!"),ne=De();for(let C=0;C<H.length;C+=1)H[C].c();K(e,"class","banner-wrap svelte-ay9o2s"),K(_,"class","library svelte-ay9o2s"),K(u,"href",pn),K(u,"target","_blank"),K(u,"class","svelte-ay9o2s"),K(m,"class","library svelte-ay9o2s"),K(g,"href",fn),K(g,"target","_blank"),K(g,"class","svelte-ay9o2s"),K(i,"class","client-doc svelte-ay9o2s"),K(b,"class","snippets svelte-ay9o2s"),K(L,"class","padded svelte-ay9o2s"),K(A,"class","padded svelte-ay9o2s"),K(h,"class","endpoint svelte-ay9o2s"),K(l,"class","docs-wrap svelte-ay9o2s")},m(C,Z){Re(C,e,Z),ut(t,e,null),Re(C,n,Z),Re(C,l,Z),F(l,i),F(i,r),F(r,a),F(r,_),F(r,s),F(r,u),F(u,f),F(r,d),F(r,m),F(r,k),F(r,g),F(g,p),F(r,c),F(l,E),F(l,h),F(h,b);for(let fe=0;fe<q.length;fe+=1)q[fe]&&q[fe].m(b,null);F(h,w),F(h,L),F(h,v),ut(I,h,null),F(h,R),F(h,A),F(A,T),M&&M.m(A,null),F(A,B),F(h,ne);for(let fe=0;fe<H.length;fe+=1)H[fe]&&H[fe].m(h,null);V=!0},p(C,Z){const fe={};if(Z&5&&(fe.root=C[2]||C[0]),t.$set(fe),Z&264){S=$t(C[8]);let $;for($=0;$<S.length;$+=1){const Te=Co(C,S,$);q[$]?q[$].p(Te,Z):(q[$]=$o(Te),q[$].c(),q[$].m(b,null))}for(;$<q.length;$+=1)q[$].d(1);q.length=S.length}const mt={};if(Z&8&&(mt.current_language=C[3]),I.$set(mt),C[2]?M?M.p(C,Z):(M=Ro(C),M.c(),M.m(A,B)):M&&(M.d(1),M=null),Z&127){J=$t(C[1]);let $;for($=0;$<J.length;$+=1){const Te=Io(C,J,$);H[$]?(H[$].p(Te,Z),ce(H[$],1)):(H[$]=Vo(Te),H[$].c(),ce(H[$],1),H[$].m(h,null))}for(In(),$=J.length;$<H.length;$+=1)nt($);Tn()}},i(C){if(!V){ce(t.$$.fragment,C),ce(I.$$.fragment,C);for(let Z=0;Z<J.length;Z+=1)ce(H[Z]);V=!0}},o(C){Pe(t.$$.fragment,C),Pe(I.$$.fragment,C),H=H.filter(Boolean);for(let Z=0;Z<H.length;Z+=1)Pe(H[Z]);V=!1},d(C){C&&($e(e),$e(n),$e(l)),_t(t),qo(q,C),_t(I),M&&M.d(),qo(H,C)}}}function $o(o){let e,t,n,l,i=o[23]+"",r,a,_,s,u;function f(){return o[12](o[23])}return{c(){e=ue("li"),t=ue("img"),l=De(),r=ye(i),a=De(),ja(t.src,n=o[24])||K(t,"src",n),K(t,"alt",""),K(t,"class","svelte-ay9o2s"),K(e,"class",_="snippet "+(o[3]===o[23]?"current-lang":"inactive-lang")+" svelte-ay9o2s")},m(d,m){Re(d,e,m),F(e,t),F(e,l),F(e,r),F(e,a),s||(u=Sa(e,"click",f),s=!0)},p(d,m){o=d,m&8&&_!==(_="snippet "+(o[3]===o[23]?"current-lang":"inactive-lang")+" svelte-ay9o2s")&&K(e,"class",_)},d(d){d&&$e(e),s=!1,u()}}}function Ro(o){let e,t,n,l,i;return{c(){e=ye(`If this is a private Space, you may need to pass your
						Hugging Face token as well (`),t=ue("a"),n=ye("read more"),i=ye(")."),K(t,"href",l=(o[3]=="python"?pn:fn)+jo),K(t,"class","underline svelte-ay9o2s"),K(t,"target","_blank")},m(r,a){Re(r,e,a),Re(r,t,a),F(t,n),Re(r,i,a)},p(r,a){a&8&&l!==(l=(r[3]=="python"?pn:fn)+jo)&&K(t,"href",l)},d(r){r&&($e(e),$e(t),$e(i))}}}function So(o){let e,t,n,l,i,r,a,_;return t=new ca({props:{named:!0,endpoint_parameters:o[5].named_endpoints["/"+o[20].api_name].parameters,js_parameters:o[6].named_endpoints["/"+o[20].api_name].parameters,dependency:o[20],dependency_index:o[22],current_language:o[3],root:o[2]||o[0]}}),l=new ps({props:{endpoint_returns:o[5].named_endpoints["/"+o[20].api_name].parameters,js_returns:o[6].named_endpoints["/"+o[20].api_name].parameters,is_running:o[4],current_language:o[3]}}),r=new Da({props:{endpoint_returns:o[5].named_endpoints["/"+o[20].api_name].returns,js_returns:o[6].named_endpoints["/"+o[20].api_name].returns,is_running:o[4],current_language:o[3]}}),{c(){e=ue("div"),at(t.$$.fragment),n=De(),at(l.$$.fragment),i=De(),at(r.$$.fragment),a=De(),K(e,"class","endpoint-container svelte-ay9o2s")},m(s,u){Re(s,e,u),ut(t,e,null),F(e,n),ut(l,e,null),F(e,i),ut(r,e,null),F(e,a),_=!0},p(s,u){const f={};u&34&&(f.endpoint_parameters=s[5].named_endpoints["/"+s[20].api_name].parameters),u&66&&(f.js_parameters=s[6].named_endpoints["/"+s[20].api_name].parameters),u&2&&(f.dependency=s[20]),u&8&&(f.current_language=s[3]),u&5&&(f.root=s[2]||s[0]),t.$set(f);const d={};u&34&&(d.endpoint_returns=s[5].named_endpoints["/"+s[20].api_name].parameters),u&66&&(d.js_returns=s[6].named_endpoints["/"+s[20].api_name].parameters),u&16&&(d.is_running=s[4]),u&8&&(d.current_language=s[3]),l.$set(d);const m={};u&34&&(m.endpoint_returns=s[5].named_endpoints["/"+s[20].api_name].returns),u&66&&(m.js_returns=s[6].named_endpoints["/"+s[20].api_name].returns),u&16&&(m.is_running=s[4]),u&8&&(m.current_language=s[3]),r.$set(m)},i(s){_||(ce(t.$$.fragment,s),ce(l.$$.fragment,s),ce(r.$$.fragment,s),_=!0)},o(s){Pe(t.$$.fragment,s),Pe(l.$$.fragment,s),Pe(r.$$.fragment,s),_=!1},d(s){s&&$e(e),_t(t),_t(l),_t(r)}}}function Vo(o){let e,t,n=o[20].show_api&&So(o);return{c(){n&&n.c(),e=qn()},m(l,i){n&&n.m(l,i),Re(l,e,i),t=!0},p(l,i){l[20].show_api?n?(n.p(l,i),i&2&&ce(n,1)):(n=So(l),n.c(),ce(n,1),n.m(e.parentNode,e)):n&&(In(),Pe(n,1,1,()=>{n=null}),Tn())},i(l){t||(ce(n),t=!0)},o(l){Pe(n),t=!1},d(l){l&&$e(e),n&&n.d(l)}}}function Ba(o){let e,t,n=o[5]&&Do(o);return{c(){n&&n.c(),e=qn()},m(l,i){n&&n.m(l,i),Re(l,e,i),t=!0},p(l,[i]){l[5]?n?(n.p(l,i),i&32&&ce(n,1)):(n=Do(l),n.c(),ce(n,1),n.m(e.parentNode,e)):n&&(In(),Pe(n,1,1,()=>{n=null}),Tn())},i(l){t||(ce(n),t=!0)},o(l){Pe(n),t=!1},d(l){l&&$e(e),n&&n.d(l)}}}const fn="https://www.gradio.app/guides/getting-started-with-the-js-client",pn="https://www.gradio.app/guides/getting-started-with-the-python-client",jo="#connecting-to-a-hugging-face-space";function ql(o,e){if(o.id===e)return o;if(o.children)for(let t of o.children){let n=ql(t,e);if(n)return n}return null}function Ua(o,e,t){let{dependencies:n}=e,{root:l}=e,{app:i}=e,{space_id:r}=e,{root_node:a}=e,_=n.filter(h=>h.show_api).length;l===""&&(l=location.protocol+"//"+location.host+location.pathname),l.endsWith("/")||(l+="/");let s="python";const u=[["python",fa],["javascript",pa]];let f=!1;n.map(h=>h.inputs.map(b=>{let w=ql(a,b)?.props?.default;return w===void 0?w="":typeof w=="object"&&(w=JSON.stringify(w)),w})),n.map(h=>new Array(h.outputs.length)),n.map(h=>new Array(h.inputs.length).fill(!1));async function d(){return await(await fetch(l+"info")).json()}async function m(){return await i.view_api()}let k,g;d().then(h=>{t(5,k=h)}),m().then(h=>{t(6,g=h)}),Na(()=>(document.body.style.overflow="hidden","parentIFrame"in window&&window.parentIFrame?.scrollTo(0,0),()=>{document.body.style.overflow="auto"}));function p(h){To.call(this,o,h)}const c=h=>t(3,s=h);function E(h){To.call(this,o,h)}return o.$$set=h=>{"dependencies"in h&&t(1,n=h.dependencies),"root"in h&&t(0,l=h.root),"app"in h&&t(9,i=h.app),"space_id"in h&&t(2,r=h.space_id),"root_node"in h&&t(10,a=h.root_node)},[l,n,r,s,f,k,g,_,u,i,a,p,c,E]}class Fa extends $a{constructor(e){super(),Ra(this,e,Ua,Ba,Va,{dependencies:1,root:0,app:9,space_id:2,root_node:10})}}cl(vn);class No{#e;#t;constructor(e,t,n,l,i,r){this.#e=e,this.theme=n,this.version=l,this.#t=t,this.i18n=cl(vn),this.root=i,this.autoscroll=r}dispatch(e,t){const n=new CustomEvent("gradio",{bubbles:!0,detail:{data:t,id:this.#e,event:e}});this.#t.dispatchEvent(n)}}const{SvelteComponent:Ha,add_flush_callback:Wa,assign:mn,bind:Mo,binding_callbacks:dn,bubble:Ga,check_outros:Ja,compute_rest_props:zo,construct_svelte_component:Bo,create_component:Uo,create_slot:Qa,destroy_component:Fo,detach:Ka,empty:Za,exclude_internal_props:Ya,get_all_dirty_from_scope:Xa,get_slot_changes:xa,get_spread_object:Ho,get_spread_update:Wo,group_outros:e_,init:t_,insert:n_,mount_component:Go,not_equal:o_,transition_in:gn,transition_out:hn,update_slot_base:l_}=window.__gradio__svelte__internal,{bind:i_,binding_callbacks:r_}=window.__gradio__svelte__internal;function s_(o){let e;const t=o[12].default,n=Qa(t,o,o[16],null);return{c(){n&&n.c()},m(l,i){n&&n.m(l,i),e=!0},p(l,i){n&&n.p&&(!e||i&65536)&&l_(n,t,l,l[16],e?xa(t,l[16],i,null):Xa(l[16]),null)},i(l){e||(gn(n,l),e=!0)},o(l){hn(n,l),e=!1},d(l){n&&n.d(l)}}}function a_(o){let e,t,n,l;const i=[{elem_id:o[6]},{elem_classes:o[7]},{target:o[3]},o[9],{theme_mode:o[4]},{root:o[2]},{gradio:o[5]}];function r(s){o[14](s)}var a=o[8];function _(s,u){let f={$$slots:{default:[s_]},$$scope:{ctx:s}};if(u!==void 0&&u&764)f=Wo(i,[u&64&&{elem_id:s[6]},u&128&&{elem_classes:s[7]},u&8&&{target:s[3]},u&512&&Ho(s[9]),u&16&&{theme_mode:s[4]},u&4&&{root:s[2]},u&32&&{gradio:s[5]}]);else for(let d=0;d<i.length;d+=1)f=mn(f,i[d]);return s[1]!==void 0&&(f.value=s[1]),{props:f}}return a&&(e=Bo(a,_(o)),o[13](e),dn.push(()=>Mo(e,"value",r)),e.$on("prop_change",o[15])),{c(){e&&Uo(e.$$.fragment),n=Za()},m(s,u){e&&Go(e,s,u),n_(s,n,u),l=!0},p(s,[u]){if(a!==(a=s[8])){if(e){e_();const f=e;hn(f.$$.fragment,1,0,()=>{Fo(f,1)}),Ja()}a?(e=Bo(a,_(s,u)),s[13](e),dn.push(()=>Mo(e,"value",r)),e.$on("prop_change",s[15]),Uo(e.$$.fragment),gn(e.$$.fragment,1),Go(e,n.parentNode,n)):e=null}else if(a){const f=u&764?Wo(i,[u&64&&{elem_id:s[6]},u&128&&{elem_classes:s[7]},u&8&&{target:s[3]},u&512&&Ho(s[9]),u&16&&{theme_mode:s[4]},u&4&&{root:s[2]},u&32&&{gradio:s[5]}]):{};u&65536&&(f.$$scope={dirty:u,ctx:s}),!t&&u&2&&(t=!0,f.value=s[1],Wa(()=>t=!1)),e.$set(f)}},i(s){l||(e&&gn(e.$$.fragment,s),l=!0)},o(s){e&&hn(e.$$.fragment,s),l=!1},d(s){s&&Ka(n),o[13](null),e&&Fo(e,s)}}}function __(o,e,t){const n=["root","component","target","theme_mode","instance","value","gradio","elem_id","elem_classes","id"];let l=zo(e,n),{$$slots:i={},$$scope:r}=e,{root:a}=e,{component:_}=e,{target:s}=e,{theme_mode:u}=e,{instance:f}=e,{value:d}=e,{gradio:m}=e,{elem_id:k}=e,{elem_classes:g}=e,{id:p}=e;const c=(v,I,R)=>new CustomEvent("prop_change",{detail:{id:v,prop:I,value:R}});function E(v){return new Proxy(v,{construct(R,A){const T=new R(...A),B=Object.getOwnPropertyNames(T).filter(V=>!V.startsWith("$"));function ne(V){return function(S){const q=c(p,V,S);s.dispatchEvent(q)}}return B.forEach(V=>{r_.push(()=>i_(T,V,ne(V)))}),T}})}const h=E(_);function b(v){dn[v?"unshift":"push"](()=>{f=v,t(0,f)})}function w(v){d=v,t(1,d)}function L(v){Ga.call(this,o,v)}return o.$$set=v=>{e=mn(mn({},e),Ya(v)),t(9,l=zo(e,n)),"root"in v&&t(2,a=v.root),"component"in v&&t(10,_=v.component),"target"in v&&t(3,s=v.target),"theme_mode"in v&&t(4,u=v.theme_mode),"instance"in v&&t(0,f=v.instance),"value"in v&&t(1,d=v.value),"gradio"in v&&t(5,m=v.gradio),"elem_id"in v&&t(6,k=v.elem_id),"elem_classes"in v&&t(7,g=v.elem_classes),"id"in v&&t(11,p=v.id),"$$scope"in v&&t(16,r=v.$$scope)},[f,d,a,s,u,m,k,g,h,l,_,p,i,b,w,L,r]}class u_ extends Ha{constructor(e){super(),t_(this,e,__,a_,o_,{root:2,component:10,target:3,theme_mode:4,instance:0,value:1,gradio:5,elem_id:6,elem_classes:7,id:11})}}const{SvelteComponent:c_,add_flush_callback:Jo,assign:f_,bind:Qo,binding_callbacks:Ko,bubble:Zo,check_outros:Il,create_component:Cl,destroy_component:Dl,detach:Cn,empty:Dn,ensure_array_like:Yo,get_spread_object:p_,get_spread_update:m_,group_outros:$l,init:d_,insert:$n,mount_component:Rl,outro_and_destroy_block:g_,safe_not_equal:h_,transition_in:ct,transition_out:Lt,update_keyed_each:v_}=window.__gradio__svelte__internal,{onMount:b_,createEventDispatcher:k_,setContext:w_}=window.__gradio__svelte__internal;function Xo(o,e,t){const n=o.slice();return n[13]=e[t],n}function xo(o){let e=[],t=new Map,n,l,i=Yo(o[0].children);const r=a=>a[13].id;for(let a=0;a<i.length;a+=1){let _=Xo(o,i,a),s=r(_);t.set(s,e[a]=el(s,_))}return{c(){for(let a=0;a<e.length;a+=1)e[a].c();n=Dn()},m(a,_){for(let s=0;s<e.length;s+=1)e[s]&&e[s].m(a,_);$n(a,n,_),l=!0},p(a,_){_&15&&(i=Yo(a[0].children),$l(),e=v_(e,_,r,1,a,i,t,n.parentNode,g_,el,n,Xo),Il())},i(a){if(!l){for(let _=0;_<i.length;_+=1)ct(e[_]);l=!0}},o(a){for(let _=0;_<e.length;_+=1)Lt(e[_]);l=!1},d(a){a&&Cn(n);for(let _=0;_<e.length;_+=1)e[_].d(a)}}}function el(o,e){let t,n,l;return n=new Sl({props:{node:e[13],component:e[13].component,target:e[2],id:e[13].id,root:e[1],theme_mode:e[3]}}),n.$on("destroy",e[7]),n.$on("mount",e[8]),{key:o,first:null,c(){t=Dn(),Cl(n.$$.fragment),this.first=t},m(i,r){$n(i,t,r),Rl(n,i,r),l=!0},p(i,r){e=i;const a={};r&1&&(a.node=e[13]),r&1&&(a.component=e[13].component),r&4&&(a.target=e[2]),r&1&&(a.id=e[13].id),r&2&&(a.root=e[1]),r&8&&(a.theme_mode=e[3]),n.$set(a)},i(i){l||(ct(n.$$.fragment,i),l=!0)},o(i){Lt(n.$$.fragment,i),l=!1},d(i){i&&Cn(t),Dl(n,i)}}}function y_(o){let e,t,n=o[0].children&&o[0].children.length&&xo(o);return{c(){n&&n.c(),e=Dn()},m(l,i){n&&n.m(l,i),$n(l,e,i),t=!0},p(l,i){l[0].children&&l[0].children.length?n?(n.p(l,i),i&1&&ct(n,1)):(n=xo(l),n.c(),ct(n,1),n.m(e.parentNode,e)):n&&($l(),Lt(n,1,1,()=>{n=null}),Il())},i(l){t||(ct(n),t=!0)},o(l){Lt(n),t=!1},d(l){l&&Cn(e),n&&n.d(l)}}}function E_(o){let e,t,n,l;const i=[{id:o[0].id},{component:o[0].component},{elem_id:"elem_id"in o[0].props&&o[0].props.elem_id||`component-${o[0].id}`},{elem_classes:"elem_classes"in o[0].props&&o[0].props.elem_classes||[]},{target:o[2]},o[0].props,{theme_mode:o[3]},{root:o[1]},{gradio:new No(o[0].id,o[2],o[3],o[4],o[1],o[5])}];function r(s){o[9](s)}function a(s){o[10](s)}let _={$$slots:{default:[y_]},$$scope:{ctx:o}};for(let s=0;s<i.length;s+=1)_=f_(_,i[s]);return o[0].instance!==void 0&&(_.instance=o[0].instance),o[0].props.value!==void 0&&(_.value=o[0].props.value),e=new u_({props:_}),Ko.push(()=>Qo(e,"instance",r)),Ko.push(()=>Qo(e,"value",a)),e.$on("prop_change",A_),{c(){Cl(e.$$.fragment)},m(s,u){Rl(e,s,u),l=!0},p(s,[u]){const f=u&63?m_(i,[u&1&&{id:s[0].id},u&1&&{component:s[0].component},u&1&&{elem_id:"elem_id"in s[0].props&&s[0].props.elem_id||`component-${s[0].id}`},u&1&&{elem_classes:"elem_classes"in s[0].props&&s[0].props.elem_classes||[]},u&4&&{target:s[2]},u&1&&p_(s[0].props),u&8&&{theme_mode:s[3]},u&2&&{root:s[1]},{gradio:new No(s[0].id,s[2],s[3],s[4],s[1],s[5])}]):{};u&65551&&(f.$$scope={dirty:u,ctx:s}),!t&&u&1&&(t=!0,f.instance=s[0].instance,Jo(()=>t=!1)),!n&&u&1&&(n=!0,f.value=s[0].props.value,Jo(()=>n=!1)),e.$set(f)},i(s){l||(ct(e.$$.fragment,s),l=!0)},o(s){Lt(e.$$.fragment,s),l=!1},d(s){Dl(e,s)}}}function A_(o){}function L_(o,e,t){let{root:n}=e,{node:l}=e,{parent:i=null}=e,{target:r}=e,{theme_mode:a}=e,{version:_}=e,{autoscroll:s}=e;const u=k_();let f=[];b_(()=>{u("mount",l.id);for(const p of f)u("mount",p.id);return()=>{u("destroy",l.id);for(const p of f)u("mount",p.id)}}),w_("BLOCK_KEY",i);function d(p){Zo.call(this,o,p)}function m(p){Zo.call(this,o,p)}function k(p){o.$$.not_equal(l.instance,p)&&(l.instance=p,t(0,l),t(12,f))}function g(p){o.$$.not_equal(l.props.value,p)&&(l.props.value=p,t(0,l),t(12,f))}return o.$$set=p=>{"root"in p&&t(1,n=p.root),"node"in p&&t(0,l=p.node),"parent"in p&&t(6,i=p.parent),"target"in p&&t(2,r=p.target),"theme_mode"in p&&t(3,a=p.theme_mode),"version"in p&&t(4,_=p.version),"autoscroll"in p&&t(5,s=p.autoscroll)},o.$$.update=()=>{o.$$.dirty&1&&t(0,l.children=l.children&&l.children.filter(p=>{const c=l.type!=="statustracker";return c||f.push(p),c}),l),o.$$.dirty&1&&l.type==="form"&&(l.children?.every(p=>!p.props.visible)?t(0,l.props.visible=!1,l):t(0,l.props.visible=!0,l))},[l,n,r,a,_,s,i,d,m,k,g]}class Sl extends c_{constructor(e){super(),d_(this,e,L_,E_,h_,{root:1,node:0,parent:6,target:2,theme_mode:3,version:4,autoscroll:5})}}const{SvelteComponent:P_,create_component:O_,destroy_component:T_,init:q_,mount_component:I_,safe_not_equal:C_,transition_in:D_,transition_out:$_}=window.__gradio__svelte__internal,{onMount:R_,createEventDispatcher:S_}=window.__gradio__svelte__internal;function V_(o){let e,t;return e=new Sl({props:{node:o[0],root:o[1],target:o[2],theme_mode:o[3],version:o[4],autoscroll:o[5]}}),{c(){O_(e.$$.fragment)},m(n,l){I_(e,n,l),t=!0},p(n,[l]){const i={};l&1&&(i.node=n[0]),l&2&&(i.root=n[1]),l&4&&(i.target=n[2]),l&8&&(i.theme_mode=n[3]),l&16&&(i.version=n[4]),l&32&&(i.autoscroll=n[5]),e.$set(i)},i(n){t||(D_(e.$$.fragment,n),t=!0)},o(n){$_(e.$$.fragment,n),t=!1},d(n){T_(e,n)}}}function j_(o,e,t){let{rootNode:n}=e,{root:l}=e,{target:i}=e,{theme_mode:r}=e,{version:a}=e,{autoscroll:_}=e;const s=S_();return R_(()=>{s("mount")}),o.$$set=u=>{"rootNode"in u&&t(0,n=u.rootNode),"root"in u&&t(1,l=u.root),"target"in u&&t(2,i=u.target),"theme_mode"in u&&t(3,r=u.theme_mode),"version"in u&&t(4,a=u.version),"autoscroll"in u&&t(5,_=u.autoscroll)},[n,l,i,r,a,_]}class N_ extends P_{constructor(e){super(),q_(this,e,j_,V_,C_,{rootNode:0,root:1,target:2,theme_mode:3,version:4,autoscroll:5})}}const M_=""+new URL("logo-3707f936.svg",import.meta.url).href,z_={accordion:{component:()=>P(()=>import("./Index-d1113178.js"),["./Index-d1113178.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./Index-ab6a99fa.js","./Index-2853eb31.css","./Index-8f1feca1.css"],import.meta.url)},annotatedimage:{component:()=>P(()=>import("./Index-d17bcd64.js"),["./Index-d17bcd64.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./BlockLabel-f27805b1.js","./Empty-28f63bf0.js","./Image-eaba773f.js","./file-url-bef2dc1b.js","./Index-f0e43e7d.css"],import.meta.url)},audio:{example:()=>P(()=>import("./Example-ba6a74fc.js"),["./Example-ba6a74fc.js","./Example-98fc2b2c.css"],import.meta.url),component:()=>P(()=>import("./index-769c92f6.js"),["./index-769c92f6.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./utils-572af92b.js","./BlockLabel-f27805b1.js","./IconButton-7294c90b.js","./Empty-28f63bf0.js","./ShareButton-7dae44e7.js","./DownloadLink-7ff36416.js","./file-url-bef2dc1b.js","./Music-755043aa.js","./Trim-1b343e72.js","./Undo-b088de14.js","./ModifyUpload.svelte_svelte_type_style_lang-4ba51100.js","./ModifyUpload-13b13e32.css","./ModifyUpload-66b0c302.js","./Clear-2c7bae91.js","./SelectSource-f5281119.js","./Upload-351cc897.js","./UploadText-39c67ae9.js","./Example-ba6a74fc.js","./Example-98fc2b2c.css","./index-da1c5d77.css"],import.meta.url)},box:{component:()=>P(()=>import("./Index-0b8b008b.js"),["./Index-0b8b008b.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css"],import.meta.url)},button:{component:()=>P(()=>import("./Index-035d2c1f.js"),["./Index-035d2c1f.js","./index-a80d931b.js","./index-a889f790.css","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./Index-cf60e5e6.css","./SelectSource-ffeae268.css"],import.meta.url)},chatbot:{component:()=>P(()=>import("./Index-76012e4b.js"),["./Index-76012e4b.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./utils-572af92b.js","./index-2f00b72c.js","./ShareButton-7dae44e7.js","./IconButton-7294c90b.js","./file-url-bef2dc1b.js","./Image-21c02477.js","./Image-c2f962bb.css","./Video-fd4d29ec.js","./Video-a80c372b.css","./Index.svelte_svelte_type_style_lang-e1d4a36d.js","./Example.svelte_svelte_type_style_lang-648fc18a.js","./prism-python-b0b31d02.js","./Example-41e63572.css","./Index-edf307d2.css","./Check-965babbe.js","./Copy-b365948f.js","./BlockLabel-f27805b1.js","./Index-64282ded.css"],import.meta.url)},checkbox:{example:()=>P(()=>import("./Example-a07d2295.js"),["./Example-a07d2295.js","./Example-98fc2b2c.css"],import.meta.url),component:()=>P(()=>import("./Index-0bdb78da.js"),["./Index-0bdb78da.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./Info-84f5385d.js","./Index-51c40da3.css"],import.meta.url)},checkboxgroup:{example:()=>P(()=>import("./Example-4f39ac0e.js"),["./Example-4f39ac0e.js","./Example-98fc2b2c.css"],import.meta.url),component:()=>P(()=>import("./Index-d8c9c0b2.js"),["./Index-d8c9c0b2.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./BlockTitle-7f7c9ef8.js","./Info-84f5385d.js","./Index-8f24ac6d.css"],import.meta.url)},code:{example:()=>P(()=>import("./Example-0b8f33de.js"),["./Example-0b8f33de.js","./Example-f75cba10.css"],import.meta.url),component:()=>P(()=>import("./Index-26d165be.js").then(o=>o.F),["./Index-26d165be.js","./index-a80d931b.js","./index-a889f790.css","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./Check-965babbe.js","./Copy-b365948f.js","./DownloadLink-7ff36416.js","./file-url-bef2dc1b.js","./BlockLabel-f27805b1.js","./Empty-28f63bf0.js","./Example-0b8f33de.js","./Example-f75cba10.css","./Index-5de9d2f6.css"],import.meta.url)},colorpicker:{example:()=>P(()=>import("./Example-d088ea82.js"),["./Example-d088ea82.js","./Example-ee935b0c.css"],import.meta.url),component:()=>P(()=>import("./Index-4de14cd0.js"),["./Index-4de14cd0.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./BlockTitle-7f7c9ef8.js","./Info-84f5385d.js","./Example-d088ea82.js","./Example-ee935b0c.css","./Index-cd311153.css"],import.meta.url)},column:{component:()=>P(()=>import("./Index-ab6a99fa.js"),["./Index-ab6a99fa.js","./Index-2853eb31.css"],import.meta.url)},dataframe:{example:()=>P(()=>import("./Example-6955c51f.js"),["./Example-6955c51f.js","./Example-da1a7264.css"],import.meta.url),component:()=>P(()=>import("./Index-337282dc.js"),["./Index-337282dc.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./index-2f00b72c.js","./utils-572af92b.js","./ModifyUpload.svelte_svelte_type_style_lang-4ba51100.js","./ModifyUpload-13b13e32.css","./Index.svelte_svelte_type_style_lang-e1d4a36d.js","./Example.svelte_svelte_type_style_lang-648fc18a.js","./prism-python-b0b31d02.js","./Example-41e63572.css","./Index-edf307d2.css","./dsv-576afacd.js","./Example-6955c51f.js","./Example-da1a7264.css","./Index-83656a6f.css"],import.meta.url)},dataset:{component:()=>P(()=>import("./Index-e2226da2.js"),["./Index-e2226da2.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./Index-ca7e817a.css"],import.meta.url)},downloadbutton:{component:()=>P(()=>import("./Index-56b17309.js"),["./Index-56b17309.js","./index-a80d931b.js","./index-a889f790.css","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./Index-c40745a6.css"],import.meta.url)},dropdown:{example:()=>P(()=>import("./Example-d4392175.js"),["./Example-d4392175.js","./Example-98fc2b2c.css"],import.meta.url),component:()=>P(()=>import("./Index-a15403b4.js"),["./Index-a15403b4.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./SelectSource-ffeae268.css","./BlockTitle-7f7c9ef8.js","./Info-84f5385d.js","./DropdownArrow-bb2afb7e.js","./Example-d4392175.js","./Example-98fc2b2c.css","./Index-56683778.css"],import.meta.url)},file:{example:()=>P(()=>import("./Example-4a4da230.js"),["./Example-4a4da230.js","./Example-1cda6415.css"],import.meta.url),component:()=>P(()=>import("./Index-adf98f57.js"),["./Index-adf98f57.js","./FileUpload-1878093d.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./BlockLabel-f27805b1.js","./Empty-28f63bf0.js","./File-d0b52941.js","./ModifyUpload.svelte_svelte_type_style_lang-4ba51100.js","./ModifyUpload-13b13e32.css","./ModifyUpload-66b0c302.js","./IconButton-7294c90b.js","./Clear-2c7bae91.js","./DownloadLink-7ff36416.js","./file-url-bef2dc1b.js","./Undo-b088de14.js","./FileUpload-a4fc0425.css","./UploadText-39c67ae9.js","./Upload-351cc897.js","./Example-4a4da230.js","./Example-1cda6415.css"],import.meta.url)},fileexplorer:{example:()=>P(()=>import("./Example-4231f92e.js"),["./Example-4231f92e.js","./Example-a67e2a3d.css"],import.meta.url),component:()=>P(()=>import("./Index-8b0ec0c9.js"),["./Index-8b0ec0c9.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./File-d0b52941.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./SelectSource-ffeae268.css","./BlockLabel-f27805b1.js","./Index-ebbec2e8.css"],import.meta.url)},form:{component:()=>P(()=>import("./Index-09f26e4b.js"),["./Index-09f26e4b.js","./Index-3812b7f1.css"],import.meta.url)},gallery:{component:()=>P(()=>import("./Index-187b9380.js"),["./Index-187b9380.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./UploadText-39c67ae9.js","./Upload-351cc897.js","./BlockLabel-f27805b1.js","./IconButton-7294c90b.js","./Empty-28f63bf0.js","./ShareButton-7dae44e7.js","./utils-572af92b.js","./DownloadLink-7ff36416.js","./file-url-bef2dc1b.js","./Image-eaba773f.js","./ModifyUpload.svelte_svelte_type_style_lang-4ba51100.js","./ModifyUpload-13b13e32.css","./ModifyUpload-66b0c302.js","./Clear-2c7bae91.js","./Undo-b088de14.js","./Image-21c02477.js","./Image-c2f962bb.css","./FileUpload-1878093d.js","./File-d0b52941.js","./FileUpload-a4fc0425.css","./Index-c05a8c59.css","./Example-1cda6415.css"],import.meta.url)},group:{component:()=>P(()=>import("./Index-2d8f150a.js"),["./Index-2d8f150a.js","./Index-37519934.css"],import.meta.url)},highlightedtext:{component:()=>P(()=>import("./Index-b1c13a4a.js"),["./Index-b1c13a4a.js","./color-03a28f80.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./BlockLabel-f27805b1.js","./Empty-28f63bf0.js","./Index-e24fc675.css"],import.meta.url)},html:{example:()=>P(()=>import("./Example-ecf93e4d.js"),["./Example-ecf93e4d.js","./Example-9cefd3b7.css"],import.meta.url),component:()=>P(()=>import("./Index-5db44e2f.js"),["./Index-5db44e2f.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./SelectSource-ffeae268.css","./Index-329f8260.css"],import.meta.url)},image:{example:()=>P(()=>import("./Example-092ffb31.js"),["./Example-092ffb31.js","./Image-21c02477.js","./file-url-bef2dc1b.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./Image-c2f962bb.css","./Example-bcfbe567.css"],import.meta.url),component:()=>P(()=>import("./Index-bc5996aa.js"),["./Index-bc5996aa.js","./ImageUploader-528d6ef1.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./utils-572af92b.js","./BlockLabel-f27805b1.js","./IconButton-7294c90b.js","./Empty-28f63bf0.js","./ShareButton-7dae44e7.js","./DownloadLink-7ff36416.js","./file-url-bef2dc1b.js","./Image-eaba773f.js","./Image-21c02477.js","./Image-c2f962bb.css","./SelectSource-f5281119.js","./Upload-351cc897.js","./DropdownArrow-bb2afb7e.js","./ModifyUpload.svelte_svelte_type_style_lang-4ba51100.js","./ModifyUpload-13b13e32.css","./Clear-2c7bae91.js","./ImageUploader-b33972d3.css","./UploadText-39c67ae9.js","./Example-092ffb31.js","./Example-bcfbe567.css"],import.meta.url)},imageeditor:{example:()=>P(()=>import("./Example-40dc08d9.js"),["./Example-40dc08d9.js","./ImageUploader-528d6ef1.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./utils-572af92b.js","./BlockLabel-f27805b1.js","./IconButton-7294c90b.js","./Empty-28f63bf0.js","./ShareButton-7dae44e7.js","./DownloadLink-7ff36416.js","./file-url-bef2dc1b.js","./Image-eaba773f.js","./Image-21c02477.js","./Image-c2f962bb.css","./SelectSource-f5281119.js","./Upload-351cc897.js","./DropdownArrow-bb2afb7e.js","./ModifyUpload.svelte_svelte_type_style_lang-4ba51100.js","./ModifyUpload-13b13e32.css","./Clear-2c7bae91.js","./ImageUploader-b33972d3.css","./Example-8f5e1f39.css","./Example-bcfbe567.css"],import.meta.url),component:()=>P(()=>import("./Index-f1ffe7dc.js"),["./Index-f1ffe7dc.js","./ImageUploader-528d6ef1.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./utils-572af92b.js","./BlockLabel-f27805b1.js","./IconButton-7294c90b.js","./Empty-28f63bf0.js","./ShareButton-7dae44e7.js","./DownloadLink-7ff36416.js","./file-url-bef2dc1b.js","./Image-eaba773f.js","./Image-21c02477.js","./Image-c2f962bb.css","./SelectSource-f5281119.js","./Upload-351cc897.js","./DropdownArrow-bb2afb7e.js","./ModifyUpload.svelte_svelte_type_style_lang-4ba51100.js","./ModifyUpload-13b13e32.css","./Clear-2c7bae91.js","./ImageUploader-b33972d3.css","./__vite-browser-external-d8e3c0c7.js","./Check-965babbe.js","./Undo-b088de14.js","./Index-4d721543.css","./Example-bcfbe567.css"],import.meta.url)},json:{component:()=>P(()=>import("./Index-01713e13.js"),["./Index-01713e13.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./Check-965babbe.js","./Copy-b365948f.js","./Empty-28f63bf0.js","./BlockLabel-f27805b1.js","./Index-b658ebcd.css"],import.meta.url)},label:{component:()=>P(()=>import("./Index-d137a234.js"),["./Index-d137a234.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./SelectSource-ffeae268.css","./BlockLabel-f27805b1.js","./Empty-28f63bf0.js","./Index-d4781e2f.css"],import.meta.url)},markdown:{example:()=>P(()=>import("./Example-49f98efe.js"),["./Example-49f98efe.js","./Example.svelte_svelte_type_style_lang-648fc18a.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./prism-python-b0b31d02.js","./Example-41e63572.css"],import.meta.url),component:()=>P(()=>import("./Index-f45b736b.js"),["./Index-f45b736b.js","./Index.svelte_svelte_type_style_lang-e1d4a36d.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./utils-572af92b.js","./Example.svelte_svelte_type_style_lang-648fc18a.js","./prism-python-b0b31d02.js","./Example-41e63572.css","./Index-edf307d2.css","./Example-49f98efe.js"],import.meta.url)},model3d:{example:()=>P(()=>import("./Example-4f6d17e3.js"),["./Example-4f6d17e3.js","./Example-98fc2b2c.css"],import.meta.url),component:()=>P(()=>import("./Index-2cd93917.js"),["./Index-2cd93917.js","./index-a80d931b.js","./index-a889f790.css","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./BlockLabel-f27805b1.js","./IconButton-7294c90b.js","./DownloadLink-7ff36416.js","./file-url-bef2dc1b.js","./File-d0b52941.js","./Undo-b088de14.js","./ModifyUpload.svelte_svelte_type_style_lang-4ba51100.js","./ModifyUpload-13b13e32.css","./ModifyUpload-66b0c302.js","./Clear-2c7bae91.js","./Empty-28f63bf0.js","./UploadText-39c67ae9.js","./Upload-351cc897.js","./Example-4f6d17e3.js","./Example-98fc2b2c.css","./Index-324ab27d.css"],import.meta.url)},multimodaltextbox:{example:()=>P(()=>import("./Example-f2263091.js"),["./Example-f2263091.js","./Image-21c02477.js","./file-url-bef2dc1b.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./Image-c2f962bb.css","./Example-4820cb27.css"],import.meta.url),component:()=>P(()=>import("./Index-0a194bc1.js"),["./Index-0a194bc1.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./BlockTitle-7f7c9ef8.js","./Info-84f5385d.js","./Clear-2c7bae91.js","./File-d0b52941.js","./Music-755043aa.js","./Video-8670328f.js","./ModifyUpload.svelte_svelte_type_style_lang-4ba51100.js","./ModifyUpload-13b13e32.css","./Image-21c02477.js","./file-url-bef2dc1b.js","./Image-c2f962bb.css","./Example-f2263091.js","./Example-4820cb27.css","./Index-925db390.css"],import.meta.url)},number:{example:()=>P(()=>import("./Example-cdade505.js"),["./Example-cdade505.js","./Example-98fc2b2c.css"],import.meta.url),component:()=>P(()=>import("./Index-3c20b113.js"),["./Index-3c20b113.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./BlockTitle-7f7c9ef8.js","./Info-84f5385d.js","./Index-76c3ee3f.css"],import.meta.url)},paramviewer:{example:()=>P(()=>import("./Example-9dc9204b.js"),["./Example-9dc9204b.js","./Example-98fc2b2c.css"],import.meta.url),component:()=>P(()=>import("./Index-a60f3b6d.js"),["./Index-a60f3b6d.js","./prism-python-b0b31d02.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./Index-dfb7e8b0.css"],import.meta.url)},plot:{component:()=>P(()=>import("./Index-605ce95b.js"),["./Index-605ce95b.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./SelectSource-ffeae268.css","./color-03a28f80.js","./dsv-576afacd.js","./Empty-28f63bf0.js","./BlockLabel-f27805b1.js","./Index-61df0c8a.css"],import.meta.url)},radio:{example:()=>P(()=>import("./Example-aaefd914.js"),["./Example-aaefd914.js","./Example-98fc2b2c.css"],import.meta.url),component:()=>P(()=>import("./Index-7fa6c49c.js"),["./Index-7fa6c49c.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./BlockTitle-7f7c9ef8.js","./Info-84f5385d.js","./Example-aaefd914.js","./Example-98fc2b2c.css","./Index-7f696158.css"],import.meta.url)},row:{component:()=>P(()=>import("./Index-0a208ea4.js"),["./Index-0a208ea4.js","./Index-93c91554.css"],import.meta.url)},slider:{example:()=>P(()=>import("./Example-59243695.js"),["./Example-59243695.js","./Example-98fc2b2c.css"],import.meta.url),component:()=>P(()=>import("./Index-9f1fe3ac.js"),["./Index-9f1fe3ac.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./BlockTitle-7f7c9ef8.js","./Info-84f5385d.js","./Index-8c6a6015.css"],import.meta.url)},state:{component:()=>P(()=>import("./Index-79eb3848.js"),[],import.meta.url)},statustracker:{component:()=>P(()=>import("./index-858a8699.js"),["./index-858a8699.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./SelectSource-ffeae268.css"],import.meta.url)},tabitem:{component:()=>P(()=>import("./Index-5bbd95b2.js"),["./Index-5bbd95b2.js","./Tabs-9b11644c.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./Tabs-a57f85ff.css","./Index-ab6a99fa.js","./Index-2853eb31.css","./Index-d43fcb36.css"],import.meta.url)},tabs:{component:()=>P(()=>import("./Index-112eba4d.js"),["./Index-112eba4d.js","./Tabs-9b11644c.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./Tabs-a57f85ff.css"],import.meta.url)},textbox:{example:()=>P(()=>import("./Example-baada562.js"),["./Example-baada562.js","./Example-6ded08d8.css"],import.meta.url),component:()=>P(()=>import("./Index-6c522bb4.js"),["./Index-6c522bb4.js","./Textbox-1709c01b.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./BlockTitle-7f7c9ef8.js","./Info-84f5385d.js","./Check-965babbe.js","./Copy-b365948f.js","./Textbox-dde6f8cc.css","./Example-baada562.js","./Example-6ded08d8.css"],import.meta.url)},uploadbutton:{component:()=>P(()=>import("./Index-3bd93696.js"),["./Index-3bd93696.js","./index-a80d931b.js","./index-a889f790.css","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./Index-26cfc80a.js","./Index-cf60e5e6.css","./SelectSource-ffeae268.css","./Index-efd1eb27.css"],import.meta.url)},video:{example:()=>P(()=>import("./Example-f78abbcc.js"),["./Example-f78abbcc.js","./Video-fd4d29ec.js","./file-url-bef2dc1b.js","./Index-26cfc80a.js","./index-a80d931b.js","./index-a889f790.css","./Index-cf60e5e6.css","./Video-a80c372b.css","./Example-9429a5cb.css"],import.meta.url),component:()=>P(()=>import("./index-82184621.js"),["./index-82184621.js","./ModifyUpload.svelte_svelte_type_style_lang-4ba51100.js","./index-a80d931b.js","./index-a889f790.css","./Index-26cfc80a.js","./Index-cf60e5e6.css","./ModifyUpload-13b13e32.css","./ModifyUpload-66b0c302.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./SelectSource-ffeae268.css","./IconButton-7294c90b.js","./Clear-2c7bae91.js","./DownloadLink-7ff36416.js","./file-url-bef2dc1b.js","./Undo-b088de14.js","./BlockLabel-f27805b1.js","./Video-8670328f.js","./SelectSource-f5281119.js","./Upload-351cc897.js","./ImageUploader-528d6ef1.js","./utils-572af92b.js","./Empty-28f63bf0.js","./ShareButton-7dae44e7.js","./Image-eaba773f.js","./Image-21c02477.js","./Image-c2f962bb.css","./DropdownArrow-bb2afb7e.js","./ImageUploader-b33972d3.css","./Video-fd4d29ec.js","./Video-a80c372b.css","./Trim-1b343e72.js","./Example-f78abbcc.js","./Example-9429a5cb.css","./UploadText-39c67ae9.js","./index-5522f595.css","./Example-bcfbe567.css"],import.meta.url)}},Ge={};function tl({api_url:o,name:e,id:t,variant:n}){const l=window.__GRADIO__CC__,i={...z_,...l||{}};if(Ge[`${t}-${n}`])return{component:Ge[`${t}-${n}`],name:e};try{if(!i?.[t]?.[n]&&!i?.[e]?.[n])throw new Error;return Ge[`${t}-${n}`]=(i?.[t]?.[n]||i?.[e]?.[n])(),{name:e,component:Ge[`${t}-${n}`]}}catch{try{return Ge[`${t}-${n}`]=U_(o,t,n),{name:e,component:Ge[`${t}-${n}`]}}catch(a){if(n==="example")return Ge[`${t}-${n}`]=P(()=>import("./Example-1fe376d1.js"),["./Example-1fe376d1.js","./Example-98fc2b2c.css"],import.meta.url),{name:e,component:Ge[`${t}-${n}`]};throw console.error(`failed to load: ${e}`),console.error(a),a}}}function B_(o){return new Promise((e,t)=>{const n=document.createElement("link");n.rel="stylesheet",n.href=o,document.head.appendChild(n),n.onload=()=>e(),n.onerror=()=>t()})}function U_(o,e,t){return Promise.all([B_(`${o}/custom_component/${e}/${t}/style.css`),P(()=>import(`${o}/custom_component/${e}/${t}/index.js`),[],import.meta.url)]).then(([n,l])=>l)}function F_(){const o=St({}),e=[],t=[],n=new Map,l=new Map,i=new Map,r=[];function a({fn_index:s,status:u,queue:f=!0,size:d,position:m=null,eta:k=null,message:g=null,progress:p}){const c=t[s],E=e[s],h=r[s],b=c.map(w=>{let L;const v=n.get(w)||0;if(h==="pending"&&u!=="pending"){let I=v-1;n.set(w,I<0?0:I),L=I>0?"pending":u}else h==="pending"&&u==="pending"?L="pending":h!=="pending"&&u==="pending"?(L="pending",n.set(w,v+1)):L=u;return{id:w,queue_position:m,queue_size:d,eta:k,status:L,message:g,progress:p}});E.forEach(w=>{const L=l.get(w)||0;if(h==="pending"&&u!=="pending"){let v=L-1;l.set(w,v<0?0:v),i.set(w,u)}else h!=="pending"&&u==="pending"?(l.set(w,L+1),i.set(w,u)):i.delete(w)}),o.update(w=>(b.forEach(({id:L,queue_position:v,queue_size:I,eta:R,status:A,message:T,progress:B})=>{w[L]={queue:f,queue_size:I,queue_position:v,eta:R,message:T,progress:B,status:A,fn_index:s}}),w)),r[s]=u}function _(s,u,f){e[s]=u,t[s]=f}return{update:a,register:_,subscribe:o.subscribe,get_status_for_fn(s){return r[s]},get_inputs_to_update(){return i}}}let lt=[];function H_(){let o,e=St({}),t={},n,l,i,r,a=F_();const _=St();let s,u,f;function d({app:h,components:b,layout:w,dependencies:L,root:v,options:I}){f=h,u=b,n=new Set,l=new Set,lt=[],i=new Map,o=new Map,r={};const R={id:w.id,type:"column",props:{interactive:!1,scale:I.fill_height?1:null},has_modes:!1,instance:null,component:null,component_class_id:""};b.push(R),L.forEach((A,T)=>{a.register(T,A.inputs,A.outputs),A.frontend_fn=W_(A.js,!!A.backend_fn,A.inputs.length,A.outputs.length),G_(A.targets,T,t),J_(A,n,l)}),e.set(t),i=Y_(b,v),r=b.reduce((A,T)=>(A[T.id]=T,A),{}),m(w).then(()=>{_.set(R)})}async function m(h){const b=r[h.id];return b.component=(await i.get(b.component_class_id))?.default,b.type==="dataset"&&(b.props.component_map=jl(b.type,b.component_class_id,s,u,b.props.components).example_components),t[b.id]&&(b.props.attached_events=Object.keys(t[b.id])),b.props.interactive=K_(b.id,b.props.interactive,b.props.value,n,l),b.props.server=Z_(b.id,b.props.server_fns,f),o.set(b.id,b),h.children&&(b.children=await Promise.all(h.children.map(w=>m(w)))),b}let k=!1,g=St(!1);function p(){_.update(h=>{for(let b=0;b<lt.length;b++)for(let w=0;w<lt[b].length;w++){const L=lt[b][w],v=r[L.id];if(!v)continue;let I;Array.isArray(L.value)?I=[...L.value]:L.value===null?I=null:typeof L.value=="object"?I={...L.value}:I=L.value,v.props[L.prop]=I}return h}),lt=[],k=!1,g.set(!1)}function c(h){h&&(lt.push(h),k||(k=!0,g.set(!0),requestAnimationFrame(p)))}function E(h){const b=o.get(h);return b?b.instance.get_value?b.instance.get_value():b.props.value:null}return{layout:_,targets:e,update_value:c,get_data:E,loading_status:a,scheduled_updates:g,create_layout:(...h)=>requestAnimationFrame(()=>d(...h))}}const Vl=Object.getPrototypeOf(async function(){}).constructor;function W_(o,e,t,n){if(!o)return null;const l=e?t===1:n===1;try{return new Vl("__fn_args",`  let result = await (${o})(...__fn_args);
  return (${l} && !Array.isArray(result)) ? [result] : result;`)}catch(i){return console.error("Could not parse custom js method."),console.error(i),null}}function G_(o,e,t){return o.forEach(([n,l])=>{t[n]||(t[n]={}),t[n]?.[l]&&!t[n]?.[l].includes(e)?t[n][l].push(e):t[n][l]=[e]}),t}function J_(o,e,t){return o.inputs.forEach(n=>e.add(n)),o.outputs.forEach(n=>t.add(n)),[e,t]}function Q_(o){return Array.isArray(o)&&o.length===0||o===""||o===0||!o}function K_(o,e,t,n,l){return e===!1?!1:e===!0?!0:!!(n.has(o)||!l.has(o)&&Q_(t))}function Z_(o,e,t){return e?e.reduce((n,l)=>(n[l]=async(...i)=>(i.length===1&&(i=i[0]),await t.component_server(o,l,i)),n),{}):{}}function jl(o,e,t,n,l){let i=new Map;o==="dataset"&&l&&l.forEach(a=>{if(i.has(a))return;let _;const s=n.find(u=>u.type===a);s&&(_=tl({api_url:t,name:a,id:s.component_class_id,variant:"example"}),i.set(a,_.component))});const r=tl({api_url:t,name:o,id:e,variant:"component"});return{component:r.component,name:r.name,example_components:i.size>0?i:void 0}}function Y_(o,e){let t=new Map;return o.forEach(n=>{const{component:l,example_components:i}=jl(n.type,n.component_class_id,e,o);if(t.set(n.component_class_id,l),i)for(const[r,a]of i)t.set(r,a)}),t}const{SvelteComponent:X_,append:de,attr:Y,check_outros:nn,component_subscribe:ht,create_component:Rn,destroy_component:Sn,detach:pe,element:Oe,empty:on,globals:x_,group_outros:ln,init:eu,insert:Le,listen:Nl,mount_component:Vn,noop:rn,safe_not_equal:tu,set_data:Ml,set_style:Rt,space:Ve,src_url_equal:jn,text:zl,transition_in:qe,transition_out:Ne}=window.__gradio__svelte__internal,{document:bt}=x_,{tick:sn}=window.__gradio__svelte__internal;function nl(o){return bt.title=o[2],{c:rn,m:rn,d:rn}}function ol(o){let e,t,n,l;return{c(){e=Oe("script"),e.innerHTML="",n=Ve(),l=Oe("script"),l.textContent=`window.dataLayer = window.dataLayer || [];
			function gtag() {
				dataLayer.push(arguments);
			}
			gtag("js", new Date());
			gtag("config", "UA-156449732-1", {
				cookie_flags: "samesite=none;secure"
			});`,e.async=!0,e.defer=!0,jn(e.src,t="https://www.googletagmanager.com/gtag/js?id=UA-156449732-1")||Y(e,"src",t)},m(i,r){Le(i,e,r),Le(i,n,r),Le(i,l,r)},d(i){i&&(pe(e),pe(n),pe(l))}}}function ll(o){let e,t;return e=new N_({props:{rootNode:o[14],root:o[0],target:o[4],theme_mode:o[10],version:o[13],autoscroll:o[5]}}),e.$on("mount",o[24]),e.$on("destroy",o[33]),{c(){Rn(e.$$.fragment)},m(n,l){Vn(e,n,l),t=!0},p(n,l){const i={};l[0]&16384&&(i.rootNode=n[14]),l[0]&1&&(i.root=n[0]),l[0]&16&&(i.target=n[4]),l[0]&1024&&(i.theme_mode=n[10]),l[0]&8192&&(i.version=n[13]),l[0]&32&&(i.autoscroll=n[5]),e.$set(i)},i(n){t||(qe(e.$$.fragment,n),t=!0)},o(n){Ne(e.$$.fragment,n),t=!1},d(n){Sn(e,n)}}}function il(o){let e,t,n,l=o[17]("common.built_with_gradio")+"",i,r,a,_,s,u=o[6]&&rl(o);return{c(){e=Oe("footer"),u&&u.c(),t=Ve(),n=Oe("a"),i=zl(l),r=Ve(),a=Oe("img"),jn(a.src,_=M_)||Y(a,"src",_),Y(a,"alt",s=o[17]("common.logo")),Y(a,"class","svelte-16bt5n8"),Y(n,"href","https://gradio.app"),Y(n,"class","built-with svelte-16bt5n8"),Y(n,"target","_blank"),Y(n,"rel","noreferrer"),Y(e,"class","svelte-16bt5n8")},m(f,d){Le(f,e,d),u&&u.m(e,null),de(e,t),de(e,n),de(n,i),de(n,r),de(n,a)},p(f,d){f[6]?u?u.p(f,d):(u=rl(f),u.c(),u.m(e,t)):u&&(u.d(1),u=null),d[0]&131072&&l!==(l=f[17]("common.built_with_gradio")+"")&&Ml(i,l),d[0]&131072&&s!==(s=f[17]("common.logo"))&&Y(a,"alt",s)},d(f){f&&pe(e),u&&u.d()}}}function rl(o){let e,t=o[17]("errors.use_via_api")+"",n,l,i,r,a,_,s,u,f;return{c(){e=Oe("button"),n=zl(t),l=Ve(),i=Oe("img"),_=Ve(),s=Oe("div"),s.textContent="·",jn(i.src,r=kl)||Y(i,"src",r),Y(i,"alt",a=o[17]("common.logo")),Y(i,"class","svelte-16bt5n8"),Y(e,"class","show-api svelte-16bt5n8"),Y(s,"class","svelte-16bt5n8")},m(d,m){Le(d,e,m),de(e,n),de(e,l),de(e,i),Le(d,_,m),Le(d,s,m),u||(f=Nl(e,"click",o[34]),u=!0)},p(d,m){m[0]&131072&&t!==(t=d[17]("errors.use_via_api")+"")&&Ml(n,t),m[0]&131072&&a!==(a=d[17]("common.logo"))&&Y(i,"alt",a)},d(d){d&&(pe(e),pe(_),pe(s)),u=!1,f()}}}function sl(o){let e,t,n,l,i,r,a,_;return i=new Fa({props:{root_node:o[14],dependencies:o[1],root:o[0],app:o[11],space_id:o[12]}}),i.$on("close",o[36]),{c(){e=Oe("div"),t=Oe("div"),n=Ve(),l=Oe("div"),Rn(i.$$.fragment),Y(t,"class","backdrop svelte-16bt5n8"),Y(l,"class","api-docs-wrap svelte-16bt5n8"),Y(e,"class","api-docs svelte-16bt5n8")},m(s,u){Le(s,e,u),de(e,t),de(e,n),de(e,l),Vn(i,l,null),r=!0,a||(_=Nl(t,"click",o[35]),a=!0)},p(s,u){const f={};u[0]&16384&&(f.root_node=s[14]),u[0]&2&&(f.dependencies=s[1]),u[0]&1&&(f.root=s[0]),u[0]&2048&&(f.app=s[11]),u[0]&4096&&(f.space_id=s[12]),i.$set(f)},i(s){r||(qe(i.$$.fragment,s),r=!0)},o(s){Ne(i.$$.fragment,s),r=!1},d(s){s&&pe(e),Sn(i),a=!1,_()}}}function al(o){let e,t;return e=new dr({props:{messages:o[16]}}),e.$on("close",o[23]),{c(){Rn(e.$$.fragment)},m(n,l){Vn(e,n,l),t=!0},p(n,l){const i={};l[0]&65536&&(i.messages=n[16]),e.$set(i)},i(n){t||(qe(e.$$.fragment,n),t=!0)},o(n){Ne(e.$$.fragment,n),t=!1},d(n){Sn(e,n)}}}function nu(o){let e,t,n,l,i,r,a,_,s,u,f=o[8]&&nl(o),d=o[3]&&ol(),m=o[14]&&ll(o),k=o[7]&&il(o),g=o[15]&&o[14]&&sl(o),p=o[16]&&al(o);return{c(){f&&f.c(),e=on(),d&&d.c(),t=on(),n=Ve(),l=Oe("div"),i=Oe("div"),m&&m.c(),r=Ve(),k&&k.c(),a=Ve(),g&&g.c(),_=Ve(),p&&p.c(),s=on(),Y(i,"class","contain svelte-16bt5n8"),Rt(i,"flex-grow",o[9]?"1":"auto"),Y(l,"class","wrap svelte-16bt5n8"),Rt(l,"min-height",o[9]?"100%":"auto")},m(c,E){f&&f.m(bt.head,null),de(bt.head,e),d&&d.m(bt.head,null),de(bt.head,t),Le(c,n,E),Le(c,l,E),de(l,i),m&&m.m(i,null),de(l,r),k&&k.m(l,null),Le(c,a,E),g&&g.m(c,E),Le(c,_,E),p&&p.m(c,E),Le(c,s,E),u=!0},p(c,E){c[8]?f||(f=nl(c),f.c(),f.m(e.parentNode,e)):f&&(f.d(1),f=null),c[3]?d||(d=ol(),d.c(),d.m(t.parentNode,t)):d&&(d.d(1),d=null),c[14]?m?(m.p(c,E),E[0]&16384&&qe(m,1)):(m=ll(c),m.c(),qe(m,1),m.m(i,null)):m&&(ln(),Ne(m,1,1,()=>{m=null}),nn()),E[0]&512&&Rt(i,"flex-grow",c[9]?"1":"auto"),c[7]?k?k.p(c,E):(k=il(c),k.c(),k.m(l,null)):k&&(k.d(1),k=null),E[0]&512&&Rt(l,"min-height",c[9]?"100%":"auto"),c[15]&&c[14]?g?(g.p(c,E),E[0]&49152&&qe(g,1)):(g=sl(c),g.c(),qe(g,1),g.m(_.parentNode,_)):g&&(ln(),Ne(g,1,1,()=>{g=null}),nn()),c[16]?p?(p.p(c,E),E[0]&65536&&qe(p,1)):(p=al(c),p.c(),qe(p,1),p.m(s.parentNode,s)):p&&(ln(),Ne(p,1,1,()=>{p=null}),nn())},i(c){u||(qe(m),qe(g),qe(p),u=!0)},o(c){Ne(m),Ne(g),Ne(p),u=!1},d(c){c&&(pe(n),pe(l),pe(a),pe(_),pe(s)),f&&f.d(c),pe(e),d&&d.d(c),pe(t),m&&m.d(),k&&k.d(),g&&g.d(c),p&&p.d(c)}}}const ou=/^'([^]+)'$/,lu=15,iu=10;function _l(o){return"detail"in o}function ru(o,e,t){let n,l,i,r,a;ht(o,vn,y=>t(17,r=y)),ti();let{root:_}=e,{components:s}=e,{layout:u}=e,{dependencies:f}=e,{title:d="Gradio"}=e,{analytics_enabled:m=!1}=e,{target:k}=e,{autoscroll:g}=e,{show_api:p=!0}=e,{show_footer:c=!0}=e,{control_page_title:E=!1}=e,{app_mode:h}=e,{theme_mode:b}=e,{app:w}=e,{space_id:L}=e,{version:v}=e,{js:I}=e,{fill_height:R=!1}=e,{ready:A}=e;const{layout:T,targets:B,update_value:ne,get_data:V,loading_status:S,scheduled_updates:q,create_layout:M}=H_();ht(o,T,y=>t(14,a=y)),ht(o,B,y=>t(42,l=y)),ht(o,S,y=>t(32,n=y)),ht(o,q,y=>t(43,i=y));let H=new URLSearchParams(window.location.search).get("view")==="api"&&p;function nt(y){t(15,H=y);let G=new URLSearchParams(window.location.search);y?G.set("view","api"):G.delete("view"),history.replaceState(null,"","?"+G.toString())}let{render_complete:C=!1}=e;async function Z(y,G){const U=f[G].outputs,z=y?.map((W,_e)=>({id:U[_e],prop:"value_is_output",value:!0}));ne(z),await sn();const X=[];y?.forEach((W,_e)=>{if(typeof W=="object"&&W!==null&&W.__type__==="update")for(const[Q,Be]of Object.entries(W))Q!=="__type__"&&X.push({id:U[_e],prop:Q,value:Be});else X.push({id:U[_e],prop:"value",value:W})}),ne(X),await sn()}let fe=new Map,mt=[],$=[];function Te(y,G,U){return{message:y,fn_index:G,type:U,id:++Bl}}let Bl=-1,Ht=!1;document.addEventListener("visibilitychange",function(){document.visibilityState==="hidden"&&(Ht=!0)});const Ul=r("blocks.long_requests_queue"),Fl=r("blocks.connection_can_break"),Hl=r("blocks.lost_connection"),Nn=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);let Mn=!1,zn=!1;function dt(y,G=null,U=null){let z=()=>{};function X(){z()}i?z=q.subscribe(W=>{W||(Bn(y,G,U),X())}):Bn(y,G,U)}async function Bn(y,G=null,U=null){let z=f[y];const X=S.get_status_for_fn(y);t(16,$=$.filter(({fn_index:Q})=>Q!==y)),z.cancels&&await Promise.all(z.cancels.map(async Q=>{const Be=fe.get(Q);return Be?.cancel(),Be})),(X==="pending"||X==="generating")&&(z.pending_request=!0);let W={fn_index:y,data:await Promise.all(z.inputs.map(Q=>V(Q))),event_data:z.collects_event_data?U:null,trigger_id:G};z.frontend_fn?z.frontend_fn(W.data.concat(await Promise.all(z.inputs.map(Q=>V(Q))))).then(Q=>{z.backend_fn?(W.data=Q,_e(W)):Z(Q,y)}):z.backend_fn&&(z.trigger_mode==="once"?z.pending_request||_e(W):z.trigger_mode==="multiple"?_e(W):z.trigger_mode==="always_last"&&(z.pending_request?z.final_event=W:_e(W)));async function _e(Q){const Be=w.submit(Q.fn_index,Q.data,Q.event_data,Q.trigger_id).on("data",({data:Ie,fn_index:oe})=>{z.pending_request&&z.final_event&&(z.pending_request=!1,_e(z.final_event)),z.pending_request=!1,Z(Ie,oe),Wt(n)}).on("status",({fn_index:Ie,...oe})=>{if(S.update({...oe,status:oe.stage,progress:oe.progress_data,fn_index:Ie}),Wt(n),!Mn&&L!==null&&oe.position!==void 0&&oe.position>=2&&oe.eta!==void 0&&oe.eta>lu&&(Mn=!0,t(16,$=[Te(Ul,Ie,"warning"),...$])),!zn&&Nn&&oe.eta!==void 0&&oe.eta>iu&&(zn=!0,t(16,$=[Te(Fl,Ie,"warning"),...$])),oe.stage==="complete"&&(f.map(async(Ue,qt)=>{Ue.trigger_after===Ie&&dt(qt,Q.trigger_id)}),Be.destroy()),oe.broken&&Nn&&Ht)window.setTimeout(()=>{t(16,$=[Te(Hl,Ie,"error"),...$])},0),dt(y,Q.trigger_id,U),Ht=!1;else if(oe.stage==="error"){if(oe.message){const Ue=oe.message.replace(ou,(qt,xl)=>xl);t(16,$=[Te(Ue,Ie,"error"),...$])}f.map(async(Ue,qt)=>{Ue.trigger_after===Ie&&!Ue.trigger_only_on_success&&dt(qt,Q.trigger_id)}),Be.destroy()}}).on("log",({log:Ie,fn_index:oe,level:Ue})=>{t(16,$=[Te(Ie,oe,Ue),...$])});fe.set(y,Be)}}function Wl(y,G){if(L===null)return;const U=new URL(`https://huggingface.co/spaces/${L}/discussions/new`);y!==void 0&&y.length>0&&U.searchParams.set("title",y),U.searchParams.set("description",G),window.open(U.toString(),"_blank")}function Gl(y){const G=y.detail;t(16,$=$.filter(U=>U.id!==G))}const Jl=y=>!!(y&&new URL(y,location.href).origin!==location.origin);async function Ql(){I&&await new Vl(`let result = await (${I})();
					return (!Array.isArray(result)) ? [result] : result;`)(),await sn();for(var y=k.getElementsByTagName("a"),G=0;G<y.length;G++){const U=y[G].getAttribute("target"),z=y[G].getAttribute("href");Jl(z)&&U!=="_blank"&&y[G].setAttribute("target","_blank")}f.forEach((U,z)=>{U.targets[0][1]==="load"&&dt(z)}),!C&&(k.addEventListener("prop_change",U=>{if(!_l(U))throw new Error("not a custom event");const{id:z,prop:X,value:W}=U.detail;ne([{id:z,prop:X,value:W}])}),k.addEventListener("gradio",U=>{if(!_l(U))throw new Error("not a custom event");const{id:z,event:X,data:W}=U.detail;if(X==="share"){const{title:_e,description:Q}=W;Wl(_e,Q)}else X==="error"||X==="warning"?t(16,$=[Te(W,-1,X),...$]):l[z]?.[X]?.forEach(Q=>{requestAnimationFrame(()=>{dt(Q,z,W)})})}),t(27,C=!0))}function Un(y){mt=mt.map(G=>G.filter(U=>U!==y))}function Wt(y){const G=Object.entries(y).map(([X,W])=>{let _e=f[W.fn_index];return W.scroll_to_output=_e.scroll_to_output,W.show_progress=_e.show_progress,{id:parseInt(X),prop:"loading_status",value:W}}),U=S.get_inputs_to_update(),z=Array.from(U).map(([X,W])=>({id:X,prop:"pending",value:W==="pending"}));ne([...G,...z])}const Kl=({detail:y})=>Un(y),Zl=()=>{nt(!H)},Yl=()=>{nt(!1)},Xl=()=>{nt(!1)};return o.$$set=y=>{"root"in y&&t(0,_=y.root),"components"in y&&t(28,s=y.components),"layout"in y&&t(29,u=y.layout),"dependencies"in y&&t(1,f=y.dependencies),"title"in y&&t(2,d=y.title),"analytics_enabled"in y&&t(3,m=y.analytics_enabled),"target"in y&&t(4,k=y.target),"autoscroll"in y&&t(5,g=y.autoscroll),"show_api"in y&&t(6,p=y.show_api),"show_footer"in y&&t(7,c=y.show_footer),"control_page_title"in y&&t(8,E=y.control_page_title),"app_mode"in y&&t(9,h=y.app_mode),"theme_mode"in y&&t(10,b=y.theme_mode),"app"in y&&t(11,w=y.app),"space_id"in y&&t(12,L=y.space_id),"version"in y&&t(13,v=y.version),"js"in y&&t(30,I=y.js),"fill_height"in y&&t(31,R=y.fill_height),"ready"in y&&t(26,A=y.ready),"render_complete"in y&&t(27,C=y.render_complete)},o.$$.update=()=>{o.$$.dirty[0]&805308419|o.$$.dirty[1]&1&&M({components:s,layout:u,dependencies:f,root:_,app:w,options:{fill_height:R}}),o.$$.dirty[0]&16384&&t(26,A=!!a),o.$$.dirty[1]&2&&Wt(n)},[_,f,d,m,k,g,p,c,E,h,b,w,L,v,a,H,$,r,T,B,S,q,nt,Gl,Ql,Un,A,C,s,u,I,R,n,Kl,Zl,Yl,Xl]}class su extends X_{constructor(e){super(),eu(this,e,ru,nu,tu,{root:0,components:28,layout:29,dependencies:1,title:2,analytics_enabled:3,target:4,autoscroll:5,show_api:6,show_footer:7,control_page_title:8,app_mode:9,theme_mode:10,app:11,space_id:12,version:13,js:30,fill_height:31,ready:26,render_complete:27},null,[-1,-1])}}const pu=Object.freeze(Object.defineProperty({__proto__:null,default:su},Symbol.toStringTag,{value:"Module"}));export{pu as B,dr as T};
//# sourceMappingURL=Blocks-3011bbe9.js.map
