{"version": 3, "mappings": "mtDAAAA,GAeAC,EAAAC,EAAAC,CAAA,EAPEC,GAMCF,EAAAG,CAAA,o1BCdHL,GAeAC,EAAAC,EAAAC,CAAA,EAPEC,GAMCF,EAAAG,CAAA,yUCJK,YAAU,6vBAVlBL,GAoBAC,EAAAC,EAAAC,CAAA,EAVCC,GAAwBF,EAAAI,CAAA,UACxBF,GAKEF,EAAAK,CAAA,EAAAH,GAGAF,EAAAM,CAAA,0UCTK,aAAW,w5BAVnBR,GAyBAC,EAAAC,EAAAC,CAAA,EAfCC,GAAyBF,EAAAI,CAAA,UACzBF,GAGEF,EAAAK,CAAA,EAAAH,GAKAF,EAAAM,CAAA,EAAAJ,GAKAF,EAAAO,CAAA,gVCdK,cAAY,+6BAVpBT,GA0BAC,EAAAC,EAAAC,CAAA,EAhBCC,GAA0BF,EAAAI,CAAA,UAC1BF,GAUCF,EAAAQ,CAAA,EATCN,GAKCM,EAAAH,CAAA,EAAAH,GAGAM,EAAAF,CAAA,EACFJ,GAIAF,EAAAS,CAAA,EAHCP,GAEAO,EAAAC,CAAA,EADCR,GAA4CQ,EAAAC,CAAA,uGCvBhD,IAAIC,GAAaC,YAAQA,WAAK,WAAc,SAAUC,EAASC,EAAYC,EAAGC,EAAW,CACrF,SAASC,EAAMC,EAAO,CAAE,OAAOA,aAAiBH,EAAIG,EAAQ,IAAIH,EAAE,SAAUI,EAAS,CAAEA,EAAQD,CAAK,CAAE,CAAE,CAAI,CAC5G,OAAO,IAAKH,IAAMA,EAAI,UAAU,SAAUI,EAASC,EAAQ,CACvD,SAASC,EAAUH,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,KAAKE,CAAK,CAAC,CAAE,OAAUK,EAAP,CAAYH,EAAOG,CAAC,EAAM,CAC3F,SAASC,EAASN,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,MAASE,CAAK,CAAC,CAAI,OAAQK,EAAP,CAAYH,EAAOG,CAAC,EAAM,CAC9F,SAASD,EAAKG,EAAQ,CAAEA,EAAO,KAAON,EAAQM,EAAO,KAAK,EAAIR,EAAMQ,EAAO,KAAK,EAAE,KAAKJ,EAAWG,CAAQ,CAAI,CAC9GF,GAAMN,EAAYA,EAAU,MAAMH,EAASC,GAAc,CAAE,IAAG,KAAI,CAAE,CAC5E,CAAK,CACL,EAEA,SAASY,GAAOC,EAAWC,EAAY,CACnC,OAAOjB,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAChD,MAAMkB,EAAW,IAAI,aAAa,CAAE,WAAAD,CAAY,GAEhD,OADeC,EAAS,gBAAgBF,CAAS,EACnC,QAAQ,IAAME,EAAS,MAAO,EACpD,CAAK,CACL,CAEA,SAASC,GAAUC,EAAa,CAC5B,MAAMC,EAAeD,EAAY,CAAC,EAClC,GAAIC,EAAa,KAAMC,GAAMA,EAAI,GAAKA,EAAI,EAAE,EAAG,CAC3C,MAAMC,EAASF,EAAa,OAC5B,IAAIG,EAAM,EACV,QAASC,EAAI,EAAGA,EAAIF,EAAQE,IAAK,CAC7B,MAAMC,EAAO,KAAK,IAAIL,EAAaI,CAAC,CAAC,EACjCC,EAAOF,IACPA,EAAME,GAEd,UAAWC,KAAWP,EAClB,QAASK,EAAI,EAAGA,EAAIF,EAAQE,IACxBE,EAAQF,CAAC,GAAKD,EAI1B,OAAOJ,CACX,CAEA,SAASQ,GAAaR,EAAaS,EAAU,CAEzC,OAAI,OAAOT,EAAY,CAAC,GAAM,WAC1BA,EAAc,CAACA,CAAW,GAE9BD,GAAUC,CAAW,EACd,CACH,SAAAS,EACA,OAAQT,EAAY,CAAC,EAAE,OACvB,WAAYA,EAAY,CAAC,EAAE,OAASS,EACpC,iBAAkBT,EAAY,OAC9B,eAAiBK,GAAgEL,IAAYK,CAAC,EAC9F,gBAAiB,YAAY,UAAU,gBACvC,cAAe,YAAY,UAAU,aAC7C,CACA,CACA,MAAMK,GAAU,CACZ,OAAAf,GACA,aAAAa,EACJ,ECxDA,IAAI5B,GAAaC,YAAQA,WAAK,WAAc,SAAUC,EAASC,EAAYC,EAAGC,EAAW,CACrF,SAASC,EAAMC,EAAO,CAAE,OAAOA,aAAiBH,EAAIG,EAAQ,IAAIH,EAAE,SAAUI,EAAS,CAAEA,EAAQD,CAAK,CAAE,CAAE,CAAI,CAC5G,OAAO,IAAKH,IAAMA,EAAI,UAAU,SAAUI,EAASC,EAAQ,CACvD,SAASC,EAAUH,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,KAAKE,CAAK,CAAC,CAAE,OAAUK,EAAP,CAAYH,EAAOG,CAAC,EAAM,CAC3F,SAASC,EAASN,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,MAASE,CAAK,CAAC,CAAI,OAAQK,EAAP,CAAYH,EAAOG,CAAC,EAAM,CAC9F,SAASD,EAAKG,EAAQ,CAAEA,EAAO,KAAON,EAAQM,EAAO,KAAK,EAAIR,EAAMQ,EAAO,KAAK,EAAE,KAAKJ,EAAWG,CAAQ,CAAI,CAC9GF,GAAMN,EAAYA,EAAU,MAAMH,EAASC,GAAc,CAAE,IAAG,KAAI,CAAE,CAC5E,CAAK,CACL,EACA,SAAS4B,GAAUC,EAAKC,EAAkBC,EAAa,CACnD,IAAIC,EAAIC,EACR,OAAOpC,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAEhD,MAAMqC,EAAW,MAAM,MAAML,EAAKE,CAAW,EAE7C,CACI,MAAMI,GAAUH,EAAKE,EAAS,MAAO,EAAC,QAAU,MAAQF,IAAO,OAAS,OAASA,EAAG,UAAS,EACvFI,EAAgB,QAAQH,EAAKC,EAAS,WAAa,MAAQD,IAAO,OAAS,OAASA,EAAG,IAAI,gBAAgB,CAAC,EAClH,IAAII,EAAiB,EAErB,MAAMC,EAAe,CAACC,EAAMnC,IAAUP,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAC/E,GAAI0C,EACA,OAEJF,GAAiEjC,GAAM,QAAW,EAClF,MAAMoC,EAAa,KAAK,MAAOH,EAAiBD,EAAiB,GAAG,EACpE,OAAAN,EAAiBU,CAAU,EAE4BL,GAAO,KAAI,EAAG,KAAK,CAAC,CAAE,KAAAI,EAAM,MAAAnC,CAAK,IAAOkC,EAAaC,EAAMnC,CAAK,CAAC,CACxI,CAAa,EAC+C+B,GAAO,KAAI,EAAG,KAAK,CAAC,CAAE,KAAAI,EAAM,MAAAnC,CAAK,IAAOkC,EAAaC,EAAMnC,CAAK,CAAC,CACpH,CACD,OAAO8B,EAAS,MACxB,CAAK,CACL,CACA,MAAMO,GAAU,CACZ,UAAAb,EACJ,ECpCA,MAAMc,EAAa,CACf,aAAc,CACV,KAAK,UAAY,GAEjB,KAAK,GAAK,KAAK,iBAEf,KAAK,GAAK,KAAK,mBAClB,CAED,iBAAiBC,EAAOC,EAAUC,EAAS,CAKvC,GAJK,KAAK,UAAUF,CAAK,IACrB,KAAK,UAAUA,CAAK,EAAI,IAAI,KAEhC,KAAK,UAAUA,CAAK,EAAE,IAAIC,CAAQ,EACoBC,GAAQ,KAAM,CAChE,MAAMC,EAAkB,IAAM,CAC1B,KAAK,oBAAoBH,EAAOG,CAAe,EAC/C,KAAK,oBAAoBH,EAAOC,CAAQ,CACxD,EACY,YAAK,iBAAiBD,EAAOG,CAAe,EACrCA,EAEX,MAAO,IAAM,KAAK,oBAAoBH,EAAOC,CAAQ,CACxD,CACD,oBAAoBD,EAAOC,EAAU,CACjC,IAAIZ,GACHA,EAAK,KAAK,UAAUW,CAAK,KAAO,MAAQX,IAAO,QAAkBA,EAAG,OAAOY,CAAQ,CACvF,CAED,KAAKD,EAAOC,EAAU,CAClB,OAAO,KAAK,GAAGD,EAAOC,EAAU,CAAE,KAAM,EAAI,CAAE,CACjD,CAED,OAAQ,CACJ,KAAK,UAAY,EACpB,CAED,KAAKG,KAAcC,EAAM,CACjB,KAAK,UAAUD,CAAS,GACxB,KAAK,UAAUA,CAAS,EAAE,QAASH,GAAaA,EAAS,GAAGI,CAAI,CAAC,CAExE,CACL,CC1CA,MAAMC,WAAeP,EAAa,CAC9B,YAAYG,EAAS,CACjB,QACA,KAAK,gBAAkB,GACnBA,EAAQ,OACR,KAAK,MAAQA,EAAQ,MACrB,KAAK,gBAAkB,IAGvB,KAAK,MAAQ,SAAS,cAAc,OAAO,EAG3CA,EAAQ,gBACR,KAAK,MAAM,SAAW,IAGtBA,EAAQ,WACR,KAAK,MAAM,SAAW,IAGtBA,EAAQ,cAAgB,MACxB,KAAK,eAAe,UAAW,IAAM,CAC7BA,EAAQ,cAAgB,OACxB,KAAK,MAAM,aAAeA,EAAQ,aAEtD,CAAa,CAER,CACD,aAAaF,EAAOO,EAAUL,EAAS,CACnC,YAAK,MAAM,iBAAiBF,EAAOO,EAAUL,CAAO,EAC7C,IAAM,KAAK,MAAM,oBAAoBF,EAAOO,CAAQ,CAC9D,CACD,eAAeP,EAAOO,EAAU,CAC5B,OAAO,KAAK,aAAaP,EAAOO,EAAU,CAAE,KAAM,EAAI,CAAE,CAC3D,CACD,QAAS,CACL,OAAO,KAAK,MAAM,YAAc,KAAK,MAAM,KAAO,EACrD,CACD,WAAY,CACR,MAAMC,EAAM,KAAK,SACbA,EAAI,WAAW,OAAO,GACtB,IAAI,gBAAgBA,CAAG,CAE9B,CACD,OAAOtB,EAAKuB,EAAM,CAEd,GADY,KAAK,WACLvB,EACR,OACJ,KAAK,UAAS,EACd,MAAMwB,EAASD,aAAgB,KAAO,IAAI,gBAAgBA,CAAI,EAAIvB,EAClE,KAAK,MAAM,IAAMwB,EACjB,KAAK,MAAM,MACd,CACD,SAAU,CACN,KAAK,MAAM,QACP,MAAK,kBAET,KAAK,MAAM,SACX,KAAK,UAAS,EACd,KAAK,MAAM,IAAM,GAEjB,KAAK,MAAM,OACd,CACD,gBAAgBC,EAAS,CACrB,KAAK,MAAQA,CAChB,CAED,MAAO,CACH,OAAO,KAAK,MAAM,MACrB,CAED,OAAQ,CACJ,KAAK,MAAM,OACd,CAED,WAAY,CACR,MAAO,CAAC,KAAK,MAAM,QAAU,CAAC,KAAK,MAAM,KAC5C,CAED,QAAQC,EAAM,CACV,KAAK,MAAM,YAAcA,CAC5B,CAED,aAAc,CACV,OAAO,KAAK,MAAM,QACrB,CAED,gBAAiB,CACb,OAAO,KAAK,MAAM,WACrB,CAED,WAAY,CACR,OAAO,KAAK,MAAM,MACrB,CAED,UAAUC,EAAQ,CACd,KAAK,MAAM,OAASA,CACvB,CAED,UAAW,CACP,OAAO,KAAK,MAAM,KACrB,CAED,SAASC,EAAO,CACZ,KAAK,MAAM,MAAQA,CACtB,CAED,iBAAkB,CACd,OAAO,KAAK,MAAM,YACrB,CAED,gBAAgBC,EAAMC,EAAe,CAE7BA,GAAiB,OACjB,KAAK,MAAM,eAAiBA,GAEhC,KAAK,MAAM,aAAeD,CAC7B,CAED,iBAAkB,CACd,OAAO,KAAK,KACf,CAED,UAAUE,EAAQ,CAGd,OADc,KAAK,MACN,UAAUA,CAAM,CAChC,CACL,CCjIO,SAASC,GAAcP,EAASQ,EAAQC,EAASC,EAAOC,EAAY,EAAG,CAC1E,IAAIC,EAAQ,IAAM,CAEtB,EACI,GAAI,CAACZ,EACD,OAAOY,EACX,MAAMC,EAAQ1D,GAAM,CAEhB,GAAIA,EAAE,SAAW,EACb,OACJA,EAAE,eAAc,EAChBA,EAAE,gBAAe,EACjB6C,EAAQ,MAAM,YAAc,OAC5B,IAAIc,EAAS3D,EAAE,QACX4D,EAAS5D,EAAE,QACX6D,EAAa,GACjB,MAAMC,EAAQ9D,GAAM,CAChBA,EAAE,eAAc,EAChBA,EAAE,gBAAe,EACjB,MAAM+D,EAAI/D,EAAE,QACNgE,EAAIhE,EAAE,QACZ,GAAI6D,GAAc,KAAK,IAAIE,EAAIJ,CAAM,GAAKH,GAAa,KAAK,IAAIQ,EAAIJ,CAAM,GAAKJ,EAAW,CACtF,KAAM,CAAE,KAAAS,EAAM,IAAAC,CAAK,EAAGrB,EAAQ,sBAAqB,EAC9CgB,IACDA,EAAa,GACqCP,IAAQK,EAASM,EAAML,EAASM,CAAG,GAEzFb,EAAOU,EAAIJ,EAAQK,EAAIJ,EAAQG,EAAIE,EAAMD,EAAIE,CAAG,EAChDP,EAASI,EACTH,EAASI,EAEzB,EACcG,EAASnE,GAAM,CACb6D,IACA7D,EAAE,eAAc,EAChBA,EAAE,gBAAe,EAEjC,EACcoE,EAAK,IAAM,CACbvB,EAAQ,MAAM,YAAc,GACxBgB,GAC8CN,MAElDE,GACZ,EACQ,SAAS,iBAAiB,cAAeK,CAAI,EAC7C,SAAS,iBAAiB,YAAaM,CAAE,EACzC,SAAS,iBAAiB,eAAgBA,CAAE,EAC5C,SAAS,iBAAiB,QAASD,EAAO,EAAI,EAC9CV,EAAQ,IAAM,CACV,SAAS,oBAAoB,cAAeK,CAAI,EAChD,SAAS,oBAAoB,YAAaM,CAAE,EAC5C,SAAS,oBAAoB,eAAgBA,CAAE,EAC/C,WAAW,IAAM,CACb,SAAS,oBAAoB,QAASD,EAAO,EAAI,CACpD,EAAE,EAAE,CACjB,CACA,EACI,OAAAtB,EAAQ,iBAAiB,cAAea,CAAI,EACrC,IAAM,CACTD,IACAZ,EAAQ,oBAAoB,cAAea,CAAI,CACvD,CACA,CC7DA,MAAMW,WAAiBpC,EAAa,CAChC,YAAYG,EAASkC,EAAc,CAC/B,QACA,KAAK,SAAW,GAChB,KAAK,YAAc,GACnB,KAAK,UAAY,KACjB,KAAK,eAAiB,KACtB,KAAK,WAAa,GAClB,KAAK,QAAUlC,EACf,MAAMmC,EAAS,KAAK,2BAA2BnC,EAAQ,SAAS,EAChE,KAAK,OAASmC,EACd,KAAM,CAACC,EAAKC,CAAM,EAAI,KAAK,SAAQ,EACnCF,EAAO,YAAYC,CAAG,EACtB,KAAK,UAAYA,EACjB,KAAK,gBAAkBC,EAAO,cAAc,SAAS,EACrD,KAAK,QAAUA,EAAO,cAAc,UAAU,EAC9C,KAAK,cAAgBA,EAAO,cAAc,WAAW,EACrD,KAAK,gBAAkBA,EAAO,cAAc,WAAW,EACvD,KAAK,OAASA,EAAO,cAAc,SAAS,EACxCH,GACAG,EAAO,YAAYH,CAAY,EAEnC,KAAK,WAAU,CAClB,CACD,2BAA2BI,EAAW,CAClC,IAAIH,EAOJ,GANI,OAAOG,GAAc,SACrBH,EAAS,SAAS,cAAcG,CAAS,EAEpCA,aAAqB,cAC1BH,EAASG,GAET,CAACH,EACD,MAAM,IAAI,MAAM,qBAAqB,EAEzC,OAAOA,CACV,CACD,YAAa,CACT,MAAMI,EAAoB3E,GAAM,CAC5B,MAAMb,EAAO,KAAK,QAAQ,sBAAqB,EACzC4E,EAAI/D,EAAE,QAAUb,EAAK,KACrB6E,EAAIhE,EAAE,QAAUb,EAAK,KACrByF,EAAYb,EAAI5E,EAAK,MACrB0F,EAAYb,EAAI7E,EAAK,OAC3B,MAAO,CAACyF,EAAWC,CAAS,CACxC,EAEQ,KAAK,QAAQ,iBAAiB,QAAU7E,GAAM,CAC1C,KAAM,CAAC+D,EAAGC,CAAC,EAAIW,EAAiB3E,CAAC,EACjC,KAAK,KAAK,QAAS+D,EAAGC,CAAC,CACnC,CAAS,EAED,KAAK,QAAQ,iBAAiB,WAAahE,GAAM,CAC7C,KAAM,CAAC+D,EAAGC,CAAC,EAAIW,EAAiB3E,CAAC,EACjC,KAAK,KAAK,WAAY+D,EAAGC,CAAC,CACtC,CAAS,EAEG,KAAK,QAAQ,YACb,KAAK,SAAQ,EAGjB,KAAK,gBAAgB,iBAAiB,SAAU,IAAM,CAClD,KAAM,CAAE,WAAAc,EAAY,YAAAC,EAAa,YAAAC,CAAW,EAAK,KAAK,gBAChDrB,EAASmB,EAAaC,EACtBE,GAAQH,EAAaE,GAAeD,EAC1C,KAAK,KAAK,SAAUpB,EAAQsB,CAAI,CAC5C,CAAS,EAED,MAAMC,EAAQ,KAAK,YAAY,GAAG,EAClC,KAAK,eAAiB,IAAI,eAAe,IAAM,CAC3CA,EAAM,IAAM,KAAK,SAAQ,CAAE,CACvC,CAAS,EACD,KAAK,eAAe,QAAQ,KAAK,eAAe,CACnD,CACD,UAAW,CACP9B,GAAc,KAAK,QAEnB,CAAC+B,EAAGC,EAAIrB,IAAM,CACV,KAAK,KAAK,OAAQ,KAAK,IAAI,EAAG,KAAK,IAAI,EAAGA,EAAI,KAAK,QAAQ,sBAAqB,EAAG,KAAK,CAAC,CAAC,CAC7F,EAED,IAAO,KAAK,WAAa,GAEzB,IAAO,KAAK,WAAa,EAAM,CAClC,CACD,WAAY,CAER,OAAI,KAAK,QAAQ,QAAU,KAChB,IACN,MAAM,OAAO,KAAK,QAAQ,MAAM,CAAC,EAElC,KAAK,QAAQ,SAAW,QACjB,KAAK,OAAO,cAAgB,IAF5B,OAAO,KAAK,QAAQ,MAAM,CAIxC,CACD,UAAW,CACP,MAAMS,EAAM,SAAS,cAAc,KAAK,EAClCC,EAASD,EAAI,aAAa,CAAE,KAAM,MAAM,CAAE,EAChD,OAAAC,EAAO,UAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBA8BH,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA2Cd,CAACD,EAAKC,CAAM,CACtB,CAED,WAAWrC,EAAS,CAChB,GAAI,KAAK,QAAQ,YAAcA,EAAQ,UAAW,CAC9C,MAAMiD,EAAY,KAAK,2BAA2BjD,EAAQ,SAAS,EACnEiD,EAAU,YAAY,KAAK,SAAS,EACpC,KAAK,OAASA,EAEdjD,EAAQ,YAAc,CAAC,KAAK,QAAQ,YACpC,KAAK,SAAQ,EAEjB,KAAK,QAAUA,EAEf,KAAK,SAAQ,CAChB,CACD,YAAa,CACT,OAAO,KAAK,OACf,CACD,WAAY,CACR,OAAO,KAAK,gBAAgB,UAC/B,CACD,SAAU,CACN,IAAIb,EACJ,KAAK,UAAU,UACdA,EAAK,KAAK,kBAAoB,MAAQA,IAAO,QAAkBA,EAAG,WAAU,CAChF,CACD,YAAY+D,EAAU,GAAI,CACtB,MAAMC,EAAU,GAChB,YAAK,SAAS,KAAKA,CAAO,EAClB9C,GAAa,CACjB8C,EAAQ,SAAW,aAAaA,EAAQ,OAAO,EAC/CA,EAAQ,QAAU,WAAW9C,EAAU6C,CAAO,CAC1D,CACK,CAED,mBAAmBE,EAAO,CACtB,GAAI,CAAC,MAAM,QAAQA,CAAK,EACpB,OAAOA,GAAS,GACpB,GAAIA,EAAM,OAAS,EACf,OAAOA,EAAM,CAAC,GAAK,GACvB,MAAMC,EAAgB,SAAS,cAAc,QAAQ,EAE/CC,EADMD,EAAc,WAAW,IAAI,EACpB,qBAAqB,EAAG,EAAG,EAAGA,EAAc,MAAM,EACjEE,EAAsB,GAAKH,EAAM,OAAS,GAChD,OAAAA,EAAM,QAAQ,CAACA,EAAOI,IAAU,CAC5B,MAAMC,EAASD,EAAQD,EACvBD,EAAS,aAAaG,EAAQL,CAAK,CAC/C,CAAS,EACME,CACV,CACD,kBAAkBlF,EAAa4B,EAAS0D,EAAKC,EAAQ,CACjD,MAAMC,EAAaxF,EAAY,CAAC,EAC1ByF,EAAgBzF,EAAY,CAAC,GAAKA,EAAY,CAAC,EAC/CG,EAASqF,EAAW,OACpB,CAAE,MAAAE,EAAO,OAAAC,GAAWL,EAAI,OACxBM,EAAaD,EAAS,EACtBE,EAAa,OAAO,kBAAoB,EACxCC,EAAWlE,EAAQ,SAAWA,EAAQ,SAAWiE,EAAa,EAC9DE,EAASnE,EAAQ,OAASA,EAAQ,OAASiE,EAAajE,EAAQ,SAAWkE,EAAW,EAAI,EAC1FE,EAAYpE,EAAQ,WAAa,EACjCqE,EAAgBP,GAASI,EAAWC,GAAU5F,EAC9C+F,EAASF,GAAa,cAAeV,EAAM,YAAc,OAC/DA,EAAI,UAAS,EACb,IAAIa,EAAQ,EACRC,EAAS,EACTC,EAAY,EAChB,QAAShG,EAAI,EAAGA,GAAKF,EAAQE,IAAK,CAC9B,MAAMkD,EAAI,KAAK,MAAMlD,EAAI4F,CAAa,EACtC,GAAI1C,EAAI4C,EAAO,CACX,MAAMG,EAAe,KAAK,MAAMF,EAASR,EAAaL,CAAM,EACtDgB,EAAkB,KAAK,MAAMF,EAAYT,EAAaL,CAAM,EAC5DiB,EAAYF,EAAeC,GAAmB,EAEpD,IAAI/C,EAAIoC,EAAaU,EACjB1E,EAAQ,WAAa,MACrB4B,EAAI,EAEC5B,EAAQ,WAAa,WAC1B4B,EAAImC,EAASa,GAEjBlB,EAAIY,CAAM,EAAEC,GAASL,EAAWC,GAASvC,EAAGsC,EAAUU,EAAWR,CAAS,EAC1EG,EAAQ5C,EACR6C,EAAS,EACTC,EAAY,EAEhB,MAAMI,EAAe,KAAK,IAAIjB,EAAWnF,CAAC,GAAK,CAAC,EAC1CqG,EAAkB,KAAK,IAAIjB,EAAcpF,CAAC,GAAK,CAAC,EAClDoG,EAAeL,IACfA,EAASK,GACTC,EAAkBL,IAClBA,EAAYK,GAEpBpB,EAAI,KAAI,EACRA,EAAI,UAAS,CAChB,CACD,mBAAmBtF,EAAa2G,EAAUrB,EAAKC,EAAQ,CACnD,MAAMqB,EAAexB,GAAU,CAC3B,MAAM7E,EAAUP,EAAYoF,CAAK,GAAKpF,EAAY,CAAC,EAC7CG,EAASI,EAAQ,OACjB,CAAE,OAAAoF,CAAM,EAAKL,EAAI,OACjBM,EAAaD,EAAS,EACtBkB,EAASvB,EAAI,OAAO,MAAQnF,EAClCmF,EAAI,OAAO,EAAGM,CAAU,EACxB,IAAIO,EAAQ,EACR/F,EAAM,EACV,QAASC,EAAI,EAAGA,GAAKF,EAAQE,IAAK,CAC9B,MAAMkD,EAAI,KAAK,MAAMlD,EAAIwG,CAAM,EAC/B,GAAItD,EAAI4C,EAAO,CACX,MAAMW,EAAI,KAAK,MAAM1G,EAAMwF,EAAaL,CAAM,GAAK,EAC7C/B,EAAIoC,EAAakB,GAAK1B,IAAU,EAAI,GAAK,GAC/CE,EAAI,OAAOa,EAAO3C,CAAC,EACnB2C,EAAQ5C,EACRnD,EAAM,EAEV,MAAMjB,EAAQ,KAAK,IAAIoB,EAAQF,CAAC,GAAK,CAAC,EAClClB,EAAQiB,IACRA,EAAMjB,GAEdmG,EAAI,OAAOa,EAAOP,CAAU,CACxC,EACQN,EAAI,UAAS,EACbsB,EAAY,CAAC,EACbA,EAAY,CAAC,EACbtB,EAAI,KAAI,EACRA,EAAI,UAAS,CAChB,CACD,eAAetF,EAAa4B,EAAS0D,EAAK,CAGtC,GAFAA,EAAI,UAAY,KAAK,mBAAmB1D,EAAQ,SAAS,EAErDA,EAAQ,eAAgB,CACxBA,EAAQ,eAAe5B,EAAasF,CAAG,EACvC,OAGJ,IAAIC,EAAS3D,EAAQ,WAAa,EAClC,GAAIA,EAAQ,UAAW,CACnB,MAAMxB,EAAM,MAAM,KAAKJ,EAAY,CAAC,CAAC,EAAE,OAAO,CAACI,EAAKjB,IAAU,KAAK,IAAIiB,EAAK,KAAK,IAAIjB,CAAK,CAAC,EAAG,CAAC,EAC/FoG,EAASnF,EAAM,EAAIA,EAAM,EAG7B,GAAIwB,EAAQ,UAAYA,EAAQ,QAAUA,EAAQ,SAAU,CACxD,KAAK,kBAAkB5B,EAAa4B,EAAS0D,EAAKC,CAAM,EACxD,OAGJ,KAAK,mBAAmBvF,EAAa4B,EAAS0D,EAAKC,CAAM,CAC5D,CACD,mBAAmBvF,EAAa4B,EAAS8D,EAAOC,EAAQoB,EAAOC,EAAKC,EAAiBC,EAAmB,CACpG,MAAMrB,EAAa,OAAO,kBAAoB,EACxCsB,EAAS,SAAS,cAAc,QAAQ,EACxChH,EAASH,EAAY,CAAC,EAAE,OAC9BmH,EAAO,MAAQ,KAAK,MAAOzB,GAASsB,EAAMD,GAAU5G,CAAM,EAC1DgH,EAAO,OAASxB,EAASE,EACzBsB,EAAO,MAAM,MAAQ,GAAG,KAAK,MAAMA,EAAO,MAAQtB,CAAU,MAC5DsB,EAAO,MAAM,OAAS,GAAGxB,MACzBwB,EAAO,MAAM,KAAO,GAAG,KAAK,MAAOJ,EAAQrB,EAASG,EAAa1F,CAAM,MACvE8G,EAAgB,YAAYE,CAAM,EAClC,MAAM7B,EAAM6B,EAAO,WAAW,IAAI,EAGlC,GAFA,KAAK,eAAenH,EAAY,IAAKO,GAAYA,EAAQ,MAAMwG,EAAOC,CAAG,CAAC,EAAGpF,EAAS0D,CAAG,EAErF6B,EAAO,MAAQ,GAAKA,EAAO,OAAS,EAAG,CACvC,MAAMC,EAAiBD,EAAO,YACxBE,EAAcD,EAAe,WAAW,IAAI,EAClDC,EAAY,UAAUF,EAAQ,EAAG,CAAC,EAElCE,EAAY,yBAA2B,YACvCA,EAAY,UAAY,KAAK,mBAAmBzF,EAAQ,aAAa,EAErEyF,EAAY,SAAS,EAAG,EAAGF,EAAO,MAAOA,EAAO,MAAM,EACtDD,EAAkB,YAAYE,CAAc,EAEnD,CACD,cAAcpH,EAAa4B,EAAS8D,EAAO,CAEvC,MAAMuB,EAAkB,SAAS,cAAc,KAAK,EAC9CtB,EAAS,KAAK,YACpBsB,EAAgB,MAAM,OAAS,GAAGtB,MAClC,KAAK,cAAc,MAAM,UAAY,GAAGA,MACxC,KAAK,cAAc,YAAYsB,CAAe,EAE9C,MAAMC,EAAoBD,EAAgB,YAC1C,KAAK,gBAAgB,YAAYC,CAAiB,EAElD,KAAM,CAAE,WAAA5C,EAAY,YAAAC,EAAa,YAAAC,CAAW,EAAK,KAAK,gBAChD8C,EAAMtH,EAAY,CAAC,EAAE,OACrBuH,EAAQD,EAAM/C,EACpB,IAAIiD,EAAgB,KAAK,IAAI3D,GAAS,iBAAkBW,CAAW,EAEnE,GAAI5C,EAAQ,UAAYA,EAAQ,OAAQ,CACpC,MAAMkE,EAAWlE,EAAQ,UAAY,GAC/BmE,EAASnE,EAAQ,QAAUkE,EAAW,EACtC2B,EAAgB3B,EAAWC,EAC7ByB,EAAgBC,IAAkB,IAClCD,EAAgB,KAAK,MAAMA,EAAgBC,CAAa,EAAIA,GAGpE,MAAMV,EAAQ,KAAK,MAAM,KAAK,IAAIzC,CAAU,EAAIiD,CAAK,EAC/CP,EAAM,KAAK,MAAMD,EAAQS,EAAgBD,CAAK,EAC9CG,EAAcV,EAAMD,EAEpBY,EAAO,CAACZ,EAAOC,IAAQ,CACzB,KAAK,mBAAmBhH,EAAa4B,EAAS8D,EAAOC,EAAQ,KAAK,IAAI,EAAGoB,CAAK,EAAG,KAAK,IAAIC,EAAKM,CAAG,EAAGL,EAAiBC,CAAiB,CACnJ,EAEcU,EAAY,KAAK,cACjBC,EAAY,KAAK,cACjBC,EAAa,CAACC,EAAWC,IAAY,CACvCL,EAAKI,EAAWC,CAAO,EACnBD,EAAY,GACZH,EAAU,IAAM,CACZE,EAAWC,EAAYL,EAAaM,EAAUN,CAAW,CAC7E,CAAiB,CAEjB,EACcO,EAAa,CAACF,EAAWC,IAAY,CACvCL,EAAKI,EAAWC,CAAO,EACnBA,EAAUV,GACVO,EAAU,IAAM,CACZI,EAAWF,EAAYL,EAAaM,EAAUN,CAAW,CAC7E,CAAiB,CAEjB,EACQI,EAAWf,EAAOC,CAAG,EACjBA,EAAMM,GACNW,EAAWjB,EAAKA,EAAMU,CAAW,CAExC,CACD,OAAO9H,EAAW,CAEd,KAAK,SAAS,QAASmF,GAAYA,EAAQ,SAAW,aAAaA,EAAQ,OAAO,CAAC,EACnF,KAAK,SAAW,GAEhB,KAAK,cAAc,UAAY,GAC/B,KAAK,gBAAgB,UAAY,GACjC,KAAK,QAAQ,MAAM,MAAQ,GAEvB,KAAK,QAAQ,OAAS,OACtB,KAAK,gBAAgB,MAAM,MACvB,OAAO,KAAK,QAAQ,OAAU,SAAW,GAAG,KAAK,QAAQ,UAAY,KAAK,QAAQ,OAG1F,MAAMc,EAAa,OAAO,kBAAoB,EACxCqC,EAAc,KAAK,gBAAgB,YACnC3D,EAAc,KAAK,KAAK3E,EAAU,UAAY,KAAK,QAAQ,aAAe,EAAE,EAElF,KAAK,YAAc2E,EAAc2D,EACjC,MAAMC,EAAiB,KAAK,QAAQ,YAAc,CAAC,KAAK,YAElDzC,GAASyC,EAAiBD,EAAc3D,GAAesB,EAS7D,GAPA,KAAK,QAAQ,MAAM,MAAQsC,EAAiB,OAAS,GAAG5D,MAExD,KAAK,gBAAgB,MAAM,UAAY,KAAK,YAAc,OAAS,SACnE,KAAK,gBAAgB,UAAU,OAAO,cAAe,CAAC,CAAC,KAAK,QAAQ,aAAa,EACjF,KAAK,OAAO,MAAM,gBAAkB,GAAG,KAAK,QAAQ,aAAe,KAAK,QAAQ,gBAChF,KAAK,OAAO,MAAM,MAAQ,GAAG,KAAK,QAAQ,gBAEtC,KAAK,QAAQ,cAEb,QAASlE,EAAI,EAAGA,EAAIT,EAAU,iBAAkBS,IAAK,CACjD,MAAMuB,EAAU,OAAO,OAAO,OAAO,OAAO,CAAE,EAAE,KAAK,OAAO,EAAG,KAAK,QAAQ,cAAcvB,CAAC,CAAC,EAC5F,KAAK,cAAc,CAACT,EAAU,eAAeS,CAAC,CAAC,EAAGuB,EAAS8D,CAAK,MAGnE,CAED,MAAM0C,EAAW,CAACxI,EAAU,eAAe,CAAC,CAAC,EACzCA,EAAU,iBAAmB,GAC7BwI,EAAS,KAAKxI,EAAU,eAAe,CAAC,CAAC,EAC7C,KAAK,cAAcwI,EAAU,KAAK,QAAS1C,CAAK,EAEpD,KAAK,UAAY9F,EACjB,KAAK,KAAK,QAAQ,CACrB,CACD,UAAW,CAEP,GAAI,CAAC,KAAK,UACN,OAEJ,MAAMyI,EAAoB,KAAK,gBAAgB,YAE/C,KAAK,OAAO,KAAK,SAAS,EAE1B,MAAMC,EAAqB,KAAK,gBAAgB,YAChD,KAAK,gBAAgB,YAAcA,EAAqBD,CAC3D,CACD,KAAKE,EAAa,CACd,KAAK,QAAQ,YAAcA,EAC3B,KAAK,SAAQ,CAChB,CACD,eAAeC,EAAUC,EAAY,GAAO,CACxC,KAAM,CAAE,YAAAjE,EAAa,WAAAF,EAAY,YAAAC,CAAW,EAAK,KAAK,gBAChDmE,EAAgBnE,EAAciE,EAC9BG,EAASnE,EAAc,EACvBoE,EAAYH,GAAa,KAAK,QAAQ,YAAc,CAAC,KAAK,WAAaE,EAASnE,EACtF,GAAIkE,EAAgBpE,EAAasE,GAAaF,EAAgBpE,EAE1D,GAAI,KAAK,QAAQ,YAAc,CAAC,KAAK,WAAY,CAE7C,MAAMuE,EAAUF,EAAS,GACrBD,GAAiBpE,EAAaqE,IAAWE,GAAWH,EAAgBpE,EAAaE,EACjF,KAAK,gBAAgB,YAAcqE,EAInC,KAAK,gBAAgB,WAAaH,EAAgBC,OAGjD,KAAK,WAGV,KAAK,gBAAgB,WACjBD,EAAgBpE,EAAaoE,EAAgB,GAAMA,EAAgBlE,EAAc,GAIrF,KAAK,gBAAgB,WAAakE,EAI1C,CACI,KAAM,CAAE,WAAApE,CAAU,EAAK,KAAK,gBACtBnB,EAASmB,EAAaC,EACtBE,GAAQH,EAAaE,GAAeD,EAC1C,KAAK,KAAK,SAAUpB,EAAQsB,CAAI,CACnC,CACJ,CACD,eAAe+D,EAAUC,EAAW,CAChC,GAAI,MAAMD,CAAQ,EACd,OACJ,MAAMM,EAAWN,EAAW,IAC5B,KAAK,cAAc,MAAM,SAAW,WAAWM,4BAAmCA,WAClF,KAAK,gBAAgB,MAAM,MAAQ,GAAGA,KACtC,KAAK,OAAO,MAAM,KAAO,GAAGA,KAC5B,KAAK,OAAO,MAAM,WAAa,KAAK,MAAMA,CAAQ,IAAM,IAAM,IAAI,KAAK,QAAQ,gBAAkB,GAC7F,KAAK,aAAe,KAAK,QAAQ,YACjC,KAAK,eAAeN,EAAUC,CAAS,CAE9C,CACL,CACA5E,GAAS,iBAAmB,ICjgB5B,MAAMkF,WAActH,EAAa,CAC7B,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,YAAc,MACtB,CACD,OAAQ,CACJ,KAAK,YAAc,KAAK,GAAG,OAAQ,IAAM,CACrC,sBAAsB,IAAM,CACxB,KAAK,KAAK,MAAM,CAChC,CAAa,CACb,CAAS,EACD,KAAK,KAAK,MAAM,CACnB,CACD,MAAO,CACH,KAAK,YAAW,CACnB,CACD,SAAU,CACN,KAAK,YAAW,CACnB,CACL,CCpBA,IAAI7C,GAAaC,YAAQA,WAAK,WAAc,SAAUC,EAASC,EAAYC,EAAGC,EAAW,CACrF,SAASC,EAAMC,EAAO,CAAE,OAAOA,aAAiBH,EAAIG,EAAQ,IAAIH,EAAE,SAAUI,EAAS,CAAEA,EAAQD,CAAK,CAAE,CAAE,CAAI,CAC5G,OAAO,IAAKH,IAAMA,EAAI,UAAU,SAAUI,EAASC,EAAQ,CACvD,SAASC,EAAUH,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,KAAKE,CAAK,CAAC,CAAE,OAAUK,EAAP,CAAYH,EAAOG,CAAC,EAAM,CAC3F,SAASC,EAASN,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,MAASE,CAAK,CAAC,CAAI,OAAQK,EAAP,CAAYH,EAAOG,CAAC,EAAM,CAC9F,SAASD,EAAKG,EAAQ,CAAEA,EAAO,KAAON,EAAQM,EAAO,KAAK,EAAIR,EAAMQ,EAAO,KAAK,EAAE,KAAKJ,EAAWG,CAAQ,CAAI,CAC9GF,GAAMN,EAAYA,EAAU,MAAMH,EAASC,GAAc,CAAE,IAAG,KAAI,CAAE,CAC5E,CAAK,CACL,EAKA,MAAMiK,WAAuBvH,EAAa,CACtC,YAAYwH,EAAe,IAAI,aAAgB,CAC3C,QACA,KAAK,WAAa,KAClB,KAAK,SAAW,GAChB,KAAK,cAAgB,EACrB,KAAK,eAAiB,EACtB,KAAK,OAAS,GACd,KAAK,OAAS,KACd,KAAK,WAAa,GAClB,KAAK,OAAS,GACd,KAAK,YAAc,KACnB,KAAK,aAAeA,EACpB,KAAK,SAAW,KAAK,aAAa,WAAU,EAC5C,KAAK,SAAS,QAAQ,KAAK,aAAa,WAAW,CACtD,CACD,MAAO,CACH,OAAOrK,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAE5D,CAAS,CACJ,CACD,IAAI,KAAM,CACN,OAAO,KAAK,UACf,CACD,IAAI,IAAIO,EAAO,CACX,KAAK,WAAaA,EAClB,MAAMA,CAAK,EACN,KAAM8B,GAAaA,EAAS,YAAW,CAAE,EACzC,KAAMiI,GAAgB,KAAK,aAAa,gBAAgBA,CAAW,CAAC,EACpE,KAAMC,GAAgB,CACvB,KAAK,OAASA,EACd,KAAK,KAAK,gBAAgB,EAC1B,KAAK,KAAK,SAAS,EACf,KAAK,UACL,KAAK,KAAI,CACzB,CAAS,CACJ,CACD,OAAQ,CACJ,IAAIpI,EACC,KAAK,SAEV,KAAK,OAAS,IACbA,EAAK,KAAK,cAAgB,MAAQA,IAAO,QAAkBA,EAAG,WAAU,EACzE,KAAK,WAAa,KAAK,aAAa,mBAAkB,EACtD,KAAK,WAAW,OAAS,KAAK,OAC9B,KAAK,WAAW,QAAQ,KAAK,QAAQ,EACjC,KAAK,gBAAkB,KAAK,WAC5B,KAAK,eAAiB,GAE1B,KAAK,WAAW,MAAM,KAAK,aAAa,YAAa,KAAK,cAAc,EACxE,KAAK,cAAgB,KAAK,aAAa,YACvC,KAAK,WAAW,QAAU,IAAM,CACxB,KAAK,aAAe,KAAK,WACzB,KAAK,MAAK,EACV,KAAK,KAAK,OAAO,EAEjC,EACK,CACD,QAAS,CACL,IAAIA,EACA,KAAK,SAET,KAAK,OAAS,IACbA,EAAK,KAAK,cAAgB,MAAQA,IAAO,QAAkBA,EAAG,KAAI,EACnE,KAAK,gBAAkB,KAAK,aAAa,YAAc,KAAK,cAC/D,CACD,MAAO,CACH,OAAOnC,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAChD,KAAK,MAAK,EACV,KAAK,KAAK,MAAM,CAC5B,CAAS,CACJ,CACD,OAAQ,CACJ,KAAK,OAAM,EACX,KAAK,KAAK,OAAO,CACpB,CACD,UAAUwK,EAAU,CAChB,OAAOxK,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAEhD,OADW,KAAK,aACN,UAAUwK,CAAQ,CACxC,CAAS,CACJ,CACD,IAAI,cAAe,CACf,IAAIrI,EAAIC,EACR,OAAQA,GAAMD,EAAK,KAAK,cAAgB,MAAQA,IAAO,OAAS,OAASA,EAAG,aAAa,SAAW,MAAQC,IAAO,OAASA,EAAK,CACpI,CACD,IAAI,aAAa7B,EAAO,CAChB,KAAK,aACL,KAAK,WAAW,aAAa,MAAQA,EAE5C,CACD,IAAI,aAAc,CACd,OAAO,KAAK,OAAS,KAAK,eAAiB,KAAK,eAAiB,KAAK,aAAa,YAAc,KAAK,aACzG,CACD,IAAI,YAAYA,EAAO,CACnB,KAAK,KAAK,SAAS,EACf,KAAK,OACL,KAAK,eAAiBA,GAGtB,KAAK,OAAM,EACX,KAAK,eAAiBA,EACtB,KAAK,MAAK,GAEd,KAAK,KAAK,YAAY,CACzB,CACD,IAAI,UAAW,CACX,IAAI4B,EACJ,QAASA,EAAK,KAAK,UAAY,MAAQA,IAAO,OAAS,OAASA,EAAG,WAAa,CACnF,CACD,IAAI,QAAS,CACT,OAAO,KAAK,SAAS,KAAK,KAC7B,CACD,IAAI,OAAO5B,EAAO,CACd,KAAK,SAAS,KAAK,MAAQA,EAC3B,KAAK,KAAK,cAAc,CAC3B,CACD,IAAI,OAAQ,CACR,OAAO,KAAK,MACf,CACD,IAAI,MAAMA,EAAO,CACT,KAAK,SAAWA,IAEpB,KAAK,OAASA,EACV,KAAK,OACL,KAAK,SAAS,aAGd,KAAK,SAAS,QAAQ,KAAK,aAAa,WAAW,EAE1D,CAED,aAAc,CACV,OAAO,KAAK,QACf,CACL,CCpJA,IAAIP,GAAaC,YAAQA,WAAK,WAAc,SAAUC,EAASC,EAAYC,EAAGC,EAAW,CACrF,SAASC,EAAMC,EAAO,CAAE,OAAOA,aAAiBH,EAAIG,EAAQ,IAAIH,EAAE,SAAUI,EAAS,CAAEA,EAAQD,CAAK,CAAE,CAAE,CAAI,CAC5G,OAAO,IAAKH,IAAMA,EAAI,UAAU,SAAUI,EAASC,EAAQ,CACvD,SAASC,EAAUH,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,KAAKE,CAAK,CAAC,CAAE,OAAUK,EAAP,CAAYH,EAAOG,CAAC,EAAM,CAC3F,SAASC,EAASN,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,MAASE,CAAK,CAAC,CAAI,OAAQK,EAAP,CAAYH,EAAOG,CAAC,EAAM,CAC9F,SAASD,EAAKG,EAAQ,CAAEA,EAAO,KAAON,EAAQM,EAAO,KAAK,EAAIR,EAAMQ,EAAO,KAAK,EAAE,KAAKJ,EAAWG,CAAQ,CAAI,CAC9GF,GAAMN,EAAYA,EAAU,MAAMH,EAASC,GAAc,CAAE,IAAG,KAAI,CAAE,CAC5E,CAAK,CACL,EAOA,MAAMsK,GAAiB,CACnB,UAAW,OACX,cAAe,OACf,YAAa,EACb,YAAa,EACb,WAAY,GACZ,SAAU,GACV,WAAY,GACZ,WAAY,GACZ,WAAY,GACZ,WAAY,GAChB,EACA,MAAMC,WAAmBtH,EAAO,CAE5B,OAAO,OAAOJ,EAAS,CACnB,OAAO,IAAI0H,GAAW1H,CAAO,CAChC,CAED,YAAYA,EAAS,CACjB,MAAM2H,EAAQ3H,EAAQ,QACjBA,EAAQ,UAAY,WAAa,IAAIoH,GAAmB,QAC7D,MAAM,CACF,MAAAO,EACA,cAAe3H,EAAQ,cACvB,SAAUA,EAAQ,SAClB,aAAcA,EAAQ,SAClC,CAAS,EACD,KAAK,QAAU,GACf,KAAK,YAAc,KACnB,KAAK,cAAgB,GACrB,KAAK,mBAAqB,GAC1B,KAAK,QAAU,OAAO,OAAO,GAAIyH,GAAgBzH,CAAO,EACxD,KAAK,MAAQ,IAAImH,GACjB,MAAMjF,EAAeyF,EAAQ,OAAY,KAAK,gBAAe,EAC7D,KAAK,SAAW,IAAI1F,GAAS,KAAK,QAASC,CAAY,EACvD,KAAK,iBAAgB,EACrB,KAAK,mBAAkB,EACvB,KAAK,gBAAe,EACpB,KAAK,YAAW,EAEhB,MAAMlD,EAAM,KAAK,QAAQ,KAAO,KAAK,SACjCA,EACA,KAAK,KAAKA,EAAK,KAAK,QAAQ,MAAO,KAAK,QAAQ,QAAQ,EAEnD,KAAK,QAAQ,OAAS,KAAK,QAAQ,UAExC,KAAK,eAAc,CAE1B,CACD,iBAAkB,CAEd,KAAK,cAAc,KAAK,KAAK,MAAM,GAAG,OAAQ,IAAM,CAChD,MAAM4I,EAAc,KAAK,iBACzB,KAAK,SAAS,eAAeA,EAAc,KAAK,YAAW,EAAI,EAAI,EACnE,KAAK,KAAK,aAAcA,CAAW,EACnC,KAAK,KAAK,eAAgBA,CAAW,CACxC,EAAC,CACL,CACD,kBAAmB,CACf,KAAK,mBAAmB,KAAK,KAAK,aAAa,aAAc,IAAM,CAC/D,MAAMA,EAAc,KAAK,iBACzB,KAAK,SAAS,eAAeA,EAAc,KAAK,cAAe,KAAK,UAAS,CAAE,EAC/E,KAAK,KAAK,aAAcA,CAAW,CACtC,GAAG,KAAK,aAAa,OAAQ,IAAM,CAChC,KAAK,KAAK,MAAM,EAChB,KAAK,MAAM,OACd,GAAG,KAAK,aAAa,QAAS,IAAM,CACjC,KAAK,KAAK,OAAO,EACjB,KAAK,MAAM,MACd,GAAG,KAAK,aAAa,UAAW,IAAM,CACnC,KAAK,MAAM,MACd,GAAG,KAAK,aAAa,QAAS,IAAM,CACjC,KAAK,KAAK,QAAQ,CACrB,GAAG,KAAK,aAAa,UAAW,IAAM,CACnC,KAAK,KAAK,UAAW,KAAK,eAAgB,EAC7C,EAAC,CACL,CACD,oBAAqB,CACjB,KAAK,cAAc,KAEnB,KAAK,SAAS,GAAG,QAAS,CAACpF,EAAWC,IAAc,CAC5C,KAAK,QAAQ,WACb,KAAK,OAAOD,CAAS,EACrB,KAAK,KAAK,cAAeA,EAAY,KAAK,YAAW,CAAE,EACvD,KAAK,KAAK,QAASA,EAAWC,CAAS,EAEvD,CAAS,EAED,KAAK,SAAS,GAAG,WAAY,CAACD,EAAWC,IAAc,CACnD,KAAK,KAAK,WAAYD,EAAWC,CAAS,CACtD,CAAS,EAED,KAAK,SAAS,GAAG,SAAU,CAAClB,EAAQsB,IAAS,CACzC,MAAMhE,EAAW,KAAK,cACtB,KAAK,KAAK,SAAU0C,EAAS1C,EAAUgE,EAAOhE,CAAQ,CAClE,CAAS,EAED,KAAK,SAAS,GAAG,SAAU,IAAM,CAC7B,KAAK,KAAK,QAAQ,CACrB,EAAC,EAEF,CACI,IAAIgJ,EACJ,KAAK,cAAc,KAAK,KAAK,SAAS,GAAG,OAASrF,GAAc,CACvD,KAAK,QAAQ,WAGlB,KAAK,SAAS,eAAeA,CAAS,EAEtC,aAAaqF,CAAQ,EACrBA,EAAW,WAAW,IAAM,CACxB,KAAK,OAAOrF,CAAS,CACxB,EAAE,KAAK,UAAS,EAAK,EAAI,GAAG,EAC7B,KAAK,KAAK,cAAeA,EAAY,KAAK,YAAW,CAAE,EACvD,KAAK,KAAK,OAAQA,CAAS,EAC9B,EAAC,CACL,CACJ,CACD,aAAc,CACV,IAAIrD,EACG,GAAAA,EAAK,KAAK,QAAQ,WAAa,MAAQA,IAAO,SAAkBA,EAAG,QAE1E,KAAK,QAAQ,QAAQ,QAAS2I,GAAW,CACrC,KAAK,eAAeA,CAAM,CACtC,CAAS,CACJ,CACD,yBAA0B,CACtB,KAAK,mBAAmB,QAASC,GAAgBA,EAAa,GAC9D,KAAK,mBAAqB,EAC7B,CAED,WAAW/H,EAAS,CAChB,KAAK,QAAU,OAAO,OAAO,CAAE,EAAE,KAAK,QAASA,CAAO,EACtD,KAAK,SAAS,WAAW,KAAK,OAAO,EACjCA,EAAQ,WACR,KAAK,gBAAgBA,EAAQ,SAAS,EAEtCA,EAAQ,eAAiB,OACzB,KAAK,gBAAiB,EAAC,SAAWA,EAAQ,cAEjD,CAED,eAAe8H,EAAQ,CACnB,OAAAA,EAAO,KAAK,IAAI,EAChB,KAAK,QAAQ,KAAKA,CAAM,EAExB,KAAK,cAAc,KAAKA,EAAO,KAAK,UAAW,IAAM,CACjD,KAAK,QAAU,KAAK,QAAQ,OAAQE,GAAMA,IAAMF,CAAM,CACzD,EAAC,EACKA,CACV,CAED,YAAa,CACT,OAAO,KAAK,SAAS,YACxB,CAED,WAAY,CACR,OAAO,KAAK,SAAS,WACxB,CAED,kBAAmB,CACf,OAAO,KAAK,OACf,CACD,gBAAiB,CACb,OAAO9K,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAC5C,KAAK,QAAQ,OAAS,KAAK,QAAQ,WACnC,KAAK,YAAc8B,GAAQ,aAAa,KAAK,QAAQ,MAAO,KAAK,QAAQ,QAAQ,EACjF,MAAM,QAAQ,UACd,KAAK,cAAa,EAElC,CAAS,CACJ,CACD,eAAgB,CACZ,OAAO9B,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAC5C,KAAK,cACL,KAAK,KAAK,SAAU,KAAK,YAAa,GACtC,KAAK,SAAS,OAAO,KAAK,WAAW,EAErD,CAAS,CACJ,CACD,UAAUgC,EAAKuB,EAAMnC,EAAaS,EAAU,CACxC,OAAO7B,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAMhD,GALA,KAAK,KAAK,OAAQgC,CAAG,EACjB,CAAC,KAAK,QAAQ,OAAS,KAAK,UAAW,GACvC,KAAK,MAAK,EACd,KAAK,YAAc,KAEf,CAACuB,GAAQ,CAACnC,EAAa,CACvB,MAAM6J,EAActI,GAAe,KAAK,KAAK,UAAWA,CAAU,EAClEY,EAAO,MAAMX,GAAQ,UAAUZ,EAAKiJ,EAAY,KAAK,QAAQ,WAAW,EAa5E,GAVA,KAAK,OAAOjJ,EAAKuB,CAAI,EAGrB1B,GACK,MAAM,QAAQ,QAAQA,GAAY,KAAK,YAAW,CAAE,KAChD,MAAM,IAAI,QAASrB,GAAY,CAC5B,KAAK,eAAe,iBAAkB,IAAMA,EAAQ,KAAK,YAAa,EAAC,CAC/F,CAAqB,KACA,MAAM,QAAQ,QAAQ,CAAC,GAE5BY,EACA,KAAK,YAAcU,GAAQ,aAAaV,EAAaS,CAAQ,UAExD0B,EAAM,CACX,MAAM+G,EAAc,MAAM/G,EAAK,cAC/B,KAAK,YAAc,MAAMzB,GAAQ,OAAOwI,EAAa,KAAK,QAAQ,UAAU,EAEhF,KAAK,cAAa,EAClB,KAAK,KAAK,QAAS,KAAK,YAAa,EACjD,CAAS,CACJ,CAED,KAAKtI,EAAKZ,EAAaS,EAAU,CAC7B,OAAO7B,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAChD,MAAM,KAAK,UAAUgC,EAAK,OAAWZ,EAAaS,CAAQ,CACtE,CAAS,CACJ,CAED,SAAS0B,EAAMnC,EAAaS,EAAU,CAClC,OAAO7B,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAChD,MAAM,KAAK,UAAU,OAAQuD,EAAMnC,EAAaS,CAAQ,CACpE,CAAS,CACJ,CAED,KAAK8H,EAAa,CACd,GAAI,CAAC,KAAK,YACN,MAAM,IAAI,MAAM,iBAAiB,EAErC,KAAK,SAAS,KAAKA,CAAW,EAC9B,KAAK,KAAK,OAAQA,CAAW,CAChC,CAED,gBAAiB,CACb,OAAO,KAAK,WACf,CAED,YAAY,CAAE,SAAAH,EAAW,EAAG,UAAA0B,EAAY,IAAM,UAAAC,EAAY,GAAO,EAAG,GAAI,CACpE,GAAI,CAAC,KAAK,YACN,MAAM,IAAI,MAAM,oCAAoC,EAExD,MAAMC,EAAc,KAAK,IAAI5B,EAAU,KAAK,YAAY,gBAAgB,EAClE6B,EAAQ,GACd,QAAS5J,EAAI,EAAGA,EAAI2J,EAAa3J,IAAK,CAClC,MAAME,EAAU,KAAK,YAAY,eAAeF,CAAC,EAC3C6J,EAAO,GACPC,EAAa,KAAK,MAAM5J,EAAQ,OAASuJ,CAAS,EACxD,QAASzJ,EAAI,EAAGA,EAAIyJ,EAAWzJ,IAAK,CAChC,MAAM+J,EAAS7J,EAAQ,MAAMF,EAAI8J,GAAa9J,EAAI,GAAK8J,CAAU,EAC3D/J,EAAM,KAAK,IAAI,GAAGgK,CAAM,EAC9BF,EAAK,KAAK,KAAK,MAAM9J,EAAM2J,CAAS,EAAIA,CAAS,EAErDE,EAAM,KAAKC,CAAI,EAEnB,OAAOD,CACV,CAED,aAAc,CACV,IAAIxJ,EAAW,MAAM,YAAW,GAAM,EAEtC,OAAKA,IAAa,GAAKA,IAAa,MAAa,KAAK,cAClDA,EAAW,KAAK,YAAY,UAEzBA,CACV,CAED,kBAAkB4J,EAAe,CAC7B,KAAK,QAAQ,SAAWA,CAC3B,CAED,OAAO7B,EAAU,CACb,MAAMlG,EAAO,KAAK,YAAW,EAAKkG,EAClC,KAAK,QAAQlG,CAAI,CACpB,CAED,WAAY,CACR,OAAO1D,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAChD,OAAO,KAAK,YAAc,KAAK,QAAU,KAAK,MAC1D,CAAS,CACJ,CAED,MAAO,CACH,KAAK,MAAK,EACV,KAAK,QAAQ,CAAC,CACjB,CAED,KAAK0L,EAAS,CACV,KAAK,QAAQ,KAAK,eAAgB,EAAGA,CAAO,CAC/C,CAED,OAAQ,CACJ,KAAK,KAAK,GAAI,CAAC,CAAC,CAAC,CAAC,EAAG,IAAK,CAC7B,CAED,gBAAgBjI,EAAS,CACrB,KAAK,wBAAuB,EAC5B,MAAM,gBAAgBA,CAAO,EAC7B,KAAK,iBAAgB,CACxB,CAED,SAAU,CACN,KAAK,KAAK,SAAS,EACnB,KAAK,QAAQ,QAASqH,GAAWA,EAAO,QAAO,CAAE,EACjD,KAAK,cAAc,QAASC,GAAgBA,EAAa,GACzD,KAAK,wBAAuB,EAC5B,KAAK,MAAM,UACX,KAAK,SAAS,UACd,MAAM,QAAO,CAChB,CACL,CCrUO,SAASY,GAAiBpB,EAAsC,CACtE,MAAMqB,EAAYrB,EAAY,iBACxBhJ,EAASgJ,EAAY,OAASqB,EAAY,EAAI,GAC9CC,EAAS,IAAI,YAAYtK,CAAM,EAC/BuK,EAAO,IAAI,SAASD,CAAM,EAChC,IAAIpF,EAAS,EAGb,MAAMsF,EAAc,SACnBD,EACArF,EACAuF,EACO,CACP,QAASvK,EAAI,EAAGA,EAAIuK,EAAO,OAAQvK,IAClCqK,EAAK,SAASrF,EAAShF,EAAGuK,EAAO,WAAWvK,CAAC,CAAC,CAC/C,EAGWsK,EAAAD,EAAMrF,EAAQ,MAAM,EACtBA,GAAA,EACVqF,EAAK,UAAUrF,EAAQlF,EAAS,EAAG,EAAI,EAC7BkF,GAAA,EACEsF,EAAAD,EAAMrF,EAAQ,MAAM,EACtBA,GAAA,EACEsF,EAAAD,EAAMrF,EAAQ,MAAM,EACtBA,GAAA,EACLqF,EAAA,UAAUrF,EAAQ,GAAI,EAAI,EACrBA,GAAA,EACLqF,EAAA,UAAUrF,EAAQ,EAAG,EAAI,EACpBA,GAAA,EACLqF,EAAA,UAAUrF,EAAQmF,EAAW,EAAI,EAC5BnF,GAAA,EACVqF,EAAK,UAAUrF,EAAQ8D,EAAY,WAAY,EAAI,EACzC9D,GAAA,EACVqF,EAAK,UAAUrF,EAAQ8D,EAAY,WAAa,EAAIqB,EAAW,EAAI,EACzDnF,GAAA,EACVqF,EAAK,UAAUrF,EAAQmF,EAAY,EAAG,EAAI,EAChCnF,GAAA,EACLqF,EAAA,UAAUrF,EAAQ,GAAI,EAAI,EACrBA,GAAA,EACEsF,EAAAD,EAAMrF,EAAQ,MAAM,EACtBA,GAAA,EACVqF,EAAK,UAAUrF,EAAQ8D,EAAY,OAASqB,EAAY,EAAG,EAAI,EACrDnF,GAAA,EAGV,QAAShF,EAAI,EAAGA,EAAI8I,EAAY,OAAQ9I,IACvC,QAASE,EAAU,EAAGA,EAAUiK,EAAWjK,IAAW,CACrD,MAAM6J,EAAS,KAAK,IACnB,GACA,KAAK,IAAI,EAAGjB,EAAY,eAAe5I,CAAO,EAAEF,CAAC,CAAC,GAEnDqK,EAAK,SAASrF,EAAQ+E,EAAS,MAAQ,EAAI,EACjC/E,GAAA,EAIL,WAAI,WAAWoF,CAAM,CAC7B,CC1CO,MAAMI,GAAgB,MAC5B1B,EACApC,EACAC,EACA8D,IACyB,CACnB,MAAA7B,EAAe,IAAI,aAAa,CACrC,WAAY6B,GAAwB3B,EAAY,WAChD,EACK4B,EAAmB5B,EAAY,iBAC/BtJ,EAAaiL,GAAwB3B,EAAY,WAEvD,IAAI6B,EAAgB7B,EAAY,OAC5B8B,EAAc,EAEdlE,GAASC,IACEiE,EAAA,KAAK,MAAMlE,EAAQlH,CAAU,EAE3CmL,EADkB,KAAK,MAAMhE,EAAMnH,CAAU,EACjBoL,GAG7B,MAAMC,EAAqBjC,EAAa,aACvC8B,EACAC,EACAnL,CAAA,EAGD,QAASU,EAAU,EAAGA,EAAUwK,EAAkBxK,IAAW,CACtD,MAAAP,EAAcmJ,EAAY,eAAe5I,CAAO,EAChD4K,EAAcD,EAAmB,eAAe3K,CAAO,EAC7D,QAASF,EAAI,EAAGA,EAAI2K,EAAe3K,IAClC8K,EAAY9K,CAAC,EAAIL,EAAYiL,EAAc5K,CAAC,EAI9C,OAAOkK,GAAiBW,CAAkB,CAC3C,EAaaE,GAAa,CAACC,EAAsBC,IAAyB,CACpED,GACLA,EAAS,KAAKC,CAAM,CACrB,EAEaC,GAAyB,CACrCC,EACAC,KAEKA,IACUA,EAAA,GAEPD,EAAiB,IAAOC,GAAe,UC7EhD,KAAO,CAAC,aAAa,CAAC,KAAK,UAAU,GAAG,KAAK,GAAG,KAAK,iBAAiB,KAAK,GAAG,KAAK,mBAAmB,CAAC,iBAAiBC,EAAElM,EAAEa,EAAE,CAAC,GAAG,KAAK,UAAUqL,CAAC,IAAI,KAAK,UAAUA,CAAC,EAAE,IAAI,KAAK,KAAK,UAAUA,CAAC,EAAE,IAAIlM,CAAC,EAAiBa,GAAE,KAAK,CAAC,MAAMA,EAAE,IAAI,CAAC,KAAK,oBAAoBqL,EAAErL,CAAC,EAAE,KAAK,oBAAoBqL,EAAElM,CAAC,CAAC,EAAE,OAAO,KAAK,iBAAiBkM,EAAErL,CAAC,EAAEA,EAAE,MAAM,IAAI,KAAK,oBAAoBqL,EAAElM,CAAC,CAAC,CAAC,oBAAoBkM,EAAElM,EAAE,CAAC,IAAIa,GAAUA,EAAE,KAAK,UAAUqL,CAAC,KAA1B,MAAuCrL,IAAT,QAAYA,EAAE,OAAOb,CAAC,CAAC,CAAC,KAAKkM,EAAElM,EAAE,CAAC,OAAO,KAAK,GAAGkM,EAAElM,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,UAAU,EAAE,CAAC,KAAKkM,KAAKlM,EAAE,CAAC,KAAK,UAAUkM,CAAC,GAAG,KAAK,UAAUA,CAAC,EAAE,QAASA,GAAGA,EAAE,GAAGlM,CAAC,CAAG,EAAC,KAAC,cAAgBkM,EAAC,CAAC,YAAYA,EAAE,CAAC,QAAQ,KAAK,cAAc,CAAE,EAAC,KAAK,QAAQA,CAAC,CAAC,QAAQ,EAAE,KAAKA,EAAE,CAAC,KAAK,WAAWA,EAAE,KAAK,OAAQ,EAAC,SAAS,CAAC,KAAK,KAAK,SAAS,EAAE,KAAK,cAAc,QAASA,GAAGA,EAAC,EAAI,CAAC,EAAC,SAASrL,GAAEqL,EAAE,EAAErL,EAAE,EAAEsL,EAAE,EAAE,CAAC,IAAI,EAAE,IAAI,CAAE,EAAC,GAAG,CAACD,EAAE,OAAO,EAAE,MAAME,EAAEA,GAAG,CAAC,GAAOA,EAAE,SAAN,EAAa,OAAOA,EAAE,eAAc,EAAGA,EAAE,gBAAe,EAAGF,EAAE,MAAM,YAAY,OAAO,IAAI,EAAEE,EAAE,QAAQ9E,EAAE8E,EAAE,QAAQ,EAAE,GAAG,MAAMC,EAAE3L,GAAG,CAACA,EAAE,eAAgB,EAACA,EAAE,gBAAiB,EAAC,MAAM4L,EAAE5L,EAAE,QAAQ0L,EAAE1L,EAAE,QAAQ,GAAG,GAAG,KAAK,IAAI4L,EAAE,CAAC,GAAGH,GAAG,KAAK,IAAIC,EAAE9E,CAAC,GAAG6E,EAAE,CAAC,KAAK,CAAC,KAAKzL,EAAE,IAAIyL,CAAC,EAAED,EAAE,wBAAwB,IAAI,EAAE,GAAYrL,IAAE,EAAEH,EAAE4G,EAAE6E,CAAC,GAAG,EAAEG,EAAE,EAAEF,EAAE9E,EAAEgF,EAAE5L,EAAE0L,EAAED,CAAC,EAAE,EAAEG,EAAEhF,EAAE8E,EAAE,EAAEG,EAAEL,GAAG,CAAC,IAAIA,EAAE,eAAgB,EAACA,EAAE,kBAAkB,EAAEM,EAAE,IAAI,CAACN,EAAE,MAAM,YAAY,GAAG,GAAa,IAAG,EAAE,EAAC,CAAE,EAAE,SAAS,iBAAiB,cAAcG,CAAC,EAAE,SAAS,iBAAiB,YAAYG,CAAC,EAAE,SAAS,iBAAiB,eAAeA,CAAC,EAAE,SAAS,iBAAiB,QAAQD,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,oBAAoB,cAAcF,CAAC,EAAE,SAAS,oBAAoB,YAAYG,CAAC,EAAE,SAAS,oBAAoB,eAAeA,CAAC,EAAE,WAAY,IAAI,CAAC,SAAS,oBAAoB,QAAQD,EAAE,EAAE,CAAC,EAAG,EAAE,CAAC,CAAC,EAAE,OAAOL,EAAE,iBAAiB,cAAcE,CAAC,EAAE,IAAI,CAAC,EAAG,EAACF,EAAE,oBAAoB,cAAcE,CAAC,CAAC,CAAC,CAAC,MAAM1L,WAAUwL,EAAC,CAAC,YAAYA,EAAElM,EAAEa,EAAE,EAAE,CAAC,IAAIH,EAAEyL,EAAEG,EAAEF,EAAE,EAAE9E,EAAE,EAAE,QAAQ,KAAK,cAActH,EAAE,KAAK,iBAAiBa,EAAE,KAAK,UAAU,EAAE,KAAK,UAAU,EAAE,EAAE,KAAK,GAAGqL,EAAE,IAAI,UAAU,KAAK,OAAQ,EAAC,SAAS,EAAE,EAAE,MAAM,CAAC,IAAI,KAAK,MAAM,KAAK,cAAcA,EAAE,KAAK,EAAE,KAAK,IAAI,KAAK,eAAsBxL,EAAEwL,EAAE,OAAZ,MAA2BxL,IAAT,OAAWA,EAAEwL,EAAE,KAAK,EAAE,KAAK,MAAaC,EAAED,EAAE,QAAZ,MAA4BC,IAAT,QAAYA,EAAE,KAAK,QAAeG,EAAEJ,EAAE,UAAZ,MAA8BI,IAAT,QAAYA,EAAE,KAAK,OAAcF,EAAEF,EAAE,SAAZ,MAA6BE,IAAT,OAAWA,EAAE,qBAAqB,KAAK,WAAkB,EAAEF,EAAE,aAAZ,MAAiC,IAAT,OAAW,EAAE,KAAK,UAAU,KAAK,WAAkB5E,EAAE4E,EAAE,aAAZ,MAAiC5E,IAAT,OAAWA,EAAE,KAAK,UAAU,KAAK,YAAmB,EAAE4E,EAAE,cAAZ,MAAkC,IAAT,OAAW,EAAE,GAAG,KAAK,QAAQ,KAAK,YAAW,EAAG,KAAK,WAAWA,EAAE,OAAO,EAAE,KAAK,QAAS,EAAC,KAAK,eAAgB,EAAC,KAAK,gBAAiB,EAAC,cAAcA,EAAE,CAAC,OAAO,KAAK,IAAI,EAAE,KAAK,IAAI,KAAK,cAAcA,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAMA,EAAE,KAAK,QAAQ,KAAK,IAAI,KAAK,QAAQ,aAAa,OAAO,GAAGA,EAAE,SAAS,YAAY,KAAK,IAAI,CAAC,CAAC,iBAAiBA,EAAE,CAAC,MAAMlM,EAAE,SAAS,cAAc,KAAK,EAAEA,EAAE,aAAa,cAAc,MAAM,EAAEA,EAAE,aAAa,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAA+R,EAAEA,EAAE,aAAa,OAAO,kCAAkC,EAAE,MAAM,EAAEA,EAAE,UAAW,EAAC,EAAE,aAAa,cAAc,OAAO,EAAE,EAAE,MAAM,KAAK,GAAG,EAAE,MAAM,MAAM,IAAI,EAAE,MAAM,YAAY,EAAE,MAAM,WAAW,EAAE,MAAM,WAAW,GAAG,EAAE,MAAM,aAAa,cAAc,EAAE,aAAa,OAAO,mCAAmC,EAAEkM,EAAE,YAAYlM,CAAC,EAAEkM,EAAE,YAAY,CAAC,EAAErL,GAAEb,EAAGkM,GAAG,KAAK,SAASA,EAAE,OAAO,EAAI,IAAI,KAAO,IAAI,KAAK,cAAe,EAAE,CAAC,EAAErL,GAAE,EAAGqL,GAAG,KAAK,SAASA,EAAE,KAAK,EAAI,IAAI,KAAO,IAAI,KAAK,cAAa,EAAI,CAAC,CAAC,CAAC,oBAAoBA,EAAE,CAAC,MAAMlM,EAAEkM,EAAE,cAAc,sBAAsB,EAAErL,EAAEqL,EAAE,cAAc,uBAAuB,EAAElM,GAAGkM,EAAE,YAAYlM,CAAC,EAAEa,GAAGqL,EAAE,YAAYrL,CAAC,CAAC,CAAC,aAAa,CAAC,MAAMqL,EAAE,SAAS,cAAc,KAAK,EAAElM,EAAE,KAAK,QAAQ,KAAK,IAAI,IAAIa,EAAE,EAAEH,EAAE,IAAI,OAAO,KAAK,YAAY,GAAG,KAAK,WAAW,KAAK,mBAAmBA,EAAE,IAAI,KAAK,iBAAiBG,EAAEH,EAAE,KAAK,YAAYwL,EAAE,aAAa,QAAQ;AAAA;AAAA,aAA2CrL;AAAA,gBAAsBH;AAAA,0BAAgCV,EAAE,OAAO,KAAK;AAAA,qBAA8BA,EAAE,aAAa,KAAK,MAAM;AAAA;AAAA;AAAA;AAAA,gBAAmI,KAAK,KAAK,OAAO;AAAA;AAAA,KAA8C,EAAE,CAACA,GAAG,KAAK,QAAQ,KAAK,iBAAiBkM,CAAC,EAAEA,CAAC,CAAC,gBAAgB,CAAC,MAAMA,EAAE,KAAK,MAAM,KAAK,cAAclM,GAAG,KAAK,cAAc,KAAK,KAAK,KAAK,cAAc,KAAK,QAAQ,MAAM,KAAK,IAAIkM,EAAE,IAAI,KAAK,QAAQ,MAAM,MAAM,IAAIlM,EAAE,GAAG,CAAC,iBAAiB,CAAC,KAAK,CAAC,QAAQkM,CAAC,EAAE,KAAKA,IAAIA,EAAE,iBAAiB,QAAS,GAAG,KAAK,KAAK,QAAQ,CAAC,GAAIA,EAAE,iBAAiB,aAAc,GAAG,KAAK,KAAK,OAAO,CAAC,CAAG,EAACA,EAAE,iBAAiB,aAAc,GAAG,KAAK,KAAK,QAAQ,CAAC,CAAC,EAAGA,EAAE,iBAAiB,WAAY,GAAG,KAAK,KAAK,WAAW,CAAC,CAAG,EAACrL,GAAEqL,EAAG,GAAG,KAAK,OAAO,CAAC,EAAI,IAAI,KAAK,cAAe,EAAG,IAAI,KAAK,YAAa,GAAG,CAAC,eAAe,CAAC,KAAK,OAAO,KAAK,QAAQ,MAAM,OAAO,WAAW,CAAC,aAAa,CAAC,KAAK,OAAO,KAAK,QAAQ,MAAM,OAAO,OAAO,KAAK,KAAK,YAAY,EAAE,CAAC,UAAUA,EAAElM,EAAE,CAAC,GAAG,CAAC,KAAK,QAAQ,cAAc,OAAO,MAAMa,EAAEqL,EAAE,KAAK,QAAQ,cAAc,YAAY,KAAK,cAAcxL,EAAEV,GAAaA,IAAV,QAAY,KAAK,MAAM,KAAK,MAAMa,EAAEsL,EAAEnM,GAAWA,IAAR,MAAU,KAAK,IAAI,KAAK,IAAIa,EAAEyL,EAAEH,EAAEzL,EAAEA,GAAG,GAAGyL,GAAG,KAAK,eAAezL,GAAGyL,GAAGG,GAAG,KAAK,WAAWA,GAAG,KAAK,YAAY,KAAK,MAAM5L,EAAE,KAAK,IAAIyL,EAAE,KAAK,eAAgB,EAAC,KAAK,KAAK,QAAQ,EAAE,CAAC,OAAOD,EAAE,CAAC,KAAK,MAAM,KAAK,UAAUA,CAAC,CAAC,CAAC,SAASA,EAAElM,EAAE,CAAC,KAAK,QAAQ,KAAK,UAAUkM,EAAElM,CAAC,CAAC,CAAC,eAAe,CAAC,KAAK,QAAQ,KAAK,KAAK,YAAY,CAAC,CAAC,kBAAkBkM,EAAE,CAAC,KAAK,cAAcA,EAAE,KAAK,gBAAgB,CAAC,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,CAAC,WAAWA,EAAE,CAAC,IAAIlM,EAAE,IAAWA,EAAE,KAAK,WAAf,MAAkCA,IAAT,QAAYA,EAAE,OAAM,EAAGkM,EAAE,CAAC,GAAa,OAAOA,GAAjB,SAAmB,CAAC,KAAK,QAAQ,SAAS,cAAc,KAAK,EAAE,MAAMlM,EAAE,KAAK,QAAQ,KAAK,IAAI,KAAK,QAAQ,MAAM,QAAQ,SAASA,EAAE,GAAG,OAAO,KAAK,QAAQ,YAAYkM,OAAO,KAAK,QAAQA,EAAE,KAAK,QAAQ,aAAa,OAAO,gBAAgB,EAAE,KAAK,QAAQ,YAAY,KAAK,OAAO,OAAO,KAAK,QAAQ,MAAM,CAAC,WAAWA,EAAE,CAAC,IAAIlM,EAAEa,EAAE,GAAGqL,EAAE,QAAQ,KAAK,MAAMA,EAAE,MAAM,KAAK,QAAQ,MAAM,gBAAgB,KAAK,OAAgBA,EAAE,OAAX,SAAkB,KAAK,KAAKA,EAAE,KAAK,KAAK,QAAQ,MAAM,OAAO,KAAK,KAAK,OAAO,WAAoBA,EAAE,QAAX,QAA2BA,EAAE,MAAX,OAAe,CAAC,MAAMxL,EAAE,KAAK,QAAQ,KAAK,IAAI,KAAK,MAAM,KAAK,eAAsBV,EAAEkM,EAAE,SAAZ,MAA6BlM,IAAT,OAAWA,EAAE,KAAK,KAAK,EAAE,KAAK,IAAI,KAAK,eAAsBa,EAAEqL,EAAE,OAAZ,MAA2BrL,IAAT,OAAWA,EAAEH,EAAE,KAAK,MAAM,KAAK,GAAG,EAAE,KAAK,eAAc,EAAG,KAAK,QAAS,EAAC,GAAGwL,EAAE,SAAS,KAAK,WAAWA,EAAE,OAAO,EAAEA,EAAE,KAAK,KAAK,GAAGA,EAAE,GAAG,KAAK,WAAoBA,EAAE,SAAX,QAAmBA,EAAE,SAAS,KAAK,OAAO,CAAC,MAAMlM,EAAE,KAAK,QAAQ,KAAK,IAAI,KAAK,OAAOkM,EAAE,OAAO,KAAK,QAAQ,CAAClM,EAAE,KAAK,iBAAiB,KAAK,OAAO,EAAE,KAAK,oBAAoB,KAAK,OAAO,EAAE,CAAC,QAAQ,CAAC,KAAK,KAAK,QAAQ,EAAE,KAAK,QAAQ,OAAQ,EAAC,KAAK,QAAQ,IAAI,CAAC,KAAAyM,GAAC,MAAMN,WAAUnM,EAAC,CAAC,YAAYkM,EAAE,CAAC,MAAMA,CAAC,EAAE,KAAK,QAAQ,GAAG,KAAK,iBAAiB,KAAK,sBAAsB,CAAC,OAAO,OAAOA,EAAE,CAAC,OAAO,IAAIC,GAAED,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,WAAW,MAAM,MAAM,+BAA+B,EAAE,KAAK,WAAW,aAAa,YAAY,KAAK,gBAAgB,EAAE,IAAIA,EAAE,GAAG,KAAK,cAAc,KAAK,KAAK,WAAW,GAAG,aAAclM,GAAG,CAAC,MAAMa,EAAE,KAAK,QAAQ,OAAQqL,GAAGA,EAAE,OAAOlM,GAAGkM,EAAE,KAAKlM,GAAIa,EAAE,QAASb,GAAG,CAACkM,EAAE,SAASlM,CAAC,GAAG,KAAK,KAAK,YAAYA,CAAC,CAAC,GAAIkM,EAAE,QAASA,GAAG,CAACrL,EAAE,SAASqL,CAAC,GAAG,KAAK,KAAK,aAAaA,CAAC,CAAC,GAAIA,EAAErL,CAAC,EAAG,CAAC,CAAC,sBAAsB,CAAC,MAAMqL,EAAE,SAAS,cAAc,KAAK,EAAE,OAAOA,EAAE,aAAa,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAA2J,EAAEA,CAAC,CAAC,YAAY,CAAC,OAAO,KAAK,OAAO,CAAC,iBAAiBA,EAAE,CAAC,GAAG,CAACA,EAAE,QAAQ,OAAO,MAAMlM,EAAEkM,EAAE,QAAQrL,EAAEb,EAAE,wBAAwB,KAAKU,EAAEwL,EAAE,QAAQ,YAAYC,EAAE,KAAK,QAAQ,OAAQnM,GAAG,CAAC,GAAGA,IAAIkM,GAAG,CAAClM,EAAE,QAAQ,MAAM,GAAG,MAAMmM,EAAEnM,EAAE,QAAQ,sBAAuB,EAAC,KAAKsM,EAAEtM,EAAE,QAAQ,YAAY,OAAOa,EAAEsL,EAAEG,GAAGH,EAAEtL,EAAEH,CAAC,CAAG,EAAC,IAAKwL,GAAG,CAAC,IAAIlM,EAAE,QAAeA,EAAEkM,EAAE,WAAZ,MAA+BlM,IAAT,OAAW,OAAOA,EAAE,sBAAqB,EAAG,SAAS,CAAC,CAAG,EAAC,OAAQ,CAACkM,EAAElM,IAAIkM,EAAElM,EAAG,CAAC,EAAEA,EAAE,MAAM,UAAU,GAAGmM,KAAK,CAAC,WAAWD,EAAE,CAAC,KAAK,iBAAiB,YAAYA,EAAE,OAAO,EAAE,KAAK,iBAAiBA,CAAC,EAAE,KAAK,QAAQ,KAAKA,CAAC,EAAE,MAAMlM,EAAE,CAACkM,EAAE,GAAG,aAAc,IAAI,CAAC,KAAK,iBAAiBA,CAAC,EAAE,KAAK,KAAK,iBAAiBA,CAAC,CAAC,CAAC,EAAGA,EAAE,GAAG,OAAQ,IAAI,CAAC,IAAIlM,EAAEa,GAAUb,EAAE,KAAK,cAAf,MAAqCA,IAAT,QAAYA,EAAE,KAAM,GAASa,EAAE,KAAK,cAAf,MAAqCA,IAAT,QAAYA,EAAE,QAAQqL,EAAE,KAAK,CAAC,CAAG,EAACA,EAAE,GAAG,QAASlM,GAAG,CAAC,KAAK,KAAK,iBAAiBkM,EAAElM,CAAC,CAAC,GAAIkM,EAAE,GAAG,WAAYlM,GAAG,CAAC,KAAK,KAAK,wBAAwBkM,EAAElM,CAAC,CAAC,GAAIkM,EAAE,KAAK,SAAU,IAAI,CAAClM,EAAE,QAASkM,GAAGA,EAAG,GAAG,KAAK,QAAQ,KAAK,QAAQ,OAAQlM,GAAGA,IAAIkM,EAAG,CAAC,CAAE,EAAE,KAAK,cAAc,KAAK,GAAGlM,CAAC,EAAE,KAAK,KAAK,iBAAiBkM,CAAC,CAAC,CAAC,UAAUA,EAAE,CAAC,IAAIlM,EAAEa,EAAE,GAAG,CAAC,KAAK,WAAW,MAAM,MAAM,+BAA+B,EAAE,MAAMsL,EAAE,KAAK,WAAW,cAAc,GAAUtL,GAAUb,EAAE,KAAK,cAAf,MAAqCA,IAAT,OAAW,OAAOA,EAAE,oBAA1D,MAAsFa,IAAT,OAAW,OAAOA,EAAE,iBAAiBuL,EAAE,IAAI1L,GAAEwL,EAAEC,EAAE,CAAC,EAAE,OAAOA,EAAE,KAAK,WAAWC,CAAC,EAAE,KAAK,cAAc,KAAK,KAAK,WAAW,KAAK,QAASF,GAAG,CAACE,EAAE,kBAAkBF,CAAC,EAAE,KAAK,WAAWE,CAAC,CAAC,CAAC,CAAE,EAAEA,CAAC,CAAC,oBAAoBF,EAAE,CAAC,IAAIlM,EAAEmM,EAAE,MAAMG,GAAUH,GAAUnM,EAAE,KAAK,cAAf,MAAqCA,IAAT,OAAW,OAAOA,EAAE,WAAY,KAAtE,MAAkFmM,IAAT,OAAW,OAAOA,EAAE,cAAc,KAAK,EAAE,GAAG,CAACG,EAAE,MAAM,IAAI,GAAG,IAAIF,EAAE,KAAKM,EAAE,EAAE,OAAO7L,GAAEyL,EAAG,CAACJ,EAAElM,EAAEa,IAAI,CAACuL,GAAGA,EAAE,UAAUF,EAAErL,EAAE6L,EAAE,MAAM,OAAO,CAAC,EAAI1M,GAAG,CAAC,IAAIa,EAAEsL,EAAE,GAAGO,EAAE1M,EAAE,CAAC,KAAK,WAAW,OAAO,MAAMsM,EAAE,KAAK,WAAW,YAAW,EAAGhF,GAAU6E,GAAUtL,EAAE,KAAK,cAAf,MAAqCA,IAAT,OAAW,OAAOA,EAAE,eAAc,KAAxE,MAAsFsL,IAAT,OAAW,OAAOA,EAAE,iBAAiBQ,EAAE,KAAK,WAAW,WAAU,EAAG,YAAYN,EAAErM,EAAE2M,EAAEL,EAAEC,GAAGvM,EAAE,GAAG2M,EAAEL,EAAEF,EAAE,IAAI1L,GAAE,OAAO,OAAO,OAAO,OAAO,CAAE,EAACwL,CAAC,EAAE,CAAC,MAAMG,EAAE,IAAIE,CAAC,CAAC,EAAED,EAAEhF,CAAC,EAAE,KAAK,iBAAiB,YAAY8E,EAAE,OAAO,CAAC,EAAI,IAAI,CAACA,IAAI,KAAK,WAAWA,CAAC,EAAEA,EAAE,KAAK,EAAG,CAAC,cAAc,CAAC,KAAK,QAAQ,QAASF,GAAGA,EAAE,OAAQ,EAAE,CAAC,SAAS,CAAC,KAAK,aAAY,EAAG,MAAM,QAAO,CAAE,CAAC,2zBCKrlT,OAAApG,MAAiB,EAAC,EAEbA,KAAgB,GAAG,EAEnBA,MAAiB,GAAG,uVAPlB,cAAA8G,CAAqB,EAAAC,uUCDvB,SAAAC,EAAA,SAAuB,8NAiCzBhH,EAAa,WARrBxH,GAgBCC,EAAAwO,EAAAtO,CAAA,yFAROqH,EAAa,wEA9BT,kBAAA8G,EAAgB,CAAC,EAAAC,EACjB,oBAAAG,EAAqB,EAAK,EAAAH,GAC1B,SAAAhB,CAAoB,EAAAgB,EAE3BI,EAEJH,GAAO,KACNI,YAGKA,EAAY,KACb,IAAAC,EAASF,EACRE,IAELA,EAAO,MAAM,WAAU,iDACtBP,EAAgB,4BACQA,EAAgB,oDAO/BK,EAAaG,WAQJ,MAAAC,EAAA,IAAAC,EAAA,EAAAN,EAAqB,EAAK,IACnChN,GAAC,CACPA,EAAE,kBAAkB,mBACvBsN,EAAA,EAAAV,EAAgB,WAAW5M,EAAE,OAAO,KAAK,GACzC6L,EAAS,UAAUe,CAAa,2LAhBhBM,EAAY,k9CC2NvBpH,EAAS,IAAIA,EAAI,KAAK,IAAEyH,GAAAzH,CAAA,uCAcxB,OAAAA,OAAS,GAAE,oIAdXA,EAAS,IAAIA,EAAI,KAAK,kgBAC1BxH,GAUQC,EAAAiP,EAAA/O,CAAA,+WAYRH,GAA6DC,EAAAkP,EAAAhP,CAAA,YAC7DH,GACAC,EAAAmP,EAAAjP,CAAA,sBAFsCqH,EAAS,kBACTA,EAAkB,0PATxDxH,GAMQC,EAAAiP,EAAA/O,CAAA,uCAHGqH,EAAkB,8OAtF1BA,EAAkB,IAAA6H,GAAA7H,CAAA,2DA6CjBA,EAAO,0CAsBR,IAAA8H,EAAA9H,OAAYA,EAAW,IAAA+H,GAAA/H,CAAA,8HA9CpBA,EAAa,UAAC,GAAC,yOA9BTgI,GAAAL,EAAA,QAAA3H,EAAA,GACV,sBACA,oBAAoB,6FAetBA,EAAc,KACZA,EAAc,IAAC,QAAQA,EAAiB,QAAKA,EAAe,8BAJjDA,EAAkB,4FAuBCiI,EAAAC,EAAA,aAAAC,EAAA,qBAAAlC,GAChCjG,EACA,GAAAA,EAAiB,mGAaNA,EAAO,GAAGA,KAAK,aAAa,EAAIA,KAAK,YAAY,2CAUhCiI,EAAAG,EAAA,aAAAC,EAAA,mBAAApC,GAC5BjG,EACA,GAAAA,EAAiB,kNApErBxH,GA8GKC,EAAA6P,EAAA3P,CAAA,EA7GJC,EAmCK0P,EAAAC,CAAA,EAlCJ3P,EASQ2P,EAAAZ,CAAA,4CAMR/O,EAkBQ2P,EAAAX,CAAA,EADPhP,EAA4BgP,EAAAY,CAAA,uBAI9B5P,EAuCK0P,EAAAG,CAAA,EAtCJ7P,EAaQ6P,EAAAP,CAAA,sBACRtP,EAUQ6P,EAAAC,CAAA,wBACR9P,EAYQ6P,EAAAL,CAAA,sBAGTxP,EA8BK0P,EAAAK,CAAA,gNAzGUX,GAAAL,EAAA,QAAA3H,EAAA,GACV,sBACA,oBAAoB,EAOnBA,EAAkB,0HAqBfA,EAAa,wDAbnBA,EAAc,KACZA,EAAc,IAAC,QAAQA,EAAiB,QAAKA,EAAe,kEAJjDA,EAAkB,KAuBC,CAAA4I,GAAAC,EAAA,QAAAV,OAAA,qBAAAlC,GAChCjG,EACA,GAAAA,EAAiB,wLAaNA,EAAO,GAAGA,KAAK,aAAa,EAAIA,KAAK,YAAY,0BAUhC,CAAA4I,GAAAC,EAAA,QAAAR,OAAA,mBAAApC,GAC5BjG,EACA,GAAAA,EAAiB,kDAYdA,OAAYA,EAAW,qXArOlB,SAAA+F,CAAoB,EAAAgB,GACpB,eAAAb,CAAsB,EAAAa,GACtB,KAAA+B,CAAmB,EAAA/B,GACnB,QAAAgC,CAAgB,EAAAhC,EAChB,WAAAiC,EAAY,EAAK,EAAAjC,EACjB,aAAAkC,EAAc,EAAK,EAAAlC,GACnB,kBAAAmC,CAAuD,EAAAnC,EACvD,MAAAoC,EAAO,EAAE,EAAApC,GACT,UAAAnI,CAAyB,EAAAmI,GACzB,mBAAAqC,CAA8B,EAAArC,GAC9B,iBAAAsC,EAAgB,IAAAtC,GAChB,qBAAAuC,EAAoB,IAAAvC,EACpB,oBAAAG,EAAqB,EAAK,EAAAH,EAC1B,UAAAwC,EAAW,EAAI,EAAAxC,EAEf,cAAAyC,EAAe,CAAC,EAAAzC,EAEvB0C,GAAkB,GAAK,EAAG,IAAK,CAAC,EAChCC,EAAgBD,EAAe,CAAC,EAEhCE,EACAC,EAA8B,KAE9BC,EACAC,EACAC,EAAe,GAEfjD,EAAgB,QAkBdkD,EAAa,UAClBJ,EAAeD,EAAW,UAAS,CAClC,MAAOzD,EAAiB,EACxB,IAAKA,EAAiB,EACnB,GAAAoD,KAGJ9B,EAAA,GAAAgC,EAAeI,EAAa,IAAMA,EAAa,KAAK,GA2B/CK,EAAS,KACV,GAAAlE,GAAY4D,GACXC,EAAY,OACTnI,EAAQmI,EAAa,MACrBlI,EAAMkI,EAAa,IACzBV,EAAkBzH,EAAOC,CAAG,EAC5B8F,EAAA,EAAA2B,EAAO,EAAE,EACT3B,EAAA,GAAAoC,EAAe,IAAI,IAKhBM,EAAY,KACjBP,GAAY,WAAU,EAAG,QAASQ,GAAM,CACvCA,EAAO,OAAM,IAEdR,GAAY,aAAY,GAGnBS,EAAkB,KACvBF,IACIf,IAAS,OACZ3B,EAAA,EAAA2B,EAAO,EAAE,GAET3B,EAAA,EAAA2B,EAAO,MAAM,EACba,MAIIK,EAAmB,CAAIC,EAAgBC,IAAW,KACnDC,GACAC,GAECb,IACDU,IAAW,OACVC,IAAQ,aACXC,GAAWZ,EAAa,MAAQ,IAChCa,GAASb,EAAa,MAEtBY,GAAWZ,EAAa,MAAQ,IAChCa,GAASb,EAAa,KAGnBW,IAAQ,aACXC,GAAWZ,EAAa,MACxBa,GAASb,EAAa,IAAM,MAE5BY,GAAWZ,EAAa,MACxBa,GAASb,EAAa,IAAM,KAI9BA,EAAa,WACZ,OAAOY,GACP,IAAKC,EAAA,GAGNjD,EAAA,GAAAgC,EAAeI,EAAa,IAAMA,EAAa,KAAK,IAqBlCc,EAAA,IAAAlD,EAAA,EAAAN,GAAsBA,CAAkB,kEAkBxDM,EAAA,GAAAkC,EACCD,GACEA,EAAe,QAAQC,CAAa,EAAI,GAAKD,EAAe,SAG/D1D,EAAS,gBAAgB2D,CAAa,SAetC3D,EAAS,KACRE,GAAuBC,EAAgBmD,EAAiB,WAAW,EACjE,IAOYsB,EAAA,IAAA5E,EAAS,kBAgBxBA,EAAS,KACRE,GAAuBC,EAAgBmD,EAAiB,WAAW,UAclED,IACAc,IACA1C,EAAA,EAAA2B,EAAO,EAAE,6qBAhNb3B,EAAA,GAAEmC,EAAa5D,EAAS,eAAe6E,GAAc,OAAM,2BAEzDjB,GAAY,GAAG,aAAeQ,GAAM,CACtCA,EAAO,KAAI,2BAGTR,GAAY,GAAG,iBAAmBQ,GAAM,CAC1C3C,EAAA,GAAAgC,EAAeW,EAAO,IAAMA,EAAO,KAAK,2BAGtCR,GAAY,GAAG,iBAAgB,CAAGQ,EAAQjQ,IAAC,CAC7CA,EAAE,gBAAe,EACjBsN,EAAA,GAAAoC,EAAeO,CAAM,EACrBA,EAAO,KAAI,4BAaLP,EAAY,CACZ,MAAAiB,EAAajM,EAAU,SAAS,CAAC,EAAG,WAE1C4I,EAAA,GAAAsC,EAAoBe,EAAW,cAAc,uBAAuB,GACpErD,EAAA,GAAAqC,EAAmBgB,EAAW,cAAc,sBAAsB,GAE9DhB,GAAoBC,IACvBD,EAAiB,aAAa,OAAQ,QAAQ,EAC9CC,EAAkB,aAAa,OAAQ,QAAQ,EAC/CD,GAAkB,aAAa,aAAc,2BAA2B,EACxEC,GAAmB,aAAa,aAAc,yBAAyB,EACvED,GAAkB,aAAa,WAAY,GAAG,EAC9CC,GAAmB,aAAa,WAAY,GAAG,EAE/CD,EAAiB,iBAAiB,QAAO,KACpCF,GAAUnC,EAAA,GAAEuC,EAAe,MAAM,IAGtCD,EAAkB,iBAAiB,QAAO,KACrCH,GAAUnC,EAAA,GAAEuC,EAAe,OAAO,6BAiEtCJ,GACF,OAAO,iBAAiB,UAAYzP,GAAC,CAChCA,EAAE,MAAQ,YACbmQ,EAAoBN,EAAc,WAAW,EACnC7P,EAAE,MAAQ,cACpBmQ,EAAoBN,EAAc,YAAY,kvBC3JxC,SAAA/C,EAAA,SAAuB,2BAUvB,uBAAA8D,WAAqC,wEAyJtCC,EAAA/K,EAAS,aAAUA,MAAe,GAACgL,GAAAhL,CAAA,IAOrCA,EAAQ,KAAAyH,GAAAzH,CAAA,8eAhBAA,EAAK,GAAG,YAAcA,EAAK,GAAG,kBAAkB,UAF9DxH,GAqCKC,EAAAwS,EAAAtS,CAAA,EAjCJC,GAEKqS,EAAAxC,CAAA,EADJ7P,GAA0C6P,EAAAF,CAAA,mBAG3C3P,GAQKqS,EAAA3C,CAAA,EAPJ1P,GAA8C0P,EAAA4C,CAAA,mBAC9CtS,GAKK0P,EAAAK,CAAA,yBADJ/P,GAAsD+P,EAAAwC,CAAA,+CAHjDnL,EAAS,aAAUA,MAAe,yDAOpCA,EAAQ,4HAhBAA,EAAK,GAAG,YAAcA,EAAK,GAAG,yPAPtCoL,GAAAC,EAAA,IAAAC,EAAAtL,KAAM,GAAG,GAAAiI,EAAAoD,EAAA,MAAAC,CAAA,gBAEJD,EAAA,SAAAE,EAAAvL,KAAkB,iBAJ7BxH,GAKCC,EAAA4S,EAAA1S,CAAA,UAHKkQ,EAAA,IAAAuC,GAAAC,EAAA,IAAAC,EAAAtL,KAAM,GAAG,gBAEJ6I,EAAA,KAAA0C,OAAAvL,KAAkB,wXAeCwL,EAAAC,GAAYzL,EAAY,0GAAlDxH,GAA0DC,EAAAuE,EAAArE,CAAA,kBAAhCkQ,EAAA,OAAA2C,OAAAC,GAAYzL,EAAY,UAAA0L,GAAAtF,EAAAoF,CAAA,2PAkBxCxL,EAAW,skBAAXA,EAAW,4mBA1CrB,OAAAA,OAAU,KAAI,EAITA,KAAM,UAAS,0UApIb,OAAAnG,EAAyB,IAAI,EAAAkN,GAE7B,MAAA4E,CAAa,EAAA5E,GACb,KAAA+B,CAAmB,EAAA/B,GACnB,cAAA6E,EAAa,IAGG,QAAQ,QAAO,GAAA7E,EAC/B,aAAAkC,EAAc,EAAK,EAAAlC,EACnB,UAAAwC,EAAW,EAAI,EAAAxC,GACf,qBAAAuC,EAAoB,IAAAvC,GACpB,kBAAA8E,CAAsC,EAAA9E,GACtC,iBAAAsC,CAAiC,EAAAtC,EACjC,MAAAoC,EAAO,EAAE,EAAApC,GACT,mBAAAqC,EAAkB,UAEzBxK,EACAmH,EACAgD,EAAU,GAEV+C,EACAC,EACA7F,EAEAsD,EAAe,EAEftC,EAAqB,GAEnB,MAAA8E,EAAWlB,KAQXmB,EAAe,KACpBzE,EAAA,GAAAzB,EAAW/B,GAAW,OAAM,CAChB,UAAApF,EACR,GAAAiN,CAAA,IAEJK,GAAiBrS,GAAO,GAAG,EAAE,KAAMsS,GAAY,CAC1C,GAAAA,GAAgBpG,SACZA,EAAS,KAAKoG,CAAY,KA4C9BjD,EAAiB,MACtBzH,EACAC,IAAW,CAEX8F,EAAA,EAAA2B,EAAO,EAAE,QACHiD,EAAcrG,GAAU,iBAC1BqG,GACG,MAAA7G,GACL6G,EACA3K,EACAC,EACAmK,EAAkB,UAAU,EAC3B,WAAYQ,GAAuB,OAC9BT,EAAa,CAAES,CAAW,EAAG,QAAQ,EAC3CtG,GAAU,QAAO,OACjBnH,EAAU,UAAY,GAAEA,CAAA,IAE1BoN,EAAS,MAAM,GAGD,eAAAM,EAAW1H,EAAY,CAC/B,MAAAsH,GAAiBtH,CAAI,EAAE,KAAMuH,GAAY,OACzCA,GAAgBtS,GAAO,kBACrBkM,GAAU,KAAKoG,CAAY,IAMpCnF,GAAO,KACN,OAAO,iBAAiB,UAAY9M,GAAC,CAC/B,CAAA6L,GAAYmB,IACbhN,EAAE,MAAQ,cAAgBiP,IAAS,OACtCrD,GAAWC,EAAU,EAAG,EACd7L,EAAE,MAAQ,aAAeiP,IAAS,QAC5CrD,GAAWC,EAAQ,GAAM,iDAuBInH,EAAS0I,8DAItBwE,EAAOxE,8DAKNyE,EAAWzE,6kBAzJ5BE,EAAA,GAAAlM,EAAMzB,GAAO,GAAG,mBA+CZ+E,IAAc,SAChBmH,IAAa,QAAWA,EAAS,QAAO,OAC5CnH,EAAU,UAAY,GAAEA,CAAA,EACxBqN,IACAzE,EAAA,GAAAuB,EAAU,EAAK,qBAGbhD,GAAU,GAAG,SAAW5K,GAAa,CACvCqM,EAAA,GAAAtB,EAAiB/K,CAAQ,EACzB4Q,QAAgBA,EAAY,YAAcN,GAAYtQ,CAAQ,EAAA4Q,CAAA,qBAG5DhG,GAAU,GACZ,aACC7B,GACA4H,QAAYA,EAAQ,YAAcL,GAAYvH,CAAW,EAAA4H,CAAA,oBAGxD/F,GAAU,GAAG,QAAO,KACjB8F,EAAkB,SAGtB9F,GAAU,KAAI,EAFdA,GAAU,KAAI,qBAMbA,GAAU,GAAG,SAAQ,KACvByB,EAAA,GAAAuB,EAAU,EAAK,EACfiD,EAAS,MAAM,qBAEbjG,GAAU,GAAG,QAAO,KACtByB,EAAA,GAAAuB,EAAU,EAAK,EACfiD,EAAS,OAAO,qBAEdjG,GAAU,GAAG,OAAM,KACrByB,EAAA,GAAAuB,EAAU,EAAI,EACdiD,EAAS,MAAM,wBA8Bb1Q,GAAOgR,EAAWhR,CAAG,qkBCxHf,CAAAwP,iCAAqC,kWAmCxC9K,EAAoB,IAAAyH,GAAAzH,CAAA,IAKpBA,EAAiB,IAAAuM,GAAAvM,CAAA,2SANvBxH,GAmBKC,EAAAiG,EAAA/F,CAAA,yEAlBCqH,EAAoB,kGAKpBA,EAAiB,unBAJD,KAAAA,KAAM,IAAe,SAAAA,EAAM,cAAaA,KAAM,gHAA9C6I,EAAA,IAAA2D,EAAA,KAAAxM,KAAM,KAAe6I,EAAA,IAAA2D,EAAA,SAAAxM,EAAM,cAAaA,KAAM,2LAC/CyM,GAAiB,MAAAzM,KAAK,iBAAiB,sEAAtB6I,EAAA,KAAA6D,EAAA,MAAA1M,KAAK,iBAAiB,yeATtD2M,SACC,SACA3M,EAAK,IAAIA,EAAI,GAAC,aAAa,0CAG9B,OAAAA,OAAU,KAAI,gMAHXA,EAAK,IAAIA,EAAI,GAAC,aAAa,sSAzBvB,UAAAnG,EAAyB,IAAI,EAAAkN,GAC7B,MAAA4E,CAAa,EAAA5E,EACb,YAAA6F,EAAa,EAAI,EAAA7F,EACjB,sBAAA8F,EAAuB,EAAI,EAAA9F,EAC3B,mBAAA+F,EAAoB,EAAK,EAAA/F,GACzB,KAAA+B,CAAmB,EAAA/B,GACnB,kBAAA8E,CAAsC,EAAA9E,GACtC,iBAAAsC,CAAiC,EAAAtC,EACjC,UAAAwC,EAAW,EAAI,EAAAxC,EAEpB,MAAAiF,EAAWlB,aA8BIjR,GACZA,gCACWkT,GAAoBlT,EAAM,IAAK,KAAK,cADjC,okBAvBpBA,GAASmS,EAAS,SAAUnS,CAAK,kPC9BrC,SAASK,GAAEA,EAAEkM,EAAErL,EAAEsL,EAAE,CAAC,OAAO,IAAItL,IAAIA,EAAE,UAAW,SAASyL,EAAE5L,EAAE,CAAC,SAAS0L,EAAEpM,EAAE,CAAC,GAAG,CAACqM,EAAEF,EAAE,KAAKnM,CAAC,CAAC,CAAC,OAAOA,EAAN,CAASU,EAAEV,CAAC,CAAC,CAAC,CAAC,SAAS0M,EAAE1M,EAAE,CAAC,GAAG,CAACqM,EAAEF,EAAE,MAAMnM,CAAC,CAAC,CAAC,OAAOA,EAAN,CAASU,EAAEV,CAAC,CAAC,CAAC,CAAC,SAASqM,EAAErM,EAAE,CAAC,IAAIkM,EAAElM,EAAE,KAAKsM,EAAEtM,EAAE,KAAK,GAAGkM,EAAElM,EAAE,MAAMkM,aAAarL,EAAEqL,EAAE,IAAIrL,EAAG,SAASb,EAAE,CAACA,EAAEkM,CAAC,CAAC,CAAC,GAAI,KAAKE,EAAEM,CAAC,CAAC,CAACL,GAAGF,EAAEA,EAAE,MAAMnM,EAAEkM,GAAG,EAAE,GAAG,MAAM,CAAC,EAAG,CAAqD,MAAMA,EAAC,CAAC,aAAa,CAAC,KAAK,UAAU,GAAG,KAAK,GAAG,KAAK,iBAAiB,KAAK,GAAG,KAAK,mBAAmB,CAAC,iBAAiB,EAAE,EAAErL,EAAE,CAAC,GAAG,KAAK,UAAU,CAAC,IAAI,KAAK,UAAU,CAAC,EAAE,IAAI,KAAK,KAAK,UAAU,CAAC,EAAE,IAAI,CAAC,EAAiBA,GAAE,KAAK,CAAC,MAAMA,EAAE,IAAI,CAAC,KAAK,oBAAoB,EAAEA,CAAC,EAAE,KAAK,oBAAoB,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,iBAAiB,EAAEA,CAAC,EAAEA,EAAE,MAAM,IAAI,KAAK,oBAAoB,EAAE,CAAC,CAAC,CAAC,oBAAoB,EAAE,EAAE,CAAC,IAAIA,GAAUA,EAAE,KAAK,UAAU,CAAC,KAA1B,MAAuCA,IAAT,QAAYA,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,KAAK,GAAG,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,UAAU,EAAE,CAAC,KAAK,KAAK,EAAE,CAAC,KAAK,UAAU,CAAC,GAAG,KAAK,UAAU,CAAC,EAAE,QAASb,GAAGA,EAAE,GAAG,CAAC,CAAC,CAAE,CAAC,CAAC,MAAMa,WAAUqL,EAAC,CAAC,YAAY,EAAE,CAAC,MAAO,EAAC,KAAK,cAAc,GAAG,KAAK,QAAQ,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,KAAK,WAAW,EAAE,KAAK,OAAM,CAAE,CAAC,SAAS,CAAC,KAAK,KAAK,SAAS,EAAE,KAAK,cAAc,QAAS,GAAG,EAAG,EAAE,CAAC,CAAC,MAAMC,GAAE,CAAC,aAAa,YAAY,aAAa,YAAY,WAAW,EAAE,MAAMG,WAAUzL,EAAC,CAAC,YAAY,EAAE,CAAC,IAAI,EAAE,MAAM,OAAO,OAAO,OAAO,OAAO,GAAG,CAAC,EAAE,CAAC,oBAA2B,EAAE,EAAE,sBAAZ,MAA0C,IAAT,OAAW,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,OAAO,KAAK,KAAK,cAAc,IAAI,CAAC,OAAO,OAAO,EAAE,CAAC,OAAO,IAAIyL,GAAE,GAAG,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,IAAI,aAAazL,EAAE,EAAE,wBAAwB,CAAC,EAAEsL,EAAE,EAAE,eAAc,EAAGtL,EAAE,QAAQsL,CAAC,EAAE,MAAM,EAAEA,EAAE,kBAAkBzL,EAAE,IAAI,aAAa,CAAC,EAAE0L,EAAE,EAAE,EAAE,WAAW,IAAI,EAAE,MAAM,EAAE,IAAI,CAACD,EAAE,uBAAuBzL,CAAC,EAAE,KAAK,aAAa,KAAK,WAAW,QAAQ,YAAY,EAAE,KAAK,WAAW,QAAQ,SAAS,GAAG,KAAK,WAAW,KAAK,GAAG,CAACA,CAAC,EAAE0L,CAAC,GAAG,EAAE,sBAAsB,CAAC,CAAC,EAAE,OAAO,EAAG,EAAC,IAAI,CAAC,qBAAqB,CAAC,EAAWvL,GAAE,WAAU,EAAY,GAAE,MAAO,EAAC,CAAC,SAASqL,EAAE,CAAC,OAAOlM,GAAE,KAAK,OAAO,OAAQ,WAAW,CAAC,IAAIA,EAAE,GAAG,CAACA,EAAE,MAAM,UAAU,aAAa,aAAa,CAAC,MAAM,CAAiBkM,GAAE,UAAW,CAAC,SAASA,EAAE,QAAQ,CAAC,CAAC,CAAC,OAAOlM,EAAN,CAAS,MAAM,IAAI,MAAM,mCAAmCA,EAAE,OAAO,CAAC,CAAC,MAAMa,EAAE,KAAK,gBAAgBb,CAAC,EAAE,OAAO,KAAK,cAAc,KAAK,KAAK,KAAK,UAAUa,CAAC,CAAC,EAAE,KAAK,OAAOb,EAAEA,CAAC,CAAG,EAAC,SAAS,CAAC,KAAK,SAAS,KAAK,OAAO,YAAY,QAAS,GAAG,EAAE,KAAI,GAAK,KAAK,OAAO,KAAK,KAAK,cAAc,KAAK,CAAC,eAAekM,EAAE,CAAC,OAAOlM,GAAE,KAAK,OAAO,OAAQ,WAAW,CAAC,MAAMA,EAAE,KAAK,SAAS,MAAM,KAAK,SAASkM,CAAC,GAAGrL,EAAE,KAAK,eAAe,IAAI,cAAcb,EAAE,CAAC,SAAS,KAAK,QAAQ,UAAUmM,GAAE,KAAMnM,GAAG,cAAc,gBAAgBA,CAAC,GAAI,mBAAmB,KAAK,QAAQ,kBAAkB,CAAC,EAAE,KAAK,cAAca,EAAE,KAAK,cAAe,EAAC,MAAMyL,EAAE,CAAE,EAACzL,EAAE,gBAAgBb,GAAG,CAACA,EAAE,KAAK,KAAK,GAAGsM,EAAE,KAAKtM,EAAE,IAAI,CAAC,EAAEa,EAAE,OAAO,IAAI,CAAC,IAAIb,EAAE,MAAMkM,EAAE,IAAI,KAAKI,EAAE,CAAC,KAAKzL,EAAE,QAAQ,CAAC,EAAE,KAAK,KAAK,aAAaqL,CAAC,EAAO,KAAK,QAAQ,sBAAlB,MAAgDlM,EAAE,KAAK,cAAf,MAAqCA,IAAT,QAAYA,EAAE,KAAK,IAAI,gBAAgBkM,CAAC,CAAC,EAAE,EAAErL,EAAE,MAAK,EAAG,KAAK,KAAK,cAAc,CAAC,CAAG,EAAC,aAAa,CAAC,IAAI,EAAE,QAA6B,EAAE,KAAK,iBAAf,MAAwC,IAAT,OAAW,OAAO,EAAE,SAAlE,WAAwE,CAAC,UAAU,CAAC,IAAI,EAAE,QAA0B,EAAE,KAAK,iBAAf,MAAwC,IAAT,OAAW,OAAO,EAAE,SAA/D,QAAqE,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,YAAa,KAAW,EAAE,KAAK,iBAAf,MAAwC,IAAT,QAAY,EAAE,KAAI,EAAG,CAAC,gBAAgB,CAAC,IAAI,EAAE,KAAK,YAAW,KAAa,EAAE,KAAK,iBAAf,MAAwC,IAAT,QAAY,EAAE,MAAO,EAAC,KAAK,KAAK,cAAc,EAAE,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,SAAQ,KAAa,EAAE,KAAK,iBAAf,MAAwC,IAAT,QAAY,EAAE,SAAS,KAAK,KAAK,eAAe,EAAE,CAAC,OAAO,0BAA0B,CAAC,OAAOb,GAAE,KAAK,OAAO,OAAQ,WAAW,CAAC,OAAO,UAAU,aAAa,iBAAkB,EAAC,KAAM,GAAG,EAAE,OAAQA,GAAkBA,EAAE,OAAjB,aAAyB,EAAG,EAAC,SAAS,CAAC,MAAM,QAAS,EAAC,KAAK,cAAe,EAAC,KAAK,SAAS,CAAC,qOCGlqH,CAAA4Q,iCAAqC,6GAsCtC9K,EAAU,yBAAf,OAAIjF,GAAA,iKAACiF,EAAU,sBAAf,OAAIjF,GAAA,6HAAJ,sDAFgByQ,EAAAxL,KAAK,qBAAqB,8EAA5CxH,GAAsDC,EAAAuU,EAAArU,CAAA,kBAApCkQ,EAAA,GAAA2C,OAAAxL,KAAK,qBAAqB,OAAA0L,GAAAtF,EAAAoF,CAAA,wCAGPA,EAAAxL,KAAU,MAAK,yCAApCgN,EAAA,QAAAC,EAAAjN,KAAU,iCAAzBxH,GAA4DC,EAAAuU,EAAArU,CAAA,kBAAxBkQ,EAAA,GAAA2C,OAAAxL,KAAU,MAAK,KAAA0L,GAAAtF,EAAAoF,CAAA,EAApC3C,EAAA,GAAAoE,OAAAjN,KAAU,wGAJtBA,EAAU,GAAC,SAAW,EAAC+H,wJAFlB/H,EAAU,GAAC,SAAW,UAHjCxH,GAYQC,EAAAyU,EAAAvU,CAAA,oGATGqH,EAAU,GAAC,SAAW,2EA/BrB,KAAA8I,CAAmB,EAAA/B,GACnB,WAAAoG,EAAU,IAAApG,EAEf,MAAAiF,EAAWlB,0HAIhB,QACIsC,EAAW,GACfC,GAAa,yBAAwB,EAAG,KACtCC,GAA0B,CAC1B9F,EAAA,EAAA2F,EAAaG,CAAO,EACpBA,EAAQ,QAASC,GAAM,CAClBA,EAAO,UACVH,EAAY,KAAKG,CAAM,IAGzB/F,EAAA,EAAA2F,EAAaC,CAAW,UAGlBI,GACJ,MAAAA,aAAe,cAAgBA,EAAI,MAAQ,mBAC9CxB,EAAS,QAASlD,EAAK,8BAA8B,GAEhD0E,8dC2EmCxN,EAAW,oEAAnDxH,GAA0DC,EAAAuE,EAAArE,CAAA,2BAAlBqH,EAAW,8CA1CTyN,EAAAzN,KAAK,cAAc,WAazD0N,EAAA1N,KAAK,YAAY,aAcjB2N,EAAA3N,KAAK,YAAY,iBAYsB4N,EAAA5N,KAAK,cAAc,kCAE1D,IAAA6N,EAAA7N,OAAWA,EAAuB,IAAA+H,GAAA/H,CAAA,8ZApClBiI,GAAAL,EAAA,QAAAkG,EAAA,gBAAA9N,EAAO,YAAQ,EAAK,qBAAuB,IAAE,ySAVpExH,GAmDKC,EAAAgQ,EAAA9P,CAAA,EAlDJC,EAgDK6P,EAAAF,CAAA,EA/CJ3P,EAIA2P,EAAAZ,CAAA,yBAEA/O,EAWA2P,EAAAX,CAAA,yBAEAhP,EAYA2P,EAAAL,CAAA,yBAEAtP,EAKA2P,EAAAG,CAAA,+BACA9P,EAIA2P,EAAAH,CAAA,8LAxC2C,CAAAQ,GAAAC,EAAA,IAAA4E,OAAAzN,KAAK,cAAc,OAAA0L,GAAAqC,EAAAN,CAAA,GAazD,CAAA7E,GAAAC,EAAA,IAAA6E,OAAA1N,KAAK,YAAY,OAAA0L,GAAAsC,EAAAN,CAAA,GARD,CAAA9E,GAAAC,EAAA,GAAAiF,OAAA,gBAAA9N,EAAO,YAAa,uBAAuB,IAAE,sCAsB7D,CAAA4I,GAAAC,EAAA,IAAA8E,OAAA3N,KAAK,YAAY,OAAA0L,GAAAuC,EAAAN,CAAA,GAYsB,CAAA/E,GAAAC,EAAA,IAAA+E,OAAA5N,KAAK,cAAc,OAAA0L,GAAAwC,EAAAN,CAAA,EAE1D5N,OAAWA,EAAuB,oYAjG7B,OAAAmO,CAAoB,EAAApH,GACpB,KAAA+B,CAAmB,EAAA/B,EAE1BoG,EAAU,GACViB,EACAC,EACAC,EACAC,EACAC,GAEO,YAAAC,CAAmB,EAAA1H,GACnB,wBAAA2H,CAA4C,EAAA3H,EAC5C,QAAA4H,EAAS,EAAK,EAAA5H,4CA0CZqH,EAAY9G,kBAEP,MAAAoD,EAAA,IAAAyD,EAAO,2DAIZI,EAAUjH,+BAGhB6G,EAAO,aACVA,EAAO,gBAAe,EACtBA,EAAO,cAAa,GAGrBA,EAAO,cAAa,6CAKVK,EAAgBlH,gCAItB6G,EAAO,aACVA,EAAO,gBAAe,EACtBA,EAAO,cAAa,GAGrBA,EAAO,cAAa,6CAMVE,EAAW/G,kBAEN,MAAAqD,EAAA,IAAAwD,EAAO,2DAGZG,EAAYhH,kBAEP,MAAAsH,EAAA,IAAAT,EAAO,4RAjFtBA,EAAO,GAAG,eAAc,KAC1BA,EAAO,SAAQ,EAEf3G,EAAA,EAAA4G,EAAa,MAAM,QAAU,OAAMA,CAAA,EACnC5G,EAAA,EAAA+G,EAAW,MAAM,QAAU,OAAMA,CAAA,EACjC/G,EAAA,EAAA6G,EAAY,MAAM,QAAU,QAAOA,CAAA,kBAGjCF,EAAO,GAAG,aAAY,KACpBA,EAAO,aACVA,EAAO,gBAAe,EACtBA,EAAO,cAAa,GAErBA,EAAO,QAAO,EAEd3G,EAAA,EAAA4G,EAAa,MAAM,QAAU,OAAMA,CAAA,EACnC5G,EAAA,EAAA+G,EAAW,MAAM,QAAU,OAAMA,CAAA,EACjC/G,EAAA,EAAA6G,EAAY,MAAM,QAAU,OAAMA,CAAA,MAClCD,EAAa,SAAW,GAAKA,CAAA,kBAG3BD,EAAO,GAAG,eAAc,KAC1B3G,EAAA,EAAA6G,EAAY,MAAM,QAAU,OAAMA,CAAA,EAClC7G,EAAA,EAAA8G,EAAa,MAAM,QAAU,QAAOA,CAAA,EACpC9G,EAAA,EAAA+G,EAAW,MAAM,QAAU,OAAMA,CAAA,EACjC/G,EAAA,GAAAgH,EAAiB,MAAM,QAAU,OAAMA,CAAA,kBAGrCL,EAAO,GAAG,gBAAe,KAC3B3G,EAAA,EAAA6G,EAAY,MAAM,QAAU,QAAOA,CAAA,EACnC7G,EAAA,EAAA8G,EAAa,MAAM,QAAU,OAAMA,CAAA,EACnC9G,EAAA,EAAA4G,EAAa,MAAM,QAAU,OAAMA,CAAA,EACnC5G,EAAA,EAAA+G,EAAW,MAAM,QAAU,OAAMA,CAAA,EACjC/G,EAAA,GAAAgH,EAAiB,MAAM,QAAU,OAAMA,CAAA,ohBCpD/B,SAAAxH,EAAA,SAAuB,2BAEvB,uBAAA8D,WAAqC,wDA+MtCC,EAAA/K,EAAS,aAAUA,MAAe,GAAC6H,GAAA7H,CAAA,yBAGnCA,EAAM,IAAAgL,8MANbxS,GAYKC,EAAAgQ,EAAA9P,CAAA,EAXJC,GAAiD6P,EAAAzL,CAAA,mBACjDpE,GASK6P,EAAAF,CAAA,6CARCvI,EAAS,aAAUA,MAAe,0LACTwL,EAAAC,GAAYzL,EAAY,8FAArDxH,GAA6DC,EAAAuE,EAAArE,CAAA,kBAAhCkQ,EAAA,UAAA2C,OAAAC,GAAYzL,EAAY,UAAA0L,GAAAtF,EAAAoF,CAAA,oIAKrDhT,GAAyDC,EAAAuE,EAAArE,CAAA,kEAFjC6S,EAAAC,GAAYzL,EAAO,yFAA3CxH,GAAmDC,EAAAuE,EAAArE,CAAA,kBAA3BkQ,EAAA,UAAA2C,OAAAC,GAAYzL,EAAO,UAAA0L,GAAAtF,EAAAoF,CAAA,iGAapB,wBAAAxL,KAAiB,wBAC7B,YAAAyL,GAAYzL,EAAO,kNADP6I,EAAA,OAAAgG,EAAA,wBAAA7O,KAAiB,yBAC7B6I,EAAA,WAAAgG,EAAA,YAAApD,GAAYzL,EAAO,sQAOrBA,EAAkB,0EAKhB,8FANEA,EAAiB,yBAAjBA,EAAiB,mRACrBA,EAAkB,0NADdA,EAAiB,sPA5B5B+K,GAAA/K,EAAU,KAAAA,EAAkB,KAAAA,KAAiB,yBAAuByH,GAAAzH,CAAA,EAgBrE8O,EAAA9O,OAAwBA,EAAa,IAAAuM,GAAAvM,CAAA,EAUrC8H,EAAA9H,MAAqBA,EAAa,IAAA+H,GAAA/H,CAAA,4RAlCxCxH,GAmDKC,EAAAkQ,EAAAhQ,CAAA,EAlDJC,GAIC+P,EAAAJ,CAAA,mBACD3P,GAAsE+P,EAAAF,CAAA,8FAEhEzI,EAAU,KAAAA,EAAkB,KAAAA,KAAiB,+EAgB9CA,OAAwBA,EAAa,yGAUrCA,MAAqBA,EAAa,0PA3N5B,KAAAmJ,CAAY,EAAApC,GACZ,KAAA+B,CAAmB,EAAA/B,GACnB,cAAA6E,CAGmB,EAAA7E,GACnB,kBAAA8E,CAAsC,EAAA9E,GACtC,iBAAAsC,EAAgB,CAC1B,wBAAyB,KAAAtC,GAEf,mBAAAqC,CAA8B,EAAArC,EAC9B,UAAAwC,EAAW,EAAI,EAAAxC,EAEtBgI,EACAC,EACAjG,EAAU,GAEVkG,EACAC,EAEAf,EACAgB,EAA+B,KAG/BrD,EACAC,EACA7F,EACAlB,EAAU,EACVoK,EACAT,EAAS,GAETnF,EAAe,QAEb6F,EAAc,KACnB,cAAcD,CAAQ,EACtB5H,EAAA,GAAA4H,EAAW,sBACVpK,IAAOA,CAAA,GACL,OAGEgH,EAAWlB,KA6EXwE,EAAmB,KACpBJ,GAAqB1H,EAAA,EAAA0H,EAAoB,UAAY,GAAEA,CAAA,EACvDH,IAAgB,QAAWA,EAAY,QAAO,EAC7CG,IACLH,EAAc/K,GAAW,OAAM,IAC3B6H,EACH,UAAW,GACX,UAAWqD,IAGZ1H,EAAA,EAAA2G,EAASY,EAAY,eAAe1B,GAAa,OAAM,IACvDc,EAAO,SAAQ,IAGVoB,EAAyB,KAC1B,IAAAC,EAAYP,EACX,CAAAE,IAAkBK,OACvBR,EAAoBhL,GAAW,OAAM,CACpC,UAAWwL,EACX,IAAKL,EACF,GAAAtD,MAiBC3C,EAAiB,MACtBzH,EACAC,IAAW,CAEX8F,EAAA,EAAA2B,EAAO,MAAM,QACPiD,EAAc4C,EAAkB,iBAClC5C,GAAW,MACR7G,GAAc6G,EAAa3K,EAAOC,CAAG,EAAE,KAAI,MACzC+N,GAAwB,OACxB7D,EAAa,CAAE6D,CAAY,EAAG,QAAQ,QACtC7D,EAAa,CAAE6D,CAAY,EAAG,gBAAgB,EACpDT,EAAkB,QAAO,EACzBO,MAGHvD,EAAS,MAAM,GAGhBhF,GAAO,KACNsI,IAEA,OAAO,iBAAiB,UAAYpV,GAAC,CAChCA,EAAE,MAAQ,aACb4L,GAAWkJ,EAAmB,EAAG,EACvB9U,EAAE,MAAQ,aACpB4L,GAAWkJ,EAAiB,GAAM,gDASzBE,EAAmB5H,qDAGf2H,EAAkB3H,qDAIfwE,EAAOxE,6DAQLyE,EAAWzE,0DAkBf0H,EAAiBnV,oZA1K/BsU,GAAQ,GAAG,eAAc,KAIvB,GAHJkB,IACA7H,EAAA,GAAAmH,EAAS,EAAI,EACb3C,EAAS,iBAAiB,EACtB3C,EAAiB,wBAAuB,CACvC,IAAAqG,EAAiBR,EACjBQ,IAAgBA,EAAe,MAAM,QAAU,mCAIlDvB,GAAQ,GAAG,mBAAqBtR,GAAI,CACtC2K,EAAA,GAAAxC,EAAU,CAAC,EACXwC,EAAA,GAAAmH,EAAS,EAAK,EACd,cAAcS,CAAQ,YAEfO,EAAY,MAAS9S,EAAK,cAI1B+S,EAAqB,UAHP,aAAY,CAC/B,WAAY/D,EAAkB,aAEI,gBAAgB8D,CAAY,EAE3DC,SACGrK,GAAcqK,CAAY,EAAE,WAAYvE,IAAiB,OACxDO,EAAa,CAAEP,EAAK,EAAG,QAAQ,QAC/BO,EAAa,CAAEP,EAAK,EAAG,gBAAgB,UAEvCnR,GACR,QAAQ,MAAMA,CAAC,4BAIdiU,GAAQ,GAAG,eAAc,KAC3BnC,EAAS,iBAAiB,EAC1B,cAAcoD,CAAQ,uBAGpBjB,GAAQ,GAAG,gBAAe,KAC5BkB,0BAGEL,GAAmB,GAAG,SAAW7T,GAAa,CAChDqM,EAAA,GAAAtB,EAAiB/K,CAAQ,EACzB4Q,QAAgBA,EAAY,YAAcN,GAAYtQ,CAAQ,EAAA4Q,CAAA,wBAG5DiD,GAAmB,GACrB,aACC9K,GACA4H,QAAYA,EAAQ,YAAcL,GAAYvH,CAAW,EAAA4H,CAAA,qBAGxDkD,GAAmB,GAAG,QAAO,KAC/BhD,EAAS,OAAO,EAChBxE,EAAA,GAAAuB,EAAU,EAAK,sBAGbiG,GAAmB,GAAG,OAAM,KAC9BhD,EAAS,MAAM,EACfxE,EAAA,GAAAuB,EAAU,EAAI,sBAGZiG,GAAmB,GAAG,SAAQ,KAChChD,EAAS,MAAM,EACfxE,EAAA,GAAAuB,EAAU,EAAK,uBA2BboF,GAAQ,GAAG,aAAetR,GAAI,CAChC2K,EAAA,EAAA2H,EAAgB,IAAI,gBAAgBtS,CAAI,GAElC,MAAAgT,EAAaX,EACbM,EAAYP,EAEdY,IAAYA,EAAW,MAAM,QAAU,QACvCL,GAAaL,IAChBK,EAAU,UAAY,GACtBD,wlBClKO,SAAAvI,EAAA,SAAuB,sFA6CfhH,EAAS,GAAG,QAAU,MAAM,UAF5CxH,GAGCC,EAAAiG,EAAA/F,CAAA,uCADeqH,EAAS,GAAG,QAAU,MAAM,wDA6BzC8P,EAAA9P,KAAK,cAAc,0LAVrBxH,GAWQC,EAAAiP,EAAA/O,CAAA,EAJPC,GAEM8O,EAAAqI,CAAA,0DACLlH,EAAA,IAAAiH,OAAA9P,KAAK,cAAc,OAAA0L,GAAAsE,EAAAF,CAAA,wDAbnB9P,EAAgB,GAAGA,KAAK,aAAa,EAAIA,KAAK,YAAY,uLAVpDA,EAAgB,GAAG,qBAAuB,aAAa,6BAD/DxH,GAYQC,EAAAiP,EAAA/O,CAAA,EALPC,GAGM8O,EAAAuI,CAAA,wEACLjQ,EAAgB,GAAGA,KAAK,aAAa,EAAIA,KAAK,YAAY,QAAA0L,GAAAsC,EAAAN,CAAA,iBAVpD1N,EAAgB,GAAG,qBAAuB,aAAa,iGAT5D+K,EAAA/K,KAAiB,yBAAuBuM,GAAAvM,CAAA,yBAOvCA,EAAS,GAAA+H,qUARhBvP,GAuCKC,EAAAgQ,EAAA9P,CAAA,yBAhCJC,GA+BK6P,EAAAF,CAAA,kDArCAvI,KAAiB,kWAnCX,cAAAwP,EAAY,EAAK,EAAAzI,EACjB,kBAAAmJ,EAAmB,EAAK,EAAAnJ,GACxB,KAAAoJ,CAAgB,EAAApJ,GAChB,OAAAoH,CAAkB,EAAApH,GAClB,KAAA+B,CAAmB,EAAA/B,GACnB,kBAAA8E,CAAsC,EAAA9E,GACtC,iBAAAsC,EAAgB,CAC1B,wBAAyB,KAAAtC,EAGtBgI,EACAqB,EAEAlB,EAEA/B,EAAU,GAEdnG,GAAO,KACNsI,YAGKA,EAAmB,KACpBP,IAAgB,QAAWA,EAAY,QAAO,EAC7CG,IACLH,EAAc/K,GAAW,OAAM,IAC3B6H,EACH,OAAQ,IACR,UAAWqD,IAGZ1H,EAAA,EAAA4I,EAAiBrB,EAAY,eAAe1B,GAAa,OAAM,gDAOnD6B,EAAmB5H,wBAS5B8I,GAAgB,QAAO,EACvBD,YAaAC,GAAgB,SAAQ,EACxBjC,q6BCpEK,YAAAkC,GAAY,UAAAC,GAAW,sBAAAxF,EAAA,SAAqC,4FAyQzD9K,EAAoB,GAAGA,KAAM,IAAM,cACnC,oBAHAA,EAAK,+iBAELA,EAAoB,GAAGA,KAAM,IAAM,siBA1CzC,OAAAA,OAAkB,aAAY,EAyBzBA,OAAkB,SAAQ,wtBAIzBA,EAAW,yWA5B2B,EAAI,kBAArBA,EAAK,iDAC/BA,EAAS,85DARV2M,GACC,MAAA3M,EAAkB,eAAYA,OAAU,WACxCA,EAAK,IAAIA,EAAI,GAAC,aAAa,iDAG7BA,EAAK,KAAK,MAAQA,EAAS,qFAiEyBA,EAAK,8PAlE/DxH,GAmEKC,EAAAiG,EAAA/F,CAAA,2FAtEGkQ,EAAA,OAAA0H,EAAA,MAAAvQ,EAAkB,eAAYA,OAAU,yBACxCA,EAAK,IAAIA,EAAI,GAAC,aAAa,6ZApK5B,MAAAwQ,GAAmB,IACnBC,GAAmB,oDArCd,OAAA5W,EAAyB,IAAI,EAAAkN,GAC7B,MAAA4E,CAAa,EAAA5E,GACb,KAAA2J,CAAY,EAAA3J,EACZ,YAAA6F,EAAa,EAAI,EAAA7F,EACjB,sBAAA8F,EAAuB,EAAK,EAAA9F,GAC5B,QAAA4J,EAAO,CAIa,aAAc,QAAQ,GAAA5J,EAC1C,SAAA6J,EAAU,EAAK,EAAA7J,EACf,WAAA8J,EAAY,EAAK,EAAA9J,GACjB,KAAA+B,CAAmB,EAAA/B,GACnB,kBAAA8E,CAAsC,EAAA9E,GACtC,qBAAAuC,EAAoB,IAAAvC,GACpB,iBAAAsC,EAAgB,IAAAtC,GAChB,SAAA+J,CAAiB,EAAA/J,GACjB,cAAAgK,CAAsC,EAAAhK,GACtC,mBAAAqC,EAAkB,UAClB,UAAAG,EAAW,EAAI,EAAAxC,QAGpBiK,EAAYX,GAAgC,cAAc,EAM5D,IAAAb,EAAY,GACZyB,EACA9H,EAAO,GACP+H,EACAC,EAAc,GACdC,EAAuC,GACvCC,EAAS,GAITC,EAAY,GACZC,WAKKC,GAAW,CACnBD,EAAe,eACP,sBAA2B,2KAC3B,sBAAuC,oEAI5CV,GACHW,IAGK,MAAAxF,EAAWlB,KAiBXc,EAAa,MAClB6F,EACArV,IAA6C,CAEzC,IAAAsV,GAAkB,SAAKD,EAAO,WAAW,EACvC,MAAAE,SAAYC,GAAa,CAAEF,EAAW,EAAGtV,IAAU,QAAQ,EACjEoL,EAAA,EAAA3N,GACQ,MAAAgY,GAAOF,GAAKjB,EAAM,OAAWM,CAAS,IAAI,OAChD,OAAO,EAEP,CAAC,GAEHhF,EAAS5P,EAAOvC,CAAK,GAGtByW,GAAS,KACJO,GAAaI,GAAYA,EAAS,QAAU,YAC/CA,EAAS,KAAI,mBAIAa,GAAa,KACvBC,MAGHA,EAAM,MAAS,UAAU,aAAa,aAAY,CAAG,MAAO,EAAI,SACxDvE,GACH,cAAU,aAAY,CAC1BxB,EAAS,QAASlD,EAAK,yBAAyB,UAG7C,GAAA0E,aAAe,cAAgBA,EAAI,MAAQ,kBAAiB,CAC/DxB,EAAS,QAASlD,EAAK,8BAA8B,gBAGhD0E,EAEH,GAAAuE,GAAU,SACVlB,EAAS,QACH,cAAAmB,EAAe,SAAAC,EAAQ,EAAM,SAAAC,WAAmB,QAAQ,IAChEX,CAAe,EAEV,MAAAU,SAAeC,GAAO,GAC5BjB,MAAee,EAAcD,EAAU,UAAU,WAAW,GAC5Dd,EAAS,iBAAiB,gBAAiBkB,CAAY,OAEvDlB,EAAQ,IAAO,cAAcc,CAAM,EACnCd,EAAS,iBAAiB,gBAAkB7U,GAAK,CAChDkV,EAAa,KAAKlV,EAAM,IAAI,IAE7B6U,EAAS,iBAAiB,OAAM,UAC/BzJ,EAAA,GAAAgI,EAAY,EAAK,QACX5D,EAAc0F,EAAc,QAAQ,QACpC1F,EAAc0F,EAAc,gBAAgB,EAClDA,EAAY,KAGdD,EAAS,IAGK,eAAAc,EAAa/V,EAAiB,CACxC,IAAA+I,EAAe,MAAA/I,EAAM,KAAK,YAAW,EACrCgW,GAAO,IAAO,WAAWjN,CAAM,KAC9B+L,SACJA,EAAM,IAAO,WAAW/L,EAAO,MAAM,EAAGsL,EAAgB,IACxD2B,OAAc,WAAWjN,EAAO,MAAMsL,EAAgB,IAEnDG,EACHO,EAAe,KAAKiB,EAAO,OAEvB,IAAAC,IAAanB,CAAM,EAAE,OAAOC,GAAiBiB,EAAO,GACxDxG,EAAcyG,GAAW,QAAQ,OACjClB,EAAc,oBAaDhD,GAAM,CACpB3G,EAAA,GAAAgI,EAAY,EAAI,EAChBxD,EAAS,iBAAiB,EACrBqF,SAAcS,IACnBtK,EAAA,GAAA0J,EAAS,MAAS,EACdL,GACHI,EAAS,MAAMT,EAAgB,WAIxB8B,GAAK,CACbtG,EAAS,SAAU,IAAI,EACvBA,EAAS,OAAO,EAChBxE,EAAA,GAAA2B,EAAO,EAAE,EACT3B,EAAA,EAAA3N,EAAQ,IAAI,EAGJ,SAAA0Y,GAAc,OAAAC,GAAM,CAC5BhL,EAAA,EAAA3N,EAAQ2Y,CAAM,EACdxG,EAAS,SAAUwG,CAAM,EACzBxG,EAAS,SAAUwG,CAAM,WAGjBrC,GAAI,CACZ3I,EAAA,GAAAgI,EAAY,EAAK,EAEbqB,IACH7E,EAAS,gBAAgB,EACzBiF,EAAS,KAAI,EACTL,GACHpJ,EAAA,GAAA4J,EAAuC,EAAI,EAE5CxF,EAAc0F,EAAc,gBAAgB,EAC5CtF,EAAS,OAAO,EAChBxE,EAAA,GAAA2B,EAAO,EAAE,0JA4CK,MAAAsJ,GAAA,SAAAD,CAAM,IAAOxG,EAAS,QAASwG,CAAM,EAUnCE,GAAA,IAAAlL,EAAA,GAAA2B,EAAO,MAAM,g5BA7N5B6C,EAAS,OAAQ8E,CAAQ,2BA4HrBM,GAAwCR,IAAY,KAC1DpJ,EAAA,GAAA4J,EAAuC,EAAK,EACxCF,GAAUC,GAAc,CACvB,IAAAkB,EAA2B,CAAAnB,CAAM,EAAE,OAAOC,CAAc,OAC5DA,EAAc,IACdvF,EAAcyG,EAAW,QAAQ,qzBCCzB,QAAArS,EAAU,WAAQA,QAAkB,SAAW,SAAW,oBACtDA,EAAQ,IAAG,QAAU,eACzB,kBACO,sMAHP6I,EAAA,aAAA8J,EAAA,QAAA3S,EAAU,WAAQA,QAAkB,SAAW,SAAW,sCACtDA,EAAQ,IAAG,QAAU,4XArCzB,oBACIA,EAAQ,IAAG,QAAU,eACzB,kBACO,mOAFHA,EAAQ,IAAG,QAAU,oXAqFf,KAAAA,MAAO,sFAAP6I,EAAA,YAAA+J,EAAA,KAAA5S,MAAO,0IAtCb,WAAAA,MAAO,YACb,MAAAA,MAAO,IAAI,EACbA,EAAc,0RA+BZ,KAAAA,MAAO,2fADHA,EAAY,gJAhCV,WAAAA,MAAO,YACb6I,EAAA,iBAAA7I,MAAO,IAAI,aACbA,EAAc,6UA+BZ6I,EAAA,YAAAgK,EAAA,KAAA7S,MAAO,8XArED,WAAAA,MAAO,YACb,MAAAA,MAAO,IAAI,EACbA,EAAc,kGAIZ,KAAAA,MAAO,iZAND,WAAAA,MAAO,YACb6I,EAAA,iBAAA7I,MAAO,IAAI,aACbA,EAAc,8BAIZ6I,EAAA,YAAAiK,EAAA,KAAA9S,MAAO,mfApBVA,EAAW,qUA1HL,YAAA+S,EAAU,EAAE,EAAAhM,GACZ,aAAAiM,EAAY,IAAAjM,EACZ,SAAAkM,EAAU,EAAI,EAAAlM,GACd,YAAAkC,CAAoB,EAAAlC,EACpB,OAAAlN,EAAyB,IAAI,EAAAkN,GAC7B,QAAA4J,CAIgB,EAAA5J,GAChB,MAAA4E,CAAa,EAAA5E,GACb,KAAA2J,CAAY,EAAA3J,GACZ,WAAA6F,CAAmB,EAAA7F,EACnB,WAAAnI,EAAY,EAAI,EAAAmI,EAChB,OAAA9E,EAAuB,IAAI,EAAA8E,EAC3B,WAAAmM,EAAgC,MAAS,EAAAnM,GACzC,eAAAoM,CAA6B,EAAApM,EAC7B,UAAAqM,EAAW,EAAK,EAAArM,GAChB,qBAAA8F,CAA6B,EAAA9F,EAC7B,mBAAA+F,EAAoB,EAAK,EAAA/F,EACzB,UAAAwC,EAAW,EAAI,EAAAxC,GACf,iBAAAsC,EAAgB,IAAAtC,GAChB,QAAA6J,CAAgB,EAAA7J,GAChB,UAAA8J,CAAkB,EAAA9J,GAClB,OAAAsM,CAgBT,EAAAtM,EAEEuM,EAA6B,KAE7BvC,EAEAwC,EAAiC1Z,QAM/BuP,EAAkB,KACnBmK,IAAkB,MAAQ1Z,IAAU0Z,GAIxC/L,EAAA,EAAA3N,EAAQ0Z,CAAa,OAUlBzC,EAMAjF,EAEA2H,EAAe,iBAClB,SAAS,eAAe,EACvB,iBAAiB,gBAAgB,QAmB7BlK,EAAoB,CACzB,MAAOD,EAAiB,kBACxB,KAAM,GACN,OAAQ,aAGAoK,GAAsB,CAC9B,SAAS,gBAAgB,MAAM,YAC9B,sBACAnK,EAAqB,OAASkK,CAAY,EAI5CC,IAES,SAAAC,GAAe,OAAAlB,GAAM,CACtB,MAAAmB,EAAOC,CAAM,EAAIpB,EAAO,SAAS,mBAAmB,EACvD,WAAW,UAAU,EACrB,SAAS,OAAO,EACpBhL,EAAA,EAAA2L,EAAiBA,GAAc,QAC/BA,EAAe,OAASS,EAAiCT,CAAA,MACzDA,EAAe,QAAUX,EAAMW,CAAA,EAC/BE,EAAO,SAASM,EAA8BnB,CAAM,UAiCxCtY,GAAMmZ,EAAO,SAAS,QAASnZ,EAAE,MAAM,IACvCA,GAAMmZ,EAAO,SAAS,QAASnZ,EAAE,MAAM,QACnCmZ,EAAO,SAAS,MAAM,QACrBA,EAAO,SAAS,OAAO,QACxBA,EAAO,SAAS,MAAM,sCA0BvB,OAAAb,CAAM,IAAAhL,EAAA,EAAQ3N,EAAQ2Y,CAAM,OAC5B,OAAAA,KAAM,CACnBhL,EAAA,EAAA3N,EAAQ2Y,CAAM,EACda,EAAO,SAAS,SAAUxZ,CAAK,QAEpB,OAAA2Y,CAAM,IAAAhL,EAAA,GAAQsJ,EAAW0B,CAAM,SAS5Ba,EAAO,SAAS,MAAM,SACtBA,EAAO,SAAS,MAAM,SACrBA,EAAO,SAAS,OAAO,SACxBA,EAAO,SAAS,MAAM,SACXA,EAAO,SAAS,iBAAiB,SACjCA,EAAO,SAAS,iBAAiB,EACvCQ,GAAA3Z,GAAMmZ,EAAO,SAAS,gBAAgB,SACzCA,EAAO,SAAS,QAAQ,SACzBA,EAAO,SAAS,OAAO,60BA3JlCxZ,GAAS0Z,IAAkB,MACjC/L,EAAA,GAAA+L,EAAgB1Z,CAAK,2BAYjB,KAAK,UAAUA,CAAK,IAAM,KAAK,UAAUyZ,CAAS,IACrD9L,EAAA,GAAA8L,EAAYzZ,CAAK,EACjBwZ,EAAO,SAAS,QAAQ,0BAMzB,CAAOtC,GAAiBJ,QACxBI,EAAgBJ,EAAQ,CAAC,gCASvB9E,EAAiB,CACnB,OAAQ,GACR,UAAWxC,EAAiB,gBAAkB,UAC9C,cAAeA,EAAiB,yBAA2BmK,EAC3D,SAAU,EACV,OAAQ,EACR,YAAa,EACb,YAAa,UACH,SAAAJ,EACV,UAAW,GACX,WAAY,GACZ,UAAW,GACX,YAAa,GACb,cAAe/J,EAAiB,cAChC,WAAYA,EAAiB,aAAe", "names": ["insert", "target", "svg", "anchor", "append", "path", "title", "path0", "path1", "path2", "g", "defs", "clipPath", "rect", "__awaiter", "this", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "reject", "fulfilled", "step", "e", "rejected", "result", "decode", "audioData", "sampleRate", "audioCtx", "normalize", "channelData", "firstChannel", "n", "length", "max", "i", "absN", "channel", "createBuffer", "duration", "Decoder", "fetchBlob", "url", "progressCallback", "requestInit", "_a", "_b", "response", "reader", "contentLength", "<PERSON><PERSON><PERSON><PERSON>", "processChunk", "done", "percentage", "<PERSON><PERSON><PERSON>", "EventEmitter", "event", "listener", "options", "unsubscribeOnce", "eventName", "args", "Player", "callback", "src", "blob", "newSrc", "element", "time", "volume", "muted", "rate", "<PERSON><PERSON><PERSON>", "sinkId", "makeDraggable", "onDrag", "onStart", "onEnd", "threshold", "unsub", "down", "startX", "startY", "isDragging", "move", "x", "y", "left", "top", "click", "up", "<PERSON><PERSON><PERSON>", "audioElement", "parent", "div", "shadow", "container", "getClickPosition", "relativeX", "relativeY", "scrollLeft", "scrollWidth", "clientWidth", "endX", "delay", "_", "__", "newParent", "delayMs", "context", "color", "canvasElement", "gradient", "colorStopPercentage", "index", "offset", "ctx", "vScale", "topChannel", "bottomChannel", "width", "height", "halfHeight", "pixelRatio", "<PERSON><PERSON><PERSON><PERSON>", "barGap", "barRadius", "barIndexScale", "rectFn", "prevX", "maxTop", "maxBottom", "topBarHeight", "bottomBarHeight", "barHeight", "magnitudeTop", "magnitudeBottom", "_options", "drawChannel", "hScale", "h", "start", "end", "canvasContainer", "progressContainer", "canvas", "progressCanvas", "progressCtx", "len", "scale", "viewportWidth", "totalBarWidth", "viewportLen", "draw", "<PERSON><PERSON><PERSON><PERSON>", "tail<PERSON>ela<PERSON>", "renderHead", "fromIndex", "toIndex", "renderTail", "parentWidth", "useParentWidth", "channels", "oldCursorPosition", "newCursortPosition", "minPxPerSec", "progress", "isPlaying", "progressWidth", "center", "minScroll", "minDiff", "percents", "Timer", "WebAudioPlayer", "audioContext", "arrayBuffer", "audioBuffer", "deviceId", "defaultOptions", "WaveSurfer", "media", "currentTime", "debounce", "plugin", "unsubscribe", "p", "onProgress", "max<PERSON><PERSON><PERSON>", "precision", "maxChannels", "peaks", "data", "sampleSize", "sample", "isInteractive", "seconds", "audioBufferToWav", "numOfChan", "buffer", "view", "writeString", "string", "process_audio", "waveform_sample_rate", "numberOfChannels", "<PERSON><PERSON><PERSON><PERSON>", "startOffset", "trimmedAudioBuffer", "trimmedData", "skip_audio", "waveform", "amount", "get_skip_rewind_amount", "audio_duration", "skip_length", "t", "s", "o", "d", "r", "u", "c", "s$1", "a", "l", "currentVolume", "$$props", "onMount", "input", "show_volume_slider", "volumeElement", "adjustSlider", "slider", "$$value", "focusout_handler", "$$invalidate", "create_if_block_2", "button", "button0", "button1", "create_if_block_4", "if_block2", "create_if_block", "set_style", "attr", "button2", "button2_aria_label_value", "button4", "button4_aria_label_value", "div3", "div0", "span", "div1", "button3", "div2", "current", "dirty", "i18n", "playing", "show_redo", "interactive", "handle_trim_audio", "mode", "handle_reset_value", "waveform_options", "trim_region_settings", "editable", "trimDuration", "playbackSpeeds", "playbackSpeed", "trimRegion", "activeRegion", "leftRegionHandle", "rightRegionHandle", "activeHandle", "addTrimRegion", "trimAudio", "clearRegions", "region", "toggleTrimmingMode", "adjustRegionHandles", "handle", "key", "newStart", "newEnd", "click_handler", "click_handler_3", "RegionsPlugin", "shadowRoot", "createEventDispatcher", "if_block0", "create_if_block_3", "div4", "time0", "time1", "src_url_equal", "audio", "audio_src_value", "audio_autoplay_value", "t_value", "format_time", "set_data", "label", "dispatch_blob", "waveform_settings", "timeRef", "durationRef", "dispatch", "create_waveform", "resolve_wasm_src", "resolved_src", "decodedData", "trimmedBlob", "load_audio", "create_if_block_1", "downloadlink_changes", "Download", "iconbutton_changes", "Music", "show_label", "show_download_button", "show_share_button", "uploadToHuggingFace", "option", "option_value_value", "select", "micDevices", "tempDevices", "RecordPlugin", "devices", "device", "err", "t0_value", "t2_value", "t4_value", "t7_value", "if_block", "button1_class_value", "t0", "t2", "t4", "t7", "record", "recordButton", "pauseButton", "resumeButton", "stopButton", "stopButtonPaused", "record_time", "show_recording_waveform", "timing", "click_handler_4", "waveformrecordcontrols_changes", "if_block1", "micWaveform", "recordingWaveform", "recordingContainer", "microphoneContainer", "recordedAudio", "interval", "start_interval", "create_mic_waveform", "create_recording_waveform", "recording", "trimmedAudio", "waveformCanvas", "array_buffer", "audio_buffer", "microphone", "t1_value", "span1", "t1", "span2", "paused_recording", "stop", "waveformRecord", "getContext", "onDestroy", "blocklabel_changes", "STREAM_TIMESLICE", "NUM_HEADER_BYTES", "root", "sources", "pending", "streaming", "dragging", "active_source", "upload_fn", "recorder", "header", "pending_stream", "submit_pending_stream_on_pending_end", "inited", "audio_chunks", "module_promises", "get_modules", "blobs", "_audio_blob", "val", "prepare_files", "upload", "prepare_audio", "stream", "MediaRecorder", "register", "connect", "handle_chunk", "payload", "blobParts", "clear", "handle_load", "detail", "error_handler", "edit_handler_1", "block_changes", "uploadtext_changes", "interactiveaudio_changes", "staticaudio_changes", "elem_id", "elem_classes", "visible", "min_width", "loading_status", "autoplay", "gradio", "old_value", "initial_value", "color_accent", "set_trim_region_colour", "handle_error", "level", "status", "stop_recording_handler"], "sources": ["../../../../js/icons/src/Backward.svelte", "../../../../js/icons/src/Forward.svelte", "../../../../js/icons/src/VolumeLow.svelte", "../../../../js/icons/src/VolumeHigh.svelte", "../../../../js/icons/src/VolumeMuted.svelte", "../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/decoder.js", "../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/fetcher.js", "../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/event-emitter.js", "../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/player.js", "../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/draggable.js", "../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/renderer.js", "../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/timer.js", "../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/webaudio.js", "../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/wavesurfer.js", "../../../../js/audio/shared/audioBufferToWav.ts", "../../../../js/audio/shared/utils.ts", "../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/plugins/regions.js", "../../../../js/audio/shared/VolumeLevels.svelte", "../../../../js/audio/shared/VolumeControl.svelte", "../../../../js/audio/shared/WaveformControls.svelte", "../../../../js/audio/player/AudioPlayer.svelte", "../../../../js/audio/static/StaticAudio.svelte", "../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/plugins/record.js", "../../../../js/audio/shared/DeviceSelect.svelte", "../../../../js/audio/shared/WaveformRecordControls.svelte", "../../../../js/audio/recorder/AudioRecorder.svelte", "../../../../js/audio/streaming/StreamAudio.svelte", "../../../../js/audio/interactive/InteractiveAudio.svelte", "../../../../js/audio/Index.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"24px\"\n\theight=\"24px\"\n\tfill=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tviewBox=\"0 0 24 24\"\n\tcolor=\"currentColor\"\n\t><path\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t\td=\"M21.044 5.704a.6.6 0 0 1 .956.483v11.626a.6.6 0 0 1-.956.483l-7.889-5.813a.6.6 0 0 1 0-.966l7.89-5.813ZM10.044 5.704a.6.6 0 0 1 .956.483v11.626a.6.6 0 0 1-.956.483l-7.888-5.813a.6.6 0 0 1 0-.966l7.888-5.813Z\"\n\t/></svg\n>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"24px\"\n\theight=\"24px\"\n\tfill=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tviewBox=\"0 0 24 24\"\n\tcolor=\"currentColor\"\n\t><path\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t\td=\"M2.956 5.704A.6.6 0 0 0 2 6.187v11.626a.6.6 0 0 0 .956.483l7.889-5.813a.6.6 0 0 0 0-.966l-7.89-5.813ZM13.956 5.704a.6.6 0 0 0-.956.483v11.626a.6.6 0 0 0 .956.483l7.889-5.813a.6.6 0 0 0 0-.966l-7.89-5.813Z\"\n\t/></svg\n>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tstroke-width=\"1.5\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\tstroke=\"currentColor\"\n\tcolor=\"currentColor\"\n>\n\t<title>Low volume</title>\n\t<path\n\t\td=\"M19.5 7.5C19.5 7.5 21 9 21 11.5C21 14 19.5 15.5 19.5 15.5\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t/><path\n\t\td=\"M2 13.8571V10.1429C2 9.03829 2.89543 8.14286 4 8.14286H6.9C7.09569 8.14286 7.28708 8.08544 7.45046 7.97772L13.4495 4.02228C14.1144 3.5839 15 4.06075 15 4.85714V19.1429C15 19.9392 14.1144 20.4161 13.4495 19.9777L7.45046 16.0223C7.28708 15.9146 7.09569 15.8571 6.9 15.8571H4C2.89543 15.8571 2 14.9617 2 13.8571Z\"\n\t\tstroke-width=\"1.5\"\n\t/></svg\n>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tstroke-width=\"1.5\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\tcolor=\"currentColor\"\n>\n\t<title>High volume</title>\n\t<path\n\t\td=\"M1 13.8571V10.1429C1 9.03829 1.89543 8.14286 3 8.14286H5.9C6.09569 8.14286 6.28708 8.08544 6.45046 7.97772L12.4495 4.02228C13.1144 3.5839 14 4.06075 14 4.85714V19.1429C14 19.9392 13.1144 20.4161 12.4495 19.9777L6.45046 16.0223C6.28708 15.9146 6.09569 15.8571 5.9 15.8571H3C1.89543 15.8571 1 14.9617 1 13.8571Z\"\n\t\tstroke-width=\"1.5\"\n\t/><path\n\t\td=\"M17.5 7.5C17.5 7.5 19 9 19 11.5C19 14 17.5 15.5 17.5 15.5\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t/><path\n\t\td=\"M20.5 4.5C20.5 4.5 23 7 23 11.5C23 16 20.5 18.5 20.5 18.5\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t/></svg\n>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tstroke-width=\"1.5\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\tstroke=\"currentColor\"\n\tcolor=\"currentColor\"\n>\n\t<title>Muted volume</title>\n\t<g clip-path=\"url(#clip0_3173_16686)\"\n\t\t><path\n\t\t\td=\"M18 14L20.0005 12M22 10L20.0005 12M20.0005 12L18 10M20.0005 12L22 14\"\n\t\t\tstroke-width=\"1.5\"\n\t\t\tstroke-linecap=\"round\"\n\t\t\tstroke-linejoin=\"round\"\n\t\t/><path\n\t\t\td=\"M2 13.8571V10.1429C2 9.03829 2.89543 8.14286 4 8.14286H6.9C7.09569 8.14286 7.28708 8.08544 7.45046 7.97772L13.4495 4.02228C14.1144 3.5839 15 4.06075 15 4.85714V19.1429C15 19.9392 14.1144 20.4161 13.4495 19.9777L7.45046 16.0223C7.28708 15.9146 7.09569 15.8571 6.9 15.8571H4C2.89543 15.8571 2 14.9617 2 13.8571Z\"\n\t\t\tstroke-width=\"1.5\"\n\t\t/></g\n\t><defs\n\t\t><clipPath id=\"clip0_3173_16686\"\n\t\t\t><rect width=\"24\" height=\"24\" fill=\"white\" /></clipPath\n\t\t></defs\n\t></svg\n>\n", "var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n/** Decode an array buffer into an audio buffer */\nfunction decode(audioData, sampleRate) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const audioCtx = new AudioContext({ sampleRate });\n        const decode = audioCtx.decodeAudioData(audioData);\n        return decode.finally(() => audioCtx.close());\n    });\n}\n/** Normalize peaks to -1..1 */\nfunction normalize(channelData) {\n    const firstChannel = channelData[0];\n    if (firstChannel.some((n) => n > 1 || n < -1)) {\n        const length = firstChannel.length;\n        let max = 0;\n        for (let i = 0; i < length; i++) {\n            const absN = Math.abs(firstChannel[i]);\n            if (absN > max)\n                max = absN;\n        }\n        for (const channel of channelData) {\n            for (let i = 0; i < length; i++) {\n                channel[i] /= max;\n            }\n        }\n    }\n    return channelData;\n}\n/** Create an audio buffer from pre-decoded audio data */\nfunction createBuffer(channelData, duration) {\n    // If a single array of numbers is passed, make it an array of arrays\n    if (typeof channelData[0] === 'number')\n        channelData = [channelData];\n    // Normalize to -1..1\n    normalize(channelData);\n    return {\n        duration,\n        length: channelData[0].length,\n        sampleRate: channelData[0].length / duration,\n        numberOfChannels: channelData.length,\n        getChannelData: (i) => channelData === null || channelData === void 0 ? void 0 : channelData[i],\n        copyFromChannel: AudioBuffer.prototype.copyFromChannel,\n        copyToChannel: AudioBuffer.prototype.copyToChannel,\n    };\n}\nconst Decoder = {\n    decode,\n    createBuffer,\n};\nexport default Decoder;\n", "var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nfunction fetchBlob(url, progressCallback, requestInit) {\n    var _a, _b;\n    return __awaiter(this, void 0, void 0, function* () {\n        // Fetch the resource\n        const response = yield fetch(url, requestInit);\n        // Read the data to track progress\n        {\n            const reader = (_a = response.clone().body) === null || _a === void 0 ? void 0 : _a.getReader();\n            const contentLength = Number((_b = response.headers) === null || _b === void 0 ? void 0 : _b.get('Content-Length'));\n            let receivedLength = 0;\n            // Process the data\n            const processChunk = (done, value) => __awaiter(this, void 0, void 0, function* () {\n                if (done)\n                    return;\n                // Add to the received length\n                receivedLength += (value === null || value === void 0 ? void 0 : value.length) || 0;\n                const percentage = Math.round((receivedLength / contentLength) * 100);\n                progressCallback(percentage);\n                // Continue reading data\n                return reader === null || reader === void 0 ? void 0 : reader.read().then(({ done, value }) => processChunk(done, value));\n            });\n            reader === null || reader === void 0 ? void 0 : reader.read().then(({ done, value }) => processChunk(done, value));\n        }\n        return response.blob();\n    });\n}\nconst Fetcher = {\n    fetchBlob,\n};\nexport default Fetcher;\n", "/** A simple event emitter that can be used to listen to and emit events. */\nclass EventEmitter {\n    constructor() {\n        this.listeners = {};\n        /** Subscribe to an event. Returns an unsubscribe function. */\n        this.on = this.addEventListener;\n        /** Unsubscribe from an event */\n        this.un = this.removeEventListener;\n    }\n    /** Add an event listener */\n    addEventListener(event, listener, options) {\n        if (!this.listeners[event]) {\n            this.listeners[event] = new Set();\n        }\n        this.listeners[event].add(listener);\n        if (options === null || options === void 0 ? void 0 : options.once) {\n            const unsubscribeOnce = () => {\n                this.removeEventListener(event, unsubscribeOnce);\n                this.removeEventListener(event, listener);\n            };\n            this.addEventListener(event, unsubscribeOnce);\n            return unsubscribeOnce;\n        }\n        return () => this.removeEventListener(event, listener);\n    }\n    removeEventListener(event, listener) {\n        var _a;\n        (_a = this.listeners[event]) === null || _a === void 0 ? void 0 : _a.delete(listener);\n    }\n    /** Subscribe to an event only once */\n    once(event, listener) {\n        return this.on(event, listener, { once: true });\n    }\n    /** Clear all events */\n    unAll() {\n        this.listeners = {};\n    }\n    /** Emit an event */\n    emit(eventName, ...args) {\n        if (this.listeners[eventName]) {\n            this.listeners[eventName].forEach((listener) => listener(...args));\n        }\n    }\n}\nexport default EventEmitter;\n", "import EventEmitter from './event-emitter.js';\nclass Player extends EventEmitter {\n    constructor(options) {\n        super();\n        this.isExternalMedia = false;\n        if (options.media) {\n            this.media = options.media;\n            this.isExternalMedia = true;\n        }\n        else {\n            this.media = document.createElement('audio');\n        }\n        // Controls\n        if (options.mediaControls) {\n            this.media.controls = true;\n        }\n        // Autoplay\n        if (options.autoplay) {\n            this.media.autoplay = true;\n        }\n        // Speed\n        if (options.playbackRate != null) {\n            this.onceMediaEvent('canplay', () => {\n                if (options.playbackRate != null) {\n                    this.media.playbackRate = options.playbackRate;\n                }\n            });\n        }\n    }\n    onMediaEvent(event, callback, options) {\n        this.media.addEventListener(event, callback, options);\n        return () => this.media.removeEventListener(event, callback);\n    }\n    onceMediaEvent(event, callback) {\n        return this.onMediaEvent(event, callback, { once: true });\n    }\n    getSrc() {\n        return this.media.currentSrc || this.media.src || '';\n    }\n    revokeSrc() {\n        const src = this.getSrc();\n        if (src.startsWith('blob:')) {\n            URL.revokeObjectURL(src);\n        }\n    }\n    setSrc(url, blob) {\n        const src = this.getSrc();\n        if (src === url)\n            return;\n        this.revokeSrc();\n        const newSrc = blob instanceof Blob ? URL.createObjectURL(blob) : url;\n        this.media.src = newSrc;\n        this.media.load();\n    }\n    destroy() {\n        this.media.pause();\n        if (this.isExternalMedia)\n            return;\n        this.media.remove();\n        this.revokeSrc();\n        this.media.src = '';\n        // Load resets the media element to its initial state\n        this.media.load();\n    }\n    setMediaElement(element) {\n        this.media = element;\n    }\n    /** Start playing the audio */\n    play() {\n        return this.media.play();\n    }\n    /** Pause the audio */\n    pause() {\n        this.media.pause();\n    }\n    /** Check if the audio is playing */\n    isPlaying() {\n        return !this.media.paused && !this.media.ended;\n    }\n    /** Jumpt to a specific time in the audio (in seconds) */\n    setTime(time) {\n        this.media.currentTime = time;\n    }\n    /** Get the duration of the audio in seconds */\n    getDuration() {\n        return this.media.duration;\n    }\n    /** Get the current audio position in seconds */\n    getCurrentTime() {\n        return this.media.currentTime;\n    }\n    /** Get the audio volume */\n    getVolume() {\n        return this.media.volume;\n    }\n    /** Set the audio volume */\n    setVolume(volume) {\n        this.media.volume = volume;\n    }\n    /** Get the audio muted state */\n    getMuted() {\n        return this.media.muted;\n    }\n    /** Mute or unmute the audio */\n    setMuted(muted) {\n        this.media.muted = muted;\n    }\n    /** Get the playback speed */\n    getPlaybackRate() {\n        return this.media.playbackRate;\n    }\n    /** Set the playback speed, pass an optional false to NOT preserve the pitch */\n    setPlaybackRate(rate, preservePitch) {\n        // preservePitch is true by default in most browsers\n        if (preservePitch != null) {\n            this.media.preservesPitch = preservePitch;\n        }\n        this.media.playbackRate = rate;\n    }\n    /** Get the HTML media element */\n    getMediaElement() {\n        return this.media;\n    }\n    /** Set a sink id to change the audio output device */\n    setSinkId(sinkId) {\n        // See https://developer.mozilla.org/en-US/docs/Web/API/HTMLMediaElement/setSinkId\n        const media = this.media;\n        return media.setSinkId(sinkId);\n    }\n}\nexport default Player;\n", "export function makeDraggable(element, onDrag, onStart, onEnd, threshold = 5) {\n    let unsub = () => {\n        return;\n    };\n    if (!element)\n        return unsub;\n    const down = (e) => {\n        // Ignore the right mouse button\n        if (e.button === 2)\n            return;\n        e.preventDefault();\n        e.stopPropagation();\n        element.style.touchAction = 'none';\n        let startX = e.clientX;\n        let startY = e.clientY;\n        let isDragging = false;\n        const move = (e) => {\n            e.preventDefault();\n            e.stopPropagation();\n            const x = e.clientX;\n            const y = e.clientY;\n            if (isDragging || Math.abs(x - startX) >= threshold || Math.abs(y - startY) >= threshold) {\n                const { left, top } = element.getBoundingClientRect();\n                if (!isDragging) {\n                    isDragging = true;\n                    onStart === null || onStart === void 0 ? void 0 : onStart(startX - left, startY - top);\n                }\n                onDrag(x - startX, y - startY, x - left, y - top);\n                startX = x;\n                startY = y;\n            }\n        };\n        const click = (e) => {\n            if (isDragging) {\n                e.preventDefault();\n                e.stopPropagation();\n            }\n        };\n        const up = () => {\n            element.style.touchAction = '';\n            if (isDragging) {\n                onEnd === null || onEnd === void 0 ? void 0 : onEnd();\n            }\n            unsub();\n        };\n        document.addEventListener('pointermove', move);\n        document.addEventListener('pointerup', up);\n        document.addEventListener('pointerleave', up);\n        document.addEventListener('click', click, true);\n        unsub = () => {\n            document.removeEventListener('pointermove', move);\n            document.removeEventListener('pointerup', up);\n            document.removeEventListener('pointerleave', up);\n            setTimeout(() => {\n                document.removeEventListener('click', click, true);\n            }, 10);\n        };\n    };\n    element.addEventListener('pointerdown', down);\n    return () => {\n        unsub();\n        element.removeEventListener('pointerdown', down);\n    };\n}\n", "import { makeDraggable } from './draggable.js';\nimport EventEmitter from './event-emitter.js';\nclass Renderer extends EventEmitter {\n    constructor(options, audioElement) {\n        super();\n        this.timeouts = [];\n        this.isScrolling = false;\n        this.audioData = null;\n        this.resizeObserver = null;\n        this.isDragging = false;\n        this.options = options;\n        const parent = this.parentFromOptionsContainer(options.container);\n        this.parent = parent;\n        const [div, shadow] = this.initHtml();\n        parent.appendChild(div);\n        this.container = div;\n        this.scrollContainer = shadow.querySelector('.scroll');\n        this.wrapper = shadow.querySelector('.wrapper');\n        this.canvasWrapper = shadow.querySelector('.canvases');\n        this.progressWrapper = shadow.querySelector('.progress');\n        this.cursor = shadow.querySelector('.cursor');\n        if (audioElement) {\n            shadow.appendChild(audioElement);\n        }\n        this.initEvents();\n    }\n    parentFromOptionsContainer(container) {\n        let parent;\n        if (typeof container === 'string') {\n            parent = document.querySelector(container);\n        }\n        else if (container instanceof HTMLElement) {\n            parent = container;\n        }\n        if (!parent) {\n            throw new Error('Container not found');\n        }\n        return parent;\n    }\n    initEvents() {\n        const getClickPosition = (e) => {\n            const rect = this.wrapper.getBoundingClientRect();\n            const x = e.clientX - rect.left;\n            const y = e.clientX - rect.left;\n            const relativeX = x / rect.width;\n            const relativeY = y / rect.height;\n            return [relativeX, relativeY];\n        };\n        // Add a click listener\n        this.wrapper.addEventListener('click', (e) => {\n            const [x, y] = getClickPosition(e);\n            this.emit('click', x, y);\n        });\n        // Add a double click listener\n        this.wrapper.addEventListener('dblclick', (e) => {\n            const [x, y] = getClickPosition(e);\n            this.emit('dblclick', x, y);\n        });\n        // Drag\n        if (this.options.dragToSeek) {\n            this.initDrag();\n        }\n        // Add a scroll listener\n        this.scrollContainer.addEventListener('scroll', () => {\n            const { scrollLeft, scrollWidth, clientWidth } = this.scrollContainer;\n            const startX = scrollLeft / scrollWidth;\n            const endX = (scrollLeft + clientWidth) / scrollWidth;\n            this.emit('scroll', startX, endX);\n        });\n        // Re-render the waveform on container resize\n        const delay = this.createDelay(100);\n        this.resizeObserver = new ResizeObserver(() => {\n            delay(() => this.reRender());\n        });\n        this.resizeObserver.observe(this.scrollContainer);\n    }\n    initDrag() {\n        makeDraggable(this.wrapper, \n        // On drag\n        (_, __, x) => {\n            this.emit('drag', Math.max(0, Math.min(1, x / this.wrapper.getBoundingClientRect().width)));\n        }, \n        // On start drag\n        () => (this.isDragging = true), \n        // On end drag\n        () => (this.isDragging = false));\n    }\n    getHeight() {\n        const defaultHeight = 128;\n        if (this.options.height == null)\n            return defaultHeight;\n        if (!isNaN(Number(this.options.height)))\n            return Number(this.options.height);\n        if (this.options.height === 'auto')\n            return this.parent.clientHeight || defaultHeight;\n        return defaultHeight;\n    }\n    initHtml() {\n        const div = document.createElement('div');\n        const shadow = div.attachShadow({ mode: 'open' });\n        shadow.innerHTML = `\n      <style>\n        :host {\n          user-select: none;\n          min-width: 1px;\n        }\n        :host audio {\n          display: block;\n          width: 100%;\n        }\n        :host .scroll {\n          overflow-x: auto;\n          overflow-y: hidden;\n          width: 100%;\n          position: relative;\n        }\n        :host .noScrollbar {\n          scrollbar-color: transparent;\n          scrollbar-width: none;\n        }\n        :host .noScrollbar::-webkit-scrollbar {\n          display: none;\n          -webkit-appearance: none;\n        }\n        :host .wrapper {\n          position: relative;\n          overflow: visible;\n          z-index: 2;\n        }\n        :host .canvases {\n          min-height: ${this.getHeight()}px;\n        }\n        :host .canvases > div {\n          position: relative;\n        }\n        :host canvas {\n          display: block;\n          position: absolute;\n          top: 0;\n          image-rendering: pixelated;\n        }\n        :host .progress {\n          pointer-events: none;\n          position: absolute;\n          z-index: 2;\n          top: 0;\n          left: 0;\n          width: 0;\n          height: 100%;\n          overflow: hidden;\n        }\n        :host .progress > div {\n          position: relative;\n        }\n        :host .cursor {\n          pointer-events: none;\n          position: absolute;\n          z-index: 5;\n          top: 0;\n          left: 0;\n          height: 100%;\n          border-radius: 2px;\n        }\n      </style>\n\n      <div class=\"scroll\" part=\"scroll\">\n        <div class=\"wrapper\" part=\"wrapper\">\n          <div class=\"canvases\"></div>\n          <div class=\"progress\" part=\"progress\"></div>\n          <div class=\"cursor\" part=\"cursor\"></div>\n        </div>\n      </div>\n    `;\n        return [div, shadow];\n    }\n    /** Wavesurfer itself calls this method. Do not call it manually. */\n    setOptions(options) {\n        if (this.options.container !== options.container) {\n            const newParent = this.parentFromOptionsContainer(options.container);\n            newParent.appendChild(this.container);\n            this.parent = newParent;\n        }\n        if (options.dragToSeek && !this.options.dragToSeek) {\n            this.initDrag();\n        }\n        this.options = options;\n        // Re-render the waveform\n        this.reRender();\n    }\n    getWrapper() {\n        return this.wrapper;\n    }\n    getScroll() {\n        return this.scrollContainer.scrollLeft;\n    }\n    destroy() {\n        var _a;\n        this.container.remove();\n        (_a = this.resizeObserver) === null || _a === void 0 ? void 0 : _a.disconnect();\n    }\n    createDelay(delayMs = 10) {\n        const context = {};\n        this.timeouts.push(context);\n        return (callback) => {\n            context.timeout && clearTimeout(context.timeout);\n            context.timeout = setTimeout(callback, delayMs);\n        };\n    }\n    // Convert array of color values to linear gradient\n    convertColorValues(color) {\n        if (!Array.isArray(color))\n            return color || '';\n        if (color.length < 2)\n            return color[0] || '';\n        const canvasElement = document.createElement('canvas');\n        const ctx = canvasElement.getContext('2d');\n        const gradient = ctx.createLinearGradient(0, 0, 0, canvasElement.height);\n        const colorStopPercentage = 1 / (color.length - 1);\n        color.forEach((color, index) => {\n            const offset = index * colorStopPercentage;\n            gradient.addColorStop(offset, color);\n        });\n        return gradient;\n    }\n    renderBarWaveform(channelData, options, ctx, vScale) {\n        const topChannel = channelData[0];\n        const bottomChannel = channelData[1] || channelData[0];\n        const length = topChannel.length;\n        const { width, height } = ctx.canvas;\n        const halfHeight = height / 2;\n        const pixelRatio = window.devicePixelRatio || 1;\n        const barWidth = options.barWidth ? options.barWidth * pixelRatio : 1;\n        const barGap = options.barGap ? options.barGap * pixelRatio : options.barWidth ? barWidth / 2 : 0;\n        const barRadius = options.barRadius || 0;\n        const barIndexScale = width / (barWidth + barGap) / length;\n        const rectFn = barRadius && 'roundRect' in ctx ? 'roundRect' : 'rect';\n        ctx.beginPath();\n        let prevX = 0;\n        let maxTop = 0;\n        let maxBottom = 0;\n        for (let i = 0; i <= length; i++) {\n            const x = Math.round(i * barIndexScale);\n            if (x > prevX) {\n                const topBarHeight = Math.round(maxTop * halfHeight * vScale);\n                const bottomBarHeight = Math.round(maxBottom * halfHeight * vScale);\n                const barHeight = topBarHeight + bottomBarHeight || 1;\n                // Vertical alignment\n                let y = halfHeight - topBarHeight;\n                if (options.barAlign === 'top') {\n                    y = 0;\n                }\n                else if (options.barAlign === 'bottom') {\n                    y = height - barHeight;\n                }\n                ctx[rectFn](prevX * (barWidth + barGap), y, barWidth, barHeight, barRadius);\n                prevX = x;\n                maxTop = 0;\n                maxBottom = 0;\n            }\n            const magnitudeTop = Math.abs(topChannel[i] || 0);\n            const magnitudeBottom = Math.abs(bottomChannel[i] || 0);\n            if (magnitudeTop > maxTop)\n                maxTop = magnitudeTop;\n            if (magnitudeBottom > maxBottom)\n                maxBottom = magnitudeBottom;\n        }\n        ctx.fill();\n        ctx.closePath();\n    }\n    renderLineWaveform(channelData, _options, ctx, vScale) {\n        const drawChannel = (index) => {\n            const channel = channelData[index] || channelData[0];\n            const length = channel.length;\n            const { height } = ctx.canvas;\n            const halfHeight = height / 2;\n            const hScale = ctx.canvas.width / length;\n            ctx.moveTo(0, halfHeight);\n            let prevX = 0;\n            let max = 0;\n            for (let i = 0; i <= length; i++) {\n                const x = Math.round(i * hScale);\n                if (x > prevX) {\n                    const h = Math.round(max * halfHeight * vScale) || 1;\n                    const y = halfHeight + h * (index === 0 ? -1 : 1);\n                    ctx.lineTo(prevX, y);\n                    prevX = x;\n                    max = 0;\n                }\n                const value = Math.abs(channel[i] || 0);\n                if (value > max)\n                    max = value;\n            }\n            ctx.lineTo(prevX, halfHeight);\n        };\n        ctx.beginPath();\n        drawChannel(0);\n        drawChannel(1);\n        ctx.fill();\n        ctx.closePath();\n    }\n    renderWaveform(channelData, options, ctx) {\n        ctx.fillStyle = this.convertColorValues(options.waveColor);\n        // Custom rendering function\n        if (options.renderFunction) {\n            options.renderFunction(channelData, ctx);\n            return;\n        }\n        // Vertical scaling\n        let vScale = options.barHeight || 1;\n        if (options.normalize) {\n            const max = Array.from(channelData[0]).reduce((max, value) => Math.max(max, Math.abs(value)), 0);\n            vScale = max ? 1 / max : 1;\n        }\n        // Render waveform as bars\n        if (options.barWidth || options.barGap || options.barAlign) {\n            this.renderBarWaveform(channelData, options, ctx, vScale);\n            return;\n        }\n        // Render waveform as a polyline\n        this.renderLineWaveform(channelData, options, ctx, vScale);\n    }\n    renderSingleCanvas(channelData, options, width, height, start, end, canvasContainer, progressContainer) {\n        const pixelRatio = window.devicePixelRatio || 1;\n        const canvas = document.createElement('canvas');\n        const length = channelData[0].length;\n        canvas.width = Math.round((width * (end - start)) / length);\n        canvas.height = height * pixelRatio;\n        canvas.style.width = `${Math.floor(canvas.width / pixelRatio)}px`;\n        canvas.style.height = `${height}px`;\n        canvas.style.left = `${Math.floor((start * width) / pixelRatio / length)}px`;\n        canvasContainer.appendChild(canvas);\n        const ctx = canvas.getContext('2d');\n        this.renderWaveform(channelData.map((channel) => channel.slice(start, end)), options, ctx);\n        // Draw a progress canvas\n        if (canvas.width > 0 && canvas.height > 0) {\n            const progressCanvas = canvas.cloneNode();\n            const progressCtx = progressCanvas.getContext('2d');\n            progressCtx.drawImage(canvas, 0, 0);\n            // Set the composition method to draw only where the waveform is drawn\n            progressCtx.globalCompositeOperation = 'source-in';\n            progressCtx.fillStyle = this.convertColorValues(options.progressColor);\n            // This rectangle acts as a mask thanks to the composition method\n            progressCtx.fillRect(0, 0, canvas.width, canvas.height);\n            progressContainer.appendChild(progressCanvas);\n        }\n    }\n    renderChannel(channelData, options, width) {\n        // A container for canvases\n        const canvasContainer = document.createElement('div');\n        const height = this.getHeight();\n        canvasContainer.style.height = `${height}px`;\n        this.canvasWrapper.style.minHeight = `${height}px`;\n        this.canvasWrapper.appendChild(canvasContainer);\n        // A container for progress canvases\n        const progressContainer = canvasContainer.cloneNode();\n        this.progressWrapper.appendChild(progressContainer);\n        // Determine the currently visible part of the waveform\n        const { scrollLeft, scrollWidth, clientWidth } = this.scrollContainer;\n        const len = channelData[0].length;\n        const scale = len / scrollWidth;\n        let viewportWidth = Math.min(Renderer.MAX_CANVAS_WIDTH, clientWidth);\n        // Adjust width to avoid gaps between canvases when using bars\n        if (options.barWidth || options.barGap) {\n            const barWidth = options.barWidth || 0.5;\n            const barGap = options.barGap || barWidth / 2;\n            const totalBarWidth = barWidth + barGap;\n            if (viewportWidth % totalBarWidth !== 0) {\n                viewportWidth = Math.floor(viewportWidth / totalBarWidth) * totalBarWidth;\n            }\n        }\n        const start = Math.floor(Math.abs(scrollLeft) * scale);\n        const end = Math.floor(start + viewportWidth * scale);\n        const viewportLen = end - start;\n        // Draw a portion of the waveform from start peak to end peak\n        const draw = (start, end) => {\n            this.renderSingleCanvas(channelData, options, width, height, Math.max(0, start), Math.min(end, len), canvasContainer, progressContainer);\n        };\n        // Draw the waveform in viewport chunks, each with a delay\n        const headDelay = this.createDelay();\n        const tailDelay = this.createDelay();\n        const renderHead = (fromIndex, toIndex) => {\n            draw(fromIndex, toIndex);\n            if (fromIndex > 0) {\n                headDelay(() => {\n                    renderHead(fromIndex - viewportLen, toIndex - viewportLen);\n                });\n            }\n        };\n        const renderTail = (fromIndex, toIndex) => {\n            draw(fromIndex, toIndex);\n            if (toIndex < len) {\n                tailDelay(() => {\n                    renderTail(fromIndex + viewportLen, toIndex + viewportLen);\n                });\n            }\n        };\n        renderHead(start, end);\n        if (end < len) {\n            renderTail(end, end + viewportLen);\n        }\n    }\n    render(audioData) {\n        // Clear previous timeouts\n        this.timeouts.forEach((context) => context.timeout && clearTimeout(context.timeout));\n        this.timeouts = [];\n        // Clear the canvases\n        this.canvasWrapper.innerHTML = '';\n        this.progressWrapper.innerHTML = '';\n        this.wrapper.style.width = '';\n        // Width\n        if (this.options.width != null) {\n            this.scrollContainer.style.width =\n                typeof this.options.width === 'number' ? `${this.options.width}px` : this.options.width;\n        }\n        // Determine the width of the waveform\n        const pixelRatio = window.devicePixelRatio || 1;\n        const parentWidth = this.scrollContainer.clientWidth;\n        const scrollWidth = Math.ceil(audioData.duration * (this.options.minPxPerSec || 0));\n        // Whether the container should scroll\n        this.isScrolling = scrollWidth > parentWidth;\n        const useParentWidth = this.options.fillParent && !this.isScrolling;\n        // Width of the waveform in pixels\n        const width = (useParentWidth ? parentWidth : scrollWidth) * pixelRatio;\n        // Set the width of the wrapper\n        this.wrapper.style.width = useParentWidth ? '100%' : `${scrollWidth}px`;\n        // Set additional styles\n        this.scrollContainer.style.overflowX = this.isScrolling ? 'auto' : 'hidden';\n        this.scrollContainer.classList.toggle('noScrollbar', !!this.options.hideScrollbar);\n        this.cursor.style.backgroundColor = `${this.options.cursorColor || this.options.progressColor}`;\n        this.cursor.style.width = `${this.options.cursorWidth}px`;\n        // Render the waveform\n        if (this.options.splitChannels) {\n            // Render a waveform for each channel\n            for (let i = 0; i < audioData.numberOfChannels; i++) {\n                const options = Object.assign(Object.assign({}, this.options), this.options.splitChannels[i]);\n                this.renderChannel([audioData.getChannelData(i)], options, width);\n            }\n        }\n        else {\n            // Render a single waveform for the first two channels (left and right)\n            const channels = [audioData.getChannelData(0)];\n            if (audioData.numberOfChannels > 1)\n                channels.push(audioData.getChannelData(1));\n            this.renderChannel(channels, this.options, width);\n        }\n        this.audioData = audioData;\n        this.emit('render');\n    }\n    reRender() {\n        // Return if the waveform has not been rendered yet\n        if (!this.audioData)\n            return;\n        // Remember the current cursor position\n        const oldCursorPosition = this.progressWrapper.clientWidth;\n        // Set the new zoom level and re-render the waveform\n        this.render(this.audioData);\n        // Adjust the scroll position so that the cursor stays in the same place\n        const newCursortPosition = this.progressWrapper.clientWidth;\n        this.scrollContainer.scrollLeft += newCursortPosition - oldCursorPosition;\n    }\n    zoom(minPxPerSec) {\n        this.options.minPxPerSec = minPxPerSec;\n        this.reRender();\n    }\n    scrollIntoView(progress, isPlaying = false) {\n        const { clientWidth, scrollLeft, scrollWidth } = this.scrollContainer;\n        const progressWidth = scrollWidth * progress;\n        const center = clientWidth / 2;\n        const minScroll = isPlaying && this.options.autoCenter && !this.isDragging ? center : clientWidth;\n        if (progressWidth > scrollLeft + minScroll || progressWidth < scrollLeft) {\n            // Scroll to the center\n            if (this.options.autoCenter && !this.isDragging) {\n                // If the cursor is in viewport but not centered, scroll to the center slowly\n                const minDiff = center / 20;\n                if (progressWidth - (scrollLeft + center) >= minDiff && progressWidth < scrollLeft + clientWidth) {\n                    this.scrollContainer.scrollLeft += minDiff;\n                }\n                else {\n                    // Otherwise, scroll to the center immediately\n                    this.scrollContainer.scrollLeft = progressWidth - center;\n                }\n            }\n            else if (this.isDragging) {\n                // Scroll just a little bit to allow for some space between the cursor and the edge\n                const gap = 10;\n                this.scrollContainer.scrollLeft =\n                    progressWidth < scrollLeft ? progressWidth - gap : progressWidth - clientWidth + gap;\n            }\n            else {\n                // Scroll to the beginning\n                this.scrollContainer.scrollLeft = progressWidth;\n            }\n        }\n        // Emit the scroll event\n        {\n            const { scrollLeft } = this.scrollContainer;\n            const startX = scrollLeft / scrollWidth;\n            const endX = (scrollLeft + clientWidth) / scrollWidth;\n            this.emit('scroll', startX, endX);\n        }\n    }\n    renderProgress(progress, isPlaying) {\n        if (isNaN(progress))\n            return;\n        const percents = progress * 100;\n        this.canvasWrapper.style.clipPath = `polygon(${percents}% 0, 100% 0, 100% 100%, ${percents}% 100%)`;\n        this.progressWrapper.style.width = `${percents}%`;\n        this.cursor.style.left = `${percents}%`;\n        this.cursor.style.marginLeft = Math.round(percents) === 100 ? `-${this.options.cursorWidth}px` : '';\n        if (this.isScrolling && this.options.autoScroll) {\n            this.scrollIntoView(progress, isPlaying);\n        }\n    }\n}\nRenderer.MAX_CANVAS_WIDTH = 4000;\nexport default Renderer;\n", "import EventEmitter from './event-emitter.js';\nclass Timer extends EventEmitter {\n    constructor() {\n        super(...arguments);\n        this.unsubscribe = () => undefined;\n    }\n    start() {\n        this.unsubscribe = this.on('tick', () => {\n            requestAnimationFrame(() => {\n                this.emit('tick');\n            });\n        });\n        this.emit('tick');\n    }\n    stop() {\n        this.unsubscribe();\n    }\n    destroy() {\n        this.unsubscribe();\n    }\n}\nexport default Timer;\n", "var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nimport EventEmitter from './event-emitter.js';\n/**\n * A Web Audio buffer player emulating the behavior of an HTML5 Audio element.\n */\nclass WebAudioPlayer extends EventEmitter {\n    constructor(audioContext = new AudioContext()) {\n        super();\n        this.bufferNode = null;\n        this.autoplay = false;\n        this.playStartTime = 0;\n        this.playedDuration = 0;\n        this._muted = false;\n        this.buffer = null;\n        this.currentSrc = '';\n        this.paused = true;\n        this.crossOrigin = null;\n        this.audioContext = audioContext;\n        this.gainNode = this.audioContext.createGain();\n        this.gainNode.connect(this.audioContext.destination);\n    }\n    load() {\n        return __awaiter(this, void 0, void 0, function* () {\n            return;\n        });\n    }\n    get src() {\n        return this.currentSrc;\n    }\n    set src(value) {\n        this.currentSrc = value;\n        fetch(value)\n            .then((response) => response.arrayBuffer())\n            .then((arrayBuffer) => this.audioContext.decodeAudioData(arrayBuffer))\n            .then((audioBuffer) => {\n            this.buffer = audioBuffer;\n            this.emit('loadedmetadata');\n            this.emit('canplay');\n            if (this.autoplay)\n                this.play();\n        });\n    }\n    _play() {\n        var _a;\n        if (!this.paused)\n            return;\n        this.paused = false;\n        (_a = this.bufferNode) === null || _a === void 0 ? void 0 : _a.disconnect();\n        this.bufferNode = this.audioContext.createBufferSource();\n        this.bufferNode.buffer = this.buffer;\n        this.bufferNode.connect(this.gainNode);\n        if (this.playedDuration >= this.duration) {\n            this.playedDuration = 0;\n        }\n        this.bufferNode.start(this.audioContext.currentTime, this.playedDuration);\n        this.playStartTime = this.audioContext.currentTime;\n        this.bufferNode.onended = () => {\n            if (this.currentTime >= this.duration) {\n                this.pause();\n                this.emit('ended');\n            }\n        };\n    }\n    _pause() {\n        var _a;\n        if (this.paused)\n            return;\n        this.paused = true;\n        (_a = this.bufferNode) === null || _a === void 0 ? void 0 : _a.stop();\n        this.playedDuration += this.audioContext.currentTime - this.playStartTime;\n    }\n    play() {\n        return __awaiter(this, void 0, void 0, function* () {\n            this._play();\n            this.emit('play');\n        });\n    }\n    pause() {\n        this._pause();\n        this.emit('pause');\n    }\n    setSinkId(deviceId) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const ac = this.audioContext;\n            return ac.setSinkId(deviceId);\n        });\n    }\n    get playbackRate() {\n        var _a, _b;\n        return (_b = (_a = this.bufferNode) === null || _a === void 0 ? void 0 : _a.playbackRate.value) !== null && _b !== void 0 ? _b : 1;\n    }\n    set playbackRate(value) {\n        if (this.bufferNode) {\n            this.bufferNode.playbackRate.value = value;\n        }\n    }\n    get currentTime() {\n        return this.paused ? this.playedDuration : this.playedDuration + this.audioContext.currentTime - this.playStartTime;\n    }\n    set currentTime(value) {\n        this.emit('seeking');\n        if (this.paused) {\n            this.playedDuration = value;\n        }\n        else {\n            this._pause();\n            this.playedDuration = value;\n            this._play();\n        }\n        this.emit('timeupdate');\n    }\n    get duration() {\n        var _a;\n        return ((_a = this.buffer) === null || _a === void 0 ? void 0 : _a.duration) || 0;\n    }\n    get volume() {\n        return this.gainNode.gain.value;\n    }\n    set volume(value) {\n        this.gainNode.gain.value = value;\n        this.emit('volumechange');\n    }\n    get muted() {\n        return this._muted;\n    }\n    set muted(value) {\n        if (this._muted === value)\n            return;\n        this._muted = value;\n        if (this._muted) {\n            this.gainNode.disconnect();\n        }\n        else {\n            this.gainNode.connect(this.audioContext.destination);\n        }\n    }\n    /** Get the GainNode used to play the audio. Can be used to attach filters. */\n    getGainNode() {\n        return this.gainNode;\n    }\n}\nexport default WebAudioPlayer;\n", "var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nimport Decoder from './decoder.js';\nimport Fetcher from './fetcher.js';\nimport Player from './player.js';\nimport Renderer from './renderer.js';\nimport Timer from './timer.js';\nimport WebAudioPlayer from './webaudio.js';\nconst defaultOptions = {\n    waveColor: '#999',\n    progressColor: '#555',\n    cursorWidth: 1,\n    minPxPerSec: 0,\n    fillParent: true,\n    interact: true,\n    dragToSeek: false,\n    autoScroll: true,\n    autoCenter: true,\n    sampleRate: 8000,\n};\nclass WaveSurfer extends Player {\n    /** Create a new WaveSurfer instance */\n    static create(options) {\n        return new WaveSurfer(options);\n    }\n    /** Create a new WaveSurfer instance */\n    constructor(options) {\n        const media = options.media ||\n            (options.backend === 'WebAudio' ? new WebAudioPlayer() : undefined);\n        super({\n            media,\n            mediaControls: options.mediaControls,\n            autoplay: options.autoplay,\n            playbackRate: options.audioRate,\n        });\n        this.plugins = [];\n        this.decodedData = null;\n        this.subscriptions = [];\n        this.mediaSubscriptions = [];\n        this.options = Object.assign({}, defaultOptions, options);\n        this.timer = new Timer();\n        const audioElement = media ? undefined : this.getMediaElement();\n        this.renderer = new Renderer(this.options, audioElement);\n        this.initPlayerEvents();\n        this.initRendererEvents();\n        this.initTimerEvents();\n        this.initPlugins();\n        // Load audio if URL is passed or an external media with an src\n        const url = this.options.url || this.getSrc();\n        if (url) {\n            this.load(url, this.options.peaks, this.options.duration);\n        }\n        else if (this.options.peaks && this.options.duration) {\n            // If pre-decoded peaks and duration are provided, render a waveform w/o loading audio\n            this.loadPredecoded();\n        }\n    }\n    initTimerEvents() {\n        // The timer fires every 16ms for a smooth progress animation\n        this.subscriptions.push(this.timer.on('tick', () => {\n            const currentTime = this.getCurrentTime();\n            this.renderer.renderProgress(currentTime / this.getDuration(), true);\n            this.emit('timeupdate', currentTime);\n            this.emit('audioprocess', currentTime);\n        }));\n    }\n    initPlayerEvents() {\n        this.mediaSubscriptions.push(this.onMediaEvent('timeupdate', () => {\n            const currentTime = this.getCurrentTime();\n            this.renderer.renderProgress(currentTime / this.getDuration(), this.isPlaying());\n            this.emit('timeupdate', currentTime);\n        }), this.onMediaEvent('play', () => {\n            this.emit('play');\n            this.timer.start();\n        }), this.onMediaEvent('pause', () => {\n            this.emit('pause');\n            this.timer.stop();\n        }), this.onMediaEvent('emptied', () => {\n            this.timer.stop();\n        }), this.onMediaEvent('ended', () => {\n            this.emit('finish');\n        }), this.onMediaEvent('seeking', () => {\n            this.emit('seeking', this.getCurrentTime());\n        }));\n    }\n    initRendererEvents() {\n        this.subscriptions.push(\n        // Seek on click\n        this.renderer.on('click', (relativeX, relativeY) => {\n            if (this.options.interact) {\n                this.seekTo(relativeX);\n                this.emit('interaction', relativeX * this.getDuration());\n                this.emit('click', relativeX, relativeY);\n            }\n        }), \n        // Double click\n        this.renderer.on('dblclick', (relativeX, relativeY) => {\n            this.emit('dblclick', relativeX, relativeY);\n        }), \n        // Scroll\n        this.renderer.on('scroll', (startX, endX) => {\n            const duration = this.getDuration();\n            this.emit('scroll', startX * duration, endX * duration);\n        }), \n        // Redraw\n        this.renderer.on('render', () => {\n            this.emit('redraw');\n        }));\n        // Drag\n        {\n            let debounce;\n            this.subscriptions.push(this.renderer.on('drag', (relativeX) => {\n                if (!this.options.interact)\n                    return;\n                // Update the visual position\n                this.renderer.renderProgress(relativeX);\n                // Set the audio position with a debounce\n                clearTimeout(debounce);\n                debounce = setTimeout(() => {\n                    this.seekTo(relativeX);\n                }, this.isPlaying() ? 0 : 200);\n                this.emit('interaction', relativeX * this.getDuration());\n                this.emit('drag', relativeX);\n            }));\n        }\n    }\n    initPlugins() {\n        var _a;\n        if (!((_a = this.options.plugins) === null || _a === void 0 ? void 0 : _a.length))\n            return;\n        this.options.plugins.forEach((plugin) => {\n            this.registerPlugin(plugin);\n        });\n    }\n    unsubscribePlayerEvents() {\n        this.mediaSubscriptions.forEach((unsubscribe) => unsubscribe());\n        this.mediaSubscriptions = [];\n    }\n    /** Set new wavesurfer options and re-render it */\n    setOptions(options) {\n        this.options = Object.assign({}, this.options, options);\n        this.renderer.setOptions(this.options);\n        if (options.audioRate) {\n            this.setPlaybackRate(options.audioRate);\n        }\n        if (options.mediaControls != null) {\n            this.getMediaElement().controls = options.mediaControls;\n        }\n    }\n    /** Register a wavesurfer.js plugin */\n    registerPlugin(plugin) {\n        plugin.init(this);\n        this.plugins.push(plugin);\n        // Unregister plugin on destroy\n        this.subscriptions.push(plugin.once('destroy', () => {\n            this.plugins = this.plugins.filter((p) => p !== plugin);\n        }));\n        return plugin;\n    }\n    /** For plugins only: get the waveform wrapper div */\n    getWrapper() {\n        return this.renderer.getWrapper();\n    }\n    /** Get the current scroll position in pixels */\n    getScroll() {\n        return this.renderer.getScroll();\n    }\n    /** Get all registered plugins */\n    getActivePlugins() {\n        return this.plugins;\n    }\n    loadPredecoded() {\n        return __awaiter(this, void 0, void 0, function* () {\n            if (this.options.peaks && this.options.duration) {\n                this.decodedData = Decoder.createBuffer(this.options.peaks, this.options.duration);\n                yield Promise.resolve(); // wait for event listeners to subscribe\n                this.renderDecoded();\n            }\n        });\n    }\n    renderDecoded() {\n        return __awaiter(this, void 0, void 0, function* () {\n            if (this.decodedData) {\n                this.emit('decode', this.getDuration());\n                this.renderer.render(this.decodedData);\n            }\n        });\n    }\n    loadAudio(url, blob, channelData, duration) {\n        return __awaiter(this, void 0, void 0, function* () {\n            this.emit('load', url);\n            if (!this.options.media && this.isPlaying())\n                this.pause();\n            this.decodedData = null;\n            // Fetch the entire audio as a blob if pre-decoded data is not provided\n            if (!blob && !channelData) {\n                const onProgress = (percentage) => this.emit('loading', percentage);\n                blob = yield Fetcher.fetchBlob(url, onProgress, this.options.fetchParams);\n            }\n            // Set the mediaelement source\n            this.setSrc(url, blob);\n            // Wait for the audio duration\n            // It should be a promise to allow event listeners to subscribe to the ready and decode events\n            duration =\n                (yield Promise.resolve(duration || this.getDuration())) ||\n                    (yield new Promise((resolve) => {\n                        this.onceMediaEvent('loadedmetadata', () => resolve(this.getDuration()));\n                    })) ||\n                    (yield Promise.resolve(0));\n            // Decode the audio data or use user-provided peaks\n            if (channelData) {\n                this.decodedData = Decoder.createBuffer(channelData, duration);\n            }\n            else if (blob) {\n                const arrayBuffer = yield blob.arrayBuffer();\n                this.decodedData = yield Decoder.decode(arrayBuffer, this.options.sampleRate);\n            }\n            this.renderDecoded();\n            this.emit('ready', this.getDuration());\n        });\n    }\n    /** Load an audio file by URL, with optional pre-decoded audio data */\n    load(url, channelData, duration) {\n        return __awaiter(this, void 0, void 0, function* () {\n            yield this.loadAudio(url, undefined, channelData, duration);\n        });\n    }\n    /** Load an audio blob */\n    loadBlob(blob, channelData, duration) {\n        return __awaiter(this, void 0, void 0, function* () {\n            yield this.loadAudio('blob', blob, channelData, duration);\n        });\n    }\n    /** Zoom the waveform by a given pixels-per-second factor */\n    zoom(minPxPerSec) {\n        if (!this.decodedData) {\n            throw new Error('No audio loaded');\n        }\n        this.renderer.zoom(minPxPerSec);\n        this.emit('zoom', minPxPerSec);\n    }\n    /** Get the decoded audio data */\n    getDecodedData() {\n        return this.decodedData;\n    }\n    /** Get decoded peaks */\n    exportPeaks({ channels = 2, maxLength = 8000, precision = 10000 } = {}) {\n        if (!this.decodedData) {\n            throw new Error('The audio has not been decoded yet');\n        }\n        const maxChannels = Math.min(channels, this.decodedData.numberOfChannels);\n        const peaks = [];\n        for (let i = 0; i < maxChannels; i++) {\n            const channel = this.decodedData.getChannelData(i);\n            const data = [];\n            const sampleSize = Math.round(channel.length / maxLength);\n            for (let i = 0; i < maxLength; i++) {\n                const sample = channel.slice(i * sampleSize, (i + 1) * sampleSize);\n                const max = Math.max(...sample);\n                data.push(Math.round(max * precision) / precision);\n            }\n            peaks.push(data);\n        }\n        return peaks;\n    }\n    /** Get the duration of the audio in seconds */\n    getDuration() {\n        let duration = super.getDuration() || 0;\n        // Fall back to the decoded data duration if the media duration is incorrect\n        if ((duration === 0 || duration === Infinity) && this.decodedData) {\n            duration = this.decodedData.duration;\n        }\n        return duration;\n    }\n    /** Toggle if the waveform should react to clicks */\n    toggleInteraction(isInteractive) {\n        this.options.interact = isInteractive;\n    }\n    /** Seek to a percentage of audio as [0..1] (0 = beginning, 1 = end) */\n    seekTo(progress) {\n        const time = this.getDuration() * progress;\n        this.setTime(time);\n    }\n    /** Play or pause the audio */\n    playPause() {\n        return __awaiter(this, void 0, void 0, function* () {\n            return this.isPlaying() ? this.pause() : this.play();\n        });\n    }\n    /** Stop the audio and go to the beginning */\n    stop() {\n        this.pause();\n        this.setTime(0);\n    }\n    /** Skip N or -N seconds from the current position */\n    skip(seconds) {\n        this.setTime(this.getCurrentTime() + seconds);\n    }\n    /** Empty the waveform by loading a tiny silent audio */\n    empty() {\n        this.load('', [[0]], 0.001);\n    }\n    /** Set HTML media element */\n    setMediaElement(element) {\n        this.unsubscribePlayerEvents();\n        super.setMediaElement(element);\n        this.initPlayerEvents();\n    }\n    /** Unmount wavesurfer */\n    destroy() {\n        this.emit('destroy');\n        this.plugins.forEach((plugin) => plugin.destroy());\n        this.subscriptions.forEach((unsubscribe) => unsubscribe());\n        this.unsubscribePlayerEvents();\n        this.timer.destroy();\n        this.renderer.destroy();\n        super.destroy();\n    }\n}\nexport default WaveSurfer;\n", "export function audioBufferToWav(audioBuffer: AudioBuffer): Uint8Array {\n\tconst numOfChan = audioBuffer.numberOfChannels;\n\tconst length = audioBuffer.length * numOfChan * 2 + 44;\n\tconst buffer = new ArrayBuffer(length);\n\tconst view = new DataView(buffer);\n\tlet offset = 0;\n\n\t// Write WAV header\n\tconst writeString = function (\n\t\tview: DataView,\n\t\toffset: number,\n\t\tstring: string\n\t): void {\n\t\tfor (let i = 0; i < string.length; i++) {\n\t\t\tview.setUint8(offset + i, string.charCodeAt(i));\n\t\t}\n\t};\n\n\twriteString(view, offset, \"RIFF\");\n\toffset += 4;\n\tview.setUint32(offset, length - 8, true);\n\toffset += 4;\n\twriteString(view, offset, \"WAVE\");\n\toffset += 4;\n\twriteString(view, offset, \"fmt \");\n\toffset += 4;\n\tview.setUint32(offset, 16, true);\n\toffset += 4; // Sub-chunk size, 16 for PCM\n\tview.setUint16(offset, 1, true);\n\toffset += 2; // PCM format\n\tview.setUint16(offset, numOfChan, true);\n\toffset += 2;\n\tview.setUint32(offset, audioBuffer.sampleRate, true);\n\toffset += 4;\n\tview.setUint32(offset, audioBuffer.sampleRate * 2 * numOfChan, true);\n\toffset += 4;\n\tview.setUint16(offset, numOfChan * 2, true);\n\toffset += 2;\n\tview.setUint16(offset, 16, true);\n\toffset += 2;\n\twriteString(view, offset, \"data\");\n\toffset += 4;\n\tview.setUint32(offset, audioBuffer.length * numOfChan * 2, true);\n\toffset += 4;\n\n\t// Write PCM audio data\n\tfor (let i = 0; i < audioBuffer.length; i++) {\n\t\tfor (let channel = 0; channel < numOfChan; channel++) {\n\t\t\tconst sample = Math.max(\n\t\t\t\t-1,\n\t\t\t\tMath.min(1, audioBuffer.getChannelData(channel)[i])\n\t\t\t);\n\t\t\tview.setInt16(offset, sample * 0x7fff, true);\n\t\t\toffset += 2;\n\t\t}\n\t}\n\n\treturn new Uint8Array(buffer);\n}\n", "import type WaveSurfer from \"wavesurfer.js\";\nimport { audioBufferToWav } from \"./audioBufferToWav\";\n\nexport interface LoadedParams {\n\tautoplay?: boolean;\n}\n\nexport function blob_to_data_url(blob: Blob): Promise<string> {\n\treturn new Promise((fulfill, reject) => {\n\t\tlet reader = new FileReader();\n\t\treader.onerror = reject;\n\t\treader.onload = () => fulfill(reader.result as string);\n\t\treader.readAsDataURL(blob);\n\t});\n}\n\nexport const process_audio = async (\n\taudioBuffer: AudioBuffer,\n\tstart?: number,\n\tend?: number,\n\twaveform_sample_rate?: number\n): Promise<Uint8Array> => {\n\tconst audioContext = new AudioContext({\n\t\tsampleRate: waveform_sample_rate || audioBuffer.sampleRate\n\t});\n\tconst numberOfChannels = audioBuffer.numberOfChannels;\n\tconst sampleRate = waveform_sample_rate || audioBuffer.sampleRate;\n\n\tlet trimmedLength = audioBuffer.length;\n\tlet startOffset = 0;\n\n\tif (start && end) {\n\t\tstartOffset = Math.round(start * sampleRate);\n\t\tconst endOffset = Math.round(end * sampleRate);\n\t\ttrimmedLength = endOffset - startOffset;\n\t}\n\n\tconst trimmedAudioBuffer = audioContext.createBuffer(\n\t\tnumberOfChannels,\n\t\ttrimmedLength,\n\t\tsampleRate\n\t);\n\n\tfor (let channel = 0; channel < numberOfChannels; channel++) {\n\t\tconst channelData = audioBuffer.getChannelData(channel);\n\t\tconst trimmedData = trimmedAudioBuffer.getChannelData(channel);\n\t\tfor (let i = 0; i < trimmedLength; i++) {\n\t\t\ttrimmedData[i] = channelData[startOffset + i];\n\t\t}\n\t}\n\n\treturn audioBufferToWav(trimmedAudioBuffer);\n};\n\nexport function loaded(\n\tnode: HTMLAudioElement,\n\t{ autoplay }: LoadedParams = {}\n): void {\n\tasync function handle_playback(): Promise<void> {\n\t\tif (!autoplay) return;\n\t\tnode.pause();\n\t\tawait node.play();\n\t}\n}\n\nexport const skip_audio = (waveform: WaveSurfer, amount: number): void => {\n\tif (!waveform) return;\n\twaveform.skip(amount);\n};\n\nexport const get_skip_rewind_amount = (\n\taudio_duration: number,\n\tskip_length?: number | null\n): number => {\n\tif (!skip_length) {\n\t\tskip_length = 5;\n\t}\n\treturn (audio_duration / 100) * skip_length || 5;\n};\n", "class t{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(t,e,i){if(this.listeners[t]||(this.listeners[t]=new Set),this.listeners[t].add(e),null==i?void 0:i.once){const i=()=>{this.removeEventListener(t,i),this.removeEventListener(t,e)};return this.addEventListener(t,i),i}return()=>this.removeEventListener(t,e)}removeEventListener(t,e){var i;null===(i=this.listeners[t])||void 0===i||i.delete(e)}once(t,e){return this.on(t,e,{once:!0})}unAll(){this.listeners={}}emit(t,...e){this.listeners[t]&&this.listeners[t].forEach((t=>t(...e)))}}class e extends t{constructor(t){super(),this.subscriptions=[],this.options=t}onInit(){}init(t){this.wavesurfer=t,this.onInit()}destroy(){this.emit(\"destroy\"),this.subscriptions.forEach((t=>t()))}}function i(t,e,i,n,s=5){let r=()=>{};if(!t)return r;const o=o=>{if(2===o.button)return;o.preventDefault(),o.stopPropagation(),t.style.touchAction=\"none\";let a=o.clientX,h=o.clientY,l=!1;const d=n=>{n.preventDefault(),n.stopPropagation();const r=n.clientX,o=n.clientY;if(l||Math.abs(r-a)>=s||Math.abs(o-h)>=s){const{left:n,top:s}=t.getBoundingClientRect();l||(l=!0,null==i||i(a-n,h-s)),e(r-a,o-h,r-n,o-s),a=r,h=o}},u=t=>{l&&(t.preventDefault(),t.stopPropagation())},c=()=>{t.style.touchAction=\"\",l&&(null==n||n()),r()};document.addEventListener(\"pointermove\",d),document.addEventListener(\"pointerup\",c),document.addEventListener(\"pointerleave\",c),document.addEventListener(\"click\",u,!0),r=()=>{document.removeEventListener(\"pointermove\",d),document.removeEventListener(\"pointerup\",c),document.removeEventListener(\"pointerleave\",c),setTimeout((()=>{document.removeEventListener(\"click\",u,!0)}),10)}};return t.addEventListener(\"pointerdown\",o),()=>{r(),t.removeEventListener(\"pointerdown\",o)}}class n extends t{constructor(t,e,i=0){var n,s,r,o,a,h,l;super(),this.totalDuration=e,this.numberOfChannels=i,this.minLength=0,this.maxLength=1/0,this.id=t.id||`region-${Math.random().toString(32).slice(2)}`,this.start=this.clampPosition(t.start),this.end=this.clampPosition(null!==(n=t.end)&&void 0!==n?n:t.start),this.drag=null===(s=t.drag)||void 0===s||s,this.resize=null===(r=t.resize)||void 0===r||r,this.color=null!==(o=t.color)&&void 0!==o?o:\"rgba(0, 0, 0, 0.1)\",this.minLength=null!==(a=t.minLength)&&void 0!==a?a:this.minLength,this.maxLength=null!==(h=t.maxLength)&&void 0!==h?h:this.maxLength,this.channelIdx=null!==(l=t.channelIdx)&&void 0!==l?l:-1,this.element=this.initElement(),this.setContent(t.content),this.setPart(),this.renderPosition(),this.initMouseEvents()}clampPosition(t){return Math.max(0,Math.min(this.totalDuration,t))}setPart(){const t=this.start===this.end;this.element.setAttribute(\"part\",`${t?\"marker\":\"region\"} ${this.id}`)}addResizeHandles(t){const e=document.createElement(\"div\");e.setAttribute(\"data-resize\",\"left\"),e.setAttribute(\"style\",\"\\n        position: absolute;\\n        z-index: 2;\\n        width: 6px;\\n        height: 100%;\\n        top: 0;\\n        left: 0;\\n        border-left: 2px solid rgba(0, 0, 0, 0.5);\\n        border-radius: 2px 0 0 2px;\\n        cursor: ew-resize;\\n        word-break: keep-all;\\n      \"),e.setAttribute(\"part\",\"region-handle region-handle-left\");const n=e.cloneNode();n.setAttribute(\"data-resize\",\"right\"),n.style.left=\"\",n.style.right=\"0\",n.style.borderRight=n.style.borderLeft,n.style.borderLeft=\"\",n.style.borderRadius=\"0 2px 2px 0\",n.setAttribute(\"part\",\"region-handle region-handle-right\"),t.appendChild(e),t.appendChild(n);i(e,(t=>this.onResize(t,\"start\")),(()=>null),(()=>this.onEndResizing()),1),i(n,(t=>this.onResize(t,\"end\")),(()=>null),(()=>this.onEndResizing()),1)}removeResizeHandles(t){const e=t.querySelector('[data-resize=\"left\"]'),i=t.querySelector('[data-resize=\"right\"]');e&&t.removeChild(e),i&&t.removeChild(i)}initElement(){const t=document.createElement(\"div\"),e=this.start===this.end;let i=0,n=100;return this.channelIdx>=0&&this.channelIdx<this.numberOfChannels&&(n=100/this.numberOfChannels,i=n*this.channelIdx),t.setAttribute(\"style\",`\\n      position: absolute;\\n      top: ${i}%;\\n      height: ${n}%;\\n      background-color: ${e?\"none\":this.color};\\n      border-left: ${e?\"2px solid \"+this.color:\"none\"};\\n      border-radius: 2px;\\n      box-sizing: border-box;\\n      transition: background-color 0.2s ease;\\n      cursor: ${this.drag?\"grab\":\"default\"};\\n      pointer-events: all;\\n    `),!e&&this.resize&&this.addResizeHandles(t),t}renderPosition(){const t=this.start/this.totalDuration,e=(this.totalDuration-this.end)/this.totalDuration;this.element.style.left=100*t+\"%\",this.element.style.right=100*e+\"%\"}initMouseEvents(){const{element:t}=this;t&&(t.addEventListener(\"click\",(t=>this.emit(\"click\",t))),t.addEventListener(\"mouseenter\",(t=>this.emit(\"over\",t))),t.addEventListener(\"mouseleave\",(t=>this.emit(\"leave\",t))),t.addEventListener(\"dblclick\",(t=>this.emit(\"dblclick\",t))),i(t,(t=>this.onMove(t)),(()=>this.onStartMoving()),(()=>this.onEndMoving())))}onStartMoving(){this.drag&&(this.element.style.cursor=\"grabbing\")}onEndMoving(){this.drag&&(this.element.style.cursor=\"grab\",this.emit(\"update-end\"))}_onUpdate(t,e){if(!this.element.parentElement)return;const i=t/this.element.parentElement.clientWidth*this.totalDuration,n=e&&\"start\"!==e?this.start:this.start+i,s=e&&\"end\"!==e?this.end:this.end+i,r=s-n;n>=0&&s<=this.totalDuration&&n<=s&&r>=this.minLength&&r<=this.maxLength&&(this.start=n,this.end=s,this.renderPosition(),this.emit(\"update\"))}onMove(t){this.drag&&this._onUpdate(t)}onResize(t,e){this.resize&&this._onUpdate(t,e)}onEndResizing(){this.resize&&this.emit(\"update-end\")}_setTotalDuration(t){this.totalDuration=t,this.renderPosition()}play(){this.emit(\"play\")}setContent(t){var e;if(null===(e=this.content)||void 0===e||e.remove(),t){if(\"string\"==typeof t){this.content=document.createElement(\"div\");const e=this.start===this.end;this.content.style.padding=`0.2em ${e?.2:.4}em`,this.content.textContent=t}else this.content=t;this.content.setAttribute(\"part\",\"region-content\"),this.element.appendChild(this.content)}else this.content=void 0}setOptions(t){var e,i;if(t.color&&(this.color=t.color,this.element.style.backgroundColor=this.color),void 0!==t.drag&&(this.drag=t.drag,this.element.style.cursor=this.drag?\"grab\":\"default\"),void 0!==t.start||void 0!==t.end){const n=this.start===this.end;this.start=this.clampPosition(null!==(e=t.start)&&void 0!==e?e:this.start),this.end=this.clampPosition(null!==(i=t.end)&&void 0!==i?i:n?this.start:this.end),this.renderPosition(),this.setPart()}if(t.content&&this.setContent(t.content),t.id&&(this.id=t.id,this.setPart()),void 0!==t.resize&&t.resize!==this.resize){const e=this.start===this.end;this.resize=t.resize,this.resize&&!e?this.addResizeHandles(this.element):this.removeResizeHandles(this.element)}}remove(){this.emit(\"remove\"),this.element.remove(),this.element=null}}class s extends e{constructor(t){super(t),this.regions=[],this.regionsContainer=this.initRegionsContainer()}static create(t){return new s(t)}onInit(){if(!this.wavesurfer)throw Error(\"WaveSurfer is not initialized\");this.wavesurfer.getWrapper().appendChild(this.regionsContainer);let t=[];this.subscriptions.push(this.wavesurfer.on(\"timeupdate\",(e=>{const i=this.regions.filter((t=>t.start<=e&&t.end>=e));i.forEach((e=>{t.includes(e)||this.emit(\"region-in\",e)})),t.forEach((t=>{i.includes(t)||this.emit(\"region-out\",t)})),t=i})))}initRegionsContainer(){const t=document.createElement(\"div\");return t.setAttribute(\"style\",\"\\n      position: absolute;\\n      top: 0;\\n      left: 0;\\n      width: 100%;\\n      height: 100%;\\n      z-index: 3;\\n      pointer-events: none;\\n    \"),t}getRegions(){return this.regions}avoidOverlapping(t){if(!t.content)return;const e=t.content,i=e.getBoundingClientRect().left,n=t.element.scrollWidth,s=this.regions.filter((e=>{if(e===t||!e.content)return!1;const s=e.content.getBoundingClientRect().left,r=e.element.scrollWidth;return i<s+r&&s<i+n})).map((t=>{var e;return(null===(e=t.content)||void 0===e?void 0:e.getBoundingClientRect().height)||0})).reduce(((t,e)=>t+e),0);e.style.marginTop=`${s}px`}saveRegion(t){this.regionsContainer.appendChild(t.element),this.avoidOverlapping(t),this.regions.push(t);const e=[t.on(\"update-end\",(()=>{this.avoidOverlapping(t),this.emit(\"region-updated\",t)})),t.on(\"play\",(()=>{var e,i;null===(e=this.wavesurfer)||void 0===e||e.play(),null===(i=this.wavesurfer)||void 0===i||i.setTime(t.start)})),t.on(\"click\",(e=>{this.emit(\"region-clicked\",t,e)})),t.on(\"dblclick\",(e=>{this.emit(\"region-double-clicked\",t,e)})),t.once(\"remove\",(()=>{e.forEach((t=>t())),this.regions=this.regions.filter((e=>e!==t))}))];this.subscriptions.push(...e),this.emit(\"region-created\",t)}addRegion(t){var e,i;if(!this.wavesurfer)throw Error(\"WaveSurfer is not initialized\");const s=this.wavesurfer.getDuration(),r=null===(i=null===(e=this.wavesurfer)||void 0===e?void 0:e.getDecodedData())||void 0===i?void 0:i.numberOfChannels,o=new n(t,s,r);return s?this.saveRegion(o):this.subscriptions.push(this.wavesurfer.once(\"ready\",(t=>{o._setTotalDuration(t),this.saveRegion(o)}))),o}enableDragSelection(t){var e,s;const r=null===(s=null===(e=this.wavesurfer)||void 0===e?void 0:e.getWrapper())||void 0===s?void 0:s.querySelector(\"div\");if(!r)return()=>{};let o=null,a=0;return i(r,((t,e,i)=>{o&&o._onUpdate(t,i>a?\"end\":\"start\")}),(e=>{var i,s;if(a=e,!this.wavesurfer)return;const r=this.wavesurfer.getDuration(),h=null===(s=null===(i=this.wavesurfer)||void 0===i?void 0:i.getDecodedData())||void 0===s?void 0:s.numberOfChannels,l=this.wavesurfer.getWrapper().clientWidth,d=e/l*r,u=(e+5)/l*r;o=new n(Object.assign(Object.assign({},t),{start:d,end:u}),r,h),this.regionsContainer.appendChild(o.element)}),(()=>{o&&(this.saveRegion(o),o=null)}))}clearRegions(){this.regions.forEach((t=>t.remove()))}destroy(){this.clearRegions(),super.destroy()}}export{s as default};\n", "<script lang=\"ts\">\n\timport { VolumeMuted, VolumeHigh, VolumeLow } from \"@gradio/icons\";\n\texport let currentVolume: number;\n</script>\n\n{#if currentVolume == 0}\n\t<VolumeMuted />\n{:else if currentVolume < 0.5}\n\t<VolumeLow />\n{:else if currentVolume >= 0.5}\n\t<VolumeHigh />\n{/if}\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport WaveSurfer from \"wavesurfer.js\";\n\n\texport let currentVolume = 1;\n\texport let show_volume_slider = false;\n\texport let waveform: WaveSurfer;\n\n\tlet volumeElement: HTMLInputElement;\n\n\tonMount(() => {\n\t\tadjustSlider();\n\t});\n\n\tconst adjustSlider = (): void => {\n\t\tlet slider = volumeElement;\n\t\tif (!slider) return;\n\n\t\tslider.style.background = `linear-gradient(to right, var(--color-accent) ${\n\t\t\tcurrentVolume * 100\n\t\t}%, var(--neutral-400) ${currentVolume * 100}%)`;\n\t};\n\n\t$: currentVolume, adjustSlider();\n</script>\n\n<input\n\tbind:this={volumeElement}\n\tid=\"volume\"\n\tclass=\"volume-slider\"\n\ttype=\"range\"\n\tmin=\"0\"\n\tmax=\"1\"\n\tstep=\"0.01\"\n\tvalue={currentVolume}\n\ton:focusout={() => (show_volume_slider = false)}\n\ton:input={(e) => {\n\t\tif (e.target instanceof HTMLInputElement) {\n\t\t\tcurrentVolume = parseFloat(e.target.value);\n\t\t\twaveform.setVolume(currentVolume);\n\t\t}\n\t}}\n/>\n\n<style>\n\t.volume-slider {\n\t\t-webkit-appearance: none;\n\t\tappearance: none;\n\t\twidth: var(--size-20);\n\t\taccent-color: var(--color-accent);\n\t\theight: 4px;\n\t\tcursor: pointer;\n\t\toutline: none;\n\t\tborder-radius: 15px;\n\t\tbackground-color: var(--neutral-400);\n\t}\n\n\tinput[type=\"range\"]::-webkit-slider-thumb {\n\t\t-webkit-appearance: none;\n\t\tappearance: none;\n\t\theight: 15px;\n\t\twidth: 15px;\n\t\tbackground-color: var(--color-accent);\n\t\tborder-radius: 50%;\n\t\tborder: none;\n\t\ttransition: 0.2s ease-in-out;\n\t}\n\n\tinput[type=\"range\"]::-moz-range-thumb {\n\t\theight: 15px;\n\t\twidth: 15px;\n\t\tbackground-color: var(--color-accent);\n\t\tborder-radius: 50%;\n\t\tborder: none;\n\t\ttransition: 0.2s ease-in-out;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { Play, Pause, Forward, Backward, Undo, Trim } from \"@gradio/icons\";\n\timport { get_skip_rewind_amount } from \"../shared/utils\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport WaveSurfer from \"wavesurfer.js\";\n\timport RegionsPlugin, {\n\t\ttype Region\n\t} from \"wavesurfer.js/dist/plugins/regions.js\";\n\timport type { WaveformOptions } from \"./types\";\n\timport VolumeLevels from \"./VolumeLevels.svelte\";\n\timport VolumeControl from \"./VolumeControl.svelte\";\n\n\texport let waveform: WaveSurfer;\n\texport let audio_duration: number;\n\texport let i18n: I18nFormatter;\n\texport let playing: boolean;\n\texport let show_redo = false;\n\texport let interactive = false;\n\texport let handle_trim_audio: (start: number, end: number) => void;\n\texport let mode = \"\";\n\texport let container: HTMLDivElement;\n\texport let handle_reset_value: () => void;\n\texport let waveform_options: WaveformOptions = {};\n\texport let trim_region_settings: WaveformOptions = {};\n\texport let show_volume_slider = false;\n\texport let editable = true;\n\n\texport let trimDuration = 0;\n\n\tlet playbackSpeeds = [0.5, 1, 1.5, 2];\n\tlet playbackSpeed = playbackSpeeds[1];\n\n\tlet trimRegion: RegionsPlugin;\n\tlet activeRegion: Region | null = null;\n\n\tlet leftRegionHandle: HTMLDivElement | null;\n\tlet rightRegionHandle: HTMLDivElement | null;\n\tlet activeHandle = \"\";\n\n\tlet currentVolume = 1;\n\n\t$: trimRegion = waveform.registerPlugin(RegionsPlugin.create());\n\n\t$: trimRegion?.on(\"region-out\", (region) => {\n\t\tregion.play();\n\t});\n\n\t$: trimRegion?.on(\"region-updated\", (region) => {\n\t\ttrimDuration = region.end - region.start;\n\t});\n\n\t$: trimRegion?.on(\"region-clicked\", (region, e) => {\n\t\te.stopPropagation(); // prevent triggering a click on the waveform\n\t\tactiveRegion = region;\n\t\tregion.play();\n\t});\n\n\tconst addTrimRegion = (): void => {\n\t\tactiveRegion = trimRegion.addRegion({\n\t\t\tstart: audio_duration / 4,\n\t\t\tend: audio_duration / 2,\n\t\t\t...trim_region_settings\n\t\t});\n\n\t\ttrimDuration = activeRegion.end - activeRegion.start;\n\t};\n\n\t$: if (activeRegion) {\n\t\tconst shadowRoot = container.children[0]!.shadowRoot!;\n\n\t\trightRegionHandle = shadowRoot.querySelector('[data-resize=\"right\"]');\n\t\tleftRegionHandle = shadowRoot.querySelector('[data-resize=\"left\"]');\n\n\t\tif (leftRegionHandle && rightRegionHandle) {\n\t\t\tleftRegionHandle.setAttribute(\"role\", \"button\");\n\t\t\trightRegionHandle.setAttribute(\"role\", \"button\");\n\t\t\tleftRegionHandle?.setAttribute(\"aria-label\", \"Drag to adjust start time\");\n\t\t\trightRegionHandle?.setAttribute(\"aria-label\", \"Drag to adjust end time\");\n\t\t\tleftRegionHandle?.setAttribute(\"tabindex\", \"0\");\n\t\t\trightRegionHandle?.setAttribute(\"tabindex\", \"0\");\n\n\t\t\tleftRegionHandle.addEventListener(\"focus\", () => {\n\t\t\t\tif (trimRegion) activeHandle = \"left\";\n\t\t\t});\n\n\t\t\trightRegionHandle.addEventListener(\"focus\", () => {\n\t\t\t\tif (trimRegion) activeHandle = \"right\";\n\t\t\t});\n\t\t}\n\t}\n\n\tconst trimAudio = (): void => {\n\t\tif (waveform && trimRegion) {\n\t\t\tif (activeRegion) {\n\t\t\t\tconst start = activeRegion.start;\n\t\t\t\tconst end = activeRegion.end;\n\t\t\t\thandle_trim_audio(start, end);\n\t\t\t\tmode = \"\";\n\t\t\t\tactiveRegion = null;\n\t\t\t}\n\t\t}\n\t};\n\n\tconst clearRegions = (): void => {\n\t\ttrimRegion?.getRegions().forEach((region) => {\n\t\t\tregion.remove();\n\t\t});\n\t\ttrimRegion?.clearRegions();\n\t};\n\n\tconst toggleTrimmingMode = (): void => {\n\t\tclearRegions();\n\t\tif (mode === \"edit\") {\n\t\t\tmode = \"\";\n\t\t} else {\n\t\t\tmode = \"edit\";\n\t\t\taddTrimRegion();\n\t\t}\n\t};\n\n\tconst adjustRegionHandles = (handle: string, key: string): void => {\n\t\tlet newStart;\n\t\tlet newEnd;\n\n\t\tif (!activeRegion) return;\n\t\tif (handle === \"left\") {\n\t\t\tif (key === \"ArrowLeft\") {\n\t\t\t\tnewStart = activeRegion.start - 0.05;\n\t\t\t\tnewEnd = activeRegion.end;\n\t\t\t} else {\n\t\t\t\tnewStart = activeRegion.start + 0.05;\n\t\t\t\tnewEnd = activeRegion.end;\n\t\t\t}\n\t\t} else {\n\t\t\tif (key === \"ArrowLeft\") {\n\t\t\t\tnewStart = activeRegion.start;\n\t\t\t\tnewEnd = activeRegion.end - 0.05;\n\t\t\t} else {\n\t\t\t\tnewStart = activeRegion.start;\n\t\t\t\tnewEnd = activeRegion.end + 0.05;\n\t\t\t}\n\t\t}\n\n\t\tactiveRegion.setOptions({\n\t\t\tstart: newStart,\n\t\t\tend: newEnd\n\t\t});\n\n\t\ttrimDuration = activeRegion.end - activeRegion.start;\n\t};\n\n\t$: trimRegion &&\n\t\twindow.addEventListener(\"keydown\", (e) => {\n\t\t\tif (e.key === \"ArrowLeft\") {\n\t\t\t\tadjustRegionHandles(activeHandle, \"ArrowLeft\");\n\t\t\t} else if (e.key === \"ArrowRight\") {\n\t\t\t\tadjustRegionHandles(activeHandle, \"ArrowRight\");\n\t\t\t}\n\t\t});\n</script>\n\n<div class=\"controls\" data-testid=\"waveform-controls\">\n\t<div class=\"control-wrapper\">\n\t\t<button\n\t\t\tclass=\"action icon volume\"\n\t\t\tstyle:color={show_volume_slider\n\t\t\t\t? \"var(--color-accent)\"\n\t\t\t\t: \"var(--neutral-400)\"}\n\t\t\taria-label=\"Adjust volume\"\n\t\t\ton:click={() => (show_volume_slider = !show_volume_slider)}\n\t\t>\n\t\t\t<VolumeLevels {currentVolume} />\n\t\t</button>\n\n\t\t{#if show_volume_slider}\n\t\t\t<VolumeControl bind:currentVolume bind:show_volume_slider {waveform} />\n\t\t{/if}\n\n\t\t<button\n\t\t\tclass:hidden={show_volume_slider}\n\t\t\tclass=\"playback icon\"\n\t\t\taria-label={`Adjust playback speed to ${\n\t\t\t\tplaybackSpeeds[\n\t\t\t\t\t(playbackSpeeds.indexOf(playbackSpeed) + 1) % playbackSpeeds.length\n\t\t\t\t]\n\t\t\t}x`}\n\t\t\ton:click={() => {\n\t\t\t\tplaybackSpeed =\n\t\t\t\t\tplaybackSpeeds[\n\t\t\t\t\t\t(playbackSpeeds.indexOf(playbackSpeed) + 1) % playbackSpeeds.length\n\t\t\t\t\t];\n\n\t\t\t\twaveform.setPlaybackRate(playbackSpeed);\n\t\t\t}}\n\t\t>\n\t\t\t<span>{playbackSpeed}x</span>\n\t\t</button>\n\t</div>\n\n\t<div class=\"play-pause-wrapper\">\n\t\t<button\n\t\t\tclass=\"rewind icon\"\n\t\t\taria-label={`Skip backwards by ${get_skip_rewind_amount(\n\t\t\t\taudio_duration,\n\t\t\t\twaveform_options.skip_length\n\t\t\t)} seconds`}\n\t\t\ton:click={() =>\n\t\t\t\twaveform.skip(\n\t\t\t\t\tget_skip_rewind_amount(audio_duration, waveform_options.skip_length) *\n\t\t\t\t\t\t-1\n\t\t\t\t)}\n\t\t>\n\t\t\t<Backward />\n\t\t</button>\n\t\t<button\n\t\t\tclass=\"play-pause-button icon\"\n\t\t\ton:click={() => waveform.playPause()}\n\t\t\taria-label={playing ? i18n(\"audio.pause\") : i18n(\"audio.play\")}\n\t\t>\n\t\t\t{#if playing}\n\t\t\t\t<Pause />\n\t\t\t{:else}\n\t\t\t\t<Play />\n\t\t\t{/if}\n\t\t</button>\n\t\t<button\n\t\t\tclass=\"skip icon\"\n\t\t\taria-label=\"Skip forward by {get_skip_rewind_amount(\n\t\t\t\taudio_duration,\n\t\t\t\twaveform_options.skip_length\n\t\t\t)} seconds\"\n\t\t\ton:click={() =>\n\t\t\t\twaveform.skip(\n\t\t\t\t\tget_skip_rewind_amount(audio_duration, waveform_options.skip_length)\n\t\t\t\t)}\n\t\t>\n\t\t\t<Forward />\n\t\t</button>\n\t</div>\n\n\t<div class=\"settings-wrapper\">\n\t\t{#if editable && interactive}\n\t\t\t{#if show_redo && mode === \"\"}\n\t\t\t\t<button\n\t\t\t\t\tclass=\"action icon\"\n\t\t\t\t\taria-label=\"Reset audio\"\n\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\thandle_reset_value();\n\t\t\t\t\t\tclearRegions();\n\t\t\t\t\t\tmode = \"\";\n\t\t\t\t\t}}\n\t\t\t\t>\n\t\t\t\t\t<Undo />\n\t\t\t\t</button>\n\t\t\t{/if}\n\n\t\t\t{#if mode === \"\"}\n\t\t\t\t<button\n\t\t\t\t\tclass=\"action icon\"\n\t\t\t\t\taria-label=\"Trim audio to selection\"\n\t\t\t\t\ton:click={toggleTrimmingMode}\n\t\t\t\t>\n\t\t\t\t\t<Trim />\n\t\t\t\t</button>\n\t\t\t{:else}\n\t\t\t\t<button class=\"text-button\" on:click={trimAudio}>Trim</button>\n\t\t\t\t<button class=\"text-button\" on:click={toggleTrimmingMode}>Cancel</button\n\t\t\t\t>\n\t\t\t{/if}\n\t\t{/if}\n\t</div>\n</div>\n\n<style>\n\t.settings-wrapper {\n\t\tdisplay: flex;\n\t\tjustify-self: self-end;\n\t\talign-items: center;\n\t\tgrid-area: editing;\n\t}\n\t.text-button {\n\t\tborder: 1px solid var(--neutral-400);\n\t\tborder-radius: var(--radius-sm);\n\t\tfont-weight: 300;\n\t\tfont-size: var(--size-3);\n\t\ttext-align: center;\n\t\tcolor: var(--neutral-400);\n\t\theight: var(--size-5);\n\t\tfont-weight: bold;\n\t\tpadding: 0 5px;\n\t\tmargin-left: 5px;\n\t}\n\n\t.text-button:hover,\n\t.text-button:focus {\n\t\tcolor: var(--color-accent);\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.controls {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: 1fr 1fr 1fr;\n\t\tgrid-template-areas: \"controls playback editing\";\n\t\tmargin-top: 5px;\n\t\talign-items: center;\n\t\tposition: relative;\n\t\tflex-wrap: wrap;\n\t\tjustify-content: space-between;\n\t}\n\t.controls div {\n\t\tmargin: var(--size-1) 0;\n\t}\n\n\t@media (max-width: 600px) {\n\t\t.controls {\n\t\t\tgrid-template-columns: 1fr 1fr;\n\t\t\tgrid-template-rows: auto auto;\n\t\t\tgrid-template-areas:\n\t\t\t\t\"playback playback\"\n\t\t\t\t\"controls editing\";\n\t\t\toverflow: scroll;\n\t\t}\n\t}\n\n\t.hidden {\n\t\tdisplay: none;\n\t}\n\n\t.control-wrapper {\n\t\tdisplay: flex;\n\t\tjustify-self: self-start;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tgrid-area: controls;\n\t}\n\n\t.action {\n\t\twidth: var(--size-5);\n\t\tcolor: var(--neutral-400);\n\t\tmargin-left: var(--spacing-md);\n\t}\n\t.icon:hover,\n\t.icon:focus {\n\t\tcolor: var(--color-accent);\n\t}\n\t.play-pause-wrapper {\n\t\tdisplay: flex;\n\t\tjustify-self: center;\n\t\tgrid-area: playback;\n\t}\n\n\t@media (max-width: 600px) {\n\t\t.play-pause-wrapper {\n\t\t\tmargin: var(--spacing-md);\n\t\t}\n\t}\n\t.playback {\n\t\tborder: 1px solid var(--neutral-400);\n\t\tborder-radius: var(--radius-sm);\n\t\twidth: 5.5ch;\n\t\tfont-weight: 300;\n\t\tfont-size: var(--size-3);\n\t\ttext-align: center;\n\t\tcolor: var(--neutral-400);\n\t\theight: var(--size-5);\n\t\tfont-weight: bold;\n\t}\n\n\t.playback:hover,\n\t.playback:focus {\n\t\tcolor: var(--color-accent);\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.rewind,\n\t.skip {\n\t\tmargin: 0 10px;\n\t\tcolor: var(--neutral-400);\n\t}\n\n\t.play-pause-button {\n\t\twidth: var(--size-8);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tcolor: var(--neutral-400);\n\t\tfill: var(--neutral-400);\n\t}\n\n\t.volume {\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tmargin-right: var(--spacing-xl);\n\t\twidth: var(--size-5);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport { Music } from \"@gradio/icons\";\n\timport { format_time, type I18nFormatter } from \"@gradio/utils\";\n\timport WaveSurfer from \"wavesurfer.js\";\n\timport { skip_audio, process_audio } from \"../shared/utils\";\n\timport WaveformControls from \"../shared/WaveformControls.svelte\";\n\timport { Empty } from \"@gradio/atoms\";\n\timport { resolve_wasm_src } from \"@gradio/wasm/svelte\";\n\timport type { FileData } from \"@gradio/client\";\n\timport type { WaveformOptions } from \"../shared/types\";\n\timport { createEventDispatcher } from \"svelte\";\n\n\texport let value: null | FileData = null;\n\t$: url = value?.url;\n\texport let label: string;\n\texport let i18n: I18nFormatter;\n\texport let dispatch_blob: (\n\t\tblobs: Uint8Array[] | Blob[],\n\t\tevent: \"stream\" | \"change\" | \"stop_recording\"\n\t) => Promise<void> = () => Promise.resolve();\n\texport let interactive = false;\n\texport let editable = true;\n\texport let trim_region_settings = {};\n\texport let waveform_settings: Record<string, any>;\n\texport let waveform_options: WaveformOptions;\n\texport let mode = \"\";\n\texport let handle_reset_value: () => void = () => {};\n\n\tlet container: HTMLDivElement;\n\tlet waveform: WaveSurfer | undefined;\n\tlet playing = false;\n\n\tlet timeRef: HTMLTimeElement;\n\tlet durationRef: HTMLTimeElement;\n\tlet audio_duration: number;\n\n\tlet trimDuration = 0;\n\n\tlet show_volume_slider = false;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tstop: undefined;\n\t\tplay: undefined;\n\t\tpause: undefined;\n\t\tedit: undefined;\n\t\tend: undefined;\n\t}>();\n\n\tconst create_waveform = (): void => {\n\t\twaveform = WaveSurfer.create({\n\t\t\tcontainer: container,\n\t\t\t...waveform_settings\n\t\t});\n\t\tresolve_wasm_src(value?.url).then((resolved_src) => {\n\t\t\tif (resolved_src && waveform) {\n\t\t\t\treturn waveform.load(resolved_src);\n\t\t\t}\n\t\t});\n\t};\n\n\t$: if (container !== undefined) {\n\t\tif (waveform !== undefined) waveform.destroy();\n\t\tcontainer.innerHTML = \"\";\n\t\tcreate_waveform();\n\t\tplaying = false;\n\t}\n\n\t$: waveform?.on(\"decode\", (duration: any) => {\n\t\taudio_duration = duration;\n\t\tdurationRef && (durationRef.textContent = format_time(duration));\n\t});\n\n\t$: waveform?.on(\n\t\t\"timeupdate\",\n\t\t(currentTime: any) =>\n\t\t\ttimeRef && (timeRef.textContent = format_time(currentTime))\n\t);\n\n\t$: waveform?.on(\"ready\", () => {\n\t\tif (!waveform_settings.autoplay) {\n\t\t\twaveform?.stop();\n\t\t} else {\n\t\t\twaveform?.play();\n\t\t}\n\t});\n\n\t$: waveform?.on(\"finish\", () => {\n\t\tplaying = false;\n\t\tdispatch(\"stop\");\n\t});\n\t$: waveform?.on(\"pause\", () => {\n\t\tplaying = false;\n\t\tdispatch(\"pause\");\n\t});\n\t$: waveform?.on(\"play\", () => {\n\t\tplaying = true;\n\t\tdispatch(\"play\");\n\t});\n\n\tconst handle_trim_audio = async (\n\t\tstart: number,\n\t\tend: number\n\t): Promise<void> => {\n\t\tmode = \"\";\n\t\tconst decodedData = waveform?.getDecodedData();\n\t\tif (decodedData)\n\t\t\tawait process_audio(\n\t\t\t\tdecodedData,\n\t\t\t\tstart,\n\t\t\t\tend,\n\t\t\t\twaveform_settings.sampleRate\n\t\t\t).then(async (trimmedBlob: Uint8Array) => {\n\t\t\t\tawait dispatch_blob([trimmedBlob], \"change\");\n\t\t\t\twaveform?.destroy();\n\t\t\t\tcontainer.innerHTML = \"\";\n\t\t\t});\n\t\tdispatch(\"edit\");\n\t};\n\n\tasync function load_audio(data: string): Promise<void> {\n\t\tawait resolve_wasm_src(data).then((resolved_src) => {\n\t\t\tif (!resolved_src || value?.is_stream) return;\n\t\t\treturn waveform?.load(resolved_src);\n\t\t});\n\t}\n\n\t$: url && load_audio(url);\n\n\tonMount(() => {\n\t\twindow.addEventListener(\"keydown\", (e) => {\n\t\t\tif (!waveform || show_volume_slider) return;\n\t\t\tif (e.key === \"ArrowRight\" && mode !== \"edit\") {\n\t\t\t\tskip_audio(waveform, 0.1);\n\t\t\t} else if (e.key === \"ArrowLeft\" && mode !== \"edit\") {\n\t\t\t\tskip_audio(waveform, -0.1);\n\t\t\t}\n\t\t});\n\t});\n</script>\n\n{#if value === null}\n\t<Empty size=\"small\">\n\t\t<Music />\n\t</Empty>\n{:else if value.is_stream}\n\t<audio\n\t\tclass=\"standard-player\"\n\t\tsrc={value.url}\n\t\tcontrols\n\t\tautoplay={waveform_settings.autoplay}\n\t/>\n{:else}\n\t<div\n\t\tclass=\"component-wrapper\"\n\t\tdata-testid={label ? \"waveform-\" + label : \"unlabelled-audio\"}\n\t>\n\t\t<div class=\"waveform-container\">\n\t\t\t<div id=\"waveform\" bind:this={container} />\n\t\t</div>\n\n\t\t<div class=\"timestamps\">\n\t\t\t<time bind:this={timeRef} id=\"time\">0:00</time>\n\t\t\t<div>\n\t\t\t\t{#if mode === \"edit\" && trimDuration > 0}\n\t\t\t\t\t<time id=\"trim-duration\">{format_time(trimDuration)}</time>\n\t\t\t\t{/if}\n\t\t\t\t<time bind:this={durationRef} id=\"duration\">0:00</time>\n\t\t\t</div>\n\t\t</div>\n\n\t\t{#if waveform}\n\t\t\t<WaveformControls\n\t\t\t\t{container}\n\t\t\t\t{waveform}\n\t\t\t\t{playing}\n\t\t\t\t{audio_duration}\n\t\t\t\t{i18n}\n\t\t\t\t{interactive}\n\t\t\t\t{handle_trim_audio}\n\t\t\t\tbind:mode\n\t\t\t\tbind:trimDuration\n\t\t\t\tbind:show_volume_slider\n\t\t\t\tshow_redo={interactive}\n\t\t\t\t{handle_reset_value}\n\t\t\t\t{waveform_options}\n\t\t\t\t{trim_region_settings}\n\t\t\t\t{editable}\n\t\t\t/>\n\t\t{/if}\n\t</div>\n{/if}\n\n<style>\n\t.component-wrapper {\n\t\tpadding: var(--size-3);\n\t\twidth: 100%;\n\t}\n\n\t:global(::part(wrapper)) {\n\t\tmargin-bottom: var(--size-2);\n\t}\n\n\t.timestamps {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\twidth: 100%;\n\t\tpadding: var(--size-1) 0;\n\t}\n\n\t#time {\n\t\tcolor: var(--neutral-400);\n\t}\n\n\t#duration {\n\t\tcolor: var(--neutral-400);\n\t}\n\n\t#trim-duration {\n\t\tcolor: var(--color-accent);\n\t\tmargin-right: var(--spacing-sm);\n\t}\n\t.waveform-container {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\twidth: var(--size-full);\n\t}\n\n\t#waveform {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tposition: relative;\n\t}\n\n\t.standard-player {\n\t\twidth: 100%;\n\t\tpadding: var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { uploadToHuggingFace } from \"@gradio/utils\";\n\timport { Empty } from \"@gradio/atoms\";\n\timport { ShareButton, IconButton, BlockLabel } from \"@gradio/atoms\";\n\timport { Download, Music } from \"@gradio/icons\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport AudioPlayer from \"../player/AudioPlayer.svelte\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { DownloadLink } from \"@gradio/wasm/svelte\";\n\timport type { WaveformOptions } from \"../shared/types\";\n\n\texport let value: null | FileData = null;\n\texport let label: string;\n\texport let show_label = true;\n\texport let show_download_button = true;\n\texport let show_share_button = false;\n\texport let i18n: I18nFormatter;\n\texport let waveform_settings: Record<string, any>;\n\texport let waveform_options: WaveformOptions;\n\texport let editable = true;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: FileData;\n\t\tplay: undefined;\n\t\tpause: undefined;\n\t\tend: undefined;\n\t\tstop: undefined;\n\t}>();\n\n\t$: value && dispatch(\"change\", value);\n</script>\n\n<BlockLabel\n\t{show_label}\n\tIcon={Music}\n\tfloat={false}\n\tlabel={label || i18n(\"audio.audio\")}\n/>\n\n{#if value !== null}\n\t<div class=\"icon-buttons\">\n\t\t{#if show_download_button}\n\t\t\t<DownloadLink href={value.url} download={value.orig_name || value.path}>\n\t\t\t\t<IconButton Icon={Download} label={i18n(\"common.download\")} />\n\t\t\t</DownloadLink>\n\t\t{/if}\n\t\t{#if show_share_button}\n\t\t\t<ShareButton\n\t\t\t\t{i18n}\n\t\t\t\ton:error\n\t\t\t\ton:share\n\t\t\t\tformatter={async (value) => {\n\t\t\t\t\tif (!value) return \"\";\n\t\t\t\t\tlet url = await uploadToHuggingFace(value.url, \"url\");\n\t\t\t\t\treturn `<audio controls src=\"${url}\"></audio>`;\n\t\t\t\t}}\n\t\t\t\t{value}\n\t\t\t/>\n\t\t{/if}\n\t</div>\n\n\t<AudioPlayer\n\t\t{value}\n\t\t{label}\n\t\t{i18n}\n\t\t{waveform_settings}\n\t\t{waveform_options}\n\t\t{editable}\n\t\ton:pause\n\t\ton:play\n\t\ton:stop\n\t/>\n{:else}\n\t<Empty size=\"small\">\n\t\t<Music />\n\t</Empty>\n{/if}\n\n<style>\n\t.icon-buttons {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: 6px;\n\t\tright: 6px;\n\t\tgap: var(--size-1);\n\t}\n</style>\n", "function e(e,t,i,s){return new(i||(i=Promise))((function(r,n){function o(e){try{d(s.next(e))}catch(e){n(e)}}function a(e){try{d(s.throw(e))}catch(e){n(e)}}function d(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(o,a)}d((s=s.apply(e,t||[])).next())}))}\"function\"==typeof SuppressedError&&SuppressedError;class t{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,i){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),null==i?void 0:i.once){const i=()=>{this.removeEventListener(e,i),this.removeEventListener(e,t)};return this.addEventListener(e,i),i}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var i;null===(i=this.listeners[e])||void 0===i||i.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach((e=>e(...t)))}}class i extends t{constructor(e){super(),this.subscriptions=[],this.options=e}onInit(){}init(e){this.wavesurfer=e,this.onInit()}destroy(){this.emit(\"destroy\"),this.subscriptions.forEach((e=>e()))}}const s=[\"audio/webm\",\"audio/wav\",\"audio/mpeg\",\"audio/mp4\",\"audio/mp3\"];class r extends i{constructor(e){var t;super(Object.assign(Object.assign({},e),{audioBitsPerSecond:null!==(t=e.audioBitsPerSecond)&&void 0!==t?t:128e3})),this.stream=null,this.mediaRecorder=null}static create(e){return new r(e||{})}renderMicStream(e){const t=new AudioContext,i=t.createMediaStreamSource(e),s=t.createAnalyser();i.connect(s);const r=s.frequencyBinCount,n=new Float32Array(r),o=r/t.sampleRate;let a;const d=()=>{s.getFloatTimeDomainData(n),this.wavesurfer&&(this.wavesurfer.options.cursorWidth=0,this.wavesurfer.options.interact=!1,this.wavesurfer.load(\"\",[n],o)),a=requestAnimationFrame(d)};return d(),()=>{cancelAnimationFrame(a),null==i||i.disconnect(),null==t||t.close()}}startMic(t){return e(this,void 0,void 0,(function*(){let e;try{e=yield navigator.mediaDevices.getUserMedia({audio:!(null==t?void 0:t.deviceId)||{deviceId:t.deviceId}})}catch(e){throw new Error(\"Error accessing the microphone: \"+e.message)}const i=this.renderMicStream(e);return this.subscriptions.push(this.once(\"destroy\",i)),this.stream=e,e}))}stopMic(){this.stream&&(this.stream.getTracks().forEach((e=>e.stop())),this.stream=null,this.mediaRecorder=null)}startRecording(t){return e(this,void 0,void 0,(function*(){const e=this.stream||(yield this.startMic(t)),i=this.mediaRecorder||new MediaRecorder(e,{mimeType:this.options.mimeType||s.find((e=>MediaRecorder.isTypeSupported(e))),audioBitsPerSecond:this.options.audioBitsPerSecond});this.mediaRecorder=i,this.stopRecording();const r=[];i.ondataavailable=e=>{e.data.size>0&&r.push(e.data)},i.onstop=()=>{var e;const t=new Blob(r,{type:i.mimeType});this.emit(\"record-end\",t),!1!==this.options.renderRecordedAudio&&(null===(e=this.wavesurfer)||void 0===e||e.load(URL.createObjectURL(t)))},i.start(),this.emit(\"record-start\")}))}isRecording(){var e;return\"recording\"===(null===(e=this.mediaRecorder)||void 0===e?void 0:e.state)}isPaused(){var e;return\"paused\"===(null===(e=this.mediaRecorder)||void 0===e?void 0:e.state)}stopRecording(){var e;this.isRecording()&&(null===(e=this.mediaRecorder)||void 0===e||e.stop())}pauseRecording(){var e;this.isRecording()&&(null===(e=this.mediaRecorder)||void 0===e||e.pause(),this.emit(\"record-pause\"))}resumeRecording(){var e;this.isPaused()&&(null===(e=this.mediaRecorder)||void 0===e||e.resume(),this.emit(\"record-resume\"))}static getAvailableAudioDevices(){return e(this,void 0,void 0,(function*(){return navigator.mediaDevices.enumerateDevices().then((e=>e.filter((e=>\"audioinput\"===e.kind))))}))}destroy(){super.destroy(),this.stopRecording(),this.stopMic()}}export{r as default};\n", "<script lang=\"ts\">\n\timport RecordPlugin from \"wavesurfer.js/dist/plugins/record.js\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport { createEventDispatcher } from \"svelte\";\n\n\texport let i18n: I18nFormatter;\n\texport let micDevices: MediaDeviceInfo[] = [];\n\n\tconst dispatch = createEventDispatcher<{\n\t\terror: string;\n\t}>();\n\n\t$: try {\n\t\tlet tempDevices: MediaDeviceInfo[] = [];\n\t\tRecordPlugin.getAvailableAudioDevices().then(\n\t\t\t(devices: MediaDeviceInfo[]) => {\n\t\t\t\tmicDevices = devices;\n\t\t\t\tdevices.forEach((device) => {\n\t\t\t\t\tif (device.deviceId) {\n\t\t\t\t\t\ttempDevices.push(device);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tmicDevices = tempDevices;\n\t\t\t}\n\t\t);\n\t} catch (err) {\n\t\tif (err instanceof DOMException && err.name == \"NotAllowedError\") {\n\t\t\tdispatch(\"error\", i18n(\"audio.allow_recording_access\"));\n\t\t}\n\t\tthrow err;\n\t}\n</script>\n\n<select\n\tclass=\"mic-select\"\n\taria-label=\"Select input device\"\n\tdisabled={micDevices.length === 0}\n>\n\t{#if micDevices.length === 0}\n\t\t<option value=\"\">{i18n(\"audio.no_microphone\")}</option>\n\t{:else}\n\t\t{#each micDevices as micDevice}\n\t\t\t<option value={micDevice.deviceId}>{micDevice.label}</option>\n\t\t{/each}\n\t{/if}\n</select>\n\n<style>\n\t.mic-select {\n\t\theight: var(--size-8);\n\t\tbackground: var(--block-background-fill);\n\t\tpadding: 0px var(--spacing-xxl);\n\t\tborder-radius: var(--radius-full);\n\t\tfont-size: var(--text-md);\n\t\tborder: 1px solid var(--neutral-400);\n\t\tgap: var(--size-1);\n\t}\n\n\tselect {\n\t\ttext-overflow: ellipsis;\n\t\tmax-width: var(--size-40);\n\t}\n\n\t@media (max-width: 375px) {\n\t\tselect {\n\t\t\twidth: 100%;\n\t\t}\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { Pause } from \"@gradio/icons\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport RecordPlugin from \"wavesurfer.js/dist/plugins/record.js\";\n\timport DeviceSelect from \"./DeviceSelect.svelte\";\n\n\texport let record: RecordPlugin;\n\texport let i18n: I18nFormatter;\n\n\tlet micDevices: MediaDeviceInfo[] = [];\n\tlet recordButton: HTMLButtonElement;\n\tlet pauseButton: HTMLButtonElement;\n\tlet resumeButton: HTMLButtonElement;\n\tlet stopButton: HTMLButtonElement;\n\tlet stopButtonPaused: HTMLButtonElement;\n\n\texport let record_time: string;\n\texport let show_recording_waveform: boolean | undefined;\n\texport let timing = false;\n\n\t$: record.on(\"record-start\", () => {\n\t\trecord.startMic();\n\n\t\trecordButton.style.display = \"none\";\n\t\tstopButton.style.display = \"flex\";\n\t\tpauseButton.style.display = \"block\";\n\t});\n\n\t$: record.on(\"record-end\", () => {\n\t\tif (record.isPaused()) {\n\t\t\trecord.resumeRecording();\n\t\t\trecord.stopRecording();\n\t\t}\n\t\trecord.stopMic();\n\n\t\trecordButton.style.display = \"flex\";\n\t\tstopButton.style.display = \"none\";\n\t\tpauseButton.style.display = \"none\";\n\t\trecordButton.disabled = false;\n\t});\n\n\t$: record.on(\"record-pause\", () => {\n\t\tpauseButton.style.display = \"none\";\n\t\tresumeButton.style.display = \"block\";\n\t\tstopButton.style.display = \"none\";\n\t\tstopButtonPaused.style.display = \"flex\";\n\t});\n\n\t$: record.on(\"record-resume\", () => {\n\t\tpauseButton.style.display = \"block\";\n\t\tresumeButton.style.display = \"none\";\n\t\trecordButton.style.display = \"none\";\n\t\tstopButton.style.display = \"flex\";\n\t\tstopButtonPaused.style.display = \"none\";\n\t});\n</script>\n\n<div class=\"controls\">\n\t<div class=\"wrapper\">\n\t\t<button\n\t\t\tbind:this={recordButton}\n\t\t\tclass=\"record record-button\"\n\t\t\ton:click={() => record.startRecording()}>{i18n(\"audio.record\")}</button\n\t\t>\n\n\t\t<button\n\t\t\tbind:this={stopButton}\n\t\t\tclass=\"stop-button {record.isPaused() ? 'stop-button-paused' : ''}\"\n\t\t\ton:click={() => {\n\t\t\t\tif (record.isPaused()) {\n\t\t\t\t\trecord.resumeRecording();\n\t\t\t\t\trecord.stopRecording();\n\t\t\t\t}\n\n\t\t\t\trecord.stopRecording();\n\t\t\t}}>{i18n(\"audio.stop\")}</button\n\t\t>\n\n\t\t<button\n\t\t\tbind:this={stopButtonPaused}\n\t\t\tid=\"stop-paused\"\n\t\t\tclass=\"stop-button-paused\"\n\t\t\ton:click={() => {\n\t\t\t\tif (record.isPaused()) {\n\t\t\t\t\trecord.resumeRecording();\n\t\t\t\t\trecord.stopRecording();\n\t\t\t\t}\n\n\t\t\t\trecord.stopRecording();\n\t\t\t}}>{i18n(\"audio.stop\")}</button\n\t\t>\n\n\t\t<button\n\t\t\taria-label=\"pause\"\n\t\t\tbind:this={pauseButton}\n\t\t\tclass=\"pause-button\"\n\t\t\ton:click={() => record.pauseRecording()}><Pause /></button\n\t\t>\n\t\t<button\n\t\t\tbind:this={resumeButton}\n\t\t\tclass=\"resume-button\"\n\t\t\ton:click={() => record.resumeRecording()}>{i18n(\"audio.resume\")}</button\n\t\t>\n\t\t{#if timing && !show_recording_waveform}\n\t\t\t<time class=\"duration-button duration\">{record_time}</time>\n\t\t{/if}\n\t</div>\n\t<DeviceSelect bind:micDevices {i18n} />\n</div>\n\n<style>\n\t.controls {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tflex-wrap: wrap;\n\t}\n\n\t.wrapper {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tflex-wrap: wrap;\n\t}\n\n\t.record {\n\t\tmargin-right: var(--spacing-md);\n\t}\n\n\t.stop-button-paused {\n\t\tdisplay: none;\n\t\theight: var(--size-8);\n\t\twidth: var(--size-20);\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder-radius: var(--radius-3xl);\n\t\talign-items: center;\n\t\tborder: 1px solid var(--neutral-400);\n\t\tmargin: var(--size-1) var(--size-1) 0 0;\n\t}\n\n\t.stop-button-paused::before {\n\t\tcontent: \"\";\n\t\theight: var(--size-4);\n\t\twidth: var(--size-4);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--primary-600);\n\t\tmargin: 0 var(--spacing-xl);\n\t}\n\t.stop-button::before {\n\t\tcontent: \"\";\n\t\theight: var(--size-4);\n\t\twidth: var(--size-4);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--primary-600);\n\t\tmargin: 0 var(--spacing-xl);\n\t\tanimation: scaling 1800ms infinite;\n\t}\n\n\t.stop-button {\n\t\tdisplay: none;\n\t\theight: var(--size-8);\n\t\twidth: var(--size-20);\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder-radius: var(--radius-3xl);\n\t\talign-items: center;\n\t\tborder: 1px solid var(--primary-600);\n\t\tmargin: var(--size-1) var(--size-1) 0 0;\n\t}\n\n\t.record-button::before {\n\t\tcontent: \"\";\n\t\theight: var(--size-4);\n\t\twidth: var(--size-4);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--primary-600);\n\t\tmargin: 0 var(--spacing-xl);\n\t}\n\n\t.record-button {\n\t\theight: var(--size-8);\n\t\twidth: var(--size-24);\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder-radius: var(--radius-3xl);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tborder: 1px solid var(--neutral-400);\n\t}\n\n\t.stop-button:disabled {\n\t\tcursor: not-allowed;\n\t}\n\n\t.record-button:disabled {\n\t\tcursor: not-allowed;\n\t\topacity: 0.5;\n\t}\n\n\t@keyframes scaling {\n\t\t0% {\n\t\t\tbackground-color: var(--primary-600);\n\t\t\tscale: 1;\n\t\t}\n\t\t50% {\n\t\t\tbackground-color: var(--primary-600);\n\t\t\tscale: 1.2;\n\t\t}\n\t\t100% {\n\t\t\tbackground-color: var(--primary-600);\n\t\t\tscale: 1;\n\t\t}\n\t}\n\n\t.pause-button {\n\t\tdisplay: none;\n\t\theight: var(--size-8);\n\t\twidth: var(--size-20);\n\t\tborder: 1px solid var(--neutral-400);\n\t\tborder-radius: var(--radius-3xl);\n\t\tpadding: var(--spacing-md);\n\t\tmargin: var(--size-1) var(--size-1) 0 0;\n\t}\n\n\t.resume-button {\n\t\tdisplay: none;\n\t\theight: var(--size-8);\n\t\twidth: var(--size-20);\n\t\tborder: 1px solid var(--neutral-400);\n\t\tborder-radius: var(--radius-3xl);\n\t\tpadding: var(--spacing-xl);\n\t\tline-height: 1px;\n\t\tfont-size: var(--text-md);\n\t\tmargin: var(--size-1) var(--size-1) 0 0;\n\t}\n\n\t.duration {\n\t\tdisplay: flex;\n\t\theight: var(--size-8);\n\t\twidth: var(--size-20);\n\t\tborder: 1px solid var(--neutral-400);\n\t\tborder-radius: var(--radius-3xl);\n\t\tpadding: var(--spacing-md);\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin: var(--size-1) var(--size-1) 0 0;\n\t}\n\n\t:global(::part(region)) {\n\t\tborder-radius: var(--radius-md);\n\t\theight: 98% !important;\n\t\tborder: 1px solid var(--trim-region-color);\n\t\tbackground-color: unset;\n\t\tborder-width: 1px 3px;\n\t}\n\n\t:global(::part(region))::after {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: var(--trim-region-color);\n\t\topacity: 0.2;\n\t\tborder-radius: var(--radius-md);\n\t}\n\n\t:global(::part(region-handle)) {\n\t\twidth: 5px !important;\n\t\tborder: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport WaveSurfer from \"wavesurfer.js\";\n\timport { skip_audio, process_audio } from \"../shared/utils\";\n\timport WSRecord from \"wavesurfer.js/dist/plugins/record.js\";\n\timport WaveformControls from \"../shared/WaveformControls.svelte\";\n\timport WaveformRecordControls from \"../shared/WaveformRecordControls.svelte\";\n\timport RecordPlugin from \"wavesurfer.js/dist/plugins/record.js\";\n\timport type { WaveformOptions } from \"../shared/types\";\n\timport { format_time } from \"@gradio/utils\";\n\n\texport let mode: string;\n\texport let i18n: I18nFormatter;\n\texport let dispatch_blob: (\n\t\tblobs: Uint8Array[] | Blob[],\n\t\tevent: \"stream\" | \"change\" | \"stop_recording\"\n\t) => Promise<void> | undefined;\n\texport let waveform_settings: Record<string, any>;\n\texport let waveform_options: WaveformOptions = {\n\t\tshow_recording_waveform: true\n\t};\n\texport let handle_reset_value: () => void;\n\texport let editable = true;\n\n\tlet micWaveform: WaveSurfer;\n\tlet recordingWaveform: WaveSurfer;\n\tlet playing = false;\n\n\tlet recordingContainer: HTMLDivElement;\n\tlet microphoneContainer: HTMLDivElement;\n\n\tlet record: WSRecord;\n\tlet recordedAudio: string | null = null;\n\n\t// timestamps\n\tlet timeRef: HTMLTimeElement;\n\tlet durationRef: HTMLTimeElement;\n\tlet audio_duration: number;\n\tlet seconds = 0;\n\tlet interval: NodeJS.Timeout;\n\tlet timing = false;\n\t// trimming\n\tlet trimDuration = 0;\n\n\tconst start_interval = (): void => {\n\t\tclearInterval(interval);\n\t\tinterval = setInterval(() => {\n\t\t\tseconds++;\n\t\t}, 1000);\n\t};\n\n\tconst dispatch = createEventDispatcher<{\n\t\tstart_recording: undefined;\n\t\tpause_recording: undefined;\n\t\tstop_recording: undefined;\n\t\tstop: undefined;\n\t\tplay: undefined;\n\t\tpause: undefined;\n\t\tend: undefined;\n\t\tedit: undefined;\n\t}>();\n\n\t$: record?.on(\"record-start\", () => {\n\t\tstart_interval();\n\t\ttiming = true;\n\t\tdispatch(\"start_recording\");\n\t\tif (waveform_options.show_recording_waveform) {\n\t\t\tlet waveformCanvas = microphoneContainer;\n\t\t\tif (waveformCanvas) waveformCanvas.style.display = \"block\";\n\t\t}\n\t});\n\n\t$: record?.on(\"record-end\", async (blob) => {\n\t\tseconds = 0;\n\t\ttiming = false;\n\t\tclearInterval(interval);\n\t\ttry {\n\t\t\tconst array_buffer = await blob.arrayBuffer();\n\t\t\tconst context = new AudioContext({\n\t\t\t\tsampleRate: waveform_settings.sampleRate\n\t\t\t});\n\t\t\tconst audio_buffer = await context.decodeAudioData(array_buffer);\n\n\t\t\tif (audio_buffer)\n\t\t\t\tawait process_audio(audio_buffer).then(async (audio: Uint8Array) => {\n\t\t\t\t\tawait dispatch_blob([audio], \"change\");\n\t\t\t\t\tawait dispatch_blob([audio], \"stop_recording\");\n\t\t\t\t});\n\t\t} catch (e) {\n\t\t\tconsole.error(e);\n\t\t}\n\t});\n\n\t$: record?.on(\"record-pause\", () => {\n\t\tdispatch(\"pause_recording\");\n\t\tclearInterval(interval);\n\t});\n\n\t$: record?.on(\"record-resume\", () => {\n\t\tstart_interval();\n\t});\n\n\t$: recordingWaveform?.on(\"decode\", (duration: any) => {\n\t\taudio_duration = duration;\n\t\tdurationRef && (durationRef.textContent = format_time(duration));\n\t});\n\n\t$: recordingWaveform?.on(\n\t\t\"timeupdate\",\n\t\t(currentTime: any) =>\n\t\t\ttimeRef && (timeRef.textContent = format_time(currentTime))\n\t);\n\n\t$: recordingWaveform?.on(\"pause\", () => {\n\t\tdispatch(\"pause\");\n\t\tplaying = false;\n\t});\n\n\t$: recordingWaveform?.on(\"play\", () => {\n\t\tdispatch(\"play\");\n\t\tplaying = true;\n\t});\n\n\t$: recordingWaveform?.on(\"finish\", () => {\n\t\tdispatch(\"stop\");\n\t\tplaying = false;\n\t});\n\n\tconst create_mic_waveform = (): void => {\n\t\tif (microphoneContainer) microphoneContainer.innerHTML = \"\";\n\t\tif (micWaveform !== undefined) micWaveform.destroy();\n\t\tif (!microphoneContainer) return;\n\t\tmicWaveform = WaveSurfer.create({\n\t\t\t...waveform_settings,\n\t\t\tnormalize: false,\n\t\t\tcontainer: microphoneContainer\n\t\t});\n\n\t\trecord = micWaveform.registerPlugin(RecordPlugin.create());\n\t\trecord.startMic();\n\t};\n\n\tconst create_recording_waveform = (): void => {\n\t\tlet recording = recordingContainer;\n\t\tif (!recordedAudio || !recording) return;\n\t\trecordingWaveform = WaveSurfer.create({\n\t\t\tcontainer: recording,\n\t\t\turl: recordedAudio,\n\t\t\t...waveform_settings\n\t\t});\n\t};\n\n\t$: record?.on(\"record-end\", (blob) => {\n\t\trecordedAudio = URL.createObjectURL(blob);\n\n\t\tconst microphone = microphoneContainer;\n\t\tconst recording = recordingContainer;\n\n\t\tif (microphone) microphone.style.display = \"none\";\n\t\tif (recording && recordedAudio) {\n\t\t\trecording.innerHTML = \"\";\n\t\t\tcreate_recording_waveform();\n\t\t}\n\t});\n\n\tconst handle_trim_audio = async (\n\t\tstart: number,\n\t\tend: number\n\t): Promise<void> => {\n\t\tmode = \"edit\";\n\t\tconst decodedData = recordingWaveform.getDecodedData();\n\t\tif (decodedData)\n\t\t\tawait process_audio(decodedData, start, end).then(\n\t\t\t\tasync (trimmedAudio: Uint8Array) => {\n\t\t\t\t\tawait dispatch_blob([trimmedAudio], \"change\");\n\t\t\t\t\tawait dispatch_blob([trimmedAudio], \"stop_recording\");\n\t\t\t\t\trecordingWaveform.destroy();\n\t\t\t\t\tcreate_recording_waveform();\n\t\t\t\t}\n\t\t\t);\n\t\tdispatch(\"edit\");\n\t};\n\n\tonMount(() => {\n\t\tcreate_mic_waveform();\n\n\t\twindow.addEventListener(\"keydown\", (e) => {\n\t\t\tif (e.key === \"ArrowRight\") {\n\t\t\t\tskip_audio(recordingWaveform, 0.1);\n\t\t\t} else if (e.key === \"ArrowLeft\") {\n\t\t\t\tskip_audio(recordingWaveform, -0.1);\n\t\t\t}\n\t\t});\n\t});\n</script>\n\n<div class=\"component-wrapper\">\n\t<div\n\t\tclass=\"microphone\"\n\t\tbind:this={microphoneContainer}\n\t\tdata-testid=\"microphone-waveform\"\n\t/>\n\t<div bind:this={recordingContainer} data-testid=\"recording-waveform\" />\n\n\t{#if (timing || recordedAudio) && waveform_options.show_recording_waveform}\n\t\t<div class=\"timestamps\">\n\t\t\t<time bind:this={timeRef} class=\"time\">0:00</time>\n\t\t\t<div>\n\t\t\t\t{#if mode === \"edit\" && trimDuration > 0}\n\t\t\t\t\t<time class=\"trim-duration\">{format_time(trimDuration)}</time>\n\t\t\t\t{/if}\n\t\t\t\t{#if timing}\n\t\t\t\t\t<time class=\"duration\">{format_time(seconds)}</time>\n\t\t\t\t{:else}\n\t\t\t\t\t<time bind:this={durationRef} class=\"duration\">0:00</time>\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t</div>\n\t{/if}\n\n\t{#if microphoneContainer && !recordedAudio}\n\t\t<WaveformRecordControls\n\t\t\tbind:record\n\t\t\t{i18n}\n\t\t\t{timing}\n\t\t\tshow_recording_waveform={waveform_options.show_recording_waveform}\n\t\t\trecord_time={format_time(seconds)}\n\t\t/>\n\t{/if}\n\n\t{#if recordingWaveform && recordedAudio}\n\t\t<WaveformControls\n\t\t\tbind:waveform={recordingWaveform}\n\t\t\tcontainer={recordingContainer}\n\t\t\t{playing}\n\t\t\t{audio_duration}\n\t\t\t{i18n}\n\t\t\t{editable}\n\t\t\tinteractive={true}\n\t\t\t{handle_trim_audio}\n\t\t\tbind:trimDuration\n\t\t\tbind:mode\n\t\t\tshow_redo\n\t\t\t{handle_reset_value}\n\t\t\t{waveform_options}\n\t\t/>\n\t{/if}\n</div>\n\n<style>\n\t.microphone {\n\t\twidth: 100%;\n\t\tdisplay: none;\n\t}\n\n\t.component-wrapper {\n\t\tpadding: var(--size-3);\n\t\twidth: 100%;\n\t}\n\n\t.timestamps {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\twidth: 100%;\n\t\tpadding: var(--size-1) 0;\n\t\tmargin: var(--spacing-md) 0;\n\t}\n\n\t.time {\n\t\tcolor: var(--neutral-400);\n\t}\n\n\t.duration {\n\t\tcolor: var(--neutral-400);\n\t}\n\n\t.trim-duration {\n\t\tcolor: var(--color-accent);\n\t\tmargin-right: var(--spacing-sm);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport WaveSurfer from \"wavesurfer.js\";\n\timport RecordPlugin from \"wavesurfer.js/dist/plugins/record.js\";\n\timport type { WaveformOptions } from \"../shared/types\";\n\timport DeviceSelect from \"../shared/DeviceSelect.svelte\";\n\n\texport let recording = false;\n\texport let paused_recording = false;\n\texport let stop: () => void;\n\texport let record: () => void;\n\texport let i18n: I18nFormatter;\n\texport let waveform_settings: Record<string, any>;\n\texport let waveform_options: WaveformOptions = {\n\t\tshow_recording_waveform: true\n\t};\n\n\tlet micWaveform: WaveSurfer;\n\tlet waveformRecord: RecordPlugin;\n\n\tlet microphoneContainer: HTMLDivElement;\n\n\tlet micDevices: MediaDeviceInfo[] = [];\n\n\tonMount(() => {\n\t\tcreate_mic_waveform();\n\t});\n\n\tconst create_mic_waveform = (): void => {\n\t\tif (micWaveform !== undefined) micWaveform.destroy();\n\t\tif (!microphoneContainer) return;\n\t\tmicWaveform = WaveSurfer.create({\n\t\t\t...waveform_settings,\n\t\t\theight: 100,\n\t\t\tcontainer: microphoneContainer\n\t\t});\n\n\t\twaveformRecord = micWaveform.registerPlugin(RecordPlugin.create());\n\t};\n</script>\n\n<div class=\"mic-wrap\">\n\t{#if waveform_options.show_recording_waveform}\n\t\t<div\n\t\t\tbind:this={microphoneContainer}\n\t\t\tstyle:display={recording ? \"block\" : \"none\"}\n\t\t/>\n\t{/if}\n\t<div class=\"controls\">\n\t\t{#if recording}\n\t\t\t<button\n\t\t\t\tclass={paused_recording ? \"stop-button-paused\" : \"stop-button\"}\n\t\t\t\ton:click={() => {\n\t\t\t\t\twaveformRecord?.stopMic();\n\t\t\t\t\tstop();\n\t\t\t\t}}\n\t\t\t>\n\t\t\t\t<span class=\"record-icon\">\n\t\t\t\t\t<span class=\"pinger\" />\n\t\t\t\t\t<span class=\"dot\" />\n\t\t\t\t</span>\n\t\t\t\t{paused_recording ? i18n(\"audio.pause\") : i18n(\"audio.stop\")}\n\t\t\t</button>\n\t\t{:else}\n\t\t\t<button\n\t\t\t\tclass=\"record-button\"\n\t\t\t\ton:click={() => {\n\t\t\t\t\twaveformRecord?.startMic();\n\t\t\t\t\trecord();\n\t\t\t\t}}\n\t\t\t>\n\t\t\t\t<span class=\"record-icon\">\n\t\t\t\t\t<span class=\"dot\" />\n\t\t\t\t</span>\n\t\t\t\t{i18n(\"audio.record\")}\n\t\t\t</button>\n\t\t{/if}\n\n\t\t<DeviceSelect bind:micDevices {i18n} />\n\t</div>\n</div>\n\n<style>\n\t.controls {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tflex-wrap: wrap;\n\t}\n\n\t.mic-wrap {\n\t\tdisplay: block;\n\t\talign-items: center;\n\t\tmargin: var(--spacing-xl);\n\t}\n\n\t.stop-button-paused {\n\t\tdisplay: none;\n\t\theight: var(--size-8);\n\t\twidth: var(--size-20);\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder-radius: var(--radius-3xl);\n\t\talign-items: center;\n\t\tborder: 1px solid var(--neutral-400);\n\t\tmargin-right: 5px;\n\t}\n\n\t.stop-button-paused::before {\n\t\tcontent: \"\";\n\t\theight: var(--size-4);\n\t\twidth: var(--size-4);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--primary-600);\n\t\tmargin: 0 var(--spacing-xl);\n\t}\n\n\t.stop-button::before {\n\t\tcontent: \"\";\n\t\theight: var(--size-4);\n\t\twidth: var(--size-4);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--primary-600);\n\t\tmargin: 0 var(--spacing-xl);\n\t\tanimation: scaling 1800ms infinite;\n\t}\n\n\t.stop-button {\n\t\theight: var(--size-8);\n\t\twidth: var(--size-20);\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder-radius: var(--radius-3xl);\n\t\talign-items: center;\n\t\tborder: 1px solid var(--primary-600);\n\t\tmargin-right: 5px;\n\t\tdisplay: flex;\n\t}\n\n\t.record-button::before {\n\t\tcontent: \"\";\n\t\theight: var(--size-4);\n\t\twidth: var(--size-4);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--primary-600);\n\t\tmargin: 0 var(--spacing-xl);\n\t}\n\n\t.record-button {\n\t\theight: var(--size-8);\n\t\twidth: var(--size-24);\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder-radius: var(--radius-3xl);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tborder: 1px solid var(--neutral-400);\n\t}\n\n\t@keyframes scaling {\n\t\t0% {\n\t\t\tbackground-color: var(--primary-600);\n\t\t\tscale: 1;\n\t\t}\n\t\t50% {\n\t\t\tbackground-color: var(--primary-600);\n\t\t\tscale: 1.2;\n\t\t}\n\t\t100% {\n\t\t\tbackground-color: var(--primary-600);\n\t\t\tscale: 1;\n\t\t}\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { get<PERSON>ontex<PERSON>, on<PERSON><PERSON><PERSON>, createEventDispatcher } from \"svelte\";\n\timport { Upload, ModifyUpload } from \"@gradio/upload\";\n\timport {\n\t\tupload,\n\t\tprepare_files,\n\t\ttype FileData,\n\t\ttype upload_files\n\t} from \"@gradio/client\";\n\timport { BlockLabel } from \"@gradio/atoms\";\n\timport { Music } from \"@gradio/icons\";\n\timport AudioPlayer from \"../player/AudioPlayer.svelte\";\n\n\timport type { IBlobEvent, IMediaRecorder } from \"extendable-media-recorder\";\n\timport type { I18nFormatter } from \"js/app/src/gradio_helper\";\n\timport AudioRecorder from \"../recorder/AudioRecorder.svelte\";\n\timport StreamAudio from \"../streaming/StreamAudio.svelte\";\n\timport { SelectSource } from \"@gradio/atoms\";\n\timport type { WaveformOptions } from \"../shared/types\";\n\n\texport let value: null | FileData = null;\n\texport let label: string;\n\texport let root: string;\n\texport let show_label = true;\n\texport let show_download_button = false;\n\texport let sources:\n\t\t| [\"microphone\"]\n\t\t| [\"upload\"]\n\t\t| [\"microphone\", \"upload\"]\n\t\t| [\"upload\", \"microphone\"] = [\"microphone\", \"upload\"];\n\texport let pending = false;\n\texport let streaming = false;\n\texport let i18n: I18nFormatter;\n\texport let waveform_settings: Record<string, any>;\n\texport let trim_region_settings = {};\n\texport let waveform_options: WaveformOptions = {};\n\texport let dragging: boolean;\n\texport let active_source: \"microphone\" | \"upload\";\n\texport let handle_reset_value: () => void = () => {};\n\texport let editable = true;\n\n\t// Needed for wasm support\n\tconst upload_fn = getContext<typeof upload_files>(\"upload_files\");\n\n\t$: dispatch(\"drag\", dragging);\n\n\t// TODO: make use of this\n\t// export let type: \"normal\" | \"numpy\" = \"normal\";\n\tlet recording = false;\n\tlet recorder: IMediaRecorder;\n\tlet mode = \"\";\n\tlet header: Uint8Array | undefined = undefined;\n\tlet pending_stream: Uint8Array[] = [];\n\tlet submit_pending_stream_on_pending_end = false;\n\tlet inited = false;\n\n\tconst STREAM_TIMESLICE = 500;\n\tconst NUM_HEADER_BYTES = 44;\n\tlet audio_chunks: Blob[] = [];\n\tlet module_promises: [\n\t\tPromise<typeof import(\"extendable-media-recorder\")>,\n\t\tPromise<typeof import(\"extendable-media-recorder-wav-encoder\")>\n\t];\n\n\tfunction get_modules(): void {\n\t\tmodule_promises = [\n\t\t\timport(\"extendable-media-recorder\"),\n\t\t\timport(\"extendable-media-recorder-wav-encoder\")\n\t\t];\n\t}\n\n\tif (streaming) {\n\t\tget_modules();\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: FileData | null;\n\t\tstream: FileData;\n\t\tedit: never;\n\t\tplay: never;\n\t\tpause: never;\n\t\tstop: never;\n\t\tend: never;\n\t\tdrag: boolean;\n\t\terror: string;\n\t\tupload: FileData;\n\t\tclear: undefined;\n\t\tstart_recording: undefined;\n\t\tpause_recording: undefined;\n\t\tstop_recording: undefined;\n\t}>();\n\n\tconst dispatch_blob = async (\n\t\tblobs: Uint8Array[] | Blob[],\n\t\tevent: \"stream\" | \"change\" | \"stop_recording\"\n\t): Promise<void> => {\n\t\tlet _audio_blob = new File(blobs, \"audio.wav\");\n\t\tconst val = await prepare_files([_audio_blob], event === \"stream\");\n\t\tvalue = (\n\t\t\t(await upload(val, root, undefined, upload_fn))?.filter(\n\t\t\t\tBoolean\n\t\t\t) as FileData[]\n\t\t)[0];\n\n\t\tdispatch(event, value);\n\t};\n\n\tonDestroy(() => {\n\t\tif (streaming && recorder && recorder.state !== \"inactive\") {\n\t\t\trecorder.stop();\n\t\t}\n\t});\n\n\tasync function prepare_audio(): Promise<void> {\n\t\tlet stream: MediaStream | null;\n\n\t\ttry {\n\t\t\tstream = await navigator.mediaDevices.getUserMedia({ audio: true });\n\t\t} catch (err) {\n\t\t\tif (!navigator.mediaDevices) {\n\t\t\t\tdispatch(\"error\", i18n(\"audio.no_device_support\"));\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (err instanceof DOMException && err.name == \"NotAllowedError\") {\n\t\t\t\tdispatch(\"error\", i18n(\"audio.allow_recording_access\"));\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthrow err;\n\t\t}\n\t\tif (stream == null) return;\n\t\tif (streaming) {\n\t\t\tconst [{ MediaRecorder, register }, { connect }] = await Promise.all(\n\t\t\t\tmodule_promises\n\t\t\t);\n\t\t\tawait register(await connect());\n\t\t\trecorder = new MediaRecorder(stream, { mimeType: \"audio/wav\" });\n\t\t\trecorder.addEventListener(\"dataavailable\", handle_chunk);\n\t\t} else {\n\t\t\trecorder = new MediaRecorder(stream);\n\t\t\trecorder.addEventListener(\"dataavailable\", (event) => {\n\t\t\t\taudio_chunks.push(event.data);\n\t\t\t});\n\t\t\trecorder.addEventListener(\"stop\", async () => {\n\t\t\t\trecording = false;\n\t\t\t\tawait dispatch_blob(audio_chunks, \"change\");\n\t\t\t\tawait dispatch_blob(audio_chunks, \"stop_recording\");\n\t\t\t\taudio_chunks = [];\n\t\t\t});\n\t\t}\n\t\tinited = true;\n\t}\n\n\tasync function handle_chunk(event: IBlobEvent): Promise<void> {\n\t\tlet buffer = await event.data.arrayBuffer();\n\t\tlet payload = new Uint8Array(buffer);\n\t\tif (!header) {\n\t\t\theader = new Uint8Array(buffer.slice(0, NUM_HEADER_BYTES));\n\t\t\tpayload = new Uint8Array(buffer.slice(NUM_HEADER_BYTES));\n\t\t}\n\t\tif (pending) {\n\t\t\tpending_stream.push(payload);\n\t\t} else {\n\t\t\tlet blobParts = [header].concat(pending_stream, [payload]);\n\t\t\tdispatch_blob(blobParts, \"stream\");\n\t\t\tpending_stream = [];\n\t\t}\n\t}\n\n\t$: if (submit_pending_stream_on_pending_end && pending === false) {\n\t\tsubmit_pending_stream_on_pending_end = false;\n\t\tif (header && pending_stream) {\n\t\t\tlet blobParts: Uint8Array[] = [header].concat(pending_stream);\n\t\t\tpending_stream = [];\n\t\t\tdispatch_blob(blobParts, \"stream\");\n\t\t}\n\t}\n\n\tasync function record(): Promise<void> {\n\t\trecording = true;\n\t\tdispatch(\"start_recording\");\n\t\tif (!inited) await prepare_audio();\n\t\theader = undefined;\n\t\tif (streaming) {\n\t\t\trecorder.start(STREAM_TIMESLICE);\n\t\t}\n\t}\n\n\tfunction clear(): void {\n\t\tdispatch(\"change\", null);\n\t\tdispatch(\"clear\");\n\t\tmode = \"\";\n\t\tvalue = null;\n\t}\n\n\tfunction handle_load({ detail }: { detail: FileData }): void {\n\t\tvalue = detail;\n\t\tdispatch(\"change\", detail);\n\t\tdispatch(\"upload\", detail);\n\t}\n\n\tfunction stop(): void {\n\t\trecording = false;\n\n\t\tif (streaming) {\n\t\t\tdispatch(\"stop_recording\");\n\t\t\trecorder.stop();\n\t\t\tif (pending) {\n\t\t\t\tsubmit_pending_stream_on_pending_end = true;\n\t\t\t}\n\t\t\tdispatch_blob(audio_chunks, \"stop_recording\");\n\t\t\tdispatch(\"clear\");\n\t\t\tmode = \"\";\n\t\t}\n\t}\n</script>\n\n<BlockLabel\n\t{show_label}\n\tIcon={Music}\n\tfloat={active_source === \"upload\" && value === null}\n\tlabel={label || i18n(\"audio.audio\")}\n/>\n<div class=\"audio-container\">\n\t{#if value === null || streaming}\n\t\t{#if active_source === \"microphone\"}\n\t\t\t<ModifyUpload {i18n} on:clear={clear} absolute={true} />\n\t\t\t{#if streaming}\n\t\t\t\t<StreamAudio\n\t\t\t\t\t{record}\n\t\t\t\t\t{recording}\n\t\t\t\t\t{stop}\n\t\t\t\t\t{i18n}\n\t\t\t\t\t{waveform_settings}\n\t\t\t\t\t{waveform_options}\n\t\t\t\t/>\n\t\t\t{:else}\n\t\t\t\t<AudioRecorder\n\t\t\t\t\tbind:mode\n\t\t\t\t\t{i18n}\n\t\t\t\t\t{editable}\n\t\t\t\t\t{dispatch_blob}\n\t\t\t\t\t{waveform_settings}\n\t\t\t\t\t{waveform_options}\n\t\t\t\t\t{handle_reset_value}\n\t\t\t\t\ton:start_recording\n\t\t\t\t\ton:pause_recording\n\t\t\t\t\ton:stop_recording\n\t\t\t\t/>\n\t\t\t{/if}\n\t\t{:else if active_source === \"upload\"}\n\t\t\t<!-- explicitly listed out audio mimetypes due to iOS bug not recognizing audio/* -->\n\t\t\t<Upload\n\t\t\t\tfiletype=\"audio/aac,audio/midi,audio/mpeg,audio/ogg,audio/wav,audio/x-wav,audio/opus,audio/webm,audio/flac,audio/vnd.rn-realaudio,audio/x-ms-wma,audio/x-aiff,audio/amr,audio/*\"\n\t\t\t\ton:load={handle_load}\n\t\t\t\tbind:dragging\n\t\t\t\ton:error={({ detail }) => dispatch(\"error\", detail)}\n\t\t\t\t{root}\n\t\t\t>\n\t\t\t\t<slot />\n\t\t\t</Upload>\n\t\t{/if}\n\t{:else}\n\t\t<ModifyUpload\n\t\t\t{i18n}\n\t\t\ton:clear={clear}\n\t\t\ton:edit={() => (mode = \"edit\")}\n\t\t\tdownload={show_download_button ? value.url : null}\n\t\t\tabsolute={true}\n\t\t/>\n\n\t\t<AudioPlayer\n\t\t\tbind:mode\n\t\t\t{value}\n\t\t\t{label}\n\t\t\t{i18n}\n\t\t\t{dispatch_blob}\n\t\t\t{waveform_settings}\n\t\t\t{waveform_options}\n\t\t\t{trim_region_settings}\n\t\t\t{handle_reset_value}\n\t\t\t{editable}\n\t\t\tinteractive\n\t\t\ton:stop\n\t\t\ton:play\n\t\t\ton:pause\n\t\t\ton:edit\n\t\t/>\n\t{/if}\n\t<SelectSource {sources} bind:active_source handle_clear={clear} />\n</div>\n\n<style>\n\t.audio-container {\n\t\theight: calc(var(--size-full) - var(--size-6));\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: space-between;\n\t}\n</style>\n", "<svelte:options accessors={true} />\n\n<script lang=\"ts\">\n\timport type { Gradio, ShareData } from \"@gradio/utils\";\n\n\timport type { FileData } from \"@gradio/client\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\timport StaticAudio from \"./static/StaticAudio.svelte\";\n\timport InteractiveAudio from \"./interactive/InteractiveAudio.svelte\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport { Block, UploadText } from \"@gradio/atoms\";\n\timport type { WaveformOptions } from \"./shared/types\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let interactive: boolean;\n\texport let value: null | FileData = null;\n\texport let sources:\n\t\t| [\"microphone\"]\n\t\t| [\"upload\"]\n\t\t| [\"microphone\", \"upload\"]\n\t\t| [\"upload\", \"microphone\"];\n\texport let label: string;\n\texport let root: string;\n\texport let show_label: boolean;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let autoplay = false;\n\texport let show_download_button: boolean;\n\texport let show_share_button = false;\n\texport let editable = true;\n\texport let waveform_options: WaveformOptions = {};\n\texport let pending: boolean;\n\texport let streaming: boolean;\n\texport let gradio: Gradio<{\n\t\tchange: typeof value;\n\t\tstream: typeof value;\n\t\terror: string;\n\t\twarning: string;\n\t\tedit: never;\n\t\tplay: never;\n\t\tpause: never;\n\t\tstop: never;\n\t\tend: never;\n\t\tstart_recording: never;\n\t\tpause_recording: never;\n\t\tstop_recording: never;\n\t\tupload: never;\n\t\tclear: never;\n\t\tshare: ShareData;\n\t}>;\n\n\tlet old_value: null | FileData = null;\n\n\tlet active_source: \"microphone\" | \"upload\";\n\n\tlet initial_value: null | FileData = value;\n\n\t$: if (value && initial_value === null) {\n\t\tinitial_value = value;\n\t}\n\n\tconst handle_reset_value = (): void => {\n\t\tif (initial_value === null || value === initial_value) {\n\t\t\treturn;\n\t\t}\n\n\t\tvalue = initial_value;\n\t};\n\n\t$: {\n\t\tif (JSON.stringify(value) !== JSON.stringify(old_value)) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t}\n\t}\n\n\tlet dragging: boolean;\n\n\t$: if (!active_source && sources) {\n\t\tactive_source = sources[0];\n\t}\n\n\tlet waveform_settings: Record<string, any>;\n\n\tlet color_accent = getComputedStyle(\n\t\tdocument.documentElement\n\t).getPropertyValue(\"--color-accent\");\n\n\t$: waveform_settings = {\n\t\theight: 50,\n\t\twaveColor: waveform_options.waveform_color || \"#9ca3af\",\n\t\tprogressColor: waveform_options.waveform_progress_color || color_accent,\n\t\tbarWidth: 2,\n\t\tbarGap: 3,\n\t\tcursorWidth: 2,\n\t\tcursorColor: \"#ddd5e9\",\n\t\tautoplay: autoplay,\n\t\tbarRadius: 10,\n\t\tdragToSeek: true,\n\t\tnormalize: true,\n\t\tminPxPerSec: 20,\n\t\tmediaControls: waveform_options.show_controls,\n\t\tsampleRate: waveform_options.sample_rate || 44100\n\t};\n\n\tconst trim_region_settings = {\n\t\tcolor: waveform_options.trim_region_color,\n\t\tdrag: true,\n\t\tresize: true\n\t};\n\n\tfunction set_trim_region_colour(): void {\n\t\tdocument.documentElement.style.setProperty(\n\t\t\t\"--trim-region-color\",\n\t\t\ttrim_region_settings.color || color_accent\n\t\t);\n\t}\n\n\tset_trim_region_colour();\n\n\tfunction handle_error({ detail }: CustomEvent<string>): void {\n\t\tconst [level, status] = detail.includes(\"Invalid file type\")\n\t\t\t? [\"warning\", \"complete\"]\n\t\t\t: [\"error\", \"error\"];\n\t\tloading_status = loading_status || {};\n\t\tloading_status.status = status as LoadingStatus[\"status\"];\n\t\tloading_status.message = detail;\n\t\tgradio.dispatch(level as \"error\" | \"warning\", detail);\n\t}\n</script>\n\n{#if !interactive}\n\t<Block\n\t\tvariant={\"solid\"}\n\t\tborder_mode={dragging ? \"focus\" : \"base\"}\n\t\tpadding={false}\n\t\tallow_overflow={false}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\t{visible}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t/>\n\n\t\t<StaticAudio\n\t\t\ti18n={gradio.i18n}\n\t\t\t{show_label}\n\t\t\t{show_download_button}\n\t\t\t{show_share_button}\n\t\t\t{value}\n\t\t\t{label}\n\t\t\t{waveform_settings}\n\t\t\t{waveform_options}\n\t\t\t{editable}\n\t\t\ton:share={(e) => gradio.dispatch(\"share\", e.detail)}\n\t\t\ton:error={(e) => gradio.dispatch(\"error\", e.detail)}\n\t\t\ton:play={() => gradio.dispatch(\"play\")}\n\t\t\ton:pause={() => gradio.dispatch(\"pause\")}\n\t\t\ton:stop={() => gradio.dispatch(\"stop\")}\n\t\t/>\n\t</Block>\n{:else}\n\t<Block\n\t\tvariant={value === null && active_source === \"upload\" ? \"dashed\" : \"solid\"}\n\t\tborder_mode={dragging ? \"focus\" : \"base\"}\n\t\tpadding={false}\n\t\tallow_overflow={false}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\t{visible}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t/>\n\t\t<InteractiveAudio\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{show_download_button}\n\t\t\t{value}\n\t\t\ton:change={({ detail }) => (value = detail)}\n\t\t\ton:stream={({ detail }) => {\n\t\t\t\tvalue = detail;\n\t\t\t\tgradio.dispatch(\"stream\", value);\n\t\t\t}}\n\t\t\ton:drag={({ detail }) => (dragging = detail)}\n\t\t\t{root}\n\t\t\t{sources}\n\t\t\t{active_source}\n\t\t\t{pending}\n\t\t\t{streaming}\n\t\t\t{handle_reset_value}\n\t\t\t{editable}\n\t\t\tbind:dragging\n\t\t\ton:edit={() => gradio.dispatch(\"edit\")}\n\t\t\ton:play={() => gradio.dispatch(\"play\")}\n\t\t\ton:pause={() => gradio.dispatch(\"pause\")}\n\t\t\ton:stop={() => gradio.dispatch(\"stop\")}\n\t\t\ton:start_recording={() => gradio.dispatch(\"start_recording\")}\n\t\t\ton:pause_recording={() => gradio.dispatch(\"pause_recording\")}\n\t\t\ton:stop_recording={(e) => gradio.dispatch(\"stop_recording\")}\n\t\t\ton:upload={() => gradio.dispatch(\"upload\")}\n\t\t\ton:clear={() => gradio.dispatch(\"clear\")}\n\t\t\ton:error={handle_error}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{waveform_settings}\n\t\t\t{waveform_options}\n\t\t\t{trim_region_settings}\n\t\t>\n\t\t\t<UploadText i18n={gradio.i18n} type=\"audio\" />\n\t\t</InteractiveAudio>\n\t</Block>\n{/if}\n"], "file": "assets/index-769c92f6.js"}