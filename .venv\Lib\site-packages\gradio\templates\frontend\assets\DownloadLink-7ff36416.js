import{g as K}from"./Index-26cfc80a.js";import{s as P,g as Q}from"./file-url-bef2dc1b.js";const{SvelteComponent:X,append:Y,attr:b,detach:Z,init:$,insert:x,noop:H,safe_not_equal:ee,svg_element:B}=window.__gradio__svelte__internal;function te(s){let e,t;return{c(){e=B("svg"),t=B("path"),b(t,"fill","currentColor"),b(t,"d","M26 24v4H6v-4H4v4a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2v-4zm0-10l-1.41-1.41L17 20.17V2h-2v18.17l-7.59-7.58L6 14l10 10l10-10z"),b(e,"xmlns","http://www.w3.org/2000/svg"),b(e,"width","100%"),b(e,"height","100%"),b(e,"viewBox","0 0 32 32")},m(n,r){x(n,e,r),Y(e,t)},p:H,i:H,o:H,d(n){n&&Z(e)}}}class me extends X{constructor(e){super(),$(this,e,null,te,ee,{})}}const{SvelteComponent:le,assign:L,check_outros:O,compute_rest_props:N,create_slot:S,detach:R,element:V,empty:F,exclude_internal_props:ne,get_all_dirty_from_scope:W,get_slot_changes:j,get_spread_update:G,group_outros:M,init:oe,insert:U,listen:T,prevent_default:re,safe_not_equal:ie,set_attributes:E,transition_in:m,transition_out:w,update_slot_base:z}=window.__gradio__svelte__internal,{createEventDispatcher:ae}=window.__gradio__svelte__internal;function se(s){let e,t,n,r,u;const a=s[8].default,f=S(a,s,s[7],null);let i=[{href:s[0]},{target:t=typeof window<"u"&&window.__is_colab__?"_blank":null},{rel:"noopener noreferrer"},{download:s[1]},s[6]],l={};for(let o=0;o<i.length;o+=1)l=L(l,i[o]);return{c(){e=V("a"),f&&f.c(),E(e,l)},m(o,_){U(o,e,_),f&&f.m(e,null),n=!0,r||(u=T(e,"click",s[3].bind(null,"click")),r=!0)},p(o,_){f&&f.p&&(!n||_&128)&&z(f,a,o,o[7],n?j(a,o[7],_,null):W(o[7]),null),E(e,l=G(i,[(!n||_&1)&&{href:o[0]},{target:t},{rel:"noopener noreferrer"},(!n||_&2)&&{download:o[1]},_&64&&o[6]]))},i(o){n||(m(f,o),n=!0)},o(o){w(f,o),n=!1},d(o){o&&R(e),f&&f.d(o),r=!1,u()}}}function fe(s){let e,t,n,r;const u=[_e,ue],a=[];function f(i,l){return i[2]?0:1}return e=f(s),t=a[e]=u[e](s),{c(){t.c(),n=F()},m(i,l){a[e].m(i,l),U(i,n,l),r=!0},p(i,l){let o=e;e=f(i),e===o?a[e].p(i,l):(M(),w(a[o],1,1,()=>{a[o]=null}),O(),t=a[e],t?t.p(i,l):(t=a[e]=u[e](i),t.c()),m(t,1),t.m(n.parentNode,n))},i(i){r||(m(t),r=!0)},o(i){w(t),r=!1},d(i){i&&R(n),a[e].d(i)}}}function ue(s){let e,t,n,r;const u=s[8].default,a=S(u,s,s[7],null);let f=[s[6],{href:s[0]}],i={};for(let l=0;l<f.length;l+=1)i=L(i,f[l]);return{c(){e=V("a"),a&&a.c(),E(e,i)},m(l,o){U(l,e,o),a&&a.m(e,null),t=!0,n||(r=T(e,"click",re(s[5])),n=!0)},p(l,o){a&&a.p&&(!t||o&128)&&z(a,u,l,l[7],t?j(u,l[7],o,null):W(l[7]),null),E(e,i=G(f,[o&64&&l[6],(!t||o&1)&&{href:l[0]}]))},i(l){t||(m(a,l),t=!0)},o(l){w(a,l),t=!1},d(l){l&&R(e),a&&a.d(l),n=!1,r()}}}function _e(s){let e;const t=s[8].default,n=S(t,s,s[7],null);return{c(){n&&n.c()},m(r,u){n&&n.m(r,u),e=!0},p(r,u){n&&n.p&&(!e||u&128)&&z(n,t,r,r[7],e?j(t,r[7],u,null):W(r[7]),null)},i(r){e||(m(n,r),e=!0)},o(r){w(n,r),e=!1},d(r){n&&n.d(r)}}}function ce(s){let e,t,n,r,u;const a=[fe,se],f=[];function i(l,o){return o&1&&(e=null),e==null&&(e=!!(l[4]&&P(l[0]))),e?0:1}return t=i(s,-1),n=f[t]=a[t](s),{c(){n.c(),r=F()},m(l,o){f[t].m(l,o),U(l,r,o),u=!0},p(l,[o]){let _=t;t=i(l,o),t===_?f[t].p(l,o):(M(),w(f[_],1,1,()=>{f[_]=null}),O(),n=f[t],n?n.p(l,o):(n=f[t]=a[t](l),n.c()),m(n,1),n.m(r.parentNode,r))},i(l){u||(m(n),u=!0)},o(l){w(n),u=!1},d(l){l&&R(r),f[t].d(l)}}}function de(s,e,t){const n=["href","download"];let r=N(e,n),{$$slots:u={},$$scope:a}=e;var f=this&&this.__awaiter||function(c,v,p,k){function g(d){return d instanceof p?d:new p(function(y){y(d)})}return new(p||(p=Promise))(function(d,y){function I(h){try{C(k.next(h))}catch(D){y(D)}}function J(h){try{C(k.throw(h))}catch(D){y(D)}}function C(h){h.done?d(h.value):g(h.value).then(I,J)}C((k=k.apply(c,v||[])).next())})};let{href:i=void 0}=e,{download:l}=e;const o=ae();let _=!1;const q=K();function A(){return f(this,void 0,void 0,function*(){if(_)return;if(o("click"),i==null)throw new Error("href is not defined.");if(q==null)throw new Error("Wasm worker proxy is not available.");const v=new URL(i,window.location.href).pathname;t(2,_=!0),q.httpRequest({method:"GET",path:v,headers:{},query_string:""}).then(p=>{if(p.status!==200)throw new Error(`Failed to get file ${v} from the Wasm worker.`);const k=new Blob([p.body],{type:Q(p.headers,"content-type")}),g=URL.createObjectURL(k),d=document.createElement("a");d.href=g,d.download=l,d.click(),URL.revokeObjectURL(g)}).finally(()=>{t(2,_=!1)})})}return s.$$set=c=>{e=L(L({},e),ne(c)),t(6,r=N(e,n)),"href"in c&&t(0,i=c.href),"download"in c&&t(1,l=c.download),"$$scope"in c&&t(7,a=c.$$scope)},[i,l,_,o,q,A,r,a,u]}class we extends le{constructor(e){super(),oe(this,e,de,ce,ie,{href:0,download:1})}}export{we as D,me as a};
//# sourceMappingURL=DownloadLink-7ff36416.js.map
