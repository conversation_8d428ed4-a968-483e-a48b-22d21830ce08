const{SvelteComponent:g,append:o,attr:m,detach:v,element:y,init:h,insert:r,noop:u,safe_not_equal:b,set_data:q,text:w,toggle_class:i}=window.__gradio__svelte__internal;function E(s){let e,a;return{c(){e=y("div"),a=w(s[2]),m(e,"class","svelte-1ayixqk"),i(e,"table",s[0]==="table"),i(e,"gallery",s[0]==="gallery"),i(e,"selected",s[1])},m(t,n){r(t,e,n),o(e,a)},p(t,[n]){n&4&&q(a,t[2]),n&1&&i(e,"table",t[0]==="table"),n&1&&i(e,"gallery",t[0]==="gallery"),n&2&&i(e,"selected",t[1])},i:u,o:u,d(t){t&&v(e)}}}function k(s,e,a){let{value:t}=e,{type:n}=e,{selected:_=!1}=e,{choices:c}=e,f;if(t===null)f="";else{let l=c.find(d=>d[1]===t);f=l?l[0]:""}return s.$$set=l=>{"value"in l&&a(3,t=l.value),"type"in l&&a(0,n=l.type),"selected"in l&&a(1,_=l.selected),"choices"in l&&a(4,c=l.choices)},[n,_,f,t,c]}class C extends g{constructor(e){super(),h(this,e,k,E,b,{value:3,type:0,selected:1,choices:4})}}export{C as default};
//# sourceMappingURL=Example-aaefd914.js.map
