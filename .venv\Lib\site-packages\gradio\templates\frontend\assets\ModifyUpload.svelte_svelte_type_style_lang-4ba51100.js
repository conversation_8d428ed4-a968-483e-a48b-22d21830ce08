import{p as Ue,u as Ee}from"./index-a80d931b.js";import"./Index-26cfc80a.js";const{SvelteComponent:Fe,append:v,attr:C,detach:Z,element:T,init:Pe,insert:x,noop:K,safe_not_equal:Se,set_data:N,set_style:R,space:B,text:D,toggle_class:Q}=window.__gradio__svelte__internal,{onMount:Ce,createEventDispatcher:Te,getContext:ze}=window.__gradio__svelte__internal;function V(t){let e,n,l,o,a=j(t[2])+"",f,d,r,s,p=t[2].orig_name+"",g;return{c(){e=T("div"),n=T("span"),l=T("div"),o=T("progress"),f=D(a),r=B(),s=T("span"),g=D(p),R(o,"visibility","hidden"),R(o,"height","0"),R(o,"width","0"),o.value=d=j(t[2]),C(o,"max","100"),C(o,"class","svelte-1vsfomn"),C(l,"class","progress-bar svelte-1vsfomn"),C(s,"class","file-name svelte-1vsfomn"),C(e,"class","file svelte-1vsfomn")},m(c,_){x(c,e,_),v(e,n),v(n,l),v(l,o),v(o,f),v(e,r),v(e,s),v(s,g)},p(c,_){_&4&&a!==(a=j(c[2])+"")&&N(f,a),_&4&&d!==(d=j(c[2]))&&(o.value=d),_&4&&p!==(p=c[2].orig_name+"")&&N(g,p)},d(c){c&&Z(e)}}}function De(t){let e,n,l,o=t[0].length+"",a,f,d=t[0].length>1?"files":"file",r,s,p,g=t[2]&&V(t);return{c(){e=T("div"),n=T("span"),l=D("Uploading "),a=D(o),f=B(),r=D(d),s=D("..."),p=B(),g&&g.c(),C(n,"class","uploading svelte-1vsfomn"),C(e,"class","wrap svelte-1vsfomn"),Q(e,"progress",t[1])},m(c,_){x(c,e,_),v(e,n),v(n,l),v(n,a),v(n,f),v(n,r),v(n,s),v(e,p),g&&g.m(e,null)},p(c,[_]){_&1&&o!==(o=c[0].length+"")&&N(a,o),_&1&&d!==(d=c[0].length>1?"files":"file")&&N(r,d),c[2]?g?g.p(c,_):(g=V(c),g.c(),g.m(e,null)):g&&(g.d(1),g=null),_&2&&Q(e,"progress",c[1])},i:K,o:K,d(c){c&&Z(e),g&&g.d()}}}function j(t){return t.progress*100/(t.size||0)||0}function We(t){let e=0;return t.forEach(n=>{e+=j(n)}),document.documentElement.style.setProperty("--upload-progress-width",(e/t.length).toFixed(2)+"%"),e/t.length}function je(t,e,n){let{upload_id:l}=e,{root:o}=e,{files:a}=e,f,d=!1,r,s,p=a.map(u=>({...u,progress:0}));const g=Te();function c(u,m){n(0,p=p.map(w=>(w.orig_name===u&&(w.progress+=m),w)))}const _=ze("EventSource_factory");return Ce(()=>{f=_(new URL(`${o}/upload_progress?upload_id=${l}`)),f.onmessage=async function(u){const m=JSON.parse(u.data);d||n(1,d=!0),m.msg==="done"?(f.close(),g("done")):(n(6,r=m),c(m.orig_name,m.chunk_size))}}),t.$$set=u=>{"upload_id"in u&&n(3,l=u.upload_id),"root"in u&&n(4,o=u.root),"files"in u&&n(5,a=u.files)},t.$$.update=()=>{t.$$.dirty&1&&We(p),t.$$.dirty&65&&n(2,s=r||p[0])},[p,d,s,l,o,a,r]}class qe extends Fe{constructor(e){super(),Pe(this,e,je,De,Se,{upload_id:3,root:4,files:5})}}const{SvelteComponent:Me,append:X,attr:y,binding_callbacks:Ne,bubble:F,check_outros:$,create_component:Ie,create_slot:ee,destroy_component:Je,detach:I,element:G,empty:te,get_all_dirty_from_scope:le,get_slot_changes:ne,group_outros:ie,init:Le,insert:J,listen:k,mount_component:Oe,prevent_default:P,run_all:Re,safe_not_equal:Be,set_style:oe,space:Ge,stop_propagation:S,toggle_class:h,transition_in:E,transition_out:z,update_slot_base:re}=window.__gradio__svelte__internal,{createEventDispatcher:He,tick:Ke,getContext:Qe}=window.__gradio__svelte__internal;function Ve(t){let e,n,l,o,a,f,d,r,s,p,g;const c=t[22].default,_=ee(c,t,t[21],null);return{c(){e=G("button"),_&&_.c(),n=Ge(),l=G("input"),y(l,"aria-label","file upload"),y(l,"data-testid","file-upload"),y(l,"type","file"),y(l,"accept",o=t[14]||void 0),l.multiple=a=t[6]==="multiple"||void 0,y(l,"webkitdirectory",f=t[6]==="directory"||void 0),y(l,"mozdirectory",d=t[6]==="directory"||void 0),y(l,"class","svelte-j5bxrl"),y(e,"tabindex",r=t[9]?-1:0),y(e,"class","svelte-j5bxrl"),h(e,"hidden",t[9]),h(e,"center",t[4]),h(e,"boundedheight",t[3]),h(e,"flex",t[5]),h(e,"disable_click",t[7]),oe(e,"height","100%")},m(u,m){J(u,e,m),_&&_.m(e,null),X(e,n),X(e,l),t[30](l),s=!0,p||(g=[k(l,"change",t[16]),k(e,"drag",S(P(t[23]))),k(e,"dragstart",S(P(t[24]))),k(e,"dragend",S(P(t[25]))),k(e,"dragover",S(P(t[26]))),k(e,"dragenter",S(P(t[27]))),k(e,"dragleave",S(P(t[28]))),k(e,"drop",S(P(t[29]))),k(e,"click",t[11]),k(e,"drop",t[17]),k(e,"dragenter",t[15]),k(e,"dragleave",t[15])],p=!0)},p(u,m){_&&_.p&&(!s||m[0]&2097152)&&re(_,c,u,u[21],s?ne(c,u[21],m,null):le(u[21]),null),(!s||m[0]&16384&&o!==(o=u[14]||void 0))&&y(l,"accept",o),(!s||m[0]&64&&a!==(a=u[6]==="multiple"||void 0))&&(l.multiple=a),(!s||m[0]&64&&f!==(f=u[6]==="directory"||void 0))&&y(l,"webkitdirectory",f),(!s||m[0]&64&&d!==(d=u[6]==="directory"||void 0))&&y(l,"mozdirectory",d),(!s||m[0]&512&&r!==(r=u[9]?-1:0))&&y(e,"tabindex",r),(!s||m[0]&512)&&h(e,"hidden",u[9]),(!s||m[0]&16)&&h(e,"center",u[4]),(!s||m[0]&8)&&h(e,"boundedheight",u[3]),(!s||m[0]&32)&&h(e,"flex",u[5]),(!s||m[0]&128)&&h(e,"disable_click",u[7])},i(u){s||(E(_,u),s=!0)},o(u){z(_,u),s=!1},d(u){u&&I(e),_&&_.d(u),t[30](null),p=!1,Re(g)}}}function Xe(t){let e,n,l=!t[9]&&Y(t);return{c(){l&&l.c(),e=te()},m(o,a){l&&l.m(o,a),J(o,e,a),n=!0},p(o,a){o[9]?l&&(ie(),z(l,1,1,()=>{l=null}),$()):l?(l.p(o,a),a[0]&512&&E(l,1)):(l=Y(o),l.c(),E(l,1),l.m(e.parentNode,e))},i(o){n||(E(l),n=!0)},o(o){z(l),n=!1},d(o){o&&I(e),l&&l.d(o)}}}function Ye(t){let e,n,l,o,a;const f=t[22].default,d=ee(f,t,t[21],null);return{c(){e=G("button"),d&&d.c(),y(e,"tabindex",n=t[9]?-1:0),y(e,"class","svelte-j5bxrl"),h(e,"hidden",t[9]),h(e,"center",t[4]),h(e,"boundedheight",t[3]),h(e,"flex",t[5]),oe(e,"height","100%")},m(r,s){J(r,e,s),d&&d.m(e,null),l=!0,o||(a=k(e,"click",t[10]),o=!0)},p(r,s){d&&d.p&&(!l||s[0]&2097152)&&re(d,f,r,r[21],l?ne(f,r[21],s,null):le(r[21]),null),(!l||s[0]&512&&n!==(n=r[9]?-1:0))&&y(e,"tabindex",n),(!l||s[0]&512)&&h(e,"hidden",r[9]),(!l||s[0]&16)&&h(e,"center",r[4]),(!l||s[0]&8)&&h(e,"boundedheight",r[3]),(!l||s[0]&32)&&h(e,"flex",r[5])},i(r){l||(E(d,r),l=!0)},o(r){z(d,r),l=!1},d(r){r&&I(e),d&&d.d(r),o=!1,a()}}}function Y(t){let e,n;return e=new qe({props:{root:t[8],upload_id:t[12],files:t[13]}}),{c(){Ie(e.$$.fragment)},m(l,o){Oe(e,l,o),n=!0},p(l,o){const a={};o[0]&256&&(a.root=l[8]),o[0]&4096&&(a.upload_id=l[12]),o[0]&8192&&(a.files=l[13]),e.$set(a)},i(l){n||(E(e.$$.fragment,l),n=!0)},o(l){z(e.$$.fragment,l),n=!1},d(l){Je(e,l)}}}function Ze(t){let e,n,l,o;const a=[Ye,Xe,Ve],f=[];function d(r,s){return r[0]==="clipboard"?0:r[1]&&!r[2]?1:2}return e=d(t),n=f[e]=a[e](t),{c(){n.c(),l=te()},m(r,s){f[e].m(r,s),J(r,l,s),o=!0},p(r,s){let p=e;e=d(r),e===p?f[e].p(r,s):(ie(),z(f[p],1,1,()=>{f[p]=null}),$(),n=f[e],n?n.p(r,s):(n=f[e]=a[e](r),n.c()),E(n,1),n.m(l.parentNode,l))},i(r){o||(E(n),o=!0)},o(r){z(n),o=!1},d(r){r&&I(l),f[e].d(r)}}}function xe(t,e,n){if(!t||t==="*"||t==="file/*"||Array.isArray(t)&&t.some(o=>o==="*"||o==="file/*"))return!0;let l;if(typeof t=="string")l=t.split(",").map(o=>o.trim());else if(Array.isArray(t))l=t;else return!1;return l.includes(e)||l.some(o=>{const[a]=o.split("/").map(f=>f.trim());return o.endsWith("/*")&&n.startsWith(a+"/")})}function $e(t,e,n){let{$$slots:l={},$$scope:o}=e,{filetype:a=null}=e,{dragging:f=!1}=e,{boundedheight:d=!0}=e,{center:r=!0}=e,{flex:s=!0}=e,{file_count:p="single"}=e,{disable_click:g=!1}=e,{root:c}=e,{hidden:_=!1}=e,{format:u="file"}=e,{uploading:m=!1}=e,{hidden_upload:w=null}=e,L,O,W;const ae=Qe("upload_files"),q=He(),se=["image","video","audio","text","file"],H=i=>i.startsWith(".")||i.endsWith("/*")?i:se.includes(i)?i+"/*":"."+i;function ue(){n(18,f=!f)}function fe(){navigator.clipboard.read().then(async i=>{for(let b=0;b<i.length;b++){const A=i[b].types.find(U=>U.startsWith("image/"));if(A){i[b].getType(A).then(async U=>{const Ae=new File([U],`clipboard.${A.replace("image/","")}`);await M([Ae])});break}}})}function de(){g||w&&(n(2,w.value="",w),w.click())}async function _e(i){await Ke(),n(12,L=Math.random().toString(36).substring(2,15)),n(1,m=!0);const b=await Ee(i,c,L,ae);return q("load",p==="single"?b?.[0]:b),n(1,m=!1),b||[]}async function M(i){if(!i.length)return;let b=i.map(A=>new File([A],A.name,{type:A.type}));return n(13,O=await Ue(b)),await _e(O)}async function ce(i){const b=i.target;if(b.files)if(u!="blob")await M(Array.from(b.files));else{if(p==="single"){q("load",b.files[0]);return}q("load",b.files)}}async function ge(i){if(n(18,f=!1),!i.dataTransfer?.files)return;const b=Array.from(i.dataTransfer.files).filter(A=>{const U="."+A.name.split(".").pop();return U&&xe(W,U,A.type)||(U&&Array.isArray(a)?a.includes(U):U===a)?!0:(q("error",`Invalid file type only ${a} allowed.`),!1)});await M(b)}function pe(i){F.call(this,t,i)}function me(i){F.call(this,t,i)}function he(i){F.call(this,t,i)}function be(i){F.call(this,t,i)}function ye(i){F.call(this,t,i)}function ve(i){F.call(this,t,i)}function ke(i){F.call(this,t,i)}function we(i){Ne[i?"unshift":"push"](()=>{w=i,n(2,w)})}return t.$$set=i=>{"filetype"in i&&n(0,a=i.filetype),"dragging"in i&&n(18,f=i.dragging),"boundedheight"in i&&n(3,d=i.boundedheight),"center"in i&&n(4,r=i.center),"flex"in i&&n(5,s=i.flex),"file_count"in i&&n(6,p=i.file_count),"disable_click"in i&&n(7,g=i.disable_click),"root"in i&&n(8,c=i.root),"hidden"in i&&n(9,_=i.hidden),"format"in i&&n(19,u=i.format),"uploading"in i&&n(1,m=i.uploading),"hidden_upload"in i&&n(2,w=i.hidden_upload),"$$scope"in i&&n(21,o=i.$$scope)},t.$$.update=()=>{t.$$.dirty[0]&1&&(a==null?n(14,W=null):typeof a=="string"?n(14,W=H(a)):(n(0,a=a.map(H)),n(14,W=a.join(", "))))},[a,m,w,d,r,s,p,g,c,_,fe,de,L,O,W,ue,ce,ge,f,u,M,o,l,pe,me,he,be,ye,ve,ke,we]}class lt extends Me{constructor(e){super(),Le(this,e,$e,Ze,Be,{filetype:0,dragging:18,boundedheight:3,center:4,flex:5,file_count:6,disable_click:7,root:8,hidden:9,format:19,uploading:1,hidden_upload:2,paste_clipboard:10,open_file_upload:11,load_files:20},null,[-1,-1])}get paste_clipboard(){return this.$$.ctx[10]}get open_file_upload(){return this.$$.ctx[11]}get load_files(){return this.$$.ctx[20]}}export{lt as U};
//# sourceMappingURL=ModifyUpload.svelte_svelte_type_style_lang-4ba51100.js.map
