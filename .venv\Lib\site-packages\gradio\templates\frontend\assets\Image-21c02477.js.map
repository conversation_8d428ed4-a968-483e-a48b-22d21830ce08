{"version": 3, "file": "Image-21c02477.js", "sources": ["../../../../js/image/shared/Image.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { HTMLImgAttributes } from \"svelte/elements\";\n\tinterface Props extends HTMLImgAttributes {\n\t\t\"data-testid\"?: string;\n\t}\n\ttype $$Props = Props;\n\n\timport { resolve_wasm_src } from \"@gradio/wasm/svelte\";\n\n\texport let src: HTMLImgAttributes[\"src\"] = undefined;\n\n\tlet resolved_src: typeof src;\n\n\t// The `src` prop can be updated before the Promise from `resolve_wasm_src` is resolved.\n\t// In such a case, the resolved value for the old `src` has to be discarded,\n\t// This variable `latest_src` is used to pick up only the value resolved for the latest `src` prop.\n\tlet latest_src: typeof src;\n\t$: {\n\t\t// In normal (non-Wasm) Gradio, the `<img>` element should be rendered with the passed `src` props immediately\n\t\t// without waiting for `resolve_wasm_src()` to resolve.\n\t\t// If it waits, a blank image is displayed until the async task finishes\n\t\t// and it leads to undesirable flickering.\n\t\t// So set `src` to `resolved_src` here.\n\t\tresolved_src = src;\n\n\t\tlatest_src = src;\n\t\tconst resolving_src = src;\n\t\tresolve_wasm_src(resolving_src).then((s) => {\n\t\t\tif (latest_src === resolving_src) {\n\t\t\t\tresolved_src = s;\n\t\t\t}\n\t\t});\n\t}\n</script>\n\n<!-- svelte-ignore a11y-missing-attribute -->\n<img src={resolved_src} {...$$restProps} />\n\n<style>\n\timg {\n\t\tobject-fit: cover;\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "img", "anchor", "img_src_value", "src", "$$props", "resolved_src", "latest_src", "$$invalidate", "resolving_src", "resolve_wasm_src", "s"], "mappings": "4TAoCUA,EAAY,CAAA,GAAMA,EAAW,CAAA,8GAAvCC,EAA0CC,EAAAC,EAAAC,CAAA,uCAAhCJ,EAAY,CAAA,CAAA,GAAA,CAAA,IAAAK,CAAA,OAAML,EAAW,CAAA,qGA3B3B,CAAA,IAAAM,EAAgC,MAAS,EAAAC,EAEhDC,EAKAC,gHACH,CAMAC,EAAA,EAAAF,EAAeF,CAAG,EAElBI,EAAA,EAAAD,EAAaH,CAAG,EACV,MAAAK,EAAgBL,EACtBM,EAAiBD,CAAa,EAAE,KAAME,GAAC,CAClCJ,IAAeE,GAClBD,EAAA,EAAAF,EAAeK,CAAC"}