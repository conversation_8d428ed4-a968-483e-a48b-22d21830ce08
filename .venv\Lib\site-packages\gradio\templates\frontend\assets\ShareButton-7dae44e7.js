import{I as g}from"./IconButton-7294c90b.js";import"./Index-26cfc80a.js";import"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{S as d}from"./utils-572af92b.js";const{SvelteComponent:h,append:p,attr:s,detach:w,init:v,insert:S,noop:l,safe_not_equal:A,svg_element:_}=window.__gradio__svelte__internal;function C(a){let e,n;return{c(){e=_("svg"),n=_("path"),s(n,"d","M23,20a5,5,0,0,0-3.89,1.89L11.8,17.32a4.46,4.46,0,0,0,0-2.64l7.31-4.57A5,5,0,1,0,18,7a4.79,4.79,0,0,0,.2,1.32l-7.31,4.57a5,5,0,1,0,0,6.22l7.31,4.57A4.79,4.79,0,0,0,18,25a5,5,0,1,0,5-5ZM23,4a3,3,0,1,1-3,3A3,3,0,0,1,23,4ZM7,19a3,3,0,1,1,3-3A3,3,0,0,1,7,19Zm16,9a3,3,0,1,1,3-3A3,3,0,0,1,23,28Z"),s(n,"fill","currentColor"),s(e,"id","icon"),s(e,"xmlns","http://www.w3.org/2000/svg"),s(e,"viewBox","0 0 32 32")},m(t,r){S(t,e,r),p(e,n)},p:l,i:l,o:l,d(t){t&&w(e)}}}class b extends h{constructor(e){super(),v(this,e,null,C,A,{})}}const{SvelteComponent:y,create_component:Z,destroy_component:q,init:x,mount_component:B,safe_not_equal:I,transition_in:M,transition_out:k}=window.__gradio__svelte__internal,{createEventDispatcher:E}=window.__gradio__svelte__internal;function D(a){let e,n;return e=new g({props:{Icon:b,label:a[2]("common.share"),pending:a[3]}}),e.$on("click",a[5]),{c(){Z(e.$$.fragment)},m(t,r){B(e,t,r),n=!0},p(t,[r]){const i={};r&4&&(i.label=t[2]("common.share")),r&8&&(i.pending=t[3]),e.$set(i)},i(t){n||(M(e.$$.fragment,t),n=!0)},o(t){k(e.$$.fragment,t),n=!1},d(t){q(e,t)}}}function L(a,e,n){const t=E();let{formatter:r}=e,{value:i}=e,{i18n:m}=e,c=!1;const f=async()=>{try{n(3,c=!0);const o=await r(i);t("share",{description:o})}catch(o){console.error(o);let u=o instanceof d?o.message:"Share failed.";t("error",u)}finally{n(3,c=!1)}};return a.$$set=o=>{"formatter"in o&&n(0,r=o.formatter),"value"in o&&n(1,i=o.value),"i18n"in o&&n(2,m=o.i18n)},[r,i,m,c,t,f]}class H extends y{constructor(e){super(),x(this,e,L,D,I,{formatter:0,value:1,i18n:2})}}export{H as S};
//# sourceMappingURL=ShareButton-7dae44e7.js.map
