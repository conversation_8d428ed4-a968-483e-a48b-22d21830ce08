{"version": 3, "file": "utils-572af92b.js", "sources": ["../../../../js/utils/src/utils.ts"], "sourcesContent": ["import type { ActionReturn } from \"svelte/action\";\nexport interface SelectData {\n\tindex: number | [number, number];\n\tvalue: any;\n\tselected?: boolean;\n}\n\nexport interface LikeData {\n\tindex: number | [number, number];\n\tvalue: any;\n\tliked?: boolean;\n}\n\nexport interface KeyUpData {\n\tkey: string;\n\tinput_value: string;\n}\n\nexport interface ShareData {\n\tdescription: string;\n\ttitle?: string;\n}\n\nexport class ShareError extends Error {\n\tconstructor(message: string) {\n\t\tsuper(message);\n\t\tthis.name = \"ShareError\";\n\t}\n}\n\nexport async function uploadToHuggingFace(\n\tdata: string,\n\ttype: \"base64\" | \"url\"\n): Promise<string> {\n\tif (window.__gradio_space__ == null) {\n\t\tthrow new ShareError(\"Must be on Spaces to share.\");\n\t}\n\tlet blob: Blob;\n\tlet contentType: string;\n\tlet filename: string;\n\tif (type === \"url\") {\n\t\tconst response = await fetch(data);\n\t\tblob = await response.blob();\n\t\tcontentType = response.headers.get(\"content-type\") || \"\";\n\t\tfilename = response.headers.get(\"content-disposition\") || \"\";\n\t} else {\n\t\tblob = dataURLtoBlob(data);\n\t\tcontentType = data.split(\";\")[0].split(\":\")[1];\n\t\tfilename = \"file\" + contentType.split(\"/\")[1];\n\t}\n\n\tconst file = new File([blob], filename, { type: contentType });\n\n\t// Send file to endpoint\n\tconst uploadResponse = await fetch(\"https://huggingface.co/uploads\", {\n\t\tmethod: \"POST\",\n\t\tbody: file,\n\t\theaders: {\n\t\t\t\"Content-Type\": file.type,\n\t\t\t\"X-Requested-With\": \"XMLHttpRequest\"\n\t\t}\n\t});\n\n\t// Check status of response\n\tif (!uploadResponse.ok) {\n\t\tif (\n\t\t\tuploadResponse.headers.get(\"content-type\")?.includes(\"application/json\")\n\t\t) {\n\t\t\tconst error = await uploadResponse.json();\n\t\t\tthrow new ShareError(`Upload failed: ${error.error}`);\n\t\t}\n\t\tthrow new ShareError(`Upload failed.`);\n\t}\n\n\t// Return response if needed\n\tconst result = await uploadResponse.text();\n\treturn result;\n}\n\nfunction dataURLtoBlob(dataurl: string): Blob {\n\tvar arr = dataurl.split(\",\"),\n\t\tmime = (arr[0].match(/:(.*?);/) as RegExpMatchArray)[1],\n\t\tbstr = atob(arr[1]),\n\t\tn = bstr.length,\n\t\tu8arr = new Uint8Array(n);\n\twhile (n--) {\n\t\tu8arr[n] = bstr.charCodeAt(n);\n\t}\n\treturn new Blob([u8arr], { type: mime });\n}\n\nexport function copy(node: HTMLDivElement): ActionReturn {\n\tnode.addEventListener(\"click\", handle_copy);\n\n\tasync function handle_copy(event: MouseEvent): Promise<void> {\n\t\tconst path = event.composedPath() as HTMLButtonElement[];\n\n\t\tconst [copy_button] = path.filter(\n\t\t\t(e) => e?.tagName === \"BUTTON\" && e.classList.contains(\"copy_code_button\")\n\t\t);\n\n\t\tif (copy_button) {\n\t\t\tevent.stopImmediatePropagation();\n\n\t\t\tconst copy_text = copy_button.parentElement!.innerText.trim();\n\t\t\tconst copy_sucess_button = Array.from(\n\t\t\t\tcopy_button.children\n\t\t\t)[1] as HTMLDivElement;\n\n\t\t\tconst copied = await copy_to_clipboard(copy_text);\n\n\t\t\tif (copied) copy_feedback(copy_sucess_button);\n\n\t\t\tfunction copy_feedback(_copy_sucess_button: HTMLDivElement): void {\n\t\t\t\t_copy_sucess_button.style.opacity = \"1\";\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t_copy_sucess_button.style.opacity = \"0\";\n\t\t\t\t}, 2000);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn {\n\t\tdestroy(): void {\n\t\t\tnode.removeEventListener(\"click\", handle_copy);\n\t\t}\n\t};\n}\n\nasync function copy_to_clipboard(value: string): Promise<boolean> {\n\tlet copied = false;\n\tif (\"clipboard\" in navigator) {\n\t\tawait navigator.clipboard.writeText(value);\n\t\tcopied = true;\n\t} else {\n\t\tconst textArea = document.createElement(\"textarea\");\n\t\ttextArea.value = value;\n\n\t\ttextArea.style.position = \"absolute\";\n\t\ttextArea.style.left = \"-999999px\";\n\n\t\tdocument.body.prepend(textArea);\n\t\ttextArea.select();\n\n\t\ttry {\n\t\t\tdocument.execCommand(\"copy\");\n\t\t\tcopied = true;\n\t\t} catch (error) {\n\t\t\tconsole.error(error);\n\t\t\tcopied = false;\n\t\t} finally {\n\t\t\ttextArea.remove();\n\t\t}\n\t}\n\n\treturn copied;\n}\n\nexport const format_time = (seconds: number): string => {\n\tconst hours = Math.floor(seconds / 3600);\n\tconst minutes = Math.floor((seconds % 3600) / 60);\n\tconst seconds_remainder = Math.round(seconds) % 60;\n\tconst padded_minutes = `${minutes < 10 ? \"0\" : \"\"}${minutes}`;\n\tconst padded_seconds = `${\n\t\tseconds_remainder < 10 ? \"0\" : \"\"\n\t}${seconds_remainder}`;\n\n\tif (hours > 0) {\n\t\treturn `${hours}:${padded_minutes}:${padded_seconds}`;\n\t}\n\treturn `${minutes}:${padded_seconds}`;\n};\n\nexport type I18nFormatter = any;\nexport class Gradio<T extends Record<string, any> = Record<string, any>> {\n\t#id: number;\n\ttheme: string;\n\tversion: string;\n\ti18n: I18nFormatter;\n\t#el: HTMLElement;\n\troot: string;\n\tautoscroll: boolean;\n\n\tconstructor(\n\t\tid: number,\n\t\tel: HTMLElement,\n\t\ttheme: string,\n\t\tversion: string,\n\t\troot: string,\n\t\tautoscroll: boolean\n\t) {\n\t\tthis.#id = id;\n\t\tthis.theme = theme;\n\t\tthis.version = version;\n\t\tthis.#el = el;\n\n\t\tthis.i18n = (x: string): string => x;\n\t\tthis.root = root;\n\t\tthis.autoscroll = autoscroll;\n\t}\n\n\tdispatch<E extends keyof T>(event_name: E, data?: T[E]): void {\n\t\tconst e = new CustomEvent(\"gradio\", {\n\t\t\tbubbles: true,\n\t\t\tdetail: { data, id: this.#id, event: event_name }\n\t\t});\n\t\tthis.#el.dispatchEvent(e);\n\t}\n}\n"], "names": ["ShareError", "message", "uploadToHuggingFace", "data", "type", "blob", "contentType", "filename", "response", "dataURLtoBlob", "file", "uploadResponse", "error", "dataurl", "arr", "mime", "bstr", "n", "u8arr", "copy", "node", "handle_copy", "event", "path", "copy_button", "e", "copy_feedback", "_copy_sucess_button", "copy_text", "copy_sucess_button", "copy_to_clipboard", "value", "copied", "textArea", "format_time", "seconds", "hours", "minutes", "seconds_remainder", "padded_minutes", "padded_seconds"], "mappings": "AAuBO,MAAMA,UAAmB,KAAM,CACrC,YAAYC,EAAiB,CAC5B,MAAMA,CAAO,EACb,KAAK,KAAO,YACb,CACD,CAEsB,eAAAC,EACrBC,EACAC,EACkB,CACd,GAAA,OAAO,kBAAoB,KACxB,MAAA,IAAIJ,EAAW,6BAA6B,EAE/C,IAAAK,EACAC,EACAC,EACJ,GAAIH,IAAS,MAAO,CACb,MAAAI,EAAW,MAAM,MAAML,CAAI,EAC1BE,EAAA,MAAMG,EAAS,OACtBF,EAAcE,EAAS,QAAQ,IAAI,cAAc,GAAK,GACtDD,EAAWC,EAAS,QAAQ,IAAI,qBAAqB,GAAK,QAE1DH,EAAOI,EAAcN,CAAI,EACXG,EAAAH,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAC7CI,EAAW,OAASD,EAAY,MAAM,GAAG,EAAE,CAAC,EAGvC,MAAAI,EAAO,IAAI,KAAK,CAACL,CAAI,EAAGE,EAAU,CAAE,KAAMD,CAAA,CAAa,EAGvDK,EAAiB,MAAM,MAAM,iCAAkC,CACpE,OAAQ,OACR,KAAMD,EACN,QAAS,CACR,eAAgBA,EAAK,KACrB,mBAAoB,gBACrB,CAAA,CACA,EAGG,GAAA,CAACC,EAAe,GAAI,CACvB,GACCA,EAAe,QAAQ,IAAI,cAAc,GAAG,SAAS,kBAAkB,EACtE,CACK,MAAAC,EAAQ,MAAMD,EAAe,OACnC,MAAM,IAAIX,EAAW,kBAAkBY,EAAM,OAAO,EAE/C,MAAA,IAAIZ,EAAW,gBAAgB,EAK/B,OADQ,MAAMW,EAAe,MAErC,CAEA,SAASF,EAAcI,EAAuB,CAM7C,QALIC,EAAMD,EAAQ,MAAM,GAAG,EAC1BE,EAAQD,EAAI,CAAC,EAAE,MAAM,SAAS,EAAuB,CAAC,EACtDE,EAAO,KAAKF,EAAI,CAAC,CAAC,EAClBG,EAAID,EAAK,OACTE,EAAQ,IAAI,WAAWD,CAAC,EAClBA,KACNC,EAAMD,CAAC,EAAID,EAAK,WAAWC,CAAC,EAEtB,OAAA,IAAI,KAAK,CAACC,CAAK,EAAG,CAAE,KAAMH,EAAM,CACxC,CAEO,SAASI,EAAKC,EAAoC,CACnDA,EAAA,iBAAiB,QAASC,CAAW,EAE1C,eAAeA,EAAYC,EAAkC,CACtD,MAAAC,EAAOD,EAAM,eAEb,CAACE,CAAW,EAAID,EAAK,OACzBE,GAAMA,GAAG,UAAY,UAAYA,EAAE,UAAU,SAAS,kBAAkB,CAAA,EAG1E,GAAID,EAAa,CAYP,IAAAE,EAAT,SAAuBC,EAA2C,CACjEA,EAAoB,MAAM,QAAU,IACpC,WAAW,IAAM,CAChBA,EAAoB,MAAM,QAAU,KAClC,GAAI,CAAA,EAfRL,EAAM,yBAAyB,EAE/B,MAAMM,EAAYJ,EAAY,cAAe,UAAU,KAAK,EACtDK,EAAqB,MAAM,KAChCL,EAAY,UACX,CAAC,EAEY,MAAMM,EAAkBF,CAAS,GAEpCF,EAAcG,CAAkB,EAS9C,CAEO,MAAA,CACN,SAAgB,CACVT,EAAA,oBAAoB,QAASC,CAAW,CAC9C,CAAA,CAEF,CAEA,eAAeS,EAAkBC,EAAiC,CACjE,IAAIC,EAAS,GACb,GAAI,cAAe,UACZ,MAAA,UAAU,UAAU,UAAUD,CAAK,EAChCC,EAAA,OACH,CACA,MAAAC,EAAW,SAAS,cAAc,UAAU,EAClDA,EAAS,MAAQF,EAEjBE,EAAS,MAAM,SAAW,WAC1BA,EAAS,MAAM,KAAO,YAEb,SAAA,KAAK,QAAQA,CAAQ,EAC9BA,EAAS,OAAO,EAEZ,GAAA,CACH,SAAS,YAAY,MAAM,EAClBD,EAAA,SACDpB,GACR,QAAQ,MAAMA,CAAK,EACVoB,EAAA,EAAA,QACR,CACDC,EAAS,OAAO,CACjB,EAGM,OAAAD,CACR,CAEa,MAAAE,EAAeC,GAA4B,CACvD,MAAMC,EAAQ,KAAK,MAAMD,EAAU,IAAI,EACjCE,EAAU,KAAK,MAAOF,EAAU,KAAQ,EAAE,EAC1CG,EAAoB,KAAK,MAAMH,CAAO,EAAI,GAC1CI,EAAiB,GAAGF,EAAU,GAAK,IAAM,KAAKA,IAC9CG,EAAiB,GACtBF,EAAoB,GAAK,IAAM,KAC7BA,IAEH,OAAIF,EAAQ,EACJ,GAAGA,KAASG,KAAkBC,IAE/B,GAAGH,KAAWG,GACtB"}