const{SvelteComponent:r,append:f,attr:d,detach:g,element:u,init:v,insert:y,noop:c,safe_not_equal:o,set_data:m,text:b,toggle_class:_}=window.__gradio__svelte__internal;function S(a){let e,n,s=JSON.stringify(a[0],null,2)+"",i;return{c(){e=u("div"),n=u("pre"),i=b(s),d(e,"class","svelte-1ayixqk"),_(e,"table",a[1]==="table"),_(e,"gallery",a[1]==="gallery"),_(e,"selected",a[2])},m(l,t){y(l,e,t),f(e,n),f(n,i)},p(l,[t]){t&1&&s!==(s=JSON.stringify(l[0],null,2)+"")&&m(i,s),t&2&&_(e,"table",l[1]==="table"),t&2&&_(e,"gallery",l[1]==="gallery"),t&4&&_(e,"selected",l[2])},i:c,o:c,d(l){l&&g(e)}}}function h(a,e,n){let{value:s}=e,{type:i}=e,{selected:l=!1}=e;return a.$$set=t=>{"value"in t&&n(0,s=t.value),"type"in t&&n(1,i=t.type),"selected"in t&&n(2,l=t.selected)},[s,i,l]}class p extends r{constructor(e){super(),v(this,e,h,S,o,{value:0,type:1,selected:2})}}export{p as default};
//# sourceMappingURL=Example-9dc9204b.js.map
