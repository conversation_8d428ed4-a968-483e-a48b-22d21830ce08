import{S as y}from"./Index-26cfc80a.js";import{B as $}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{B as x}from"./BlockLabel-f27805b1.js";import{E as ee}from"./Empty-28f63bf0.js";import"./index-a80d931b.js";import"./svelte/svelte.js";const{SvelteComponent:le,append:te,attr:v,detach:ne,init:ie,insert:ae,noop:A,safe_not_equal:se,svg_element:z}=window.__gradio__svelte__internal;function oe(i){let e,t;return{c(){e=z("svg"),t=z("path"),v(t,"fill","currentColor"),v(t,"d","M4 2H2v26a2 2 0 0 0 2 2h26v-2H4v-3h22v-8H4v-4h14V5H4Zm20 17v4H4v-4ZM16 7v4H4V7Z"),v(e,"xmlns","http://www.w3.org/2000/svg"),v(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),v(e,"aria-hidden","true"),v(e,"role","img"),v(e,"class","iconify iconify--carbon"),v(e,"width","100%"),v(e,"height","100%"),v(e,"preserveAspectRatio","xMidYMid meet"),v(e,"viewBox","0 0 32 32")},m(l,n){ae(l,e,n),te(e,t)},p:A,i:A,o:A,d(l){l&&ne(e)}}}class X extends le{constructor(e){super(),ie(this,e,null,oe,se,{})}}const{SvelteComponent:ce,append:g,attr:m,destroy_each:fe,detach:E,element:M,empty:re,ensure_array_like:F,init:_e,insert:N,listen:ue,noop:G,safe_not_equal:de,set_data:Y,set_style:j,space:Z,text:I,toggle_class:V}=window.__gradio__svelte__internal,{createEventDispatcher:me}=window.__gradio__svelte__internal;function J(i,e,t){const l=i.slice();return l[5]=e[t],l[7]=t,l}function K(i){let e,t=F(i[0].confidences),l=[];for(let n=0;n<t.length;n+=1)l[n]=P(J(i,t,n));return{c(){for(let n=0;n<l.length;n+=1)l[n].c();e=re()},m(n,s){for(let a=0;a<l.length;a+=1)l[a]&&l[a].m(n,s);N(n,e,s)},p(n,s){if(s&13){t=F(n[0].confidences);let a;for(a=0;a<t.length;a+=1){const f=J(n,t,a);l[a]?l[a].p(f,s):(l[a]=P(f),l[a].c(),l[a].m(e.parentNode,e))}for(;a<l.length;a+=1)l[a].d(1);l.length=t.length}},d(n){n&&E(e),fe(l,n)}}}function O(i){let e,t,l=Math.round(i[5].confidence*100)+"",n,s;return{c(){e=M("div"),t=M("dd"),n=I(l),s=I("%"),m(e,"class","line svelte-1pq4gst"),m(t,"class","confidence svelte-1pq4gst")},m(a,f){N(a,e,f),N(a,t,f),g(t,n),g(t,s)},p(a,f){f&1&&l!==(l=Math.round(a[5].confidence*100)+"")&&Y(n,l)},d(a){a&&(E(e),E(t))}}}function P(i){let e,t,l,n,s,a,f,r=i[5].label+"",h,_,w,b,p,o,u,d=i[0].confidences&&O(i);function C(){return i[4](i[7],i[5])}return{c(){e=M("button"),t=M("div"),l=M("meter"),s=Z(),a=M("dl"),f=M("dt"),h=I(r),_=Z(),d&&d.c(),b=Z(),m(l,"aria-labelledby","meter-text"),m(l,"class","bar svelte-1pq4gst"),m(l,"min","0"),m(l,"max","100"),j(l,"width",i[5].confidence*100+"%"),j(l,"background","var(--stat-background-fill)"),l.value="100",m(l,"aria-label",n=Math.round(i[5].confidence*100)+"%"),m(f,"id",w=`meter-text-${i[5].label}`),m(f,"class","text svelte-1pq4gst"),m(a,"class","label svelte-1pq4gst"),m(t,"class","inner-wrap svelte-1pq4gst"),m(e,"class","confidence-set group svelte-1pq4gst"),m(e,"data-testid",p=`${i[5].label}-confidence-set`),V(e,"selectable",i[2])},m(S,c){N(S,e,c),g(e,t),g(t,l),g(t,s),g(t,a),g(a,f),g(f,h),g(f,_),d&&d.m(a,null),g(e,b),o||(u=ue(e,"click",C),o=!0)},p(S,c){i=S,c&1&&j(l,"width",i[5].confidence*100+"%"),c&1&&n!==(n=Math.round(i[5].confidence*100)+"%")&&m(l,"aria-label",n),c&1&&r!==(r=i[5].label+"")&&Y(h,r),c&1&&w!==(w=`meter-text-${i[5].label}`)&&m(f,"id",w),i[0].confidences?d?d.p(i,c):(d=O(i),d.c(),d.m(a,null)):d&&(d.d(1),d=null),c&1&&p!==(p=`${i[5].label}-confidence-set`)&&m(e,"data-testid",p),c&4&&V(e,"selectable",i[2])},d(S){S&&E(e),d&&d.d(),o=!1,u()}}}function be(i){let e,t,l=i[0].label+"",n,s,a=typeof i[0]=="object"&&i[0].confidences&&K(i);return{c(){e=M("div"),t=M("h2"),n=I(l),s=Z(),a&&a.c(),m(t,"class","output-class svelte-1pq4gst"),m(t,"data-testid","label-output-value"),V(t,"no-confidence",!("confidences"in i[0])),j(t,"background-color",i[1]||"transparent"),m(e,"class","container svelte-1pq4gst")},m(f,r){N(f,e,r),g(e,t),g(t,n),g(e,s),a&&a.m(e,null)},p(f,[r]){r&1&&l!==(l=f[0].label+"")&&Y(n,l),r&1&&V(t,"no-confidence",!("confidences"in f[0])),r&2&&j(t,"background-color",f[1]||"transparent"),typeof f[0]=="object"&&f[0].confidences?a?a.p(f,r):(a=K(f),a.c(),a.m(e,null)):a&&(a.d(1),a=null)},i:G,o:G,d(f){f&&E(e),a&&a.d()}}}function ge(i,e,t){let{value:l}=e;const n=me();let{color:s=void 0}=e,{selectable:a=!1}=e;const f=(r,h)=>{n("select",{index:r,value:h.label})};return i.$$set=r=>{"value"in r&&t(0,l=r.value),"color"in r&&t(1,s=r.color),"selectable"in r&&t(2,a=r.selectable)},[l,s,a,n,f]}class he extends ce{constructor(e){super(),_e(this,e,ge,be,de,{value:0,color:1,selectable:2})}}const ve=he,{SvelteComponent:ke,assign:we,check_outros:Q,create_component:B,destroy_component:H,detach:D,empty:pe,get_spread_object:qe,get_spread_update:Me,group_outros:T,init:Ce,insert:R,mount_component:L,safe_not_equal:Se,space:U,transition_in:k,transition_out:q}=window.__gradio__svelte__internal;function W(i){let e,t;return e=new x({props:{Icon:X,label:i[6],disable:i[7]===!1}}),{c(){B(e.$$.fragment)},m(l,n){L(e,l,n),t=!0},p(l,n){const s={};n&64&&(s.label=l[6]),n&128&&(s.disable=l[7]===!1),e.$set(s)},i(l){t||(k(e.$$.fragment,l),t=!0)},o(l){q(e.$$.fragment,l),t=!1},d(l){H(e,l)}}}function Be(i){let e,t;return e=new ee({props:{unpadded_box:!0,$$slots:{default:[Le]},$$scope:{ctx:i}}}),{c(){B(e.$$.fragment)},m(l,n){L(e,l,n),t=!0},p(l,n){const s={};n&65536&&(s.$$scope={dirty:n,ctx:l}),e.$set(s)},i(l){t||(k(e.$$.fragment,l),t=!0)},o(l){q(e.$$.fragment,l),t=!1},d(l){H(e,l)}}}function He(i){let e,t;return e=new ve({props:{selectable:i[12],value:i[5],color:i[4]}}),e.$on("select",i[15]),{c(){B(e.$$.fragment)},m(l,n){L(e,l,n),t=!0},p(l,n){const s={};n&4096&&(s.selectable=l[12]),n&32&&(s.value=l[5]),n&16&&(s.color=l[4]),e.$set(s)},i(l){t||(k(e.$$.fragment,l),t=!0)},o(l){q(e.$$.fragment,l),t=!1},d(l){H(e,l)}}}function Le(i){let e,t;return e=new X({}),{c(){B(e.$$.fragment)},m(l,n){L(e,l,n),t=!0},i(l){t||(k(e.$$.fragment,l),t=!0)},o(l){q(e.$$.fragment,l),t=!1},d(l){H(e,l)}}}function je(i){let e,t,l,n,s,a,f;const r=[{autoscroll:i[0].autoscroll},{i18n:i[0].i18n},i[10]];let h={};for(let o=0;o<r.length;o+=1)h=we(h,r[o]);e=new y({props:h});let _=i[11]&&W(i);const w=[He,Be],b=[];function p(o,u){return o[13]!==void 0&&o[13]!==null?0:1}return n=p(i),s=b[n]=w[n](i),{c(){B(e.$$.fragment),t=U(),_&&_.c(),l=U(),s.c(),a=pe()},m(o,u){L(e,o,u),R(o,t,u),_&&_.m(o,u),R(o,l,u),b[n].m(o,u),R(o,a,u),f=!0},p(o,u){const d=u&1025?Me(r,[u&1&&{autoscroll:o[0].autoscroll},u&1&&{i18n:o[0].i18n},u&1024&&qe(o[10])]):{};e.$set(d),o[11]?_?(_.p(o,u),u&2048&&k(_,1)):(_=W(o),_.c(),k(_,1),_.m(l.parentNode,l)):_&&(T(),q(_,1,1,()=>{_=null}),Q());let C=n;n=p(o),n===C?b[n].p(o,u):(T(),q(b[C],1,1,()=>{b[C]=null}),Q(),s=b[n],s?s.p(o,u):(s=b[n]=w[n](o),s.c()),k(s,1),s.m(a.parentNode,a))},i(o){f||(k(e.$$.fragment,o),k(_),k(s),f=!0)},o(o){q(e.$$.fragment,o),q(_),q(s),f=!1},d(o){o&&(D(t),D(l),D(a)),H(e,o),_&&_.d(o),b[n].d(o)}}}function Ee(i){let e,t;return e=new $({props:{test_id:"label",visible:i[3],elem_id:i[1],elem_classes:i[2],container:i[7],scale:i[8],min_width:i[9],padding:!1,$$slots:{default:[je]},$$scope:{ctx:i}}}),{c(){B(e.$$.fragment)},m(l,n){L(e,l,n),t=!0},p(l,[n]){const s={};n&8&&(s.visible=l[3]),n&2&&(s.elem_id=l[1]),n&4&&(s.elem_classes=l[2]),n&128&&(s.container=l[7]),n&256&&(s.scale=l[8]),n&512&&(s.min_width=l[9]),n&81137&&(s.$$scope={dirty:n,ctx:l}),e.$set(s)},i(l){t||(k(e.$$.fragment,l),t=!0)},o(l){q(e.$$.fragment,l),t=!1},d(l){H(e,l)}}}function Ne(i,e,t){let l,n,{gradio:s}=e,{elem_id:a=""}=e,{elem_classes:f=[]}=e,{visible:r=!0}=e,{color:h=void 0}=e,{value:_={}}=e,{label:w=s.i18n("label.label")}=e,{container:b=!0}=e,{scale:p=null}=e,{min_width:o=void 0}=e,{loading_status:u}=e,{show_label:d=!0}=e,{_selectable:C=!1}=e;const S=({detail:c})=>s.dispatch("select",c);return i.$$set=c=>{"gradio"in c&&t(0,s=c.gradio),"elem_id"in c&&t(1,a=c.elem_id),"elem_classes"in c&&t(2,f=c.elem_classes),"visible"in c&&t(3,r=c.visible),"color"in c&&t(4,h=c.color),"value"in c&&t(5,_=c.value),"label"in c&&t(6,w=c.label),"container"in c&&t(7,b=c.container),"scale"in c&&t(8,p=c.scale),"min_width"in c&&t(9,o=c.min_width),"loading_status"in c&&t(10,u=c.loading_status),"show_label"in c&&t(11,d=c.show_label),"_selectable"in c&&t(12,C=c._selectable)},i.$$.update=()=>{i.$$.dirty&32&&t(14,{confidences:l,label:n}=_,l,(t(13,n),t(5,_))),i.$$.dirty&24577&&s.dispatch("change")},[s,a,f,r,h,_,w,b,p,o,u,d,C,n,l,S]}class Ye extends ke{constructor(e){super(),Ce(this,e,Ne,Ee,Se,{gradio:0,elem_id:1,elem_classes:2,visible:3,color:4,value:5,label:6,container:7,scale:8,min_width:9,loading_status:10,show_label:11,_selectable:12})}}export{ve as BaseLabel,Ye as default};
//# sourceMappingURL=Index-d137a234.js.map
