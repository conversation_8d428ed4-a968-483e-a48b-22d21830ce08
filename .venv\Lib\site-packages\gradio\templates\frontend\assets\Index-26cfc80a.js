import{m as Yi,a as sr,_ as lr}from"./index-a80d931b.js";function Le(){}const wc=e=>e;function Wi(e){return e()}function Zi(e){e.forEach(Wi)}function Ki(e){return typeof e=="function"}function Ji(e,t){return e!=e?t==t:e!==t||e&&typeof e=="object"||typeof e=="function"}function en(e,...t){if(e==null){for(const n of t)n(void 0);return Le}const r=e.subscribe(...t);return r.unsubscribe?()=>r.unsubscribe():r}function Sc(e){let t;return en(e,r=>t=r)(),t}function Tc(e){const t=typeof e=="string"&&e.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return t?[parseFloat(t[1]),t[2]||"px"]:[e,"px"]}const tn=typeof window<"u";let ur=tn?()=>window.performance.now():()=>Date.now(),rn=tn?e=>requestAnimationFrame(e):Le;const Ve=new Set;function nn(e){Ve.forEach(t=>{t.c(e)||(Ve.delete(t),t.f())}),Ve.size!==0&&rn(nn)}function Qi(e){let t;return Ve.size===0&&rn(nn),{promise:new Promise(r=>{Ve.add(t={c:e,f:r})}),abort(){Ve.delete(t)}}}const je=[];function $i(e,t){return{subscribe:Ke(e,t).subscribe}}function Ke(e,t=Le){let r;const n=new Set;function o(l){if(Ji(e,l)&&(e=l,r)){const c=!je.length;for(const s of n)s[1](),je.push(s,e);if(c){for(let s=0;s<je.length;s+=2)je[s][0](je[s+1]);je.length=0}}}function i(l){o(l(e))}function a(l,c=Le){const s=[l,c];return n.add(s),n.size===1&&(r=t(o,i)||Le),l(e),()=>{n.delete(s),n.size===0&&r&&(r(),r=null)}}return{set:o,update:i,subscribe:a}}function Je(e,t,r){const n=!Array.isArray(e),o=n?[e]:e;if(!o.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const i=t.length<2;return $i(r,(a,l)=>{let c=!1;const s=[];let u=0,p=Le;const y=()=>{if(u)return;p();const d=t(n?s[0]:s,a,l);i?a(d):p=Ki(d)?d:Le},g=o.map((d,w)=>en(d,O=>{s[w]=O,u&=~(1<<w),c&&y()},()=>{u|=1<<w}));return c=!0,y(),function(){Zi(g),p(),c=!1}})}const ea=""+new URL("spaces-a79177ad.svg",import.meta.url).href;var cr=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function ta(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function ra(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var r=function n(){if(this instanceof n){var o=[null];o.push.apply(o,arguments);var i=Function.bind.apply(t,o);return new i}return t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach(function(n){var o=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(r,n,o.get?o:{enumerable:!0,get:function(){return e[n]}})}),r}var na=function(t){return oa(t)&&!ia(t)};function oa(e){return!!e&&typeof e=="object"}function ia(e){var t=Object.prototype.toString.call(e);return t==="[object RegExp]"||t==="[object Date]"||la(e)}var aa=typeof Symbol=="function"&&Symbol.for,sa=aa?Symbol.for("react.element"):60103;function la(e){return e.$$typeof===sa}function ua(e){return Array.isArray(e)?[]:{}}function st(e,t){return t.clone!==!1&&t.isMergeableObject(e)?qe(ua(e),e,t):e}function ca(e,t,r){return e.concat(t).map(function(n){return st(n,r)})}function _a(e,t){if(!t.customMerge)return qe;var r=t.customMerge(e);return typeof r=="function"?r:qe}function fa(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter(function(t){return Object.propertyIsEnumerable.call(e,t)}):[]}function _r(e){return Object.keys(e).concat(fa(e))}function on(e,t){try{return t in e}catch{return!1}}function ha(e,t){return on(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))}function da(e,t,r){var n={};return r.isMergeableObject(e)&&_r(e).forEach(function(o){n[o]=st(e[o],r)}),_r(t).forEach(function(o){ha(e,o)||(on(e,o)&&r.isMergeableObject(t[o])?n[o]=_a(o,r)(e[o],t[o],r):n[o]=st(t[o],r))}),n}function qe(e,t,r){r=r||{},r.arrayMerge=r.arrayMerge||ca,r.isMergeableObject=r.isMergeableObject||na,r.cloneUnlessOtherwiseSpecified=st;var n=Array.isArray(t),o=Array.isArray(e),i=n===o;return i?n?r.arrayMerge(e,t,r):da(e,t,r):st(t,r)}qe.all=function(t,r){if(!Array.isArray(t))throw new Error("first argument should be an array");return t.reduce(function(n,o){return qe(n,o,r)},{})};var ma=qe,pa=ma;const ga=ta(pa);var tr={},an={exports:{}};(function(e){var t,r,n,o,i,a,l,c,s,u,p,y,g,d,w,O,T,x,b,P,I,j,V,X,z,Z,se,le,Y,ne,F;(function(C){var we=typeof cr=="object"?cr:typeof self=="object"?self:typeof this=="object"?this:{};C(ue(we,ue(e.exports)));function ue(ce,f){return ce!==we&&(typeof Object.create=="function"?Object.defineProperty(ce,"__esModule",{value:!0}):ce.__esModule=!0),function(h,m){return ce[h]=f?f(h,m):m}}})(function(C){var we=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,h){f.__proto__=h}||function(f,h){for(var m in h)Object.prototype.hasOwnProperty.call(h,m)&&(f[m]=h[m])};t=function(f,h){if(typeof h!="function"&&h!==null)throw new TypeError("Class extends value "+String(h)+" is not a constructor or null");we(f,h);function m(){this.constructor=f}f.prototype=h===null?Object.create(h):(m.prototype=h.prototype,new m)},r=Object.assign||function(f){for(var h,m=1,_=arguments.length;m<_;m++){h=arguments[m];for(var E in h)Object.prototype.hasOwnProperty.call(h,E)&&(f[E]=h[E])}return f},n=function(f,h){var m={};for(var _ in f)Object.prototype.hasOwnProperty.call(f,_)&&h.indexOf(_)<0&&(m[_]=f[_]);if(f!=null&&typeof Object.getOwnPropertySymbols=="function")for(var E=0,_=Object.getOwnPropertySymbols(f);E<_.length;E++)h.indexOf(_[E])<0&&Object.prototype.propertyIsEnumerable.call(f,_[E])&&(m[_[E]]=f[_[E]]);return m},o=function(f,h,m,_){var E=arguments.length,v=E<3?h:_===null?_=Object.getOwnPropertyDescriptor(h,m):_,k;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(f,h,m,_);else for(var D=f.length-1;D>=0;D--)(k=f[D])&&(v=(E<3?k(v):E>3?k(h,m,v):k(h,m))||v);return E>3&&v&&Object.defineProperty(h,m,v),v},i=function(f,h){return function(m,_){h(m,_,f)}},a=function(f,h,m,_,E,v){function k(S){if(S!==void 0&&typeof S!="function")throw new TypeError("Function expected");return S}for(var D=_.kind,Q=D==="getter"?"get":D==="setter"?"set":"value",H=!h&&f?_.static?f:f.prototype:null,G=h||(H?Object.getOwnPropertyDescriptor(H,_.name):{}),W,q=!1,_e=m.length-1;_e>=0;_e--){var be={};for(var ve in _)be[ve]=ve==="access"?{}:_[ve];for(var ve in _.access)be.access[ve]=_.access[ve];be.addInitializer=function(S){if(q)throw new TypeError("Cannot add initializers after decoration has completed");v.push(k(S||null))};var Se=(0,m[_e])(D==="accessor"?{get:G.get,set:G.set}:G[Q],be);if(D==="accessor"){if(Se===void 0)continue;if(Se===null||typeof Se!="object")throw new TypeError("Object expected");(W=k(Se.get))&&(G.get=W),(W=k(Se.set))&&(G.set=W),(W=k(Se.init))&&E.unshift(W)}else(W=k(Se))&&(D==="field"?E.unshift(W):G[Q]=W)}H&&Object.defineProperty(H,_.name,G),q=!0},l=function(f,h,m){for(var _=arguments.length>2,E=0;E<h.length;E++)m=_?h[E].call(f,m):h[E].call(f);return _?m:void 0},c=function(f){return typeof f=="symbol"?f:"".concat(f)},s=function(f,h,m){return typeof h=="symbol"&&(h=h.description?"[".concat(h.description,"]"):""),Object.defineProperty(f,"name",{configurable:!0,value:m?"".concat(m," ",h):h})},u=function(f,h){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(f,h)},p=function(f,h,m,_){function E(v){return v instanceof m?v:new m(function(k){k(v)})}return new(m||(m=Promise))(function(v,k){function D(G){try{H(_.next(G))}catch(W){k(W)}}function Q(G){try{H(_.throw(G))}catch(W){k(W)}}function H(G){G.done?v(G.value):E(G.value).then(D,Q)}H((_=_.apply(f,h||[])).next())})},y=function(f,h){var m={label:0,sent:function(){if(v[0]&1)throw v[1];return v[1]},trys:[],ops:[]},_,E,v,k;return k={next:D(0),throw:D(1),return:D(2)},typeof Symbol=="function"&&(k[Symbol.iterator]=function(){return this}),k;function D(H){return function(G){return Q([H,G])}}function Q(H){if(_)throw new TypeError("Generator is already executing.");for(;k&&(k=0,H[0]&&(m=0)),m;)try{if(_=1,E&&(v=H[0]&2?E.return:H[0]?E.throw||((v=E.return)&&v.call(E),0):E.next)&&!(v=v.call(E,H[1])).done)return v;switch(E=0,v&&(H=[H[0]&2,v.value]),H[0]){case 0:case 1:v=H;break;case 4:return m.label++,{value:H[1],done:!1};case 5:m.label++,E=H[1],H=[0];continue;case 7:H=m.ops.pop(),m.trys.pop();continue;default:if(v=m.trys,!(v=v.length>0&&v[v.length-1])&&(H[0]===6||H[0]===2)){m=0;continue}if(H[0]===3&&(!v||H[1]>v[0]&&H[1]<v[3])){m.label=H[1];break}if(H[0]===6&&m.label<v[1]){m.label=v[1],v=H;break}if(v&&m.label<v[2]){m.label=v[2],m.ops.push(H);break}v[2]&&m.ops.pop(),m.trys.pop();continue}H=h.call(f,m)}catch(G){H=[6,G],E=0}finally{_=v=0}if(H[0]&5)throw H[1];return{value:H[0]?H[1]:void 0,done:!0}}},g=function(f,h){for(var m in f)m!=="default"&&!Object.prototype.hasOwnProperty.call(h,m)&&Y(h,f,m)},Y=Object.create?function(f,h,m,_){_===void 0&&(_=m);var E=Object.getOwnPropertyDescriptor(h,m);(!E||("get"in E?!h.__esModule:E.writable||E.configurable))&&(E={enumerable:!0,get:function(){return h[m]}}),Object.defineProperty(f,_,E)}:function(f,h,m,_){_===void 0&&(_=m),f[_]=h[m]},d=function(f){var h=typeof Symbol=="function"&&Symbol.iterator,m=h&&f[h],_=0;if(m)return m.call(f);if(f&&typeof f.length=="number")return{next:function(){return f&&_>=f.length&&(f=void 0),{value:f&&f[_++],done:!f}}};throw new TypeError(h?"Object is not iterable.":"Symbol.iterator is not defined.")},w=function(f,h){var m=typeof Symbol=="function"&&f[Symbol.iterator];if(!m)return f;var _=m.call(f),E,v=[],k;try{for(;(h===void 0||h-- >0)&&!(E=_.next()).done;)v.push(E.value)}catch(D){k={error:D}}finally{try{E&&!E.done&&(m=_.return)&&m.call(_)}finally{if(k)throw k.error}}return v},O=function(){for(var f=[],h=0;h<arguments.length;h++)f=f.concat(w(arguments[h]));return f},T=function(){for(var f=0,h=0,m=arguments.length;h<m;h++)f+=arguments[h].length;for(var _=Array(f),E=0,h=0;h<m;h++)for(var v=arguments[h],k=0,D=v.length;k<D;k++,E++)_[E]=v[k];return _},x=function(f,h,m){if(m||arguments.length===2)for(var _=0,E=h.length,v;_<E;_++)(v||!(_ in h))&&(v||(v=Array.prototype.slice.call(h,0,_)),v[_]=h[_]);return f.concat(v||Array.prototype.slice.call(h))},b=function(f){return this instanceof b?(this.v=f,this):new b(f)},P=function(f,h,m){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var _=m.apply(f,h||[]),E,v=[];return E={},k("next"),k("throw"),k("return"),E[Symbol.asyncIterator]=function(){return this},E;function k(q){_[q]&&(E[q]=function(_e){return new Promise(function(be,ve){v.push([q,_e,be,ve])>1||D(q,_e)})})}function D(q,_e){try{Q(_[q](_e))}catch(be){W(v[0][3],be)}}function Q(q){q.value instanceof b?Promise.resolve(q.value.v).then(H,G):W(v[0][2],q)}function H(q){D("next",q)}function G(q){D("throw",q)}function W(q,_e){q(_e),v.shift(),v.length&&D(v[0][0],v[0][1])}},I=function(f){var h,m;return h={},_("next"),_("throw",function(E){throw E}),_("return"),h[Symbol.iterator]=function(){return this},h;function _(E,v){h[E]=f[E]?function(k){return(m=!m)?{value:b(f[E](k)),done:!1}:v?v(k):k}:v}},j=function(f){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var h=f[Symbol.asyncIterator],m;return h?h.call(f):(f=typeof d=="function"?d(f):f[Symbol.iterator](),m={},_("next"),_("throw"),_("return"),m[Symbol.asyncIterator]=function(){return this},m);function _(v){m[v]=f[v]&&function(k){return new Promise(function(D,Q){k=f[v](k),E(D,Q,k.done,k.value)})}}function E(v,k,D,Q){Promise.resolve(Q).then(function(H){v({value:H,done:D})},k)}},V=function(f,h){return Object.defineProperty?Object.defineProperty(f,"raw",{value:h}):f.raw=h,f};var ue=Object.create?function(f,h){Object.defineProperty(f,"default",{enumerable:!0,value:h})}:function(f,h){f.default=h};X=function(f){if(f&&f.__esModule)return f;var h={};if(f!=null)for(var m in f)m!=="default"&&Object.prototype.hasOwnProperty.call(f,m)&&Y(h,f,m);return ue(h,f),h},z=function(f){return f&&f.__esModule?f:{default:f}},Z=function(f,h,m,_){if(m==="a"&&!_)throw new TypeError("Private accessor was defined without a getter");if(typeof h=="function"?f!==h||!_:!h.has(f))throw new TypeError("Cannot read private member from an object whose class did not declare it");return m==="m"?_:m==="a"?_.call(f):_?_.value:h.get(f)},se=function(f,h,m,_,E){if(_==="m")throw new TypeError("Private method is not writable");if(_==="a"&&!E)throw new TypeError("Private accessor was defined without a setter");if(typeof h=="function"?f!==h||!E:!h.has(f))throw new TypeError("Cannot write private member to an object whose class did not declare it");return _==="a"?E.call(f,m):E?E.value=m:h.set(f,m),m},le=function(f,h){if(h===null||typeof h!="object"&&typeof h!="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof f=="function"?h===f:f.has(h)},ne=function(f,h,m){if(h!=null){if(typeof h!="object"&&typeof h!="function")throw new TypeError("Object expected.");var _;if(m){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");_=h[Symbol.asyncDispose]}if(_===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");_=h[Symbol.dispose]}if(typeof _!="function")throw new TypeError("Object not disposable.");f.stack.push({value:h,dispose:_,async:m})}else m&&f.stack.push({async:!0});return h};var ce=typeof SuppressedError=="function"?SuppressedError:function(f,h,m){var _=new Error(m);return _.name="SuppressedError",_.error=f,_.suppressed=h,_};F=function(f){function h(_){f.error=f.hasError?new ce(_,f.error,"An error was suppressed during disposal."):_,f.hasError=!0}function m(){for(;f.stack.length;){var _=f.stack.pop();try{var E=_.dispose&&_.dispose.call(_.value);if(_.async)return Promise.resolve(E).then(m,function(v){return h(v),m()})}catch(v){h(v)}}if(f.hasError)throw f.error}return m()},C("__extends",t),C("__assign",r),C("__rest",n),C("__decorate",o),C("__param",i),C("__esDecorate",a),C("__runInitializers",l),C("__propKey",c),C("__setFunctionName",s),C("__metadata",u),C("__awaiter",p),C("__generator",y),C("__exportStar",g),C("__createBinding",Y),C("__values",d),C("__read",w),C("__spread",O),C("__spreadArrays",T),C("__spreadArray",x),C("__await",b),C("__asyncGenerator",P),C("__asyncDelegator",I),C("__asyncValues",j),C("__makeTemplateObject",V),C("__importStar",X),C("__importDefault",z),C("__classPrivateFieldGet",Z),C("__classPrivateFieldSet",se),C("__classPrivateFieldIn",le),C("__addDisposableResource",ne),C("__disposeResources",F)})})(an);var Re=an.exports,lt={},rr={},ht={};Object.defineProperty(ht,"__esModule",{value:!0});ht.ErrorKind=void 0;var fr;(function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"})(fr||(ht.ErrorKind=fr={}));var xt={},N={};Object.defineProperty(N,"__esModule",{value:!0});N.createNumberElement=N.createLiteralElement=N.isDateTimeSkeleton=N.isNumberSkeleton=N.isTagElement=N.isPoundElement=N.isPluralElement=N.isSelectElement=N.isTimeElement=N.isDateElement=N.isNumberElement=N.isArgumentElement=N.isLiteralElement=N.SKELETON_TYPE=N.TYPE=void 0;var ae;(function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"})(ae||(N.TYPE=ae={}));var St;(function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"})(St||(N.SKELETON_TYPE=St={}));function ba(e){return e.type===ae.literal}N.isLiteralElement=ba;function va(e){return e.type===ae.argument}N.isArgumentElement=va;function ya(e){return e.type===ae.number}N.isNumberElement=ya;function Ea(e){return e.type===ae.date}N.isDateElement=Ea;function wa(e){return e.type===ae.time}N.isTimeElement=wa;function Sa(e){return e.type===ae.select}N.isSelectElement=Sa;function Ta(e){return e.type===ae.plural}N.isPluralElement=Ta;function Pa(e){return e.type===ae.pound}N.isPoundElement=Pa;function Oa(e){return e.type===ae.tag}N.isTagElement=Oa;function ka(e){return!!(e&&typeof e=="object"&&e.type===St.number)}N.isNumberSkeleton=ka;function xa(e){return!!(e&&typeof e=="object"&&e.type===St.dateTime)}N.isDateTimeSkeleton=xa;function Ha(e){return{type:ae.literal,value:e}}N.createLiteralElement=Ha;function Aa(e,t){return{type:ae.number,value:e,style:t}}N.createNumberElement=Aa;var Xe={};Object.defineProperty(Xe,"__esModule",{value:!0});Xe.WHITE_SPACE_REGEX=Xe.SPACE_SEPARATOR_REGEX=void 0;Xe.SPACE_SEPARATOR_REGEX=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/;Xe.WHITE_SPACE_REGEX=/[\t-\r \x85\u200E\u200F\u2028\u2029]/;var sn={},tt={},hr;function Ba(){if(hr)return tt;hr=1,Object.defineProperty(tt,"__esModule",{value:!0}),tt.parseDateTimeSkeleton=void 0;var e=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;function t(r){var n={};return r.replace(e,function(o){var i=o.length;switch(o[0]){case"G":n.era=i===4?"long":i===5?"narrow":"short";break;case"y":n.year=i===2?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw new RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw new RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":n.month=["numeric","2-digit","short","long","narrow"][i-1];break;case"w":case"W":throw new RangeError("`w/W` (week) patterns are not supported");case"d":n.day=["numeric","2-digit"][i-1];break;case"D":case"F":case"g":throw new RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":n.weekday=i===4?"long":i===5?"narrow":"short";break;case"e":if(i<4)throw new RangeError("`e..eee` (weekday) patterns are not supported");n.weekday=["short","long","narrow","short"][i-4];break;case"c":if(i<4)throw new RangeError("`c..ccc` (weekday) patterns are not supported");n.weekday=["short","long","narrow","short"][i-4];break;case"a":n.hour12=!0;break;case"b":case"B":throw new RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":n.hourCycle="h12",n.hour=["numeric","2-digit"][i-1];break;case"H":n.hourCycle="h23",n.hour=["numeric","2-digit"][i-1];break;case"K":n.hourCycle="h11",n.hour=["numeric","2-digit"][i-1];break;case"k":n.hourCycle="h24",n.hour=["numeric","2-digit"][i-1];break;case"j":case"J":case"C":throw new RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":n.minute=["numeric","2-digit"][i-1];break;case"s":n.second=["numeric","2-digit"][i-1];break;case"S":case"A":throw new RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":n.timeZoneName=i<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw new RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),n}return tt.parseDateTimeSkeleton=t,tt}var Ne={},rt={},dr;function Ca(){return dr||(dr=1,Object.defineProperty(rt,"__esModule",{value:!0}),rt.WHITE_SPACE_REGEX=void 0,rt.WHITE_SPACE_REGEX=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i),rt}var mr;function Ma(){if(mr)return Ne;mr=1,Object.defineProperty(Ne,"__esModule",{value:!0}),Ne.parseNumberSkeleton=Ne.parseNumberSkeletonFromString=void 0;var e=Re,t=Ca();function r(g){if(g.length===0)throw new Error("Number skeleton cannot be empty");for(var d=g.split(t.WHITE_SPACE_REGEX).filter(function(z){return z.length>0}),w=[],O=0,T=d;O<T.length;O++){var x=T[O],b=x.split("/");if(b.length===0)throw new Error("Invalid number skeleton");for(var P=b[0],I=b.slice(1),j=0,V=I;j<V.length;j++){var X=V[j];if(X.length===0)throw new Error("Invalid number skeleton")}w.push({stem:P,options:I})}return w}Ne.parseNumberSkeletonFromString=r;function n(g){return g.replace(/^(.*?)-/,"")}var o=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,i=/^(@+)?(\+|#+)?[rs]?$/g,a=/(\*)(0+)|(#+)(0+)|(0+)/g,l=/^(0+)$/;function c(g){var d={};return g[g.length-1]==="r"?d.roundingPriority="morePrecision":g[g.length-1]==="s"&&(d.roundingPriority="lessPrecision"),g.replace(i,function(w,O,T){return typeof T!="string"?(d.minimumSignificantDigits=O.length,d.maximumSignificantDigits=O.length):T==="+"?d.minimumSignificantDigits=O.length:O[0]==="#"?d.maximumSignificantDigits=O.length:(d.minimumSignificantDigits=O.length,d.maximumSignificantDigits=O.length+(typeof T=="string"?T.length:0)),""}),d}function s(g){switch(g){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function u(g){var d;if(g[0]==="E"&&g[1]==="E"?(d={notation:"engineering"},g=g.slice(2)):g[0]==="E"&&(d={notation:"scientific"},g=g.slice(1)),d){var w=g.slice(0,2);if(w==="+!"?(d.signDisplay="always",g=g.slice(2)):w==="+?"&&(d.signDisplay="exceptZero",g=g.slice(2)),!l.test(g))throw new Error("Malformed concise eng/scientific notation");d.minimumIntegerDigits=g.length}return d}function p(g){var d={},w=s(g);return w||d}function y(g){for(var d={},w=0,O=g;w<O.length;w++){var T=O[w];switch(T.stem){case"percent":case"%":d.style="percent";continue;case"%x100":d.style="percent",d.scale=100;continue;case"currency":d.style="currency",d.currency=T.options[0];continue;case"group-off":case",_":d.useGrouping=!1;continue;case"precision-integer":case".":d.maximumFractionDigits=0;continue;case"measure-unit":case"unit":d.style="unit",d.unit=n(T.options[0]);continue;case"compact-short":case"K":d.notation="compact",d.compactDisplay="short";continue;case"compact-long":case"KK":d.notation="compact",d.compactDisplay="long";continue;case"scientific":d=e.__assign(e.__assign(e.__assign({},d),{notation:"scientific"}),T.options.reduce(function(I,j){return e.__assign(e.__assign({},I),p(j))},{}));continue;case"engineering":d=e.__assign(e.__assign(e.__assign({},d),{notation:"engineering"}),T.options.reduce(function(I,j){return e.__assign(e.__assign({},I),p(j))},{}));continue;case"notation-simple":d.notation="standard";continue;case"unit-width-narrow":d.currencyDisplay="narrowSymbol",d.unitDisplay="narrow";continue;case"unit-width-short":d.currencyDisplay="code",d.unitDisplay="short";continue;case"unit-width-full-name":d.currencyDisplay="name",d.unitDisplay="long";continue;case"unit-width-iso-code":d.currencyDisplay="symbol";continue;case"scale":d.scale=parseFloat(T.options[0]);continue;case"integer-width":if(T.options.length>1)throw new RangeError("integer-width stems only accept a single optional option");T.options[0].replace(a,function(I,j,V,X,z,Z){if(j)d.minimumIntegerDigits=V.length;else{if(X&&z)throw new Error("We currently do not support maximum integer digits");if(Z)throw new Error("We currently do not support exact integer digits")}return""});continue}if(l.test(T.stem)){d.minimumIntegerDigits=T.stem.length;continue}if(o.test(T.stem)){if(T.options.length>1)throw new RangeError("Fraction-precision stems only accept a single optional option");T.stem.replace(o,function(I,j,V,X,z,Z){return V==="*"?d.minimumFractionDigits=j.length:X&&X[0]==="#"?d.maximumFractionDigits=X.length:z&&Z?(d.minimumFractionDigits=z.length,d.maximumFractionDigits=z.length+Z.length):(d.minimumFractionDigits=j.length,d.maximumFractionDigits=j.length),""});var x=T.options[0];x==="w"?d=e.__assign(e.__assign({},d),{trailingZeroDisplay:"stripIfInteger"}):x&&(d=e.__assign(e.__assign({},d),c(x)));continue}if(i.test(T.stem)){d=e.__assign(e.__assign({},d),c(T.stem));continue}var b=s(T.stem);b&&(d=e.__assign(e.__assign({},d),b));var P=u(T.stem);P&&(d=e.__assign(e.__assign({},d),P))}return d}return Ne.parseNumberSkeleton=y,Ne}(function(e){Object.defineProperty(e,"__esModule",{value:!0});var t=Re;t.__exportStar(Ba(),e),t.__exportStar(Ma(),e)})(sn);var Ht={},At={};Object.defineProperty(At,"__esModule",{value:!0});At.timeData=void 0;At.timeData={"001":["H","h"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["H","h","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["H","hB","h","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["H","h","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["H","h","hB","hb"],CU:["H","h","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["H","hB","h","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["H","h","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["H","h","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["H","h","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["H","h","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["H","hB","h","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["H","h","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["H","h","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["H","h","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"es-BO":["H","h","hB","hb"],"es-BR":["H","h","hB","hb"],"es-EC":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"es-PE":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]};Object.defineProperty(Ht,"__esModule",{value:!0});Ht.getBestPattern=void 0;var pt=At;function Na(e,t){for(var r="",n=0;n<e.length;n++){var o=e.charAt(n);if(o==="j"){for(var i=0;n+1<e.length&&e.charAt(n+1)===o;)i++,n++;var a=1+(i&1),l=i<2?1:3+(i>>1),c="a",s=Ia(t);for((s=="H"||s=="k")&&(l=0);l-- >0;)r+=c;for(;a-- >0;)r=s+r}else o==="J"?r+="H":r+=o}return r}Ht.getBestPattern=Na;function Ia(e){var t=e.hourCycle;if(t===void 0&&e.hourCycles&&e.hourCycles.length&&(t=e.hourCycles[0]),t)switch(t){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw new Error("Invalid hourCycle")}var r=e.language,n;r!=="root"&&(n=e.maximize().region);var o=pt.timeData[n||""]||pt.timeData[r||""]||pt.timeData["".concat(r,"-001")]||pt.timeData["001"];return o[0]}var jt;Object.defineProperty(xt,"__esModule",{value:!0});xt.Parser=void 0;var La=Re,R=ht,ee=N,ln=Xe,Dt=sn,Ra=Ht,ja=new RegExp("^".concat(ln.SPACE_SEPARATOR_REGEX.source,"*")),Da=new RegExp("".concat(ln.SPACE_SEPARATOR_REGEX.source,"*$"));function M(e,t){return{start:e,end:t}}var Fa=!!String.prototype.startsWith&&"_a".startsWith("a",1),Ga=!!String.fromCodePoint,Ua=!!Object.fromEntries,Va=!!String.prototype.codePointAt,za=!!String.prototype.trimStart,qa=!!String.prototype.trimEnd,Xa=!!Number.isSafeInteger,Ya=Xa?Number.isSafeInteger:function(e){return typeof e=="number"&&isFinite(e)&&Math.floor(e)===e&&Math.abs(e)<=9007199254740991},qt=!0;try{var Wa=cn("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");qt=((jt=Wa.exec("a"))===null||jt===void 0?void 0:jt[0])==="a"}catch{qt=!1}var pr=Fa?function(t,r,n){return t.startsWith(r,n)}:function(t,r,n){return t.slice(n,n+r.length)===r},Xt=Ga?String.fromCodePoint:function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n="",o=t.length,i=0,a;o>i;){if(a=t[i++],a>1114111)throw RangeError(a+" is not a valid code point");n+=a<65536?String.fromCharCode(a):String.fromCharCode(((a-=65536)>>10)+55296,a%1024+56320)}return n},gr=Ua?Object.fromEntries:function(t){for(var r={},n=0,o=t;n<o.length;n++){var i=o[n],a=i[0],l=i[1];r[a]=l}return r},un=Va?function(t,r){return t.codePointAt(r)}:function(t,r){var n=t.length;if(!(r<0||r>=n)){var o=t.charCodeAt(r),i;return o<55296||o>56319||r+1===n||(i=t.charCodeAt(r+1))<56320||i>57343?o:(o-55296<<10)+(i-56320)+65536}},Za=za?function(t){return t.trimStart()}:function(t){return t.replace(ja,"")},Ka=qa?function(t){return t.trimEnd()}:function(t){return t.replace(Da,"")};function cn(e,t){return new RegExp(e,t)}var Yt;if(qt){var br=cn("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");Yt=function(t,r){var n;br.lastIndex=r;var o=br.exec(t);return(n=o[1])!==null&&n!==void 0?n:""}}else Yt=function(t,r){for(var n=[];;){var o=un(t,r);if(o===void 0||_n(o)||es(o))break;n.push(o),r+=o>=65536?2:1}return Xt.apply(void 0,n)};var Ja=function(){function e(t,r){r===void 0&&(r={}),this.message=t,this.position={offset:0,line:1,column:1},this.ignoreTag=!!r.ignoreTag,this.locale=r.locale,this.requiresOtherClause=!!r.requiresOtherClause,this.shouldParseSkeletons=!!r.shouldParseSkeletons}return e.prototype.parse=function(){if(this.offset()!==0)throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(t,r,n){for(var o=[];!this.isEOF();){var i=this.char();if(i===123){var a=this.parseArgument(t,n);if(a.err)return a;o.push(a.val)}else{if(i===125&&t>0)break;if(i===35&&(r==="plural"||r==="selectordinal")){var l=this.clonePosition();this.bump(),o.push({type:ee.TYPE.pound,location:M(l,this.clonePosition())})}else if(i===60&&!this.ignoreTag&&this.peek()===47){if(n)break;return this.error(R.ErrorKind.UNMATCHED_CLOSING_TAG,M(this.clonePosition(),this.clonePosition()))}else if(i===60&&!this.ignoreTag&&Wt(this.peek()||0)){var a=this.parseTag(t,r);if(a.err)return a;o.push(a.val)}else{var a=this.parseLiteral(t,r);if(a.err)return a;o.push(a.val)}}}return{val:o,err:null}},e.prototype.parseTag=function(t,r){var n=this.clonePosition();this.bump();var o=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:ee.TYPE.literal,value:"<".concat(o,"/>"),location:M(n,this.clonePosition())},err:null};if(this.bumpIf(">")){var i=this.parseMessage(t+1,r,!0);if(i.err)return i;var a=i.val,l=this.clonePosition();if(this.bumpIf("</")){if(this.isEOF()||!Wt(this.char()))return this.error(R.ErrorKind.INVALID_TAG,M(l,this.clonePosition()));var c=this.clonePosition(),s=this.parseTagName();return o!==s?this.error(R.ErrorKind.UNMATCHED_CLOSING_TAG,M(c,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">")?{val:{type:ee.TYPE.tag,value:o,children:a,location:M(n,this.clonePosition())},err:null}:this.error(R.ErrorKind.INVALID_TAG,M(l,this.clonePosition())))}else return this.error(R.ErrorKind.UNCLOSED_TAG,M(n,this.clonePosition()))}else return this.error(R.ErrorKind.INVALID_TAG,M(n,this.clonePosition()))},e.prototype.parseTagName=function(){var t=this.offset();for(this.bump();!this.isEOF()&&$a(this.char());)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(t,r){for(var n=this.clonePosition(),o="";;){var i=this.tryParseQuote(r);if(i){o+=i;continue}var a=this.tryParseUnquoted(t,r);if(a){o+=a;continue}var l=this.tryParseLeftAngleBracket();if(l){o+=l;continue}break}var c=M(n,this.clonePosition());return{val:{type:ee.TYPE.literal,value:o,location:c},err:null}},e.prototype.tryParseLeftAngleBracket=function(){return!this.isEOF()&&this.char()===60&&(this.ignoreTag||!Qa(this.peek()||0))?(this.bump(),"<"):null},e.prototype.tryParseQuote=function(t){if(this.isEOF()||this.char()!==39)return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if(t==="plural"||t==="selectordinal")break;return null;default:return null}this.bump();var r=[this.char()];for(this.bump();!this.isEOF();){var n=this.char();if(n===39)if(this.peek()===39)r.push(39),this.bump();else{this.bump();break}else r.push(n);this.bump()}return Xt.apply(void 0,r)},e.prototype.tryParseUnquoted=function(t,r){if(this.isEOF())return null;var n=this.char();return n===60||n===123||n===35&&(r==="plural"||r==="selectordinal")||n===125&&t>0?null:(this.bump(),Xt(n))},e.prototype.parseArgument=function(t,r){var n=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(R.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE,M(n,this.clonePosition()));if(this.char()===125)return this.bump(),this.error(R.ErrorKind.EMPTY_ARGUMENT,M(n,this.clonePosition()));var o=this.parseIdentifierIfPossible().value;if(!o)return this.error(R.ErrorKind.MALFORMED_ARGUMENT,M(n,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(R.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE,M(n,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:ee.TYPE.argument,value:o,location:M(n,this.clonePosition())},err:null};case 44:return this.bump(),this.bumpSpace(),this.isEOF()?this.error(R.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE,M(n,this.clonePosition())):this.parseArgumentOptions(t,r,o,n);default:return this.error(R.ErrorKind.MALFORMED_ARGUMENT,M(n,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var t=this.clonePosition(),r=this.offset(),n=Yt(this.message,r),o=r+n.length;this.bumpTo(o);var i=this.clonePosition(),a=M(t,i);return{value:n,location:a}},e.prototype.parseArgumentOptions=function(t,r,n,o){var i,a=this.clonePosition(),l=this.parseIdentifierIfPossible().value,c=this.clonePosition();switch(l){case"":return this.error(R.ErrorKind.EXPECT_ARGUMENT_TYPE,M(a,c));case"number":case"date":case"time":{this.bumpSpace();var s=null;if(this.bumpIf(",")){this.bumpSpace();var u=this.clonePosition(),p=this.parseSimpleArgStyleIfPossible();if(p.err)return p;var y=Ka(p.val);if(y.length===0)return this.error(R.ErrorKind.EXPECT_ARGUMENT_STYLE,M(this.clonePosition(),this.clonePosition()));var g=M(u,this.clonePosition());s={style:y,styleLocation:g}}var d=this.tryParseArgumentClose(o);if(d.err)return d;var w=M(o,this.clonePosition());if(s&&pr(s?.style,"::",0)){var O=Za(s.style.slice(2));if(l==="number"){var p=this.parseNumberSkeletonFromString(O,s.styleLocation);return p.err?p:{val:{type:ee.TYPE.number,value:n,location:w,style:p.val},err:null}}else{if(O.length===0)return this.error(R.ErrorKind.EXPECT_DATE_TIME_SKELETON,w);var T=O;this.locale&&(T=(0,Ra.getBestPattern)(O,this.locale));var y={type:ee.SKELETON_TYPE.dateTime,pattern:T,location:s.styleLocation,parsedOptions:this.shouldParseSkeletons?(0,Dt.parseDateTimeSkeleton)(T):{}},x=l==="date"?ee.TYPE.date:ee.TYPE.time;return{val:{type:x,value:n,location:w,style:y},err:null}}}return{val:{type:l==="number"?ee.TYPE.number:l==="date"?ee.TYPE.date:ee.TYPE.time,value:n,location:w,style:(i=s?.style)!==null&&i!==void 0?i:null},err:null}}case"plural":case"selectordinal":case"select":{var b=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(R.ErrorKind.EXPECT_SELECT_ARGUMENT_OPTIONS,M(b,La.__assign({},b)));this.bumpSpace();var P=this.parseIdentifierIfPossible(),I=0;if(l!=="select"&&P.value==="offset"){if(!this.bumpIf(":"))return this.error(R.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,M(this.clonePosition(),this.clonePosition()));this.bumpSpace();var p=this.tryParseDecimalInteger(R.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,R.ErrorKind.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(p.err)return p;this.bumpSpace(),P=this.parseIdentifierIfPossible(),I=p.val}var j=this.tryParsePluralOrSelectOptions(t,l,r,P);if(j.err)return j;var d=this.tryParseArgumentClose(o);if(d.err)return d;var V=M(o,this.clonePosition());return l==="select"?{val:{type:ee.TYPE.select,value:n,options:gr(j.val),location:V},err:null}:{val:{type:ee.TYPE.plural,value:n,options:gr(j.val),offset:I,pluralType:l==="plural"?"cardinal":"ordinal",location:V},err:null}}default:return this.error(R.ErrorKind.INVALID_ARGUMENT_TYPE,M(a,c))}},e.prototype.tryParseArgumentClose=function(t){return this.isEOF()||this.char()!==125?this.error(R.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE,M(t,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var t=0,r=this.clonePosition();!this.isEOF();){var n=this.char();switch(n){case 39:{this.bump();var o=this.clonePosition();if(!this.bumpUntil("'"))return this.error(R.ErrorKind.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,M(o,this.clonePosition()));this.bump();break}case 123:{t+=1,this.bump();break}case 125:{if(t>0)t-=1;else return{val:this.message.slice(r.offset,this.offset()),err:null};break}default:this.bump();break}}return{val:this.message.slice(r.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(t,r){var n=[];try{n=(0,Dt.parseNumberSkeletonFromString)(t)}catch{return this.error(R.ErrorKind.INVALID_NUMBER_SKELETON,r)}return{val:{type:ee.SKELETON_TYPE.number,tokens:n,location:r,parsedOptions:this.shouldParseSkeletons?(0,Dt.parseNumberSkeleton)(n):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(t,r,n,o){for(var i,a=!1,l=[],c=new Set,s=o.value,u=o.location;;){if(s.length===0){var p=this.clonePosition();if(r!=="select"&&this.bumpIf("=")){var y=this.tryParseDecimalInteger(R.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR,R.ErrorKind.INVALID_PLURAL_ARGUMENT_SELECTOR);if(y.err)return y;u=M(p,this.clonePosition()),s=this.message.slice(p.offset,this.offset())}else break}if(c.has(s))return this.error(r==="select"?R.ErrorKind.DUPLICATE_SELECT_ARGUMENT_SELECTOR:R.ErrorKind.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,u);s==="other"&&(a=!0),this.bumpSpace();var g=this.clonePosition();if(!this.bumpIf("{"))return this.error(r==="select"?R.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:R.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,M(this.clonePosition(),this.clonePosition()));var d=this.parseMessage(t+1,r,n);if(d.err)return d;var w=this.tryParseArgumentClose(g);if(w.err)return w;l.push([s,{value:d.val,location:M(g,this.clonePosition())}]),c.add(s),this.bumpSpace(),i=this.parseIdentifierIfPossible(),s=i.value,u=i.location}return l.length===0?this.error(r==="select"?R.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR:R.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR,M(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!a?this.error(R.ErrorKind.MISSING_OTHER_CLAUSE,M(this.clonePosition(),this.clonePosition())):{val:l,err:null}},e.prototype.tryParseDecimalInteger=function(t,r){var n=1,o=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(n=-1);for(var i=!1,a=0;!this.isEOF();){var l=this.char();if(l>=48&&l<=57)i=!0,a=a*10+(l-48),this.bump();else break}var c=M(o,this.clonePosition());return i?(a*=n,Ya(a)?{val:a,err:null}:this.error(r,c)):this.error(t,c)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var t=this.position.offset;if(t>=this.message.length)throw Error("out of bound");var r=un(this.message,t);if(r===void 0)throw Error("Offset ".concat(t," is at invalid UTF-16 code unit boundary"));return r},e.prototype.error=function(t,r){return{val:null,err:{kind:t,message:this.message,location:r}}},e.prototype.bump=function(){if(!this.isEOF()){var t=this.char();t===10?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=t<65536?1:2)}},e.prototype.bumpIf=function(t){if(pr(this.message,t,this.offset())){for(var r=0;r<t.length;r++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(t){var r=this.offset(),n=this.message.indexOf(t,r);return n>=0?(this.bumpTo(n),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(t){if(this.offset()>t)throw Error("targetOffset ".concat(t," must be greater than or equal to the current offset ").concat(this.offset()));for(t=Math.min(t,this.message.length);;){var r=this.offset();if(r===t)break;if(r>t)throw Error("targetOffset ".concat(t," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&_n(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var t=this.char(),r=this.offset(),n=this.message.charCodeAt(r+(t>=65536?2:1));return n??null},e}();xt.Parser=Ja;function Wt(e){return e>=97&&e<=122||e>=65&&e<=90}function Qa(e){return Wt(e)||e===47}function $a(e){return e===45||e===46||e>=48&&e<=57||e===95||e>=97&&e<=122||e>=65&&e<=90||e==183||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039}function _n(e){return e>=9&&e<=13||e===32||e===133||e>=8206&&e<=8207||e===8232||e===8233}function es(e){return e>=33&&e<=35||e===36||e>=37&&e<=39||e===40||e===41||e===42||e===43||e===44||e===45||e>=46&&e<=47||e>=58&&e<=59||e>=60&&e<=62||e>=63&&e<=64||e===91||e===92||e===93||e===94||e===96||e===123||e===124||e===125||e===126||e===161||e>=162&&e<=165||e===166||e===167||e===169||e===171||e===172||e===174||e===176||e===177||e===182||e===187||e===191||e===215||e===247||e>=8208&&e<=8213||e>=8214&&e<=8215||e===8216||e===8217||e===8218||e>=8219&&e<=8220||e===8221||e===8222||e===8223||e>=8224&&e<=8231||e>=8240&&e<=8248||e===8249||e===8250||e>=8251&&e<=8254||e>=8257&&e<=8259||e===8260||e===8261||e===8262||e>=8263&&e<=8273||e===8274||e===8275||e>=8277&&e<=8286||e>=8592&&e<=8596||e>=8597&&e<=8601||e>=8602&&e<=8603||e>=8604&&e<=8607||e===8608||e>=8609&&e<=8610||e===8611||e>=8612&&e<=8613||e===8614||e>=8615&&e<=8621||e===8622||e>=8623&&e<=8653||e>=8654&&e<=8655||e>=8656&&e<=8657||e===8658||e===8659||e===8660||e>=8661&&e<=8691||e>=8692&&e<=8959||e>=8960&&e<=8967||e===8968||e===8969||e===8970||e===8971||e>=8972&&e<=8991||e>=8992&&e<=8993||e>=8994&&e<=9e3||e===9001||e===9002||e>=9003&&e<=9083||e===9084||e>=9085&&e<=9114||e>=9115&&e<=9139||e>=9140&&e<=9179||e>=9180&&e<=9185||e>=9186&&e<=9254||e>=9255&&e<=9279||e>=9280&&e<=9290||e>=9291&&e<=9311||e>=9472&&e<=9654||e===9655||e>=9656&&e<=9664||e===9665||e>=9666&&e<=9719||e>=9720&&e<=9727||e>=9728&&e<=9838||e===9839||e>=9840&&e<=10087||e===10088||e===10089||e===10090||e===10091||e===10092||e===10093||e===10094||e===10095||e===10096||e===10097||e===10098||e===10099||e===10100||e===10101||e>=10132&&e<=10175||e>=10176&&e<=10180||e===10181||e===10182||e>=10183&&e<=10213||e===10214||e===10215||e===10216||e===10217||e===10218||e===10219||e===10220||e===10221||e===10222||e===10223||e>=10224&&e<=10239||e>=10240&&e<=10495||e>=10496&&e<=10626||e===10627||e===10628||e===10629||e===10630||e===10631||e===10632||e===10633||e===10634||e===10635||e===10636||e===10637||e===10638||e===10639||e===10640||e===10641||e===10642||e===10643||e===10644||e===10645||e===10646||e===10647||e===10648||e>=10649&&e<=10711||e===10712||e===10713||e===10714||e===10715||e>=10716&&e<=10747||e===10748||e===10749||e>=10750&&e<=11007||e>=11008&&e<=11055||e>=11056&&e<=11076||e>=11077&&e<=11078||e>=11079&&e<=11084||e>=11085&&e<=11123||e>=11124&&e<=11125||e>=11126&&e<=11157||e===11158||e>=11159&&e<=11263||e>=11776&&e<=11777||e===11778||e===11779||e===11780||e===11781||e>=11782&&e<=11784||e===11785||e===11786||e===11787||e===11788||e===11789||e>=11790&&e<=11798||e===11799||e>=11800&&e<=11801||e===11802||e===11803||e===11804||e===11805||e>=11806&&e<=11807||e===11808||e===11809||e===11810||e===11811||e===11812||e===11813||e===11814||e===11815||e===11816||e===11817||e>=11818&&e<=11822||e===11823||e>=11824&&e<=11833||e>=11834&&e<=11835||e>=11836&&e<=11839||e===11840||e===11841||e===11842||e>=11843&&e<=11855||e>=11856&&e<=11857||e===11858||e>=11859&&e<=11903||e>=12289&&e<=12291||e===12296||e===12297||e===12298||e===12299||e===12300||e===12301||e===12302||e===12303||e===12304||e===12305||e>=12306&&e<=12307||e===12308||e===12309||e===12310||e===12311||e===12312||e===12313||e===12314||e===12315||e===12316||e===12317||e>=12318&&e<=12319||e===12320||e===12336||e===64830||e===64831||e>=65093&&e<=65094}(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e._Parser=e.parse=void 0;var t=Re,r=ht,n=xt,o=N;function i(l){l.forEach(function(c){if(delete c.location,(0,o.isSelectElement)(c)||(0,o.isPluralElement)(c))for(var s in c.options)delete c.options[s].location,i(c.options[s].value);else(0,o.isNumberElement)(c)&&(0,o.isNumberSkeleton)(c.style)||((0,o.isDateElement)(c)||(0,o.isTimeElement)(c))&&(0,o.isDateTimeSkeleton)(c.style)?delete c.style.location:(0,o.isTagElement)(c)&&i(c.children)})}function a(l,c){c===void 0&&(c={}),c=t.__assign({shouldParseSkeletons:!0,requiresOtherClause:!0},c);var s=new n.Parser(l,c).parse();if(s.err){var u=SyntaxError(r.ErrorKind[s.err.kind]);throw u.location=s.err.location,u.originalMessage=s.err.message,u}return c?.captureLocation||i(s.val),s.val}e.parse=a,t.__exportStar(N,e),e._Parser=n.Parser})(rr);function ts(e,t){var r=t&&t.cache?t.cache:ss,n=t&&t.serializer?t.serializer:as,o=t&&t.strategy?t.strategy:ns;return o(e,{cache:r,serializer:n})}function rs(e){return e==null||typeof e=="number"||typeof e=="boolean"}function fn(e,t,r,n){var o=rs(n)?n:r(n),i=t.get(o);return typeof i>"u"&&(i=e.call(this,n),t.set(o,i)),i}function hn(e,t,r){var n=Array.prototype.slice.call(arguments,3),o=r(n),i=t.get(o);return typeof i>"u"&&(i=e.apply(this,n),t.set(o,i)),i}function nr(e,t,r,n,o){return r.bind(t,e,n,o)}function ns(e,t){var r=e.length===1?fn:hn;return nr(e,this,r,t.cache.create(),t.serializer)}function os(e,t){return nr(e,this,hn,t.cache.create(),t.serializer)}function is(e,t){return nr(e,this,fn,t.cache.create(),t.serializer)}var as=function(){return JSON.stringify(arguments)};function or(){this.cache=Object.create(null)}or.prototype.get=function(e){return this.cache[e]};or.prototype.set=function(e,t){this.cache[e]=t};var ss={create:function(){return new or}},ls={variadic:os,monadic:is};const us=Object.freeze(Object.defineProperty({__proto__:null,memoize:ts,strategies:ls},Symbol.toStringTag,{value:"Module"})),cs=ra(us);var xe={},ie={};Object.defineProperty(ie,"__esModule",{value:!0});ie.MissingValueError=ie.InvalidValueTypeError=ie.InvalidValueError=ie.FormatError=ie.ErrorCode=void 0;var Bt=Re,ut;(function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"})(ut||(ie.ErrorCode=ut={}));var Ct=function(e){Bt.__extends(t,e);function t(r,n,o){var i=e.call(this,r)||this;return i.code=n,i.originalMessage=o,i}return t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error);ie.FormatError=Ct;var _s=function(e){Bt.__extends(t,e);function t(r,n,o,i){return e.call(this,'Invalid values for "'.concat(r,'": "').concat(n,'". Options are "').concat(Object.keys(o).join('", "'),'"'),ut.INVALID_VALUE,i)||this}return t}(Ct);ie.InvalidValueError=_s;var fs=function(e){Bt.__extends(t,e);function t(r,n,o){return e.call(this,'Value for "'.concat(r,'" must be of type ').concat(n),ut.INVALID_VALUE,o)||this}return t}(Ct);ie.InvalidValueTypeError=fs;var hs=function(e){Bt.__extends(t,e);function t(r,n){return e.call(this,'The intl string context variable "'.concat(r,'" was not provided to the string "').concat(n,'"'),ut.MISSING_VALUE,n)||this}return t}(Ct);ie.MissingValueError=hs;Object.defineProperty(xe,"__esModule",{value:!0});xe.formatToParts=xe.isFormatXMLElementFn=xe.PART_TYPE=void 0;var oe=rr,De=ie,re;(function(e){e[e.literal=0]="literal",e[e.object=1]="object"})(re||(xe.PART_TYPE=re={}));function ds(e){return e.length<2?e:e.reduce(function(t,r){var n=t[t.length-1];return!n||n.type!==re.literal||r.type!==re.literal?t.push(r):n.value+=r.value,t},[])}function dn(e){return typeof e=="function"}xe.isFormatXMLElementFn=dn;function Et(e,t,r,n,o,i,a){if(e.length===1&&(0,oe.isLiteralElement)(e[0]))return[{type:re.literal,value:e[0].value}];for(var l=[],c=0,s=e;c<s.length;c++){var u=s[c];if((0,oe.isLiteralElement)(u)){l.push({type:re.literal,value:u.value});continue}if((0,oe.isPoundElement)(u)){typeof i=="number"&&l.push({type:re.literal,value:r.getNumberFormat(t).format(i)});continue}var p=u.value;if(!(o&&p in o))throw new De.MissingValueError(p,a);var y=o[p];if((0,oe.isArgumentElement)(u)){(!y||typeof y=="string"||typeof y=="number")&&(y=typeof y=="string"||typeof y=="number"?String(y):""),l.push({type:typeof y=="string"?re.literal:re.object,value:y});continue}if((0,oe.isDateElement)(u)){var g=typeof u.style=="string"?n.date[u.style]:(0,oe.isDateTimeSkeleton)(u.style)?u.style.parsedOptions:void 0;l.push({type:re.literal,value:r.getDateTimeFormat(t,g).format(y)});continue}if((0,oe.isTimeElement)(u)){var g=typeof u.style=="string"?n.time[u.style]:(0,oe.isDateTimeSkeleton)(u.style)?u.style.parsedOptions:n.time.medium;l.push({type:re.literal,value:r.getDateTimeFormat(t,g).format(y)});continue}if((0,oe.isNumberElement)(u)){var g=typeof u.style=="string"?n.number[u.style]:(0,oe.isNumberSkeleton)(u.style)?u.style.parsedOptions:void 0;g&&g.scale&&(y=y*(g.scale||1)),l.push({type:re.literal,value:r.getNumberFormat(t,g).format(y)});continue}if((0,oe.isTagElement)(u)){var d=u.children,w=u.value,O=o[w];if(!dn(O))throw new De.InvalidValueTypeError(w,"function",a);var T=Et(d,t,r,n,o,i),x=O(T.map(function(I){return I.value}));Array.isArray(x)||(x=[x]),l.push.apply(l,x.map(function(I){return{type:typeof I=="string"?re.literal:re.object,value:I}}))}if((0,oe.isSelectElement)(u)){var b=u.options[y]||u.options.other;if(!b)throw new De.InvalidValueError(u.value,y,Object.keys(u.options),a);l.push.apply(l,Et(b.value,t,r,n,o));continue}if((0,oe.isPluralElement)(u)){var b=u.options["=".concat(y)];if(!b){if(!Intl.PluralRules)throw new De.FormatError(`Intl.PluralRules is not available in this environment.
Try polyfilling it using "@formatjs/intl-pluralrules"
`,De.ErrorCode.MISSING_INTL_API,a);var P=r.getPluralRules(t,{type:u.pluralType}).select(y-(u.offset||0));b=u.options[P]||u.options.other}if(!b)throw new De.InvalidValueError(u.value,y,Object.keys(u.options),a);l.push.apply(l,Et(b.value,t,r,n,o,y-(u.offset||0)));continue}}return ds(l)}xe.formatToParts=Et;Object.defineProperty(lt,"__esModule",{value:!0});lt.IntlMessageFormat=void 0;var he=Re,ms=rr,Fe=cs,vr=xe;function ps(e,t){return t?he.__assign(he.__assign(he.__assign({},e||{}),t||{}),Object.keys(e).reduce(function(r,n){return r[n]=he.__assign(he.__assign({},e[n]),t[n]||{}),r},{})):e}function gs(e,t){return t?Object.keys(e).reduce(function(r,n){return r[n]=ps(e[n],t[n]),r},he.__assign({},e)):e}function Ft(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}function bs(e){return e===void 0&&(e={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:(0,Fe.memoize)(function(){for(var t,r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];return new((t=Intl.NumberFormat).bind.apply(t,he.__spreadArray([void 0],r,!1)))},{cache:Ft(e.number),strategy:Fe.strategies.variadic}),getDateTimeFormat:(0,Fe.memoize)(function(){for(var t,r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];return new((t=Intl.DateTimeFormat).bind.apply(t,he.__spreadArray([void 0],r,!1)))},{cache:Ft(e.dateTime),strategy:Fe.strategies.variadic}),getPluralRules:(0,Fe.memoize)(function(){for(var t,r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];return new((t=Intl.PluralRules).bind.apply(t,he.__spreadArray([void 0],r,!1)))},{cache:Ft(e.pluralRules),strategy:Fe.strategies.variadic})}}var vs=function(){function e(t,r,n,o){var i=this;if(r===void 0&&(r=e.defaultLocale),this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(c){var s=i.formatToParts(c);if(s.length===1)return s[0].value;var u=s.reduce(function(p,y){return!p.length||y.type!==vr.PART_TYPE.literal||typeof p[p.length-1]!="string"?p.push(y.value):p[p.length-1]+=y.value,p},[]);return u.length<=1?u[0]||"":u},this.formatToParts=function(c){return(0,vr.formatToParts)(i.ast,i.locales,i.formatters,i.formats,c,void 0,i.message)},this.resolvedOptions=function(){var c;return{locale:((c=i.resolvedLocale)===null||c===void 0?void 0:c.toString())||Intl.NumberFormat.supportedLocalesOf(i.locales)[0]}},this.getAst=function(){return i.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),typeof t=="string"){if(this.message=t,!e.__parse)throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var a=o||{};a.formatters;var l=he.__rest(a,["formatters"]);this.ast=e.__parse(t,he.__assign(he.__assign({},l),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw new TypeError("A message must be provided as a String or AST.");this.formats=gs(e.formats,n),this.formatters=o&&o.formatters||bs(this.formatterCache)}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(t){if(!(typeof Intl.Locale>"u")){var r=Intl.NumberFormat.supportedLocalesOf(t);return r.length>0?new Intl.Locale(r[0]):new Intl.Locale(typeof t=="string"?t:t[0])}},e.__parse=ms.parse,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();lt.IntlMessageFormat=vs;(function(e){Object.defineProperty(e,"__esModule",{value:!0});var t=Re,r=lt;t.__exportStar(xe,e),t.__exportStar(lt,e),t.__exportStar(ie,e),e.default=r.IntlMessageFormat})(tr);function ys(e,t){if(t==null)return;if(t in e)return e[t];const r=t.split(".");let n=e;for(let o=0;o<r.length;o++)if(typeof n=="object"){if(o>0){const i=r.slice(o,r.length).join(".");if(i in n){n=n[i];break}}n=n[r[o]]}else n=void 0;return n}const He={},Es=(e,t,r)=>r&&(t in He||(He[t]={}),e in He[t]||(He[t][e]=r),r),mn=(e,t)=>{if(t==null)return;if(t in He&&e in He[t])return He[t][e];const r=mt(t);for(let n=0;n<r.length;n++){const o=r[n],i=Ss(o,e);if(i)return Es(e,t,i)}};let ir;const dt=Ke({});function ws(e){return ir[e]||null}function pn(e){return e in ir}function Ss(e,t){if(!pn(e))return null;const r=ws(e);return ys(r,t)}function Ts(e){if(e==null)return;const t=mt(e);for(let r=0;r<t.length;r++){const n=t[r];if(pn(n))return n}}function gn(e,...t){delete He[e],dt.update(r=>(r[e]=ga.all([r[e]||{},...t]),r))}Je([dt],([e])=>Object.keys(e));dt.subscribe(e=>ir=e);const wt={};function Ps(e,t){wt[e].delete(t),wt[e].size===0&&delete wt[e]}function bn(e){return wt[e]}function Os(e){return mt(e).map(t=>{const r=bn(t);return[t,r?[...r]:[]]}).filter(([,t])=>t.length>0)}function Tt(e){return e==null?!1:mt(e).some(t=>{var r;return(r=bn(t))==null?void 0:r.size})}function ks(e,t){return Promise.all(t.map(n=>(Ps(e,n),n().then(o=>o.default||o)))).then(n=>gn(e,...n))}const nt={};function vn(e){if(!Tt(e))return e in nt?nt[e]:Promise.resolve();const t=Os(e);return nt[e]=Promise.all(t.map(([r,n])=>ks(r,n))).then(()=>{if(Tt(e))return vn(e);delete nt[e]}),nt[e]}var yr=Object.getOwnPropertySymbols,xs=Object.prototype.hasOwnProperty,Hs=Object.prototype.propertyIsEnumerable,As=(e,t)=>{var r={};for(var n in e)xs.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&yr)for(var n of yr(e))t.indexOf(n)<0&&Hs.call(e,n)&&(r[n]=e[n]);return r};const Bs={number:{scientific:{notation:"scientific"},engineering:{notation:"engineering"},compactLong:{notation:"compact",compactDisplay:"long"},compactShort:{notation:"compact",compactDisplay:"short"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}};function Cs({locale:e,id:t}){console.warn(`[svelte-i18n] The message "${t}" was not found in "${mt(e).join('", "')}".${Tt(Me())?`

Note: there are at least one loader still registered to this locale that wasn't executed.`:""}`)}const Ms={fallbackLocale:null,loadingDelay:200,formats:Bs,warnOnMissingMessages:!0,handleMissingMessage:void 0,ignoreTag:!0},ot=Ms;function Ye(){return ot}function Ns(e){const t=e,{formats:r}=t,n=As(t,["formats"]);let o=e.fallbackLocale;if(e.initialLocale)try{tr.IntlMessageFormat.resolveLocale(e.initialLocale)&&(o=e.initialLocale)}catch{console.warn(`[svelte-i18n] The initial locale "${e.initialLocale}" is not a valid locale.`)}return n.warnOnMissingMessages&&(delete n.warnOnMissingMessages,n.handleMissingMessage==null?n.handleMissingMessage=Cs:console.warn('[svelte-i18n] The "warnOnMissingMessages" option is deprecated. Please use the "handleMissingMessage" option instead.')),Object.assign(ot,n,{initialLocale:o}),r&&("number"in r&&Object.assign(ot.formats.number,r.number),"date"in r&&Object.assign(ot.formats.date,r.date),"time"in r&&Object.assign(ot.formats.time,r.time)),Qe.set(o)}const Gt=Ke(!1);var Is=Object.defineProperty,Ls=Object.defineProperties,Rs=Object.getOwnPropertyDescriptors,Er=Object.getOwnPropertySymbols,js=Object.prototype.hasOwnProperty,Ds=Object.prototype.propertyIsEnumerable,wr=(e,t,r)=>t in e?Is(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Fs=(e,t)=>{for(var r in t||(t={}))js.call(t,r)&&wr(e,r,t[r]);if(Er)for(var r of Er(t))Ds.call(t,r)&&wr(e,r,t[r]);return e},Gs=(e,t)=>Ls(e,Rs(t));let Zt;const Pt=Ke(null);function Sr(e){return e.split("-").map((t,r,n)=>n.slice(0,r+1).join("-")).reverse()}function mt(e,t=Ye().fallbackLocale){const r=Sr(e);return t?[...new Set([...r,...Sr(t)])]:r}function Me(){return Zt??void 0}Pt.subscribe(e=>{Zt=e??void 0,typeof window<"u"&&e!=null&&document.documentElement.setAttribute("lang",e)});const Us=e=>{if(e&&Ts(e)&&Tt(e)){const{loadingDelay:t}=Ye();let r;return typeof window<"u"&&Me()!=null&&t?r=window.setTimeout(()=>Gt.set(!0),t):Gt.set(!0),vn(e).then(()=>{Pt.set(e)}).finally(()=>{clearTimeout(r),Gt.set(!1)})}return Pt.set(e)},Qe=Gs(Fs({},Pt),{set:Us}),Vs=()=>typeof window>"u"?null:window.navigator.language||window.navigator.languages[0],Mt=e=>{const t=Object.create(null);return n=>{const o=JSON.stringify(n);return o in t?t[o]:t[o]=e(n)}};var zs=Object.defineProperty,Ot=Object.getOwnPropertySymbols,yn=Object.prototype.hasOwnProperty,En=Object.prototype.propertyIsEnumerable,Tr=(e,t,r)=>t in e?zs(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,ar=(e,t)=>{for(var r in t||(t={}))yn.call(t,r)&&Tr(e,r,t[r]);if(Ot)for(var r of Ot(t))En.call(t,r)&&Tr(e,r,t[r]);return e},$e=(e,t)=>{var r={};for(var n in e)yn.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&Ot)for(var n of Ot(e))t.indexOf(n)<0&&En.call(e,n)&&(r[n]=e[n]);return r};const ct=(e,t)=>{const{formats:r}=Ye();if(e in r&&t in r[e])return r[e][t];throw new Error(`[svelte-i18n] Unknown "${t}" ${e} format.`)},qs=Mt(e=>{var t=e,{locale:r,format:n}=t,o=$e(t,["locale","format"]);if(r==null)throw new Error('[svelte-i18n] A "locale" must be set to format numbers');return n&&(o=ct("number",n)),new Intl.NumberFormat(r,o)}),Xs=Mt(e=>{var t=e,{locale:r,format:n}=t,o=$e(t,["locale","format"]);if(r==null)throw new Error('[svelte-i18n] A "locale" must be set to format dates');return n?o=ct("date",n):Object.keys(o).length===0&&(o=ct("date","short")),new Intl.DateTimeFormat(r,o)}),Ys=Mt(e=>{var t=e,{locale:r,format:n}=t,o=$e(t,["locale","format"]);if(r==null)throw new Error('[svelte-i18n] A "locale" must be set to format time values');return n?o=ct("time",n):Object.keys(o).length===0&&(o=ct("time","short")),new Intl.DateTimeFormat(r,o)}),Ws=(e={})=>{var t=e,{locale:r=Me()}=t,n=$e(t,["locale"]);return qs(ar({locale:r},n))},Zs=(e={})=>{var t=e,{locale:r=Me()}=t,n=$e(t,["locale"]);return Xs(ar({locale:r},n))},Ks=(e={})=>{var t=e,{locale:r=Me()}=t,n=$e(t,["locale"]);return Ys(ar({locale:r},n))},Js=Mt((e,t=Me())=>new tr.IntlMessageFormat(e,t,Ye().formats,{ignoreTag:Ye().ignoreTag})),Qs=(e,t={})=>{var r,n,o,i;let a=t;typeof e=="object"&&(a=e,e=a.id);const{values:l,locale:c=Me(),default:s}=a;if(c==null)throw new Error("[svelte-i18n] Cannot format a message without first setting the initial locale.");let u=mn(e,c);if(!u)u=(i=(o=(n=(r=Ye()).handleMissingMessage)==null?void 0:n.call(r,{locale:c,id:e,defaultValue:s}))!=null?o:s)!=null?i:e;else if(typeof u!="string")return console.warn(`[svelte-i18n] Message with id "${e}" must be of type "string", found: "${typeof u}". Gettin its value through the "$format" method is deprecated; use the "json" method instead.`),u;if(!l)return u;let p=u;try{p=Js(u,c).format(l)}catch(y){y instanceof Error&&console.warn(`[svelte-i18n] Message "${e}" has syntax error:`,y.message)}return p},$s=(e,t)=>Ks(t).format(e),el=(e,t)=>Zs(t).format(e),tl=(e,t)=>Ws(t).format(e),rl=(e,t=Me())=>mn(e,t),wn=Je([Qe,dt],()=>Qs);Je([Qe],()=>$s);Je([Qe],()=>el);Je([Qe],()=>tl);Je([Qe,dt],()=>rl);const{SvelteComponent:nl,append:J,attr:te,binding_callbacks:ol,component_subscribe:il,create_slot:al,detach:Sn,element:Te,get_all_dirty_from_scope:sl,get_slot_changes:ll,init:ul,insert:Tn,safe_not_equal:cl,set_data:Ut,set_style:gt,space:it,text:bt,toggle_class:Ge,transition_in:_l,transition_out:fl,update_slot_base:hl}=window.__gradio__svelte__internal;function Pr(e){let t,r,n,o,i,a,l,c=e[8]("common.built_with")+"",s,u,p,y,g,d,w=e[8]("common.hosted_on")+"",O,T,x;return{c(){t=Te("div"),r=Te("span"),n=Te("a"),o=bt(e[4]),a=it(),l=Te("span"),s=bt(c),u=it(),p=Te("a"),p.textContent="Gradio",y=bt("."),g=it(),d=Te("span"),O=bt(w),T=it(),x=Te("a"),x.innerHTML=`<span class="space-logo svelte-182fdeq"><img src="${ea}" alt="Hugging Face Space" class="svelte-182fdeq"/></span> Spaces`,te(n,"href",i="https://huggingface.co/spaces/"+e[4]),te(n,"class","title svelte-182fdeq"),te(r,"class","svelte-182fdeq"),te(p,"class","gradio svelte-182fdeq"),te(p,"href","https://gradio.app"),te(l,"class","svelte-182fdeq"),te(x,"class","hf svelte-182fdeq"),te(x,"href","https://huggingface.co/spaces"),te(d,"class","svelte-182fdeq"),te(t,"class","info svelte-182fdeq")},m(b,P){Tn(b,t,P),J(t,r),J(r,n),J(n,o),J(t,a),J(t,l),J(l,s),J(l,u),J(l,p),J(l,y),J(t,g),J(t,d),J(d,O),J(d,T),J(d,x)},p(b,P){P&16&&Ut(o,b[4]),P&16&&i!==(i="https://huggingface.co/spaces/"+b[4])&&te(n,"href",i),P&256&&c!==(c=b[8]("common.built_with")+"")&&Ut(s,c),P&256&&w!==(w=b[8]("common.hosted_on")+"")&&Ut(O,w)},d(b){b&&Sn(t)}}}function dl(e){let t,r,n,o,i;const a=e[10].default,l=al(a,e,e[9],null);let c=e[5]&&e[4]&&e[6]&&Pr(e);return{c(){t=Te("div"),r=Te("div"),l&&l.c(),n=it(),c&&c.c(),te(r,"class","main svelte-182fdeq"),te(t,"class",o="gradio-container gradio-container-"+e[1]+" svelte-182fdeq"),te(t,"data-iframe-height",""),Ge(t,"app",!e[5]&&!e[3]),Ge(t,"embed-container",e[5]),Ge(t,"with-info",e[6]),gt(t,"min-height",e[7]?"initial":e[2]),gt(t,"flex-grow",e[5]?"auto":"1")},m(s,u){Tn(s,t,u),J(t,r),l&&l.m(r,null),J(t,n),c&&c.m(t,null),e[11](t),i=!0},p(s,[u]){l&&l.p&&(!i||u&512)&&hl(l,a,s,s[9],i?ll(a,s[9],u,null):sl(s[9]),null),s[5]&&s[4]&&s[6]?c?c.p(s,u):(c=Pr(s),c.c(),c.m(t,null)):c&&(c.d(1),c=null),(!i||u&2&&o!==(o="gradio-container gradio-container-"+s[1]+" svelte-182fdeq"))&&te(t,"class",o),(!i||u&42)&&Ge(t,"app",!s[5]&&!s[3]),(!i||u&34)&&Ge(t,"embed-container",s[5]),(!i||u&66)&&Ge(t,"with-info",s[6]),u&132&&gt(t,"min-height",s[7]?"initial":s[2]),u&32&&gt(t,"flex-grow",s[5]?"auto":"1")},i(s){i||(_l(l,s),i=!0)},o(s){fl(l,s),i=!1},d(s){s&&Sn(t),l&&l.d(s),c&&c.d(),e[11](null)}}}function ml(e,t,r){let n;il(e,wn,w=>r(8,n=w));let{$$slots:o={},$$scope:i}=t,{wrapper:a}=t,{version:l}=t,{initial_height:c}=t,{is_embed:s}=t,{space:u}=t,{display:p}=t,{info:y}=t,{loaded:g}=t;function d(w){ol[w?"unshift":"push"](()=>{a=w,r(0,a)})}return e.$$set=w=>{"wrapper"in w&&r(0,a=w.wrapper),"version"in w&&r(1,l=w.version),"initial_height"in w&&r(2,c=w.initial_height),"is_embed"in w&&r(3,s=w.is_embed),"space"in w&&r(4,u=w.space),"display"in w&&r(5,p=w.display),"info"in w&&r(6,y=w.info),"loaded"in w&&r(7,g=w.loaded),"$$scope"in w&&r(9,i=w.$$scope)},[a,l,c,s,u,p,y,g,n,i,o,d]}class pl extends nl{constructor(t){super(),ul(this,t,ml,dl,cl,{wrapper:0,version:1,initial_height:2,is_embed:3,space:4,display:5,info:6,loaded:7})}}function Ue(e){let t=["","k","M","G","T","P","E","Z"],r=0;for(;e>1e3&&r<t.length-1;)e/=1e3,r++;let n=t[r];return(Number.isInteger(e)?e:e.toFixed(1))+n}function Or(e){return Object.prototype.toString.call(e)==="[object Date]"}function Kt(e,t,r,n){if(typeof r=="number"||Or(r)){const o=n-r,i=(r-t)/(e.dt||1/60),a=e.opts.stiffness*o,l=e.opts.damping*i,c=(a-l)*e.inv_mass,s=(i+c)*e.dt;return Math.abs(s)<e.opts.precision&&Math.abs(o)<e.opts.precision?n:(e.settled=!1,Or(r)?new Date(r.getTime()+s):r+s)}else{if(Array.isArray(r))return r.map((o,i)=>Kt(e,t[i],r[i],n[i]));if(typeof r=="object"){const o={};for(const i in r)o[i]=Kt(e,t[i],r[i],n[i]);return o}else throw new Error(`Cannot spring ${typeof r} values`)}}function kr(e,t={}){const r=Ke(e),{stiffness:n=.15,damping:o=.8,precision:i=.01}=t;let a,l,c,s=e,u=e,p=1,y=0,g=!1;function d(O,T={}){u=O;const x=c={};return e==null||T.hard||w.stiffness>=1&&w.damping>=1?(g=!0,a=ur(),s=O,r.set(e=u),Promise.resolve()):(T.soft&&(y=1/((T.soft===!0?.5:+T.soft)*60),p=0),l||(a=ur(),g=!1,l=Qi(b=>{if(g)return g=!1,l=null,!1;p=Math.min(p+y,1);const P={inv_mass:p,opts:w,settled:!0,dt:(b-a)*60/1e3},I=Kt(P,s,e,u);return a=b,s=e,r.set(e=I),P.settled&&(l=null),!P.settled})),new Promise(b=>{l.promise.then(()=>{x===c&&b()})}))}const w={set:d,update:(O,T)=>d(O(u,e),T),subscribe:r.subscribe,stiffness:n,damping:o,precision:i};return w}const{SvelteComponent:gl,append:pe,attr:L,component_subscribe:xr,detach:bl,element:vl,init:yl,insert:El,noop:Hr,safe_not_equal:wl,set_style:vt,svg_element:ge,toggle_class:Ar}=window.__gradio__svelte__internal,{onMount:Sl}=window.__gradio__svelte__internal;function Tl(e){let t,r,n,o,i,a,l,c,s,u,p,y;return{c(){t=vl("div"),r=ge("svg"),n=ge("g"),o=ge("path"),i=ge("path"),a=ge("path"),l=ge("path"),c=ge("g"),s=ge("path"),u=ge("path"),p=ge("path"),y=ge("path"),L(o,"d","M255.926 0.754768L509.702 139.936V221.027L255.926 81.8465V0.754768Z"),L(o,"fill","#FF7C00"),L(o,"fill-opacity","0.4"),L(o,"class","svelte-zyxd38"),L(i,"d","M509.69 139.936L254.981 279.641V361.255L509.69 221.55V139.936Z"),L(i,"fill","#FF7C00"),L(i,"class","svelte-zyxd38"),L(a,"d","M0.250138 139.937L254.981 279.641V361.255L0.250138 221.55V139.937Z"),L(a,"fill","#FF7C00"),L(a,"fill-opacity","0.4"),L(a,"class","svelte-zyxd38"),L(l,"d","M255.923 0.232622L0.236328 139.936V221.55L255.923 81.8469V0.232622Z"),L(l,"fill","#FF7C00"),L(l,"class","svelte-zyxd38"),vt(n,"transform","translate("+e[1][0]+"px, "+e[1][1]+"px)"),L(s,"d","M255.926 141.5L509.702 280.681V361.773L255.926 222.592V141.5Z"),L(s,"fill","#FF7C00"),L(s,"fill-opacity","0.4"),L(s,"class","svelte-zyxd38"),L(u,"d","M509.69 280.679L254.981 420.384V501.998L509.69 362.293V280.679Z"),L(u,"fill","#FF7C00"),L(u,"class","svelte-zyxd38"),L(p,"d","M0.250138 280.681L254.981 420.386V502L0.250138 362.295V280.681Z"),L(p,"fill","#FF7C00"),L(p,"fill-opacity","0.4"),L(p,"class","svelte-zyxd38"),L(y,"d","M255.923 140.977L0.236328 280.68V362.294L255.923 222.591V140.977Z"),L(y,"fill","#FF7C00"),L(y,"class","svelte-zyxd38"),vt(c,"transform","translate("+e[2][0]+"px, "+e[2][1]+"px)"),L(r,"viewBox","-1200 -1200 3000 3000"),L(r,"fill","none"),L(r,"xmlns","http://www.w3.org/2000/svg"),L(r,"class","svelte-zyxd38"),L(t,"class","svelte-zyxd38"),Ar(t,"margin",e[0])},m(g,d){El(g,t,d),pe(t,r),pe(r,n),pe(n,o),pe(n,i),pe(n,a),pe(n,l),pe(r,c),pe(c,s),pe(c,u),pe(c,p),pe(c,y)},p(g,[d]){d&2&&vt(n,"transform","translate("+g[1][0]+"px, "+g[1][1]+"px)"),d&4&&vt(c,"transform","translate("+g[2][0]+"px, "+g[2][1]+"px)"),d&1&&Ar(t,"margin",g[0])},i:Hr,o:Hr,d(g){g&&bl(t)}}}function Pl(e,t,r){let n,o,{margin:i=!0}=t;const a=kr([0,0]);xr(e,a,y=>r(1,n=y));const l=kr([0,0]);xr(e,l,y=>r(2,o=y));let c;async function s(){await Promise.all([a.set([125,140]),l.set([-125,-140])]),await Promise.all([a.set([-125,140]),l.set([125,-140])]),await Promise.all([a.set([-125,0]),l.set([125,-0])]),await Promise.all([a.set([125,0]),l.set([-125,0])])}async function u(){await s(),c||u()}async function p(){await Promise.all([a.set([125,0]),l.set([-125,0])]),u()}return Sl(()=>(p(),()=>c=!0)),e.$$set=y=>{"margin"in y&&r(0,i=y.margin)},[i,n,o,a,l]}class Ol extends gl{constructor(t){super(),yl(this,t,Pl,Tl,wl,{margin:0})}}const{SvelteComponent:kl,append:Ie,attr:ye,binding_callbacks:Br,check_outros:Pn,create_component:xl,create_slot:Hl,destroy_component:Al,destroy_each:On,detach:A,element:Oe,empty:et,ensure_array_like:kt,get_all_dirty_from_scope:Bl,get_slot_changes:Cl,group_outros:kn,init:Ml,insert:B,mount_component:Nl,noop:Jt,safe_not_equal:Il,set_data:de,set_style:Ae,space:Ee,text:U,toggle_class:fe,transition_in:We,transition_out:Ze,update_slot_base:Ll}=window.__gradio__svelte__internal,{tick:Rl}=window.__gradio__svelte__internal,{onDestroy:jl}=window.__gradio__svelte__internal,Dl=e=>({}),Cr=e=>({});function Mr(e,t,r){const n=e.slice();return n[38]=t[r],n[40]=r,n}function Nr(e,t,r){const n=e.slice();return n[38]=t[r],n}function Fl(e){let t,r=e[1]("common.error")+"",n,o,i;const a=e[29].error,l=Hl(a,e,e[28],Cr);return{c(){t=Oe("span"),n=U(r),o=Ee(),l&&l.c(),ye(t,"class","error svelte-12bm2fk")},m(c,s){B(c,t,s),Ie(t,n),B(c,o,s),l&&l.m(c,s),i=!0},p(c,s){(!i||s[0]&2)&&r!==(r=c[1]("common.error")+"")&&de(n,r),l&&l.p&&(!i||s[0]&268435456)&&Ll(l,a,c,c[28],i?Cl(a,c[28],s,Dl):Bl(c[28]),Cr)},i(c){i||(We(l,c),i=!0)},o(c){Ze(l,c),i=!1},d(c){c&&(A(t),A(o)),l&&l.d(c)}}}function Gl(e){let t,r,n,o,i,a,l,c,s,u=e[8]==="default"&&e[18]&&e[6]==="full"&&Ir(e);function p(b,P){if(b[7])return zl;if(b[2]!==null&&b[3]!==void 0&&b[2]>=0)return Vl;if(b[2]===0)return Ul}let y=p(e),g=y&&y(e),d=e[5]&&jr(e);const w=[Wl,Yl],O=[];function T(b,P){return b[15]!=null?0:b[6]==="full"?1:-1}~(i=T(e))&&(a=O[i]=w[i](e));let x=!e[5]&&qr(e);return{c(){u&&u.c(),t=Ee(),r=Oe("div"),g&&g.c(),n=Ee(),d&&d.c(),o=Ee(),a&&a.c(),l=Ee(),x&&x.c(),c=et(),ye(r,"class","progress-text svelte-12bm2fk"),fe(r,"meta-text-center",e[8]==="center"),fe(r,"meta-text",e[8]==="default")},m(b,P){u&&u.m(b,P),B(b,t,P),B(b,r,P),g&&g.m(r,null),Ie(r,n),d&&d.m(r,null),B(b,o,P),~i&&O[i].m(b,P),B(b,l,P),x&&x.m(b,P),B(b,c,P),s=!0},p(b,P){b[8]==="default"&&b[18]&&b[6]==="full"?u?u.p(b,P):(u=Ir(b),u.c(),u.m(t.parentNode,t)):u&&(u.d(1),u=null),y===(y=p(b))&&g?g.p(b,P):(g&&g.d(1),g=y&&y(b),g&&(g.c(),g.m(r,n))),b[5]?d?d.p(b,P):(d=jr(b),d.c(),d.m(r,null)):d&&(d.d(1),d=null),(!s||P[0]&256)&&fe(r,"meta-text-center",b[8]==="center"),(!s||P[0]&256)&&fe(r,"meta-text",b[8]==="default");let I=i;i=T(b),i===I?~i&&O[i].p(b,P):(a&&(kn(),Ze(O[I],1,1,()=>{O[I]=null}),Pn()),~i?(a=O[i],a?a.p(b,P):(a=O[i]=w[i](b),a.c()),We(a,1),a.m(l.parentNode,l)):a=null),b[5]?x&&(x.d(1),x=null):x?x.p(b,P):(x=qr(b),x.c(),x.m(c.parentNode,c))},i(b){s||(We(a),s=!0)},o(b){Ze(a),s=!1},d(b){b&&(A(t),A(r),A(o),A(l),A(c)),u&&u.d(b),g&&g.d(),d&&d.d(),~i&&O[i].d(b),x&&x.d(b)}}}function Ir(e){let t,r=`translateX(${(e[17]||0)*100-100}%)`;return{c(){t=Oe("div"),ye(t,"class","eta-bar svelte-12bm2fk"),Ae(t,"transform",r)},m(n,o){B(n,t,o)},p(n,o){o[0]&131072&&r!==(r=`translateX(${(n[17]||0)*100-100}%)`)&&Ae(t,"transform",r)},d(n){n&&A(t)}}}function Ul(e){let t;return{c(){t=U("processing |")},m(r,n){B(r,t,n)},p:Jt,d(r){r&&A(t)}}}function Vl(e){let t,r=e[2]+1+"",n,o,i,a;return{c(){t=U("queue: "),n=U(r),o=U("/"),i=U(e[3]),a=U(" |")},m(l,c){B(l,t,c),B(l,n,c),B(l,o,c),B(l,i,c),B(l,a,c)},p(l,c){c[0]&4&&r!==(r=l[2]+1+"")&&de(n,r),c[0]&8&&de(i,l[3])},d(l){l&&(A(t),A(n),A(o),A(i),A(a))}}}function zl(e){let t,r=kt(e[7]),n=[];for(let o=0;o<r.length;o+=1)n[o]=Rr(Nr(e,r,o));return{c(){for(let o=0;o<n.length;o+=1)n[o].c();t=et()},m(o,i){for(let a=0;a<n.length;a+=1)n[a]&&n[a].m(o,i);B(o,t,i)},p(o,i){if(i[0]&128){r=kt(o[7]);let a;for(a=0;a<r.length;a+=1){const l=Nr(o,r,a);n[a]?n[a].p(l,i):(n[a]=Rr(l),n[a].c(),n[a].m(t.parentNode,t))}for(;a<n.length;a+=1)n[a].d(1);n.length=r.length}},d(o){o&&A(t),On(n,o)}}}function Lr(e){let t,r=e[38].unit+"",n,o,i=" ",a;function l(u,p){return u[38].length!=null?Xl:ql}let c=l(e),s=c(e);return{c(){s.c(),t=Ee(),n=U(r),o=U(" | "),a=U(i)},m(u,p){s.m(u,p),B(u,t,p),B(u,n,p),B(u,o,p),B(u,a,p)},p(u,p){c===(c=l(u))&&s?s.p(u,p):(s.d(1),s=c(u),s&&(s.c(),s.m(t.parentNode,t))),p[0]&128&&r!==(r=u[38].unit+"")&&de(n,r)},d(u){u&&(A(t),A(n),A(o),A(a)),s.d(u)}}}function ql(e){let t=Ue(e[38].index||0)+"",r;return{c(){r=U(t)},m(n,o){B(n,r,o)},p(n,o){o[0]&128&&t!==(t=Ue(n[38].index||0)+"")&&de(r,t)},d(n){n&&A(r)}}}function Xl(e){let t=Ue(e[38].index||0)+"",r,n,o=Ue(e[38].length)+"",i;return{c(){r=U(t),n=U("/"),i=U(o)},m(a,l){B(a,r,l),B(a,n,l),B(a,i,l)},p(a,l){l[0]&128&&t!==(t=Ue(a[38].index||0)+"")&&de(r,t),l[0]&128&&o!==(o=Ue(a[38].length)+"")&&de(i,o)},d(a){a&&(A(r),A(n),A(i))}}}function Rr(e){let t,r=e[38].index!=null&&Lr(e);return{c(){r&&r.c(),t=et()},m(n,o){r&&r.m(n,o),B(n,t,o)},p(n,o){n[38].index!=null?r?r.p(n,o):(r=Lr(n),r.c(),r.m(t.parentNode,t)):r&&(r.d(1),r=null)},d(n){n&&A(t),r&&r.d(n)}}}function jr(e){let t,r=e[0]?`/${e[19]}`:"",n,o;return{c(){t=U(e[20]),n=U(r),o=U("s")},m(i,a){B(i,t,a),B(i,n,a),B(i,o,a)},p(i,a){a[0]&1048576&&de(t,i[20]),a[0]&524289&&r!==(r=i[0]?`/${i[19]}`:"")&&de(n,r)},d(i){i&&(A(t),A(n),A(o))}}}function Yl(e){let t,r;return t=new Ol({props:{margin:e[8]==="default"}}),{c(){xl(t.$$.fragment)},m(n,o){Nl(t,n,o),r=!0},p(n,o){const i={};o[0]&256&&(i.margin=n[8]==="default"),t.$set(i)},i(n){r||(We(t.$$.fragment,n),r=!0)},o(n){Ze(t.$$.fragment,n),r=!1},d(n){Al(t,n)}}}function Wl(e){let t,r,n,o,i,a=`${e[15]*100}%`,l=e[7]!=null&&Dr(e);return{c(){t=Oe("div"),r=Oe("div"),l&&l.c(),n=Ee(),o=Oe("div"),i=Oe("div"),ye(r,"class","progress-level-inner svelte-12bm2fk"),ye(i,"class","progress-bar svelte-12bm2fk"),Ae(i,"width",a),ye(o,"class","progress-bar-wrap svelte-12bm2fk"),ye(t,"class","progress-level svelte-12bm2fk")},m(c,s){B(c,t,s),Ie(t,r),l&&l.m(r,null),Ie(t,n),Ie(t,o),Ie(o,i),e[30](i)},p(c,s){c[7]!=null?l?l.p(c,s):(l=Dr(c),l.c(),l.m(r,null)):l&&(l.d(1),l=null),s[0]&32768&&a!==(a=`${c[15]*100}%`)&&Ae(i,"width",a)},i:Jt,o:Jt,d(c){c&&A(t),l&&l.d(),e[30](null)}}}function Dr(e){let t,r=kt(e[7]),n=[];for(let o=0;o<r.length;o+=1)n[o]=zr(Mr(e,r,o));return{c(){for(let o=0;o<n.length;o+=1)n[o].c();t=et()},m(o,i){for(let a=0;a<n.length;a+=1)n[a]&&n[a].m(o,i);B(o,t,i)},p(o,i){if(i[0]&16512){r=kt(o[7]);let a;for(a=0;a<r.length;a+=1){const l=Mr(o,r,a);n[a]?n[a].p(l,i):(n[a]=zr(l),n[a].c(),n[a].m(t.parentNode,t))}for(;a<n.length;a+=1)n[a].d(1);n.length=r.length}},d(o){o&&A(t),On(n,o)}}}function Fr(e){let t,r,n,o,i=e[40]!==0&&Zl(),a=e[38].desc!=null&&Gr(e),l=e[38].desc!=null&&e[14]&&e[14][e[40]]!=null&&Ur(),c=e[14]!=null&&Vr(e);return{c(){i&&i.c(),t=Ee(),a&&a.c(),r=Ee(),l&&l.c(),n=Ee(),c&&c.c(),o=et()},m(s,u){i&&i.m(s,u),B(s,t,u),a&&a.m(s,u),B(s,r,u),l&&l.m(s,u),B(s,n,u),c&&c.m(s,u),B(s,o,u)},p(s,u){s[38].desc!=null?a?a.p(s,u):(a=Gr(s),a.c(),a.m(r.parentNode,r)):a&&(a.d(1),a=null),s[38].desc!=null&&s[14]&&s[14][s[40]]!=null?l||(l=Ur(),l.c(),l.m(n.parentNode,n)):l&&(l.d(1),l=null),s[14]!=null?c?c.p(s,u):(c=Vr(s),c.c(),c.m(o.parentNode,o)):c&&(c.d(1),c=null)},d(s){s&&(A(t),A(r),A(n),A(o)),i&&i.d(s),a&&a.d(s),l&&l.d(s),c&&c.d(s)}}}function Zl(e){let t;return{c(){t=U(" /")},m(r,n){B(r,t,n)},d(r){r&&A(t)}}}function Gr(e){let t=e[38].desc+"",r;return{c(){r=U(t)},m(n,o){B(n,r,o)},p(n,o){o[0]&128&&t!==(t=n[38].desc+"")&&de(r,t)},d(n){n&&A(r)}}}function Ur(e){let t;return{c(){t=U("-")},m(r,n){B(r,t,n)},d(r){r&&A(t)}}}function Vr(e){let t=(100*(e[14][e[40]]||0)).toFixed(1)+"",r,n;return{c(){r=U(t),n=U("%")},m(o,i){B(o,r,i),B(o,n,i)},p(o,i){i[0]&16384&&t!==(t=(100*(o[14][o[40]]||0)).toFixed(1)+"")&&de(r,t)},d(o){o&&(A(r),A(n))}}}function zr(e){let t,r=(e[38].desc!=null||e[14]&&e[14][e[40]]!=null)&&Fr(e);return{c(){r&&r.c(),t=et()},m(n,o){r&&r.m(n,o),B(n,t,o)},p(n,o){n[38].desc!=null||n[14]&&n[14][n[40]]!=null?r?r.p(n,o):(r=Fr(n),r.c(),r.m(t.parentNode,t)):r&&(r.d(1),r=null)},d(n){n&&A(t),r&&r.d(n)}}}function qr(e){let t,r;return{c(){t=Oe("p"),r=U(e[9]),ye(t,"class","loading svelte-12bm2fk")},m(n,o){B(n,t,o),Ie(t,r)},p(n,o){o[0]&512&&de(r,n[9])},d(n){n&&A(t)}}}function Kl(e){let t,r,n,o,i;const a=[Gl,Fl],l=[];function c(s,u){return s[4]==="pending"?0:s[4]==="error"?1:-1}return~(r=c(e))&&(n=l[r]=a[r](e)),{c(){t=Oe("div"),n&&n.c(),ye(t,"class",o="wrap "+e[8]+" "+e[6]+" svelte-12bm2fk"),fe(t,"hide",!e[4]||e[4]==="complete"||e[6]==="hidden"),fe(t,"translucent",e[8]==="center"&&(e[4]==="pending"||e[4]==="error")||e[11]||e[6]==="minimal"),fe(t,"generating",e[4]==="generating"),fe(t,"border",e[12]),Ae(t,"position",e[10]?"absolute":"static"),Ae(t,"padding",e[10]?"0":"var(--size-8) 0")},m(s,u){B(s,t,u),~r&&l[r].m(t,null),e[31](t),i=!0},p(s,u){let p=r;r=c(s),r===p?~r&&l[r].p(s,u):(n&&(kn(),Ze(l[p],1,1,()=>{l[p]=null}),Pn()),~r?(n=l[r],n?n.p(s,u):(n=l[r]=a[r](s),n.c()),We(n,1),n.m(t,null)):n=null),(!i||u[0]&320&&o!==(o="wrap "+s[8]+" "+s[6]+" svelte-12bm2fk"))&&ye(t,"class",o),(!i||u[0]&336)&&fe(t,"hide",!s[4]||s[4]==="complete"||s[6]==="hidden"),(!i||u[0]&2384)&&fe(t,"translucent",s[8]==="center"&&(s[4]==="pending"||s[4]==="error")||s[11]||s[6]==="minimal"),(!i||u[0]&336)&&fe(t,"generating",s[4]==="generating"),(!i||u[0]&4416)&&fe(t,"border",s[12]),u[0]&1024&&Ae(t,"position",s[10]?"absolute":"static"),u[0]&1024&&Ae(t,"padding",s[10]?"0":"var(--size-8) 0")},i(s){i||(We(n),i=!0)},o(s){Ze(n),i=!1},d(s){s&&A(t),~r&&l[r].d(),e[31](null)}}}let yt=[],Vt=!1;async function Jl(e,t=!0){if(!(window.__gradio_mode__==="website"||window.__gradio_mode__!=="app"&&t!==!0)){if(yt.push(e),!Vt)Vt=!0;else return;await Rl(),requestAnimationFrame(()=>{let r=[0,0];for(let n=0;n<yt.length;n++){const i=yt[n].getBoundingClientRect();(n===0||i.top+window.scrollY<=r[0])&&(r[0]=i.top+window.scrollY,r[1]=n)}window.scrollTo({top:r[0]-20,behavior:"smooth"}),Vt=!1,yt=[]})}}function Ql(e,t,r){let n,{$$slots:o={},$$scope:i}=t,{i18n:a}=t,{eta:l=null}=t,{queue_position:c}=t,{queue_size:s}=t,{status:u}=t,{scroll_to_output:p=!1}=t,{timer:y=!0}=t,{show_progress:g="full"}=t,{message:d=null}=t,{progress:w=null}=t,{variant:O="default"}=t,{loading_text:T="Loading..."}=t,{absolute:x=!0}=t,{translucent:b=!1}=t,{border:P=!1}=t,{autoscroll:I}=t,j,V=!1,X=0,z=0,Z=null,se=null,le=0,Y=null,ne,F=null,C=!0;const we=()=>{r(0,l=r(26,Z=r(19,f=null))),r(24,X=performance.now()),r(25,z=0),V=!0,ue()};function ue(){requestAnimationFrame(()=>{r(25,z=(performance.now()-X)/1e3),V&&ue()})}function ce(){r(25,z=0),r(0,l=r(26,Z=r(19,f=null))),V&&(V=!1)}jl(()=>{V&&ce()});let f=null;function h(_){Br[_?"unshift":"push"](()=>{F=_,r(16,F),r(7,w),r(14,Y),r(15,ne)})}function m(_){Br[_?"unshift":"push"](()=>{j=_,r(13,j)})}return e.$$set=_=>{"i18n"in _&&r(1,a=_.i18n),"eta"in _&&r(0,l=_.eta),"queue_position"in _&&r(2,c=_.queue_position),"queue_size"in _&&r(3,s=_.queue_size),"status"in _&&r(4,u=_.status),"scroll_to_output"in _&&r(21,p=_.scroll_to_output),"timer"in _&&r(5,y=_.timer),"show_progress"in _&&r(6,g=_.show_progress),"message"in _&&r(22,d=_.message),"progress"in _&&r(7,w=_.progress),"variant"in _&&r(8,O=_.variant),"loading_text"in _&&r(9,T=_.loading_text),"absolute"in _&&r(10,x=_.absolute),"translucent"in _&&r(11,b=_.translucent),"border"in _&&r(12,P=_.border),"autoscroll"in _&&r(23,I=_.autoscroll),"$$scope"in _&&r(28,i=_.$$scope)},e.$$.update=()=>{e.$$.dirty[0]&218103809&&(l===null&&r(0,l=Z),l!=null&&Z!==l&&(r(27,se=(performance.now()-X)/1e3+l),r(19,f=se.toFixed(1)),r(26,Z=l))),e.$$.dirty[0]&167772160&&r(17,le=se===null||se<=0||!z?null:Math.min(z/se,1)),e.$$.dirty[0]&128&&w!=null&&r(18,C=!1),e.$$.dirty[0]&114816&&(w!=null?r(14,Y=w.map(_=>{if(_.index!=null&&_.length!=null)return _.index/_.length;if(_.progress!=null)return _.progress})):r(14,Y=null),Y?(r(15,ne=Y[Y.length-1]),F&&(ne===0?r(16,F.style.transition="0",F):r(16,F.style.transition="150ms",F))):r(15,ne=void 0)),e.$$.dirty[0]&16&&(u==="pending"?we():ce()),e.$$.dirty[0]&10493968&&j&&p&&(u==="pending"||u==="complete")&&Jl(j,I),e.$$.dirty[0]&4194320,e.$$.dirty[0]&33554432&&r(20,n=z.toFixed(1))},[l,a,c,s,u,y,g,w,O,T,x,b,P,j,Y,ne,F,le,C,f,n,p,d,I,X,z,Z,se,i,o,h,m]}class $l extends kl{constructor(t){super(),Ml(this,t,Ql,Kl,Il,{i18n:1,eta:0,queue_position:2,queue_size:3,status:4,scroll_to_output:21,timer:5,show_progress:6,message:22,progress:7,variant:8,loading_text:9,absolute:10,translucent:11,border:12,autoscroll:23},null,[-1,-1])}}const xn={built_with_gradio:"تم الإنشاء بإستخدام Gradio",clear:"أمسح",or:"أو",submit:"أرسل"},Hn={click_to_upload:"إضغط للتحميل",drop_audio:"أسقط الملف الصوتي هنا",drop_csv:"أسقط ملف البيانات هنا",drop_file:"أسقط الملف هنا",drop_image:"أسقط الصورة هنا",drop_video:"أسقط الفيديو هنا"},eu={common:xn,upload_text:Hn},tu=Object.freeze(Object.defineProperty({__proto__:null,common:xn,default:eu,upload_text:Hn},Symbol.toStringTag,{value:"Module"})),An={built_with_gradio:"Construït amb gradio",clear:"Neteja",empty:"Buit",error:"Error",loading:"S'està carregant",or:"o",submit:"Envia"},Bn={click_to_upload:"Feu clic per pujar",drop_audio:"Deixeu anar l'àudio aquí",drop_csv:"Deixeu anar el CSV aquí",drop_file:"Deixeu anar el fitxer aquí",drop_image:"Deixeu anar la imatge aquí",drop_video:"Deixeu anar el vídeo aquí"},ru={common:An,upload_text:Bn},nu=Object.freeze(Object.defineProperty({__proto__:null,common:An,default:ru,upload_text:Bn},Symbol.toStringTag,{value:"Module"})),Cn={annotated_image:"وێنەی نیشانە کراو"},Mn={allow_recording_access:"تکایە ڕێگە بدە بە بەکارهێنانی مایکرۆفۆنەکە بۆ تۆمارکردن.",audio:"دەنگ",record_from_microphone:"تۆمارکردن لە مایکەوە",stop_recording:"تۆمارکردن بوەستێنە"},Nn={connection_can_break:"لە مۆبایلدا، پەیوەندییەکە دەکرێت بپچڕێت ئەگەر ئەم تابە چالاک نەبێت یان ئامێرەکە بچێتە دۆخی پشوو، ئەمەش شوێنی خۆت لە ڕیزدا لەدەست دەدات.",long_requests_queue:"ڕیزێکی درێژی داواکاری هەیە. ئەم سپەیسە دووباد بکە بۆی چاوەڕوان نەبیت.",lost_connection:"پەیوەندی پچڕا بەهۆی جێهێشتنی پەیج. "},In={checkbox:"بۆکسی هەڵبژاردن",checkbox_group:"گروپی بۆکسی هەڵبژاردن"},Ln={code:"کۆد"},Rn={color_picker:"ڕەنگ هەڵبژاردە"},jn={built_with:"دروستکراوە لەگەڵ...",built_with_gradio:"Gradio دروستکراوە بە",clear:"خاوێنکردنەوە",download:"دابەزاندن",edit:"بژارکردن",empty:"بەتاڵ",error:"هەڵە",hosted_on:"میوانداری کراوە لە",loading:"بارکردن",logo:"لۆگۆ",or:"یان",remove:"لابردن",share:"هاوبەشکردن",submit:"پێشکەشکردن",undo:"پووچکردنەوە"},Dn={incorrect_format:"فۆرماتێکی هەڵە، تەنها فایلەکانی CSV و TSV پشتگیری دەکرێن",new_column:"ستوونی نوێ",new_row:"ڕیزێکی نوێ"},Fn={dropdown:"فڕێدانە خوار"},Gn={build_error:"هەڵەی دروستکردن هەیە",config_error:"هەڵەی ڕێکخستن هەیە",contact_page_author:"تکایە پەیوەندی بە نووسەری پەیجەوە بکەن بۆ ئەوەی ئاگاداریان بکەنەوە.",no_app_file:"هیچ فایلێکی ئەپ نییە",runtime_error:"هەڵەیەکی runtime هەیە",space_not_working:'"سپەیسەکە کارناکات چونکە" {0}',space_paused:"فەزاکە وەستاوە",use_via_api:"لە ڕێگەی API بەکاری بهێنە"},Un={uploading:"بارکردن..."},Vn={highlighted_text:"دەقی ڕۆشن کراو"},zn={allow_webcam_access:"تکایە ڕێگە بدە بە بەکارهێنانی وێبکامەکە بۆ تۆمارکردن.",brush_color:"ڕەنگی فڵچە",brush_radius:"تیژڕەوی فڵچە",image:"وێنە",remove_image:"لابردنی وێنە",select_brush_color:"ڕەنگی فڵچە هەڵبژێرە",start_drawing:"دەست بکە بە وێنەکێشان",use_brush:"فڵچە بەکاربهێنە"},qn={label:"لەیبڵ"},Xn={enable_cookies:"ئەگەر تۆ سەردانی HuggingFace Space دەکەیت لە دۆخی نادیاردا، پێویستە کووکی لایەنی سێیەم چالاک بکەیت.",incorrect_credentials:"بڕوانامەی هەڵە",login:"چونه‌ ژووره‌وه‌"},Yn={number:"ژمارە"},Wn={plot:"هێڵکاری"},Zn={radio:"ڕادیۆ"},Kn={slider:"خلیسکە"},Jn={click_to_upload:"کلیک بکە بۆ بارکردن",drop_audio:"دەنگ لێرە دابنێ",drop_csv:"لێرەدا CSV دابنێ",drop_file:"فایل لێرە دابنێ",drop_image:"وێنە لێرەدا دابنێ",drop_video:"ڤیدیۆ لێرە دابنێ"},ou={"3D_model":{"3d_model":"مۆدێلی سێ ڕەهەندی"},annotated_image:Cn,audio:Mn,blocks:Nn,checkbox:In,code:Ln,color_picker:Rn,common:jn,dataframe:Dn,dropdown:Fn,errors:Gn,file:Un,highlighted_text:Vn,image:zn,label:qn,login:Xn,number:Yn,plot:Wn,radio:Zn,slider:Kn,upload_text:Jn},iu=Object.freeze(Object.defineProperty({__proto__:null,annotated_image:Cn,audio:Mn,blocks:Nn,checkbox:In,code:Ln,color_picker:Rn,common:jn,dataframe:Dn,default:ou,dropdown:Fn,errors:Gn,file:Un,highlighted_text:Vn,image:zn,label:qn,login:Xn,number:Yn,plot:Wn,radio:Zn,slider:Kn,upload_text:Jn},Symbol.toStringTag,{value:"Module"})),Qn={built_with_gradio:"Mit Gradio erstellt",clear:"Löschen",or:"oder",submit:"Absenden"},$n={click_to_upload:"Hochladen",drop_audio:"Audio hier ablegen",drop_csv:"CSV Datei hier ablegen",drop_file:"Datei hier ablegen",drop_image:"Bild hier ablegen",drop_video:"Video hier ablegen"},au={common:Qn,upload_text:$n},su=Object.freeze(Object.defineProperty({__proto__:null,common:Qn,default:au,upload_text:$n},Symbol.toStringTag,{value:"Module"})),eo={annotated_image:"Annotated Image"},to={allow_recording_access:"Please allow access to the microphone for recording.",audio:"Audio",record_from_microphone:"Record from microphone",stop_recording:"Stop recording",no_device_support:"Media devices could not be accessed. Check that you are running on a secure origin (https) or localhost (or you have passed a valid SSL certificate to ssl_verify), and you have allowed browser access to your device.",stop:"Stop",resume:"Resume",record:"Record",no_microphone:"No microphone found",pause:"Pause",play:"Play"},ro={connection_can_break:"On mobile, the connection can break if this tab is unfocused or the device sleeps, losing your position in queue.",long_requests_queue:"There is a long queue of requests pending. Duplicate this Space to skip.",lost_connection:"Lost connection due to leaving page. Rejoining queue..."},no={checkbox:"Checkbox",checkbox_group:"Checkbox Group"},oo={code:"Code"},io={color_picker:"Color Picker"},ao={built_with:"built with",built_with_gradio:"Built with Gradio",clear:"Clear",download:"Download",edit:"Edit",empty:"Empty",error:"Error",hosted_on:"Hosted on",loading:"Loading",logo:"logo",or:"or",remove:"Remove",share:"Share",submit:"Submit",undo:"Undo",no_devices:"No devices found"},so={incorrect_format:"Incorrect format, only CSV and TSV files are supported",new_column:"New column",new_row:"New row"},lo={dropdown:"Dropdown"},uo={build_error:"there is a build error",config_error:"there is a config error",contact_page_author:"Please contact the author of the page to let them know.",no_app_file:"there is no app file",runtime_error:"there is a runtime error",space_not_working:`"Space isn't working because" {0}`,space_paused:"the space is paused",use_via_api:"Use via API"},co={uploading:"Uploading..."},_o={highlighted_text:"Highlighted Text"},fo={allow_webcam_access:"Please allow access to the webcam for recording.",brush_color:"Brush color",brush_radius:"Brush radius",image:"Image",remove_image:"Remove Image",select_brush_color:"Select brush color",start_drawing:"Start drawing",use_brush:"Use brush"},ho={label:"Label"},mo={enable_cookies:"If you are visiting a HuggingFace Space in Incognito mode, you must enable third party cookies.",incorrect_credentials:"Incorrect Credentials",login:"Login"},po={number:"Number"},go={plot:"Plot"},bo={radio:"Radio"},vo={slider:"Slider"},yo={click_to_upload:"Click to Upload",drop_audio:"Drop Audio Here",drop_csv:"Drop CSV Here",drop_file:"Drop File Here",drop_image:"Drop Image Here",drop_video:"Drop Video Here",drop_gallery:"Drop Image(s) Here",paste_clipboard:"Paste from Clipboard"},lu={"3D_model":{"3d_model":"3D Model"},annotated_image:eo,audio:to,blocks:ro,checkbox:no,code:oo,color_picker:io,common:ao,dataframe:so,dropdown:lo,errors:uo,file:co,highlighted_text:_o,image:fo,label:ho,login:mo,number:po,plot:go,radio:bo,slider:vo,upload_text:yo},uu=Object.freeze(Object.defineProperty({__proto__:null,annotated_image:eo,audio:to,blocks:ro,checkbox:no,code:oo,color_picker:io,common:ao,dataframe:so,default:lu,dropdown:lo,errors:uo,file:co,highlighted_text:_o,image:fo,label:ho,login:mo,number:po,plot:go,radio:bo,slider:vo,upload_text:yo},Symbol.toStringTag,{value:"Module"})),Eo={built_with_gradio:"Construido con Gradio",clear:"Limpiar",or:"o",submit:"Enviar"},wo={click_to_upload:"Haga click para cargar",drop_audio:"Coloque el audio aquí",drop_csv:"Coloque el CSV aquí",drop_file:"Coloque el archivo aquí",drop_image:"Coloque la imagen aquí",drop_video:"Coloque el video aquí",drop_gallery:"Coloque las imagenes aquí"},cu={common:Eo,upload_text:wo},_u=Object.freeze(Object.defineProperty({__proto__:null,common:Eo,default:cu,upload_text:wo},Symbol.toStringTag,{value:"Module"})),So={built_with_gradio:"Gradiorekin eraikia",clear:"Garbitu",or:"edo",submit:"Bidali"},To={click_to_upload:"Klik egin kargatzeko",drop_audio:"Jarri hemen audioa",drop_csv:"Jarri hemen CSVa",drop_file:"Jarri hemen fitxategia",drop_image:"Jarri hemen irudia",drop_video:"Jarri hemen bideoa"},fu={common:So,upload_text:To},hu=Object.freeze(Object.defineProperty({__proto__:null,common:So,default:fu,upload_text:To},Symbol.toStringTag,{value:"Module"})),Po={built_with_gradio:"ساخته شده با gradio",clear:"حذف",or:"یا",submit:"ارسال"},Oo={click_to_upload:"برای آپلود کلیک کنید",drop_audio:"صوت را اینجا رها کنید",drop_csv:"فایل csv را  اینجا رها کنید",drop_file:"فایل را اینجا رها کنید",drop_image:"تصویر را اینجا رها کنید",drop_video:"ویدیو را اینجا رها کنید"},du={common:Po,upload_text:Oo},mu=Object.freeze(Object.defineProperty({__proto__:null,common:Po,default:du,upload_text:Oo},Symbol.toStringTag,{value:"Module"})),ko={allow_recording_access:"Veuillez autoriser l'accès à l'enregistrement",audio:"Audio",record_from_microphone:"Enregistrer avec le microphone",stop_recording:"Arrêter l'enregistrement"},xo={built_with:"Construit avec",built_with_gradio:"Construit avec Gradio",clear:"Effacer",download:"Télécharger",edit:"Éditer",error:"Erreur",loading:"Chargement",logo:"logo",or:"ou",remove:"Supprimer",share:"Partager",submit:"Soumettre"},Ho={click_to_upload:"Cliquer pour Télécharger",drop_audio:"Déposer l'Audio Ici",drop_csv:"Déposer le CSV Ici",drop_file:"Déposer le Fichier Ici",drop_image:"Déposer l'Image Ici",drop_video:"Déposer la Vidéo Ici"},pu={audio:ko,common:xo,upload_text:Ho},gu=Object.freeze(Object.defineProperty({__proto__:null,audio:ko,common:xo,default:pu,upload_text:Ho},Symbol.toStringTag,{value:"Module"})),Ao={built_with_gradio:"בנוי עם גרדיו",clear:"נקה",or:"או",submit:"שלח"},Bo={click_to_upload:"לחץ כדי להעלות",drop_audio:"גרור לכאן קובץ שמע",drop_csv:"גרור csv קובץ לכאן",drop_file:"גרור קובץ לכאן",drop_image:"גרור קובץ תמונה לכאן",drop_video:"גרור קובץ סרטון לכאן"},bu={common:Ao,upload_text:Bo},vu=Object.freeze(Object.defineProperty({__proto__:null,common:Ao,default:bu,upload_text:Bo},Symbol.toStringTag,{value:"Module"})),Co={built_with_gradio:"Gradio से बना",clear:"हटाये",or:"या",submit:"सबमिट करे"},Mo={click_to_upload:"अपलोड के लिए बटन दबायें",drop_audio:"यहाँ ऑडियो ड्रॉप करें",drop_csv:"यहाँ CSV ड्रॉप करें",drop_file:"यहाँ File ड्रॉप करें",drop_image:"यहाँ इमेज ड्रॉप करें",drop_video:"यहाँ वीडियो ड्रॉप करें"},yu={common:Co,upload_text:Mo},Eu=Object.freeze(Object.defineProperty({__proto__:null,common:Co,default:yu,upload_text:Mo},Symbol.toStringTag,{value:"Module"})),No={built_with_gradio:"gradioで作ろう",clear:"クリア",or:"または",submit:"送信"},Io={click_to_upload:"クリックしてアップロード",drop_audio:"ここに音声をドロップ",drop_csv:"ここにCSVをドロップ",drop_file:"ここにファイルをドロップ",drop_image:"ここに画像をドロップ",drop_video:"ここに動画をドロップ"},wu={common:No,upload_text:Io},Su=Object.freeze(Object.defineProperty({__proto__:null,common:No,default:wu,upload_text:Io},Symbol.toStringTag,{value:"Module"})),Lo={built_with_gradio:"gradio로 제작되었습니다",clear:"클리어",or:"또는",submit:"제출하기"},Ro={click_to_upload:"클릭해서 업로드하기",drop_audio:"오디오를 끌어 놓으세요",drop_csv:"CSV파일을 끌어 놓으세요",drop_file:"파일을 끌어 놓으세요",drop_image:"이미지를 끌어 놓으세요",drop_video:"비디오를 끌어 놓으세요"},Tu={common:Lo,upload_text:Ro},Pu=Object.freeze(Object.defineProperty({__proto__:null,common:Lo,default:Tu,upload_text:Ro},Symbol.toStringTag,{value:"Module"})),jo={built_with_gradio:"sukurta su gradio",clear:"Trinti",or:"arba",submit:"Pateikti"},Do={click_to_upload:"Spustelėkite norėdami įkelti",drop_audio:"Įkelkite garso įrašą čia",drop_csv:"Įkelkite CSV čia",drop_file:"Įkelkite bylą čia",drop_image:"Įkelkite paveikslėlį čia",drop_video:"Įkelkite vaizdo įrašą čia"},Ou={common:jo,upload_text:Do},ku=Object.freeze(Object.defineProperty({__proto__:null,common:jo,default:Ou,upload_text:Do},Symbol.toStringTag,{value:"Module"})),Fo={built_with_gradio:"gemaakt met gradio",clear:"Wis",or:"of",submit:"Zend in"},Go={click_to_upload:"Klik om the Uploaden",drop_audio:"Sleep een Geluidsbestand hier",drop_csv:"Sleep een CSV hier",drop_file:"Sleep een Document hier",drop_image:"Sleep een Afbeelding hier",drop_video:"Sleep een Video hier"},xu={common:Fo,upload_text:Go},Hu=Object.freeze(Object.defineProperty({__proto__:null,common:Fo,default:xu,upload_text:Go},Symbol.toStringTag,{value:"Module"})),Uo={built_with_gradio:"utworzone z gradio",clear:"Wyczyść",or:"lub",submit:"Zatwierdź"},Vo={click_to_upload:"Kliknij, aby przesłać",drop_audio:"Przeciągnij tutaj audio",drop_csv:"Przeciągnij tutaj CSV",drop_file:"Przeciągnij tutaj plik",drop_image:"Przeciągnij tutaj zdjęcie",drop_video:"Przeciągnij tutaj video"},Au={common:Uo,upload_text:Vo},Bu=Object.freeze(Object.defineProperty({__proto__:null,common:Uo,default:Au,upload_text:Vo},Symbol.toStringTag,{value:"Module"})),zo={built_with_gradio:"Construído com gradio",clear:"Limpar",error:"Erro",flag:"Marcar",loading:"Carregando",or:"ou",submit:"Enviar"},qo={click_to_upload:"Clique para o Upload",drop_audio:"Solte o Áudio Aqui",drop_csv:"Solte o CSV Aqui",drop_file:"Solte o Arquivo Aqui",drop_image:"Solte a Imagem Aqui",drop_video:"Solte o Vídeo Aqui"},Cu={common:zo,upload_text:qo},Mu=Object.freeze(Object.defineProperty({__proto__:null,common:zo,default:Cu,upload_text:qo},Symbol.toStringTag,{value:"Module"})),Xo={annotated_image:"Аннотированное изображение"},Yo={allow_recording_access:"Пожалуйста, предоставьте доступ к микрофону для записи.",audio:"Аудио",record_from_microphone:"Записать с микрофона",stop_recording:"Остановить запись",no_device_support:"Не удалось получить доступ к медиаустройствам. Убедитесь, что вы работаете на защищенном источнике (https) или localhost (или передали действительный SSL-сертификат в ssl_verify), и разрешили браузеру доступ к устройству.",stop:"Стоп",resume:"Продолжить",record:"Записать",no_microphone:"Микрофон не найден",pause:"Пауза",play:"Воспроизвести"},Wo={connection_can_break:"На мобильных устройствах соединение может прерваться, если вкладка будет переключена или устройство отключится, что приведет к потере вашей позиции в очереди.",long_requests_queue:"Очередь запросов длинная. Продублируйте это пространство, чтобы пропустить.",lost_connection:"Потеряно соединение из-за ухода со страницы. Повторное подключение..."},Zo={checkbox:"Чекбокс",checkbox_group:"Группа чекбоксов"},Ko={code:"Код"},Jo={color_picker:"Выбор цвета"},Qo={built_with:"создано с",built_with_gradio:"Создано с помощью Gradio",clear:"Очистить",download:"Скачать",edit:"Изменить",empty:"Пусто",error:"Ошибка",hosted_on:"Размещено на",loading:"Загрузка",logo:"логотип",or:"или",remove:"Удалить",share:"Поделиться",submit:"Отправить",undo:"Отменить",no_devices:"Не найдено ни одного устройства"},$o={incorrect_format:"Неправильный формат, поддерживаются только файлы CSV и TSV",new_column:"Новая колонка",new_row:"Новый ряд"},ei={dropdown:"Dropdown"},ti={build_error:"возникла ошибка сборки",config_error:"возникла ошибка конфигурации",contact_page_author:"Пожалуйста, свяжитесь с автором страницы, чтобы сообщить ему об этом.",no_app_file:"отсутствует файл приложения",runtime_error:"возникла проблема с выполнением",space_not_working:'"Пространство не работает, потому что" {0}',space_paused:"пространство приостановлено",use_via_api:"Использовать через API"},ri={uploading:"Загружаем..."},ni={highlighted_text:"Выделенный текст"},oi={allow_webcam_access:"Пожалуйста, разрешите доступ к веб-камере для записи.",brush_color:"Цвет кисти",brush_radius:"Радиус кисти",image:"Изображение",remove_image:"Удалить изображение",select_brush_color:"Выберите цвет кисти",start_drawing:"Начните рисовать",use_brush:"Используйте кисть"},ii={label:"Лейбл"},ai={enable_cookies:"Если вы посещаете пространство HuggingFace в режиме инкогнито, вы должны разрешить сторонние файлы cookie.",incorrect_credentials:"Неправильные учетные данные",login:"Вход в систему"},si={number:"Число"},li={plot:"Схема"},ui={radio:"Радио"},ci={slider:"Слайдер"},_i={click_to_upload:"Нажмите, чтобы загрузить",drop_audio:"Перетащите аудио сюда",drop_csv:"Перетащите файл CSV сюда",drop_file:"Перетащите файл сюда",drop_image:"Перетащите изображение сюда",drop_video:"Перетащите видео сюда",drop_gallery:"Перетащите изображение(-я) сюда",paste_clipboard:"Вставка из буфера обмена"},Nu={"3D_model":{"3d_model":"3D-модель"},annotated_image:Xo,audio:Yo,blocks:Wo,checkbox:Zo,code:Ko,color_picker:Jo,common:Qo,dataframe:$o,dropdown:ei,errors:ti,file:ri,highlighted_text:ni,image:oi,label:ii,login:ai,number:si,plot:li,radio:ui,slider:ci,upload_text:_i},Iu=Object.freeze(Object.defineProperty({__proto__:null,annotated_image:Xo,audio:Yo,blocks:Wo,checkbox:Zo,code:Ko,color_picker:Jo,common:Qo,dataframe:$o,default:Nu,dropdown:ei,errors:ti,file:ri,highlighted_text:ni,image:oi,label:ii,login:ai,number:si,plot:li,radio:ui,slider:ci,upload_text:_i},Symbol.toStringTag,{value:"Module"})),fi={built_with_gradio:"கிரேடியோ வுடன் உருவாக்கப்பட்டது",clear:"அழிக்கவும்",or:"அல்லது",submit:"சமர்ப்பிக்கவும்"},hi={click_to_upload:"பதிவேற்ற அழுத்தவும்",drop_audio:"ஆடியோவை பதிவேற்றவும்",drop_csv:"csv ஐ பதிவேற்றவும்",drop_file:"கோப்பை பதிவேற்றவும்",drop_image:"படத்தை பதிவேற்றவும்",drop_video:"காணொளியை பதிவேற்றவும்"},Lu={common:fi,upload_text:hi},Ru=Object.freeze(Object.defineProperty({__proto__:null,common:fi,default:Lu,upload_text:hi},Symbol.toStringTag,{value:"Module"})),di={built_with_gradio:"Gradio ile oluşturulmuştur",clear:"Temizle",or:"veya",submit:"Yükle"},mi={click_to_upload:"Yüklemek için Tıkla",drop_audio:"Kaydı Buraya Sürükle",drop_csv:"CSV'yi Buraya Sürükle",drop_file:"Dosyayı Buraya Sürükle",drop_image:"Resmi Buraya Sürükle",drop_video:"Videoyu Buraya Sürükle"},ju={common:di,upload_text:mi},Du=Object.freeze(Object.defineProperty({__proto__:null,common:di,default:ju,upload_text:mi},Symbol.toStringTag,{value:"Module"})),pi={built_with_gradio:"Зроблено на основі gradio",clear:"Очистити",or:"або",submit:"Надіслати"},gi={click_to_upload:"Натисніть щоб завантажити",drop_audio:"Перетягніть аудіо сюди",drop_csv:"Перетягніть CSV-файл сюди",drop_file:"Перетягніть файл сюди",drop_image:"Перетягніть зображення сюди",drop_video:"Перетягніть відео сюди"},Fu={common:pi,upload_text:gi},Gu=Object.freeze(Object.defineProperty({__proto__:null,common:pi,default:Fu,upload_text:gi},Symbol.toStringTag,{value:"Module"})),bi={built_with_gradio:"کے ساتھ بنایا گیا Gradio",clear:"ہٹا دیں",or:"یا",submit:"جمع کریں"},vi={click_to_upload:"اپ لوڈ کے لیے کلک کریں",drop_audio:"یہاں آڈیو ڈراپ کریں",drop_csv:"یہاں فائل ڈراپ کریں",drop_file:"یہاں فائل ڈراپ کریں",drop_image:"یہاں تصویر ڈراپ کریں",drop_video:"یہاں ویڈیو ڈراپ کریں"},Uu={common:bi,upload_text:vi},Vu=Object.freeze(Object.defineProperty({__proto__:null,common:bi,default:Uu,upload_text:vi},Symbol.toStringTag,{value:"Module"})),yi={built_with_gradio:"gradio bilan qilingan",clear:"Tozalash",submit:"Yubor"},Ei={click_to_upload:"Yuklash uchun Bosing",drop_audio:"Audioni Shu Yerga Tashlang",drop_csv:"CSVni Shu Yerga Tashlang",drop_file:"Faylni Shu Yerga Tashlang",drop_image:"Rasmni Shu Yerga Tashlang",drop_video:"Videoni Shu Yerga Tashlang"},zu={common:yi,upload_text:Ei},qu=Object.freeze(Object.defineProperty({__proto__:null,common:yi,default:zu,upload_text:Ei},Symbol.toStringTag,{value:"Module"})),wi={annotated_image:"标注图像"},Si={allow_recording_access:"请允许访问麦克风以进行录音。",audio:"音频",record_from_microphone:"从麦克风录制",stop_recording:"停止录制",no_device_support:"无法访问媒体设备。请检查您是否在安全来源（https）或本地主机上运行（或者您已经通过 ssl_verify 传递了有效的 SSL 证书），并且您已经允许浏览器访问您的设备。",stop:"停止",resume:"继续",record:"录制",no_microphone:"找不到麦克风",pause:"暂停",play:"播放"},Ti={connection_can_break:"在移动设备上，如果此标签页失去焦点或设备休眠，连接可能会中断，导致您在队列中失去位置。",long_requests_queue:"有一个长时间的待处理请求队列。复制此空间以跳过。",lost_connection:"由于离开页面，连接已丢失。重新加入队列..."},Pi={checkbox:"复选框",checkbox_group:"复选框组"},Oi={code:"代码"},ki={color_picker:"颜色选择器"},xi={built_with:"构建于",built_with_gradio:"使用 Gradio 构建",clear:"清除",download:"下载",edit:"编辑",empty:"空",error:"错误",hosted_on:"托管在",loading:"加载中",logo:"标志",or:"或",remove:"移除",share:"分享",submit:"提交",undo:"撤销"},Hi={incorrect_format:"格式不正确，仅支持 CSV 和 TSV 文件",new_column:"新列",new_row:"新行"},Ai={dropdown:"下拉菜单"},Bi={build_error:"存在构建错误",config_error:"存在配置错误",contact_page_author:"请联系页面的作者并告知他们。",no_app_file:"不存在应用文件",runtime_error:"存在运行时错误",space_not_working:'"空间无法工作，原因：" {0}',space_paused:"空间已暂停",use_via_api:"通过 API 使用"},Ci={uploading:"正在上传..."},Mi={highlighted_text:"高亮文本"},Ni={allow_webcam_access:"请允许访问网络摄像头以进行录制。",brush_color:"画笔颜色",brush_radius:"画笔半径",image:"图像",remove_image:"移除图像",select_brush_color:"选择画笔颜色",start_drawing:"开始绘画",use_brush:"使用画笔"},Ii={label:"标签"},Li={enable_cookies:"如果您正在使用隐身模式访问 HuggingFace 空间，您必须启用第三方 cookie。",incorrect_credentials:"凭据不正确",login:"登录"},Ri={number:"数字"},ji={plot:"图表"},Di={radio:"单选框"},Fi={slider:"滑块"},Gi={click_to_upload:"点击上传",drop_audio:"将音频拖放到此处",drop_csv:"将 CSV 文件拖放到此处",drop_file:"将文件拖放到此处",drop_image:"将图像拖放到此处",drop_video:"将视频拖放到此处"},Xu={"3D_model":{"3d_model":"3D模型"},annotated_image:wi,audio:Si,blocks:Ti,checkbox:Pi,code:Oi,color_picker:ki,common:xi,dataframe:Hi,dropdown:Ai,errors:Bi,file:Ci,highlighted_text:Mi,image:Ni,label:Ii,login:Li,number:Ri,plot:ji,radio:Di,slider:Fi,upload_text:Gi},Yu=Object.freeze(Object.defineProperty({__proto__:null,annotated_image:wi,audio:Si,blocks:Ti,checkbox:Pi,code:Oi,color_picker:ki,common:xi,dataframe:Hi,default:Xu,dropdown:Ai,errors:Bi,file:Ci,highlighted_text:Mi,image:Ni,label:Ii,login:Li,number:Ri,plot:ji,radio:Di,slider:Fi,upload_text:Gi},Symbol.toStringTag,{value:"Module"})),Ui={built_with_gradio:"使用Gradio構建",clear:"清除",or:"或",submit:"提交"},Vi={click_to_upload:"點擊上傳",drop_audio:"拖放音訊至此處",drop_csv:"拖放CSV至此處",drop_file:"拖放檔案至此處",drop_image:"拖放圖片至此處",drop_video:"拖放影片至此處"},Wu={common:Ui,upload_text:Vi},Zu=Object.freeze(Object.defineProperty({__proto__:null,common:Ui,default:Wu,upload_text:Vi},Symbol.toStringTag,{value:"Module"})),Xr=Object.assign({"./lang/ar.json":tu,"./lang/ca.json":nu,"./lang/ckb.json":iu,"./lang/de.json":su,"./lang/en.json":uu,"./lang/es.json":_u,"./lang/eu.json":hu,"./lang/fa.json":mu,"./lang/fr.json":gu,"./lang/he.json":vu,"./lang/hi.json":Eu,"./lang/ja.json":Su,"./lang/ko.json":Pu,"./lang/lt.json":ku,"./lang/nl.json":Hu,"./lang/pl.json":Bu,"./lang/pt-BR.json":Mu,"./lang/ru.json":Iu,"./lang/ta.json":Ru,"./lang/tr.json":Du,"./lang/uk.json":Gu,"./lang/ur.json":Vu,"./lang/uz.json":qu,"./lang/zh-CN.json":Yu,"./lang/zh-TW.json":Zu});function Ku(){let e={};for(const t in Xr){const r=t.split("/").pop().split(".").shift();e[r]=Xr[t].default}return e}const Yr=Ku();for(const e in Yr)gn(e,Yr[e]);async function Ju(){await Ns({fallbackLocale:"en",initialLocale:Vs()})}const{setContext:Qu,getContext:$u}=window.__gradio__svelte__internal,zi="WORKER_PROXY_CONTEXT_KEY";function ec(e){Qu(zi,e)}function Pc(){return $u(zi)}const{SvelteComponent:tc,add_flush_callback:Qt,append:ke,assign:rc,attr:Be,bind:$t,binding_callbacks:er,check_outros:Wr,component_subscribe:Zr,create_component:Nt,destroy_component:It,detach:_t,element:ze,empty:nc,get_spread_object:oc,get_spread_update:ic,group_outros:Kr,init:ac,insert:ft,mount_component:Lt,safe_not_equal:sc,set_data:qi,space:Xi,text:at,transition_in:Pe,transition_out:Ce}=window.__gradio__svelte__internal,{onMount:Jr,setContext:zt,createEventDispatcher:lc}=window.__gradio__svelte__internal;function Qr(e){let t,r;return t=new $l({props:{absolute:!e[4],status:e[14],timer:!1,queue_position:null,queue_size:null,translucent:!0,loading_text:e[15],i18n:e[21],autoscroll:e[0],$$slots:{error:[_c]},$$scope:{ctx:e}}}),{c(){Nt(t.$$.fragment)},m(n,o){Lt(t,n,o),r=!0},p(n,o){const i={};o[0]&16&&(i.absolute=!n[4]),o[0]&16384&&(i.status=n[14]),o[0]&32768&&(i.loading_text=n[15]),o[0]&2097152&&(i.i18n=n[21]),o[0]&1&&(i.autoscroll=n[0]),o[0]&2105600|o[1]&1048576&&(i.$$scope={dirty:o,ctx:n}),t.$set(i)},i(n){r||(Pe(t.$$.fragment,n),r=!0)},o(n){Ce(t.$$.fragment,n),r=!1},d(n){It(t,n)}}}function uc(e){let t,r=e[21]("errors.contact_page_author")+"",n;return{c(){t=ze("p"),n=at(r),Be(t,"class","svelte-y6l4b")},m(o,i){ft(o,t,i),ke(t,n)},p(o,i){i[0]&2097152&&r!==(r=o[21]("errors.contact_page_author")+"")&&qi(n,r)},d(o){o&&_t(t)}}}function cc(e){let t,r,n,o,i,a;return{c(){t=ze("p"),r=at("Please "),n=ze("a"),o=at("contact the author of the space"),a=at(" to let them know."),Be(n,"href",i="https://huggingface.co/spaces/"+e[8]+"/discussions/new?title="+e[22].title(e[13]?.detail)+"&description="+e[22].description(e[13]?.detail,location.origin)),Be(n,"class","svelte-y6l4b"),Be(t,"class","svelte-y6l4b")},m(l,c){ft(l,t,c),ke(t,r),ke(t,n),ke(n,o),ke(t,a)},p(l,c){c[0]&8448&&i!==(i="https://huggingface.co/spaces/"+l[8]+"/discussions/new?title="+l[22].title(l[13]?.detail)+"&description="+l[22].description(l[13]?.detail,location.origin))&&Be(n,"href",i)},d(l){l&&_t(t)}}}function _c(e){let t,r,n,o=(e[13]?.message||"")+"",i,a;function l(u,p){return(u[13].status==="space_error"||u[13].status==="paused")&&u[13].discussions_enabled?cc:uc}let c=l(e),s=c(e);return{c(){t=ze("div"),r=ze("p"),n=ze("strong"),i=at(o),a=Xi(),s.c(),Be(r,"class","svelte-y6l4b"),Be(t,"class","error svelte-y6l4b"),Be(t,"slot","error")},m(u,p){ft(u,t,p),ke(t,r),ke(r,n),ke(n,i),ke(t,a),s.m(t,null)},p(u,p){p[0]&8192&&o!==(o=(u[13]?.message||"")+"")&&qi(i,o),c===(c=l(u))&&s?s.p(u,p):(s.d(1),s=c(u),s&&(s.c(),s.m(t,null)))},d(u){u&&_t(t),s.d()}}}function fc(e){let t,r,n,o;const i=[{app:e[17]},e[12],{fill_height:!e[4]&&e[12].fill_height},{theme_mode:e[16]},{control_page_title:e[5]},{target:e[9]},{autoscroll:e[0]},{show_footer:!e[4]},{app_mode:e[3]},{version:e[1]}];function a(s){e[34](s)}function l(s){e[35](s)}let c={};for(let s=0;s<i.length;s+=1)c=rc(c,i[s]);return e[10]!==void 0&&(c.ready=e[10]),e[11]!==void 0&&(c.render_complete=e[11]),t=new e[19]({props:c}),er.push(()=>$t(t,"ready",a)),er.push(()=>$t(t,"render_complete",l)),{c(){Nt(t.$$.fragment)},m(s,u){Lt(t,s,u),o=!0},p(s,u){const p=u[0]&201275?ic(i,[u[0]&131072&&{app:s[17]},u[0]&4096&&oc(s[12]),u[0]&4112&&{fill_height:!s[4]&&s[12].fill_height},u[0]&65536&&{theme_mode:s[16]},u[0]&32&&{control_page_title:s[5]},u[0]&512&&{target:s[9]},u[0]&1&&{autoscroll:s[0]},u[0]&16&&{show_footer:!s[4]},u[0]&8&&{app_mode:s[3]},u[0]&2&&{version:s[1]}]):{};!r&&u[0]&1024&&(r=!0,p.ready=s[10],Qt(()=>r=!1)),!n&&u[0]&2048&&(n=!0,p.render_complete=s[11],Qt(()=>n=!1)),t.$set(p)},i(s){o||(Pe(t.$$.fragment,s),o=!0)},o(s){Ce(t.$$.fragment,s),o=!1},d(s){It(t,s)}}}function hc(e){let t,r;return t=new e[20]({props:{auth_message:e[12].auth_message,root:e[12].root,space_id:e[8],app_mode:e[3]}}),{c(){Nt(t.$$.fragment)},m(n,o){Lt(t,n,o),r=!0},p(n,o){const i={};o[0]&4096&&(i.auth_message=n[12].auth_message),o[0]&4096&&(i.root=n[12].root),o[0]&256&&(i.space_id=n[8]),o[0]&8&&(i.app_mode=n[3]),t.$set(i)},i(n){r||(Pe(t.$$.fragment,n),r=!0)},o(n){Ce(t.$$.fragment,n),r=!1},d(n){It(t,n)}}}function dc(e){let t,r,n,o,i,a=(e[14]==="pending"||e[14]==="error")&&!(e[12]&&e[12]?.auth_required)&&Qr(e);const l=[hc,fc],c=[];function s(u,p){return u[12]?.auth_required&&u[20]?0:u[12]&&u[19]&&u[18]?1:-1}return~(r=s(e))&&(n=c[r]=l[r](e)),{c(){a&&a.c(),t=Xi(),n&&n.c(),o=nc()},m(u,p){a&&a.m(u,p),ft(u,t,p),~r&&c[r].m(u,p),ft(u,o,p),i=!0},p(u,p){(u[14]==="pending"||u[14]==="error")&&!(u[12]&&u[12]?.auth_required)?a?(a.p(u,p),p[0]&20480&&Pe(a,1)):(a=Qr(u),a.c(),Pe(a,1),a.m(t.parentNode,t)):a&&(Kr(),Ce(a,1,1,()=>{a=null}),Wr());let y=r;r=s(u),r===y?~r&&c[r].p(u,p):(n&&(Kr(),Ce(c[y],1,1,()=>{c[y]=null}),Wr()),~r?(n=c[r],n?n.p(u,p):(n=c[r]=l[r](u),n.c()),Pe(n,1),n.m(o.parentNode,o)):n=null)},i(u){i||(Pe(a),Pe(n),i=!0)},o(u){Ce(a),Ce(n),i=!1},d(u){u&&(_t(t),_t(o)),a&&a.d(u),~r&&c[r].d(u)}}}function mc(e){let t,r,n;function o(a){e[36](a)}let i={display:e[6]&&e[4],is_embed:e[4],info:!!e[8]&&e[7],version:e[1],initial_height:e[2],space:e[8],loaded:e[14]==="complete",$$slots:{default:[dc]},$$scope:{ctx:e}};return e[9]!==void 0&&(i.wrapper=e[9]),t=new pl({props:i}),er.push(()=>$t(t,"wrapper",o)),{c(){Nt(t.$$.fragment)},m(a,l){Lt(t,a,l),n=!0},p(a,l){const c={};l[0]&80&&(c.display=a[6]&&a[4]),l[0]&16&&(c.is_embed=a[4]),l[0]&384&&(c.info=!!a[8]&&a[7]),l[0]&2&&(c.version=a[1]),l[0]&4&&(c.initial_height=a[2]),l[0]&256&&(c.space=a[8]),l[0]&16384&&(c.loaded=a[14]==="complete"),l[0]&4194107|l[1]&1048576&&(c.$$scope={dirty:l,ctx:a}),!r&&l[0]&512&&(r=!0,c.wrapper=a[9],Qt(()=>r=!1)),t.$set(c)},i(a){n||(Pe(t.$$.fragment,a),n=!0)},o(a){Ce(t.$$.fragment,a),n=!1},d(a){It(t,a)}}}let pc=-1;function gc(){const e=Ke({}),t=new Map,r=new IntersectionObserver(o=>{o.forEach(i=>{if(i.isIntersecting){let a=t.get(i.target);a!==void 0&&e.update(l=>({...l,[a]:!0}))}})});function n(o,i){t.set(i,o),r.observe(i)}return{register:n,subscribe:e.subscribe}}const $r=gc();async function bc(e){if(e){const t=new DOMParser,r=Array.from(t.parseFromString(e,"text/html").head.children);if(r)for(let n of r){let o=document.createElement(n.tagName);if(Array.from(n.attributes).forEach(i=>{o.setAttribute(i.name,i.value)}),o.textContent=n.textContent,o.tagName=="META"&&o.getAttribute("property")){const a=Array.from(document.head.getElementsByTagName("meta")??[]).find(l=>l.getAttribute("property")==o.getAttribute("property")&&!l.isEqualNode(o));if(a){document.head.replaceChild(o,a);continue}}document.head.appendChild(o)}}}function vc(e,t,r){let n,o;Zr(e,wn,S=>r(21,n=S)),Zr(e,$r,S=>r(33,o=S)),Ju();const i=lc();let{autoscroll:a}=t,{version:l}=t,{initial_height:c}=t,{app_mode:s}=t,{is_embed:u}=t,{theme_mode:p="system"}=t,{control_page_title:y}=t,{container:g}=t,{info:d}=t,{eager:w}=t,O,{mount_css:T=Yi}=t,{client:x}=t,{upload_files:b}=t,{worker_proxy:P=void 0}=t;P&&(ec(P),P.addEventListener("progress-update",S=>{r(15,C=S.detail+"...")}));let{fetch_implementation:I=fetch}=t;zt("fetch_implementation",I);let{EventSource_factory:j=S=>new EventSource(S)}=t;zt("EventSource_factory",j);let{space:V}=t,{host:X}=t,{src:z}=t,Z=pc++,se="pending",le,Y=!1,ne=!1,F,C=n("common.loading")+"...",we,ue,ce=null;async function f(S){S&&(ce=sr(S,l,ce||void 0)),await T(F.root+"/theme.css",document.head),F.stylesheets&&await Promise.all(F.stylesheets.map($=>$.startsWith("http:")||$.startsWith("https:")?T($,document.head):fetch(F.root+"/"+$).then(me=>me.text()).then(me=>{sr(me,l)})))}function h(S){const $=window.__gradio_mode__==="website";let K;if($)K="light";else{const Rt=new URL(window.location.toString()).searchParams.get("__theme");K=p||Rt||"system"}return K==="dark"||K==="light"?_(S,K):K=m(S),K}function m(S){const $=K();window?.matchMedia("(prefers-color-scheme: dark)")?.addEventListener("change",K);function K(){let me=window?.matchMedia?.("(prefers-color-scheme: dark)").matches?"dark":"light";return _(S,me),me}return $}function _(S,$){const K=u?S.parentElement:document.body,me=u?S:S.parentElement;me.style.background="var(--body-background-fill)",$==="dark"?K.classList.add("dark"):K.classList.remove("dark")}let E={message:"",load_status:"pending",status:"sleeping",detail:"SLEEPING"},v,k=!1;function D(S){r(13,E=S)}Jr(async()=>{r(16,we=h(le));const S=window.__GRADIO_DEV__,$=window.__GRADIO__SERVER_PORT__;ue=S==="dev"?`http://localhost:${typeof $=="number"?$:7860}`:X||V||z||location.origin,r(17,v=await x(ue,{status_callback:D})),r(12,F=v.config),window.__gradio_space__=F.space_id,r(13,E={message:"",load_status:"complete",status:"running",detail:"RUNNING"}),await f(F.css),await bc(F.head),r(18,k=!0),window.__is_colab__=F.is_colab,i("loaded"),F.dev_mode&&setTimeout(()=>{const{host:K}=new URL(ue);let me=new URL(`http://${K}/dev/reload`);O=new EventSource(me),O.onmessage=async function(Rt){Rt.data==="CHANGE"&&(r(17,v=await x(ue,{status_callback:D})),r(12,F=v.config),window.__gradio_space__=F.space_id,await f(F.css))}},200)}),zt("upload_files",b);let Q,H;async function G(){r(19,Q=(await lr(()=>import("./Blocks-3011bbe9.js").then(S=>S.B),["./Blocks-3011bbe9.js","./index-a80d931b.js","./index-a889f790.css","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./SelectSource-ffeae268.css","./Blocks-68a6bb76.css"],import.meta.url)).default)}async function W(){r(20,H=(await lr(()=>import("./Login-008fa4c1.js"),["./Login-008fa4c1.js","./Index-09f26e4b.js","./Index-3812b7f1.css","./Textbox-1709c01b.js","./SelectSource.svelte_svelte_type_style_lang-0a285437.js","./index-a80d931b.js","./index-a889f790.css","./SelectSource-ffeae268.css","./BlockTitle-7f7c9ef8.js","./Info-84f5385d.js","./Check-965babbe.js","./Copy-b365948f.js","./Textbox-dde6f8cc.css","./Index-ab6a99fa.js","./Index-2853eb31.css","./Login-9c3cc0eb.css","./Example-6ded08d8.css"],import.meta.url)).default)}function q(){F.auth_required?W():G()}const _e={readable_error:{NO_APP_FILE:n("errors.no_app_file"),CONFIG_ERROR:n("errors.config_error"),BUILD_ERROR:n("errors.build_error"),RUNTIME_ERROR:n("errors.runtime_error"),PAUSED:n("errors.space_paused")},title(S){return encodeURIComponent(n("errors.space_not_working"))},description(S,$){return encodeURIComponent(`Hello,

Firstly, thanks for creating this space!

I noticed that the space isn't working correctly because there is ${this.readable_error[S]||"an error"}.

It would be great if you could take a look at this because this space is being embedded on ${$}.

Thanks!`)}};Jr(async()=>{$r.register(Z,le)});function be(S){Y=S,r(10,Y)}function ve(S){ne=S,r(11,ne)}function Se(S){le=S,r(9,le)}return e.$$set=S=>{"autoscroll"in S&&r(0,a=S.autoscroll),"version"in S&&r(1,l=S.version),"initial_height"in S&&r(2,c=S.initial_height),"app_mode"in S&&r(3,s=S.app_mode),"is_embed"in S&&r(4,u=S.is_embed),"theme_mode"in S&&r(23,p=S.theme_mode),"control_page_title"in S&&r(5,y=S.control_page_title),"container"in S&&r(6,g=S.container),"info"in S&&r(7,d=S.info),"eager"in S&&r(24,w=S.eager),"mount_css"in S&&r(25,T=S.mount_css),"client"in S&&r(26,x=S.client),"upload_files"in S&&r(27,b=S.upload_files),"worker_proxy"in S&&r(28,P=S.worker_proxy),"fetch_implementation"in S&&r(29,I=S.fetch_implementation),"EventSource_factory"in S&&r(30,j=S.EventSource_factory),"space"in S&&r(8,V=S.space),"host"in S&&r(31,X=S.host),"src"in S&&r(32,z=S.src)},e.$$.update=()=>{e.$$.dirty[0]&4096&&F?.app_id&&F.app_id,e.$$.dirty[0]&9216&&r(14,se=!Y&&E.load_status!=="error"?"pending":!Y&&E.load_status==="error"?"error":E.load_status),e.$$.dirty[0]&16781312|e.$$.dirty[1]&4&&F&&(w||o[Z])&&q(),e.$$.dirty[0]&2560&&ne&&le.dispatchEvent(new CustomEvent("render",{bubbles:!0,cancelable:!1,composed:!0}))},[a,l,c,s,u,y,g,d,V,le,Y,ne,F,E,se,C,we,v,k,Q,H,n,_e,p,w,T,x,b,P,I,j,X,z,o,be,ve,Se]}class yc extends tc{constructor(t){super(),ac(this,t,vc,mc,sc,{autoscroll:0,version:1,initial_height:2,app_mode:3,is_embed:4,theme_mode:23,control_page_title:5,container:6,info:7,eager:24,mount_css:25,client:26,upload_files:27,worker_proxy:28,fetch_implementation:29,EventSource_factory:30,space:8,host:31,src:32},null,[-1,-1])}}const Oc=Object.freeze(Object.defineProperty({__proto__:null,default:yc},Symbol.toStringTag,{value:"Module"}));export{wn as $,Oc as I,Ol as L,$l as S,ta as a,Ki as b,cr as c,Sc as d,Ju as e,ra as f,Pc as g,kr as h,wc as i,Tc as s,Ke as w};
//# sourceMappingURL=Index-26cfc80a.js.map
