const{SvelteComponent:u,append:c,attr:g,detach:o,element:r,init:d,insert:v,noop:f,safe_not_equal:y,set_data:m,text:b,toggle_class:i}=window.__gradio__svelte__internal;function p(a){let e,n=(a[0]?a[0]:"")+"",s;return{c(){e=r("pre"),s=b(n),g(e,"class","svelte-agpzo2"),i(e,"table",a[1]==="table"),i(e,"gallery",a[1]==="gallery"),i(e,"selected",a[2])},m(t,l){v(t,e,l),c(e,s)},p(t,[l]){l&1&&n!==(n=(t[0]?t[0]:"")+"")&&m(s,n),l&2&&i(e,"table",t[1]==="table"),l&2&&i(e,"gallery",t[1]==="gallery"),l&4&&i(e,"selected",t[2])},i:f,o:f,d(t){t&&o(e)}}}function h(a,e,n){let{value:s}=e,{type:t}=e,{selected:l=!1}=e;return a.$$set=_=>{"value"in _&&n(0,s=_.value),"type"in _&&n(1,t=_.type),"selected"in _&&n(2,l=_.selected)},[s,t,l]}class w extends u{constructor(e){super(),d(this,e,h,p,y,{value:0,type:1,selected:2})}}export{w as default};
//# sourceMappingURL=Example-0b8f33de.js.map
