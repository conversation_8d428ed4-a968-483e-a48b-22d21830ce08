const{SvelteComponent:u,append:c,attr:d,detach:v,element:g,init:o,insert:r,noop:f,safe_not_equal:y,set_data:m,text:b,toggle_class:_}=window.__gradio__svelte__internal;function h(a){let e,n=(a[0]?a[0]:"")+"",s;return{c(){e=g("div"),s=b(n),d(e,"class","svelte-1ayixqk"),_(e,"table",a[1]==="table"),_(e,"gallery",a[1]==="gallery"),_(e,"selected",a[2])},m(t,l){r(t,e,l),c(e,s)},p(t,[l]){l&1&&n!==(n=(t[0]?t[0]:"")+"")&&m(s,n),l&2&&_(e,"table",t[1]==="table"),l&2&&_(e,"gallery",t[1]==="gallery"),l&4&&_(e,"selected",t[2])},i:f,o:f,d(t){t&&v(e)}}}function q(a,e,n){let{value:s}=e,{type:t}=e,{selected:l=!1}=e;return a.$$set=i=>{"value"in i&&n(0,s=i.value),"type"in i&&n(1,t=i.type),"selected"in i&&n(2,l=i.selected)},[s,t,l]}class w extends u{constructor(e){super(),o(this,e,q,h,y,{value:0,type:1,selected:2})}}export{w as default};
//# sourceMappingURL=Example-4f6d17e3.js.map
