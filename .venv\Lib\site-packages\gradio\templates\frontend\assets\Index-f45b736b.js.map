{"version": 3, "file": "Index-f45b736b.js", "sources": ["../../../../js/markdown/Index.svelte"], "sourcesContent": ["<script context=\"module\" lang=\"ts\">\n\texport { default as MarkdownCode } from \"./shared/MarkdownCode.svelte\";\n\texport { default as BaseMarkdown } from \"./shared/Markdown.svelte\";\n\texport { default as BaseExample } from \"./Example.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport Markdown from \"./shared/Markdown.svelte\";\n\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { Block } from \"@gradio/atoms\";\n\n\texport let label: string;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value = \"\";\n\texport let loading_status: LoadingStatus;\n\texport let rtl = false;\n\texport let sanitize_html = true;\n\texport let line_breaks = false;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t}>;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let header_links = false;\n\n\t$: label, gradio.dispatch(\"change\");\n</script>\n\n<Block\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\tcontainer={false}\n\tallow_overflow={true}\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\tvariant=\"center\"\n\t/>\n\t<div class:pending={loading_status?.status === \"pending\"}>\n\t\t<Markdown\n\t\t\tmin_height={loading_status && loading_status.status !== \"complete\"}\n\t\t\t{value}\n\t\t\t{elem_classes}\n\t\t\t{visible}\n\t\t\t{rtl}\n\t\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\t\t{latex_delimiters}\n\t\t\t{sanitize_html}\n\t\t\t{line_breaks}\n\t\t\t{header_links}\n\t\t/>\n\t</div>\n</Block>\n\n<style>\n\tdiv {\n\t\ttransition: 150ms;\n\t}\n\n\t.pending {\n\t\topacity: 0.2;\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "div", "anchor", "dirty", "markdown_changes", "label", "$$props", "elem_id", "elem_classes", "visible", "value", "loading_status", "rtl", "sanitize_html", "line_breaks", "gradio", "latex_delimiters", "header_links"], "mappings": "suBA4Cc,CAAA,WAAAA,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,CAAA,iHAKL,WAAAA,EAAkB,CAAA,GAAAA,EAAe,CAAA,EAAA,SAAW,wQAFtCA,EAAc,CAAA,GAAE,SAAW,SAAS,4BAAxDC,EAaKC,EAAAC,EAAAC,CAAA,8CAlBQC,EAAA,KAAA,CAAA,WAAAL,KAAO,UAAU,EACvBK,EAAA,KAAA,CAAA,KAAAL,KAAO,IAAI,UACbA,EAAc,CAAA,CAAA,iCAKLK,EAAA,KAAAC,EAAA,WAAAN,EAAkB,CAAA,GAAAA,EAAe,CAAA,EAAA,SAAW,iQAFtCA,EAAc,CAAA,GAAE,SAAW,SAAS,+OAT7C,kBACK,yUA3BL,MAAAO,CAAa,EAAAC,EACb,CAAA,QAAAC,EAAU,EAAE,EAAAD,GACZ,aAAAE,EAAY,EAAA,EAAAF,EACZ,CAAA,QAAAG,EAAU,EAAI,EAAAH,EACd,CAAA,MAAAI,EAAQ,EAAE,EAAAJ,GACV,eAAAK,CAA6B,EAAAL,EAC7B,CAAA,IAAAM,EAAM,EAAK,EAAAN,EACX,CAAA,cAAAO,EAAgB,EAAI,EAAAP,EACpB,CAAA,YAAAQ,EAAc,EAAK,EAAAR,GACnB,OAAAS,CAET,EAAAT,GACS,iBAAAU,CAIR,EAAAV,EACQ,CAAA,aAAAW,EAAe,EAAK,EAAAX,cAyBZS,EAAO,SAAS,QAAQ,gfAvBjCA,EAAO,SAAS,QAAQ"}