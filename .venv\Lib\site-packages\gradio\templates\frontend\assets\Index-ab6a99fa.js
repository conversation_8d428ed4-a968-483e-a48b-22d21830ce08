const{SvelteComponent:w,attr:c,create_slot:b,detach:j,element:r,get_all_dirty_from_scope:I,get_slot_changes:q,init:C,insert:S,null_to_empty:h,safe_not_equal:k,set_style:o,toggle_class:f,transition_in:z,transition_out:A,update_slot_base:B}=window.__gradio__svelte__internal;function D(s){let e,_,m=`calc(min(${s[2]}px, 100%))`,t;const u=s[8].default,a=b(u,s,s[7],null);return{c(){e=r("div"),a&&a.c(),c(e,"id",s[3]),c(e,"class",_=h(s[4].join(" "))+" svelte-vt1mxs"),f(e,"gap",s[1]),f(e,"compact",s[6]==="compact"),f(e,"panel",s[6]==="panel"),f(e,"hide",!s[5]),o(e,"flex-grow",s[0]),o(e,"min-width",m)},m(l,n){S(l,e,n),a&&a.m(e,null),t=!0},p(l,[n]){a&&a.p&&(!t||n&128)&&B(a,u,l,l[7],t?q(u,l[7],n,null):I(l[7]),null),(!t||n&8)&&c(e,"id",l[3]),(!t||n&16&&_!==(_=h(l[4].join(" "))+" svelte-vt1mxs"))&&c(e,"class",_),(!t||n&18)&&f(e,"gap",l[1]),(!t||n&80)&&f(e,"compact",l[6]==="compact"),(!t||n&80)&&f(e,"panel",l[6]==="panel"),(!t||n&48)&&f(e,"hide",!l[5]),n&1&&o(e,"flex-grow",l[0]),n&4&&m!==(m=`calc(min(${l[2]}px, 100%))`)&&o(e,"min-width",m)},i(l){t||(z(a,l),t=!0)},o(l){A(a,l),t=!1},d(l){l&&j(e),a&&a.d(l)}}}function E(s,e,_){let{$$slots:m={},$$scope:t}=e,{scale:u=null}=e,{gap:a=!0}=e,{min_width:l=0}=e,{elem_id:n=""}=e,{elem_classes:d=[]}=e,{visible:g=!0}=e,{variant:v="default"}=e;return s.$$set=i=>{"scale"in i&&_(0,u=i.scale),"gap"in i&&_(1,a=i.gap),"min_width"in i&&_(2,l=i.min_width),"elem_id"in i&&_(3,n=i.elem_id),"elem_classes"in i&&_(4,d=i.elem_classes),"visible"in i&&_(5,g=i.visible),"variant"in i&&_(6,v=i.variant),"$$scope"in i&&_(7,t=i.$$scope)},[u,a,l,n,d,g,v,t,m]}class F extends w{constructor(e){super(),C(this,e,E,D,k,{scale:0,gap:1,min_width:2,elem_id:3,elem_classes:4,visible:5,variant:6})}}export{F as default};
//# sourceMappingURL=Index-ab6a99fa.js.map
