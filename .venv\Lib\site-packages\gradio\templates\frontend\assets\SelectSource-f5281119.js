import"./Index-26cfc80a.js";import{U as K,I as L}from"./Upload-351cc897.js";import"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";const{SvelteComponent:O,append:U,attr:_,detach:Q,init:T,insert:X,noop:j,safe_not_equal:Y,svg_element:S}=window.__gradio__svelte__internal;function Z(c){let e,l,t,i,o;return{c(){e=S("svg"),l=S("path"),t=S("path"),i=S("line"),o=S("line"),_(l,"d","M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"),_(t,"d","M19 10v2a7 7 0 0 1-14 0v-2"),_(i,"x1","12"),_(i,"y1","19"),_(i,"x2","12"),_(i,"y2","23"),_(o,"x1","8"),_(o,"y1","23"),_(o,"x2","16"),_(o,"y2","23"),_(e,"xmlns","http://www.w3.org/2000/svg"),_(e,"width","100%"),_(e,"height","100%"),_(e,"viewBox","0 0 24 24"),_(e,"fill","none"),_(e,"stroke","currentColor"),_(e,"stroke-width","2"),_(e,"stroke-linecap","round"),_(e,"stroke-linejoin","round"),_(e,"class","feather feather-mic")},m(n,a){X(n,e,a),U(e,l),U(e,t),U(e,i),U(e,o)},p:j,i:j,o:j,d(n){n&&Q(e)}}}class y extends O{constructor(e){super(),T(this,e,null,Z,Y,{})}}const{SvelteComponent:x,append:D,attr:g,detach:ee,init:te,insert:le,noop:A,safe_not_equal:ne,svg_element:N}=window.__gradio__svelte__internal;function ie(c){let e,l,t;return{c(){e=N("svg"),l=N("path"),t=N("path"),g(l,"fill","currentColor"),g(l,"d","M12 2c-4.963 0-9 4.038-9 9c0 3.328 1.82 6.232 4.513 7.79l-2.067 1.378A1 1 0 0 0 6 22h12a1 1 0 0 0 .555-1.832l-2.067-1.378C19.18 17.232 21 14.328 21 11c0-4.962-4.037-9-9-9zm0 16c-3.859 0-7-3.141-7-7c0-3.86 3.141-7 7-7s7 3.14 7 7c0 3.859-3.141 7-7 7z"),g(t,"fill","currentColor"),g(t,"d","M12 6c-2.757 0-5 2.243-5 5s2.243 5 5 5s5-2.243 5-5s-2.243-5-5-5zm0 8c-1.654 0-3-1.346-3-3s1.346-3 3-3s3 1.346 3 3s-1.346 3-3 3z"),g(e,"xmlns","http://www.w3.org/2000/svg"),g(e,"width","100%"),g(e,"height","100%"),g(e,"viewBox","0 0 24 24")},m(i,o){le(i,e,o),D(e,l),D(e,t)},p:A,i:A,o:A,d(i){i&&ee(e)}}}class oe extends x{constructor(e){super(),te(this,e,null,ie,ne,{})}}const{SvelteComponent:ce,append:R,attr:b,check_outros:q,create_component:B,destroy_component:I,detach:$,element:M,empty:re,group_outros:z,init:ae,insert:C,listen:P,mount_component:W,safe_not_equal:se,space:V,toggle_class:v,transition_in:d,transition_out:h}=window.__gradio__svelte__internal;function E(c){let e,l=c[1].includes("upload"),t,i=c[1].includes("microphone"),o,n=c[1].includes("webcam"),a,w=c[1].includes("clipboard"),k,s=l&&F(c),u=i&&G(c),f=n&&H(c),r=w&&J(c);return{c(){e=M("span"),s&&s.c(),t=V(),u&&u.c(),o=V(),f&&f.c(),a=V(),r&&r.c(),b(e,"class","source-selection svelte-1ebruwp"),b(e,"data-testid","source-select")},m(m,p){C(m,e,p),s&&s.m(e,null),R(e,t),u&&u.m(e,null),R(e,o),f&&f.m(e,null),R(e,a),r&&r.m(e,null),k=!0},p(m,p){p&2&&(l=m[1].includes("upload")),l?s?(s.p(m,p),p&2&&d(s,1)):(s=F(m),s.c(),d(s,1),s.m(e,t)):s&&(z(),h(s,1,1,()=>{s=null}),q()),p&2&&(i=m[1].includes("microphone")),i?u?(u.p(m,p),p&2&&d(u,1)):(u=G(m),u.c(),d(u,1),u.m(e,o)):u&&(z(),h(u,1,1,()=>{u=null}),q()),p&2&&(n=m[1].includes("webcam")),n?f?(f.p(m,p),p&2&&d(f,1)):(f=H(m),f.c(),d(f,1),f.m(e,a)):f&&(z(),h(f,1,1,()=>{f=null}),q()),p&2&&(w=m[1].includes("clipboard")),w?r?(r.p(m,p),p&2&&d(r,1)):(r=J(m),r.c(),d(r,1),r.m(e,null)):r&&(z(),h(r,1,1,()=>{r=null}),q())},i(m){k||(d(s),d(u),d(f),d(r),k=!0)},o(m){h(s),h(u),h(f),h(r),k=!1},d(m){m&&$(e),s&&s.d(),u&&u.d(),f&&f.d(),r&&r.d()}}}function F(c){let e,l,t,i,o;return l=new K({}),{c(){e=M("button"),B(l.$$.fragment),b(e,"class","icon svelte-1ebruwp"),b(e,"aria-label","Upload file"),v(e,"selected",c[0]==="upload"||!c[0])},m(n,a){C(n,e,a),W(l,e,null),t=!0,i||(o=P(e,"click",c[6]),i=!0)},p(n,a){(!t||a&1)&&v(e,"selected",n[0]==="upload"||!n[0])},i(n){t||(d(l.$$.fragment,n),t=!0)},o(n){h(l.$$.fragment,n),t=!1},d(n){n&&$(e),I(l),i=!1,o()}}}function G(c){let e,l,t,i,o;return l=new y({}),{c(){e=M("button"),B(l.$$.fragment),b(e,"class","icon svelte-1ebruwp"),b(e,"aria-label","Record audio"),v(e,"selected",c[0]==="microphone")},m(n,a){C(n,e,a),W(l,e,null),t=!0,i||(o=P(e,"click",c[7]),i=!0)},p(n,a){(!t||a&1)&&v(e,"selected",n[0]==="microphone")},i(n){t||(d(l.$$.fragment,n),t=!0)},o(n){h(l.$$.fragment,n),t=!1},d(n){n&&$(e),I(l),i=!1,o()}}}function H(c){let e,l,t,i,o;return l=new oe({}),{c(){e=M("button"),B(l.$$.fragment),b(e,"class","icon svelte-1ebruwp"),b(e,"aria-label","Capture from camera"),v(e,"selected",c[0]==="webcam")},m(n,a){C(n,e,a),W(l,e,null),t=!0,i||(o=P(e,"click",c[8]),i=!0)},p(n,a){(!t||a&1)&&v(e,"selected",n[0]==="webcam")},i(n){t||(d(l.$$.fragment,n),t=!0)},o(n){h(l.$$.fragment,n),t=!1},d(n){n&&$(e),I(l),i=!1,o()}}}function J(c){let e,l,t,i,o;return l=new L({}),{c(){e=M("button"),B(l.$$.fragment),b(e,"class","icon svelte-1ebruwp"),b(e,"aria-label","Paste from clipboard"),v(e,"selected",c[0]==="clipboard")},m(n,a){C(n,e,a),W(l,e,null),t=!0,i||(o=P(e,"click",c[9]),i=!0)},p(n,a){(!t||a&1)&&v(e,"selected",n[0]==="clipboard")},i(n){t||(d(l.$$.fragment,n),t=!0)},o(n){h(l.$$.fragment,n),t=!1},d(n){n&&$(e),I(l),i=!1,o()}}}function ue(c){let e,l,t=c[2].length>1&&E(c);return{c(){t&&t.c(),e=re()},m(i,o){t&&t.m(i,o),C(i,e,o),l=!0},p(i,[o]){i[2].length>1?t?(t.p(i,o),o&4&&d(t,1)):(t=E(i),t.c(),d(t,1),t.m(e.parentNode,e)):t&&(z(),h(t,1,1,()=>{t=null}),q())},i(i){l||(d(t),l=!0)},o(i){h(t),l=!1},d(i){i&&$(e),t&&t.d(i)}}}function fe(c,e,l){let t,{sources:i}=e,{active_source:o}=e,{handle_clear:n=()=>{}}=e,{handle_select:a=()=>{}}=e;async function w(r){n(),l(0,o=r),a(r)}const k=()=>w("upload"),s=()=>w("microphone"),u=()=>w("webcam"),f=()=>w("clipboard");return c.$$set=r=>{"sources"in r&&l(1,i=r.sources),"active_source"in r&&l(0,o=r.active_source),"handle_clear"in r&&l(4,n=r.handle_clear),"handle_select"in r&&l(5,a=r.handle_select)},c.$$.update=()=>{c.$$.dirty&2&&l(2,t=[...new Set(i)])},[o,i,t,w,n,a,k,s,u,f]}class pe extends ce{constructor(e){super(),ae(this,e,fe,ue,se,{sources:1,active_source:0,handle_clear:4,handle_select:5})}}export{pe as S,oe as W};
//# sourceMappingURL=SelectSource-f5281119.js.map
