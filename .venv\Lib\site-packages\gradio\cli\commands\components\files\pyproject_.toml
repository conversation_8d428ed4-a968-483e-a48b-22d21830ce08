[build-system]
requires = [
  "hatchling",
  "hatch-requirements-txt",
  "hatch-fancy-pypi-readme>=22.5.0",
]
build-backend = "hatchling.build"

[project]
name = "<<name>>"
version = "0.0.1"
description = "Python library for easily interacting with trained machine learning models"
readme = "README.md"
license = "Apache-2.0"
requires-python = ">=3.8"
authors = [{ name = "YOUR NAME", email = "<EMAIL>" }]
keywords = [
  "gradio-custom-component",
  "<<template>>"
]
# Add dependencies here
dependencies = ["gradio>=4.0,<5.0"]
classifiers = [
  'Development Status :: 3 - Alpha',
  'License :: OSI Approved :: Apache Software License',
  'Operating System :: OS Independent',
  'Programming Language :: Python :: 3',
  'Programming Language :: Python :: 3 :: Only',
  'Programming Language :: Python :: 3.8',
  'Programming Language :: Python :: 3.9',
  'Programming Language :: Python :: 3.10',
  'Programming Language :: Python :: 3.11',
  'Topic :: Scientific/Engineering',
  'Topic :: Scientific/Engineering :: Artificial Intelligence',
  'Topic :: Scientific/Engineering :: Visualization',
]

[project.optional-dependencies]
dev = ["build", "twine"]

[tool.hatch.build]
artifacts = ["/backend/<<name>>/templates", "*.pyi"]

[tool.hatch.build.targets.wheel]
packages = ["/backend/<<name>>"]
