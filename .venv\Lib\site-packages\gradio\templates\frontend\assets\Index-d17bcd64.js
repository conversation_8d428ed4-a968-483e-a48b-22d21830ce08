import{B as re}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{B as fe}from"./BlockLabel-f27805b1.js";import{E as me}from"./Empty-28f63bf0.js";import{S as ce}from"./Index-26cfc80a.js";import{I as x}from"./Image-eaba773f.js";import"./index-a80d931b.js";import{r as V}from"./file-url-bef2dc1b.js";import"./svelte/svelte.js";const{SvelteComponent:ge,append:D,assign:he,attr:b,check_outros:be,create_component:P,destroy_component:j,destroy_each:ee,detach:d,element:z,empty:de,ensure_array_like:F,get_spread_object:ve,get_spread_update:ke,group_outros:we,init:Ie,insert:v,listen:E,mount_component:C,noop:W,run_all:Be,safe_not_equal:Me,set_data:Se,set_style:X,space:L,src_url_equal:G,text:qe,toggle_class:q,transition_in:w,transition_out:I}=window.__gradio__svelte__internal;function Y(n,e,t){const l=n.slice();return l[27]=e[t],l[29]=t,l}function Z(n,e,t){const l=n.slice();return l[27]=e[t],l[29]=t,l}function ze(n){let e,t,l,i,o,a,f=F(n[14]?n[14]?.annotations:[]),m=[];for(let u=0;u<f.length;u+=1)m[u]=y(Z(n,f,u));let c=n[6]&&n[14]&&p(n);return{c(){e=z("div"),t=z("img"),i=L();for(let u=0;u<m.length;u+=1)m[u].c();o=L(),c&&c.c(),a=de(),b(t,"class","base-image svelte-m3v3vb"),G(t.src,l=n[14]?n[14].image.url:null)||b(t,"src",l),b(t,"alt","the base file that is annotated"),q(t,"fit-height",n[7]),b(e,"class","image-container svelte-m3v3vb")},m(u,g){v(u,e,g),D(e,t),D(e,i);for(let r=0;r<m.length;r+=1)m[r]&&m[r].m(e,null);v(u,o,g),c&&c.m(u,g),v(u,a,g)},p(u,g){if(g[0]&16384&&!G(t.src,l=u[14]?u[14].image.url:null)&&b(t,"src",l),g[0]&128&&q(t,"fit-height",u[7]),g[0]&49680){f=F(u[14]?u[14]?.annotations:[]);let r;for(r=0;r<f.length;r+=1){const k=Z(u,f,r);m[r]?m[r].p(k,g):(m[r]=y(k),m[r].c(),m[r].m(e,null))}for(;r<m.length;r+=1)m[r].d(1);m.length=f.length}u[6]&&u[14]?c?c.p(u,g):(c=p(u),c.c(),c.m(a.parentNode,a)):c&&(c.d(1),c=null)},i:W,o:W,d(u){u&&(d(e),d(o),d(a)),ee(m,u),c&&c.d(u)}}}function Ee(n){let e,t;return e=new me({props:{size:"large",unpadded_box:!0,$$slots:{default:[Pe]},$$scope:{ctx:n}}}),{c(){P(e.$$.fragment)},m(l,i){C(e,l,i),t=!0},p(l,i){const o={};i[1]&1&&(o.$$scope={dirty:i,ctx:l}),e.$set(o)},i(l){t||(w(e.$$.fragment,l),t=!0)},o(l){I(e.$$.fragment,l),t=!1},d(l){j(e,l)}}}function y(n){let e,t,l,i;return{c(){e=z("img"),b(e,"alt",t="segmentation mask identifying "+n[4]+" within the uploaded file"),b(e,"class","mask fit-height svelte-m3v3vb"),G(e.src,l=n[27].image.url)||b(e,"src",l),b(e,"style",i=n[9]&&n[27].label in n[9]?null:`filter: hue-rotate(${Math.round(n[29]*360/n[14]?.annotations.length)}deg);`),q(e,"active",n[15]==n[27].label),q(e,"inactive",n[15]!=n[27].label&&n[15]!=null)},m(o,a){v(o,e,a)},p(o,a){a[0]&16&&t!==(t="segmentation mask identifying "+o[4]+" within the uploaded file")&&b(e,"alt",t),a[0]&16384&&!G(e.src,l=o[27].image.url)&&b(e,"src",l),a[0]&16896&&i!==(i=o[9]&&o[27].label in o[9]?null:`filter: hue-rotate(${Math.round(o[29]*360/o[14]?.annotations.length)}deg);`)&&b(e,"style",i),a[0]&49152&&q(e,"active",o[15]==o[27].label),a[0]&49152&&q(e,"inactive",o[15]!=o[27].label&&o[15]!=null)},d(o){o&&d(e)}}}function p(n){let e,t=F(n[14].annotations),l=[];for(let i=0;i<t.length;i+=1)l[i]=$(Y(n,t,i));return{c(){e=z("div");for(let i=0;i<l.length;i+=1)l[i].c();b(e,"class","legend svelte-m3v3vb")},m(i,o){v(i,e,o);for(let a=0;a<l.length;a+=1)l[a]&&l[a].m(e,null)},p(i,o){if(o[0]&475648){t=F(i[14].annotations);let a;for(a=0;a<t.length;a+=1){const f=Y(i,t,a);l[a]?l[a].p(f,o):(l[a]=$(f),l[a].c(),l[a].m(e,null))}for(;a<l.length;a+=1)l[a].d(1);l.length=t.length}},d(i){i&&d(e),ee(l,i)}}}function $(n){let e,t=n[27].label+"",l,i,o,a;function f(){return n[22](n[27])}function m(){return n[23](n[27])}function c(){return n[26](n[29],n[27])}return{c(){e=z("button"),l=qe(t),i=L(),b(e,"class","legend-item svelte-m3v3vb"),X(e,"background-color",n[9]&&n[27].label in n[9]?n[9][n[27].label]+"88":`hsla(${Math.round(n[29]*360/n[14].annotations.length)}, 100%, 50%, 0.3)`)},m(u,g){v(u,e,g),D(e,l),D(e,i),o||(a=[E(e,"mouseover",f),E(e,"focus",m),E(e,"mouseout",n[24]),E(e,"blur",n[25]),E(e,"click",c)],o=!0)},p(u,g){n=u,g[0]&16384&&t!==(t=n[27].label+"")&&Se(l,t),g[0]&16896&&X(e,"background-color",n[9]&&n[27].label in n[9]?n[9][n[27].label]+"88":`hsla(${Math.round(n[29]*360/n[14].annotations.length)}, 100%, 50%, 0.3)`)},d(u){u&&d(e),o=!1,Be(a)}}}function Pe(n){let e,t;return e=new x({}),{c(){P(e.$$.fragment)},m(l,i){C(e,l,i),t=!0},i(l){t||(w(e.$$.fragment,l),t=!0)},o(l){I(e.$$.fragment,l),t=!1},d(l){j(e,l)}}}function je(n){let e,t,l,i,o,a,f,m;const c=[{autoscroll:n[3].autoscroll},{i18n:n[3].i18n},n[13]];let u={};for(let _=0;_<c.length;_+=1)u=he(u,c[_]);e=new ce({props:u}),l=new fe({props:{show_label:n[5],Icon:x,label:n[4]||n[3].i18n("image.image")}});const g=[Ee,ze],r=[];function k(_,h){return _[14]==null?0:1}return a=k(n),f=r[a]=g[a](n),{c(){P(e.$$.fragment),t=L(),P(l.$$.fragment),i=L(),o=z("div"),f.c(),b(o,"class","container svelte-m3v3vb")},m(_,h){C(e,_,h),v(_,t,h),C(l,_,h),v(_,i,h),v(_,o,h),r[a].m(o,null),m=!0},p(_,h){const N=h[0]&8200?ke(c,[h[0]&8&&{autoscroll:_[3].autoscroll},h[0]&8&&{i18n:_[3].i18n},h[0]&8192&&ve(_[13])]):{};e.$set(N);const B={};h[0]&32&&(B.show_label=_[5]),h[0]&24&&(B.label=_[4]||_[3].i18n("image.image")),l.$set(B);let M=a;a=k(_),a===M?r[a].p(_,h):(we(),I(r[M],1,1,()=>{r[M]=null}),be(),f=r[a],f?f.p(_,h):(f=r[a]=g[a](_),f.c()),w(f,1),f.m(o,null))},i(_){m||(w(e.$$.fragment,_),w(l.$$.fragment,_),w(f),m=!0)},o(_){I(e.$$.fragment,_),I(l.$$.fragment,_),I(f),m=!1},d(_){_&&(d(t),d(i),d(o)),j(e,_),j(l,_),r[a].d()}}}function Ce(n){let e,t;return e=new re({props:{visible:n[2],elem_id:n[0],elem_classes:n[1],padding:!1,height:n[7],width:n[8],allow_overflow:!1,container:n[10],scale:n[11],min_width:n[12],$$slots:{default:[je]},$$scope:{ctx:n}}}),{c(){P(e.$$.fragment)},m(l,i){C(e,l,i),t=!0},p(l,i){const o={};i[0]&4&&(o.visible=l[2]),i[0]&1&&(o.elem_id=l[0]),i[0]&2&&(o.elem_classes=l[1]),i[0]&128&&(o.height=l[7]),i[0]&256&&(o.width=l[8]),i[0]&1024&&(o.container=l[10]),i[0]&2048&&(o.scale=l[11]),i[0]&4096&&(o.min_width=l[12]),i[0]&58104|i[1]&1&&(o.$$scope={dirty:i,ctx:l}),e.$set(o)},i(l){t||(w(e.$$.fragment,l),t=!0)},o(l){I(e.$$.fragment,l),t=!1},d(l){j(e,l)}}}function Le(n,e,t){let{elem_id:l=""}=e,{elem_classes:i=[]}=e,{visible:o=!0}=e,{value:a=null}=e,f=null,m=null,{gradio:c}=e,{label:u=c.i18n("annotated_image.annotated_image")}=e,{show_label:g=!0}=e,{show_legend:r=!0}=e,{height:k}=e,{width:_}=e,{color_map:h}=e,{container:N=!0}=e,{scale:B=null}=e,{min_width:M=void 0}=e,H=null,{loading_status:R}=e,J=null;function K(s){t(15,H=s)}function O(){t(15,H=null)}function T(s,A){c.dispatch("select",{value:u,index:s})}const le=s=>K(s.label),ne=s=>K(s.label),te=()=>O(),ie=()=>O(),ae=(s,A)=>T(s,A.label);return n.$$set=s=>{"elem_id"in s&&t(0,l=s.elem_id),"elem_classes"in s&&t(1,i=s.elem_classes),"visible"in s&&t(2,o=s.visible),"value"in s&&t(19,a=s.value),"gradio"in s&&t(3,c=s.gradio),"label"in s&&t(4,u=s.label),"show_label"in s&&t(5,g=s.show_label),"show_legend"in s&&t(6,r=s.show_legend),"height"in s&&t(7,k=s.height),"width"in s&&t(8,_=s.width),"color_map"in s&&t(9,h=s.color_map),"container"in s&&t(10,N=s.container),"scale"in s&&t(11,B=s.scale),"min_width"in s&&t(12,M=s.min_width),"loading_status"in s&&t(13,R=s.loading_status)},n.$$.update=()=>{if(n.$$.dirty[0]&3670024)if(a!==f&&(t(20,f=a),c.dispatch("change")),a){const s={image:a.image,annotations:a.annotations.map(S=>({image:S.image,label:S.label}))};t(14,m=s);const A=V(s.image.url),oe=Promise.all(s.annotations.map(S=>V(S.image.url))),Q=Promise.all([A,oe]);t(21,J=Q),Q.then(([S,se])=>{if(J!==Q)return;const ue={image:{...s.image,url:S??void 0},annotations:s.annotations.map((U,_e)=>({...U,image:{...U.image,url:se[_e]??void 0}}))};t(14,m=ue)})}else t(14,m=null)},[l,i,o,c,u,g,r,k,_,h,N,B,M,R,m,H,K,O,T,a,f,J,le,ne,te,ie,ae]}class Oe extends ge{constructor(e){super(),Ie(this,e,Le,Ce,Me,{elem_id:0,elem_classes:1,visible:2,value:19,gradio:3,label:4,show_label:5,show_legend:6,height:7,width:8,color_map:9,container:10,scale:11,min_width:12,loading_status:13},null,[-1,-1])}}export{Oe as default};
//# sourceMappingURL=Index-d17bcd64.js.map
