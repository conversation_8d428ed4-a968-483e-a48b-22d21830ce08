import{P as I}from"./prism-python-b0b31d02.js";import"./Index-26cfc80a.js";import"./index-a80d931b.js";import"./svelte/svelte.js";(function(a){a.languages.typescript=a.languages.extend("javascript",{"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|type)\s+)(?!keyof\b)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?:\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,lookbehind:!0,greedy:!0,inside:null},builtin:/\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\b/}),a.languages.typescript.keyword.push(/\b(?:abstract|declare|is|keyof|readonly|require)\b/,/\b(?:asserts|infer|interface|module|namespace|type)\b(?=\s*(?:[{_$a-zA-Z\xA0-\uFFFF]|$))/,/\btype\b(?=\s*(?:[\{*]|$))/),delete a.languages.typescript.parameter,delete a.languages.typescript["literal-property"];var e=a.languages.extend("typescript",{});delete e["class-name"],a.languages.typescript["class-name"].inside=e,a.languages.insertBefore("typescript","function",{decorator:{pattern:/@[$\w\xA0-\uFFFF]+/,inside:{at:{pattern:/^@/,alias:"operator"},function:/^[\s\S]+/}},"generic-function":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\s*\()/,greedy:!0,inside:{function:/^#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*/,generic:{pattern:/<[\s\S]+/,alias:"class-name",inside:e}}}}),a.languages.ts=a.languages.typescript})(Prism);const{HtmlTag:D,SvelteComponent:J,append:_,attr:m,binding_callbacks:K,destroy_block:Q,detach:y,element:h,empty:j,ensure_array_like:L,init:U,insert:b,listen:W,noop:T,safe_not_equal:X,set_data:G,set_style:Y,space:C,text:Z,toggle_class:F,update_keyed_each:P}=window.__gradio__svelte__internal;function V(a,e,n){const t=a.slice();return t[10]=e[n].type,t[11]=e[n].description,t[12]=e[n].default,t[13]=e[n].name,t[14]=e,t[15]=n,t}function B(a){let e=[],n=new Map,t,l=L(a[1]);const i=s=>s[13];for(let s=0;s<l.length;s+=1){let o=V(a,l,s),f=i(o);n.set(f,e[s]=R(f,o))}return{c(){for(let s=0;s<e.length;s+=1)e[s].c();t=j()},m(s,o){for(let f=0;f<e.length;f+=1)e[f]&&e[f].m(s,o);b(s,t,o)},p(s,o){o&15&&(l=L(s[1]),e=P(e,o,i,1,s,l,n,t.parentNode,Q,R,t,V))},d(s){s&&y(t);for(let o=0;o<e.length;o+=1)e[o].d(s)}}}function E(a){let e,n,t=a[10]+"",l;return{c(){e=Z(": "),n=new D(!1),l=j(),n.a=l},m(i,s){b(i,e,s),n.m(t,i,s),b(i,l,s)},p(i,s){s&2&&t!==(t=i[10]+"")&&n.p(t)},d(i){i&&(y(e),y(l),n.d())}}}function H(a){let e,n,t=a[12]&&M(a),l=a[11]&&O(a);return{c(){t&&t.c(),e=C(),l&&l.c(),n=j()},m(i,s){t&&t.m(i,s),b(i,e,s),l&&l.m(i,s),b(i,n,s)},p(i,s){i[12]?t?t.p(i,s):(t=M(i),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null),i[11]?l?l.p(i,s):(l=O(i),l.c(),l.m(n.parentNode,n)):l&&(l.d(1),l=null)},d(i){i&&(y(e),y(n)),t&&t.d(i),l&&l.d(i)}}}function M(a){let e,n,t,l,i,s,o=a[12]+"";return{c(){e=h("div"),n=h("span"),n.textContent="default",t=C(),l=h("code"),i=Z("= "),s=new D(!1),m(n,"class","svelte-1n1otz8"),Y(n,"padding-right","4px"),s.a=null,m(l,"class","svelte-1n1otz8"),m(e,"class","default svelte-1n1otz8"),F(e,"last",!a[11])},m(f,d){b(f,e,d),_(e,n),_(e,t),_(e,l),_(l,i),s.m(o,l)},p(f,d){d&2&&o!==(o=f[12]+"")&&s.p(o),d&2&&F(e,"last",!f[11])},d(f){f&&y(e)}}}function O(a){let e,n,t=a[11]+"",l;return{c(){e=h("div"),n=h("p"),l=Z(t),m(e,"class","description svelte-1n1otz8")},m(i,s){b(i,e,s),_(e,n),_(n,l)},p(i,s){s&2&&t!==(t=i[11]+"")&&G(l,t)},d(i){i&&y(e)}}}function R(a,e){let n,t,l,i,s=e[13]+"",o,f=e[15],d,w,p,S,r,g,v,u=e[10]&&E(e);const q=()=>e[6](i,f),z=()=>e[6](null,f);function N(){return e[7](e[15])}let c=e[3][e[15]]&&H(e);return{key:a,first:null,c(){n=h("div"),t=h("div"),l=h("pre"),i=h("code"),o=Z(s),u&&u.c(),w=C(),p=h("button"),p.textContent="▲",S=C(),c&&c.c(),r=C(),m(i,"class","svelte-1n1otz8"),m(l,"class",d="language-"+e[0]+" svelte-1n1otz8"),m(p,"class","arrow svelte-1n1otz8"),F(p,"disabled",!e[11]&&!e[12]),F(p,"hidden",!e[3][e[15]]),m(t,"class","type svelte-1n1otz8"),m(n,"class","param md svelte-1n1otz8"),F(n,"open",e[3][e[15]]),this.first=n},m(A,k){b(A,n,k),_(n,t),_(t,l),_(l,i),_(i,o),u&&u.m(i,null),q(),_(t,w),_(t,p),_(n,S),c&&c.m(n,null),_(n,r),g||(v=W(p,"click",N),g=!0)},p(A,k){e=A,k&2&&s!==(s=e[13]+"")&&G(o,s),e[10]?u?u.p(e,k):(u=E(e),u.c(),u.m(i,null)):u&&(u.d(1),u=null),f!==e[15]&&(z(),f=e[15],q()),k&1&&d!==(d="language-"+e[0]+" svelte-1n1otz8")&&m(l,"class",d),k&2&&F(p,"disabled",!e[11]&&!e[12]),k&10&&F(p,"hidden",!e[3][e[15]]),e[3][e[15]]?c?c.p(e,k):(c=H(e),c.c(),c.m(n,r)):c&&(c.d(1),c=null),k&10&&F(n,"open",e[3][e[15]])},d(A){A&&y(n),u&&u.d(),z(),c&&c.d(),g=!1,v()}}}function $(a){let e,n=a[1]&&B(a);return{c(){e=h("div"),n&&n.c(),m(e,"class","wrap svelte-1n1otz8")},m(t,l){b(t,e,l),n&&n.m(e,null)},p(t,[l]){t[1]?n?n.p(t,l):(n=B(t),n.c(),n.m(e,null)):n&&(n.d(1),n=null)},i:T,o:T,d(t){t&&y(e),n&&n.d()}}}function x(a,e,n){let t,{docs:l}=e,{lang:i="python"}=e,{linkify:s=[]}=e,o;function f(r,g){let v=I.highlight(r,I.languages[g],g);for(const u of s)v=v.replace(new RegExp(u,"g"),`<a href="#h-${u.toLocaleLowerCase()}">${u}</a>`);return v}function d(r,g){return Object.entries(r).map(([v,{type:u,description:q,default:z}])=>{let N=u?f(u,g):null;return{name:v,type:N,description:q,default:z?f(z,g):null}})}let w=[];function p(r,g){K[r?"unshift":"push"](()=>{w[g]=r,n(2,w)})}const S=r=>n(3,t[r]=!t[r],t);return a.$$set=r=>{"docs"in r&&n(4,l=r.docs),"lang"in r&&n(0,i=r.lang),"linkify"in r&&n(5,s=r.linkify)},a.$$.update=()=>{a.$$.dirty&17&&setTimeout(()=>{n(1,o=d(l,i))},0),a.$$.dirty&2&&n(3,t=o&&o.map(r=>!1))},[i,o,w,t,l,s,p,S]}class ee extends J{constructor(e){super(),U(this,e,x,$,X,{docs:4,lang:0,linkify:5})}}const{SvelteComponent:te,create_component:ne,destroy_component:le,init:ie,mount_component:se,safe_not_equal:ae,transition_in:oe,transition_out:fe}=window.__gradio__svelte__internal;function re(a){let e,n;return e=new ee({props:{docs:a[0],linkify:a[1]}}),{c(){ne(e.$$.fragment)},m(t,l){se(e,t,l),n=!0},p(t,[l]){const i={};l&1&&(i.docs=t[0]),l&2&&(i.linkify=t[1]),e.$set(i)},i(t){n||(oe(e.$$.fragment,t),n=!0)},o(t){fe(e.$$.fragment,t),n=!1},d(t){le(e,t)}}}function ue(a,e,n){let{value:t}=e,{linkify:l=[]}=e;return a.$$set=i=>{"value"in i&&n(0,t=i.value),"linkify"in i&&n(1,l=i.linkify)},[t,l]}class ge extends te{constructor(e){super(),ie(this,e,ue,re,ae,{value:0,linkify:1})}}export{ge as default};
//# sourceMappingURL=Index-a60f3b6d.js.map
