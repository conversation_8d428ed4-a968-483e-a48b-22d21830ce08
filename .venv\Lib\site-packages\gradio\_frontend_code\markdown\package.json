{"name": "@gradio/markdown", "version": "0.6.8", "description": "Gradio UI packages", "type": "module", "author": "", "license": "ISC", "private": false, "main_changeset": true, "main": "Index.svelte", "exports": {".": "./Index.svelte", "./example": "./Example.svelte", "./package.json": "./package.json"}, "dependencies": {"@gradio/atoms": "workspace:^", "@gradio/statustracker": "workspace:^", "@gradio/utils": "workspace:^", "@types/dompurify": "^3.0.2", "@types/katex": "^0.16.0", "@types/prismjs": "1.26.3", "dompurify": "^3.0.3", "github-slugger": "^2.0.0", "katex": "^0.16.7", "marked": "^12.0.0", "marked-gfm-heading-id": "^3.1.2", "marked-highlight": "^2.0.1", "prismjs": "1.29.0"}}