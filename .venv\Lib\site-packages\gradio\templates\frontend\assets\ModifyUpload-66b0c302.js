import"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{I as h}from"./IconButton-7294c90b.js";import{C as B}from"./Clear-2c7bae91.js";import{D as E,a as j}from"./DownloadLink-7ff36416.js";import"./Index-26cfc80a.js";import{U as z}from"./Undo-b088de14.js";import"./ModifyUpload.svelte_svelte_type_style_lang-4ba51100.js";const{SvelteComponent:P,append:A,attr:d,detach:F,init:G,insert:H,noop:k,safe_not_equal:J,svg_element:D}=window.__gradio__svelte__internal;function K(o){let e,t;return{c(){e=D("svg"),t=D("path"),d(t,"d","M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"),d(e,"xmlns","http://www.w3.org/2000/svg"),d(e,"width","100%"),d(e,"height","100%"),d(e,"viewBox","0 0 24 24"),d(e,"fill","none"),d(e,"stroke","currentColor"),d(e,"stroke-width","1.5"),d(e,"stroke-linecap","round"),d(e,"stroke-linejoin","round"),d(e,"class","feather feather-edit-2")},m(n,l){H(n,e,l),A(e,t)},p:k,i:k,o:k,d(n){n&&F(e)}}}class N extends P{constructor(e){super(),G(this,e,null,K,J,{})}}const{SvelteComponent:O,append:$,attr:Q,check_outros:v,create_component:g,destroy_component:p,detach:R,element:T,group_outros:C,init:V,insert:W,mount_component:w,safe_not_equal:X,set_style:q,space:I,toggle_class:L,transition_in:u,transition_out:_}=window.__gradio__svelte__internal,{createEventDispatcher:Y}=window.__gradio__svelte__internal;function M(o){let e,t;return e=new h({props:{Icon:N,label:o[4]("common.edit")}}),e.$on("click",o[6]),{c(){g(e.$$.fragment)},m(n,l){w(e,n,l),t=!0},p(n,l){const i={};l&16&&(i.label=n[4]("common.edit")),e.$set(i)},i(n){t||(u(e.$$.fragment,n),t=!0)},o(n){_(e.$$.fragment,n),t=!1},d(n){p(e,n)}}}function S(o){let e,t;return e=new h({props:{Icon:z,label:o[4]("common.undo")}}),e.$on("click",o[7]),{c(){g(e.$$.fragment)},m(n,l){w(e,n,l),t=!0},p(n,l){const i={};l&16&&(i.label=n[4]("common.undo")),e.$set(i)},i(n){t||(u(e.$$.fragment,n),t=!0)},o(n){_(e.$$.fragment,n),t=!1},d(n){p(e,n)}}}function U(o){let e,t;return e=new E({props:{href:o[2],download:!0,$$slots:{default:[Z]},$$scope:{ctx:o}}}),{c(){g(e.$$.fragment)},m(n,l){w(e,n,l),t=!0},p(n,l){const i={};l&4&&(i.href=n[2]),l&528&&(i.$$scope={dirty:l,ctx:n}),e.$set(i)},i(n){t||(u(e.$$.fragment,n),t=!0)},o(n){_(e.$$.fragment,n),t=!1},d(n){p(e,n)}}}function Z(o){let e,t;return e=new h({props:{Icon:j,label:o[4]("common.download")}}),{c(){g(e.$$.fragment)},m(n,l){w(e,n,l),t=!0},p(n,l){const i={};l&16&&(i.label=n[4]("common.download")),e.$set(i)},i(n){t||(u(e.$$.fragment,n),t=!0)},o(n){_(e.$$.fragment,n),t=!1},d(n){p(e,n)}}}function y(o){let e,t,n,l,i,b,s=o[0]&&M(o),a=o[1]&&S(o),c=o[2]&&U(o);return i=new h({props:{Icon:B,label:o[4]("common.clear")}}),i.$on("click",o[8]),{c(){e=T("div"),s&&s.c(),t=I(),a&&a.c(),n=I(),c&&c.c(),l=I(),g(i.$$.fragment),Q(e,"class","svelte-19sk1im"),L(e,"not-absolute",!o[3]),q(e,"position",o[3]?"absolute":"static")},m(r,m){W(r,e,m),s&&s.m(e,null),$(e,t),a&&a.m(e,null),$(e,n),c&&c.m(e,null),$(e,l),w(i,e,null),b=!0},p(r,[m]){r[0]?s?(s.p(r,m),m&1&&u(s,1)):(s=M(r),s.c(),u(s,1),s.m(e,t)):s&&(C(),_(s,1,1,()=>{s=null}),v()),r[1]?a?(a.p(r,m),m&2&&u(a,1)):(a=S(r),a.c(),u(a,1),a.m(e,n)):a&&(C(),_(a,1,1,()=>{a=null}),v()),r[2]?c?(c.p(r,m),m&4&&u(c,1)):(c=U(r),c.c(),u(c,1),c.m(e,l)):c&&(C(),_(c,1,1,()=>{c=null}),v());const f={};m&16&&(f.label=r[4]("common.clear")),i.$set(f),(!b||m&8)&&L(e,"not-absolute",!r[3]),m&8&&q(e,"position",r[3]?"absolute":"static")},i(r){b||(u(s),u(a),u(c),u(i.$$.fragment,r),b=!0)},o(r){_(s),_(a),_(c),_(i.$$.fragment,r),b=!1},d(r){r&&R(e),s&&s.d(),a&&a.d(),c&&c.d(),p(i)}}}function x(o,e,t){let{editable:n=!1}=e,{undoable:l=!1}=e,{download:i=null}=e,{absolute:b=!0}=e,{i18n:s}=e;const a=Y(),c=()=>a("edit"),r=()=>a("undo"),m=f=>{a("clear"),f.stopPropagation()};return o.$$set=f=>{"editable"in f&&t(0,n=f.editable),"undoable"in f&&t(1,l=f.undoable),"download"in f&&t(2,i=f.download),"absolute"in f&&t(3,b=f.absolute),"i18n"in f&&t(4,s=f.i18n)},[n,l,i,b,s,a,c,r,m]}class re extends O{constructor(e){super(),V(this,e,x,y,X,{editable:0,undoable:1,download:2,absolute:3,i18n:4})}}export{re as M};
//# sourceMappingURL=ModifyUpload-66b0c302.js.map
