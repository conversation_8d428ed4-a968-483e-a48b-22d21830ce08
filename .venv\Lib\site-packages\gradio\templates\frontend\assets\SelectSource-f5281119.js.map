{"version": 3, "file": "SelectSource-f5281119.js", "sources": ["../../../../js/icons/src/Microphone.svelte", "../../../../js/icons/src/Webcam.svelte", "../../../../js/atoms/src/SelectSource.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"2\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-mic\"\n\t><path d=\"M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z\" /><path\n\t\td=\"M19 10v2a7 7 0 0 1-14 0v-2\"\n\t/><line x1=\"12\" y1=\"19\" x2=\"12\" y2=\"23\" /><line\n\t\tx1=\"8\"\n\t\ty1=\"23\"\n\t\tx2=\"16\"\n\t\ty2=\"23\"\n\t/></svg\n>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\t><path\n\t\tfill=\"currentColor\"\n\t\td=\"M12 2c-4.963 0-9 4.038-9 9c0 3.328 1.82 6.232 4.513 7.79l-2.067 1.378A1 1 0 0 0 6 22h12a1 1 0 0 0 .555-1.832l-2.067-1.378C19.18 17.232 21 14.328 21 11c0-4.962-4.037-9-9-9zm0 16c-3.859 0-7-3.141-7-7c0-3.86 3.141-7 7-7s7 3.14 7 7c0 3.859-3.141 7-7 7z\"\n\t/><path\n\t\tfill=\"currentColor\"\n\t\td=\"M12 6c-2.757 0-5 2.243-5 5s2.243 5 5 5s5-2.243 5-5s-2.243-5-5-5zm0 8c-1.654 0-3-1.346-3-3s1.346-3 3-3s3 1.346 3 3s-1.346 3-3 3z\"\n\t/></svg\n>\n", "<script lang=\"ts\">\n\timport { Microphone, Upload, Webcam, ImagePaste } from \"@gradio/icons\";\n\n\ttype source_types = \"upload\" | \"microphone\" | \"webcam\" | \"clipboard\" | null;\n\n\texport let sources: Partial<source_types>[];\n\texport let active_source: Partial<source_types>;\n\texport let handle_clear: () => void = () => {};\n\texport let handle_select: (\n\t\tsource_type: Partial<source_types>\n\t) => void = () => {};\n\n\t$: unique_sources = [...new Set(sources)];\n\n\tasync function handle_select_source(\n\t\tsource: Partial<source_types>\n\t): Promise<void> {\n\t\thandle_clear();\n\t\tactive_source = source;\n\t\thandle_select(source);\n\t}\n</script>\n\n{#if unique_sources.length > 1}\n\t<span class=\"source-selection\" data-testid=\"source-select\">\n\t\t{#if sources.includes(\"upload\")}\n\t\t\t<button\n\t\t\t\tclass=\"icon\"\n\t\t\t\tclass:selected={active_source === \"upload\" || !active_source}\n\t\t\t\taria-label=\"Upload file\"\n\t\t\t\ton:click={() => handle_select_source(\"upload\")}><Upload /></button\n\t\t\t>\n\t\t{/if}\n\n\t\t{#if sources.includes(\"microphone\")}\n\t\t\t<button\n\t\t\t\tclass=\"icon\"\n\t\t\t\tclass:selected={active_source === \"microphone\"}\n\t\t\t\taria-label=\"Record audio\"\n\t\t\t\ton:click={() => handle_select_source(\"microphone\")}\n\t\t\t\t><Microphone /></button\n\t\t\t>\n\t\t{/if}\n\n\t\t{#if sources.includes(\"webcam\")}\n\t\t\t<button\n\t\t\t\tclass=\"icon\"\n\t\t\t\tclass:selected={active_source === \"webcam\"}\n\t\t\t\taria-label=\"Capture from camera\"\n\t\t\t\ton:click={() => handle_select_source(\"webcam\")}><Webcam /></button\n\t\t\t>\n\t\t{/if}\n\t\t{#if sources.includes(\"clipboard\")}\n\t\t\t<button\n\t\t\t\tclass=\"icon\"\n\t\t\t\tclass:selected={active_source === \"clipboard\"}\n\t\t\t\taria-label=\"Paste from clipboard\"\n\t\t\t\ton:click={() => handle_select_source(\"clipboard\")}\n\t\t\t\t><ImagePaste /></button\n\t\t\t>\n\t\t{/if}\n\t</span>\n{/if}\n\n<style>\n\t.source-selection {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder-top: 1px solid var(--border-color-primary);\n\t\twidth: 95%;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tmargin-left: auto;\n\t\tmargin-right: auto;\n\t}\n\n\t.icon {\n\t\twidth: 22px;\n\t\theight: 22px;\n\t\tmargin: var(--spacing-lg) var(--spacing-xs);\n\t\tpadding: var(--spacing-xs);\n\t\tcolor: var(--neutral-400);\n\t\tborder-radius: var(--radius-md);\n\t}\n\n\t.selected {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.icon:hover,\n\t.icon:focus {\n\t\tcolor: var(--color-accent);\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path0", "path1", "line0", "line1", "ctx", "span", "button", "toggle_class", "create_if_block", "sources", "$$props", "active_source", "handle_clear", "handle_select", "handle_select_source", "source", "$$invalidate", "click_handler", "click_handler_1", "click_handler_2", "click_handler_3", "unique_sources"], "mappings": "o3BAAAA,EAmBAC,EAAAC,EAAAC,CAAA,EAREC,EAAiEF,EAAAG,CAAA,EAAAD,EAEhEF,EAAAI,CAAA,EAAAF,EAAwCF,EAAAK,CAAA,EAAAH,EAKxCF,EAAAM,CAAA,82BClBHR,GAYAC,EAAAC,EAAAC,CAAA,EAPEC,EAGCF,EAAAG,CAAA,EAAAD,EAGAF,EAAAI,CAAA,4ZCcIG,EAAO,CAAA,EAAC,SAAS,QAAQ,MASzBA,EAAO,CAAA,EAAC,SAAS,YAAY,MAU7BA,EAAO,CAAA,EAAC,SAAS,QAAQ,MAQzBA,EAAO,CAAA,EAAC,SAAS,WAAW,mNA5BlCT,EAqCMC,EAAAS,EAAAP,CAAA,wGApCAM,EAAO,CAAA,EAAC,SAAS,QAAQ,wGASzBA,EAAO,CAAA,EAAC,SAAS,YAAY,wGAU7BA,EAAO,CAAA,EAAC,SAAS,QAAQ,wGAQzBA,EAAO,CAAA,EAAC,SAAS,WAAW,+XAxBfA,EAAa,CAAA,IAAK,UAAQ,CAAKA,EAAa,CAAA,CAAA,UAF7DT,EAKAC,EAAAU,EAAAR,CAAA,mFAHiBM,EAAa,CAAA,IAAK,UAAQ,CAAKA,EAAa,CAAA,CAAA,wPAS5CG,EAAAD,EAAA,WAAAF,OAAkB,YAAY,UAF/CT,EAMAC,EAAAU,EAAAR,CAAA,oEAJiBS,EAAAD,EAAA,WAAAF,OAAkB,YAAY,gQAU9BG,EAAAD,EAAA,WAAAF,OAAkB,QAAQ,UAF3CT,EAKAC,EAAAU,EAAAR,CAAA,oEAHiBS,EAAAD,EAAA,WAAAF,OAAkB,QAAQ,gQAQ1BG,EAAAD,EAAA,WAAAF,OAAkB,WAAW,UAF9CT,EAMAC,EAAAU,EAAAR,CAAA,oEAJiBS,EAAAD,EAAA,WAAAF,OAAkB,WAAW,0HAhC5CA,EAAc,CAAA,EAAC,OAAS,GAACI,EAAAJ,CAAA,yEAAzBA,EAAc,CAAA,EAAC,OAAS,iMAlBjB,QAAAK,CAAgC,EAAAC,GAChC,cAAAC,CAAoC,EAAAD,GACpC,aAAAE,EAAY,IAAA,OACZ,cAAAC,EAAa,IAAA,MAMT,eAAAC,EACdC,EAA6B,CAE7BH,IACAI,EAAA,EAAAL,EAAgBI,CAAM,EACtBF,EAAcE,CAAM,EAWF,MAAAE,EAAA,IAAAH,EAAqB,QAAQ,EAS7BI,EAAA,IAAAJ,EAAqB,YAAY,EAUjCK,EAAA,IAAAL,EAAqB,QAAQ,EAQ7BM,EAAA,IAAAN,EAAqB,WAAW,uNA7ChDE,EAAA,EAAAK,EAAyB,CAAA,GAAA,IAAA,IAAIZ,CAAO,CAAA,CAAA"}