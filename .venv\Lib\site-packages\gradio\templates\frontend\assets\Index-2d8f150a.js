const{SvelteComponent:c,append:m,attr:d,create_slot:g,detach:v,element:f,get_all_dirty_from_scope:b,get_slot_changes:w,init:h,insert:p,safe_not_equal:y,set_style:a,toggle_class:r,transition_in:j,transition_out:k,update_slot_base:I}=window.__gradio__svelte__internal;function q(_){let l,t,o,n;const u=_[4].default,i=g(u,_,_[3],null);return{c(){l=f("div"),t=f("div"),i&&i.c(),d(t,"class","styler svelte-iyf88w"),a(t,"--block-radius","0px"),a(t,"--block-border-width","0px"),a(t,"--layout-gap","1px"),a(t,"--form-gap-width","1px"),a(t,"--button-border-width","0px"),a(t,"--button-large-radius","0px"),a(t,"--button-small-radius","0px"),d(l,"id",_[0]),d(l,"class",o="gr-group "+_[1].join(" ")+" svelte-iyf88w"),r(l,"hide",!_[2])},m(e,s){p(e,l,s),m(l,t),i&&i.m(t,null),n=!0},p(e,[s]){i&&i.p&&(!n||s&8)&&I(i,u,e,e[3],n?w(u,e[3],s,null):b(e[3]),null),(!n||s&1)&&d(l,"id",e[0]),(!n||s&2&&o!==(o="gr-group "+e[1].join(" ")+" svelte-iyf88w"))&&d(l,"class",o),(!n||s&6)&&r(l,"hide",!e[2])},i(e){n||(j(i,e),n=!0)},o(e){k(i,e),n=!1},d(e){e&&v(l),i&&i.d(e)}}}function C(_,l,t){let{$$slots:o={},$$scope:n}=l,{elem_id:u=""}=l,{elem_classes:i=[]}=l,{visible:e=!0}=l;return _.$$set=s=>{"elem_id"in s&&t(0,u=s.elem_id),"elem_classes"in s&&t(1,i=s.elem_classes),"visible"in s&&t(2,e=s.visible),"$$scope"in s&&t(3,n=s.$$scope)},[u,i,e,n,o]}class S extends c{constructor(l){super(),h(this,l,C,q,y,{elem_id:0,elem_classes:1,visible:2})}}export{S as default};
//# sourceMappingURL=Index-2d8f150a.js.map
