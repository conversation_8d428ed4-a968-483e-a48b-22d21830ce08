import{M as C}from"./Index.svelte_svelte_type_style_lang-e1d4a36d.js";import{S as j}from"./Index-26cfc80a.js";import{B as q}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{default as y}from"./Example-49f98efe.js";import{M as p}from"./Example.svelte_svelte_type_style_lang-648fc18a.js";import"./utils-572af92b.js";import"./index-a80d931b.js";import"./svelte/svelte.js";import"./prism-python-b0b31d02.js";const{SvelteComponent:E,assign:I,attr:A,create_component:d,destroy_component:h,detach:z,element:D,get_spread_object:F,get_spread_update:G,init:H,insert:M,mount_component:c,safe_not_equal:J,space:K,toggle_class:B,transition_in:k,transition_out:b}=window.__gradio__svelte__internal;function L(t){let l,n,a,_,m;const f=[{autoscroll:t[8].autoscroll},{i18n:t[8].i18n},t[4],{variant:"center"}];let u={};for(let e=0;e<f.length;e+=1)u=I(u,f[e]);return l=new j({props:u}),_=new C({props:{min_height:t[4]&&t[4].status!=="complete",value:t[3],elem_classes:t[1],visible:t[2],rtl:t[5],latex_delimiters:t[9],sanitize_html:t[6],line_breaks:t[7],header_links:t[10]}}),_.$on("change",t[12]),{c(){d(l.$$.fragment),n=K(),a=D("div"),d(_.$$.fragment),A(a,"class","svelte-1ed2p3z"),B(a,"pending",t[4]?.status==="pending")},m(e,s){c(l,e,s),M(e,n,s),M(e,a,s),c(_,a,null),m=!0},p(e,s){const o=s&272?G(f,[s&256&&{autoscroll:e[8].autoscroll},s&256&&{i18n:e[8].i18n},s&16&&F(e[4]),f[3]]):{};l.$set(o);const r={};s&16&&(r.min_height=e[4]&&e[4].status!=="complete"),s&8&&(r.value=e[3]),s&2&&(r.elem_classes=e[1]),s&4&&(r.visible=e[2]),s&32&&(r.rtl=e[5]),s&512&&(r.latex_delimiters=e[9]),s&64&&(r.sanitize_html=e[6]),s&128&&(r.line_breaks=e[7]),s&1024&&(r.header_links=e[10]),_.$set(r),(!m||s&16)&&B(a,"pending",e[4]?.status==="pending")},i(e){m||(k(l.$$.fragment,e),k(_.$$.fragment,e),m=!0)},o(e){b(l.$$.fragment,e),b(_.$$.fragment,e),m=!1},d(e){e&&(z(n),z(a)),h(l,e),h(_)}}}function N(t){let l,n;return l=new q({props:{visible:t[2],elem_id:t[0],elem_classes:t[1],container:!1,allow_overflow:!0,$$slots:{default:[L]},$$scope:{ctx:t}}}),{c(){d(l.$$.fragment)},m(a,_){c(l,a,_),n=!0},p(a,[_]){const m={};_&4&&(m.visible=a[2]),_&1&&(m.elem_id=a[0]),_&2&&(m.elem_classes=a[1]),_&10238&&(m.$$scope={dirty:_,ctx:a}),l.$set(m)},i(a){n||(k(l.$$.fragment,a),n=!0)},o(a){b(l.$$.fragment,a),n=!1},d(a){h(l,a)}}}function O(t,l,n){let{label:a}=l,{elem_id:_=""}=l,{elem_classes:m=[]}=l,{visible:f=!0}=l,{value:u=""}=l,{loading_status:e}=l,{rtl:s=!1}=l,{sanitize_html:o=!0}=l,{line_breaks:r=!1}=l,{gradio:g}=l,{latex_delimiters:w}=l,{header_links:v=!1}=l;const S=()=>g.dispatch("change");return t.$$set=i=>{"label"in i&&n(11,a=i.label),"elem_id"in i&&n(0,_=i.elem_id),"elem_classes"in i&&n(1,m=i.elem_classes),"visible"in i&&n(2,f=i.visible),"value"in i&&n(3,u=i.value),"loading_status"in i&&n(4,e=i.loading_status),"rtl"in i&&n(5,s=i.rtl),"sanitize_html"in i&&n(6,o=i.sanitize_html),"line_breaks"in i&&n(7,r=i.line_breaks),"gradio"in i&&n(8,g=i.gradio),"latex_delimiters"in i&&n(9,w=i.latex_delimiters),"header_links"in i&&n(10,v=i.header_links)},t.$$.update=()=>{t.$$.dirty&2304&&g.dispatch("change")},[_,m,f,u,e,s,o,r,g,w,v,a,S]}class X extends E{constructor(l){super(),H(this,l,O,N,J,{label:11,elem_id:0,elem_classes:1,visible:2,value:3,loading_status:4,rtl:5,sanitize_html:6,line_breaks:7,gradio:8,latex_delimiters:9,header_links:10})}}export{y as BaseExample,C as BaseMarkdown,p as MarkdownCode,X as default};
//# sourceMappingURL=Index-f45b736b.js.map
