import"./Index-26cfc80a.js";import{U as A,I as B}from"./Upload-351cc897.js";import"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";const{SvelteComponent:D,append:m,attr:w,check_outros:E,create_component:T,destroy_component:j,detach:g,element:U,group_outros:F,init:G,insert:b,mount_component:z,safe_not_equal:H,set_data:I,space:q,text:v,toggle_class:P,transition_in:k,transition_out:h}=window.__gradio__svelte__internal;function J(i){let e,o;return e=new A({}),{c(){T(e.$$.fragment)},m(t,n){z(e,t,n),o=!0},i(t){o||(k(e.$$.fragment,t),o=!0)},o(t){h(e.$$.fragment,t),o=!1},d(t){j(e,t)}}}function K(i){let e,o;return e=new B({}),{c(){T(e.$$.fragment)},m(t,n){z(e,t,n),o=!0},i(t){o||(k(e.$$.fragment,t),o=!0)},o(t){h(e.$$.fragment,t),o=!1},d(t){j(e,t)}}}function S(i){let e,o,t=i[1]("common.or")+"",n,d,u,_=(i[2]||i[1]("upload_text.click_to_upload"))+"",f;return{c(){e=U("span"),o=v("- "),n=v(t),d=v(" -"),u=q(),f=v(_),w(e,"class","or svelte-b0hvie")},m(l,s){b(l,e,s),m(e,o),m(e,n),m(e,d),b(l,u,s),b(l,f,s)},p(l,s){s&2&&t!==(t=l[1]("common.or")+"")&&I(n,t),s&6&&_!==(_=(l[2]||l[1]("upload_text.click_to_upload"))+"")&&I(f,_)},d(l){l&&(g(e),g(u),g(f))}}}function L(i){let e,o,t,n,d,u=i[1](i[5][i[0]]||i[5].file)+"",_,f,l;const s=[K,J],c=[];function C(r,p){return r[0]==="clipboard"?0:1}t=C(i),n=c[t]=s[t](i);let a=i[3]!=="short"&&S(i);return{c(){e=U("div"),o=U("span"),n.c(),d=q(),_=v(u),f=q(),a&&a.c(),w(o,"class","icon-wrap svelte-b0hvie"),P(o,"hovered",i[4]),w(e,"class","wrap svelte-b0hvie")},m(r,p){b(r,e,p),m(e,o),c[t].m(o,null),m(e,d),m(e,_),m(e,f),a&&a.m(e,null),l=!0},p(r,[p]){let y=t;t=C(r),t!==y&&(F(),h(c[y],1,1,()=>{c[y]=null}),E(),n=c[t],n||(n=c[t]=s[t](r),n.c()),k(n,1),n.m(o,null)),(!l||p&16)&&P(o,"hovered",r[4]),(!l||p&3)&&u!==(u=r[1](r[5][r[0]]||r[5].file)+"")&&I(_,u),r[3]!=="short"?a?a.p(r,p):(a=S(r),a.c(),a.m(e,null)):a&&(a.d(1),a=null)},i(r){l||(k(n),l=!0)},o(r){h(n),l=!1},d(r){r&&g(e),c[t].d(),a&&a.d()}}}function M(i,e,o){let{type:t="file"}=e,{i18n:n}=e,{message:d=void 0}=e,{mode:u="full"}=e,{hovered:_=!1}=e;const f={image:"upload_text.drop_image",video:"upload_text.drop_video",audio:"upload_text.drop_audio",file:"upload_text.drop_file",csv:"upload_text.drop_csv",gallery:"upload_text.drop_gallery",clipboard:"upload_text.paste_clipboard"};return i.$$set=l=>{"type"in l&&o(0,t=l.type),"i18n"in l&&o(1,n=l.i18n),"message"in l&&o(2,d=l.message),"mode"in l&&o(3,u=l.mode),"hovered"in l&&o(4,_=l.hovered)},[t,n,d,u,_,f]}class R extends D{constructor(e){super(),G(this,e,M,L,H,{type:0,i18n:1,message:2,mode:3,hovered:4})}}export{R as U};
//# sourceMappingURL=UploadText-39c67ae9.js.map
