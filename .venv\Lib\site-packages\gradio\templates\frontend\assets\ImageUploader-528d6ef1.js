import{f as mt}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{u as gt}from"./utils-572af92b.js";import{B as at}from"./BlockLabel-f27805b1.js";import{I as st}from"./IconButton-7294c90b.js";import{E as pt}from"./Empty-28f63bf0.js";import{S as bt}from"./ShareButton-7dae44e7.js";import{D as ht,a as wt}from"./DownloadLink-7ff36416.js";import"./Index-26cfc80a.js";import{I as Le}from"./Image-eaba773f.js";import{I as ct}from"./Image-21c02477.js";import{p as vt,u as kt}from"./index-a80d931b.js";import{W as $t,S as yt}from"./SelectSource-f5281119.js";import{D as ut}from"./DropdownArrow-bb2afb7e.js";import{U as St}from"./ModifyUpload.svelte_svelte_type_style_lang-4ba51100.js";import{C as It}from"./Clear-2c7bae91.js";const{SvelteComponent:qt,append:Oe,attr:N,detach:Ct,init:Dt,insert:Et,noop:Se,safe_not_equal:Wt,svg_element:Ie}=window.__gradio__svelte__internal;function Mt(o){let e,n,t;return{c(){e=Ie("svg"),n=Ie("path"),t=Ie("circle"),N(n,"d","M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"),N(t,"cx","12"),N(t,"cy","13"),N(t,"r","4"),N(e,"xmlns","http://www.w3.org/2000/svg"),N(e,"width","100%"),N(e,"height","100%"),N(e,"viewBox","0 0 24 24"),N(e,"fill","none"),N(e,"stroke","currentColor"),N(e,"stroke-width","1.5"),N(e,"stroke-linecap","round"),N(e,"stroke-linejoin","round"),N(e,"class","feather feather-camera")},m(l,i){Et(l,e,i),Oe(e,n),Oe(e,t)},p:Se,i:Se,o:Se,d(l){l&&Ct(e)}}}class Bt extends qt{constructor(e){super(),Dt(this,e,null,Mt,Wt,{})}}const{SvelteComponent:Nt,append:Rt,attr:z,detach:Tt,init:Ht,insert:Ut,noop:qe,safe_not_equal:Pt,svg_element:Ve}=window.__gradio__svelte__internal;function jt(o){let e,n;return{c(){e=Ve("svg"),n=Ve("circle"),z(n,"cx","12"),z(n,"cy","12"),z(n,"r","10"),z(e,"xmlns","http://www.w3.org/2000/svg"),z(e,"width","100%"),z(e,"height","100%"),z(e,"viewBox","0 0 24 24"),z(e,"stroke-width","1.5"),z(e,"stroke-linecap","round"),z(e,"stroke-linejoin","round"),z(e,"class","feather feather-circle")},m(t,l){Ut(t,e,l),Rt(e,n)},p:qe,i:qe,o:qe,d(t){t&&Tt(e)}}}class Lt extends Nt{constructor(e){super(),Ht(this,e,null,jt,Pt,{})}}const{SvelteComponent:zt,append:At,attr:R,detach:Ft,init:Ot,insert:Vt,noop:Ce,safe_not_equal:Xt,svg_element:Xe}=window.__gradio__svelte__internal;function Yt(o){let e,n;return{c(){e=Xe("svg"),n=Xe("rect"),R(n,"x","3"),R(n,"y","3"),R(n,"width","18"),R(n,"height","18"),R(n,"rx","2"),R(n,"ry","2"),R(e,"xmlns","http://www.w3.org/2000/svg"),R(e,"width","100%"),R(e,"height","100%"),R(e,"viewBox","0 0 24 24"),R(e,"stroke-width","1.5"),R(e,"stroke-linecap","round"),R(e,"stroke-linejoin","round"),R(e,"class","feather feather-square")},m(t,l){Vt(t,e,l),At(e,n)},p:Ce,i:Ce,o:Ce,d(t){t&&Ft(e)}}}class Gt extends zt{constructor(e){super(),Ot(this,e,null,Yt,Xt,{})}}const _t=o=>{let e;if(o.currentTarget instanceof Element)e=o.currentTarget.querySelector("img");else return[NaN,NaN];const n=e.getBoundingClientRect(),t=e.naturalWidth/n.width,l=e.naturalHeight/n.height;if(t>l){const _=e.naturalHeight/t,c=(n.height-_)/2;var i=Math.round((o.clientX-n.left)*t),r=Math.round((o.clientY-n.top-c)*t)}else{const _=e.naturalWidth/l,c=(n.width-_)/2;var i=Math.round((o.clientX-n.left-c)*l),r=Math.round((o.clientY-n.top)*l)}return i<0||i>=e.naturalWidth||r<0||r>=e.naturalHeight?null:[i,r]};const{SvelteComponent:Jt,append:Ye,attr:De,bubble:Ge,check_outros:Ne,create_component:V,destroy_component:X,detach:te,element:Ee,empty:Kt,group_outros:Re,init:Qt,insert:ne,listen:Zt,mount_component:Y,safe_not_equal:xt,space:Te,toggle_class:Je,transition_in:M,transition_out:P}=window.__gradio__svelte__internal,{createEventDispatcher:en}=window.__gradio__svelte__internal;function tn(o){let e,n,t,l,i,r,_,c,g,a=o[3]&&Ke(o),s=o[5]&&Qe(o);return r=new ct({props:{src:o[0].url,alt:"",loading:"lazy"}}),{c(){e=Ee("div"),a&&a.c(),n=Te(),s&&s.c(),t=Te(),l=Ee("button"),i=Ee("div"),V(r.$$.fragment),De(e,"class","icon-buttons svelte-1l6wqyv"),De(i,"class","image-container svelte-1l6wqyv"),Je(i,"selectable",o[4]),De(l,"class","svelte-1l6wqyv")},m(m,h){ne(m,e,h),a&&a.m(e,null),Ye(e,n),s&&s.m(e,null),ne(m,t,h),ne(m,l,h),Ye(l,i),Y(r,i,null),_=!0,c||(g=Zt(l,"click",o[7]),c=!0)},p(m,h){m[3]?a?(a.p(m,h),h&8&&M(a,1)):(a=Ke(m),a.c(),M(a,1),a.m(e,n)):a&&(Re(),P(a,1,1,()=>{a=null}),Ne()),m[5]?s?(s.p(m,h),h&32&&M(s,1)):(s=Qe(m),s.c(),M(s,1),s.m(e,null)):s&&(Re(),P(s,1,1,()=>{s=null}),Ne());const S={};h&1&&(S.src=m[0].url),r.$set(S),(!_||h&16)&&Je(i,"selectable",m[4])},i(m){_||(M(a),M(s),M(r.$$.fragment,m),_=!0)},o(m){P(a),P(s),P(r.$$.fragment,m),_=!1},d(m){m&&(te(e),te(t),te(l)),a&&a.d(),s&&s.d(),X(r),c=!1,g()}}}function nn(o){let e,n;return e=new pt({props:{unpadded_box:!0,size:"large",$$slots:{default:[on]},$$scope:{ctx:o}}}),{c(){V(e.$$.fragment)},m(t,l){Y(e,t,l),n=!0},p(t,l){const i={};l&4096&&(i.$$scope={dirty:l,ctx:t}),e.$set(i)},i(t){n||(M(e.$$.fragment,t),n=!0)},o(t){P(e.$$.fragment,t),n=!1},d(t){X(e,t)}}}function Ke(o){let e,n;return e=new ht({props:{href:o[0].url,download:o[0].orig_name||"image",$$slots:{default:[ln]},$$scope:{ctx:o}}}),{c(){V(e.$$.fragment)},m(t,l){Y(e,t,l),n=!0},p(t,l){const i={};l&1&&(i.href=t[0].url),l&1&&(i.download=t[0].orig_name||"image"),l&4160&&(i.$$scope={dirty:l,ctx:t}),e.$set(i)},i(t){n||(M(e.$$.fragment,t),n=!0)},o(t){P(e.$$.fragment,t),n=!1},d(t){X(e,t)}}}function ln(o){let e,n;return e=new st({props:{Icon:wt,label:o[6]("common.download")}}),{c(){V(e.$$.fragment)},m(t,l){Y(e,t,l),n=!0},p(t,l){const i={};l&64&&(i.label=t[6]("common.download")),e.$set(i)},i(t){n||(M(e.$$.fragment,t),n=!0)},o(t){P(e.$$.fragment,t),n=!1},d(t){X(e,t)}}}function Qe(o){let e,n;return e=new bt({props:{i18n:o[6],formatter:o[8],value:o[0]}}),e.$on("share",o[9]),e.$on("error",o[10]),{c(){V(e.$$.fragment)},m(t,l){Y(e,t,l),n=!0},p(t,l){const i={};l&64&&(i.i18n=t[6]),l&1&&(i.value=t[0]),e.$set(i)},i(t){n||(M(e.$$.fragment,t),n=!0)},o(t){P(e.$$.fragment,t),n=!1},d(t){X(e,t)}}}function on(o){let e,n;return e=new Le({}),{c(){V(e.$$.fragment)},m(t,l){Y(e,t,l),n=!0},i(t){n||(M(e.$$.fragment,t),n=!0)},o(t){P(e.$$.fragment,t),n=!1},d(t){X(e,t)}}}function rn(o){let e,n,t,l,i,r;e=new at({props:{show_label:o[2],Icon:Le,label:o[1]||o[6]("image.image")}});const _=[nn,tn],c=[];function g(a,s){return a[0]===null||!a[0].url?0:1}return t=g(o),l=c[t]=_[t](o),{c(){V(e.$$.fragment),n=Te(),l.c(),i=Kt()},m(a,s){Y(e,a,s),ne(a,n,s),c[t].m(a,s),ne(a,i,s),r=!0},p(a,[s]){const m={};s&4&&(m.show_label=a[2]),s&66&&(m.label=a[1]||a[6]("image.image")),e.$set(m);let h=t;t=g(a),t===h?c[t].p(a,s):(Re(),P(c[h],1,1,()=>{c[h]=null}),Ne(),l=c[t],l?l.p(a,s):(l=c[t]=_[t](a),l.c()),M(l,1),l.m(i.parentNode,i))},i(a){r||(M(e.$$.fragment,a),M(l),r=!0)},o(a){P(e.$$.fragment,a),P(l),r=!1},d(a){a&&(te(n),te(i)),X(e,a),c[t].d(a)}}}function an(o,e,n){let{value:t}=e,{label:l=void 0}=e,{show_label:i}=e,{show_download_button:r=!0}=e,{selectable:_=!1}=e,{show_share_button:c=!1}=e,{i18n:g}=e;const a=en(),s=u=>{let b=_t(u);b&&a("select",{index:b,value:null})},m=async u=>u?`<img src="${await gt(u,"base64")}" />`:"";function h(u){Ge.call(this,o,u)}function S(u){Ge.call(this,o,u)}return o.$$set=u=>{"value"in u&&n(0,t=u.value),"label"in u&&n(1,l=u.label),"show_label"in u&&n(2,i=u.show_label),"show_download_button"in u&&n(3,r=u.show_download_button),"selectable"in u&&n(4,_=u.selectable),"show_share_button"in u&&n(5,c=u.show_share_button),"i18n"in u&&n(6,g=u.i18n)},[t,l,i,r,_,c,g,s,m,h,S]}class sn extends Jt{constructor(e){super(),Qt(this,e,an,rn,xt,{value:0,label:1,show_label:2,show_download_button:3,selectable:4,show_share_button:5,i18n:6})}}const Gl=sn;const{SvelteComponent:cn,append:re,attr:We,create_component:un,destroy_component:_n,detach:fn,element:Me,init:dn,insert:mn,listen:gn,mount_component:pn,noop:bn,safe_not_equal:hn,set_style:wn,space:vn,text:kn,transition_in:$n,transition_out:yn}=window.__gradio__svelte__internal,{createEventDispatcher:Sn}=window.__gradio__svelte__internal;function In(o){let e,n,t,l,i,r="Click to Access Webcam",_,c,g,a;return l=new $t({}),{c(){e=Me("button"),n=Me("div"),t=Me("span"),un(l.$$.fragment),i=vn(),_=kn(r),We(t,"class","icon-wrap svelte-qbrfs"),We(n,"class","wrap svelte-qbrfs"),We(e,"class","svelte-qbrfs"),wn(e,"height","100%")},m(s,m){mn(s,e,m),re(e,n),re(n,t),pn(l,t,null),re(n,i),re(n,_),c=!0,g||(a=gn(e,"click",o[1]),g=!0)},p:bn,i(s){c||($n(l.$$.fragment,s),c=!0)},o(s){yn(l.$$.fragment,s),c=!1},d(s){s&&fn(e),_n(l),g=!1,a()}}}function qn(o){const e=Sn();return[e,()=>e("click")]}class Cn extends cn{constructor(e){super(),dn(this,e,qn,In,hn,{})}}const{SvelteComponent:Dn,action_destroyer:En,add_render_callback:Wn,append:F,attr:y,binding_callbacks:Mn,check_outros:le,create_component:Z,create_in_transition:Bn,destroy_component:x,destroy_each:Nn,detach:T,element:j,empty:ze,ensure_array_like:Ze,group_outros:oe,init:Rn,insert:H,listen:pe,mount_component:ee,noop:Ae,run_all:Tn,safe_not_equal:Hn,set_data:ft,set_input_value:He,space:ie,stop_propagation:Un,text:dt,toggle_class:ae,transition_in:q,transition_out:D}=window.__gradio__svelte__internal,{createEventDispatcher:Pn,onMount:jn}=window.__gradio__svelte__internal;function xe(o,e,n){const t=o.slice();return t[31]=e[n],t}function Ln(o){let e,n,t,l,i,r,_,c,g,a,s;const m=[Fn,An],h=[];function S(w,v){return w[1]==="video"||w[0]?0:1}t=S(o),l=h[t]=m[t](o);let u=!o[6]&&et(o),b=o[9]&&tt(o);return{c(){e=j("div"),n=j("button"),l.c(),r=ie(),u&&u.c(),_=ie(),b&&b.c(),c=ze(),y(n,"aria-label",i=o[1]==="image"?"capture photo":"start recording"),y(n,"class","svelte-1aa1mud"),y(e,"class","button-wrap svelte-1aa1mud")},m(w,v){H(w,e,v),F(e,n),h[t].m(n,null),F(e,r),u&&u.m(e,null),H(w,_,v),b&&b.m(w,v),H(w,c,v),g=!0,a||(s=pe(n,"click",o[11]),a=!0)},p(w,v){let B=t;t=S(w),t===B?h[t].p(w,v):(oe(),D(h[B],1,1,()=>{h[B]=null}),le(),l=h[t],l?l.p(w,v):(l=h[t]=m[t](w),l.c()),q(l,1),l.m(n,null)),(!g||v[0]&2&&i!==(i=w[1]==="image"?"capture photo":"start recording"))&&y(n,"aria-label",i),w[6]?u&&(oe(),D(u,1,1,()=>{u=null}),le()):u?(u.p(w,v),v[0]&64&&q(u,1)):(u=et(w),u.c(),q(u,1),u.m(e,null)),w[9]?b?(b.p(w,v),v[0]&512&&q(b,1)):(b=tt(w),b.c(),q(b,1),b.m(c.parentNode,c)):b&&(oe(),D(b,1,1,()=>{b=null}),le())},i(w){g||(q(l),q(u),q(b),g=!0)},o(w){D(l),D(u),D(b),g=!1},d(w){w&&(T(e),T(_),T(c)),h[t].d(),u&&u.d(),b&&b.d(w),a=!1,s()}}}function zn(o){let e,n,t,l;return n=new Cn({}),n.$on("click",o[19]),{c(){e=j("div"),Z(n.$$.fragment),y(e,"title","grant webcam access")},m(i,r){H(i,e,r),ee(n,e,null),l=!0},p:Ae,i(i){l||(q(n.$$.fragment,i),i&&(t||Wn(()=>{t=Bn(e,mt,{delay:100,duration:200}),t.start()})),l=!0)},o(i){D(n.$$.fragment,i),l=!1},d(i){i&&T(e),x(n)}}}function An(o){let e,n,t;return n=new Bt({}),{c(){e=j("div"),Z(n.$$.fragment),y(e,"class","icon svelte-1aa1mud"),y(e,"title","capture photo")},m(l,i){H(l,e,i),ee(n,e,null),t=!0},p:Ae,i(l){t||(q(n.$$.fragment,l),t=!0)},o(l){D(n.$$.fragment,l),t=!1},d(l){l&&T(e),x(n)}}}function Fn(o){let e,n,t,l;const i=[Vn,On],r=[];function _(c,g){return c[6]?0:1}return e=_(o),n=r[e]=i[e](o),{c(){n.c(),t=ze()},m(c,g){r[e].m(c,g),H(c,t,g),l=!0},p(c,g){let a=e;e=_(c),e!==a&&(oe(),D(r[a],1,1,()=>{r[a]=null}),le(),n=r[e],n||(n=r[e]=i[e](c),n.c()),q(n,1),n.m(t.parentNode,t))},i(c){l||(q(n),l=!0)},o(c){D(n),l=!1},d(c){c&&T(t),r[e].d(c)}}}function On(o){let e,n,t;return n=new Lt({}),{c(){e=j("div"),Z(n.$$.fragment),y(e,"class","icon red svelte-1aa1mud"),y(e,"title","start recording")},m(l,i){H(l,e,i),ee(n,e,null),t=!0},i(l){t||(q(n.$$.fragment,l),t=!0)},o(l){D(n.$$.fragment,l),t=!1},d(l){l&&T(e),x(n)}}}function Vn(o){let e,n,t;return n=new Gt({}),{c(){e=j("div"),Z(n.$$.fragment),y(e,"class","icon red svelte-1aa1mud"),y(e,"title","stop recording")},m(l,i){H(l,e,i),ee(n,e,null),t=!0},i(l){t||(q(n.$$.fragment,l),t=!0)},o(l){D(n.$$.fragment,l),t=!1},d(l){l&&T(e),x(n)}}}function et(o){let e,n,t,l,i;return n=new ut({}),{c(){e=j("button"),Z(n.$$.fragment),y(e,"class","icon svelte-1aa1mud"),y(e,"aria-label","select input source")},m(r,_){H(r,e,_),ee(n,e,null),t=!0,l||(i=pe(e,"click",o[12]),l=!0)},p:Ae,i(r){t||(q(n.$$.fragment,r),t=!0)},o(r){D(n.$$.fragment,r),t=!1},d(r){r&&T(e),x(n),l=!1,i()}}}function tt(o){let e,n,t,l,i,r,_;t=new ut({});function c(s,m){return s[8].length===0?Yn:Xn}let g=c(o),a=g(o);return{c(){e=j("select"),n=j("button"),Z(t.$$.fragment),l=ie(),a.c(),y(n,"class","inset-icon svelte-1aa1mud"),y(e,"class","select-wrap svelte-1aa1mud"),y(e,"aria-label","select source")},m(s,m){H(s,e,m),F(e,n),ee(t,n,null),F(n,l),a.m(e,null),i=!0,r||(_=[pe(n,"click",Un(o[20])),En(Fe.call(null,e,o[14]))],r=!0)},p(s,m){g===(g=c(s))&&a?a.p(s,m):(a.d(1),a=g(s),a&&(a.c(),a.m(e,null)))},i(s){i||(q(t.$$.fragment,s),i=!0)},o(s){D(t.$$.fragment,s),i=!1},d(s){s&&T(e),x(t),a.d(),r=!1,Tn(_)}}}function Xn(o){let e,n=Ze(o[8]),t=[];for(let l=0;l<n.length;l+=1)t[l]=nt(xe(o,n,l));return{c(){for(let l=0;l<t.length;l+=1)t[l].c();e=ze()},m(l,i){for(let r=0;r<t.length;r+=1)t[r]&&t[r].m(l,i);H(l,e,i)},p(l,i){if(i[0]&8448){n=Ze(l[8]);let r;for(r=0;r<n.length;r+=1){const _=xe(l,n,r);t[r]?t[r].p(_,i):(t[r]=nt(_),t[r].c(),t[r].m(e.parentNode,e))}for(;r<t.length;r+=1)t[r].d(1);t.length=n.length}},d(l){l&&T(e),Nn(t,l)}}}function Yn(o){let e,n=o[3]("common.no_devices")+"",t;return{c(){e=j("option"),t=dt(n),e.__value="",He(e,e.__value),y(e,"class","svelte-1aa1mud")},m(l,i){H(l,e,i),F(e,t)},p(l,i){i[0]&8&&n!==(n=l[3]("common.no_devices")+"")&&ft(t,n)},d(l){l&&T(e)}}}function nt(o){let e,n=o[31].label+"",t,l,i,r,_;function c(){return o[21](o[31])}return{c(){e=j("option"),t=dt(n),l=ie(),e.__value=i=`
							`+o[31].label+`
						`,He(e,e.__value),y(e,"class","svelte-1aa1mud")},m(g,a){H(g,e,a),F(e,t),F(e,l),r||(_=pe(e,"click",c),r=!0)},p(g,a){o=g,a[0]&256&&n!==(n=o[31].label+"")&&ft(t,n),a[0]&256&&i!==(i=`
							`+o[31].label+`
						`)&&(e.__value=i,He(e,e.__value))},d(g){g&&T(e),r=!1,_()}}}function Gn(o){let e,n,t,l,i,r;const _=[zn,Ln],c=[];function g(a,s){return a[7]?1:0}return l=g(o),i=c[l]=_[l](o),{c(){e=j("div"),n=j("video"),t=ie(),i.c(),y(n,"class","svelte-1aa1mud"),ae(n,"flip",o[2]),ae(n,"hide",!o[7]),y(e,"class","wrap svelte-1aa1mud")},m(a,s){H(a,e,s),F(e,n),o[18](n),F(e,t),c[l].m(e,null),r=!0},p(a,s){(!r||s[0]&4)&&ae(n,"flip",a[2]),(!r||s[0]&128)&&ae(n,"hide",!a[7]);let m=l;l=g(a),l===m?c[l].p(a,s):(oe(),D(c[m],1,1,()=>{c[m]=null}),le(),i=c[l],i?i.p(a,s):(i=c[l]=_[l](a),i.c()),q(i,1),i.m(e,null))},i(a){r||(q(i),r=!0)},o(a){D(i),r=!1},d(a){a&&T(e),o[18](null),c[l].d()}}}function Fe(o,e){const n=t=>{o&&!o.contains(t.target)&&!t.defaultPrevented&&e(t)};return document.addEventListener("click",n,!0),{destroy(){document.removeEventListener("click",n,!0)}}}function Jn(o,e,n){let t,l,{streaming:i=!1}=e,{pending:r=!1}=e,{root:_=""}=e,{mode:c="image"}=e,{mirror_webcam:g}=e,{include_audio:a}=e,{i18n:s}=e;const m=Pn();jn(()=>l=document.createElement("canvas"));const h={width:{ideal:1920},height:{ideal:1440}};async function S(p){if(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia){m("error",s("image.no_webcam_support"));return}try{v=await navigator.mediaDevices.getUserMedia({video:p?{deviceId:{exact:p},...h}:h,audio:a}),n(5,t.srcObject=v,t),n(5,t.muted=!0,t),t.play(),n(7,k=!0)}catch(C){if(C instanceof DOMException&&C.name=="NotAllowedError")m("error",s("image.allow_webcam_access"));else throw C}}function u(){var p=l.getContext("2d");(!i||i&&b)&&t.videoWidth&&t.videoHeight&&(l.width=t.videoWidth,l.height=t.videoHeight,p.drawImage(t,0,0,t.videoWidth,t.videoHeight),g&&(p.scale(-1,1),p.drawImage(t,-t.videoWidth,0)),l.toBlob(C=>{m(i?"stream":"capture",C)},"image/png",.8))}let b=!1,w=[],v,B,E;function A(){if(b){E.stop();let p=new Blob(w,{type:B}),C=new FileReader;C.onload=async function($e){if($e.target){let ye=new File([p],"sample."+B.substring(6));const f=await vt([ye]);let G=(await kt(f,_))?.filter(Boolean)[0];m("capture",G),m("stop_recording")}},C.readAsDataURL(p)}else{m("start_recording"),w=[];let p=["video/webm","video/mp4"];for(let C of p)if(MediaRecorder.isTypeSupported(C)){B=C;break}if(B===null){console.error("No supported MediaRecorder mimeType");return}E=new MediaRecorder(v,{mimeType:B}),E.addEventListener("dataavailable",function(C){w.push(C.data)}),E.start(200)}n(6,b=!b)}let k=!1;function d(){c==="image"&&i&&n(6,b=!b),c==="image"?u():A(),!b&&v&&(v.getTracks().forEach(p=>p.stop()),n(5,t.srcObject=null,t),n(7,k=!1))}i&&c==="image"&&window.setInterval(()=>{t&&!r&&u()},500);async function $(){const p=await navigator.mediaDevices.enumerateDevices();n(8,O=p.filter(C=>C.kind==="videoinput")),n(9,L=!0)}let O=[];async function U(p){await S(p),n(9,L=!1)}let L=!1;function be(p){p.preventDefault(),p.stopPropagation(),n(9,L=!1)}function he(p){Mn[p?"unshift":"push"](()=>{t=p,n(5,t)})}const we=async()=>S(),ve=()=>n(9,L=!1),ke=p=>U(p.deviceId);return o.$$set=p=>{"streaming"in p&&n(0,i=p.streaming),"pending"in p&&n(15,r=p.pending),"root"in p&&n(16,_=p.root),"mode"in p&&n(1,c=p.mode),"mirror_webcam"in p&&n(2,g=p.mirror_webcam),"include_audio"in p&&n(17,a=p.include_audio),"i18n"in p&&n(3,s=p.i18n)},[i,c,g,s,Fe,t,b,k,O,L,S,d,$,U,be,r,_,a,he,we,ve,ke]}class Kn extends Dn{constructor(e){super(),Rn(this,e,Jn,Gn,Hn,{streaming:0,pending:15,root:16,mode:1,mirror_webcam:2,include_audio:17,i18n:3,click_outside:4},null,[-1,-1])}get click_outside(){return Fe}}const Qn=Kn;const{SvelteComponent:Zn,attr:xn,create_component:el,destroy_component:tl,detach:nl,element:ll,init:ol,insert:il,mount_component:rl,noop:al,safe_not_equal:sl,transition_in:cl,transition_out:ul}=window.__gradio__svelte__internal,{createEventDispatcher:_l}=window.__gradio__svelte__internal;function fl(o){let e,n,t;return n=new st({props:{Icon:It,label:"Remove Image"}}),n.$on("click",o[1]),{c(){e=ll("div"),el(n.$$.fragment),xn(e,"class","svelte-s6ybro")},m(l,i){il(l,e,i),rl(n,e,null),t=!0},p:al,i(l){t||(cl(n.$$.fragment,l),t=!0)},o(l){ul(n.$$.fragment,l),t=!1},d(l){l&&nl(e),tl(n)}}}function dl(o){const e=_l();return[e,t=>{e("remove_image"),t.stopPropagation()}]}class ml extends Zn{constructor(e){super(),ol(this,e,dl,fl,sl,{})}}const{SvelteComponent:gl,add_flush_callback:Ue,append:se,attr:ue,bind:Pe,binding_callbacks:de,bubble:Be,check_outros:_e,create_component:J,create_slot:pl,destroy_component:K,detach:me,element:je,empty:bl,get_all_dirty_from_scope:hl,get_slot_changes:wl,group_outros:fe,init:vl,insert:ge,listen:kl,mount_component:Q,noop:$l,safe_not_equal:yl,space:ce,toggle_class:lt,transition_in:I,transition_out:W,update_slot_base:Sl}=window.__gradio__svelte__internal,{createEventDispatcher:Il,tick:ql}=window.__gradio__svelte__internal;function ot(o){let e,n;return e=new ml({}),e.$on("remove_image",o[22]),{c(){J(e.$$.fragment)},m(t,l){Q(e,t,l),n=!0},p:$l,i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){W(e.$$.fragment,t),n=!1},d(t){K(e,t)}}}function it(o){let e;const n=o[21].default,t=pl(n,o,o[33],null);return{c(){t&&t.c()},m(l,i){t&&t.m(l,i),e=!0},p(l,i){t&&t.p&&(!e||i[1]&4)&&Sl(t,n,l,l[33],e?wl(n,l[33],i,null):hl(l[33]),null)},i(l){e||(I(t,l),e=!0)},o(l){W(t,l),e=!1},d(l){t&&t.d(l)}}}function Cl(o){let e,n,t=o[1]===null&&it(o);return{c(){t&&t.c(),e=bl()},m(l,i){t&&t.m(l,i),ge(l,e,i),n=!0},p(l,i){l[1]===null?t?(t.p(l,i),i[0]&2&&I(t,1)):(t=it(l),t.c(),I(t,1),t.m(e.parentNode,e)):t&&(fe(),W(t,1,1,()=>{t=null}),_e())},i(l){n||(I(t),n=!0)},o(l){W(t),n=!1},d(l){l&&me(e),t&&t.d(l)}}}function Dl(o){let e,n,t,l,i;return n=new ct({props:{src:o[1].url,alt:o[1].alt_text}}),{c(){e=je("div"),J(n.$$.fragment),ue(e,"class","image-frame svelte-rrgd5g"),lt(e,"selectable",o[7])},m(r,_){ge(r,e,_),Q(n,e,null),t=!0,l||(i=kl(e,"click",o[18]),l=!0)},p(r,_){const c={};_[0]&2&&(c.src=r[1].url),_[0]&2&&(c.alt=r[1].alt_text),n.$set(c),(!t||_[0]&128)&&lt(e,"selectable",r[7])},i(r){t||(I(n.$$.fragment,r),t=!0)},o(r){W(n.$$.fragment,r),t=!1},d(r){r&&me(e),K(n),l=!1,i()}}}function El(o){let e,n;return e=new Qn({props:{root:o[8],mirror_webcam:o[6],streaming:o[5],mode:"image",include_audio:!1,i18n:o[9]}}),e.$on("capture",o[27]),e.$on("stream",o[28]),e.$on("error",o[29]),e.$on("drag",o[30]),e.$on("upload",o[31]),{c(){J(e.$$.fragment)},m(t,l){Q(e,t,l),n=!0},p(t,l){const i={};l[0]&256&&(i.root=t[8]),l[0]&64&&(i.mirror_webcam=t[6]),l[0]&32&&(i.streaming=t[5]),l[0]&512&&(i.i18n=t[9]),e.$set(i)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){W(e.$$.fragment,t),n=!1},d(t){K(e,t)}}}function rt(o){let e,n,t;function l(r){o[32](r)}let i={sources:o[4],handle_clear:o[15],handle_select:o[19]};return o[0]!==void 0&&(i.active_source=o[0]),e=new yt({props:i}),de.push(()=>Pe(e,"active_source",l)),{c(){J(e.$$.fragment)},m(r,_){Q(e,r,_),t=!0},p(r,_){const c={};_[0]&16&&(c.sources=r[4]),!n&&_[0]&1&&(n=!0,c.active_source=r[0],Ue(()=>n=!1)),e.$set(c)},i(r){t||(I(e.$$.fragment,r),t=!0)},o(r){W(e.$$.fragment,r),t=!1},d(r){K(e,r)}}}function Wl(o){let e,n,t,l,i,r,_,c,g,a,s,m,h=o[4].length>1||o[4].includes("clipboard"),S;e=new at({props:{show_label:o[3],Icon:Le,label:o[2]||"Image"}});let u=o[1]?.url&&!o[12]&&ot(o);function b(d){o[24](d)}function w(d){o[25](d)}let v={hidden:o[1]!==null||o[0]==="webcam",filetype:o[0]==="clipboard"?"clipboard":"image/*",root:o[8],disable_click:!o[4].includes("upload"),$$slots:{default:[Cl]},$$scope:{ctx:o}};o[10]!==void 0&&(v.uploading=o[10]),o[11]!==void 0&&(v.dragging=o[11]),r=new St({props:v}),o[23](r),de.push(()=>Pe(r,"uploading",b)),de.push(()=>Pe(r,"dragging",w)),r.$on("load",o[14]),r.$on("error",o[26]);const B=[El,Dl],E=[];function A(d,$){return d[0]==="webcam"&&(d[5]||!d[5]&&!d[1])?0:d[1]!==null&&!d[5]?1:-1}~(a=A(o))&&(s=E[a]=B[a](o));let k=h&&rt(o);return{c(){J(e.$$.fragment),n=ce(),t=je("div"),u&&u.c(),l=ce(),i=je("div"),J(r.$$.fragment),g=ce(),s&&s.c(),m=ce(),k&&k.c(),ue(i,"class","upload-container svelte-rrgd5g"),ue(t,"data-testid","image"),ue(t,"class","image-container svelte-rrgd5g")},m(d,$){Q(e,d,$),ge(d,n,$),ge(d,t,$),u&&u.m(t,null),se(t,l),se(t,i),Q(r,i,null),se(i,g),~a&&E[a].m(i,null),se(t,m),k&&k.m(t,null),S=!0},p(d,$){const O={};$[0]&8&&(O.show_label=d[3]),$[0]&4&&(O.label=d[2]||"Image"),e.$set(O),d[1]?.url&&!d[12]?u?(u.p(d,$),$[0]&4098&&I(u,1)):(u=ot(d),u.c(),I(u,1),u.m(t,l)):u&&(fe(),W(u,1,1,()=>{u=null}),_e());const U={};$[0]&3&&(U.hidden=d[1]!==null||d[0]==="webcam"),$[0]&1&&(U.filetype=d[0]==="clipboard"?"clipboard":"image/*"),$[0]&256&&(U.root=d[8]),$[0]&16&&(U.disable_click=!d[4].includes("upload")),$[0]&2|$[1]&4&&(U.$$scope={dirty:$,ctx:d}),!_&&$[0]&1024&&(_=!0,U.uploading=d[10],Ue(()=>_=!1)),!c&&$[0]&2048&&(c=!0,U.dragging=d[11],Ue(()=>c=!1)),r.$set(U);let L=a;a=A(d),a===L?~a&&E[a].p(d,$):(s&&(fe(),W(E[L],1,1,()=>{E[L]=null}),_e()),~a?(s=E[a],s?s.p(d,$):(s=E[a]=B[a](d),s.c()),I(s,1),s.m(i,null)):s=null),$[0]&16&&(h=d[4].length>1||d[4].includes("clipboard")),h?k?(k.p(d,$),$[0]&16&&I(k,1)):(k=rt(d),k.c(),I(k,1),k.m(t,null)):k&&(fe(),W(k,1,1,()=>{k=null}),_e())},i(d){S||(I(e.$$.fragment,d),I(u),I(r.$$.fragment,d),I(s),I(k),S=!0)},o(d){W(e.$$.fragment,d),W(u),W(r.$$.fragment,d),W(s),W(k),S=!1},d(d){d&&(me(n),me(t)),K(e,d),u&&u.d(),o[23](null),K(r),~a&&E[a].d(),k&&k.d()}}}function Ml(o,e,n){let t,{$$slots:l={},$$scope:i}=e,{value:r}=e,{label:_=void 0}=e,{show_label:c}=e,{sources:g=["upload","clipboard","webcam"]}=e,{streaming:a=!1}=e,{pending:s=!1}=e,{mirror_webcam:m}=e,{selectable:h=!1}=e,{root:S}=e,{i18n:u}=e,b,w=!1,{active_source:v=null}=e;function B({detail:f}){n(1,r=f),k("upload")}function E(){n(1,r=null),k("clear"),k("change",null)}async function A(f){n(20,s=!0);const G=await b.load_files([new File([f],"webcam.png")]);n(1,r=G?.[0]||null),await ql(),k(a?"stream":"change"),n(20,s=!1)}const k=Il();let d=!1;function $(f){let G=_t(f);G&&k("select",{index:G,value:null})}async function O(f){switch(f){case"clipboard":b.paste_clipboard();break}}const U=()=>{n(1,r=null),k("clear")};function L(f){de[f?"unshift":"push"](()=>{b=f,n(13,b)})}function be(f){w=f,n(10,w)}function he(f){d=f,n(11,d)}function we(f){Be.call(this,o,f)}const ve=f=>A(f.detail),ke=f=>A(f.detail);function p(f){Be.call(this,o,f)}function C(f){Be.call(this,o,f)}const $e=f=>A(f.detail);function ye(f){v=f,n(0,v),n(4,g)}return o.$$set=f=>{"value"in f&&n(1,r=f.value),"label"in f&&n(2,_=f.label),"show_label"in f&&n(3,c=f.show_label),"sources"in f&&n(4,g=f.sources),"streaming"in f&&n(5,a=f.streaming),"pending"in f&&n(20,s=f.pending),"mirror_webcam"in f&&n(6,m=f.mirror_webcam),"selectable"in f&&n(7,h=f.selectable),"root"in f&&n(8,S=f.root),"i18n"in f&&n(9,u=f.i18n),"active_source"in f&&n(0,v=f.active_source),"$$scope"in f&&n(33,i=f.$$scope)},o.$$.update=()=>{o.$$.dirty[0]&17&&!v&&g&&n(0,v=g[0]),o.$$.dirty[0]&33&&n(12,t=a&&v==="webcam"),o.$$.dirty[0]&5120&&w&&!t&&n(1,r=null),o.$$.dirty[0]&2048&&k("drag",d)},[v,r,_,c,g,a,m,h,S,u,w,d,t,b,B,E,A,k,$,O,s,l,U,L,be,he,we,ve,ke,p,C,$e,ye,i]}class Bl extends gl{constructor(e){super(),vl(this,e,Ml,Wl,yl,{value:1,label:2,show_label:3,sources:4,streaming:5,pending:20,mirror_webcam:6,selectable:7,root:8,i18n:9,active_source:0},null,[-1,-1])}}const Jl=Bl;export{Jl as I,Gl as S,Qn as W};
//# sourceMappingURL=ImageUploader-528d6ef1.js.map
