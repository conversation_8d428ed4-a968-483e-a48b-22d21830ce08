import E from"./Index-09f26e4b.js";import{T as O}from"./Textbox-1709c01b.js";import{a as G,B as S}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{$ as J}from"./Index-26cfc80a.js";/* empty css                                              */import"./index-a80d931b.js";import K from"./Index-ab6a99fa.js";import"./BlockTitle-7f7c9ef8.js";import"./Info-84f5385d.js";import"./Check-965babbe.js";import"./Copy-b365948f.js";import"./svelte/svelte.js";const{SvelteComponent:Q,add_flush_callback:P,append:H,attr:L,bind:j,binding_callbacks:A,component_subscribe:R,create_component:v,destroy_component:w,detach:c,element:B,init:U,insert:b,mount_component:k,safe_not_equal:V,set_data:N,space:x,text:D,toggle_class:q,transition_in:h,transition_out:T}=window.__gradio__svelte__internal;function z(f){let e;return{c(){e=B("p"),L(e,"class","auth svelte-1ogxbi0")},m(t,n){b(t,e,n),e.innerHTML=f[0]},p(t,n){n&1&&(e.innerHTML=t[0])},d(t){t&&c(e)}}}function C(f){let e,t=f[6]("login.enable_cookies")+"",n;return{c(){e=B("p"),n=D(t),L(e,"class","auth svelte-1ogxbi0")},m(s,l){b(s,e,l),H(e,n)},p(s,l){l&64&&t!==(t=s[6]("login.enable_cookies")+"")&&N(n,t)},d(s){s&&c(e)}}}function F(f){let e,t=f[6]("login.incorrect_credentials")+"",n;return{c(){e=B("p"),n=D(t),L(e,"class","creds svelte-1ogxbi0")},m(s,l){b(s,e,l),H(e,n)},p(s,l){l&64&&t!==(t=s[6]("login.incorrect_credentials")+"")&&N(n,t)},d(s){s&&c(e)}}}function W(f){let e,t,n;function s(o){f[9](o)}let l={label:"username",lines:1,show_label:!0,max_lines:1};return f[3]!==void 0&&(l.value=f[3]),e=new O({props:l}),A.push(()=>j(e,"value",s)),e.$on("submit",f[7]),{c(){v(e.$$.fragment)},m(o,_){k(e,o,_),n=!0},p(o,_){const a={};!t&&_&8&&(t=!0,a.value=o[3],P(()=>t=!1)),e.$set(a)},i(o){n||(h(e.$$.fragment,o),n=!0)},o(o){T(e.$$.fragment,o),n=!1},d(o){w(e,o)}}}function X(f){let e,t,n;function s(o){f[10](o)}let l={label:"password",lines:1,show_label:!0,max_lines:1,type:"password"};return f[4]!==void 0&&(l.value=f[4]),e=new O({props:l}),A.push(()=>j(e,"value",s)),e.$on("submit",f[7]),{c(){v(e.$$.fragment)},m(o,_){k(e,o,_),n=!0},p(o,_){const a={};!t&&_&16&&(t=!0,a.value=o[4],P(()=>t=!1)),e.$set(a)},i(o){n||(h(e.$$.fragment,o),n=!0)},o(o){T(e.$$.fragment,o),n=!1},d(o){w(e,o)}}}function Y(f){let e,t,n,s;return e=new S({props:{$$slots:{default:[W]},$$scope:{ctx:f}}}),n=new S({props:{$$slots:{default:[X]},$$scope:{ctx:f}}}),{c(){v(e.$$.fragment),t=x(),v(n.$$.fragment)},m(l,o){k(e,l,o),b(l,t,o),k(n,l,o),s=!0},p(l,o){const _={};o&2056&&(_.$$scope={dirty:o,ctx:l}),e.$set(_);const a={};o&2064&&(a.$$scope={dirty:o,ctx:l}),n.$set(a)},i(l){s||(h(e.$$.fragment,l),h(n.$$.fragment,l),s=!0)},o(l){T(e.$$.fragment,l),T(n.$$.fragment,l),s=!1},d(l){l&&c(t),w(e,l),w(n,l)}}}function Z(f){let e=f[6]("login.login")+"",t;return{c(){t=D(e)},m(n,s){b(n,t,s)},p(n,s){s&64&&e!==(e=n[6]("login.login")+"")&&N(t,e)},d(n){n&&c(t)}}}function y(f){let e,t=f[6]("login.login")+"",n,s,l,o,_,a,d,$,g,m=f[0]&&z(f),p=f[2]&&C(f),r=f[5]&&F(f);return a=new E({props:{$$slots:{default:[Y]},$$scope:{ctx:f}}}),$=new G({props:{size:"lg",variant:"primary",$$slots:{default:[Z]},$$scope:{ctx:f}}}),$.$on("click",f[7]),{c(){e=B("h2"),n=D(t),s=x(),m&&m.c(),l=x(),p&&p.c(),o=x(),r&&r.c(),_=x(),v(a.$$.fragment),d=x(),v($.$$.fragment),L(e,"class","svelte-1ogxbi0")},m(i,u){b(i,e,u),H(e,n),b(i,s,u),m&&m.m(i,u),b(i,l,u),p&&p.m(i,u),b(i,o,u),r&&r.m(i,u),b(i,_,u),k(a,i,u),b(i,d,u),k($,i,u),g=!0},p(i,u){(!g||u&64)&&t!==(t=i[6]("login.login")+"")&&N(n,t),i[0]?m?m.p(i,u):(m=z(i),m.c(),m.m(l.parentNode,l)):m&&(m.d(1),m=null),i[2]?p?p.p(i,u):(p=C(i),p.c(),p.m(o.parentNode,o)):p&&(p.d(1),p=null),i[5]?r?r.p(i,u):(r=F(i),r.c(),r.m(_.parentNode,_)):r&&(r.d(1),r=null);const I={};u&2072&&(I.$$scope={dirty:u,ctx:i}),a.$set(I);const M={};u&2112&&(M.$$scope={dirty:u,ctx:i}),$.$set(M)},i(i){g||(h(a.$$.fragment,i),h($.$$.fragment,i),g=!0)},o(i){T(a.$$.fragment,i),T($.$$.fragment,i),g=!1},d(i){i&&(c(e),c(s),c(l),c(o),c(_),c(d)),m&&m.d(i),p&&p.d(i),r&&r.d(i),w(a,i),w($,i)}}}function ee(f){let e,t,n;return t=new K({props:{variant:"panel",min_width:480,$$slots:{default:[y]},$$scope:{ctx:f}}}),{c(){e=B("div"),v(t.$$.fragment),L(e,"class","wrap svelte-1ogxbi0"),q(e,"min-h-screen",f[1])},m(s,l){b(s,e,l),k(t,e,null),n=!0},p(s,[l]){const o={};l&2173&&(o.$$scope={dirty:l,ctx:s}),t.$set(o),(!n||l&2)&&q(e,"min-h-screen",s[1])},i(s){n||(h(t.$$.fragment,s),n=!0)},o(s){T(t.$$.fragment,s),n=!1},d(s){s&&c(e),w(t)}}}function te(f,e,t){let n;R(f,J,r=>t(6,n=r));let{root:s}=e,{auth_message:l}=e,{app_mode:o}=e,{space_id:_}=e,a="",d="",$=!1;const g=async()=>{const r=new FormData;r.append("username",a),r.append("password",d);let i=await fetch(s+"/login",{method:"POST",body:r});i.status===400?(t(5,$=!0),t(3,a=""),t(4,d="")):i.status==200&&location.reload()};function m(r){a=r,t(3,a)}function p(r){d=r,t(4,d)}return f.$$set=r=>{"root"in r&&t(8,s=r.root),"auth_message"in r&&t(0,l=r.auth_message),"app_mode"in r&&t(1,o=r.app_mode),"space_id"in r&&t(2,_=r.space_id)},[l,o,_,a,d,$,n,g,s,m,p]}class ce extends Q{constructor(e){super(),U(this,e,te,ee,V,{root:8,auth_message:0,app_mode:1,space_id:2})}}export{ce as default};
//# sourceMappingURL=Login-008fa4c1.js.map
