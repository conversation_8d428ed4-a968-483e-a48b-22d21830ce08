#!/usr/bin/env python3
"""
Ollama Performance Optimization Script
Optimizes Ollama settings for faster text generation on RTX 4060
"""

import subprocess
import os
import sys

def run_command(command):
    """Run a command and return the result"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def optimize_ollama_settings():
    """Apply optimizations to Ollama for faster performance"""
    
    print("🚀 Optimizing Ollama for RTX 4060...")
    
    # Set environment variables for better performance
    optimizations = {
        'OLLAMA_NUM_PARALLEL': '1',  # Single parallel request for stability
        'OLLAMA_MAX_LOADED_MODELS': '1',  # Keep only one model loaded
        'OLLAMA_FLASH_ATTENTION': 'true',  # Enable flash attention if available
        'OLLAMA_GPU_OVERHEAD': '0',  # Minimize GPU overhead
        'OLLAMA_CONTEXT_LENGTH': '2048',  # Reduced context for speed
        'CUDA_VISIBLE_DEVICES': '0',  # Use first GPU only
    }
    
    print("Setting environment variables...")
    for key, value in optimizations.items():
        os.environ[key] = value
        print(f"  {key}={value}")
    
    # Create a batch file for Windows to set these permanently
    batch_content = """@echo off
echo Setting Ollama optimizations for RTX 4060...
set OLLAMA_NUM_PARALLEL=1
set OLLAMA_MAX_LOADED_MODELS=1
set OLLAMA_FLASH_ATTENTION=true
set OLLAMA_GPU_OVERHEAD=0
set OLLAMA_CONTEXT_LENGTH=2048
set CUDA_VISIBLE_DEVICES=0
echo Optimizations applied!
"""
    
    with open("optimize_ollama.bat", "w") as f:
        f.write(batch_content)
    
    print("✅ Created optimize_ollama.bat for permanent settings")
    
    # Test Ollama connection
    print("\n🔍 Testing Ollama connection...")
    success, stdout, stderr = run_command('& "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama\\ollama.exe" list')
    
    if success:
        print("✅ Ollama is accessible")
        print("Available models:")
        print(stdout)
    else:
        print("❌ Ollama connection failed:")
        print(stderr)
        return False
    
    # Check if our model is available
    if 'kristada673/solar-10.7b-instruct-v1.0-uncensored' in stdout:
        print("✅ Solar 10.7B uncensored model is available")
    else:
        print("⚠️  Solar 10.7B model not found in list")
    
    return True

def create_performance_test():
    """Create a simple performance test script"""
    
    test_script = '''
import time
import requests
import json

def test_ollama_performance():
    """Test Ollama response time"""
    
    url = "http://localhost:11434/api/generate"
    
    payload = {
        "model": "kristada673/solar-10.7b-instruct-v1.0-uncensored",
        "prompt": "Hello, how are you?",
        "stream": False,
        "options": {
            "num_ctx": 2048,
            "num_predict": 100,
            "temperature": 0.7,
            "top_k": 40,
            "top_p": 0.9
        }
    }
    
    print("🧪 Testing Ollama performance...")
    start_time = time.time()
    
    try:
        response = requests.post(url, json=payload, timeout=60)
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            response_time = end_time - start_time
            
            print(f"✅ Response received in {response_time:.2f} seconds")
            print(f"📝 Response: {data.get('response', 'No response')[:100]}...")
            
            if response_time < 10:
                print("🚀 Excellent performance!")
            elif response_time < 30:
                print("✅ Good performance")
            else:
                print("⚠️  Slow performance - consider optimizations")
                
        else:
            print(f"❌ Error: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    test_ollama_performance()
'''
    
    with open("test_performance.py", "w") as f:
        f.write(test_script)
    
    print("✅ Created test_performance.py")

def main():
    """Main optimization function"""
    
    print("=" * 60)
    print("🔥 CHARLIE UNCENSORED - OLLAMA OPTIMIZER")
    print("=" * 60)
    
    # Apply optimizations
    if optimize_ollama_settings():
        print("\n✅ Optimizations applied successfully!")
    else:
        print("\n❌ Some optimizations failed")
        return False
    
    # Create performance test
    create_performance_test()
    
    print("\n" + "=" * 60)
    print("📋 NEXT STEPS:")
    print("=" * 60)
    print("1. Run 'optimize_ollama.bat' to set permanent environment variables")
    print("2. Restart Ollama server for optimizations to take effect")
    print("3. Run 'python test_performance.py' to test performance")
    print("4. Run 'python enhanced_app.py' to start the enhanced chatbot")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    main()
