{"version": 3, "file": "IconButton-7294c90b.js", "sources": ["../../../../js/atoms/src/IconButton.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { type ComponentType } from \"svelte\";\n\texport let Icon: ComponentType;\n\texport let label = \"\";\n\texport let show_label = false;\n\texport let pending = false;\n\texport let size: \"small\" | \"large\" = \"small\";\n\texport let padded = true;\n\texport let highlight = false;\n\texport let disabled = false;\n\texport let hasPopup = false;\n\texport let color = \"var(--block-label-text-color)\";\n\texport let transparent = false;\n\texport let background = \"var(--background-fill-primary)\";\n\t$: _color = highlight ? \"var(--color-accent)\" : color;\n</script>\n\n<button\n\t{disabled}\n\ton:click\n\taria-label={label}\n\taria-haspopup={hasPopup}\n\ttitle={label}\n\tclass:pending\n\tclass:padded\n\tclass:highlight\n\tclass:transparent\n\tstyle:color={!disabled && _color ? _color : \"var(--block-label-text-color)\"}\n\tstyle:--bg-color={!disabled ? background : \"auto\"}\n>\n\t{#if show_label}<span>{label}</span>{/if}\n\t<div class:small={size === \"small\"} class:large={size === \"large\"}>\n\t\t<Icon />\n\t</div>\n</button>\n\n<style>\n\tbutton {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tgap: 1px;\n\t\tz-index: var(--layer-2);\n\t\t/* background: var(--background-fill-primary); */\n\t\tborder-radius: var(--radius-sm);\n\t\tcolor: var(--block-label-text-color);\n\t\tborder: 1px solid transparent;\n\t}\n\n\tbutton[disabled] {\n\t\topacity: 0.5;\n\t\tbox-shadow: none;\n\t}\n\n\tbutton[disabled]:hover {\n\t\tcursor: not-allowed;\n\t\t/* border: 1px solid var(--button-secondary-border-color); */\n\t\t/* padding: 2px; */\n\t}\n\n\t.padded {\n\t\tpadding: 2px;\n\t\tbackground: var(--bg-color);\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: 1px solid var(--button-secondary-border-color);\n\t}\n\n\t/* .padded {\n\t\tpadding: 2px;\n\t\tbackground: var(--background-fill-primary);\n\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: 1px solid var(--button-secondary-border-color);\n\t} */\n\n\t/* .padded {\n\t\tpadding: 2px;\n\t\tbackground: var(--background-fill-primary);\n\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: 1px solid var(--button-secondary-border-color);\n\t} */\n\n\tbutton:hover,\n\tbutton.highlight {\n\t\tcursor: pointer;\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.padded:hover {\n\t\tborder: 2px solid var(--button-secondary-border-color-hover);\n\t\tpadding: 1px;\n\t\tcolor: var(--block-label-text-color);\n\t}\n\n\tspan {\n\t\tpadding: 0px 1px;\n\t\tfont-size: 10px;\n\t}\n\n\tdiv {\n\t\tpadding: 2px;\n\t\tdisplay: flex;\n\t\talign-items: flex-end;\n\t}\n\n\t.small {\n\t\twidth: 14px;\n\t\theight: 14px;\n\t}\n\n\t.large {\n\t\twidth: 22px;\n\t\theight: 22px;\n\t}\n\n\t.pending {\n\t\tanimation: flash 0.5s infinite;\n\t}\n\n\t@keyframes flash {\n\t\t0% {\n\t\t\topacity: 0.5;\n\t\t}\n\t\t50% {\n\t\t\topacity: 1;\n\t\t}\n\t\t100% {\n\t\t\topacity: 0.5;\n\t\t}\n\t}\n\n\t.transparent {\n\t\tbackground: transparent;\n\t\tborder: none;\n\t\tbox-shadow: none;\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "span", "anchor", "create_if_block", "toggle_class", "div", "set_style", "button", "append", "Icon", "$$props", "label", "show_label", "pending", "size", "padded", "highlight", "disabled", "<PERSON><PERSON><PERSON><PERSON>", "color", "transparent", "background", "_color"], "mappings": "kZA8BwBA,EAAK,CAAA,CAAA,uCAAZC,EAAoBC,EAAAC,EAAAC,CAAA,0BAAbJ,EAAK,CAAA,CAAA,oDAAvBA,EAAU,CAAA,GAAAK,EAAAL,CAAA,mHACGM,EAAAC,EAAA,QAAAP,OAAS,OAAO,EAAeM,EAAAC,EAAA,QAAAP,OAAS,OAAO,mCAXrDA,EAAK,CAAA,CAAA,sBACFA,EAAQ,CAAA,CAAA,cAChBA,EAAK,CAAA,CAAA,oHAKEQ,EAAAC,EAAA,QAAA,CAAAT,MAAYA,EAAM,EAAA,EAAGA,EAAM,EAAA,EAAG,+BAA+B,mBACxDA,EAAQ,CAAA,EAAgB,OAAbA,EAAU,EAAA,CAAS,UAXlDC,EAiBQC,EAAAO,EAAAL,CAAA,wBAHPM,EAEKD,EAAAF,CAAA,4DAHAP,EAAU,CAAA,oEACGM,EAAAC,EAAA,QAAAP,OAAS,OAAO,cAAeM,EAAAC,EAAA,QAAAP,OAAS,OAAO,6DAXrDA,EAAK,CAAA,CAAA,mCACFA,EAAQ,CAAA,CAAA,yBAChBA,EAAK,CAAA,CAAA,+IAKEQ,EAAAC,EAAA,QAAA,CAAAT,MAAYA,EAAM,EAAA,EAAGA,EAAM,EAAA,EAAG,+BAA+B,2BACxDA,EAAQ,CAAA,EAAgB,OAAbA,EAAU,EAAA,CAAS,mIA1BtC,KAAAW,CAAmB,EAAAC,EACnB,CAAA,MAAAC,EAAQ,EAAE,EAAAD,EACV,CAAA,WAAAE,EAAa,EAAK,EAAAF,EAClB,CAAA,QAAAG,EAAU,EAAK,EAAAH,EACf,CAAA,KAAAI,EAA0B,OAAO,EAAAJ,EACjC,CAAA,OAAAK,EAAS,EAAI,EAAAL,EACb,CAAA,UAAAM,EAAY,EAAK,EAAAN,EACjB,CAAA,SAAAO,EAAW,EAAK,EAAAP,EAChB,CAAA,SAAAQ,EAAW,EAAK,EAAAR,EAChB,CAAA,MAAAS,EAAQ,+BAA+B,EAAAT,EACvC,CAAA,YAAAU,EAAc,EAAK,EAAAV,EACnB,CAAA,WAAAW,EAAa,gCAAgC,EAAAX,oeACrDY,EAASN,EAAY,sBAAwBG,CAAK"}