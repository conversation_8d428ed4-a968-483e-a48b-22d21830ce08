{"name": "@gradio/image", "version": "0.9.10", "description": "Gradio UI packages", "type": "module", "author": "", "license": "ISC", "private": false, "dependencies": {"@gradio/atoms": "workspace:^", "@gradio/client": "workspace:^", "@gradio/icons": "workspace:^", "@gradio/statustracker": "workspace:^", "@gradio/upload": "workspace:^", "@gradio/utils": "workspace:^", "@gradio/wasm": "workspace:^", "cropperjs": "^1.5.12", "lazy-brush": "^1.0.1", "resize-observer-polyfill": "^1.5.1"}, "main_changeset": true, "main": "./Index.svelte", "exports": {".": "./Index.svelte", "./shared": "./shared/index.ts", "./example": "./Example.svelte", "./package.json": "./package.json"}}