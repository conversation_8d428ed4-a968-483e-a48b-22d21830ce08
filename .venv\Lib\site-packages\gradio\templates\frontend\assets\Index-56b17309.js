import"./index-a80d931b.js";import{a as E}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import"./svelte/svelte.js";import"./Index-26cfc80a.js";const{SvelteComponent:j,attr:g,create_component:I,create_slot:N,destroy_component:A,detach:C,element:F,get_all_dirty_from_scope:G,get_slot_changes:H,init:J,insert:q,mount_component:K,safe_not_equal:L,space:M,src_url_equal:k,transition_in:D,transition_out:S,update_slot_base:O}=window.__gradio__svelte__internal,{createEventDispatcher:P}=window.__gradio__svelte__internal;function B(a){let i,n,e;return{c(){i=F("img"),g(i,"class","button-icon svelte-yjn27e"),k(i.src,n=a[6].url)||g(i,"src",n),g(i,"alt",e=`${a[5]} icon`)},m(l,t){q(l,i,t)},p(l,t){t&64&&!k(i.src,n=l[6].url)&&g(i,"src",n),t&32&&e!==(e=`${l[5]} icon`)&&g(i,"alt",e)},d(l){l&&C(i)}}}function Q(a){let i,n,e=a[6]&&B(a);const l=a[11].default,t=N(l,a,a[12],null);return{c(){e&&e.c(),i=M(),t&&t.c()},m(f,u){e&&e.m(f,u),q(f,i,u),t&&t.m(f,u),n=!0},p(f,u){f[6]?e?e.p(f,u):(e=B(f),e.c(),e.m(i.parentNode,i)):e&&(e.d(1),e=null),t&&t.p&&(!n||u&4096)&&O(t,l,f,f[12],n?H(l,f[12],u,null):G(f[12]),null)},i(f){n||(D(t,f),n=!0)},o(f){S(t,f),n=!1},d(f){f&&C(i),e&&e.d(f),t&&t.d(f)}}}function R(a){let i,n;return i=new E({props:{size:a[4],variant:a[3],elem_id:a[0],elem_classes:a[1],visible:a[2],scale:a[8],min_width:a[9],disabled:a[7],$$slots:{default:[Q]},$$scope:{ctx:a}}}),i.$on("click",a[10]),{c(){I(i.$$.fragment)},m(e,l){K(i,e,l),n=!0},p(e,[l]){const t={};l&16&&(t.size=e[4]),l&8&&(t.variant=e[3]),l&1&&(t.elem_id=e[0]),l&2&&(t.elem_classes=e[1]),l&4&&(t.visible=e[2]),l&256&&(t.scale=e[8]),l&512&&(t.min_width=e[9]),l&128&&(t.disabled=e[7]),l&4192&&(t.$$scope={dirty:l,ctx:e}),i.$set(t)},i(e){n||(D(i.$$.fragment,e),n=!0)},o(e){S(i.$$.fragment,e),n=!1},d(e){A(i,e)}}}function T(a,i,n){let{$$slots:e={},$$scope:l}=i,{elem_id:t=""}=i,{elem_classes:f=[]}=i,{visible:u=!0}=i,{variant:m="secondary"}=i,{size:d="lg"}=i,{value:o}=i,{icon:r}=i,{disabled:b=!1}=i,{scale:v=null}=i,{min_width:c=void 0}=i;const h=P();function _(){if(h("click"),!o?.url)return;let s;if(!o.orig_name&&o.url){const z=o.url.split("/");s=z[z.length-1],s=s.split("?")[0].split("#")[0]}else s=o.orig_name;const w=document.createElement("a");w.href=o.url,w.download=s||"file",document.body.appendChild(w),w.click(),document.body.removeChild(w)}return a.$$set=s=>{"elem_id"in s&&n(0,t=s.elem_id),"elem_classes"in s&&n(1,f=s.elem_classes),"visible"in s&&n(2,u=s.visible),"variant"in s&&n(3,m=s.variant),"size"in s&&n(4,d=s.size),"value"in s&&n(5,o=s.value),"icon"in s&&n(6,r=s.icon),"disabled"in s&&n(7,b=s.disabled),"scale"in s&&n(8,v=s.scale),"min_width"in s&&n(9,c=s.min_width),"$$scope"in s&&n(12,l=s.$$scope)},[t,f,u,m,d,o,r,b,v,c,_,e,l]}class U extends j{constructor(i){super(),J(this,i,T,R,L,{elem_id:0,elem_classes:1,visible:2,variant:3,size:4,value:5,icon:6,disabled:7,scale:8,min_width:9})}}const V=U,{SvelteComponent:W,create_component:X,destroy_component:Y,detach:Z,init:y,insert:x,mount_component:p,safe_not_equal:$,set_data:ee,text:ie,transition_in:ne,transition_out:le}=window.__gradio__svelte__internal;function te(a){let i=(a[10]?a[11].i18n(a[10]):"")+"",n;return{c(){n=ie(i)},m(e,l){x(e,n,l)},p(e,l){l&3072&&i!==(i=(e[10]?e[11].i18n(e[10]):"")+"")&&ee(n,i)},d(e){e&&Z(n)}}}function ae(a){let i,n;return i=new V({props:{value:a[3],variant:a[4],elem_id:a[0],elem_classes:a[1],size:a[6],scale:a[7],icon:a[8],min_width:a[9],visible:a[2],disabled:!a[5],$$slots:{default:[te]},$$scope:{ctx:a}}}),i.$on("click",a[12]),{c(){X(i.$$.fragment)},m(e,l){p(i,e,l),n=!0},p(e,[l]){const t={};l&8&&(t.value=e[3]),l&16&&(t.variant=e[4]),l&1&&(t.elem_id=e[0]),l&2&&(t.elem_classes=e[1]),l&64&&(t.size=e[6]),l&128&&(t.scale=e[7]),l&256&&(t.icon=e[8]),l&512&&(t.min_width=e[9]),l&4&&(t.visible=e[2]),l&32&&(t.disabled=!e[5]),l&11264&&(t.$$scope={dirty:l,ctx:e}),i.$set(t)},i(e){n||(ne(i.$$.fragment,e),n=!0)},o(e){le(i.$$.fragment,e),n=!1},d(e){Y(i,e)}}}function se(a,i,n){let{elem_id:e=""}=i,{elem_classes:l=[]}=i,{visible:t=!0}=i,{value:f}=i,{variant:u="secondary"}=i,{interactive:m}=i,{size:d="lg"}=i,{scale:o=null}=i,{icon:r=null}=i,{min_width:b=void 0}=i,{label:v=null}=i,{gradio:c}=i;const h=()=>c.dispatch("click");return a.$$set=_=>{"elem_id"in _&&n(0,e=_.elem_id),"elem_classes"in _&&n(1,l=_.elem_classes),"visible"in _&&n(2,t=_.visible),"value"in _&&n(3,f=_.value),"variant"in _&&n(4,u=_.variant),"interactive"in _&&n(5,m=_.interactive),"size"in _&&n(6,d=_.size),"scale"in _&&n(7,o=_.scale),"icon"in _&&n(8,r=_.icon),"min_width"in _&&n(9,b=_.min_width),"label"in _&&n(10,v=_.label),"gradio"in _&&n(11,c=_.gradio)},[e,l,t,f,u,m,d,o,r,b,v,c,h]}class ce extends W{constructor(i){super(),y(this,i,se,ae,$,{elem_id:0,elem_classes:1,visible:2,value:3,variant:4,interactive:5,size:6,scale:7,icon:8,min_width:9,label:10,gradio:11})}}export{V as BaseButton,ce as default};
//# sourceMappingURL=Index-56b17309.js.map
