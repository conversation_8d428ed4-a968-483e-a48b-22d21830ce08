/* empty css                                              */const{SvelteComponent:u,append:_,attr:d,detach:o,element:g,init:y,insert:v,noop:f,safe_not_equal:c,set_data:m,text:b,toggle_class:r}=window.__gradio__svelte__internal;function A(t){let e,n=(t[0]?Array.isArray(t[0])?t[0].join(", "):t[0]:"")+"",i;return{c(){e=g("div"),i=b(n),d(e,"class","svelte-rgtszb"),r(e,"table",t[1]==="table"),r(e,"gallery",t[1]==="gallery"),r(e,"selected",t[2])},m(l,a){v(l,e,a),_(e,i)},p(l,[a]){a&1&&n!==(n=(l[0]?Array.isArray(l[0])?l[0].join(", "):l[0]:"")+"")&&m(i,n),a&2&&r(e,"table",l[1]==="table"),a&2&&r(e,"gallery",l[1]==="gallery"),a&4&&r(e,"selected",l[2])},i:f,o:f,d(l){l&&o(e)}}}function h(t,e,n){let{value:i}=e,{type:l}=e,{selected:a=!1}=e;return t.$$set=s=>{"value"in s&&n(0,i=s.value),"type"in s&&n(1,l=s.type),"selected"in s&&n(2,a=s.selected)},[i,l,a]}class w extends u{constructor(e){super(),y(this,e,h,A,c,{value:0,type:1,selected:2})}}export{w as default};
//# sourceMappingURL=Example-4a4da230.js.map
