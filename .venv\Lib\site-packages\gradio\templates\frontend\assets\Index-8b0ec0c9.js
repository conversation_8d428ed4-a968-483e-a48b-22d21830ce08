import{S as Ce}from"./Index-26cfc80a.js";import{F as ye}from"./File-d0b52941.js";import{B as Se}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import{B as qe}from"./BlockLabel-f27805b1.js";import"./index-a80d931b.js";import"./svelte/svelte.js";const{SvelteComponent:Ee,append:x,attr:D,detach:pe,init:Fe,insert:je,noop:Z,safe_not_equal:Be,set_style:I,svg_element:G}=window.__gradio__svelte__internal;function Le(t){let e,n,l;return{c(){e=G("svg"),n=G("g"),l=G("path"),D(l,"d","M12.7,24.033C12.256,24.322 11.806,24.339 11.351,24.084C10.896,23.829 10.668,23.434 10.667,22.9L10.667,9.1C10.667,8.567 10.895,8.172 11.351,7.916C11.807,7.66 12.256,7.677 12.7,7.967L23.567,14.867C23.967,15.133 24.167,15.511 24.167,16C24.167,16.489 23.967,16.867 23.567,17.133L12.7,24.033Z"),I(l,"fill","currentColor"),I(l,"fill-rule","nonzero"),D(n,"transform","matrix(1,0,0,1,-10.6667,-7.73588)"),D(e,"width","100%"),D(e,"height","100%"),D(e,"viewBox","0 0 14 17"),D(e,"version","1.1"),I(e,"fill-rule","evenodd"),I(e,"clip-rule","evenodd"),I(e,"stroke-linejoin","round"),I(e,"stroke-miterlimit","2")},m(i,r){je(i,e,r),x(e,n),x(n,l)},p:Z,i:Z,o:Z,d(i){i&&pe(e)}}}class De extends Ee{constructor(e){super(),Fe(this,e,null,Le,Be,{})}}const{SvelteComponent:Ie,attr:ee,detach:Ne,element:Je,init:Oe,insert:Re,listen:te,noop:ne,run_all:Te,safe_not_equal:Ue,toggle_class:le}=window.__gradio__svelte__internal,{createEventDispatcher:ze}=window.__gradio__svelte__internal;function Ae(t){let e,n,l;return{c(){e=Je("input"),ee(e,"type","checkbox"),e.disabled=t[1],ee(e,"class","svelte-1j130g3"),le(e,"disabled",t[1]&&!t[0])},m(i,r){Re(i,e,r),e.checked=t[0],n||(l=[te(e,"change",t[3]),te(e,"input",t[4])],n=!0)},p(i,[r]){r&2&&(e.disabled=i[1]),r&1&&(e.checked=i[0]),r&3&&le(e,"disabled",i[1]&&!i[0])},i:ne,o:ne,d(i){i&&Ne(e),n=!1,Te(l)}}}function Me(t,e,n){let{value:l}=e,{disabled:i}=e;const r=ze();function s(){l=this.checked,n(0,l)}const f=()=>r("change",!l);return t.$$set=_=>{"value"in _&&n(0,l=_.value),"disabled"in _&&n(1,i=_.disabled)},[l,i,r,s,f]}class We extends Ie{constructor(e){super(),Oe(this,e,Me,Ae,Ue,{value:0,disabled:1})}}const ie=""+new URL("light-file-0e72c1e1.svg",import.meta.url).href,se=""+new URL("light-folder-4e9756c4.svg",import.meta.url).href;const{SvelteComponent:Ze,append:L,attr:q,bubble:Ge,check_outros:P,create_component:V,destroy_component:X,destroy_each:He,detach:A,element:O,ensure_array_like:oe,group_outros:Q,init:Ke,insert:M,listen:re,mount_component:Y,noop:_e,run_all:Pe,safe_not_equal:Qe,set_data:Ve,space:R,src_url_equal:fe,stop_propagation:Xe,text:Ye,toggle_class:ce,transition_in:E,transition_out:p}=window.__gradio__svelte__internal,{createEventDispatcher:$e}=window.__gradio__svelte__internal;function ae(t,e,n){const l=t.slice();return l[21]=e[n].type,l[22]=e[n].name,l[23]=e[n].valid,l[25]=n,l}function xe(t){let e,n,l;return{c(){e=O("span"),n=O("img"),fe(n.src,l=t[22]==="."?se:ie)||q(n,"src",l),q(n,"alt","file icon"),q(n,"class","svelte-hyugph"),q(e,"class","file-icon svelte-hyugph")},m(i,r){M(i,e,r),L(e,n)},p(i,r){r&64&&!fe(n.src,l=i[22]==="."?se:ie)&&q(n,"src",l)},i:_e,o:_e,d(i){i&&A(e)}}}function et(t){let e,n,l,i,r;n=new De({});function s(){return t[15](t[25])}function f(..._){return t[16](t[25],..._)}return{c(){e=O("span"),V(n.$$.fragment),q(e,"class","icon svelte-hyugph"),q(e,"role","button"),q(e,"aria-label","expand directory"),q(e,"tabindex","0"),ce(e,"hidden",!t[7].includes(t[25]))},m(_,d){M(_,e,d),Y(n,e,null),l=!0,i||(r=[re(e,"click",Xe(s)),re(e,"keydown",f)],i=!0)},p(_,d){t=_,(!l||d&128)&&ce(e,"hidden",!t[7].includes(t[25]))},i(_){l||(E(n.$$.fragment,_),l=!0)},o(_){p(n.$$.fragment,_),l=!1},d(_){_&&A(e),X(n),i=!1,Pe(r)}}}function ue(t){let e,n;function l(...s){return t[17](t[22],...s)}function i(...s){return t[18](t[22],...s)}function r(...s){return t[19](t[22],...s)}return e=new ve({props:{path:[...t[0],t[22]],selected_files:t[1].filter(l).map(de),selected_folders:t[2].filter(i).map(ge),is_selected_entirely:t[2].some(r),interactive:t[3],ls_fn:t[4],file_count:t[5],valid_for_selection:t[23]}}),e.$on("check",t[20]),{c(){V(e.$$.fragment)},m(s,f){Y(e,s,f),n=!0},p(s,f){t=s;const _={};f&65&&(_.path=[...t[0],t[22]]),f&66&&(_.selected_files=t[1].filter(l).map(de)),f&68&&(_.selected_folders=t[2].filter(i).map(ge)),f&68&&(_.is_selected_entirely=t[2].some(r)),f&8&&(_.interactive=t[3]),f&16&&(_.ls_fn=t[4]),f&32&&(_.file_count=t[5]),f&64&&(_.valid_for_selection=t[23]),e.$set(_)},i(s){n||(E(e.$$.fragment,s),n=!0)},o(s){p(e.$$.fragment,s),n=!1},d(s){X(e,s)}}}function he(t){let e,n,l,i,r,s,f,_=t[22]+"",d,h,a=t[21]==="folder"&&t[7].includes(t[25]),c,u;function k(...m){return t[13](t[22],...m)}function S(...m){return t[14](t[22],t[21],t[25],...m)}l=new We({props:{disabled:!t[3]||t[21]==="folder"&&t[5]==="single",value:(t[21]==="file"?t[1]:t[2]).some(k)}}),l.$on("change",S);const C=[et,xe],y=[];function j(m,w){return m[21]==="folder"?0:1}r=j(t),s=y[r]=C[r](t);let g=a&&ue(t);return{c(){e=O("li"),n=O("span"),V(l.$$.fragment),i=R(),s.c(),f=R(),d=Ye(_),h=R(),g&&g.c(),c=R(),q(n,"class","wrap svelte-hyugph"),q(e,"class","svelte-hyugph")},m(m,w){M(m,e,w),L(e,n),Y(l,n,null),L(n,i),y[r].m(n,null),L(n,f),L(n,d),L(e,h),g&&g.m(e,null),L(e,c),u=!0},p(m,w){t=m;const F={};w&104&&(F.disabled=!t[3]||t[21]==="folder"&&t[5]==="single"),w&70&&(F.value=(t[21]==="file"?t[1]:t[2]).some(k)),l.$set(F);let B=r;r=j(t),r===B?y[r].p(t,w):(Q(),p(y[B],1,1,()=>{y[B]=null}),P(),s=y[r],s?s.p(t,w):(s=y[r]=C[r](t),s.c()),E(s,1),s.m(n,f)),(!u||w&64)&&_!==(_=t[22]+"")&&Ve(d,_),w&192&&(a=t[21]==="folder"&&t[7].includes(t[25])),a?g?(g.p(t,w),w&192&&E(g,1)):(g=ue(t),g.c(),E(g,1),g.m(e,c)):g&&(Q(),p(g,1,1,()=>{g=null}),P())},i(m){u||(E(l.$$.fragment,m),E(s),E(g),u=!0)},o(m){p(l.$$.fragment,m),p(s),p(g),u=!1},d(m){m&&A(e),X(l),y[r].d(),g&&g.d()}}}function tt(t){let e,n,l=oe(t[6]),i=[];for(let s=0;s<l.length;s+=1)i[s]=he(ae(t,l,s));const r=s=>p(i[s],1,1,()=>{i[s]=null});return{c(){e=O("ul");for(let s=0;s<i.length;s+=1)i[s].c();q(e,"class","svelte-hyugph")},m(s,f){M(s,e,f);for(let _=0;_<i.length;_+=1)i[_]&&i[_].m(e,null);n=!0},p(s,[f]){if(f&2047){l=oe(s[6]);let _;for(_=0;_<l.length;_+=1){const d=ae(s,l,_);i[_]?(i[_].p(d,f),E(i[_],1)):(i[_]=he(d),i[_].c(),E(i[_],1),i[_].m(e,null))}for(Q(),_=l.length;_<i.length;_+=1)r(_);P()}},i(s){if(!n){for(let f=0;f<l.length;f+=1)E(i[f]);n=!0}},o(s){i=i.filter(Boolean);for(let f=0;f<i.length;f+=1)p(i[f]);n=!1},d(s){s&&A(e),He(i,s)}}}const de=t=>t.slice(1),ge=t=>t.slice(1);function nt(t,e,n){let{path:l=[]}=e,{selected_files:i=[]}=e,{selected_folders:r=[]}=e,{is_selected_entirely:s=!1}=e,{interactive:f}=e,{ls_fn:_}=e,{file_count:d="multiple"}=e,{valid_for_selection:h}=e,a=[],c=[];const u=o=>{c.includes(o)?n(7,c=c.filter(b=>b!==o)):n(7,c=[...c,o])},k=o=>{c.includes(o)||n(7,c=[...c,o])};(async()=>(n(6,a=await _(l)),h&&n(6,a=[{name:".",type:"file"},...a]),n(7,c=a.map((o,b)=>o.type==="folder"&&(s||i.some(W=>W[0]===o.name))?b:null).filter(o=>o!==null))))();const S=$e(),C=(o,b)=>b[0]===o&&b.length===1,y=(o,b,W,we)=>{let $=we.detail;S("check",{path:[...l,o],checked:$,type:b}),b==="folder"&&$&&k(W)},j=o=>u(o),g=(o,{key:b})=>{(b===" "||b==="Enter")&&u(o)},m=(o,b)=>b[0]===o,w=(o,b)=>b[0]===o,F=(o,b)=>b[0]===o&&b.length===1;function B(o){Ge.call(this,t,o)}return t.$$set=o=>{"path"in o&&n(0,l=o.path),"selected_files"in o&&n(1,i=o.selected_files),"selected_folders"in o&&n(2,r=o.selected_folders),"is_selected_entirely"in o&&n(11,s=o.is_selected_entirely),"interactive"in o&&n(3,f=o.interactive),"ls_fn"in o&&n(4,_=o.ls_fn),"file_count"in o&&n(5,d=o.file_count),"valid_for_selection"in o&&n(12,h=o.valid_for_selection)},t.$$.update=()=>{t.$$.dirty&2113&&s&&a.forEach(o=>{S("check",{path:[...l,o.name],checked:!0,type:o.type})})},[l,i,r,f,_,d,a,c,u,k,S,s,h,C,y,j,g,m,w,F,B]}class ve extends Ze{constructor(e){super(),Ke(this,e,nt,tt,Qe,{path:0,selected_files:1,selected_folders:2,is_selected_entirely:11,interactive:3,ls_fn:4,file_count:5,valid_for_selection:12})}}const{SvelteComponent:lt,attr:it,create_component:st,destroy_component:ot,detach:rt,element:_t,init:ft,insert:ct,mount_component:at,safe_not_equal:ut,transition_in:ht,transition_out:dt}=window.__gradio__svelte__internal;function gt(t){let e,n,l;return n=new ve({props:{path:[],selected_files:t[0],selected_folders:t[4],interactive:t[1],ls_fn:t[3],file_count:t[2],valid_for_selection:!1}}),n.$on("check",t[8]),{c(){e=_t("div"),st(n.$$.fragment),it(e,"class","file-wrap svelte-dicskc")},m(i,r){ct(i,e,r),at(n,e,null),l=!0},p(i,[r]){const s={};r&1&&(s.selected_files=i[0]),r&16&&(s.selected_folders=i[4]),r&2&&(s.interactive=i[1]),r&8&&(s.ls_fn=i[3]),r&4&&(s.file_count=i[2]),n.$set(s)},i(i){l||(ht(n.$$.fragment,i),l=!0)},o(i){dt(n.$$.fragment,i),l=!1},d(i){i&&rt(e),ot(n)}}}function mt(t,e,n){let{interactive:l}=e,{file_count:i="multiple"}=e,{value:r=[]}=e,{ls_fn:s}=e,f=[];const _=(c,u)=>c.join("/")===u.join("/"),d=(c,u)=>u.some(k=>_(k,c)),h=(c,u)=>c.join("/").startsWith(u.join("/")),a=c=>{const{path:u,checked:k,type:S}=c.detail;k?i==="single"?n(0,r=[u]):S==="folder"?d(u,f)||n(4,f=[...f,u]):d(u,r)||n(0,r=[...r,u]):(n(4,f=f.filter(C=>!h(u,C))),S==="folder"?(n(4,f=f.filter(C=>!h(C,u))),n(0,r=r.filter(C=>!h(C,u)))):n(0,r=r.filter(C=>!_(C,u))))};return t.$$set=c=>{"interactive"in c&&n(1,l=c.interactive),"file_count"in c&&n(2,i=c.file_count),"value"in c&&n(0,r=c.value),"ls_fn"in c&&n(3,s=c.ls_fn)},[r,l,i,s,f,_,d,h,a]}class bt extends lt{constructor(e){super(),ft(this,e,mt,gt,ut,{interactive:1,file_count:2,value:0,ls_fn:3})}}const{SvelteComponent:vt,add_flush_callback:kt,assign:wt,bind:Ct,binding_callbacks:yt,check_outros:St,create_component:T,destroy_component:U,detach:H,empty:qt,flush:v,get_spread_object:Et,get_spread_update:pt,group_outros:Ft,init:jt,insert:K,mount_component:z,noop:Bt,safe_not_equal:ke,space:me,transition_in:N,transition_out:J}=window.__gradio__svelte__internal;function be(t){let e,n,l;function i(s){t[20](s)}let r={file_count:t[7],interactive:t[14],ls_fn:t[13].ls};return t[0]!==void 0&&(r.value=t[0]),e=new bt({props:r}),yt.push(()=>Ct(e,"value",i)),{c(){T(e.$$.fragment)},m(s,f){z(e,s,f),l=!0},p(s,f){const _={};f&128&&(_.file_count=s[7]),f&16384&&(_.interactive=s[14]),f&8192&&(_.ls_fn=s[13].ls),!n&&f&1&&(n=!0,_.value=s[0],kt(()=>n=!1)),e.$set(_)},i(s){l||(N(e.$$.fragment,s),l=!0)},o(s){J(e.$$.fragment,s),l=!1},d(s){U(e,s)}}}function Lt(t){let e,n,l,i,r=t[15],s,f;const _=[t[8],{autoscroll:t[12].autoscroll},{i18n:t[12].i18n}];let d={};for(let a=0;a<_.length;a+=1)d=wt(d,_[a]);e=new Ce({props:d}),l=new qe({props:{show_label:t[5],Icon:ye,label:t[4]||"FileExplorer",float:!1}});let h=be(t);return{c(){T(e.$$.fragment),n=me(),T(l.$$.fragment),i=me(),h.c(),s=qt()},m(a,c){z(e,a,c),K(a,n,c),z(l,a,c),K(a,i,c),h.m(a,c),K(a,s,c),f=!0},p(a,c){const u=c&4352?pt(_,[c&256&&Et(a[8]),c&4096&&{autoscroll:a[12].autoscroll},c&4096&&{i18n:a[12].i18n}]):{};e.$set(u);const k={};c&32&&(k.show_label=a[5]),c&16&&(k.label=a[4]||"FileExplorer"),l.$set(k),c&32768&&ke(r,r=a[15])?(Ft(),J(h,1,1,Bt),St(),h=be(a),h.c(),N(h,1),h.m(s.parentNode,s)):h.p(a,c)},i(a){f||(N(e.$$.fragment,a),N(l.$$.fragment,a),N(h),f=!0)},o(a){J(e.$$.fragment,a),J(l.$$.fragment,a),J(h),f=!1},d(a){a&&(H(n),H(i),H(s)),U(e,a),U(l,a),h.d(a)}}}function Dt(t){let e,n;return e=new Se({props:{visible:t[3],variant:t[0]===null?"dashed":"solid",border_mode:"base",padding:!1,elem_id:t[1],elem_classes:t[2],container:t[9],scale:t[10],min_width:t[11],allow_overflow:!1,height:t[6],$$slots:{default:[Lt]},$$scope:{ctx:t}}}),{c(){T(e.$$.fragment)},m(l,i){z(e,l,i),n=!0},p(l,[i]){const r={};i&8&&(r.visible=l[3]),i&1&&(r.variant=l[0]===null?"dashed":"solid"),i&2&&(r.elem_id=l[1]),i&4&&(r.elem_classes=l[2]),i&512&&(r.container=l[9]),i&1024&&(r.scale=l[10]),i&2048&&(r.min_width=l[11]),i&64&&(r.height=l[6]),i&2159025&&(r.$$scope={dirty:i,ctx:l}),e.$set(r)},i(l){n||(N(e.$$.fragment,l),n=!0)},o(l){J(e.$$.fragment,l),n=!1},d(l){U(e,l)}}}function It(t,e,n){let l,{elem_id:i=""}=e,{elem_classes:r=[]}=e,{visible:s=!0}=e,{value:f}=e,_,{label:d}=e,{show_label:h}=e,{height:a=void 0}=e,{file_count:c="multiple"}=e,{root_dir:u}=e,{glob:k}=e,{ignore_glob:S}=e,{loading_status:C}=e,{container:y=!0}=e,{scale:j=null}=e,{min_width:g=void 0}=e,{gradio:m}=e,{server:w}=e,{interactive:F}=e;function B(o){f=o,n(0,f)}return t.$$set=o=>{"elem_id"in o&&n(1,i=o.elem_id),"elem_classes"in o&&n(2,r=o.elem_classes),"visible"in o&&n(3,s=o.visible),"value"in o&&n(0,f=o.value),"label"in o&&n(4,d=o.label),"show_label"in o&&n(5,h=o.show_label),"height"in o&&n(6,a=o.height),"file_count"in o&&n(7,c=o.file_count),"root_dir"in o&&n(16,u=o.root_dir),"glob"in o&&n(17,k=o.glob),"ignore_glob"in o&&n(18,S=o.ignore_glob),"loading_status"in o&&n(8,C=o.loading_status),"container"in o&&n(9,y=o.container),"scale"in o&&n(10,j=o.scale),"min_width"in o&&n(11,g=o.min_width),"gradio"in o&&n(12,m=o.gradio),"server"in o&&n(13,w=o.server),"interactive"in o&&n(14,F=o.interactive)},t.$$.update=()=>{t.$$.dirty&458752&&n(15,l=[u,k,S]),t.$$.dirty&528385&&JSON.stringify(f)!==JSON.stringify(_)&&(n(19,_=f),m.dispatch("change"))},[f,i,r,s,d,h,a,c,C,y,j,g,m,w,F,l,u,k,S,_,B]}class zt extends vt{constructor(e){super(),jt(this,e,It,Dt,ke,{elem_id:1,elem_classes:2,visible:3,value:0,label:4,show_label:5,height:6,file_count:7,root_dir:16,glob:17,ignore_glob:18,loading_status:8,container:9,scale:10,min_width:11,gradio:12,server:13,interactive:14})}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),v()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),v()}get visible(){return this.$$.ctx[3]}set visible(e){this.$$set({visible:e}),v()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),v()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),v()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),v()}get height(){return this.$$.ctx[6]}set height(e){this.$$set({height:e}),v()}get file_count(){return this.$$.ctx[7]}set file_count(e){this.$$set({file_count:e}),v()}get root_dir(){return this.$$.ctx[16]}set root_dir(e){this.$$set({root_dir:e}),v()}get glob(){return this.$$.ctx[17]}set glob(e){this.$$set({glob:e}),v()}get ignore_glob(){return this.$$.ctx[18]}set ignore_glob(e){this.$$set({ignore_glob:e}),v()}get loading_status(){return this.$$.ctx[8]}set loading_status(e){this.$$set({loading_status:e}),v()}get container(){return this.$$.ctx[9]}set container(e){this.$$set({container:e}),v()}get scale(){return this.$$.ctx[10]}set scale(e){this.$$set({scale:e}),v()}get min_width(){return this.$$.ctx[11]}set min_width(e){this.$$set({min_width:e}),v()}get gradio(){return this.$$.ctx[12]}set gradio(e){this.$$set({gradio:e}),v()}get server(){return this.$$.ctx[13]}set server(e){this.$$set({server:e}),v()}get interactive(){return this.$$.ctx[14]}set interactive(e){this.$$set({interactive:e}),v()}}export{zt as default};
//# sourceMappingURL=Index-8b0ec0c9.js.map
