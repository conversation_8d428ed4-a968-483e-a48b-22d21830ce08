const{SvelteComponent:a,append:c,attr:t,detach:p,init:_,insert:d,noop:r,safe_not_equal:u,svg_element:s}=window.__gradio__svelte__internal;function w(l){let e,n;return{c(){e=s("svg"),n=s("polyline"),t(n,"points","20 6 9 17 4 12"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"viewBox","2 0 20 20"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"stroke-width","3"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round")},m(o,i){d(o,e,i),c(e,n)},p:r,i:r,o:r,d(o){o&&p(e)}}}class g extends a{constructor(e){super(),_(this,e,null,w,u,{})}}export{g as C};
//# sourceMappingURL=Check-965babbe.js.map
