{"version": 3, "file": "Index-5db44e2f.js", "sources": ["../../../../js/html/shared/HTML.svelte", "../../../../js/html/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\texport let elem_classes: string[] = [];\n\texport let value: string;\n\texport let visible = true;\n\texport let min_height = false;\n\n\tconst dispatch = createEventDispatcher<{ change: undefined }>();\n\n\t$: value, dispatch(\"change\");\n</script>\n\n<div\n\tclass=\"prose {elem_classes.join(' ')}\"\n\tclass:min={min_height}\n\tclass:hide={!visible}\n>\n\t{@html value}\n</div>\n\n<style>\n\t.min {\n\t\tmin-height: var(--size-24);\n\t}\n\t.hide {\n\t\tdisplay: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport HTML from \"./shared/HTML.svelte\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { Block } from \"@gradio/atoms\";\n\n\texport let label: string;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value = \"\";\n\texport let loading_status: LoadingStatus;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t}>;\n\n\t$: label, gradio.dispatch(\"change\");\n</script>\n\n<Block {visible} {elem_id} {elem_classes} container={false}>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\tvariant=\"center\"\n\t/>\n\t<div class:pending={loading_status?.status === \"pending\"}>\n\t\t<HTML\n\t\t\tmin_height={loading_status && loading_status?.status !== \"complete\"}\n\t\t\t{value}\n\t\t\t{elem_classes}\n\t\t\t{visible}\n\t\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\t/>\n\t</div>\n</Block>\n\n<style>\n\tdiv {\n\t\ttransition: 150ms;\n\t}\n\n\t.pending {\n\t\topacity: 0.2;\n\t}\n</style>\n"], "names": ["createEventDispatcher", "ctx", "insert", "target", "div", "anchor", "elem_classes", "$$props", "value", "visible", "min_height", "dispatch", "dirty", "html_changes", "label", "elem_id", "loading_status", "gradio"], "mappings": "wTACU,CAAA,sBAAAA,CAAA,SAAqC,8FAYhCC,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,iBAAA,YACxBA,EAAU,CAAA,CAAA,cACRA,EAAO,CAAA,CAAA,UAHrBC,EAMKC,EAAAC,EAAAC,CAAA,cADGJ,EAAK,CAAA,8BAALA,EAAK,CAAA,wBAJEA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,kDACxBA,EAAU,CAAA,CAAA,mBACRA,EAAO,CAAA,CAAA,gDAbT,aAAAK,EAAY,EAAA,EAAAC,GACZ,MAAAC,CAAa,EAAAD,EACb,CAAA,QAAAE,EAAU,EAAI,EAAAF,EACd,CAAA,WAAAG,EAAa,EAAK,EAAAH,EAEvB,MAAAI,EAAWX,mMAEPW,EAAS,QAAQ,obCad,CAAA,WAAAV,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,CAAA,iHAKL,WAAAA,EAAkB,CAAA,GAAAA,EAAgB,CAAA,GAAA,SAAW,iLAFvCA,EAAc,CAAA,GAAE,SAAW,SAAS,4BAAxDC,EAQKC,EAAAC,EAAAC,CAAA,6CAbQO,EAAA,IAAA,CAAA,WAAAX,KAAO,UAAU,EACvBW,EAAA,IAAA,CAAA,KAAAX,KAAO,IAAI,UACbA,EAAc,CAAA,CAAA,iCAKLW,EAAA,KAAAC,EAAA,WAAAZ,EAAkB,CAAA,GAAAA,EAAgB,CAAA,GAAA,SAAW,qHAFvCA,EAAc,CAAA,GAAE,SAAW,SAAS,+OAPJ,uUAbzC,MAAAa,CAAa,EAAAP,EACb,CAAA,QAAAQ,EAAU,EAAE,EAAAR,GACZ,aAAAD,EAAY,EAAA,EAAAC,EACZ,CAAA,QAAAE,EAAU,EAAI,EAAAF,EACd,CAAA,MAAAC,EAAQ,EAAE,EAAAD,GACV,eAAAS,CAA6B,EAAAT,GAC7B,OAAAU,CAET,EAAAV,cAkBiBU,EAAO,SAAS,QAAQ,oSAhBjCA,EAAO,SAAS,QAAQ"}