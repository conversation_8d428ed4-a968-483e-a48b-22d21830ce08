import{B as Ie}from"./SelectSource.svelte_svelte_type_style_lang-0a285437.js";import"./Index-26cfc80a.js";import"./index-a80d931b.js";import"./svelte/svelte.js";const{SvelteComponent:je,append:B,assign:pe,attr:g,check_outros:P,construct_svelte_component:O,create_component:Y,destroy_component:D,destroy_each:F,detach:w,element:N,empty:J,ensure_array_like:C,get_spread_object:Q,get_spread_update:T,group_outros:E,init:qe,insert:z,listen:A,mount_component:G,noop:Ae,null_to_empty:$,run_all:be,safe_not_equal:Re,set_data:y,set_style:ee,space:j,svg_element:le,text:U,toggle_class:te,transition_in:p,transition_out:k}=window.__gradio__svelte__internal;function ne(i,e,l){const o=i.slice();return o[35]=e[l],o}function oe(i,e,l){const o=i.slice();return o[38]=e[l],o[40]=l,o}function ie(i,e,l){const o=i.slice();o[0]=e[l].value,o[42]=e[l].component,o[45]=l;const t=o[1][o[45]];return o[43]=t,o}function se(i,e,l){const o=i.slice();return o[46]=e[l],o}function fe(i,e,l){const o=i.slice();return o[38]=e[l],o[40]=l,o}function Se(i){let e,l,o,t,s,_,n,f=C(i[5]),r=[];for(let c=0;c<f.length;c+=1)r[c]=_e(se(i,f,c));let h=C(i[18]),u=[];for(let c=0;c<h.length;c+=1)u[c]=ue(oe(i,h,c));const v=c=>k(u[c],1,1,()=>{u[c]=null});return{c(){e=N("div"),l=N("table"),o=N("thead"),t=N("tr");for(let c=0;c<r.length;c+=1)r[c].c();s=j(),_=N("tbody");for(let c=0;c<u.length;c+=1)u[c].c();g(t,"class","tr-head svelte-13hsdno"),g(l,"tabindex","0"),g(l,"role","grid"),g(l,"class","svelte-13hsdno"),g(e,"class","table-wrap svelte-13hsdno")},m(c,b){z(c,e,b),B(e,l),B(l,o),B(o,t);for(let a=0;a<r.length;a+=1)r[a]&&r[a].m(t,null);B(l,s),B(l,_);for(let a=0;a<u.length;a+=1)u[a]&&u[a].m(_,null);n=!0},p(c,b){if(b[0]&32){f=C(c[5]);let a;for(a=0;a<f.length;a+=1){const d=se(c,f,a);r[a]?r[a].p(d,b):(r[a]=_e(d),r[a].c(),r[a].m(t,null))}for(;a<r.length;a+=1)r[a].d(1);r.length=f.length}if(b[0]&7746063){h=C(c[18]);let a;for(a=0;a<h.length;a+=1){const d=oe(c,h,a);u[a]?(u[a].p(d,b),p(u[a],1)):(u[a]=ue(d),u[a].c(),p(u[a],1),u[a].m(_,null))}for(E(),a=h.length;a<u.length;a+=1)v(a);P()}},i(c){if(!n){for(let b=0;b<h.length;b+=1)p(u[b]);n=!0}},o(c){u=u.filter(Boolean);for(let b=0;b<u.length;b+=1)k(u[b]);n=!1},d(c){c&&w(e),F(r,c),F(u,c)}}}function Ye(i){let e,l,o=C(i[15]),t=[];for(let _=0;_<o.length;_+=1)t[_]=he(fe(i,o,_));const s=_=>k(t[_],1,1,()=>{t[_]=null});return{c(){e=N("div");for(let _=0;_<t.length;_+=1)t[_].c();g(e,"class","gallery svelte-13hsdno")},m(_,n){z(_,e,n);for(let f=0;f<t.length;f+=1)t[f]&&t[f].m(e,null);l=!0},p(_,n){if(n[0]&7778831){o=C(_[15]);let f;for(f=0;f<o.length;f+=1){const r=fe(_,o,f);t[f]?(t[f].p(r,n),p(t[f],1)):(t[f]=he(r),t[f].c(),p(t[f],1),t[f].m(e,null))}for(E(),f=o.length;f<t.length;f+=1)s(f);P()}},i(_){if(!l){for(let n=0;n<o.length;n+=1)p(t[n]);l=!0}},o(_){t=t.filter(Boolean);for(let n=0;n<t.length;n+=1)k(t[n]);l=!1},d(_){_&&w(e),F(t,_)}}}function _e(i){let e,l=i[46]+"",o,t;return{c(){e=N("th"),o=U(l),t=j(),g(e,"class","svelte-13hsdno")},m(s,_){z(s,e,_),B(e,o),B(e,t)},p(s,_){_[0]&32&&l!==(l=s[46]+"")&&y(o,l)},d(s){s&&w(e)}}}function ce(i){let e,l,o,t;const s=[i[2][i[45]],{value:i[0]},{samples_dir:i[20]},{type:"table"},{selected:i[17]===i[40]},{index:i[40]}];var _=i[42];function n(f,r){let h={};if(r!==void 0&&r[0]&1441796)h=T(s,[r[0]&4&&Q(f[2][f[45]]),r[0]&262144&&{value:f[0]},r[0]&1048576&&{samples_dir:f[20]},s[3],r[0]&131072&&{selected:f[17]===f[40]},s[5]]);else for(let u=0;u<s.length;u+=1)h=pe(h,s[u]);return{props:h}}return _&&(l=O(_,n(i))),{c(){e=N("td"),l&&Y(l.$$.fragment),ee(e,"max-width",i[43]==="textbox"?"35ch":"auto"),g(e,"class",o=$(i[43])+" svelte-13hsdno")},m(f,r){z(f,e,r),l&&G(l,e,null),t=!0},p(f,r){if(r[0]&262144&&_!==(_=f[42])){if(l){E();const h=l;k(h.$$.fragment,1,0,()=>{D(h,1)}),P()}_?(l=O(_,n(f,r)),Y(l.$$.fragment),p(l.$$.fragment,1),G(l,e,null)):l=null}else if(_){const h=r[0]&1441796?T(s,[r[0]&4&&Q(f[2][f[45]]),r[0]&262144&&{value:f[0]},r[0]&1048576&&{samples_dir:f[20]},s[3],r[0]&131072&&{selected:f[17]===f[40]},s[5]]):{};l.$set(h)}(!t||r[0]&2)&&ee(e,"max-width",f[43]==="textbox"?"35ch":"auto"),(!t||r[0]&2&&o!==(o=$(f[43])+" svelte-13hsdno"))&&g(e,"class",o)},i(f){t||(l&&p(l.$$.fragment,f),t=!0)},o(f){l&&k(l.$$.fragment,f),t=!1},d(f){f&&w(e),l&&D(l)}}}function re(i){let e=i[43]!==void 0&&i[3].get(i[43])!==void 0,l,o,t=e&&ce(i);return{c(){t&&t.c(),l=J()},m(s,_){t&&t.m(s,_),z(s,l,_),o=!0},p(s,_){_[0]&10&&(e=s[43]!==void 0&&s[3].get(s[43])!==void 0),e?t?(t.p(s,_),_[0]&10&&p(t,1)):(t=ce(s),t.c(),p(t,1),t.m(l.parentNode,l)):t&&(E(),k(t,1,1,()=>{t=null}),P())},i(s){o||(p(t),o=!0)},o(s){k(t),o=!1},d(s){s&&w(l),t&&t.d(s)}}}function ue(i){let e,l,o,t,s,_=C(i[38]),n=[];for(let u=0;u<_.length;u+=1)n[u]=re(ie(i,_,u));const f=u=>k(n[u],1,1,()=>{n[u]=null});function r(){return i[30](i[40])}function h(){return i[31](i[40])}return{c(){e=N("tr");for(let u=0;u<n.length;u+=1)n[u].c();l=j(),g(e,"class","tr-body svelte-13hsdno")},m(u,v){z(u,e,v);for(let c=0;c<n.length;c+=1)n[c]&&n[c].m(e,null);B(e,l),o=!0,t||(s=[A(e,"click",r),A(e,"mouseenter",h),A(e,"mouseleave",i[32])],t=!0)},p(u,v){if(i=u,v[0]&1441806){_=C(i[38]);let c;for(c=0;c<_.length;c+=1){const b=ie(i,_,c);n[c]?(n[c].p(b,v),p(n[c],1)):(n[c]=re(b),n[c].c(),p(n[c],1),n[c].m(e,l))}for(E(),c=_.length;c<n.length;c+=1)f(c);P()}},i(u){if(!o){for(let v=0;v<_.length;v+=1)p(n[v]);o=!0}},o(u){n=n.filter(Boolean);for(let v=0;v<n.length;v+=1)k(n[v]);o=!1},d(u){u&&w(e),F(n,u),t=!1,be(s)}}}function ae(i){let e,l=i[18].length&&i[3].get(i[1][0]),o,t,s,_,n=l&&me(i);function f(){return i[27](i[40],i[38])}function r(){return i[28](i[40])}return{c(){e=N("button"),n&&n.c(),o=j(),g(e,"class","gallery-item svelte-13hsdno")},m(h,u){z(h,e,u),n&&n.m(e,null),B(e,o),t=!0,s||(_=[A(e,"click",f),A(e,"mouseenter",r),A(e,"mouseleave",i[29])],s=!0)},p(h,u){i=h,u[0]&262154&&(l=i[18].length&&i[3].get(i[1][0])),l?n?(n.p(i,u),u[0]&262154&&p(n,1)):(n=me(i),n.c(),p(n,1),n.m(e,o)):n&&(E(),k(n,1,1,()=>{n=null}),P())},i(h){t||(p(n),t=!0)},o(h){k(n),t=!1},d(h){h&&w(e),n&&n.d(),s=!1,be(_)}}}function me(i){let e,l,o;const t=[i[2][0],{value:i[38][0]},{samples_dir:i[20]},{type:"gallery"},{selected:i[17]===i[40]},{index:i[40]}];var s=i[18][0][0].component;function _(n,f){let r={};if(f!==void 0&&f[0]&1212420)r=T(t,[f[0]&4&&Q(n[2][0]),f[0]&32768&&{value:n[38][0]},f[0]&1048576&&{samples_dir:n[20]},t[3],f[0]&131072&&{selected:n[17]===n[40]},t[5]]);else for(let h=0;h<t.length;h+=1)r=pe(r,t[h]);return{props:r}}return s&&(e=O(s,_(i))),{c(){e&&Y(e.$$.fragment),l=J()},m(n,f){e&&G(e,n,f),z(n,l,f),o=!0},p(n,f){if(f[0]&262144&&s!==(s=n[18][0][0].component)){if(e){E();const r=e;k(r.$$.fragment,1,0,()=>{D(r,1)}),P()}s?(e=O(s,_(n,f)),Y(e.$$.fragment),p(e.$$.fragment,1),G(e,l.parentNode,l)):e=null}else if(s){const r=f[0]&1212420?T(t,[f[0]&4&&Q(n[2][0]),f[0]&32768&&{value:n[38][0]},f[0]&1048576&&{samples_dir:n[20]},t[3],f[0]&131072&&{selected:n[17]===n[40]},t[5]]):{};e.$set(r)}},i(n){o||(e&&p(e.$$.fragment,n),o=!0)},o(n){e&&k(e.$$.fragment,n),o=!1},d(n){n&&w(l),e&&D(e,n)}}}function he(i){let e,l,o=i[38][0]&&ae(i);return{c(){o&&o.c(),e=J()},m(t,s){o&&o.m(t,s),z(t,e,s),l=!0},p(t,s){t[38][0]?o?(o.p(t,s),s[0]&32768&&p(o,1)):(o=ae(t),o.c(),p(o,1),o.m(e.parentNode,e)):o&&(E(),k(o,1,1,()=>{o=null}),P())},i(t){l||(p(o),l=!0)},o(t){k(o),l=!1},d(t){t&&w(e),o&&o.d(t)}}}function de(i){let e,l,o=C(i[16]),t=[];for(let s=0;s<o.length;s+=1)t[s]=ge(ne(i,o,s));return{c(){e=N("div"),l=U(`Pages:
			`);for(let s=0;s<t.length;s+=1)t[s].c();g(e,"class","paginate svelte-13hsdno")},m(s,_){z(s,e,_),B(e,l);for(let n=0;n<t.length;n+=1)t[n]&&t[n].m(e,null)},p(s,_){if(_[0]&73728){o=C(s[16]);let n;for(n=0;n<o.length;n+=1){const f=ne(s,o,n);t[n]?t[n].p(f,_):(t[n]=ge(f),t[n].c(),t[n].m(e,null))}for(;n<t.length;n+=1)t[n].d(1);t.length=o.length}},d(s){s&&w(e),F(t,s)}}}function De(i){let e,l=i[35]+1+"",o,t,s,_;function n(){return i[33](i[35])}return{c(){e=N("button"),o=U(l),t=j(),g(e,"class","svelte-13hsdno"),te(e,"current-page",i[13]===i[35])},m(f,r){z(f,e,r),B(e,o),B(e,t),s||(_=A(e,"click",n),s=!0)},p(f,r){i=f,r[0]&65536&&l!==(l=i[35]+1+"")&&y(o,l),r[0]&73728&&te(e,"current-page",i[13]===i[35])},d(f){f&&w(e),s=!1,_()}}}function Fe(i){let e;return{c(){e=N("div"),e.textContent="..."},m(l,o){z(l,e,o)},p:Ae,d(l){l&&w(e)}}}function ge(i){let e;function l(s,_){return s[35]===-1?Fe:De}let o=l(i),t=o(i);return{c(){t.c(),e=J()},m(s,_){t.m(s,_),z(s,e,_)},p(s,_){o===(o=l(s))&&t?t.p(s,_):(t.d(1),t=o(s),t&&(t.c(),t.m(e.parentNode,e)))},d(s){s&&w(e),t.d(s)}}}function Ge(i){let e,l,o,t,s,_,n,f,r,h,u;const v=[Ye,Se],c=[];function b(d,H){return d[19]?0:1}n=b(i),f=c[n]=v[n](i);let a=i[14]&&de(i);return{c(){e=N("div"),l=le("svg"),o=le("path"),t=j(),s=U(i[4]),_=j(),f.c(),r=j(),a&&a.c(),h=J(),g(o,"fill","currentColor"),g(o,"d","M10 6h18v2H10zm0 18h18v2H10zm0-9h18v2H10zm-6 0h2v2H4zm0-9h2v2H4zm0 18h2v2H4z"),g(l,"xmlns","http://www.w3.org/2000/svg"),g(l,"xmlns:xlink","http://www.w3.org/1999/xlink"),g(l,"aria-hidden","true"),g(l,"role","img"),g(l,"width","1em"),g(l,"height","1em"),g(l,"preserveAspectRatio","xMidYMid meet"),g(l,"viewBox","0 0 32 32"),g(l,"class","svelte-13hsdno"),g(e,"class","label svelte-13hsdno")},m(d,H){z(d,e,H),B(e,l),B(l,o),B(e,t),B(e,s),z(d,_,H),c[n].m(d,H),z(d,r,H),a&&a.m(d,H),z(d,h,H),u=!0},p(d,H){(!u||H[0]&16)&&y(s,d[4]);let R=n;n=b(d),n===R?c[n].p(d,H):(E(),k(c[R],1,1,()=>{c[R]=null}),P(),f=c[n],f?f.p(d,H):(f=c[n]=v[n](d),f.c()),p(f,1),f.m(r.parentNode,r)),d[14]?a?a.p(d,H):(a=de(d),a.c(),a.m(h.parentNode,h)):a&&(a.d(1),a=null)},i(d){u||(p(f),u=!0)},o(d){k(f),u=!1},d(d){d&&(w(e),w(_),w(r),w(h)),c[n].d(d),a&&a.d(d)}}}function Je(i){let e,l;return e=new Ie({props:{visible:i[8],padding:!1,elem_id:i[6],elem_classes:i[7],scale:i[10],min_width:i[11],allow_overflow:!1,container:!1,$$slots:{default:[Ge]},$$scope:{ctx:i}}}),{c(){Y(e.$$.fragment)},m(o,t){G(e,o,t),l=!0},p(o,t){const s={};t[0]&256&&(s.visible=o[8]),t[0]&64&&(s.elem_id=o[6]),t[0]&128&&(s.elem_classes=o[7]),t[0]&1024&&(s.scale=o[10]),t[0]&2048&&(s.min_width=o[11]),t[0]&1045055|t[1]&262144&&(s.$$scope={dirty:t,ctx:o}),e.$set(s)},i(o){l||(p(e.$$.fragment,o),l=!0)},o(o){k(e.$$.fragment,o),l=!1},d(o){D(e,o)}}}function Ke(i,e,l){let o,{components:t}=e,{component_props:s}=e,{component_map:_}=e,{label:n="Examples"}=e,{headers:f}=e,{samples:r}=e,{elem_id:h=""}=e,{elem_classes:u=[]}=e,{visible:v=!0}=e,{value:c=null}=e,{root:b}=e,{proxy_url:a}=e,{samples_per_page:d=10}=e,{scale:H=null}=e,{min_width:R=void 0}=e,{gradio:S}=e,ve=a?`/proxy=${a}file=`:`${b}/file=`,q=0,V=r.length>d,K,L,I=[],W=-1;function X(m){l(17,W=m)}function Z(){l(17,W=-1)}let x=[];async function ke(m){l(18,x=await Promise.all(m&&m.map(async M=>await Promise.all(M.map(async(Pe,Ee)=>({value:Pe,component:(await _.get(t[Ee]))?.default}))))))}const we=(m,M)=>{l(0,c=m+q*d),S.dispatch("click",c),S.dispatch("select",{index:c,value:M})},ze=m=>X(m),Be=()=>Z(),He=m=>{l(0,c=m+q*d),S.dispatch("click",c)},Ne=m=>X(m),Me=()=>Z(),Ce=m=>l(13,q=m);return i.$$set=m=>{"components"in m&&l(1,t=m.components),"component_props"in m&&l(2,s=m.component_props),"component_map"in m&&l(3,_=m.component_map),"label"in m&&l(4,n=m.label),"headers"in m&&l(5,f=m.headers),"samples"in m&&l(23,r=m.samples),"elem_id"in m&&l(6,h=m.elem_id),"elem_classes"in m&&l(7,u=m.elem_classes),"visible"in m&&l(8,v=m.visible),"value"in m&&l(0,c=m.value),"root"in m&&l(24,b=m.root),"proxy_url"in m&&l(25,a=m.proxy_url),"samples_per_page"in m&&l(9,d=m.samples_per_page),"scale"in m&&l(10,H=m.scale),"min_width"in m&&l(11,R=m.min_width),"gradio"in m&&l(12,S=m.gradio)},i.$$.update=()=>{i.$$.dirty[0]&2&&l(19,o=t.length<2),i.$$.dirty[0]&75588096&&(l(14,V=r.length>d),V?(l(16,I=[]),l(15,K=r.slice(q*d,(q+1)*d)),l(26,L=Math.ceil(r.length/d)),[0,q,L-1].forEach(m=>{for(let M=m-2;M<=m+2;M++)M>=0&&M<L&&!I.includes(M)&&(I.length>0&&M-I[I.length-1]>1&&I.push(-1),I.push(M))})):l(15,K=r.slice())),i.$$.dirty[0]&32768&&ke(K)},[c,t,s,_,n,f,h,u,v,d,H,R,S,q,V,K,I,W,x,o,ve,X,Z,r,b,a,L,we,ze,Be,He,Ne,Me,Ce]}class Ue extends je{constructor(e){super(),qe(this,e,Ke,Je,Re,{components:1,component_props:2,component_map:3,label:4,headers:5,samples:23,elem_id:6,elem_classes:7,visible:8,value:0,root:24,proxy_url:25,samples_per_page:9,scale:10,min_width:11,gradio:12},null,[-1,-1])}}export{Ue as default};
//# sourceMappingURL=Index-e2226da2.js.map
