const{SvelteComponent:f,append:c,attr:d,detach:v,element:g,init:o,insert:r,noop:u,safe_not_equal:y,set_data:m,text:b,toggle_class:_}=window.__gradio__svelte__internal;function h(a){let e,n=(a[0]!==null?a[0]:"")+"",s;return{c(){e=g("div"),s=b(n),d(e,"class","svelte-1ayixqk"),_(e,"table",a[1]==="table"),_(e,"gallery",a[1]==="gallery"),_(e,"selected",a[2])},m(l,t){r(l,e,t),c(e,s)},p(l,[t]){t&1&&n!==(n=(l[0]!==null?l[0]:"")+"")&&m(s,n),t&2&&_(e,"table",l[1]==="table"),t&2&&_(e,"gallery",l[1]==="gallery"),t&4&&_(e,"selected",l[2])},i:u,o:u,d(l){l&&v(e)}}}function q(a,e,n){let{value:s}=e,{type:l}=e,{selected:t=!1}=e;return a.$$set=i=>{"value"in i&&n(0,s=i.value),"type"in i&&n(1,l=i.type),"selected"in i&&n(2,t=i.selected)},[s,l,t]}class w extends f{constructor(e){super(),o(this,e,q,h,y,{value:0,type:1,selected:2})}}export{w as default};
//# sourceMappingURL=Example-cdade505.js.map
