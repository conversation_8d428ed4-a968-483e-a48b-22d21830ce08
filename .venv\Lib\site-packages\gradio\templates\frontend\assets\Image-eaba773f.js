const{SvelteComponent:p,append:s,attr:e,detach:d,init:g,insert:w,noop:c,safe_not_equal:_,svg_element:o}=window.__gradio__svelte__internal;function u(a){let t,n,r,i;return{c(){t=o("svg"),n=o("rect"),r=o("circle"),i=o("polyline"),e(n,"x","3"),e(n,"y","3"),e(n,"width","18"),e(n,"height","18"),e(n,"rx","2"),e(n,"ry","2"),e(r,"cx","8.5"),e(r,"cy","8.5"),e(r,"r","1.5"),e(i,"points","21 15 16 10 5 21"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","100%"),e(t,"height","100%"),e(t,"viewBox","0 0 24 24"),e(t,"fill","none"),e(t,"stroke","currentColor"),e(t,"stroke-width","1.5"),e(t,"stroke-linecap","round"),e(t,"stroke-linejoin","round"),e(t,"class","feather feather-image")},m(l,h){w(l,t,h),s(t,n),s(t,r),s(t,i)},p:c,i:c,o:c,d(l){l&&d(t)}}}class x extends p{constructor(t){super(),g(this,t,null,u,_,{})}}export{x as I};
//# sourceMappingURL=Image-eaba773f.js.map
