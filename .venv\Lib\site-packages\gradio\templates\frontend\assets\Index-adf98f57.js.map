{"version": 3, "file": "Index-adf98f57.js", "sources": ["../../../../js/file/Index.svelte"], "sourcesContent": ["<svelte:options accessors={true} />\n\n<script context=\"module\" lang=\"ts\">\n\texport { default as FilePreview } from \"./shared/FilePreview.svelte\";\n\texport { default as BaseFileUpload } from \"./shared/FileUpload.svelte\";\n\texport { default as BaseFile } from \"./shared/File.svelte\";\n\texport { default as BaseExample } from \"./Example.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport File from \"./shared/File.svelte\";\n\timport FileUpload from \"./shared/FileUpload.svelte\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { Block, UploadText } from \"@gradio/atoms\";\n\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: null | FileData | FileData[];\n\n\texport let interactive: boolean;\n\texport let root: string;\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let height: number | undefined = undefined;\n\n\texport let _selectable = false;\n\texport let loading_status: LoadingStatus;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\terror: string;\n\t\tupload: never;\n\t\tclear: never;\n\t\tselect: SelectData;\n\t}>;\n\texport let file_count: string;\n\texport let file_types: string[] = [\"file\"];\n\n\tlet old_value = value;\n\t$: if (JSON.stringify(old_value) !== JSON.stringify(value)) {\n\t\tgradio.dispatch(\"change\");\n\t\told_value = value;\n\t}\n\n\tlet dragging = false;\n\tlet pending_upload = false;\n</script>\n\n<Block\n\t{visible}\n\tvariant={value === null ? \"dashed\" : \"solid\"}\n\tborder_mode={dragging ? \"focus\" : \"base\"}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\tstatus={pending_upload\n\t\t\t? \"generating\"\n\t\t\t: loading_status?.status || \"complete\"}\n\t/>\n\t{#if !interactive}\n\t\t<File\n\t\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t\t\tselectable={_selectable}\n\t\t\t{value}\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{height}\n\t\t\ti18n={gradio.i18n}\n\t\t/>\n\t{:else}\n\t\t<FileUpload\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{value}\n\t\t\t{file_count}\n\t\t\t{file_types}\n\t\t\tselectable={_selectable}\n\t\t\t{root}\n\t\t\t{height}\n\t\t\ton:change={({ detail }) => {\n\t\t\t\tvalue = detail;\n\t\t\t}}\n\t\t\ton:drag={({ detail }) => (dragging = detail)}\n\t\t\ton:clear={() => gradio.dispatch(\"clear\")}\n\t\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t\t\ton:upload={() => gradio.dispatch(\"upload\")}\n\t\t\ti18n={gradio.i18n}\n\t\t>\n\t\t\t<UploadText i18n={gradio.i18n} type=\"file\" />\n\t\t</FileUpload>\n\t{/if}\n</Block>\n"], "names": ["ctx", "dirty", "fileupload_changes", "file_changes", "uploadtext_changes", "block_changes", "elem_id", "$$props", "elem_classes", "visible", "value", "interactive", "root", "label", "show_label", "height", "_selectable", "loading_status", "container", "scale", "min_width", "gradio", "file_count", "file_types", "old_value", "dragging", "select_handler", "detail", "$$invalidate", "select_handler_1"], "mappings": "isCA4FeA,EAAW,CAAA,wBAUjB,KAAAA,MAAO,8WAVDA,EAAW,CAAA,8CAUjBC,EAAA,QAAAC,EAAA,KAAAF,MAAO,gMAxBDA,EAAW,CAAA,oDAKjB,KAAAA,MAAO,kHALDA,EAAW,CAAA,8FAKjBC,EAAA,QAAAE,EAAA,KAAAH,MAAO,8IAqBK,KAAAA,MAAO,mFAAPC,EAAA,QAAAG,EAAA,KAAAJ,MAAO,yIApCd,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,EAAA,GACV,OAELA,EAAc,EAAA,GAAE,QAAU,2HAExBA,EAAW,CAAA,IAAA,kKAPJ,WAAAA,MAAO,YACbC,EAAA,OAAA,CAAA,KAAAD,MAAO,IAAI,YACbA,EAAc,EAAA,CAAA,WACV,OAELA,EAAc,EAAA,GAAE,QAAU,oVAhBrB,QAAAA,EAAU,CAAA,IAAA,KAAO,SAAW,oBACxBA,EAAQ,EAAA,EAAG,QAAU,eACzB,6FAMO,oIARPC,EAAA,IAAAI,EAAA,QAAAL,EAAU,CAAA,IAAA,KAAO,SAAW,kCACxBA,EAAQ,EAAA,EAAG,QAAU,8RAvCvB,GAAA,CAAA,QAAAM,EAAU,EAAE,EAAAC,GACZ,aAAAC,EAAY,EAAA,EAAAD,EACZ,CAAA,QAAAE,EAAU,EAAI,EAAAF,GACd,MAAAG,CAAmC,EAAAH,GAEnC,YAAAI,CAAoB,EAAAJ,GACpB,KAAAK,CAAY,EAAAL,GACZ,MAAAM,CAAa,EAAAN,GACb,WAAAO,CAAmB,EAAAP,EACnB,CAAA,OAAAQ,EAA6B,MAAS,EAAAR,EAEtC,CAAA,YAAAS,EAAc,EAAK,EAAAT,GACnB,eAAAU,CAA6B,EAAAV,EAC7B,CAAA,UAAAW,EAAY,EAAI,EAAAX,EAChB,CAAA,MAAAY,EAAuB,IAAI,EAAAZ,EAC3B,CAAA,UAAAa,EAAgC,MAAS,EAAAb,GACzC,OAAAc,CAMT,EAAAd,GACS,WAAAe,CAAkB,EAAAf,EAClB,CAAA,WAAAgB,GAAwB,MAAM,CAAA,EAAAhB,EAErCiB,EAAYd,EAMZe,EAAW,GA0BC,MAAAC,EAAA,CAAA,CAAA,OAAAC,KAAaN,EAAO,SAAS,SAAUM,CAAM,MAkB7C,OAAAA,KAAM,CACnBC,EAAA,EAAAlB,EAAQiB,CAAM,OAEH,OAAAA,CAAM,IAAAC,EAAA,GAAQH,EAAWE,CAAM,QAC3BN,EAAO,SAAS,OAAO,EACzBQ,EAAA,CAAA,CAAA,OAAAF,KAAaN,EAAO,SAAS,SAAUM,CAAM,QAC1CN,EAAO,SAAS,QAAQ,6oBAvDpC,KAAK,UAAUG,CAAS,IAAM,KAAK,UAAUd,CAAK,IACxDW,EAAO,SAAS,QAAQ,EACxBO,EAAA,GAAAJ,EAAYd,CAAK"}