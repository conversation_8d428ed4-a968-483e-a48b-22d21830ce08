{"name": "@gradio/plot", "version": "0.3.5", "description": "Gradio UI packages", "type": "module", "author": "", "license": "ISC", "private": false, "dependencies": {"@gradio/atoms": "workspace:^", "@gradio/icons": "workspace:^", "@gradio/statustracker": "workspace:^", "@gradio/theme": "workspace:^", "@gradio/utils": "workspace:^", "@rollup/plugin-json": "^6.0.0", "plotly.js-dist-min": "^2.10.1", "svelte-vega": "^2.0.0", "vega": "^5.22.1", "vega-lite": "^5.12.0"}, "main_changeset": true, "exports": {".": "./Index.svelte", "./package.json": "./package.json"}}